stages:
  - pipeline_trigger
  - codecheck
  # - create_merge_request

# cache:
#   paths:
#     - .venv/

full_codecheck:
  stage: codecheck
  tags: [pipeline_trigger]
  script:
    - python -m pip install pre-commit pylint black
    - pre-commit run --all-files
    # - pylint --rcfile=.pylintrc ./*
  rules:
    # - exists:
    #     - $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    # - exists:
    #     - $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    # - exists:
    #     - $CI_COMMIT_REF_NAME
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'

incr_codecheck:
  stage: codecheck
  tags: [pipeline_trigger]
  script:
    - python -m pip install pre-commit pylint black
    - git fetch origin master
    - pre-commit run --from-ref FETCH_HEAD --to-ref HEAD
    # - pylint --rcfile=.pylintrc --from-ref FETCH_HEAD --to-ref HEAD
  only:
    refs:
      - pushes
    # changes:
    #   - "**/*.py"
    #   - ".gitlab-ci.yml"

pipeline_trigger:
  stage: pipeline_trigger
  tags: [pipeline_trigger]
  script:
    - 'curl -X POST bk-devops.h3d.com.cn/ms/process/api/external/pipelines/d634cd6281ab40caa651ebc030326c18/build -H "Content-Type: application/json" -d "{}"'
    # - 'curl -X POST -H "Content-Type: application/json"  http://jenkins-tac-team.h3d.com.cn/generic-webhook-trigger/invoke/trigger_pyframe_ci'
    - curl 'http://jenkins-tac-team.h3d.com.cn/job/pipeline/job/auto_create_mr/buildWithParameters?token=f8d4e4b8-3919-29cd-5c37-0f38521f945e&PROJECT=pyframe-pipeline'
  rules:
    - if: '$CI_COMMIT_BRANCH == "liutongqing"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'

# create_merge_request:
#   stage: create_merge_request
#   script:
#     - curl 'http://jenkins-tac-team.h3d.com.cn/job/pipeline/job/auto_create_mr/buildWithParameters?token=f8d4e4b8-3919-29cd-5c37-0f38521f945e&PROJECT=pyframe-pipeline'
