stages:
  - install
  - pipeline_trigger
  - codecheck

cache:
  paths:
    - .venv/

install:
  stage: install
  tags: [pipeline_trigger]
  script:
    - poetry config virtualenvs.path .venv
    - poetry config virtualenvs.in-project true
    - poetry install

full_codecheck:
  stage: codecheck
  tags: [pipeline_trigger]
  script:
    - poetry run pre-commit run --all-files
  rules:
    - exists:
        - $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - exists:
        - $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    - exists:
        - $CI_COMMIT_REF_NAME

incr_codecheck:
  stage: codecheck
  tags: [pipeline_trigger]
  script:
    - git fetch origin master
    - poetry run pre-commit run --from-ref FETCH_HEAD --to-ref HEAD
  only:
    refs:
      - pushes
    changes:
      - "**/*.py"
      - ".gitlab-ci.yml"

pipeline_trigger:
  stage: pipeline_trigger
  tags: [pipeline_trigger]
  script:
    - 'curl -X POST bk-devops.h3d.com.cn/ms/process/api/external/pipelines/d634cd6281ab40caa651ebc030326c18/build -H "Content-Type: application/json" -d "{}"'
  rules:
    - if: '$CI_COMMIT_BRANCH == "liutongqing"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
