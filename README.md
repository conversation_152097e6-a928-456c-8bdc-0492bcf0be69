# pyframe-pipeline

基于python框架开发的流水线

## 目录结构

```bash
frame: 流水线框架，基础模块
images: docker镜像
project: 基于python3.6.8开发的流水线
project/devops: 平台工具组的流水线
project/devpro: 效能组的流水线
project/framw: 框架组的流水线
project/m2: m2项目的流水线
project/pipeline: 流水线组的流水线
project/team1: team1组（炫舞端游引擎工具组）的流水线
project/team3: team3组（M2引擎工具组）的流水线
project/team4: team4组（炫舞手游引擎工具组）的流水线
project/testdev: 白盒测试组的流水线
project/x5m: x5m项目的流水线
project/x51: x51项目的流水线
project/x52: x52项目的流水线
project39: 基于python3.9.12开发的流水线
scripts: 脚本
services: 服务。未来服务不在这里开发。
tests: 测试用例
trigger: p4 trigger
```

## 开发环境

### 3.6.8

```bash
cd pyframe-pipeline
python -m pip install -r requirements.txt  -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn
python -m pip install pre-commit pylint black
pre-commit install
```

### 3.9.12

```bash
cd pyframe-pipeline
poetry install
poetry shell
pre-commit install
```

## 代码规范

使用以下命令检查代码是否符合规范：

```bash
pre-commit run --all-files
```
