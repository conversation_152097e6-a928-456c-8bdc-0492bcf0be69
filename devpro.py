# coding=utf-8
from operator import methodcaller

from frame import *


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--param1", default="param1", help="参数1")
@click.option("--param2", default="param2", help="参数2")
def pyframe_test(job, param1, param2):
    """
    pyframe_test流水线
    示例：
        python devpro.py pyframe_test --job=do_first_job
        python devpro.py pyframe_test --job=do_second_job
    """
    kw = {
        "job": job,
        "param1": param1,
        "param2": param2,
    }
    log.info("params: " + str(kw))
    from project.devpro.pyframe_test import pyframe_test_main

    methodcaller(job, **kw)(pyframe_test_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--param1", default="param1", help="参数1")
@click.option("--param2", default="param2", help="参数2")
def weather_report(job, param1, param2):
    """
    天气预报流水线
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-9a5f3119ac224b279338bc20df096177/history
    示例：
        python devpro.py weather_report --job=report_tomorrow_weather
    """
    kw = {
        "job": job,
        "param1": param1,
        "param2": param2,
    }
    log.info("params: " + str(kw))
    from project.devpro.weather_report import weather_report_main

    methodcaller(job, **kw)(weather_report_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def deploy_skyeye_cockpit_collect(job):
    """
    jenkins流水线构建超时提醒
    流水线地址：
        http://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-b38ec5ca87794fa092e995024b8074b8/preview
    示例：
        python devpro.py deploy_skyeye_cockpit_collect --job=clone_code
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.devpro.deploy_skyeye_cockpit_collect import deploy_skyeye_cockpit_collect_main

    methodcaller(job, **kw)(deploy_skyeye_cockpit_collect_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def docker_deploy_skyeye_cockpit_collect(job):
    """
    驾驶舱collect正式环境部署
    流水线地址：
       https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-0440e0b4fb5a45139b9a9a0189d8ba82/history
    示例：
        python devpro.py docker_deploy_skyeye_cockpit_collect --job=clone_code
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.devpro.docker_deploy_skyeye_cockpit_collect import docker_deploy_skyeye_cockpit_collect_main

    methodcaller(job, **kw)(docker_deploy_skyeye_cockpit_collect_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--deploy_mode", default="", help="部署模式: test, prod")
def liangce_server(job: str, deploy_mode: str):
    """
    部署liangce-server流水线
    测试环境流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-70ffd9657833450584577436db6ceacd
    正式环境流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-f29a03bdbf564a53a1f01f32c0dd3c07
    示例：
        python devpro.py liangce_server --job=prepare --deploy_mode=test
    """
    kw = {"job": job, "deploy_mode": deploy_mode}
    log.info("params: " + str(kw))
    from project.devpro.deploy_liangce_server import deploy_liangce_server_main

    methodcaller(job, **kw)(deploy_liangce_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--deploy_mode", default="", help="部署模式: test, prod")
def liangce_client(job: str, deploy_mode: str):
    """
    部署liangce-client流水线
    测试环境流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-55eb24d868324f53a3057b8f07b58b31
    正式环境流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-263c6b45c2f24af3bf286920553a4586
    示例：
        python devpro.py liangce_client --job=update_client --deploy_mode=test
    """
    kw = {"job": job, "deploy_mode": deploy_mode}
    log.info("params: " + str(kw))
    from project.devpro.deploy_liangce_client import deploy_liangce_client_main

    methodcaller(job, **kw)(deploy_liangce_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def vika_server(job: str):
    """
    部署vika_vika_server流水线
    x51测试环境流水线地址：

    x51正式环境流水线地址：

    x52测试环境流水线地址：

    x52正式环境流水线地址：

    示例：
        python devpro.py vika_server --job=update_client
    """
    kw = {"job": job}
    log.info("params: " + str(kw))
    from project.devpro.deploy_vika_server import deploy_vika_server_main

    methodcaller(job, **kw)(deploy_vika_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def vika_client(job: str):
    """
    部署vika_client流水线
    测试环境流水线地址：

    正式环境流水线地址：

    示例：
        python devpro.py vika_client --job=update_client
    """
    kw = {"job": job}
    log.info("params: " + str(kw))
    from project.devpro.deploy_vika_client import deploy_vika_client_main

    methodcaller(job, **kw)(deploy_vika_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--deploy_mode", default="", help="部署模式: test, prod")
def config_webtool(job, deploy_mode: str):
    """
    部署config-webtool流水线
    测试环境流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-8dbf5898cd8b4ba2a95f2425e1aab9a7
    正式环境流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-ded38c1f4ff94940a8484ae2a6512296

    示例：
        python devpro.py deploy_config_webtool --job=update_config_webtool --deploy_mode=test
    """
    kw = {"job": job, "deploy_mode": deploy_mode}
    log.info("params: " + str(kw))
    from project.devpro.deploy_config_webtool import deploy_config_webtool_main

    methodcaller(job, **kw)(deploy_config_webtool_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def jenkins_job_disable_enable(job: str):
    """
    操作jenkins流水线，比如禁用、重启、启用等
    """
    from project.devpro.jenkins_job_disable_enable import jenkins_job_disable_enable_main

    methodcaller(job)(jenkins_job_disable_enable_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def submit_tool(job: str):
    """
    手游提交工具部署流水线
    正式环境流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-63d830ba33424558beaee9098c77a280/history
    示例：
        python devpro.py deploy_submit_tool --job=update_config_webtool --deploy_mode=test
    """
    kw = {"job": job}
    log.info("params: " + str(kw))
    from project.devpro.deploy_submit_tool import deploy_submit_tool_main

    methodcaller(job, **kw)(deploy_submit_tool_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def cron_notify(job):
    """
    定时提醒
    流水线地址：
    示例：
        python devpro.py cron_notify --job=daily_report
    """
    kw = {
        "job": job,
    }
    from project.devpro.cron_notify import cron_notify_main

    methodcaller(job, **kw)(cron_notify_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--job_name", default="", help="任务名称")
@click.option("--build_number", default=0, help="构建号")
def abort_jenkins_job(job, job_name, build_number):
    """
    强制关闭jenkins构建
    示例：
        python3 devops.py abort_jenkins_job --job abort_building --job_name discard/demos/timeline_package_test --build_number 0
    """
    kw = {
        "job": job,
        "job_name": job_name,
        "build_number": build_number,
    }
    log.info("params: " + str(kw))
    from project.devpro.abort_jenkins_job import abort_jenkins_job_main

    methodcaller(job, **kw)(abort_jenkins_job_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def get_bk_agent_info(job: str):
    """
    获取蓝盾agent信息
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-b105a6f842784f128a092cc86f9d895c/history
    示例：
        python devpro.py get_bk_agent_info --job=get_project_agent
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.devpro.get_bk_agent_info import get_bk_agent_info_main

    methodcaller(job, **kw)(get_bk_agent_info_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def get_jenkins_agent_info(job: str):
    """
    获取Jenkins节点信息
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-0334ed8c48bb4dd0bf4b9d76b7339fb6/history
    示例：
        python devpro.py get_jenkins_agent_info --job=get_agent_info
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.devpro.get_jenkins_agent_info import get_jenkins_agent_info_main

    methodcaller(job, **kw)(get_jenkins_agent_info_main)


if __name__ == "__main__":
    cli()
