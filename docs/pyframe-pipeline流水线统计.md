# pyframe-pipeline流水线统计 #

## x5m

| 序号 | 流水线名称                                                   | 入口函数                  | 所属平台 |
| ---- | ------------------------------------------------------------ | ------------------------- | -------- |
| 1    | [silent包分发-PM](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-6d504df3051b4f9da84cb066b184849d/history) | silent_distribution       | 蓝盾     |
| 2    | [lite包分发-PM](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-5bc3d25cb04f4727962d3cc6d0280e5d/history) | lite_distribution         | 蓝盾     |
| 3    | [清理服务器-研效](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-d70ace1bf0c74439a5145539293a1e01/history) | clean_server              | 蓝盾     |
| 4    | [清理150.30ftp-研效](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-104a8c89e6bf4b6d89c9b7ccd91439b9/history) | clean_ftp_150_30          | 蓝盾     |
| 5    | [服务器定时编译-研效](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-ff269a1126a94630af13f452ec3447c7/history) | build_server              | 蓝盾     |
| 6    | [IOS打包上传bugly-程序](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-ed1a8d7d572a4f66a50a71c6a4018b8e/history) | bugly_upload              | 蓝盾     |
| 7    | [安装包分发-PM](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-32605c5f0cf44b478785a63112e5a3a2/history) | installation_distribution | 蓝盾     |
| 8    | [服务器core监控](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-f05f5fb705e141898148efe09595a7fc/history) | core_dump_monitor         | 蓝盾     |
| 9    | [音效打包-美术-新](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-1c49b124300c4313ae47ea39646ffb4d/history) | music_effect_package      | 蓝盾     |
| 10   | [名片打包-美术](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-202a1331df5b465ba19f4f65b12c4a5f/history) | card_package              | 蓝盾     |
| 11   | [公服部署(多条)](https://bk-devops.h3d.com.cn/console/pipeline/dgm/list/odpenxmq) | deploy_public_server      | 蓝盾     |
| 12   | [私服部署(多条)](https://bk-devops.h3d.com.cn/console/pipeline/dgm/list/odpenxmq) | deploy_private_server     | 蓝盾     |
| 13   | [Ptr、Rc、Final发布工具-PM](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-09c07eeb73fa470cb099b125ff873fba/history) | publish_ptr_rc_final      | 蓝盾     |
| 14   | [美术资源手动触发打包](http://jenkins-x5mobile.h3d.com.cn/job/manual_package_trigger/) | manual_package_trigger    | jenkins  |
| 15   | [公服重启](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-ff62ed6af5d44c74893ce3926ffd1c53/history) | restart_public_server     | 蓝盾     |
| 16   | [编译gm工具](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-212a1e6182a14621b5e4a2069f8c90f7/history) | build_gm_tool             | 蓝盾     |
| 17   | [CDN-Silent下载检查-测试](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-848c883ae0f844b9a418233c1636c384/history) | cdn_silent_download_check | 蓝盾     |
| 18   | [二进制转换](http://jenkins-x5mobile.h3d.com.cn/job/xml_change_byte/) | xml_to_byte               | jenkins  |
| 19   | [客户端打包-android](http://jenkins-x5mobile.h3d.com.cn/job/%E6%B5%81%E6%B0%B4%E7%BA%BF%E7%BB%84%E9%87%8D%E6%9E%84/job/package-android/) | package_android_client    | jenkins  |
| 20   | [silent打包-android](http://jenkins-x5mobile.h3d.com.cn/job/%E6%B5%81%E6%B0%B4%E7%BA%BF%E7%BB%84%E9%87%8D%E6%9E%84/job/silent_package_android/) | silent_package_android    | jenkins  |
| 21   | [silent打包-ios](http://jenkins-x5mobile.h3d.com.cn/job/%E6%B5%81%E6%B0%B4%E7%BA%BF%E7%BB%84%E9%87%8D%E6%9E%84/job/silent_package_ios/) | silent_package_ios        | jenkins  |
| 22   | [服务器刷热更(多条)](https://bk-devops.h3d.com.cn/console/pipeline/dgm/list/allPipeline) | refresh_hotfix            | 蓝盾     |
| 23   | [私服重启](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-65d7ff69f01245dd847a41b78b7a03e1/history) | restart_private_server    | 蓝盾     |
| 24   | [关闭私服](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-2a15254d44ba4f82be43364759eca698/history) | close_private_server      | 蓝盾     |
| 25   | 查找p4临时文件                                               | find_tmp_in_p4            | 手动触发 |
| 26   | [Timeline预览图-研效](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-ce75ba7b480b480ca0722a2a85d881ef/history) | timeline_capture_picture  | 蓝盾     |
| 27   | [Timeline录屏-研效](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-03162e64e6034b7a88a712b236fdf0a5/history) | timeline_capture_video    | 蓝盾     |
| 28   | [Timeline资源更新-研效](https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-b04f645bdcd7404093e77fc34b614d1f/history) | timeline_capture_update   | 蓝盾     |
| 29   | [客户端手动打包-ios](http://jenkins-x5mobile.h3d.com.cn/job/%E6%B5%81%E6%B0%B4%E7%BA%BF%E7%BB%84%E9%87%8D%E6%9E%84/job/lixin02-ios/) | package_ios_client        | jenkins  |

## x51

| 序号 | 流水线名称                                                   | 入口函数                      | 所属平台 |
| ---- | ------------------------------------------------------------ | ----------------------------- | -------- |
| 1    | [x51清理服务器-研效](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-8fae74ae94b34633beb04c455b2cca7d/history) | clean_server                  | 蓝盾     |
| 2    | [服务器部署-美术](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-04caf2cc55144d44a83113beb8db2ec7/history) | art_deploy_server             | 蓝盾     |
| 3    | [客户机部署-美术](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-fd6de3c8bdd1468093c07126c13a630f/history) | art_deploy_client             | 蓝盾     |
| 4    | [客户机环境更新-美术liutq测试](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-d6737cc9675c49d4b4a4327d12da0bab/history) | art_update_client_environment | 蓝盾     |
| 5    | [服务器部署-策划](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-6deac72c58ef4058b7a5472dc82b3d85/history) | plan_deploy_server            | 蓝盾     |
| 6    | [修改游戏时间-策划](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-35a84a1b2a7544c7b2827e2a1288a379/history) | plan_modify_game_time         | 蓝盾     |
| 7    | [重启服务端-策划](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-d0eb39ab594f4b8fbd53d172125176ab/detail/b-5c7a82f1e793426ba91ac5d648e3db70) | plan_restart_server           | 蓝盾     |
| 8    | [服务器部署-测试](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-7aa44d3518674d6b96f3b1da64c0921b/history) | test_deploy_server            | 蓝盾     |
| 9    | [服务器core监控](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-80e260dca005495d9082fc940f404686/history) | core_dump_monitor             | 蓝盾     |
| 10   | [idip部署-程序](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-337bc2c423a8422f85d8c752d9ea106f/history) | publish_idip                  | 蓝盾     |
| 11   | [x5bbs部署-程序](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-18044643a22b4d39a9a886fc312da128/history) | publish_bbs                   | 蓝盾     |
| 12   | [掌炫论坛提审服and现网代码发布](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-4c8c399ac8ff451c94e47dd9f59356f7/history) | publish_forum                 | 蓝盾     |
| 13   | [定时清理x51共享目录-研效](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-198145fe650a48f5b9309f2a925ea74d/history) | clean_shared_space            | 蓝盾     |
| 14   | [替换服务器Shop](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-79a535079ab447998646d72e437bee64/detail/b-c904383cecf64d5d9d99826d8a078f2f) | replace_server_shop           | 蓝盾     |
| 15   | [替换客户端Shop](https://bk-devops.h3d.com.cn/console/pipeline/x51/p-79a535079ab447998646d72e437bee64/detail/b-c904383cecf64d5d9d99826d8a078f2f) | replace_client_shop           | 蓝盾     |

## x52

| 序号 | 流水线名称                                                   | 入口函数              | 所属平台 |
| ---- | ------------------------------------------------------------ | --------------------- | -------- |
| 1    | [x52清理服务器](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-2dd5f9dc91ef4612bffcffebccd6136b/history) | clean_server          | 蓝盾     |
| 2    | [编译服务器(多条)](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-d41117d07e114609a06b265fdf055418/history) | build_server          | 蓝盾     |
| 3    | [服务器core监控](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-7ffcb4e28a614df18a240a8532ab0292/history) | core_dump_monitor     | 蓝盾     |
| 4    | [版本发布-测试](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-8f4ccb5e2e0d4ae8a4fb5956e869b545/history) | publish_versions      | 蓝盾     |
| 5    | [改版本号-测试](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-bbd03d4539e34908ab3716338e77fec3/history) | update_client_version | 蓝盾     |
| 6    | [删除不存在物品-测试](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-204627b236fb4567be097fe4c957eb51/history) | delete_not_exist_item | 蓝盾     |
| 7    | [定时清理x52共享目录-研效](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-f83c95dcf92947a180342db868efc3da/history) | clean_shared_space    | 蓝盾     |
| 8    | [美术服务器部署pyframe](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-bd21137844184e02b1a9a55e251ab4e5/history) | art_deploy_server     | 蓝盾     |
| 9    | [服务器部署-测试](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-3c6c70d4db06439eaacf26222bf2860e/history) | test_deploy_server    | 蓝盾     |
| 10   | [替换Shop.xml-测试](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-b6d327043cfc4fd1b2aedf9b6464cb81/history) | replace_shop_xml      | 蓝盾     |
| 11   | [替换大奖池-美术](https://bk-devops.h3d.com.cn/console/pipeline/x52/p-c26a7243ec16485bac2bcbe8885c2f31/history) | replace_prize_pool    | 蓝盾     |

## framw

| 序号 | 流水线名称                                                   | 入口函数           | 所属平台 |
| ---- | ------------------------------------------------------------ | ------------------ | -------- |
| 1    | [编译go服务器](http://jenkins-framw.h3d.com.cn/job/framw-go-server/) | build_go_server    | jenkins  |
| 2    | [编译unity客户端](http://jenkins-framw.h3d.com.cn/job/framw-unity-android/) | build_unity        | jenkins  |
| 3    | [编译unity to_lua](http://jenkins-framw.h3d.com.cn/view/tolua/) | to_lua             | jenkins  |
| 4    | [编译unity服务器](http://jenkins-framw.h3d.com.cn/view/server/job/framw-unity-server/) | build_unity_server | jenkins  |
| 5    | [编译ue5服务器](http://jenkins-framw.h3d.com.cn/job/framw-ue5-ts-server/) | build_ue5_server   | jenkins  |
| 6    | [编译ue5](http://jenkins-framw.h3d.com.cn/view/ue5-ts/)      | build_ue5          | jenkins  |

## pipeline

| 序号 | 流水线名称                                                   | 入口函数                     | 所属平台 |
| ---- | ------------------------------------------------------------ | ---------------------------- | -------- |
| 1    | [创建pyframe合并请求](https://bk-devops.h3d.com.cn/console/pipeline/tac-pipeline/p-d093cf8c81674d7e9bbbfd8f7b1f281a/history) | create_pyframe_merge_request | 蓝盾     |

## team1

| 序号 | 流水线名称 | 入口函数              | 所属平台 |
| ---- | ---------- | --------------------- | -------- |
|      |            | build_m1_unity_client |          |

## testdev

| 序号 | 流水线名称                                                   | 入口函数                   | 所属平台 |
| ---- | ------------------------------------------------------------ | -------------------------- | -------- |
| 1    | [x51构建mysql镜像并推送](https://bk-devops.h3d.com.cn/console/pipeline/tac-td/p-da2b5a33726a41edaf77c49535fb06ea/history) | build_x51_mysql_image      | 蓝盾     |
| 2    | [x51构建服务器镜像](https://bk-devops.h3d.com.cn/console/pipeline/tac-td/p-203728ae8da24289bff98ccbc54c5837/history) | build_x51_server_image     | 蓝盾     |
| 3    | [test-x52镜像打包](https://bk-devops.h3d.com.cn/console/pipeline/tac-td/p-90b7558dc8b44df09ad0ced1707c085f/history) | build_x52_server_image     | 蓝盾     |
| 4    | [(pyframe)x51活动测试](https://bk-devops.h3d.com.cn/console/pipeline/tac-td/p-9c040eaa1bee4e94841edabdcc92936d/history) | functional_test            | 蓝盾     |
| 5    | [x51周年庆测试](https://bk-devops.h3d.com.cn/console/pipeline/tac-td/p-bca11c468b214615844051b354050161/history) | functional_test_controller | 蓝盾     |
| 6    | [强制解除机器占用](https://bk-devops.h3d.com.cn/console/pipeline/tac-td/p-17725ef716374a069c9f0ec65d748805/history) | return_machine             | 蓝盾     |
| 7    | [whitebox_config_check](http://jenkins-testdev.h3d.com.cn/job/whitebox-x5m/) | x5m_whitebox               | jenkins  |
| 8    | [监控测开Jenkins执行超时](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-926814c254cb401283e78581d6015b94/history) | jenkins_duration_alarm     | 蓝盾     |
| 9    | [调起白盒打镜像](http://jenkins-testdev.h3d.com.cn/job/x5m_build_server_image/) | build_x5m_server_image     | jenkins  |

## devops

| 序号 | 流水线名称                                                   | 入口函数                   | 所属平台 |
| ---- | ------------------------------------------------------------ | -------------------------- | -------- |
| 1    | [强制关闭jenkins构建](http://jenkins-x5mobile.h3d.com.cn/job/abort_jenkins_building/) | abort_building_job         | jenkins  |
| 2    |                                                              | build_x51_client_codecheck |          |
| 3    |                                                              | deploy_distask_client      |          |

## devpro

| 序号 | 流水线名称                                                   | 入口函数                             | 所属平台 |
| ---- | ------------------------------------------------------------ | ------------------------------------ | -------- |
| 1    | [天气预报](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-9a5f3119ac224b279338bc20df096177/history) | weather_report                       | 蓝盾     |
| 2    | [监控手游Jenkins执行超时](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-7548cba26dd140b3b495bd34316d3e31/detail/b-bc15a2886bbe4e34b0359eaa0e19588c) | jenkins_duration_alarm               | 蓝盾     |
| 3    | [驾驶舱collect正式环境部署-研效](http://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-0440e0b4fb5a45139b9a9a0189d8ba82/history) | docker_deploy_skyeye_cockpit_collect | 蓝盾     |
| 4    | [pyframe功能测试](http://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-e7210494b40047b6941954bd68da15d0/detail/b-04c72856c2ee49ae9e40fd72c9ec942f) | pyframe_test                         | 蓝盾     |
| 5    | [良策server正式环境部署-研效](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-f29a03bdbf564a53a1f01f32c0dd3c07) | liangce_server                       | 蓝盾     |
| 6    | [良策client正式环境部署-研效](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-263c6b45c2f24af3bf286920553a4586) | liangce_client                       | 蓝盾     |
| 7    | [部署vika_vika_server流水线](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-061dffaa5f2a413ea6f78d02bb2a8129/history) | vika_server                          | 蓝盾     |
| 8    | [维格客户端测试环境部署](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-2d328d7130eb4bed84808ebad1cd4374/history) | vika_client                          | 蓝盾     |
| 9    | [配置转换正式环境部署流水线](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-ded38c1f4ff94940a8484ae2a6512296) | config_webtool                       | 蓝盾     |
| 10   | [操作炫舞手游jenkins](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-b072bb251315474ab42256007b5746fb/history) | jenkins_job_disable_enable           | 蓝盾     |
| 11   | [git自动merge](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-d580cd4b99fd4110ba293f305e9bf7e0/history) | pyframe_auto_merge                   | 蓝盾     |
| 12   | [手游提交工具部署流水线](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-63d830ba33424558beaee9098c77a280/history) | submit_tool                          | 蓝盾     |
| 13   | [炫舞手游日报提醒](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-7398881ceb2b443c9e549c4ecb97a897/history) | cron_notify                          | 蓝盾     |
| 14   | [强制关闭jenkins构建](http://jenkins-x5mobile.h3d.com.cn/job/abort_jenkins_building/) | abort_jenkins_job                    | jenkins  |
| 15   | [获取蓝盾agent信息](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-b105a6f842784f128a092cc86f9d895c/history) | get_bk_agent_info                    | 蓝盾     |
| 16   | [获取Jenkins上agent信息](https://bk-devops.h3d.com.cn/console/pipeline/dev-productivity/p-0334ed8c48bb4dd0bf4b9d76b7339fb6/history) | get_jenkins_agent_info               | 蓝盾     |

## A1

| 序号 | 流水线名称 | 入口函数             | 所属平台 |
| ---- | ---------- | -------------------- | -------- |
| 1    |            | auto_gen_test_config |          |
| 2    |            | pyframe_test         |          |

## A2

| 序号 | 流水线名称 | 入口函数     | 所属平台 |
| ---- | ---------- | ------------ | -------- |
| 1    |            | pyframe_test |          |

## A3

| 序号 | 流水线名称 | 入口函数     | 所属平台 |
| ---- | ---------- | ------------ | -------- |
| 1    |            | pyframe_test |          |

## m2

| 序号 | 流水线名称 | 入口函数         | 所属平台 |
| ---- | ---------- | ---------------- | -------- |
| 1    |            | build_ue5_client |          |
| 2    |            | git_to_p4        |          |

