# coding=utf-8
import os
import sys
from pathlib import Path, PosixPath

# 控制是否调试模式
# DEBUG = False
# 流水线组名单，用于在流水线异常时第一时间通知流水线组
MAINTAINER_LIST = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "ji<PERSON><PERSON><PERSON><PERSON>@h3d.com.cn",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "yang<PERSON><PERSON><PERSON>@h3d.com.cn",
    "<EMAIL>",
]

# 流水线值班人，用于在群通知中@值班人
MAINTAINER_ON_CALL_LIST = [
    # "<EMAIL>",
]


# 获取项目类型
PROJECT = "pytest"
PIPELINE_FUNC = "pytest_func"
if os.path.basename(sys.argv[0]).endswith(".py"):
    __filename = os.path.basename(sys.argv[0]).split(".")[0]
    if __filename in [
        "devops",
        "devpro",
        "framw",
        "m2",
        "pipeline",
        "team1",
        "team3",
        "team4",
        "testdev",
        "x5m",
        "x51",
        "x52",
    ]:
        PROJECT = __filename
        PIPELINE_FUNC = sys.argv[1]
    if "trigger/" in sys.argv[0]:
        PROJECT = f"trigger_{__filename}"
        PIPELINE_FUNC = __filename
    if "service/" in sys.argv[0]:
        PROJECT = f"service_{__filename}"
        PIPELINE_FUNC = __filename


# 检查python版本
def check_python_version():
    major_version, minor_version, micro_version = sys.version_info.major, sys.version_info.minor, sys.version_info.micro
    if major_version != 3:
        print(f"current python version is {major_version}.{minor_version}.{micro_version}, but python 3 is required")
        sys.exit(-1)
    if minor_version != 6 and minor_version != 9:
        print(f"current python version is {major_version}.{minor_version}.{micro_version}, but python 3.6.8 or 3.9.12 is required")
        sys.exit(-1)


check_python_version()


import click
from frame.env.env import env
from frame.common.common import common
from frame.log.log import log
from frame.wechat.wechat import wechat, PipelineStatus
from frame.ftp.ftp import Ftp
from frame.ftp.ftp_mgr import FtpMgr
from frame.p4.p4 import Perforce
from frame.p4.p4client import P4Client
from frame.tar.tar import tar
from frame.nexus.nexus import Nexus
from frame.cmd.cmd import cmd
from frame.cmd.cmd import Cmd
from frame.path_mgr.path_mgr import path_mgr
from frame.file_mgr.file_mgr import file_mgr
from frame.db.mysql_client import MysqlClient
from frame.db.mongo_mgr import MongoMgr
from frame.proc_mgr.proc_mgr import proc_mgr
from frame.docker.docker import Docker
from frame.docker.docker_compose import DockerCompose
from frame.git_mgr.git_mgr import GitMgr
from frame.gitlab_mgr.gitlab_mgr import GitlabMgr
from frame.exception.exception import PyframeException, UnityInvalidLicenseException
from frame.svn.svn import Svn
from frame.advance.advance import advance
from frame.pipeline_mgr.pipeline_mgr import pipeline_mgr
from frame.pipeline_mgr.jenkins.jenkins_mgr import jenkins_mgr
from frame.api.api import api
from frame.smb_mgr.smb_client import SMBClient

from frame.markdown.markdown import Markdown, Table

# 初始化click
cli = click.Group(context_settings={"token_normalize_func": lambda name: name.replace("_", "-")})
