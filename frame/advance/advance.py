# coding=utf-8

"""
advance.py
pyframe的高级功能模块，需要引用pyframe的其他模块
"""

import os
import platform
import posixpath

from functools import wraps
from typing import Optional

from func_timeout import func_timeout, FunctionTimedOut

from frame import common, wechat, env, log, cmd
from frame.nexus.nexus import Nexus
from frame.exception.exception import PyframeException
from frame.advance.pipeline_history.pipeline_history import PipelineHistory
from frame.advance.unity.unity import Unity
from frame.advance.unreal_engine.unreal_engine import UnrealEngine
from frame.advance.distcc.distcc import Distcc
from frame.advance.incredibuild.incredibuild import IncrediBuild


class Advance:
    @property
    def unity(self) -> Unity:
        return Unity()

    @property
    def unreal_engine(self) -> UnrealEngine:
        return UnrealEngine()

    @property
    def distcc(self) -> Distcc:
        return Distcc()

    @property
    def incredibuild(self) -> IncrediBuild:
        return IncrediBuild()

    def stage(self, stage: str):
        """
        流水线stage的装饰函数
        """

        def decorate(func):
            @wraps(func)
            def wrap(*args, **kwargs):
                # log.debug(args, kwargs)
                try:
                    result = func(*args, **kwargs)
                except Exception as e:
                    env.set({"PYFRAME_STAGE": stage})
                    raise PyframeException(e.__str__())

                return result

            return wrap

        return decorate

    @staticmethod
    def stage_with_exception(stage: str, exception):
        """
        抛出指定异常的流水线stage的装饰函数
        """

        def decorate(func):
            @wraps(func)
            def wrap(*args, **kwargs):
                try:
                    result = func(*args, **kwargs)
                except PyframeException as e:
                    env.set({"PYFRAME_STAGE": stage})
                    raise exception(e.__str__())

                return result

            return wrap

        return decorate

    def check_disk_size(self, threshold: int):
        """
        检查磁盘剩余空间是否可以执行流水线
        """
        free_size = common.get_disk_free_size()
        log.info(f"{common.get_host_ip()} 磁盘空间大小：{free_size}G")
        if free_size <= threshold:
            msg = "**{common.get_host_ip()}磁盘空间不足{threshold}G**"
            wechat.send_unicast_post_failure(user_list=[env.pipeline.pipeline_operator()], content=msg)
            raise PyframeException(f"{common.get_host_ip()}磁盘空间不足{threshold}G, 请及时清理磁盘空间")

    def sync_system_time(self):
        """
        同步系统时间
        """
        if platform.system() == "Linux":
            ret = cmd.run_shell(cmds=["ntpdate -u ntp.api.bz"])
            if ret[0] != 0:
                log.warn("sync system time failed")
            else:
                log.info("sync system time success")

    def get_git_version(self) -> Optional[str]:
        """
        获取git版本
        Returns:
            Optional[str]: git版本
        """
        ret = cmd.run_shell(cmds=["git --version"], return_output=True)
        if ret[0] != 0:
            raise PyframeException(f"git --version failed, 异常: {ret[1]}")
        else:
            for out in ret[1]:
                if "git version" in out:
                    version = out.replace("git version ", "")
                    return version
        return None

    def upload_pipeline_log(self, path: str) -> str:
        """
        上传流水线日志/临时制品到nexus的pipeline-log仓库
        Args:
            path: 日志路径
        Returns:
            str: nexus上日志的url
        """
        branch_name = env.get("BRANCH_NAME")
        if branch_name is None:
            dst = posixpath.join(
                "http://nexus.h3d.com.cn/repository/pipeline-log",
                env.pipeline.project_name(),
                env.pipeline.function_name(),
                env.pipeline.build_num(),
                os.path.basename(path),
            )
        else:
            dst = posixpath.join(
                "http://nexus.h3d.com.cn/repository/pipeline-log",
                env.pipeline.project_name(),
                env.pipeline.function_name(),
                branch_name,
                env.pipeline.build_num(),
                os.path.basename(path),
            )
        nexus = Nexus(username="pipeline-log", password="dgLaNSkWXVIq")
        nexus.upload(src=path, dst=dst)
        return dst

    def timeout(self, seconds: int, exception_msg: str):
        """
        超时装饰器，可能不适用于多线程(未严格测试)
        Args:
            seconds: 超时时间
            exception_msg: 超时后抛出的异常信息, 例如：xxx执行超时60s
        Returns:
            func: 装饰器
        """

        def decorate(func):
            @wraps(func)
            def timeout_wrap(*args, **kwargs):
                try:
                    func_timeout(seconds, func, args=args, kwargs=kwargs)
                except FunctionTimedOut as e:
                    log.warn(f"{e.__str__()}")
                    raise PyframeException(f"{exception_msg}, 超时{seconds}s")

            return timeout_wrap

        return decorate

    @staticmethod
    def reset_pyframe_exception():
        env.set(
            {
                "PYFRAME_EXCEPTION_MESSAGE": None,
                "PYFRAME_FAILURE_STAGE": None,
            }
        )

    @staticmethod
    def raise_unity_log_exception(log_path: str) -> None:
        """
        解析unity日志中的错误, 并抛出异常
        Args:
            log_path: unity日志路径
        """
        unity = Unity()
        unity.raise_unity_log_exception(log_path)

    @staticmethod
    def raise_ue5_log_exception(log_path: str) -> None:
        """
        解析ue5日志中的错误，并抛出异常
        Args:
            log_path: ue5日志路径
        """
        unreal_engine = UnrealEngine()
        unreal_engine.raise_ue_log_exception(log_path)

    @staticmethod
    def get_pyframe_branch() -> str:
        """
        获取pyframe的分支
        Returns:
            pyframe的分支
        """
        ret = cmd.run_shell(
            cmds=[
                "git branch --show-current",
            ],
            workdir="./",
            return_output=True,
        )
        if ret[0] != 0:
            log.error("git branch error")
            raise PyframeException("获取当前分支失败")
        current_branch = ret[1]
        return current_branch[0].strip()

    @staticmethod
    def insert_pipeline_history_on_success():
        pipeline_history = PipelineHistory()
        pipeline_history.create_db()
        pipeline_history.create_table()
        pipeline_history.record(PipelineHistory.PipelineStatus.SUCCESS)

    @staticmethod
    def insert_pipeline_history_on_failure():
        pipeline_history = PipelineHistory()
        pipeline_history.create_db()
        pipeline_history.create_table()
        pipeline_history.record(PipelineHistory.PipelineStatus.FAILURE)

    @staticmethod
    def insert_pipeline_history_on_canceled():
        pipeline_history = PipelineHistory()
        pipeline_history.create_db()
        pipeline_history.create_table()
        pipeline_history.record(PipelineHistory.PipelineStatus.CANCELED)

    @staticmethod
    def insert_pipeline_history_on_unstable():
        pipeline_history = PipelineHistory()
        pipeline_history.create_db()
        pipeline_history.create_table()
        pipeline_history.record(PipelineHistory.PipelineStatus.UNSTABLE)

    @staticmethod
    def delete_pipeline_history():
        """
        仅用于测试用例
        """
        pipeline_history = PipelineHistory()
        pipeline_history.drop_db()


advance = Advance()
