from enum import Enum

import requests

from frame.exception.exception import PyframeException
from frame.log.log import log
from frame.path_mgr.path_mgr import path_mgr


class Distcc:
    def __init__(self) -> None:
        pass

    class System(Enum):
        centos66_with_gcc9 = "centos66-with-gcc9"  # x52镜像环境
        centos72 = "centos72"  # x51镜像环境
        tlinux12 = "tlinux12"  # x5m镜像环境

    def request_distcc_hosts(self, system: System) -> str:
        resp = requests.get(url="http://distcc.tac.com/hosts", params={"env": system.value})
        if resp.status_code != 200:
            log.error(f"request distcc hosts failed, status_code: {resp.status_code}")
            raise PyframeException(f"获取distcc hosts失败, status_code: {resp.status_code}")
        log.debug(f"distcc hosts: {resp.text}")
        return resp.text

    def parse_compile_errors(self, log_path: str) -> list:
        """
        解析编译错误
        Args:
            log_path: distcc日志路径
        """
        compile_errors = []
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            if ": error" in line:
                compile_errors.append(line)
            line = f.readline()
        f.close()
        return compile_errors

    def raise_distcc_log_exception(self, log_path: str) -> None:
        """
        解析distcc日志中的错误, 并抛出异常
        Args:
            log_path: distcc日志路径
        """
        if not path_mgr.exists(log_path):
            log.error(f"distcc log {log_path} not exists")
            return
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            # 解析编译错误
            if "Scripts have compiler errors" in line:
                f.close()
                log.error(f"Scripts have compiler errors")
                compile_errors = self.parse_compile_errors(log_path)
                compile_errors_str = "\n".join(compile_errors)
                raise PyframeException(f"Scripts have compiler errors, 脚本有编译错误, 请程序检查代码问题\n{compile_errors_str}")

            line = f.readline()

    def raise_x5m_distcc_log_exception(self, log_path: str) -> None:
        """
        解析x5m distcc日志中的错误, 并抛出异常
        """
        if not path_mgr.exists(log_path):
            log.error(f"distcc log {log_path} not exists")
            raise PyframeException("distcc编译日志不存在")

        errors = []

        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            # 解析编译错误
            if " error: " in line or "错误：" in line:
                if ".cpp" in line or ".h" in line or ".c" in line or ".hpp" in line or ".cc" in line:
                    if line not in errors:
                        errors.append(line)
            line = f.readline()
        f.close()
        if len(errors) != 0:
            errors_str = "\n".join(errors)
            raise PyframeException(
                message=f"服务器代码有编译错误, 请程序检查代码问题\n{errors_str}",
                mentioned_list=["<EMAIL>"],
            )

        raise PyframeException("有编译错误, 请流水线检查代码问题")

    def raise_x51_distcc_log_exception(self, log_path: str, mentioned_list: list = []) -> None:
        """
        解析x51 distcc日志中的错误, 并抛出异常
        """
        if not path_mgr.exists(log_path):
            log.error(f"distcc log {log_path} not exists")
            return

        errors = []

        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            if "Space not enough!" in line:
                raise PyframeException(f"编译star失败, 磁盘空间不足，请清理后重试")

            if "Build is running! Can't build again" in line:
                raise PyframeException(f"编译star失败, 有其他编译任务正在运行，请稍后重试")

            # if "make: *** [all] Error 2" in line:
            #     raise PyframeException(f"编译star失败，make install failed")
            # 解析编译错误
            if " error: " in line:
                if ".cpp" in line or ".h" in line or ".c" in line or ".hpp" in line or ".cc" in line:
                    if line not in errors:
                        errors.append(line)
            line = f.readline()
        f.close()
        if len(errors) != 0:
            errors_str = "\n".join(errors)
            raise PyframeException(
                message=f"服务器代码有编译错误, 请程序检查代码问题\n{errors_str}",
                mentioned_list=mentioned_list,
            )
