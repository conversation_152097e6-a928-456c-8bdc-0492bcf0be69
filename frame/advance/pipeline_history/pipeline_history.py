# from frame.advance.advance import advance
import datetime
import time
import os

from frame.cmd.cmd import cmd
from frame.common.common import common
from frame.db import mysql_client
from frame.env.env import env
from frame.log.log import log
from frame.wechat.wechat import wechat
from frame.exception.exception import PyframeException


class PipelineHistory:
    class PipelineStatus(enumerate):
        """
        流水线状态
        """

        SUCCESS = "success"
        FAILURE = "failure"
        ABORTED = "aborted"
        CANCELED = "canceled"
        UNSTABLE = "unstable"

    def __init__(self) -> None:
        DB_CONFIG = {
            "host": "*************",
            "port": "6603",
            "username": "root",
            "password": "Horizon#d",
        }
        self.mysql = mysql_client.MysqlClient(**DB_CONFIG)
        self.project_name = env.pipeline.project_name()
        self.function_name = env.pipeline.function_name()
        self.pipeline_name = env.pipeline.pipeline_name()
        self.pipeline_operator = env.pipeline.pipeline_operator()
        self.start_type = env.pipeline.start_type()
        self.build_url = env.pipeline.build_url()
        self.build_num = env.pipeline.build_num()
        self.platform = env.pipeline.platform()
        self.machine_ip = common.get_host_ip()
        self.start_time = env.pipeline.start_time()
        if not self.start_time:
            self.start_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        else:
            if self.start_time.isdigit():
                self.start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(self.start_time) / 1000))
        self.pyframe_branch = self._get_pyframe_branch()
        self.multi_branch = env.get("BRANCH", "")
        self.error_msg = env.get("PYFRAME_EXCEPTION_MESSAGE", "")
        self.operator_email = ""
        if isinstance(self.pipeline_operator, str) and self.pipeline_operator.endswith("@h3d.com.cn"):
            self.operator_email = self.pipeline_operator

    def create_db(self):
        db_name = self.project_name
        if not self.mysql.exists_db(db_name):
            create_success = self.mysql.create_db(db_name)
            if not create_success:
                content = f"**提示:** 创建数据库{db_name}失败，请及时查看"
                return wechat.send_unicast_post_failure(content=content)

    def create_table(self):
        db_name = self.project_name
        table_name = self.function_name
        if not self.mysql.exists_table(db_name, table_name):
            table_sql = """
CREATE TABLE IF NOT EXISTS `{table_name}`
(
    `id`                int          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `pipeline_name`     varchar(255) NOT NULL COMMENT '名称',
    `function_name`     varchar(100) NOT NULL COMMENT '入口函数',
    `start_type`        varchar(100) NOT NULL COMMENT '启动方式',
    `pipeline_operator` varchar(100) NOT NULL COMMENT '操作人',
    `operator_email`    varchar(100) DEFAULT '' COMMENT '操作人邮箱',
    `build_url`         varchar(255) NOT NULL COMMENT '构建链接',
    `build_num`         varchar(100) NOT NULL COMMENT '构建号',
    `platform`          varchar(100) NOT NULL COMMENT '平台',
    `project_name`      varchar(100) NOT NULL COMMENT '项目名称',
    `machine_ip`        varchar(128) DEFAULT '' COMMENT '机器IP',
    `pyframe_branch`    varchar(32)  NOT NULL COMMENT 'pyframe分支',
    `multi_branch`      varchar(128)  DEFAULT '' COMMENT '多分支信息',
    `error_msg`         text COMMENT '报错信息',
    `status`            varchar(100) NOT NULL COMMENT '状态',
    `is_repeat`         BOOLEAN      DEFAULT FALSE COMMENT '是否重复执行',
    `start_time`        DATETIME     DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `end_time`          DATETIME     DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
    `build_duration`    int DEFAULT 0 COMMENT '总耗时',
    `create_time`       DATETIME     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       DATETIME     DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    INDEX UNION_IND (project_name, function_name, build_num, multi_branch, machine_ip)
);
                """
            create_success = self.mysql.create_table(table_sql.format(table_name=table_name), db_name)
            create_view_success = self.create_or_replace_pipeline_view()
            if not create_success:
                content = f"**提示:** 创建数据表{table_name}失败，请及时查看"
                return wechat.send_unicast_post_failure(content=content)
            elif not create_view_success:
                content = f"**提示:** 创建视图失败，请及时查看"
                return wechat.send_unicast_post_failure(content=content)

    @staticmethod
    def _get_pyframe_branch() -> str:
        """
        获取pyframe的分支
        Returns:
            pyframe的分支
        """
        pyframe_branch = os.getenv("GIT_BRANCH", "")
        if pyframe_branch:
            pyframe_branch = pyframe_branch.strip().replace("origin/", "")
        try:
            ret = cmd.run_shell(
                cmds=[
                    "git branch --show-current",
                ],
                workdir="./",
                return_output=True,
            )
            if ret[0] != 0:
                log.error("git branch error")
                return pyframe_branch
            current_branch = ret[1]
            pyframe_branch = current_branch[0].strip()
        except Exception as e:
            log.error(e)
        return pyframe_branch

    def exist_record(self) -> bool:
        """
        判断是否存在历史执行明细
        Returns:
            是否存在
        """
        sql = f"SELECT * FROM `{self.project_name}`.`{self.function_name}` WHERE project_name = '{self.project_name}' AND function_name = '{self.function_name}' AND build_num = '{self.build_num}' AND multi_branch = '{self.multi_branch}' AND machine_ip = '{self.machine_ip}'"

        ret = self.mysql.select_one(sql)
        log.debug(f"{ret}")
        return bool(ret)

    def record(self, status: PipelineStatus) -> None:
        """
        记录历史执行明细
        """
        try:
            if self.exist_record():
                self.update(status=status)
            else:
                self.insert(status=status)
        except Exception as e:
            log.error(e)
            content = f"**提示:** 记录数据库详情失败，请及时查看"
            return wechat.send_unicast_post_failure(content=content)

    def insert(self, status: PipelineStatus) -> None:
        """
        插入新纪录
        """
        build_duration = env.pipeline.build_duration_seconds()
        status = status
        is_repeat = False
        insert_sql = f"""
INSERT INTO `{self.project_name}`.`{self.function_name}` (pipeline_name, function_name, start_type, pipeline_operator, operator_email, build_url, build_num, platform, project_name, machine_ip, pyframe_branch, multi_branch, error_msg, status, is_repeat, start_time, build_duration) VALUES ('{self.pipeline_name}', '{self.function_name}', '{self.start_type}', '{self.pipeline_operator}', '{self.operator_email}', '{self.build_url}', '{self.build_num}', '{self.platform}', '{self.project_name}', '{self.machine_ip}', '{self.pyframe_branch}', '{self.multi_branch}', '{self.error_msg}', '{status}', {int(is_repeat)}, '{self.start_time}', '{build_duration}');
"""
        try:
            self.mysql.insert_one(sql=insert_sql)
        except Exception as e:
            log.error(e)

    def update(self, status: PipelineStatus) -> None:
        """
        更新已存在的记录
        """
        build_duration = env.pipeline.build_duration_seconds()
        status = status
        is_repeat = True
        update_sql = f"""
UPDATE `{self.project_name}`.`{self.function_name}`
SET pipeline_name = '{self.pipeline_name}',
    start_type = '{self.start_type}',
    pipeline_operator = '{self.pipeline_operator}',
    operator_email = '{self.operator_email}',
    build_url = '{self.build_url}',
    platform = '{self.platform}',
    machine_ip = '{self.machine_ip}',
    pyframe_branch = '{self.pyframe_branch}',
    error_msg = '{self.error_msg}',
    status = '{status}',
    is_repeat = '{int(is_repeat)}',
    start_time = '{self.start_time}',
    build_duration = '{build_duration}'
WHERE project_name = '{self.project_name}'
    AND function_name = '{self.function_name}'
    AND build_num = '{self.build_num}'
    AND multi_branch = '{self.multi_branch}'
    AND machine_ip = '{self.machine_ip}'
        """
        try:
            self.mysql.update_one(update_sql)
        except Exception as e:
            log.error(e)

    def drop_db(self):
        self.mysql.drop_db(self.project_name)

    def get_tables_of_db(self, black_dbs=None, white_dbs=None):
        """
        获取数据库中所有表
        Returns:
            所有表，示例：
            [{'db_name': 'devops', 'table_name': 'build_x51_client_codecheck'}, {'db_name': 'devpro', 'table_name': 'abort_jenkins_job'}]
        """
        query = """SELECT TABLE_SCHEMA AS db_name, TABLE_NAME AS table_name FROM information_schema.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"""

        if black_dbs:
            if not isinstance(black_dbs, list):
                raise PyframeException("参数black_dbs必须是列表")

            black_dbs = ["'" + item + "'" for item in black_dbs]
            query += f""" AND TABLE_SCHEMA NOT IN ({",".join(black_dbs)})"""
        if white_dbs:
            if not isinstance(white_dbs, list):
                raise PyframeException("参数black_dbs必须是列表")

            white_dbs = ["'" + item + "'" for item in white_dbs]
            query += f""" AND TABLE_SCHEMA IN ({",".join(white_dbs)})"""

        return self.mysql.select_all(query)

    def create_or_replace_pipeline_view(self, db_name: str = "pipeline_full_data", view_name: str = "pipeline_view", month: int = 6) -> bool:
        """
        创建聚合视图
        :param db_name: 视图所在数据库名称
        :param view_name: 视图名称
        :param month: 查询近几个月的数据作为视图源数据
        :return:
        """
        black_dbs = ["mysql", "information_schema", "performance_schema", "pipeline_full_data", "sys", "pytest", "api_service"]

        all_databases_and_tables = self.get_tables_of_db(black_dbs=black_dbs)
        log.info(all_databases_and_tables)

        # 为每个表生成UNION ALL子句
        union_all_clauses = []
        for db_table in all_databases_and_tables:
            database = db_table["db_name"]
            table = db_table["table_name"]
            union_all_clauses.append(
                f"SELECT *,'{database}' AS db FROM {database}.`{table}` WHERE `create_time` >= DATE_SUB(NOW(), INTERVAL {month} MONTH)"
            )

        select_sql = " UNION ALL ".join(union_all_clauses)
        return self.mysql.create_or_replace_view(db_name=db_name, view_name=view_name, select_sql=select_sql)
