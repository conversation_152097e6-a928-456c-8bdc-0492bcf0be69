# import platform
# import random
# import time
# from pathlib import Path
# from msedge.selenium_tools import Edge, EdgeOptions
#
# from frame.log.log import log
# from frame.exception.exception import PyframeException
# from frame.cmd.cmd import cmd
# from frame.common.common import Common
# from frame.advance.unity.const import OFFICIALS, PERSONAL_ACCOUNTS, WIN_DOWNLOAD_PATH, MAC_DOWNLOAD_PATH, LOGIN_URL, \
#     LICENSE_URL, WIN_DRIVER_PATH, MAC_DRIVER_PATH
# from abc import ABC, abstractmethod
#
#
# class UnityActivateBase(ABC):
#     official_ips = OFFICIALS.keys()
#     host_ip = Common().get_host_ip()
#     official = host_ip in official_ips
#     windows = "windows" in platform.platform().lower()
#
#     # def __new__(cls, *args, **kwargs):
#     #     if not cls.official and not cls.windows:
#     #         raise PyframeException("自动激活个人Unity目前只支持Windows系统")
#     #     return super().__new__(cls)
#
#     def __init__(self, unity_cmd: str):
#         self.unity_cmd = unity_cmd
#
#     @abstractmethod
#     def activate_unity(self):
#         pass
#
#
# class UnityActivatePersonal(UnityActivateBase):
#     edge_options = EdgeOptions()
#     # edge_options.add_argument("headless")
#
#     def __init__(self, unity_cmd: str):
#         super(UnityActivatePersonal, self).__init__(unity_cmd)
#         self.download_path = WIN_DOWNLOAD_PATH if self.windows else MAC_DOWNLOAD_PATH
#         self.driver_path = WIN_DRIVER_PATH if self.windows else MAC_DRIVER_PATH
#         self.browser = Edge(executable_path=self.driver_path, options=self.edge_options)
#         self.account = random.choice(PERSONAL_ACCOUNTS)
#
#     def gen_alf(self) -> str:
#         command = f'"{self.unity_cmd}" -batchmode -createManualActivationFile'
#         cmd.run_shell([command])
#         alf_file = ''
#         for i in Path.cwd().iterdir():
#             if i.name.endswith('.alf'):
#                 alf_file = str(i.absolute())
#                 break
#
#         log.info(f"alf_file: {alf_file}")
#         if not alf_file:
#             raise PyframeException("生成alf文件失败")
#         return alf_file
#
#     def login_unity(self):
#         self.browser.get(LOGIN_URL)
#         account_login = self.browser.find_element_by_link_text('Account Login')
#         if account_login:
#             account_login.click()
#             time.sleep(5)
#             self.browser.find_element_by_xpath('//*[@id="content-wrapper"]/div/div/div/div/ul/li[2]/a').click()
#
#         time.sleep(5)
#
#         username_input = self.browser.find_element_by_id('conversations_create_session_form_email')
#         username_input.send_keys(self.account.get("username"))
#
#         password_input = self.browser.find_element_by_id('conversations_create_session_form_password')
#         password_input.send_keys(self.account.get("password"))
#
#         self.browser.find_element_by_name('commit').click()
#
#         time.sleep(5)
#         log.info("登录unity成功")
#
#     def upload_alf(self):
#         self.browser.get(LICENSE_URL)
#
#         # 定位上传按钮，添加本地文件
#         self.browser.find_element_by_id("licenseFile").send_keys(self.gen_alf())
#         time.sleep(5)
#         # 重新定位一次
#         self.browser.find_element_by_id("licenseFile").send_keys(self.gen_alf())
#         self.browser.find_element_by_name('commit').click()
#         time.sleep(5)
#
#     def choose_personal_option(self):
#         # 选择个人
#         self.browser.find_element_by_xpath("/html/body/div/div/section/div[2]/div/div[2]/div[1]/label/h3").click()
#         time.sleep(2)
#
#         # 选择第三个选项
#         self.browser.find_element_by_xpath(
#             "/html/body/div/div/section/div[2]/div/div[2]/div[2]/div[3]/label").click()
#         time.sleep(2)
#
#         # 点击下一步
#         self.browser.find_element_by_xpath("/html/body/div/div/section/div[2]/div/div[2]/div[2]/input").click()
#         time.sleep(5)
#
#         # alert = self.browser.find_element_by_class_name('notification-alert')
#         # if alert:
#         #     raise PyframeException("激活个人选项失败，出现达到次数限制警告")
#
#     def download_ulf(self):
#         self.browser.find_element_by_name('commit').click()
#         time.sleep(5)
#
#     def ask_for_license(self):
#         ulf_file = ''
#         for i in Path(self.download_path).iterdir():
#             if i.name.endswith('.ulf'):
#                 ulf_file = str(i.absolute())
#                 break
#
#         log.info(f"ulf_file: {ulf_file}")
#         if not ulf_file:
#             raise PyframeException(f"{self.download_path}路径下未找到ulf文件")
#
#         command = f'"{self.unity_cmd}" -batchmode -manualLicenseFile {ulf_file} -logfile'
#         cmd.run_shell([command])
#         log.info("unity 激活成功")
#
#     def activate_unity(self):
#         try:
#             self.gen_alf()
#             self.login_unity()
#             self.upload_alf()
#             self.choose_personal_option()
#             self.download_ulf()
#             self.ask_for_license()
#         except Exception as e:
#             raise PyframeException(f'激活unity失败：{e}')
#         finally:
#             pass
#             # self.browser.quit()
#
#
# class UnityActivateOfficial(UnityActivateBase):
#     def activate_unity(self):
#         log.info("开始使用命令行激活Unity")
#         if self.host_ip not in OFFICIALS.keys():
#             raise PyframeException(f"OFFICIALS字典中不存在{self.host_ip}, 请联系流水线管理员添加该节点")
#         args = OFFICIALS.get(self.host_ip)
#         serial = args.get("serial")
#         if not serial:
#             raise PyframeException(f"节点 {self.host_ip} 没有配置serial")
#         command = f'"{self.unity_cmd}" -quit -batchmode -serial {serial} -username {args.get("username")} -password {args.get("password")}'
#         cmd.run_shell([command])
#         time.sleep(5)
#
#
# class UnityActivate(UnityActivateBase):
#     def activate_unity(self):
#         if self.official:
#             UnityActivateOfficial(self.unity_cmd).activate_unity()
#         else:
#             UnityActivatePersonal(self.unity_cmd).activate_unity()
