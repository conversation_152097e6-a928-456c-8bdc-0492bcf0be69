from pathlib import Path

# TODO 个人账号密码 模块已经废弃，未使用，可以忽略
OFFICIALS = {
    "192.168.4.198": {"username": "<EMAIL>", "password": "Horizon3d", "serial": ""},
    "192.168.8.59": {"username": "<EMAIL>", "password": "Horizon3d", "serial": ""},
    "192.168.8.58": {"username": "<EMAIL>", "password": "Horizon3d", "serial": ""},
    "192.168.6.181": {"username": "<EMAIL>", "password": "H3Ddgm123", "serial": "SC-MREM-CGFS-YFG9-ZZA9-42Z2"},
    "192.168.13.127": {"username": "<EMAIL>", "password": "H3Ddgm123", "serial": ""},
    "192.168.8.56": {"username": "<EMAIL>", "password": "H3Ddgm123", "serial": "SC-SXJF-MUFK-MTRN-HNNM-HF77"},
    "192.168.8.57": {"username": "<EMAIL>", "password": "H3Ddgm123", "serial": ""},
    "192.168.14.110": {"username": "<EMAIL>", "password": "Yh123456", "serial": ""},
}

# TODO 个人账号密码 模块已经废弃，未使用，可以忽略
PERSONAL_ACCOUNTS = [
    {"username": "<EMAIL>", "password": "Lang5208"},
    {"username": "<EMAIL>", "password": "Jiubugaosuni.2022"},
    {"username": "<EMAIL>", "password": "1Qwertyuiop"},
    {"username": "<EMAIL>", "password": "Unity.2022"},
]

PIC_DIR = Path(__file__).parent.joinpath("pics")

PERSONAL_JPG = f"{PIC_DIR}/personal.jpg"

JPG_LOCATION_MAP = {
    f"{PIC_DIR}/personal_next.jpg": "激活个人的下一步按钮",
    f"{PIC_DIR}/agreement.jpg": "同意按钮",
    f"{PIC_DIR}/agreement_next.jpg": "同意后下一步按钮",
}

WIN_DOWNLOAD_PATH = r"C:\Users\<USER>\Downloads"
MAC_DOWNLOAD_PATH = ""

WIN_DRIVER_PATH = rf"{WIN_DOWNLOAD_PATH}\msedgedriver.exe"
MAC_DRIVER_PATH = ""

LOGIN_URL = "https://id.unity.com/"
LICENSE_URL = "https://license.unity3d.com/manual/"
