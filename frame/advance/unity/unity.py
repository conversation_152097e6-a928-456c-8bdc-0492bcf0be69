from frame.path_mgr.path_mgr import path_mgr
from frame.log.log import log
from frame.exception.exception import PyframeException

# from frame.advance.unity.activate import UnityActivate


class Unity:
    def __init__(self) -> None:
        pass

    # @staticmethod
    # def activate_unity(unity_path: str):
    #     """
    #     激活unity
    #     Args:
    #         unity_path: unity路径
    #     """
    #     if not path_mgr.exists(unity_path):
    #         raise PyframeException(f"unity path {unity_path} not exists")
    #     UnityActivate(unity_path).activate_unity()

    def parse_compile_errors(self, log_path: str) -> list:
        """
        解析编译错误
        Args:
            log_path: unity日志路径
        """
        compile_errors = []
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            if ": error" in line:
                if line not in compile_errors:
                    compile_errors.append(line)
            line = f.readline()
        f.close()
        return compile_errors

    def raise_unity_log_exception(self, log_path: str) -> None:
        """
        解析unity日志中的错误, 并抛出异常
        Args:
            log_path: unity日志路径
        """
        if not path_mgr.exists(log_path):
            log.error(f"unity log {log_path} not exists")
            return
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            # unity崩溃
            if "A crash has been intercepted by the crash handler." in line:
                f.close()
                log.error(f"unity crash")
                raise PyframeException(f"unity crash, unity崩溃, 建议删除Library或者重启机器试试")
            # 内存不足
            if "System.OutOfMemoryException" in line:
                f.close()
                log.error(f"System.OutOfMemoryException")
                raise PyframeException(f"System.OutOfMemoryException, 内存不足, 建议重启机器")
            # 解析编译错误
            if "Scripts have compiler errors" in line:
                f.close()
                log.error(f"Scripts have compiler errors")
                compile_errors = self.parse_compile_errors(log_path)
                compile_errors_str = "\n".join(compile_errors)
                raise PyframeException(f"Scripts have compiler errors, 脚本有编译错误, 请程序检查代码问题\n{compile_errors_str}")
            # 解析激活错误
            if "Unity has not been activated with a valid License" in line or "Failed to activate/update license" in line:
                f.close()
                log.error(f"Unity has not been activated with a valid License")
                raise PyframeException(f"Unity has not been activated with a valid License, unity未激活, 请联系流水线管理员")
            # 检查工程是否已经被打开
            if "It looks like another Unity instance is running with this project open" in line:
                f.close()
                log.error(f"It looks like another Unity instance is running with this project open")
                raise PyframeException(f"It looks like another Unity instance is running with this project open, 该工程已在Unity中打开，请登录机器关闭工程再重新执行流水线")
            # 内存不足
            if "Could not allocate memory: System out of memory!" in line:
                f.close()
                log.error(f"Could not allocate memory: System out of memory!")
                raise PyframeException(f"Could not allocate memory: System out of memory!, 内存不足, 请降低机器负载或者增加机器内存")

            # 无法连接到Unity Package Manager本地服务器
            if "Cannot connect to Unity Package Manager local server" in line:
                f.close()
                log.error(f"Cannot connect to Unity Package Manager local server")
                raise PyframeException(f"Cannot connect to Unity Package Manager local server, 无法连接到Unity Package Manager本地服务器, 请重启打包机")
            if "Error building Player because scripts had compiler errors" in line:
                f.close()
                log.error("Error building Player because scripts had compiler errors")
                raise PyframeException("Error building Player because scripts had compiler errors, 脚本有编译错误, 请程序检查代码问题")

            line = f.readline()
