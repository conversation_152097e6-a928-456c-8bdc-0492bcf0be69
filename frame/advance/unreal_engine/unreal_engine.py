from frame.log.log import log
from frame.exception.exception import PyframeException


class UnrealEngine:
    def __init__(self):
        pass

    def raise_ue_log_exception(self, log_path: str) -> None:
        """
        解析ue5日志中的错误, 并抛出异常
        Args:
            log_path: unity日志路径
        """
        log.debug(f"log_path: {log_path}")

    def raise_automation_tool_log_exception(self, log_path: str) -> None:
        """
        解析AutomationTool日志中的错误, 并抛出异常
        Args:
            log_path: AutomationTool日志路径
        """
        log.info(f"start parse log: {log_path}")
        errors = []
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            line = line.strip()
            if "AutomationTool exiting with ExitCode" in line:
                index = line.find("AutomationTool exiting with ExitCode")
                line = line[index:]
                if line not in errors:
                    errors.append(line)
            if "Unhandled exception:" in line:
                index = line.find("Unhandled exception:")
                line = line[index:]
                if line not in errors:
                    errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"AutomationTool异常退出, 请程序检查代码问题\n{errors_str}")
