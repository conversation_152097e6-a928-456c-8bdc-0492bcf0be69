# coding = utf-8
import os
import subprocess
from enum import Enum
from typing import Optional
from frame.log.log import log
from frame.exception.exception import PyframeException

# from frame.env.env import env


class Cmd:
    class Verbose(Enum):
        """
        打印输出的方式
        """

        LOG = 1  # log
        PRINT = 2  # print
        NONE = 3  # 不输出

    def run_shell(
        self,
        cmds: list,
        workdir: str = "./",
        environ: Optional[dict] = None,
        verbose: Verbose = Verbose.LOG,
        return_output: Optional[bool] = False,
        encoding: Optional[str] = None,
        errors: Optional[str] = None,
        log_to_file: Optional[str] = None,
        dot_kill_me: bool = False,
    ) -> tuple:
        """
        执行命令行，支持linux, windows, mac

        Args:
            cmds: 要执行的命令
            workdir: 工作目录， 默认是./
            environ: 附加临时环境变量
            verbose: 是否展示执行过程，默认展示
            return_output: 是否返回标准输出
            encoding: 编码, 默认是None
            errors: 错误处理方式, 默认是None, 可选值: strict, ignore, replace
            log_to_file: 是否将标准输出写入文件
            dot_kill_me: 是否在流水线运行结束后杀掉进程树, 默认为False, 代表会杀掉进程树
        Returns:
            tuple: (命令行返回值, 标准输出)
        """
        if workdir == "./":
            p = os.getcwd()
            log.info(f"workdir: {p}")
        else:
            log.info(f"workdir: {workdir}")

        if dot_kill_me:
            environ = environ or {}
            environ.update(
                {
                    "JENKINS_NODE_COOKIE": "DotKillMe",
                    "DEVOPS_DONT_KILL_PROCESS_TREE": "true",
                }
            )

        custom_env = os.environ  # env.get_all()
        if environ is not None:
            custom_env.update(environ)

        for i in range(len(cmds)):
            cmds[i] = cmds[i].replace("\n", "")

        multi_cmd = " && ".join(cmds)
        log.info(f"command: {multi_cmd}")

        if len(cmds) >= 8192:
            msg = "command length is too long, please smaller than 8192"
            log.warning(msg)
            raise PyframeException(msg)

        p = subprocess.Popen(
            multi_cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            cwd=workdir,
            env=custom_env,
            universal_newlines=True,
            encoding=encoding,
            errors=errors,
        )
        if log_to_file is not None:
            # f = open(log_to_file, "w")
            f = open(log_to_file, "w", errors="ignore")

        output = []
        while True:
            try:
                out = p.stdout.readline()
            except Exception as e:
                if errors == "ignore":
                    out = e.__str__()
                else:
                    raise e
            if out:
                # out = out.decode('utf8', errors='ignore').replace('\n', '')
                # out = out.decode('gbk', errors='ignore').replace('\n', '')
                out = out.replace("\n", "")
                if return_output:
                    output.append(out)
                if verbose == self.Verbose.PRINT:
                    print(out)
                elif verbose == self.Verbose.LOG:
                    log.info(out)
                else:
                    pass
                if log_to_file is not None:
                    f.write(out + "\n")
                    f.flush()
            else:
                break
        ret = p.wait()
        p.terminate()
        if ret != 0:
            log.warn(f"return code: {ret}")
        if log_to_file is not None:
            f.close()
        return ret, output

    def exec(
        self,
        cmds: list,
        workdir: str = "./",
        environ: Optional[dict] = None,
        verbose: Verbose = Verbose.PRINT,
        return_output: Optional[bool] = False,
        encoding: Optional[str] = None,
        errors: Optional[str] = None,
        log_to_file: Optional[str] = None,
        dot_kill_me: bool = False,
        decode: Optional[str] = None,
        encode: Optional[str] = None,
    ) -> tuple:
        """
        执行命令行，支持linux, windows, mac

        Args:
            cmds: 要执行的命令
            workdir: 工作目录， 默认是./
            environ: 附加临时环境变量
            verbose: 是否展示执行过程，默认展示
            return_output: 是否返回标准输出
            encoding: 编码, 默认是None
            errors: 错误处理方式, 默认是None, 可选值: strict, ignore, replace
            log_to_file: 是否将标准输出写入文件
            dot_kill_me: 是否在流水线运行结束后杀掉进程树, 默认为False, 代表会杀掉进程树
        Returns:
            tuple: (命令行返回值, 标准输出)
        """
        if workdir == "./":
            p = os.getcwd()
            log.info(f"workdir: {p}")
        else:
            log.info(f"workdir: {workdir}")

        if dot_kill_me:
            environ = environ or {}
            environ.update(
                {
                    "JENKINS_NODE_COOKIE": "DotKillMe",
                    "DEVOPS_DONT_KILL_PROCESS_TREE": "true",
                }
            )

        custom_env = os.environ  # env.get_all()
        if environ is not None:
            custom_env.update(environ)

        for i in range(len(cmds)):
            cmds[i] = cmds[i].replace("\n", "")

        multi_cmd = " && ".join(cmds)
        log.info(f"command: {multi_cmd}")
        p = subprocess.Popen(
            multi_cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            cwd=workdir,
            env=custom_env,
            universal_newlines=True,
            encoding=encoding,
            errors=errors,
        )
        if log_to_file is not None:
            f = open(log_to_file, "w")

        output = []
        while True:
            try:
                out = p.stdout.readline()
                out = str(out)
            except Exception as e:
                if errors == "ignore":
                    out = e.__str__()
                else:
                    raise e
            if out:
                # out = out.decode('utf8', errors='ignore').replace('\n', '')
                # out = out.decode('gbk', errors='ignore').replace('\n', '')
                if decode is not None:
                    out = out.decode(decode, errors=errors)
                if encode is not None:
                    out = out.encode(encode, errors=errors)
                out = out.replace("\n", "")
                if return_output:
                    output.append(out)
                if verbose == self.Verbose.PRINT:
                    print(out)
                elif verbose == self.Verbose.LOG:
                    log.info(out)
                else:
                    pass
                if log_to_file is not None:
                    f.write(out + "\n")
                    f.flush()
            else:
                break
        ret = p.wait()
        p.terminate()
        if ret != 0:
            log.warn(f"return code: {ret}")
        if log_to_file is not None:
            f.close()
        return ret, output


cmd = Cmd()
