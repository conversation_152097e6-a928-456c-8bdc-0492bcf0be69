# coding=utf-8

"""
common.py
pyframe中的公共方法，不依赖于pyframe的其他模块
"""

import ctypes
import datetime
import hashlib
import os
import pathlib
import platform
import re
import socket
import urllib.parse
import psutil
import ntplib


class Common:
    @staticmethod
    def get_host_ip() -> str:
        """
        查询本机ip地址

        Returns:
            本机IP
        """
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
        finally:
            s.close()
        return ip

    @staticmethod
    def get_disk_free_size(disk_path: str = None) -> int:
        """
        获取当前磁盘空间大小
        支持windows, linux, macos
        """
        free_size = 0
        if disk_path is None:
            disk_path = os.getcwd()

        if platform.system() == "Windows":
            free_bytes = ctypes.c_ulonglong(0)
            ctypes.windll.kernel32.GetDiskFreeSpaceExW(ctypes.c_wchar_p(disk_path), None, None, ctypes.pointer(free_bytes))
            free_size = free_bytes.value / 1024 / 1024 // 1024
        else:
            st = os.statvfs(disk_path)
            # free_size的单位是 GB
            free_size = st.f_bavail * st.f_frsize / 1024 / 1024 // 1024

        return int(free_size)

    @staticmethod
    def check_disk_usage(threshold: int = 30) -> bool:
        """
        检查当前磁盘空间大小
        支持windows, linux, macos

        Args:
            threshold: 阈值，最小所需磁盘空间，单位Gb

        Returns:
            布尔值，是否满足
        """
        ret = True
        cur_path = os.getcwd()

        if platform.system() == "Windows":
            free_bytes = ctypes.c_ulonglong(0)
            ctypes.windll.kernel32.GetDiskFreeSpaceExW(ctypes.c_wchar_p(cur_path), None, None, ctypes.pointer(free_bytes))
            free_size = free_bytes.value / 1024 / 1024 // 1024
            print("current work dir: {}, free size: {}GB".format(cur_path, free_size))
            if free_size <= threshold:
                ret = False
        else:
            st = os.statvfs(cur_path)
            # free_size的单位是 GB
            free_size = st.f_bavail * st.f_frsize / 1024 / 1024 // 1024
            print("current work dir: {}, free size: {}GB".format(cur_path, free_size))
            if free_size <= threshold:
                ret = False

        return ret

    # @staticmethod
    # def get_folder_size(path: str = "") -> float:
    #     """
    #     检查某个文件夹大小
    #     支持windows, linux, macos

    #     Args:
    #         path: 文件夹路径，需要检查的文件夹路径
    #     Returns:
    #         单精度浮点型，单位GB
    #     """
    #     path = "./" if len(path) == 0 else path
    #     if platform.system() == 'Windows':
    #         import win32com.client as com
    #         # FSO是FileSystemObject 或 Scripting.FileSystemObject 的缩写，为 IIS 内置组件，用于操作磁盘、文件夹或文本文件。
    #         fso = com.Dispatch("Scripting.FileSystemObject")
    #         folder = fso.GetFolder(path)
    #         used_size = folder.Size / 1024 / 1024 / 1024
    #         return round(used_size, 2)
    #     else:
    #         st = os.statvfs(path)
    #         used_size = (st.f_blocks - st.f_bavail) * st.f_bsize / 1024 / 1024 / 1024
    #         return round(used_size, 2)

    @staticmethod
    def compute_md5(file: str) -> str:
        """
        计算文件的md5

        Args:
            file: 文件路径

        Returns:
            md5值，字符串
        """
        m = hashlib.md5()
        p = pathlib.Path(file)
        if not p.exists():
            raise Exception("{} doesn't exist".format(file))

        with open(file, "rb") as f:
            while True:
                data = f.read(4096)
                if not data:
                    break
                m.update(data)
        return m.hexdigest()

    @staticmethod
    def format_duration(seconds: int) -> str:
        """
        将int类型的秒数转为3h 2min 1s
        Args:
            seconds: 秒数

        Returns:
            格式化后的时间，字符串
        """
        m, s = divmod(int(seconds), 60)
        h, m = divmod(m, 60)
        duration = ""
        if h != 0:
            duration += "{}h ".format(h)
        if m != 0:
            duration += "{}min ".format(m)
        duration += "{}s".format(s)
        return duration

    @staticmethod
    def get_ftime() -> str:
        """
        获取格式化时间
        Returns:
            格式化后的时间，字符串
        """
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    @staticmethod
    def str2bool(bool_str: str) -> bool:
        """
        将"true" or "True" 转为 True
        Args:
            bool_str: 字符串
        """
        if isinstance(bool_str, bool):
            return bool_str
        elif isinstance(bool_str, str):
            b = bool_str.lower().strip()
            if b == "true":
                return True
            elif b == "false":
                return False
            else:
                raise Exception("invalid bool string: {}".format(bool_str))
        else:
            raise Exception("invalid type: {}".format(bool_str))

    @staticmethod
    def is_true(param):
        if param == "true" or param == "True" or param == "TRUE" or param is True:
            return True
        elif param == "false" or param == "False" or param == "FALSE" or param is False:
            return False
        else:
            raise Exception("invalid bool string: {}".format(param))

    @staticmethod
    def increase_x_x_x(version: str) -> str:
        """
        版本号自增, x.x.x, 适用于x52
        Args:
            version: 版本号，如1.0.0

        Returns:
            版本号，如1.0.1
        """
        version = version.strip()
        if not re.match(r"\d\.\d\.\d", version):
            raise Exception(f"invalid version: {version}")

        if version.count(".") != 2:
            raise Exception(f"invalid version: {version}")

        elems = [int(v) for v in version.split(".")]
        if elems[2] == 9:
            elems[2] = 0
            if elems[1] == 9:
                elems[1] = 0
                elems[0] += 1
            else:
                elems[1] += 1
        else:
            elems[2] += 1

        incr_version = ".".join([str(v) for v in elems])
        if not re.match(r"\d\.\d\.\d", incr_version):
            raise Exception(f"invalid version: {incr_version}")
        return incr_version

    @staticmethod
    def join_url(prefix: str, *args) -> str:
        """
        拼接url
        Args:
            prefix: url前缀
            args: 需要拼接的字符串
        Returns:
            拼接后的url
        """
        for arg in args:
            if not isinstance(arg, str):
                raise TypeError(f"args: {args} must be str")
        if not prefix.endswith("/"):
            prefix += "/"
        args = [arg.strip("/") for arg in args]
        suffix = "/".join(args)
        url = prefix + suffix
        return url

    @staticmethod
    def get_base_url(url: str) -> str:
        parse_result = urllib.parse.urlparse(url)
        if parse_result.scheme == "" or parse_result.netloc == "":
            base_url = parse_result.path
        else:
            base_url = f"{parse_result.scheme}://{parse_result.netloc}"
        return base_url

    @staticmethod
    def get_basename(url: str) -> str:
        """
        获取url中的文件名
        Args:
            url: url
        Returns:
            文件名
        """
        return os.path.basename(url)

    @staticmethod
    def is_contain_chinese(s: str) -> bool:
        """
        判断字符串中是否包含中文
        Args:
            s: 字符串
        Returns:
            布尔值
        """
        for c in s:
            if "\u4e00" <= c <= "\u9fff":
                return True
        return False

    @staticmethod
    def get_local_cpu_count() -> int:
        """
        获取本地cpu核数
        Returns:
            cpu核数
        """
        return psutil.cpu_count()

    @staticmethod
    def get_current_time() -> datetime.datetime:
        """
        获取准确的当前时间
        """
        try:
            ntp_client = ntplib.NTPClient()
            response = ntp_client.request("pool.ntp.org")
            current_time = datetime.datetime.utcfromtimestamp(response.tx_time)
            print(f"get time from pool.ntp.org, current_time: {current_time}")
        except Exception:
            current_time = datetime.datetime.utcnow()
            print(f"failed to get time from pool.ntp.org, use local time instead: {current_time}")

        return current_time


common = Common()
