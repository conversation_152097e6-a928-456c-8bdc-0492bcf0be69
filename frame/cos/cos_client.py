# -*- coding=utf-8
import os
from pathlib import Path
from typing import Union

from qcloud_cos import CosConfig, CosS3Client
from qcloud_cos.cos_exception import CosClientError, CosServiceError

from frame import log as logger


class CosClient:
    def __init__(self, *, secret_id: str, secret_key: str, region: str = "ap-beijing"):
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.region = region
        self.client = self.__client

    @property
    def __client(self):
        config = CosConfig(Region=self.region, SecretId=self.secret_id, SecretKey=self.secret_key)
        client = CosS3Client(config)
        return client

    def upload_file(
        self,
        bucket: str,
        remote_path: str,
        local_file_path: Union[str, Path],
        retry: int = 3,
    ):
        """
        上传文件，如果远端已经存在同名文件，会进行覆盖
        :param bucket: 存储桶，由 BucketName-APPID 构成
        :param remote_path: 在cos上存储的相对路径，比如访问的绝对路径是：
            examplebucket-1250000000.cos.ap-guangzhou.myqcloud.com/doc/test/pic.jpg，则remote_path为 doc/test
            如果远端不存在，则会创建
        :param local_file_path: 本地要上传的文件路径
        :param retry: 重试次数
        :return:
        """
        local_file = Path(local_file_path)
        if not local_file.exists():
            raise Exception(f"本地文件：{local_file_path} 未找到！")
        if not local_file.is_file():
            raise Exception("该方法只支持上传文件！")

        if remote_path.endswith("/"):
            remote_path.rstrip("/")

        for i in range(0, retry):
            try:
                self.client.upload_file(
                    Bucket=bucket,
                    LocalFilePath=local_file_path,
                    Key=f"{remote_path}/{local_file.name}",
                    PartSize=1,
                    MAXThread=os.cpu_count(),
                    EnableMD5=False,
                )
                logger.info(f"上传文件完成，上传路径: {bucket}/{remote_path}/{local_file.name}, 本地文件：{local_file_path}")
                break
            except CosClientError or CosServiceError as e:
                logger.error(f"上传文件第{i + 1}次失败：{e}")

    def list_objects(
        self,
        bucket: str,
        prefix: str = None,
        delimiter: str = None,
        marker: str = None,
        max_key: int = 1000,
        encoding_type: str = None,
    ):
        """
        查询存储桶下的部分或者全部对象。
            https://cloud.tencent.com/document/product/436/65823

        :param bucket: 存储桶，由 BucketName-APPID 构成
        :param prefix: 对对象的对象键进行筛选，匹配 prefix 为前缀的对象
        :param delimiter: 设置分隔符，例如设置 / 来模拟文件夹
        :param marker: 默认以 UTF-8 二进制顺序列出条目，标记返回对象的 list 的起点位置
        :param max_key: 最多返回的对象数量，默认为最大的1000
        :param encoding_type: 默认不编码，规定返回值的编码方式，可选值：url
        :return:
            {
                'MaxKeys': '1000',
                'Prefix': 'string',
                'Delimiter': 'string',
                'Marker': 'string',
                'NextMarker': 'string',
                'Name': 'examplebucket-1250000000',
                'IsTruncated': 'false'|'true',
                'EncodingType': 'url',
                'Contents':[
                    {
                        'ETag': '"a5b2e1cfb08d10f6523f7e6fbf3643d5"',
                        'StorageClass': 'STANDARD',
                        'Key': 'exampleobject',
                        'Owner': {
                            'DisplayName': '1250000000',
                            'ID': '1250000000'
                        },
                        'LastModified': '2017-08-08T09:43:35.000Z',
                        'Size': '23'
                    },
                ],
                'CommonPrefixes':[
                    {
                        'Prefix': 'string'
                    },
                ],
            }
        """
        return self.client.list_objects(
            bucket,
            Delimiter=delimiter,
            Marker=marker,
            MaxKeys=max_key,
            Prefix=prefix,
            EncodingType=encoding_type,
        )

    def download_single_file(self, bucket: str, remote_path: str, dst_path: Union[str, Path], retry: int = 3):
        """
        该高级接口仅支持下载整个文件，根据用户文件的长度自动选择简单下载以及分块下载，
        对于小于等于20MB的文件使用简单下载，大于20MB的文件使用续传下载，
        对于分块下载未完成的文件会自动进行断点续传。
        https://cloud.tencent.com/document/product/436/65821


        具体参数文档
        """

        # 使用高级接口断点续传，失败重试时不会下载已成功的分块
        for _ in range(0, retry):
            try:
                self.client.download_file(Bucket=bucket, Key=remote_path, DestFilePath=str(dst_path))
                break
            except CosClientError or CosServiceError as e:
                print(e)


if __name__ == "__main__":
    client = CosClient(
        secret_id="AKIDZy96qMEjcswmPopveCbqreDYeV6WPdKl",
        secret_key="WnpcATYQ5XlmY6OTLzQdfT3PXR6WgdMs",
    )
    client.download_single_file(
        bucket="123-dancer-beijing-1259494283",
        remote_path="other/temp/jk/ke_6.7.6_20231220165837.zip",
        dst_path="/Users/<USER>/Downloads/ke_6.7.6_20231220165837.zip",
    )
