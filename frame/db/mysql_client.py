# coding=utf-8

import pymysql
from dbutils.pooled_db import PooledDB

from frame.log.log import log


class MysqlClient:
    def __init__(self, host: str, port: str, username: str, password: str):
        self.__mysql_pool = PooledDB(
            host=host,
            port=int(port),
            user=username,
            password=password,
            creator=pymysql,
            maxconnections=20,
            mincached=2,
            maxcached=5,
            maxshared=1,
            blocking=True,
            maxusage=None,
            setsession=[],
            ping=0,
            charset="utf8",
        )

    def __create_conn(self):
        conn = self.__mysql_pool.connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        return conn, cursor

    def __close_conn(self, conn, cursor):
        conn.close()
        cursor.close()

    def select_one(self, sql: str):
        """
        搜索一个
        """
        log.info(sql)
        conn, cur = self.__create_conn()
        cur.execute(sql)
        result = cur.fetchone()
        self.__close_conn(conn, cur)
        return result

    def select_all(self, sql: str):
        """
        搜索很多
        """
        log.info(sql)
        conn, cur = self.__create_conn()
        cur.execute(sql)
        result = cur.fetchall()
        self.__close_conn(conn, cur)
        return result

    def __execute(self, sql: str):
        """
        执行
        """
        conn, cur = self.__create_conn()
        log.info(f"SQL: {sql}")
        result = cur.execute(sql)
        conn.commit()
        self.__close_conn(conn, cur)
        return result

    def exists_db(self, db_name) -> bool:
        """
        数据库是否存在
        Args:
            db_name: 数据库名称

        Returns: 查询结果 [False 不存在 True 存在]

        """
        sql = f"SELECT * FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{db_name}'; "
        return True if self.__execute(sql) else False

    def create_db(self, db_name: str, charset: str = "utf8mb4", collate: str = "utf8mb4_general_ci") -> bool:
        """
        创建数据库
        Args:
            db_name: 数据库名称
            charset: 编码格式，无特殊要求保持默认即可
            collate: 排序规则，无特殊要求保持默认即可

        Returns: 创建结果 [False 创建失败 True 创建成功]

        """
        sql = f"CREATE DATABASE {db_name} CHARACTER SET {charset} COLLATE {collate}"
        return True if self.__execute(sql) else False

    def insert_one(self, sql: str):
        """
        插入一个
        """
        log.info(sql)
        return self.__execute(sql)

    def delete_one(self, sql: str):
        """
        删除一个
        """
        log.info(sql)
        return self.__execute(sql)

    def update_one(self, sql: str):
        """
        更新一个
        """
        log.info(sql)
        return self.__execute(sql)

    def drop_table(self, sql: str):
        """
        删除表
        """
        log.info(sql)
        return self.__execute(sql)

    def alter_table(self, sql: str):
        """
        未测试
        """
        log.info(sql)
        return self.__execute(sql)

    def create_table(self, sql: str, db_name: str) -> bool:
        """
        创建数据表
        Args:
            sql: 创建表所用SQL语句
            db_name: 数据库名称

        Returns: 创建结果 [False 创建失败 True 创建成功]

        """
        create_ret = False
        conn, cur = self.__create_conn()
        try:
            cur.execute(f"USE {db_name}")
            cur.execute(sql)
            create_ret = True
        except Exception as e:
            log.error(e)
        finally:
            conn.close()
        return create_ret

    def exists_table(self, db_name, table_name) -> bool:
        """
        表是否存在
        Args:
            db_name: 数据库名称
            table_name: 数据表名称

        Returns: 查询结果 [False 不存在 True 存在]

        """
        sql = f"SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '{db_name}' AND TABLE_NAME = '{table_name}';"
        return True if self.__execute(sql) else False

    def get_all_tables(self, database: str) -> list:
        """
        获取dabtase库的所有表名
        Args:
            database: 数据库名称
        Returns:
            list: 表名列表
        """
        conn, cur = self.__create_conn()
        sql = "select table_name from information_schema.tables where table_schema='{}';".format(database)
        cur.execute(sql)
        result = cur.fetchall()
        self.__close_conn(conn, cur)
        tables = []
        for ret in result:
            tables.append(ret["table_name"])
        return tables

    def get_all_databases(self) -> list:
        """
        获取所有数据库
        Returns:
            list: 数据库列表
        """
        conn, cur = self.__create_conn()
        sql = "show databases;"
        cur.execute(sql)
        result = cur.fetchall()
        self.__close_conn(conn, cur)
        databases = []
        for ret in result:
            databases.append(ret["Database"])
        return databases

    def drop_db(self, db_name: str) -> bool:
        """
        删除数据库
        Args:
            db_name:

        Returns:

        """
        sql = f"""
            DROP SCHEMA {db_name};
        """
        return True if self.__execute(sql) else False

    def create_or_replace_view(self, db_name: str, view_name: str, select_sql: str) -> bool:
        """
        创建视图
        """
        view_sql = f"CREATE OR REPLACE VIEW {db_name}.{view_name} AS {select_sql}"
        log.info(view_sql)
        try:
            self.__execute(view_sql)
            return True
        except Exception as e:
            log.error(e)
            return False
