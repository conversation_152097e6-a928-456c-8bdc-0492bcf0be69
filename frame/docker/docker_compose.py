# coding=utf-8
from frame.cmd.cmd import cmd
from frame.exception.exception import PyframeException
from frame.log.log import log


class DockerCompose:
    def __init__(self, workdir: str, yml: str = "docker-compose.yml"):
        """
        初始化docker-compose，查询机器有无docker-compose
        Args:
            workdir:工作路径
            file:docker.compose的文件名
        """
        self._workdir = workdir
        self._yml = yml
        # 检查是否存在docker-compose
        ret = cmd.run_shell(
            cmds=["docker-compose version"],
            return_output=True,
        )
        if ret[0] != 0:
            log.error(ret[1])
            log.error("docker-compose doesn't exist. python3 -m pip install docker-compose")
            raise PyframeException("docker-compose不存在")

    def up(self, container: str = "", detach: bool = False, build: bool = False):
        """
        创建并启动容器
        Args:
            container: 容器名
            detach: 分离模式，即在后台运行容器，输出新容器名
        """
        command = ["docker-compose", "-f", self._yml, "up"]
        if detach:
            command.append("-d")
        if build:
            command.append("--build")
        command.append(container)
        ret = cmd.run_shell(
            cmds=[" ".join(command)],
            workdir=self._workdir,
        )
        if ret[0] != 0:
            log.error(f"return {ret[0]}")
            raise PyframeException("docker-compose up失败")

    def down(self, timeout: int = None):
        """
        停止并删除容器，网络，镜像和挂载
        Args:
            timeout:以秒为单位指定一个超时关闭，默认为10秒
        """
        command = f"docker-compose -f {self._yml} down"
        if timeout is not None:
            command += f" -t {timeout}"
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self._workdir,
        )
        if ret[0] != 0:
            log.error(f"return {ret[0]}")
            raise PyframeException("docker-compose down失败")

    def restart(self, container: str = ""):
        """
        重启服务
        Args:
        """
        command = f"docker-compose -f {self._yml} restart"
        if container != "":
            command += f" {container}"
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self._workdir,
        )
        if ret[0] != 0:
            log.error(f"return {ret[0]}")
            raise PyframeException("docker-compose restart失败")

    def ps(self) -> list:
        """
        列出容器
        """
        command = f"docker-compose -f {self._yml} ps"
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self._workdir,
            return_output=True,
        )
        if ret[0] != 0:
            log.error(f"return {ret[0]}")
            raise PyframeException("docker-compose ps失败")
        out = []
        start = False
        for line in ret[1]:
            if "---------------------" in line:
                start = True
                continue
            if start:
                out.append(line)
        return out

    def logs(self, timestamps: bool = True, tail: int = 100):
        """
        查看日志
        Args:
            timestamps:展示时间戳
            tail:对每个容器展示其日志的最后行数，默认100行
        """
        if timestamps:
            command = f"docker-compose -f {self._yml} down -t --tail={tail}"
        else:
            command = f"docker-compose -f {self._yml} down --tail={tail}"
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self._workdir,
        )
        if ret[0] != 0:
            log.error(f"return {ret[0]}")
            raise PyframeException("docker-compose logs失败")

    def config(self, volumes: bool = False, services: bool = False):
        """
        验证和查看docker-compose文件
        Args:
            volumes:输出卷的名字，每行一个
            services:输出服务的名字，每行一个
        """
        command = ["docker-compose", "config"]
        if services:
            command.append("--services")
        if volumes:
            command.append("--volumes")
        ret = cmd.run_shell(
            cmds=[" ".join(command)],
            workdir=self._workdir,
        )
        if ret[0] != 0:
            log.error("docker-compose config failed")
            raise PyframeException("docker-compose config失败")

    def build(self, no_cache: bool = False):
        """
        构建镜像或重新构建镜像
        Args:
            no_cache:构建镜像的时候不缓存过期资源
        """
        command = ["docker-compose", f"-f {self._yml}", "build"]
        if no_cache:
            command.append("--no-cache")
        ret = cmd.run_shell(
            cmds=[" ".join(command)],
            workdir=self._workdir,
        )
        if ret[0] != 0:
            log.error("build image failed")
            raise PyframeException("docker-compose build失败")

    def start(self, container: str = ""):
        """
        开始服务
        """
        ret = cmd.run_shell(
            cmds=[f"docker-compose start {container}"],
            workdir=self._workdir,
        )
        if ret[0] != 0:
            log.error("start service failed")
            raise PyframeException("docker-compose start失败")

    def stop(self, timeout: int = 0):
        """
        停止服务
        """
        ret = cmd.run_shell(
            cmds=["docker-compose stop"],
            workdir=self._workdir,
        )
        if ret[0] != 0:
            log.error("stop service failed")
            raise PyframeException("docker-compose stop失败")


# docker_compose = DockerCompose()
