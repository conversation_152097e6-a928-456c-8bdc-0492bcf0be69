# coding = utf-8
# import datetime
import logging
import os
import time
import re
from typing import Optional

import pymongo

from frame import PROJECT, PIPELINE_FUNC
from frame.api.api import api
from frame.common.common import common
from frame.log.log import log


# from frame.db.mongo_mgr import MongoMgr


class PipelineEnv:
    """
    获取流水线相关的环境变量, 集成jenkins和蓝盾
    """

    def __init__(self):
        self.__envs = dict(os.environ)

    def workspace(self) -> str:
        """
        获取流水线的workspace绝对路径

        Returns:
            str: 流水线的workspace绝对路径
        """
        if "WORKSPACE" in self.__envs:
            workspace = self.__envs["WORKSPACE"]
        else:
            workspace = "./"
        return workspace

    def pipeline_id(self) -> str:
        """
        获取流水线id
        jenkins是JOB_NAME, 蓝盾是BK_CI_PIPELINE_ID, 否则返回日期

        Returns:
            当前流水线的id
        """
        if "JOB_NAME" in self.__envs:  # jenkins
            pipeline_id = self.__envs["JOB_NAME"]
        elif "BK_CI_PIPELINE_ID" in self.__envs:  # 蓝盾
            pipeline_id = self.__envs["BK_CI_PIPELINE_ID"]
        else:  # 其他
            pipeline_id = time.strftime("%Y-%m-%d", time.localtime(time.time()))
        return pipeline_id

    def pipeline_name(self) -> str:
        """
        获取流水线名称
        jenkins是JOB_NAME, 蓝盾是BK_CI_PIPELINE_NAME, 否则返回手动调试

        Returns:
            流水线名称
        """
        pipeline_name = "手动调试"
        if "PIPELINE_NAME" in self.__envs:  # jenkins
            pipeline_name = self.__envs["PIPELINE_NAME"]
            pipeline_name = pipeline_name.split("#")[0].strip(" ")
        elif "BK_CI_PIPELINE_NAME" in self.__envs:  # 蓝盾
            pipeline_name = self.__envs["BK_CI_PIPELINE_NAME"]
        return pipeline_name

    def build_id(self) -> str:
        """
        获取流水线构建id
        jenkins是BUILD_ID, 蓝盾是BK_CI_BUILD_ID, 否则返回时间戳

        Returns:
            此次构建的id
        """
        if "BUILD_NUMBER" in self.__envs:  # jenkins
            build_id = self.__envs["BUILD_ID"]
        elif "BK_CI_BUILD_ID" in self.__envs:  # 蓝盾
            build_id = self.__envs["BK_CI_BUILD_ID"]
        else:  # 其他
            build_id = str(int(time.time()))
        return build_id

    def build_num(self) -> str:
        """
        获取流水线的构建号
        jenkins是BUILD_ID, 蓝盾是BK_CI_BUILD_NUM, 否则返回时间戳

        Returns:
            此次构建的序号
        """
        if "BUILD_NUMBER" in self.__envs:  # jenkins
            return self.__envs["BUILD_NUMBER"]
        elif "BK_CI_BUILD_NUM" in self.__envs:  # 蓝盾
            return self.__envs["BK_CI_BUILD_NUM"]
        else:  # 其他
            return common.get_host_ip()
            # return str(int(time.time()))

    def build_url(self) -> str:
        """
        获取当前构建日志的url
        jenkins是BUILD_URL, 蓝盾是拼接出来的, 否则返回空

        Returns:
            构建日志的url
        """
        url = ""
        if "BUILD_URL" in self.__envs:  # jenkins
            url = "{}console".format(self.__envs["BUILD_URL"])
        elif "BK_CI_PROJECT_NAME" in self.__envs:  # 蓝盾
            url = "https://bk-devops.h3d.com.cn/console/pipeline/{}/{}/detail/{}".format(
                self.__envs["BK_CI_PROJECT_NAME"], self.__envs["BK_CI_PIPELINE_ID"], self.__envs["BK_CI_BUILD_ID"]
            )
        return url

    def build_duration(self) -> str:
        """
        获取构建时长

        Returns:
            执行的时间
        """
        # self.__sync_system_time()
        if "BUILD_START_TIME" in self.__envs:
            start_time = int(self.__envs.get("BUILD_START_TIME")) / 1000
            end_time = api.timestamp()
            return common.format_duration(int(end_time - start_time))
        elif "BK_CI_BUILD_START_TIME" in self.__envs:
            start_time = int(self.__envs.get("BK_CI_BUILD_START_TIME")) / 1000
            end_time = api.timestamp()
            return common.format_duration(int(end_time - start_time))
        return ""

    def build_duration_seconds(self) -> int:
        """
        构建时间：单位（秒）
        Returns:

        """
        if "BUILD_START_TIME" in self.__envs:
            start_time = int(self.__envs.get("BUILD_START_TIME")) / 1000
            end_time = api.timestamp()
            return int(end_time - start_time)
        elif "BK_CI_BUILD_START_TIME" in self.__envs:
            start_time = int(self.__envs.get("BK_CI_BUILD_START_TIME")) / 1000
            end_time = api.timestamp()
            return int(end_time - start_time)
        else:
            return 0

    def start_time(self) -> str:
        """
        获取构建开始时间
        jenkins是BUILD_START_TIME, 蓝盾是BK_CI_BUILD_START_TIME, 否则返回空

        Returns:
            构建开始时间，时间戳字符串
        """
        # self.__sync_system_time()
        if "BUILD_START_TIME" in self.__envs:
            return self.__envs.get("BUILD_START_TIME")
        elif "BK_CI_BUILD_START_TIME" in self.__envs:
            return self.__envs.get("BK_CI_BUILD_START_TIME")
        return ""

    @staticmethod
    def __get_email_or_ip(user: str) -> str:
        """
        获取用户的邮箱或者ip
        Args:
            user: 用户名

        Returns:
            用户的邮箱或者ip
        """
        email_pattern = r"^.* (.*@h3d.com.cn$)$"
        email = re.findall(email_pattern, user)
        if email:
            return email[0]
        ip_pattern = r"\b(?:\d{1,3}\.){3}\d{1,3}\b"
        ip = re.findall(ip_pattern, user)
        if ip:
            return ip[0]
        return user

    def pipeline_operator(self) -> str:
        """
        获取流水线的操作者

        jenkins是BUILD_USER, 蓝盾是BK_CI_START_USER_NAME, 否则返回空字符串

        Returns:
            流水线的触发人
        """
        if "BUILD_USER" in self.__envs:  # jenkins
            build_user = self.__envs["BUILD_USER"]
            if "**************" in build_user:
                build_user = "p4转发服务"
            if "*************" in build_user:
                build_user = "UE5_1_0引擎发版"
            else:
                build_user = self.__get_email_or_ip(build_user)
            return build_user
        elif "BK_CI_START_USER_NAME" in self.__envs:  # 蓝盾
            return self.__envs["BK_CI_START_USER_NAME"]
        else:  # 其他
            return ""

    def pipeline_operator_email(self) -> str:
        """
        获取操作人邮箱
        Returns:

        """
        # Jenkins
        if "BUILD_USER_EMAIL" in self.__envs:
            build_user_email = self.__envs["BUILD_USER_EMAIL"]
            return self.__get_email_or_ip(build_user_email)
        # 蓝盾
        elif "BK_CI_START_USER_NAME" in self.__envs:
            return self.__envs["BK_CI_START_USER_NAME"]
        # 其他
        else:
            return ""

    def start_type(self) -> str:
        """
        流水线启动方式
        Returns:
            流水线启动方式
        """
        # bk_start_type_map = {
        #     "REMOTE": "远程触发",
        #     "MANUAL": "手动触发",
        #     "TIME_TRIGGER": "定时触发",
        #     "PIPELINE": "子流水线插件触发",
        #     "WEB_HOOK": "代码库HOOK触发",
        #     "SERVICE": "API触发",
        # }
        if "BUILD_USER" in self.__envs:
            return self.__envs["BUILD_USER"]
        elif "BK_CI_START_TYPE" in self.__envs:
            return self.__envs["BK_CI_START_TYPE"]
        else:
            return "手动调试"

    def job_name(self) -> str:
        """
        获取job名称
        jenkins是JOB_NAME, 蓝盾是BK_CI_BUILD_JOB_ID, 否则返回空字符串

        Returns:
            job名称
        """
        if "JOB_NAME" in self.__envs:
            return self.__envs["JOB_NAME"]
        elif "BK_CI_JOB_NAME" in self.__envs:
            return self.__envs["BK_CI_BUILD_JOB_ID"]
        else:
            return ""

    def function_name(self) -> str:
        """
        获取流水线函数名称

        Returns:
            job名称
        """
        return PIPELINE_FUNC

    def project_name(self) -> str:
        """
        获取项目名称

        Returns:
            项目名称
        """
        return PROJECT

    def platform(self) -> str:
        """
        判断当前平台是蓝盾还是jenkins

        Returns:
            平台信息: jenkins, 蓝盾, 手动调试
        """
        if "BUILD_START_TIME" in self.__envs:
            return "jenkins"
        elif "BK_CI_BUILD_START_TIME" in self.__envs:
            return "蓝盾"
        else:
            return "手动调试"


class Env:
    def __init__(self, ip: str = "************", port: int = 27017) -> None:
        self.pipeline = PipelineEnv()
        self.__client = pymongo.MongoClient("mongodb://{}:{}/".format(ip, port), connect=False)
        self.__database = self.__client["{}".format(PROJECT)]  # 正式数据库
        logging.info(f"Init Env __database with: ip:{ip}, port:{port}, project: {PROJECT}")
        self.__collection = self.__database["environ"]
        self.__global_collection = self.__database["global"]
        self.__global_collection.create_index([("createdAt", 1)])
        self.__collection.create_index([("createdAt", 1)], expireAfterSeconds=60 * 60 * 24 * 1)  # debug, 暂定1天的过期时间
        # self.__environment_id = "{}-{}-{}".format(common.get_host_ip(), self.__pipeline_id, self.__pipeline_number)
        self.__environment_id = "{}-{}".format(self.pipeline.pipeline_id(), self.pipeline.build_num())
        # log.debug(f"environment_id: {self.__environment_id}")
        if not self.exist_pipeline():
            self.__set_default_environ()

    def exist_pipeline(self):
        """
        判断数据库中是否已经存在此次构建的环境变量
        """
        ret = self.__collection.find_one({"_id": self.__environment_id})
        if ret is None:
            return False
        else:
            return True

    def __set_default_environ(self):
        envs = dict(os.environ)
        _id = {"_id": self.__environment_id}
        # createAt = {"createdAt": datetime.datetime.utcnow()}
        createAt = {"createdAt": common.get_current_time()}
        all_envs = {**_id, **envs, **createAt}
        try:
            ret = self.__collection.insert_one(all_envs)
            if ret is not None:
                # log.debug(f"{ret}")
                pass
        except Exception as e:
            log.warn(e)

    def set(self, envs: dict, verbose: bool = True):
        """
        设置数据库中的环境变量。
        Args:
            envs: 环境变量
        """
        if verbose:
            log.info(f"{envs}")
        self.__collection.update_one({"_id": self.__environment_id}, {"$set": envs})

    def get(self, key: str, default: str = None, verbose: bool = True) -> str or None:
        """
        获取环境变量, 先从数据库中获取, 若不存在则从os.environ获取, 若还不存在则返回None。

        Args:
            key: 环境变量的键
        Returns:
            环境变量的值
        """
        db_dict = self.__collection.find_one({"_id": self.__environment_id})
        if key in db_dict:
            if verbose:
                log.info(f"database, {key}: {db_dict[key]}")
            return db_dict.get(key)
        else:
            sys_dict = dict(os.environ)
            if key in sys_dict:
                try:
                    self.set(envs=sys_dict)
                except Exception as e:
                    log.warn(e)
                if verbose:
                    log.info(f"system, {key}: {sys_dict[key]}")
                return sys_dict.get(key)
            else:
                if verbose:
                    log.info(f"default, {key}: {default}")
                return default

    def __get_global_by_id(self, id: str):
        db_dict = self.__global_collection.find_one({"_id": id})
        return db_dict

    def set_global(self, envs: dict):
        """
        设置全局环境变量。
        Args:
            envs:
        """
        branch_name = self.get("BRANCH_NAME")  # 适配多分支
        if branch_name is None:
            id = f"{PIPELINE_FUNC}"
        else:
            id = f"{PIPELINE_FUNC}_{branch_name}"
        language = self.get("LANGUAGE")
        if language == "chin_trad":
            id = f"{id}_trad"

        if not self.__get_global_by_id(id=id):
            log.info(f"insert: {envs}")
            self.__global_collection.insert_one({"_id": id, **envs})
            return

        log.info(f"update: {envs}")
        self.__global_collection.update_one({"_id": id}, {"$set": envs})

    def get_global(self, key: str, verbose: bool = True, doc_id: str = None) -> Optional[str]:
        """
        获取全局环境变量。
        Args:
            key: 环境变量的键
            verbose:
            doc_id: _id
        Returns:
            环境变量的值
        """
        branch_name = self.get("BRANCH_NAME")  # 适配多分支
        if not doc_id:
            if branch_name is None:
                doc_id = f"{PIPELINE_FUNC}"
            else:
                doc_id = f"{PIPELINE_FUNC}_{branch_name}"
            language = self.get("LANGUAGE")
            if language == "chin_trad":
                doc_id = f"{doc_id}_trad"
        log.info(f"id: {doc_id} PIPELINE_FUNC: {PIPELINE_FUNC} branch_name: {branch_name}")
        db_dict = self.__get_global_by_id(doc_id)
        ret = None
        if db_dict:
            ret = db_dict.get(key, "")
        if verbose:
            log.info(f"{key}: {ret}")
        return ret

    def get_all(self) -> dict:
        """
        获取数据库中所有的环境变量

        Returns:
            所有环境变量
        """
        db_dict = self.__collection.find_one({"_id": self.__environment_id})
        del db_dict["createdAt"]
        return db_dict

    def get_failure_stage(self) -> str:
        """
        获取流水线失败的阶段

        Returns:
            流水线失败的阶段
        """
        return self.get(key="PYFRAME_STAGE")

    def set_failure_mentioned_list(self, mentioned_list: list):
        """
        设置失败提醒的人员列表

        Args:
            mentioned_list: 失败提醒的人员列表
        """
        if not isinstance(mentioned_list, list):
            raise TypeError("mentioned_list must be a list")
        origin_list = self.get_failure_mentioned_list()
        if origin_list:
            mentioned_list = list(set(origin_list + mentioned_list))
        self.set(envs={"PYFRAME_FAILURE_MENTIONED_LIST": mentioned_list})

    def get_failure_mentioned_list(self) -> list:
        """
        获取失败提醒的人员列表

        Returns:
            失败提醒的人员列表
        """
        mentioned_list = self.get(key="PYFRAME_FAILURE_MENTIONED_LIST")
        if mentioned_list is None:
            return []
        else:
            return mentioned_list


env = Env()
