# coding=utf-8
from frame.env.env import env


class PyframeException(Exception):
    def __init__(self, message: str, mentioned_list: list = []):
        # msg = f"message: {message}"
        if message:
            stage = env.get("PYFRAME_STAGE")
            message = message.replace("\\", "/")
            env.set(
                {
                    "PYFRAME_EXCEPTION_MESSAGE": message,
                    "PYFRAME_FAILURE_STAGE": stage,
                }
            )
            env.set_failure_mentioned_list(mentioned_list=mentioned_list)
        super().__init__(message)


class UnityInvalidLicenseException(PyframeException):
    pass
