import math
import os
import socket
import time
from ftplib import FTP, error_perm
from pathlib import Path

from frame.log.log import log


class Ftp(object):
    def __init__(self, ip: str, port: int, username: str, password: str):
        """
        FTP上传下载

        Args:
            ip: FTP服务器IP
            port: FTP服务器端口
            username: 用户名
            password: 密码
        """
        self.ip = ip
        self.port = port
        self.username = username
        self.password = password
        socket.setdefaulttimeout(60)  # 超时FTP时间设置为60秒
        self.__ftp = FTP()
        self.__ftp.connect(host=self.ip, port=self.port)
        self.__ftp.set_debuglevel(0)
        self.__ftp.encoding = "utf-8"
        self.__download_percent = 0

        try:
            self.__ftp.login(self.username, self.password)
            log.info("[{}]login ftp {}".format(self.username, self.__ftp.getwelcome()))
        except (socket.error, socket.gaierror):  # 连接错误
            log.error("cannot connect [{}:{}]".format(self.ip, self.port))
        except error_perm:  # 用户登录认证错误
            log.error("user Authentication failed ")
        except Exception as e:
            log.error(e)

    def upload(self, remote_path: str, local_path: str, file: str):
        """
        从本地上传文件到ftp

        Args:
            remote_path: FTP远程路径
            local_path: 本地路径
            file: 文件

        Returns:

        """
        flag = False
        buffer_size = 10240  # 默认是8192
        log.debug(self.__ftp.getwelcome())  # 显示登录ftp信息

        fp = open(os.path.join(local_path, file), "rb")

        try:
            self.__ftp.cwd(remote_path)  # 进入远程目录
            log.info("found folder [{}] in ftp server, upload processing.".format(remote_path))
            log.info(f"进入目录{self.__ftp.pwd()}")
            # 将传输模式改为二进制模式 ,避免提示 ftplib.error_perm: 550 SIZE not allowed in
            # ASCII
            self.__ftp.voidcmd("TYPE I")
            self.__ftp.storbinary("STOR " + file, fp, buffer_size)
            self.__ftp.set_debuglevel(0)
            log.info("上传文件 [{}] 成功".format(file))
            flag = True
        except error_perm as e:
            log.error("文件[{}]传输有误,{}".format(file, str(e)))
        except TimeoutError:
            log.error("文件[{}]传输超时".format(file))
        except Exception as e:
            log.error("文件[{}]传输异常, {}".format(file, str(e)))
        finally:
            fp.close()

        return {"file_name": file, "flag": flag}

    def is_dir(self, path: str) -> bool:
        """
        判断FTP当前路径是否为目录
        Args:
            path: FTP远程路径，如：/version_test/dynamic/android/

        Returns:

        """
        try:
            self.__ftp.cwd(path)
            return True
        except Exception as e:
            log.debug(e)
            return False

    def download(self, remote_path: str, local_path: str):
        """
        下载整个目录
        Args:
            remote_path: FTP远程路径，如：/version_test/dynamic/android/
            local_path: 本地路径，一定是目录，且末尾不能含有斜线，如：./

        Returns:

        """
        log.info("=== remote_path: {} ===".format(remote_path))
        log.info("=== local_path: {} ===".format(local_path))
        if self.is_dir(remote_path):
            self.__ftp.cwd(remote_path)
            for i in self.__ftp.nlst():
                self.download(remote_path + "/" + i, local_path)
        else:
            local_path = local_path + remote_path
            p = Path(local_path).parent
            if not p.exists():
                os.makedirs(p)
            self.download_file(remote_path, local_path)

    def download_file(self, remote_path: str, local_path: str, delete_exists: bool = True):
        """
        下载单个文件
        Args:
            remote_path: FTP远程路径，如：/version_test/dynamic/android/res_list_v27_v1058.zip
            local_path: 本地路径，一定要包含文件名称，如：./version_test/dynamic/android/res_list_v27_v1058.zip
            delete_exists: 是否要删除原来已经存在的文件
        Returns:

        """
        log.info("local_path: {}".format(local_path))
        log.info("remote_path: {}".format(remote_path))

        # 如果文件存在，则删除原先的文件
        if os.path.exists(local_path) and delete_exists:
            log.debug("delete local {} before download from {}".format(local_path, remote_path))
            os.remove(local_path)

        buffer_size = 10240  # 默认是8192
        log.info(self.__ftp.getwelcome())  # 显示登录ftp信息

        # 将传输模式改为二进制模式 ,避免提示 ftplib.error_perm: 550 SIZE not allowed in ASCII
        self.__ftp.voidcmd("TYPE I")
        remote_file_size = self.__ftp.size(remote_path)  # 文件总大小

        log.info("remote filesize [{}]".format(remote_file_size))
        download_size = 0  # 下载文件初始大小
        local_size = 0
        # check local file isn't exists and get the local file size
        if os.path.exists(local_path):
            local_size = os.stat(local_path).st_size
        if local_size >= remote_file_size:
            log.info("local file is bigger or equal remote file")
            # return
        start = time.time()
        conn = self.__ftp.transfercmd("RETR {0}".format(remote_path), local_size)

        f = open(local_path, "ab")
        while True:
            data = conn.recv(buffer_size)
            if not data:
                break
            f.write(data)
            download_size += len(data)
            self.__progress_bar(download_size, remote_file_size)
        f.close()
        try:
            self.__ftp.voidcmd("NOOP")
            log.info("keep alive cmd success")
            self.__ftp.voidresp()
            log.info("No loop cmd")
            conn.close()
            # self.__ftp.quit()
        except Exception as e:
            log.info(e)
        finally:
            end = time.time()
            log.info("consume time [{}]".format(end - start))
            # file_size = os.stat(local_path).st_size
            # log.info('local filesize [{}] md5:[{}]'.format(
            #     file_size, file_util.get_md5(local_path)))

    def __progress_bar(self, download_size, total_size):
        """
        ftp下载显示进度条
        :param download_size: 已经下载的大小
        :param total_size: 总大小
        :return:
        """
        download_percent = download_size / total_size * 100
        if download_percent - self.__download_percent > 5 or download_percent == 100:
            self.__download_percent = download_percent
            log.debug("[{:50s}] {:.2f}%".format("=" * int(math.floor(download_percent / 2)), download_percent))

    def list_dir(self, path: str):
        self.__ftp.cwd(path)
        return self.__ftp.nlst()
