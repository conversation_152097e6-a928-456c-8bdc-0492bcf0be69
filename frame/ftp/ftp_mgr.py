import math
import os
import socket
import time
from ftplib import FTP, FTP_TLS, error_perm


from frame.common.common import common
from frame.exception.exception import PyframeException
from frame.log.log import log
from frame.path_mgr.path_mgr import path_mgr


class FtpMgr(object):
    def __init__(self, ip: str, port: int, username: str, password: str, is_tls: bool = False):
        """
        FTP上传下载
        Args:
            ip: FTP服务器IP
            port: FTP服务器端口
            username: 用户名
            password: 密码
            is_tls: 是否使用TLS加密
        """
        self._ip = ip
        self._port = port
        self._username = username
        self._password = password
        socket.setdefaulttimeout(60)  # 超时FTP时间设置为60秒
        self._ftp = FTP() if not is_tls else FTP_TLS()
        try:
            self._ftp.connect(host=self._ip, port=self._port)
        except Exception as e:
            log.error(e)
            raise PyframeException(f"连接FTP: {self._ip}失败, 异常: {e}")
        self._ftp.set_debuglevel(0)
        self._ftp.encoding = "utf-8"
        self._download_percent = 0

        try:
            self._ftp.login(self._username, self._password)
            log.info(f"{self._username} login ftp {self._ftp.getwelcome()}")
        except Exception as e:
            log.error(e)
            raise PyframeException(f"登录FTP: {self._ip}失败, 异常: {e}")

    def _mkdir(self, path: str, first_in: bool = True):
        try:
            self._ftp.cwd(path)
        except error_perm as e:
            log.warn(e)
            self._mkdir(os.path.dirname(path), False)
            log.info(f"创建文件夹 {path}")
            self._ftp.mkd(path)
            if first_in:
                self._ftp.cwd(path)

    def upload_folder(self, src: str, dst: str):
        """
        从本地上传文件夹到ftp
        Args:
            src: 本地路径，如frame
            dst: FTP远程路径，如version_test/
        """
        # for path, folders, files in os.walk(self.src):
        #     dst_rel = path.replace(self.src, '')
        #     for filename in files:
        #         src_abs = os.path.join(path, filename)
        #         if src_abs == self.dst:
        #             continue
        #         t.add(src_abs, os.path.join(dst_rel, filename))
        # if self.is_dir(src):
        #     for path, _, files in os.walk(src):
        #         dst
        #     self.__ftp.cwd(src)
        #     for i in self.__ftp.nlst():
        #         temp_src = os.path.join(src, i).replace("\\", "/")
        #         temp_dst = os.path.join(dst, i).replace("\\", "/")
        #         self.download_folder(src=temp_src, dst=temp_dst)
        # else:
        #     self.upload_file(src, dst)

    def upload_file(self, src: str, dst: str):
        """
        从本地上传文件到ftp，src需要具体到文件名，dst

        Args:
            src: 本地路径，具体到文件名
            dst: FTP远程路径，不具体到文件名
        Returns:

        """
        log.info(f"src: {src}")
        log.info(f"dst: {dst}")

        buffer_size = 10240
        self._mkdir(path=dst)

        fp = open(src, "rb")

        try:
            self._ftp.cwd(dst)
            # 将传输模式改为二进制模式 ,避免提示 ftplib.error_perm: 550 SIZE not allowed in ASCII
            self._ftp.voidcmd("TYPE I")
            self._ftp.storbinary("STOR {}".format(os.path.basename(src)), fp, buffer_size)
            self._ftp.set_debuglevel(0)
            # log.info(os.path.dirname(dst))

        # except error_perm as e:
        #     log.error('文件[{}]传输有误,{}'.format(src, str(e)))
        # except TimeoutError:
        #     log.error('文件[{}]传输超时'.format(src))
        except Exception as e:
            log.error(f"上传文件{src}失败, 异常: {str(e)}")
        finally:
            fp.close()

    def is_dir(self, path: str) -> bool:
        """
        判断FTP当前路径是否为目录
        Args:
            path: FTP远程路径，如：/version_test/dynamic/android/

        Returns:
            bool: 是否为目录
        """
        try:
            self._ftp.cwd(path)
        except Exception as e:
            log.debug(e)
            return False
        return True

    def download_folder(self, src: str, dst: str):
        """
        下载整个目录
        Args:
            src: FTP远程路径，如：/version_test/dynamic/android/
            dst: 本地路径，一定是目录，且末尾不能含有斜线，如：./
        """
        log.info(f"src: {src}")
        log.info(f"dst: {dst}")
        start_time = time.time()
        if self.is_dir(src):
            self._ftp.cwd(src)
            # with ThreadPoolExecutor() as executor:
            #     futures = []
            #     for i in self._ftp.nlst():
            #         temp_src = os.path.join(src, i).replace("\\", "/")
            #         temp_dst = os.path.join(dst, i).replace("\\", "/")
            #         future = executor.submit(
            #             self.download_file,
            #             args=(
            #                 temp_src,
            #                 temp_dst,
            #             ),
            #         )
            #         futures.append(future)
            #         # self.download_folder(src=temp_src, dst=temp_dst)
            #     wait(futures, return_when=ALL_COMPLETED)
            for i in self._ftp.nlst():
                temp_src = os.path.join(src, i).replace("\\", "/")
                temp_dst = os.path.join(dst, i).replace("\\", "/")
                self.download_folder(src=temp_src, dst=temp_dst)

        else:
            self.download_file(src, dst)
        log.info(f"cost time: {common.format_duration(time.time() - start_time)}")

    # @retry(stop=stop_after_attempt(3), wait=wait_fixed(5))
    def download_file(self, src: str, dst: str, process_bar: bool = False):
        """
        下载单个文件
        Args:
            src: FTP远程路径，如：/version_test/dynamic/android/res_list_v27_v1058.zip
            dst: 本地路径，一定要包含文件名称，如：./version_test/dynamic/android/res_list_v27_v1058.zip
            process_bar: 是否显示进度条，默认显示
        """
        log.info(f"src: {src}")
        log.info(f"dst: {dst}")
        path_mgr.mkdir(path=os.path.dirname(os.path.abspath(dst)))
        # 如果文件存在，则删除原先的文件
        if os.path.exists(dst):
            log.debug(f"delete {dst} before download {src}")
            path_mgr.rm(dst)

        buffer_size = 10240  # 默认是8192

        # 将传输模式改为二进制模式 ,避免提示 ftplib.error_perm: 550 SIZE not allowed in ASCII
        self._ftp.voidcmd("TYPE I")
        remote_file_size = self._ftp.size(src)  # 文件总大小

        log.info(f"remote filesize {remote_file_size}")
        download_size = 0  # 下载文件初始大小
        local_size = 0
        start_time = time.time()
        conn = self._ftp.transfercmd("RETR {0}".format(src), local_size)

        f = open(dst, "ab")
        while True:
            data = conn.recv(buffer_size)
            if not data:
                break
            f.write(data)
            download_size += len(data)
            if process_bar:
                self._progress_bar(download_size, remote_file_size)
        f.close()
        try:
            self._ftp.voidcmd("NOOP")
            self._ftp.voidresp()
            conn.close()
        except Exception as e:
            log.warn(e)
        log.info(f"cost time {common.format_duration(time.time() - start_time)}")

    def _progress_bar(self, current_size, total_size):
        """
        ftp传输进度条
        Args:
            current_size: 已经传输的大小
            total_size: 总大小
        """
        current_percent = current_size / total_size * 100
        if current_percent - self._download_percent > 20 or current_percent == 100:
            self._download_percent = current_percent
            log.debug("[{:50s}] {:.2f}%".format("=" * int(math.floor(current_percent / 2)), current_percent))

    def dirs(self, path: str) -> list:
        """
        获取ftp目录下所有文件 包含目录
        Args:
            path: 目录地址
        """
        self._ftp.cwd(path)
        return self._ftp.nlst()

    def exists_file(self, path: str) -> bool:
        """
        判断文件是否存在
        Args:
            path: 文件地址
        Returns:
            bool: 是否存在
        """
        try:
            self._ftp.size(path)
        except Exception as e:
            log.warn(f"{path} does not exist, exception: {e}")
            return False
        return True

    def exists_folder(self, path: str) -> bool:
        """
        判断目录是否存在
        Args:
            path: 目录地址
        Returns:
            bool: 是否存在
        """
        try:
            self._ftp.cwd(path)
        except Exception as e:
            log.warn(f"{path} does not exist, exception: {e}")
            return False
        return True

    def delete_file(self, path: str) -> bool:
        """
        删除FTP上指定文件
        Args:
            path: 文件地址

        Returns: 是否删除成功
        """
        exist = self.exists_folder(os.path.dirname(path))
        if not exist:
            log.warn(f"{path} does not exist")
            return False

        try:
            # 切换到文件所在目录
            self._ftp.cwd(os.path.dirname(path))
            log.info(f"cwd {os.path.dirname(path)} success")
        except Exception as e:
            log.warn(f"{path} does not exist, exception: {e}")
            return False

        try:
            self._ftp.delete(os.path.basename(path))
            log.info(f"delete {path} success")
        except Exception as e:
            log.warn(f"{path} does not exist, exception: {e}")
            return False
        return True
