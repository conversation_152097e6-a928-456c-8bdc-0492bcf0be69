# coding=utf-8
import os
from enum import Enum
from pathlib import Path
from typing import List
from datetime import datetime, timedelta

from frame.cmd.cmd import cmd, log
from frame.exception.exception import PyframeException
from frame.git_mgr.blame import Blame


class GitMgr:
    def __init__(self, workdir: str, project_name: str):
        self._workdir = workdir
        self._project_name = project_name
        self._project_dir = os.path.join(self._workdir, project_name)
        # ret = cmd.run_shell(
        #     cmds=[
        #         "git --version"
        #     ],
        #     verbose=cmd.Verbose.NONE,
        # )
        # if ret[0] != 0:
        #     log.error("git maybe not exist")
        #     raise PyframeException("git不存在, 请先安装git")

    def workdir(self) -> str:
        return self._workdir

    def project_name(self) -> str:
        return self._project_name

    def project_dir(self) -> str:
        return self._project_dir

    def version(self) -> str:
        """
        获取git版本
        Returns:
            git版本
        """
        ret = cmd.run_shell(
            cmds=["git --version"],
            workdir=self._project_dir,
            verbose=cmd.Verbose.NONE,
            return_output=True,
        )
        if ret[0] != 0:
            log.error("git --version error")
            raise PyframeException("git不存在, 请先安装git")
        return ret[1][0]

    def exec(self, command: str):
        """
        执行git命令
        Args:
            command: git命令
        """
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
            errors="ignore",
        )
        if ret[0] != 0:
            log.error(f"{command} failed")
            raise PyframeException(f"{command}执行失败")

        return ret[1]

    def add(self, file_path: str = "."):
        """
        git add
        Args:
            file_path: 文件路径
        """
        self.exec(f"git add {file_path}")

    def get_staged_files(self):
        """
        获取暂存区文件列表
        """
        return self.exec("git diff --staged --name-only")

    def exist(self) -> bool:
        b = Path(self._project_dir).exists() and Path(os.path.join(self._project_dir, ".git")).exists()
        if not b:
            log.warn(f"{self._project_dir} doesn't exist")
        return b

    def get_current_branch(self) -> str:
        """
        获取当前分支
        Returns:
            本地当前分支
        """
        ret = cmd.run_shell(
            cmds=[
                "git branch --show-current",
            ],
            workdir=self._project_dir,
            return_output=True,
        )
        if ret[0] != 0:
            log.error("git branch error")
            raise PyframeException("获取当前分支失败")
        current_branch = ret[1]
        log.debug(current_branch[0])
        return current_branch[0]

    def get_commit_status(self, commit_id: str) -> List[str]:
        ret = cmd.run_shell(
            cmds=[
                f"git show --name-status {commit_id}",
            ],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"git show --name-status {commit_id} error")
            raise PyframeException("获取提交状态失败")
        return ret[1]

    # def get_local_latest_commit_id(self) -> str:
    #     """
    #     获取当前commit id
    #     Returns:
    #         当前commit id
    #     """
    #     ret = cmd.run_shell(
    #         cmds=[
    #             "git rev-parse HEAD",
    #         ],
    #         workdir=self._project_dir,
    #         return_output=True
    #     )
    #     if ret[0] != 0:
    #         log.error("git rev-parse HEAD error")
    #         raise PyframeException("获取当前commit id失败")
    #     commit_id = ret[1]
    #     log.debug(commit_id[0])
    #     return commit_id[0]

    def get_local_latest_commit_id(self, short: bool = False) -> str:
        """
        获取本地当前分支最新的commit_id
        """
        cmds = ["git", "rev-parse"]
        if short:
            cmds.append("--short")
        cmds.append("HEAD")

        ret = cmd.run_shell(
            cmds=[" ".join(cmds)],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"git rev-parse error")
            raise PyframeException(f"git rev-parse HEAD失败")
        return ret[1][0].strip()

    def get_local_latest_commit_msg(self) -> str:
        """
        获取本地最新一次提交的信息
        Returns:
            str: 本地最新一次提交的信息
        """
        ret = cmd.run_shell(
            cmds=["git log -1 --pretty=%B"],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"git log -1 --pretty=%B error")
            raise PyframeException(f"git log -1 --pretty=%B失败")
        return ret[1][0].strip()

    # def get_local_latest_commit_time(self) -> str:
    #     """
    #     获取本地最新一次提交的时间
    #     Returns:
    #         str: 本地最新一次提交的时间
    #     """
    #     get_time_cmd = "git log --pretty=format:'%ad' --date=iso -1"
    #     ret = cmd.run_shell(
    #         cmds=[get_time_cmd],
    #         workdir=self._project_dir,
    #         return_output=True,
    #         encoding="utf-8",
    #     )
    #     if ret[0] != 0:
    #         log.error(f"{get_time_cmd} error")
    #         raise PyframeException(f"{get_time_cmd} 失败")
    #     return ret[1][0].strip()

    def get_local_latest_committer_name(self) -> str:
        """
        获取本地最新一次提交人的名字
        Returns:
            str: 本地最新一次提交人的名字
        """
        ret = cmd.run_shell(
            cmds=["git log -1 --pretty=%cn"],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"git log -1 --pretty=%cn error")
            raise PyframeException(f"git log -1 --pretty=%cn失败")
        return ret[1][0].strip()

    def get_local_latest_committer_email(self) -> str:
        """
        获取本地最新一次提交人的邮箱
        Returns:
            str: 本地最新一次提交人的邮箱
        """
        ret = cmd.run_shell(
            cmds=["git log -1 --pretty=%ce"],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"git log -1 --pretty=%ce error")
            raise PyframeException(f"git log -1 --pretty=%ce失败")
        return ret[1][0].strip()

    def get_local_latest_commit_time(self) -> str:
        """
        获取本地最新一次提交的时间
        Returns:
            str: 本地最新一次提交的时间
        """
        ret = cmd.run_shell(
            cmds=["git log -1 --pretty=%ci"],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"git log -1 --pretty=%ci error")
            raise PyframeException(f"git log -1 --pretty=%ci失败")
        commit_time = ret[1][0].strip()
        if "+0000" in commit_time:
            result = datetime.strptime(commit_time, "%Y-%m-%d %H:%M:%S +0000") + timedelta(hours=8)
            commit_time = result.strftime("%Y-%m-%d %H:%M:%S")
        elif "+0800" in commit_time:
            result = datetime.strptime(commit_time, "%Y-%m-%d %H:%M:%S +0800")
            commit_time = result.strftime("%Y-%m-%d %H:%M:%S")

        return commit_time

    def get_local_latest_commit_hash(self) -> str:
        """
        获取本地最新一次提交的Hash值
        Returns:
            str: 本地最新一次提交的Hash值
        """
        get_hash_cmd = "git rev-parse --verify HEAD"
        ret = cmd.run_shell(
            cmds=[get_hash_cmd],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"{get_hash_cmd} error")
            raise PyframeException(f"{get_hash_cmd}失败")
        hash = ret[1][0].strip()
        return hash

    def get_local_modified_files(self) -> List[str]:
        """
        获取修改过的文件
        Returns:
            修改过的文件列表
        """
        ret = cmd.run_shell(
            cmds=[
                "git status --porcelain",
            ],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
            errors="ignore",
        )
        if ret[0] != 0:
            log.error("git status --porcelain error")
            raise PyframeException("获取修改过的文件失败")
        changed_files = ret[1]
        modified_files = []
        for file in changed_files:
            elems = file.strip().split(" ")
            # log.debug(elems)
            if len(elems) == 2 and elems[0] == "M":
                modified_files.append(elems[1])
        # log.debug(modified_files)
        return modified_files

    def get_local_deleted_files(self) -> List[str]:
        """
        获取删除过的文件
        Returns:
            删除过的文件列表
        """
        ret = cmd.run_shell(
            cmds=[
                "git status --porcelain",
            ],
            workdir=self._project_dir,
            return_output=True,
        )
        if ret[0] != 0:
            log.error("git status --porcelain error")
            raise PyframeException("获取删除过的文件失败")
        changed_files = ret[1]
        deleted_files = []
        for file in changed_files:
            elems = file.strip().split(" ")
            # log.debug(elems)
            if len(elems) == 2 and elems[0] == "D":
                deleted_files.append(elems[1])
        # log.debug(deleted_files)
        return deleted_files

    def get_local_new_files(self) -> List[str]:
        """
        获取新增的文件
        Returns:
            新增的文件列表
        """
        ret = cmd.run_shell(
            cmds=[
                "git status --porcelain",
            ],
            workdir=self._project_dir,
            return_output=True,
        )
        if ret[0] != 0:
            log.error("git status --porcelain error")
            raise PyframeException("获取新增的文件失败")
        changed_files = ret[1]
        new_files = []
        for file in changed_files:
            elems = file.strip().split(" ")
            # log.debug(elems)
            if len(elems) == 2 and elems[0] == "??":
                new_files.append(elems[1])
        # log.debug(new_files)
        return new_files

    def get_remote_modified_files(self, branch: str) -> List[str]:
        """
        获取远程分支修改过的文件
        Args:
            branch: 远程分支
        Returns:
            修改过的文件列表
        """
        ret = cmd.run_shell(
            cmds=[
                f"git diff --name-only origin/{branch}",
            ],
            workdir=self._project_dir,
            return_output=True,
        )
        if ret[0] != 0:
            log.error("git diff --name-only error")
            raise PyframeException("获取远程分支修改过的文件失败")
        modified_files = ret[1]
        log.debug(modified_files)
        return modified_files

    def get_remote_modified_files_between(self, old_commit_id: str, new_commit_id: str) -> List[str]:
        """
        获取两个commit id之间修改过的文件
        Args:
            old_commit_id: 旧commit id
            new_commit_id: 新commit id
        Returns:
            修改过的文件列表
        """
        ret = cmd.run_shell(
            cmds=[
                f"git diff --name-only {old_commit_id} {new_commit_id}",
            ],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error("git diff --name-only error")
            raise PyframeException("获取两个commit id之间修改过的文件失败")
        modified_files = ret[1]
        log.debug(modified_files)
        return modified_files

    def get_remote_modified_files_with_status_between(self, old_commit_id: str, new_commit_id: str) -> List[str]:
        """获取两个commit id之间修改过的文件及状态

        Args:
            old_commit_id (str): _description_
            new_commit_id (str): _description_

        Raises:
            PyframeException: _description_

        Returns:
            List[str]: 返回文件列表
        """
        ret = cmd.run_shell(
            cmds=[
                f"git diff --name-status {old_commit_id} {new_commit_id}",
            ],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error("git diff --name-status error")
            raise PyframeException("获取两个commit id之间修改过的文件及状态失败")
        modified_files = ret[1]
        log.debug(modified_files)
        return modified_files

    def get_local_branches(self) -> list:
        """
        获取本地分支
        Returns:
            本地分支列表
        """
        ret = cmd.run_shell(
            cmds=[
                "git branch",
            ],
            workdir=self._project_dir,
            return_output=True,
        )
        if ret[0] != 0:
            log.error("git branch error")
            raise PyframeException("获取本地分支失败")
        branches = ret[1]
        local_branches = []
        for branch in branches:
            local_branches.append(branch.strip().strip("* "))
        log.debug(local_branches)
        return local_branches

    def get_remote_branches(self) -> list:
        """
        获取远程的分支列表
        Returns:
            list: 远程分支列表
        """
        ret = cmd.run_shell(
            cmds=[
                "git branch -r",
            ],
            workdir=self._project_dir,
            return_output=True,
        )
        if ret[0] != 0:
            log.error("git branch error")
            raise PyframeException("获取远程分支失败")
        branches = ret[1]
        local_branches = []
        for branch in branches:
            local_branches.append(branch.split("origin/")[-1])
        log.debug(local_branches)
        return local_branches

    def get_content_by_tag(self, tag) -> str:
        """
        根据tag号获取对应的content信息
        Returns:
            str: content信息
        """
        ret = cmd.run_shell(
            cmds=[
                f"git tag -l --format='%(contents)' {tag}",
            ],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error("git tag -l error")
            raise PyframeException("获取tag描述失败")

        return ret[1][0]

    def get_all_tags_with_content(self) -> list:
        """
        获取所有tag信息
        Returns:
            list: tag信息列表
        """
        ret = cmd.run_shell(
            cmds=[
                "git tag -l --format='%(tag) %(contents)'",
            ],
            workdir=self._project_dir,
            return_output=True,
        )
        if ret[0] != 0:
            log.error("git branch error")
            raise PyframeException("获取所有tag信息")
        tags = {}
        for line in ret[1]:
            line = line.strip()
            if len(line) == 0:
                break
            index = line.find(" ")
            tag, content = line[:index], line[index + 1 :]
            tags[tag] = content
        return tags

    def pull(self, branch: str = None):
        """
        git pull
        Args:
            branch: 分支名
        """
        if branch is None:
            self.exec(command="git pull")
        else:
            self.exec(command=f"git pull origin {branch}")

    def advance_pull(self, branch: str, commit_id: str = None):
        """复杂拉取"""
        # 获取本地当前分支
        if branch == self.get_current_branch():
            ret = cmd.run_shell(
                cmds=["git config pull.rebase false", f"git pull origin {branch}"],
                workdir=self._project_dir,
                encoding="utf-8",
            )
            if ret[0] != 0:
                log.error("git pull error")
                raise PyframeException("git pull执行失败")
        elif branch in self.get_local_branches():
            ret = cmd.run_shell(
                cmds=["git config pull.rebase false", f"git checkout {branch}", f"git pull origin {branch}"],
                workdir=self._project_dir,
                encoding="utf-8",
            )
            if ret[0] != 0:
                log.error("git pull error")
                raise PyframeException("git pull执行失败")
        else:
            ret = cmd.run_shell(
                cmds=["git fetch", "git config pull.rebase false", f"git checkout -b {branch} origin/{branch}"],
                workdir=self._project_dir,
                encoding="utf-8",
            )
            if ret[0] != 0:
                log.error("git pull error")
                raise PyframeException("git pull执行失败")

        if commit_id is not None:
            ret = cmd.run_shell(
                cmds=[f"git reset --hard {commit_id}"],
                workdir=self._project_dir,
                encoding="utf-8",
            )
            if ret[0] != 0:
                log.error(f"git reset --hard {commit_id} error")
                raise PyframeException(f"git reset --hard {commit_id}执行失败")

    def checkout(self, branch_or_tag):
        ret = cmd.run_shell(
            cmds=["git config pull.rebase false", f"git checkout {branch_or_tag}"],
            workdir=self._project_dir,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"git checkout {branch_or_tag} error")
            raise PyframeException(f"git checkout {branch_or_tag}执行失败")

    def new_branch(self, branch_or_tag):
        ret = cmd.run_shell(
            cmds=["git config pull.rebase false", f"git checkout -b {branch_or_tag}"],
            workdir=self._project_dir,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"git checkout -b {branch_or_tag} error")

    def rm(self, file: str):
        ret = cmd.run_shell(
            cmds=[f"git rm {file}"],
            workdir=self._project_dir,
        )
        if ret[0] != 0:
            log.error(f"git rm {file} error")
            raise PyframeException(f"git rm {file}执行失败")

    def clone(self, url: str, branch: str = "master"):
        """
        克隆仓库
        Args:
            url: 仓库地址
            branch: 仓库的分支
        """
        commands = ["git clone"]
        if branch != "master":
            commands.append(f"-b {branch}")
        commands.append(url)
        commands.append(self._project_name)
        ret = cmd.run_shell(
            cmds=[" ".join(commands)],
            workdir=self._workdir,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"{' '.join(commands)} error")
            raise PyframeException(f"{' '.join(commands)}执行失败")

    def clone_with_password(self, url: str, branch: str = "master", username: str = "", password: str = ""):
        """
        使用账号密码克隆仓库
        Args:
            url: 仓库地址
            branch: 仓库的分支
            username: 用户名
            password: 密码
        """
        commands = ["git clone"]
        if branch != "master":
            commands.append(f"-b {branch}")
        if len(username) == 0 or len(password) == 0:
            raise PyframeException("missing username or password")
        username = username.replace("@", "%40")
        elems = url.split("//")
        url = f"{elems[0]}//{username}:{password}@{elems[1]}"
        commands.append(url)
        commands.append(self._project_name)
        ret = cmd.run_shell(
            cmds=[" ".join(commands)],
            workdir=self._workdir,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"{' '.join(commands)} error")
            raise PyframeException(f"{' '.join(commands)}执行失败")

    def get_romote_heads(self, url: str, username: str = "", password: str = ""):
        if username == "" or password == "":
            log.error("missing username or password")
            return
        cmds = "git ls-remote --head"
        elems = url.split("//")
        url = f"{elems[0]}//{username}:{password}@{elems[1]}"
        cmds += " " + url
        ret = cmd.run_shell(
            cmds=[cmds],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"git ls-remote -v error")
            raise PyframeException(f"git remote -v执行失败")
        branches = []
        for branch in ret[1]:
            if branch == "":
                continue
            branch = branch.split("refs/heads/")[1]
            branches.append(branch)
        return branches

    def clone_with_oauth2(self, url: str, branch: str = "master", oauth2: str = ""):
        """
        使用access_token克隆仓库
        Args:
            url: 仓库地址
            branch: 仓库的分支
            oauth2: 仓库的access_token
        """
        if len(oauth2) == 0:
            raise PyframeException("missing oauth2")

        commands = ["git clone"]
        if branch != "master":
            commands.append(f"-b {branch}")
        elems = url.split("//")
        url = f"{elems[0]}//oauth2:{oauth2}@{elems[1]}"
        commands.append(url)
        commands.append(self._project_name)
        ret = cmd.run_shell(
            cmds=[" ".join(commands)],
            workdir=self._workdir,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"{' '.join(commands)} error")
            raise PyframeException(f"{' '.join(commands)}执行失败")

    def clean(self, x: bool = True, d: bool = True, force: bool = True, exclude: str = None):
        """
        git clean
        Args:
            x: x
            d: d
            force: force
            exclude: exclude
        """
        commands = ["git clean"]
        if x:
            commands.append("-x")
        if d:
            commands.append("-d")
        if force:
            commands.append("--force")
        if exclude is not None:
            commands.append(f"--exclude={exclude}")
        ret = cmd.run_shell(
            cmds=[" ".join(commands)],
            workdir=self._project_dir,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"{' '.join(commands)} error")
            raise PyframeException(f"{' '.join(commands)}执行失败")

    class Mode(Enum):
        SOFT = 0
        MIXED = 1
        HARD = 2
        MERGE = 3
        KEEP = 4

    def reset(self, mode: Mode = Mode.HARD, commit_id: str = "", branch: str = ""):
        """
        git reset
        Args:
            mode: mode
            commit_id: commit_id
        """
        commands = ["git reset"]
        if mode == self.Mode.SOFT:
            commands.append("--soft")
        elif mode == self.Mode.MIXED:
            commands.append("--mixed")
        elif mode == self.Mode.HARD:
            commands.append("--hard")
        elif mode == self.Mode.KEEP:
            commands.append("--keep")
        commands.append(commit_id)
        if branch != "":
            commands.append(f"origin/{branch}")
        ret = cmd.run_shell(
            cmds=[" ".join(commands)],
            workdir=self._project_dir,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"{' '.join(commands)} error")
            raise PyframeException(f"{' '.join(commands)}执行失败")

    def fetch(self, branch: str = "", all: bool = False, force: bool = False):
        """
        拉取代码到版本库
        Args:
            branch: 分支
            all: 拉取所有远程
            force: 是否强制拉取
        """
        commands = ["git fetch"]
        if all:
            commands.append("--all")
        elif branch != "":
            commands.append(f"origin {branch}")
        if force:
            commands.append("-f")

        ret = cmd.run_shell(
            cmds=[" ".join(commands)],
            workdir=self._project_dir,
        )
        if ret[0] != 0:
            log.error(f"{' '.join(commands)} error")
            raise PyframeException(f"{' '.join(commands)}执行失败")

    def config_local_username(self, username: str):
        """
        设置仓库提交者的用户名
        Args:
            username: 用户名
        """
        self.exec(command=f"git config --local user.name '{username}'")

    def config_local_email(self, email: str):
        """
        设置仓库提交者的邮箱
        Args:
            email: 用户邮箱
        """
        self.exec(command=f"git config --local user.email '{email}'")

    def blame(self, path: str, line: int) -> Blame:
        """
        获取指定文件最近一次的提交信息
        Args:
            path: 文件路径
            line: 行号
        """
        ret = cmd.run_shell(
            cmds=[f'git blame "{path}" -L {line},+1 -fne --porcelain'],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f'git blame "{path}" -L {line},+1 -fne error')
            raise PyframeException(f"git blame {path} -L {line},+1 -fne 失败")

        blame = Blame()
        for line in ret[1]:
            if line.startswith("committer "):
                committer = line.split(" ")[1]
                blame.committer = committer
            elif line.startswith("committer-mail "):
                committer_mail = line.split(" ")[1].strip("<>")
                blame.committer_mail = committer_mail
            elif line.startswith("committer-time "):
                committer_time = line.split(" ")[1]
                blame.committer_time = committer_time
            elif line.startswith("committer-tz "):
                committer_tz = line.split(" ")[1]
                blame.committer_tz = committer_tz
            elif line.startswith("summary "):
                summary = line.split(" ", 1)[1]
                blame.summary = summary
            elif line.startswith("filename "):
                filename = line.split(" ", 1)[1]
                blame.filename = filename
        return blame

    def ls_files(self, pattern: str) -> List[str]:
        """
        获取当前仓库符合指定模式的文件路径
        Args:
            pattern: 模式
        示例:
            pattern = "*/campaign/extends/return_center/return_center_campaign.cpp"
            paths = ["mobile_dancer/trunk/server/products/Project_DGM/components/campaign/extends/return_center/return_center_campaign.cpp"]
        """
        ret = cmd.run_shell(
            cmds=[f"git ls-files {pattern}"],
            workdir=self._project_dir,
            return_output=True,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"git ls-files {pattern} error")
            raise PyframeException(f"git ls-files {pattern}失败")
        paths = []
        for path in ret[1]:
            paths.append(path.strip())
        return paths

    def commit(self, message: str, none_commit_exception: bool = False):
        """
        提交代码到本地仓库
        Args:
            message: 提交信息
            none_commit_exception: 没有修改是否抛出异常
        """
        ret = cmd.run_shell(
            cmds=[f'git commit -m "{message}"'],
            workdir=self._project_dir,
            encoding="utf-8",
            return_output=True,
        )
        if ret[0] != 0:
            for line in ret[1]:
                if "nothing to commit" in line or "no changes added to commit" in line:
                    if none_commit_exception:
                        raise PyframeException("git commit失败, nothing to commit")
                    else:
                        log.warn(f"{line}")
                        return
            log.error(f"git commit -m '{message}' error")
            raise PyframeException(f"git commit -m '{message}'失败")

    def push(self, branch: str = None, force: bool = False):
        """
        提交代码到远程仓库
        Args:
            branch: 分支
            force: 是否强制提交
        """
        cmds = ["git push"]

        if branch:
            cmds.append(f"origin {branch}")
        if force:
            cmds.append("--force")

        ret = cmd.run_shell(
            cmds=[" ".join(cmds)],
            workdir=self._project_dir,
            encoding="utf-8",
        )
        if ret[0] != 0:
            log.error(f"{' '.join(cmds)} error")
            raise PyframeException(f"{' '.join(cmds)}失败")
