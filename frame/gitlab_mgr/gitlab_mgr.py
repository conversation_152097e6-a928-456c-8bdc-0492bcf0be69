# coding=utf-8
# import datetime
import time
from datetime import datetime, timedelta

import gitlab

from frame.exception.exception import PyframeException
from frame.log.log import log


class GitlabMgr:
    def __init__(self, url: str, token: str, project: str):
        self._url = url
        self._token = token
        self._project_name = project
        gl = gitlab.Gitlab(self._url, self._token)
        self._project = gl.projects.get(project)

    def _get_format_time_commits(self, *args, **kwargs) -> list:
        """
        获取指定分支的commit列表，格式化commit时间
        """
        results = []

        def format_time(timestr) -> datetime:
            # 3.6 没有 isoformat，不支持 %z 格式化
            isoformat = "%Y-%m-%dT%H:%M:%S.000+00:00"
            isoformat1 = "%Y-%m-%dT%H:%M:%S.000+08:00"
            if "000+00:00" in commit.committed_date:
                result = datetime.strptime(timestr, isoformat) + timedelta(hours=8)
            else:
                result = datetime.strptime(timestr, isoformat1)
            return result.strftime("%Y-%m-%dT%H:%M:%S")

        for commit in self._project.commits.list(*args, **kwargs):
            commit.committed_date = format_time(commit.committed_date)
            results.append(commit)
        return results

    def get_newest_pipeline_id(self, branch: str) -> str:
        """
        获取指定branch分支上最新的pipeline_id
        Args:
            branch: 分支名
        Returns:
            str: pipeline_id
        """
        pipelines = self._project.pipelines.list(per_page=10, ref=branch)
        if len(pipelines) == 0:
            raise PyframeException(f"{self._project_name} doesn't have pipeline for branch {branch}")
        for pipeline in pipelines:
            log.debug(pipeline)
            break  # 打印第一个pipeline
        return pipelines[0].id

    def get_commit_id_by_pipeline_id(self, branch: str, pipeline_id: int):
        for page in range(1, 1000):  # 最多1000页，可能有问题
            pipelines = self._project.pipelines.list(per_page=100, page=page, ref=branch)
            if len(pipelines) == 0:
                log.info(f"page {page} is empty")
                break
            for pipeline in pipelines:
                if pipeline.id == pipeline_id:
                    return pipeline.sha
        # pipelines = self.__project.pipelines.list(all=True, ref=branch)
        # for pipeline in pipelines:
        #     if pipeline.id == pipeline_id:
        #         return pipeline.sha
        raise PyframeException(f"{self._project_name}仓库不存在pipeline id：{pipeline_id}")

    def get_newest_commit_id(self, branch: str, short: bool = False) -> str:
        """
        获取branch分支最新的commit_id
        Args:
            branch: 分支
            short: 是否返回短commit_id
        Returns:
            str: commit_id
        """
        # commits = self.__project.commits.list(per_page=10, ref_name=branch)
        commits = self._get_format_time_commits(per_page=10, ref_name=branch)
        if len(commits) == 0:
            raise PyframeException(f"{self._project_name} doesn't have commit for branch {branch}")
        for commit in commits:
            log.debug(commit)
            break  # 打印第一个commit

        if short:
            return commits[0].short_id
        else:
            return commits[0].id

    def get_all_branches(self) -> list:
        """
        获取所有分支
        Returns:
            list: 分支列表
        """
        ret = self._project.branches.list(all=True)
        branches = []
        if len(ret) != 0:
            branches = [b.name for b in ret]
        return branches

    def turn_tag_to_branch(self, tag: str, branch: str = None, get_all=True) -> bool:
        """
        将tag转换为branch
        Args:
            tag:
            branch:

        Returns:

        """
        tags = self._project.tags.list(get_all=get_all)
        tags = [t.name for t in tags]
        if tag not in tags:
            raise PyframeException(f"tag {tag} doesn't exist")

        # 创建分支对象, 如果分支存在，先删除
        if self.is_branch_exist(tag):
            log.info(f"branch {tag} is exist, delete it first")
            self.delete_branch(tag)

        # 如果指定了分支名称，使用指定的分支名称，否则使用tag名称
        if not branch:
            branch = tag

        # 获取tag对象及对应的commit_id
        tag_obj = self._project.tags.get(tag)
        commit_id = tag_obj.commit["id"]
        try:
            self._project.branches.create({"branch": branch, "ref": commit_id})
            return True
        except Exception as e:
            log.error(e)
            return False

    def create_merge_request(
        self,
        source_branch: str,
        target_branch: str,
        title: str = None,
        description: str = None,
        create_if_none_commit: bool = False,
        remove_source_branch: bool = False,
    ) -> str:
        """
        创建merge request
        Args:
            source_branch: 源分支
            target_branch: 目标分支
            title: 标题
            description: 描述
            create_if_none_commit: 如果没有commit，是否create
            remove_source_branch: 是否删除源分支
        Returns:
            str: merge request的web_url
        """
        if source_branch == target_branch:
            raise PyframeException(f"source_branch {source_branch} can't be equal to target_branch {target_branch}")
        branches = self.get_all_branches()
        if source_branch not in branches:
            raise PyframeException(f"source_branch {source_branch} doesn't exist")
        if target_branch not in branches:
            raise PyframeException(f"target_branch {target_branch} doesn't exist")

        # 检查是否有新的commit
        diffs = self._project.repository_compare(target_branch, source_branch)
        log.debug(diffs)
        if len(diffs["commits"]) == 0:
            log.warn(f"{source_branch} to {target_branch} has no new commit, ignore")
            if not create_if_none_commit:
                return ""
        # 检查是否有文件变更
        if len(diffs["diffs"]) == 0:
            log.warn(f"{source_branch} to {target_branch} has no new diff, ignore")
            if not create_if_none_commit:
                return ""
        # else:
        #     log.debug(diffs)

        # 检查是否已经存在merge request
        opened_mrs = self._project.mergerequests.list(state="opened", source_branch=source_branch, target_branch=target_branch)
        if len(opened_mrs) != 0:
            log.warn(f"merge request from {source_branch} to {target_branch} already exists, ignore")
            return ""

        title = f"merge {source_branch} to {target_branch}" if title is None else title
        mr = self._project.mergerequests.create(
            {
                "source_branch": source_branch,
                "target_branch": target_branch,
                "title": title,
                "description": description,
                "remove_source_branch": remove_source_branch,
            }
        )
        log.info(f"merge {source_branch} to {target_branch}")
        if not create_if_none_commit:
            if mr.diff_refs["head_sha"] == mr.diff_refs["start_sha"]:
                log.info(f"{source_branch} has no new commit")
                mr.state_event = "close"
                mr.save()
            if mr.changes_count is None or mr.changes_count == "0" or mr.changes_count == 0:
                log.warn(f"{source_branch} has no new commit")
                mr.state_event = "close"
                mr.save()
            return ""
        log.info(f"create merge requests {source_branch} to {target_branch}, web_url: {mr.web_url}")
        return mr.web_url

    def merge(
        self,
        source_branch: str,
        target_branch: str,
        max_tries: int = 3,
        title: str = None,
        description: str = None,
        merge_when_pipeline_succeeds: bool = True,
    ):
        """
        合并分支，从source_branch到target_branch
        Args:
            source_branch: 源分支
            target_branch: 目标分支
            max_tries: 最大尝试次数
            title: 标题
            description: 描述
            merge_when_pipeline_succeeds: 是否在pipeline成功后合并
        """
        gitlab_branches = self.get_all_branches()
        merge_branches = [source_branch, target_branch]
        for branch in merge_branches:
            if branch not in gitlab_branches:
                log.error(f"{self._project_name} doesn't have branch {branch}")
                raise PyframeException(f"merge失败, {self._project_name}没有该分支: {branch}")

        mrs = self._project.mergerequests.list(state="opened", source_branch=source_branch, target_branch=target_branch)
        mr = None
        for m in mrs:
            if m.source_branch == source_branch and m.target_branch == target_branch:
                mr = m
                log.info(f"find merge requests, web_url: {mr}")
                break
        if not mr:
            if title is None:
                title = f"merge {source_branch} to {target_branch}"
            if description is None:
                description = "pyframe流水线自动merge"
            mr = self._project.mergerequests.create(
                {
                    "source_branch": source_branch,
                    "target_branch": target_branch,
                    "title": title,
                    "description": description,
                }
            )
        log.info(f"create merge requests, web_url: {mr}")

        log.info(f"merge {source_branch} to {target_branch}")
        sleep_second_unit = 3
        count = 0
        while True:
            sleep_seconds = sleep_second_unit
            count += 1
            if count > max_tries + 1:
                mr.state_event = "close"
                mr.save()
                log.error(f"retry times exceed {max_tries}")
                return mr, False

            if count > 1:
                sleep_seconds = count * sleep_second_unit
                log.info(f"sleep {sleep_seconds}s")
                time.sleep(sleep_seconds)

            try:
                # 判断是否有新commit
                if mr.diff_refs["head_sha"] == mr.diff_refs["start_sha"]:
                    log.info(f"{source_branch} has no new commit！")
                    mr.state_event = "close"
                    mr.save()
                    break
                elif mr.changes_count is None or mr.changes_count == "0" or mr.changes_count == 0:
                    log.warn(f"{source_branch} has no new commit")
                    mr.state_event = "close"
                    mr.save()
                    break
                else:
                    log.info(f"sleep {sleep_seconds}s")
                    time.sleep(sleep_seconds)
                    log.info(f"merge requests: {mr}")
                    # 判断是否冲突
                    if mr.has_conflicts:
                        log.error("merge has conflicts")
                        raise PyframeException(f"merge {source_branch} to {target_branch}, 有冲突")
                    mr.merge(merge_when_pipeline_succeeds=merge_when_pipeline_succeeds)
                    log.info(
                        f"merge success, iid: {mr.iid}, source_branch: {mr.source_branch}, target_branch: {mr.target_branch}, web_url: {mr.web_url}"
                    )
                    log.info(mr)
                    break
            except gitlab.exceptions.GitlabCreateError as e:
                log.warn(e)
                log.info(f"{source_branch} to {target_branch} merge request already exists ")
            except Exception as e:
                log.warn(e)
        return mr, True

    def get_tag_list(self) -> list:
        """
        从项目上获取tag列表
        """
        ret = []
        tags = self._project.tags.list()
        for tag in tags:
            ret.append(tag.name)
        return ret

    def get_repository_tree(self, path: str = "str", ref: str = "", recursive=False, **kwargs) -> list:
        """

        Args:
            path: 父目录
            ref: 分支或者commit信息
            recursive: 是否递归
            **kwargs: 其他参数

        Returns: 返回目录列表
        """
        return self._project.repository_tree(path=path, ref=ref, recursive=recursive, **kwargs)

    def get_committer_by_commit_id(self, branch: str, commit_id: str):
        for page in range(1, 1000):  # 最多1000页，可能有问题
            # commits = self.__project.commits.list(per_page=100, page=page, ref_name=branch)
            commits = self._get_format_time_commits(per_page=100, page=page, ref_name=branch)
            if len(commits) == 0:
                log.info(f"page {page} is empty")
                break
            for commit in commits:
                if commit.id == commit_id or commit.short_id == commit_id:
                    return commit.committer_name, commit.author_email
        raise PyframeException(f"{self._project_name}仓库不存在commit：{commit_id}")

    def get_commit_by_commit_id(self, branch: str, commit_id: str):
        for page in range(1, 1000):  # 最多1000页，可能有问题
            commits = self._get_format_time_commits(per_page=100, page=page, ref_name=branch)
            if len(commits) == 0:
                log.info(f"page {page} is empty")
                break
            for commit in commits:
                if commit.id == commit_id or commit.short_id == commit_id:
                    return commit
        raise PyframeException(f"{self._project_name}仓库不存在commit：{commit_id}")

    def get_pipeline_id_by_commit_id(self, branch: str, commit_id: str):
        for page in range(1, 1000):  # 最多1000页，可能有问题
            pipelines = self._project.pipelines.list(per_page=100, page=page, ref=branch)
            if len(pipelines) == 0:
                log.info(f"page {page} is empty")
                break
            for pipeline in pipelines:
                if pipeline.sha == commit_id or pipeline.sha[0:8] == commit_id:
                    return pipeline.id
        raise PyframeException(f"{self._project_name}仓库不存在commit_after：{commit_id}对应的pipeline id")

    def get_pipeline_by_commit_id(self, commit_id: str):
        """
        根据commit_id获取最新的pipeline
        Args:
            commit_id: 必须完整, 例如: 5dc92fd8fec8dd061dfd7128f27f1a44ba5dea2a
        Returns: 返回pipeline, 当pipeline不存在时, 返回None
        """
        pipelines = self._project.pipelines.list(sha=commit_id)
        if pipelines:
            return pipelines[0]

    def create_branch(self, source_branch: str, target_branch: str) -> str:
        """
        根据source_branch创建git分支, 分支存在则不创建, 返回值为"", 空字符串
        Args:
            source_branch: 创建分支的所使用的源分支
            target_branch: 创建的分支名称
        Returns: 创建的分支名称
        """
        all_branches = self.get_all_branches()
        if source_branch not in all_branches:
            raise PyframeException(f"源分支{source_branch}不存在")

        if target_branch in all_branches:
            raise PyframeException(f"分支{target_branch}已经存在")

        try:
            branch_object = self._project.branches.create({"branch": target_branch, "ref": source_branch})
        except Exception as e:
            raise PyframeException(f"项目{self._project_name}创建分支{target_branch}失败, {e.__str__()}")

        log.info(f"从{source_branch}创建分支{target_branch}成功")
        return branch_object.name

    def auto_create_merge_request(
        self,
        stable_branch: str = "master",
        test_branch: str = "test",
        exclude_branches: list = [],
        include_branches: list = [],
    ):
        """
        自动创建merge request。分支模型：stable_branch稳定分支，test_branch测试分支，其余为开发分支
        Args:
            stable_branch: 稳定分支
            test_branch: 测试分支
        """
        if include_branches:
            branches = include_branches
        else:
            branches = self.get_all_branches()

        if exclude_branches:
            branches = list(set(branches) - set(exclude_branches))

        # 开发分支到test
        for branch in branches:
            if branch in [stable_branch, test_branch]:
                continue
            self.create_merge_request(source_branch=branch, target_branch=test_branch)

        # test分支到stable
        self.create_merge_request(
            source_branch=test_branch,
            target_branch=stable_branch,
            create_if_none_commit=True,
        )

    def auto_merge(
        self,
        stable_branch: str = "master",
        exclude_branches: list = [],
        include_branches: list = [],
    ) -> tuple:
        """
        自动合并分支。从stable_branch自动合并到其他分支
        Args:
            stable_branch: 稳定分支
            exclude_branches: 排除的分支
            include_branches: 包含的分支
        Returns:
            success_branches: 成功的分支
            failure_branches: 失败的分支
        """
        if include_branches:
            branches = include_branches
        else:
            branches = self.get_all_branches()

        if exclude_branches:
            branches = list(set(branches) - set(exclude_branches))

        success_branches = []
        failure_branches = []
        log.info(f"branches: {branches}")
        # 稳定分支到其他分支
        for branch in branches:
            if branch in [stable_branch]:
                continue
            _, status = self.merge(source_branch=stable_branch, target_branch=branch)
            if status:
                success_branches.append(branch)
            else:
                failure_branches.append(branch)
        return success_branches, failure_branches

    def is_branch_exist(self, branch_name: str) -> bool:
        """
        判断分支是否存在
        Args:
            branch_name: 分支名
        Returns:
            bool: 存在返回True，否则返回False
        """
        try:
            self._project.branches.get(branch_name)
            return True
        except Exception as e:
            log.error(f"分支{branch_name}不存在, {e.__str__()}")
            return False

    def delete_branch(self, branch_name: str) -> bool:
        """
        删除分支
        Args:
            branch_name:
        Returns:
            bool: 删除成功返回True，否则返回False
        """
        try:
            log.info(f"delete branch: {branch_name}")
            branch = self._project.branches.get(branch_name)
            branch.delete()
        except Exception as e:
            log.error(f"删除分支{branch_name}失败, {e.__str__()}")
            raise PyframeException(f"删除分支{branch_name}失败, {e.__str__()}")
