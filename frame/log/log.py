# coding=utf-8
import logging
import logging.handlers
import os


class Log(logging.Logger):
    def __init__(self, file: str = None):
        # super().__init__("")
        if file is None:
            jenkins_name = os.getenv("BUILD_TAG")
            if jenkins_name is not None:
                file = jenkins_name
            else:
                bk_ci_pipeline_name = os.getenv("BK_CI_PIPELINE_NAME")
                bk_ci_build_num = os.getenv("BK_CI_BUILD_NUM")
                if bk_ci_build_num is not None and bk_ci_pipeline_name is not None:
                    invalid_ch_list = [":", "#", "$", ".", "COM", "<", ">", "\\", "*", "?", '"']
                    for c in invalid_ch_list:
                        bk_ci_pipeline_name = bk_ci_pipeline_name.replace(c, "")
                    file = "{}-{}".format(bk_ci_pipeline_name, bk_ci_build_num)
                else:
                    file = "pipeline"
        self.file = file
        self.name = "pipeline"
        self.level = 20
        self.format = "%(asctime)s[%(levelname)s]%(filename)s:%(lineno)d %(funcName)s() %(message)s"
        super().__init__(self.name)
        self.setLevel(self.level)
        fmt = logging.Formatter(self.format)
        if file:
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            log_path = os.path.join(project_root, "logs/" + file + ".log")
            if not os.path.exists(os.path.join(project_root, "logs/")):
                os.makedirs(os.path.join(project_root, "logs/"))
            file_handler = logging.handlers.TimedRotatingFileHandler(log_path, when="midnight", interval=1, backupCount=7, encoding="utf-8")
            file_handler.suffix = "%Y_%m_%d.log"
            file_handler.setLevel(self.level)
            file_handler.setFormatter(fmt)
            self.addHandler(file_handler)
        # 设置StreamHandler,输出日志到控制台
        stream_handler = logging.StreamHandler()
        stream_handler.setLevel(self.level)
        stream_handler.setFormatter(fmt)
        self.addHandler(stream_handler)


log = Log()
