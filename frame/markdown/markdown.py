# coding=utf-8
from frame.markdown.table.table import Table


class Markdown:
    def __init__(self):
        self.markdown = ""

    def __str__(self):
        return self.markdown

    # 添加标题
    def add_title(self, title: str, level: int = 1):
        self.markdown += f"{'#'*level} {title}"
        self.markdown += "\n\n"

    # 添加内容
    def add_content(self, content: str):
        self.markdown += content
        self.markdown += "\n"

    # 添加表格
    def add_table(self, table: Table):
        self.markdown += str(table)
        self.markdown += "\n"

    # 添加图片
    def add_image(self, image: str, title: str):
        self.markdown += f"![{title}]({image})"
        self.markdown += "\n"

    def write_to_file(self, path: str):
        with open(path, "w", encoding="utf-8") as f:
            f.write(self.markdown)
