# coding = utf-8
import os
import time

import requests

from frame.common.common import common
from frame.exception.exception import PyframeException
from frame.log.log import log
from frame.path_mgr.path_mgr import path_mgr


class Nexus:
    def __init__(self, username: str, password: str):
        self._username = username
        self._password = password
        self._repository = None
        self._directory = None
        self._filename = None
        self._relative_path = None

    def upload(self, src: str, dst: str, timeout: int = 300):
        """
        上传文件到nexus制品库

        参考：
            https://www.cnblogs.com/zhuosanxun/p/15100588.html
            https://segmentfault.com/q/1010000005897469
        Args:

            src: 源文件的路径
            dst: nexus的目标链接
            timeout: 上传超时时间，默认300s，5min
        """
        self.__split_url(url=dst)
        start_time = time.time()
        src = os.path.abspath(src)
        log.info(f"src: {src}")
        log.info(f"dst: {dst}")

        if not os.path.exists(src):
            log.error(f"{src} does not exist")
            raise PyframeException(f"上传nexus失败, {src}不存在")

        if not os.path.isfile(src):
            log.error(f"{src} is not a file")
            raise PyframeException(f"上传nexus失败, {src}不是文件")

        if ":" in src[2:] or "：" in src[2:]:
            log.error(f"{src} is not a local path")
            raise PyframeException(f"上传nexus失败, {src}包含冒号")

        params = {
            "repository": self._repository,
        }

        files = {
            "raw.directory": (None, self._directory),
            "raw.asset1": (open(src, "rb")),
            "raw.asset1.filename": (None, self._filename),
        }
        try:
            response = requests.post(
                url="http://nexus.h3d.com.cn/service/rest/v1/components",
                params=params,
                files=files,
                auth=(self._username, self._password),
            )
        except Exception as e:
            raise PyframeException(f"请求制品库异常: {e}")

        if not str(response.status_code).startswith("20"):
            log.error(f"response.status_code: {response.status_code}")
            log.error(f"response.text: {response.text}")
            if response.status_code == 500:
                raise PyframeException(f"上传nexus失败, http返回码: {response.status_code}, 请联系IT检查制品库是否还有剩余空间")
            raise PyframeException(f"上传nexus失败, http返回码: {response.status_code}")

        local_md5 = common.compute_md5(src)
        while True:
            if time.time() - start_time > timeout:
                raise PyframeException(f"上传nexus失败, {common.format_duration(timeout)}超时")
            remote_md5 = self.__get_remote_md5(url=dst)
            if remote_md5 is not None:
                break

        if remote_md5 != local_md5:
            raise PyframeException("上传nexus失败, md5不匹配")
        log.info(f"upload success, cost time {common.format_duration(time.time() - start_time)}")

    def download(self, src: str, dst: str):
        """
        从nexus制品库下载制品

        Args:
            src: nexus的目标链接
            dst: 本地的文件名
        """
        log.info(f"src: {src}")
        log.info(f"dst: {dst}")
        start_time = time.time()
        dst = os.path.abspath(dst)
        dir_path = os.path.dirname(dst)
        path_mgr.mkdir(path=dir_path)
        response = requests.get(url=src, auth=(self._username, self._password), stream=True)
        if response.status_code != 200:
            log.error(f"response.status_code={response.status_code}")
            if response.status_code == 404:
                raise PyframeException(f"从nexus下载失败, http返回码: {response.status_code}, 制品库上不存在{src}")
            else:
                raise PyframeException(f"从nexus下载失败, http返回码: {response.status_code}")

        with open(dst, "wb") as f:
            for chunk in response.iter_content(chunk_size=10240):  # 每次加载10240字节
                if chunk:
                    f.write(chunk)

        if not os.path.exists(dst):
            log.error(f"{dst} does not exist")
            raise PyframeException(f"从nexus下载失败, 本地不存在{dst}")

        remote_md5 = self.__get_remote_md5(url=src)
        local_md5 = common.compute_md5(dst)
        if remote_md5 != local_md5:
            log.error("md5 check failed")
            raise PyframeException(f"从nexus下载失败, md5不匹配")
        else:
            log.info("md5 check passed")
        log.info(f"download success, cost time {common.format_duration(time.time() - start_time)}")

    def __split_url(self, url: str):
        """
        从nexus下载链接中解析关键参数
        Args:
            url: nexus链接，如http://nexus.h3d.com.cn/__repository/dev-productivity/python/3.6.8/python-3.6.8.tgz
        """
        temp = url.replace("http://nexus.h3d.com.cn/repository/", "")
        self._repository = temp.split("/")[0]
        self._relative_path = url.replace("http://nexus.h3d.com.cn/repository/{}".format(self._repository), "")
        self._directory = "/".join(self._relative_path.split("/")[0:-1]).lstrip("/")
        self._filename = url.split("/")[-1]

    def __get_remote_md5(self, url: str) -> str or None:
        self.__split_url(url=url)
        md5_url = "http://nexus.h3d.com.cn/service/rest/v1/search?repository={}&name={}".format(self._repository, self._relative_path.lstrip("/"))
        j = requests.get(url=md5_url, auth=(self._username, self._password)).json()
        remote_md5 = None
        try:
            remote_md5 = j.get("items")[0].get("assets")[0].get("checksum").get("md5")
        except Exception as e:
            if "list index out of range" not in str(e):
                log.error(f"unknown error: {e}")
            pass
        return remote_md5

    def __search(self, url: str) -> dict:
        search_result = {}
        try:
            search_result = requests.get(url=url, auth=(self._username, self._password)).json()
        except Exception as e:
            log.error(e)
        return search_result

    def search(self, name: str = "", repository: str = None, group: str = None, all_groups: bool = False) -> list:
        """
        搜索制品是否存在
        Args:
            name: 可选参数，根据此参数进行搜索，支持通配符
            repository: 可选参数，支持通配符
            group: 可选参数，支持通配符
            all_groups: 可选参数，是否获取所有数据

        Returns:

        """
        search_url = f"http://nexus.h3d.com.cn/service/rest/v1/search?name={name}"

        if repository:
            search_url = f"{search_url}&repository={repository}"
        if group:
            search_url = f"{search_url}&group={group}"
        log.info(f"search_url: {search_url}")
        items = []
        ret = self.__search(search_url)
        temp_items = ret.get("items")
        items.extend(temp_items)
        continue_token = ret.get("continuationToken")
        while continue_token:
            new_url = f"{search_url}&continuationToken={continue_token}"
            # print(new_url)
            ret = self.__search(new_url)
            temp_items = ret.get("items")
            items.extend(temp_items)
            continue_token = ret.get("continuationToken")
        return items

    @staticmethod
    def browse_repository(
        repository_name: str = None,
        node: str = None,
        action: str = "coreui_Browse",
        method: str = "read",
        c_type: str = "rpc",
        tid: int = 1,
    ) -> dict:
        """
        浏览制品库，获取制品库中的目录结构或者制品列表
        Args:
            repository_name:
            node:
            action:
            method:
            c_type:
            tid:

        Returns:

        """
        url = "http://nexus.h3d.com.cn/service/extdirect"
        data = {
            "action": action,
            "method": method,
            "data": [{"repositoryName": repository_name, "node": node}],
            "type": c_type,
            "tid": tid,
        }
        log.info(f"url: {url} data: {data}")
        try:
            ret = requests.post(url=url, json=data).json()
        except Exception as e:
            log.error(e)
            ret = {}
        log.info(f"ret: {ret}")
        return ret
