import os
import re

from P4 import P4, P4Exception

from frame.p4.path import compare_path


def check_connection(func):
    def wrapper(*args, **kwargs):
        self = args[0]
        self.connect()
        return func(*args, **kwargs)

    return wrapper


class Perforce(object):
    """P4  operation wrapper"""

    # The mode of add p4 mapping
    #                       w: overwrite
    #                       a: append write
    # DEFAULT_VIEW_MODE:    w: overwrite
    DEFAULT_VIEW_MODE = OVERWRITE_VIEW = "w"
    APPEND_VIEW = "a"
    VIEW_MODE = [OVERWRITE_VIEW, APPEND_VIEW]

    # Default version of sync
    DEFAULT_VERSION = "head"

    OPTIONS_LIST = ["allwrite", "clobber", "compress", "modtime", "rmdir"]
    P4_OPTIONS = "noallwrite noclobber nocompress unlocked nomodtime normdir"

    def __init__(self, logger=None, multiple=False, exception_level=1, **kwargs):
        """
        :param: multiple: invoke this prior to connecting if you need to use multiple P4 connections in parallel in a multi-threaded Python application.
        :param: port: p4 server, example p4.com:2002
        :param: user: user name
        :param: password: user password
        :param: charset: p4 character set, must be one of:
                none, auto, utf8, utf8-bom, iso8859-1, shiftjis, eucjp, iso8859-15,
                iso8859-5, macosroman, winansi, koi8-r, cp949, cp1251,
                utf16, utf16-nobom, utf16le, utf16le-bom, utf16be,
                utf16be-bom, utf32, utf32-nobom, utf32le, utf32le-bom, utf32be,
                cp936, cp950, cp850, cp858, cp1253, iso8859-7, utf32be-bom,
                cp852, cp1250, iso8859-2
        ...
        """
        self.client_view = []
        self.client = None
        self.password = kwargs.get("password")
        self.user = kwargs.get("user")

        self.p4 = P4(**kwargs)
        if multiple:
            self.p4.disable_tmp_cleanup()

        self.connect()
        if logger:
            self.p4.logger = logger

        self.p4.exception_level = exception_level

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()

    @check_connection
    def login(self, password=None):
        if password:
            self.password = password

        self.p4.run_login(password=self.password)

    def create_workspace(self, client, view=None, options=None, root=os.getcwd(), stream=None):
        """
        :param client: p4 workspace name
        :param view: p4 view
        :param options: p4 options
        :param root: root path, if p4_root is NONE, set default os.getcwd()
        :param stream: p4 stream
        """
        self.check_workspace(client, root)
        self.modify_client(client, view, root, options, stream)

    def set_workspace(self, client, view=None, options=None, root=os.getcwd(), stream=None):
        self.modify_client(client, view, root, options, stream)

    def check_workspace(self, workspace, root):
        """
        1. check the client name and client root path
        2. delete current client and create a new client if root path has changed
        """
        clients = self.get_clients_name()
        for client in clients:
            client_name = client.get("client")
            if workspace == client_name:
                owner = client.get("Owner")
                if owner != self.user:
                    raise P4Exception(f"client name `{client_name}` already exists")

                current_root = client.get("Root")
                if not compare_path(current_root, root):
                    self.delete_workspace(workspace)
                    break

    @staticmethod
    def completion_view(client, view_list):
        """
        convert to complete mapping
        :return: the fully p4 view list
        """
        p4_view_list = []
        for x in view_list:
            if not x.startswith("//"):
                raise P4Exception(f"wrong format for p4_view")

            view_split_list_len = len(re.sub(re.compile(r"\s+", re.S), " ", x).split(" "))
            if view_split_list_len == 1:
                p4_view_list.append(f"{x} //{client}/{x[2:]}")
            elif view_split_list_len == 2:
                p4_view_list.append(x)
            else:
                raise P4Exception(f"invalid length of p4 view")

        return p4_view_list

    @check_connection
    def modify_client(self, client, view_list=None, root=None, options=None, stream=None, mode=DEFAULT_VIEW_MODE):
        """
        :param client:
        :param view_list:
        :param root:
        :param stream:
        :param options:
        :param mode: the mode of add p4 mapping
        """
        options = options or []
        if not isinstance(options, list):
            raise P4Exception(f"parameter: `p4_options`, except `list`, got {type(options)}")

        for p4_option in options:
            if p4_option not in self.OPTIONS_LIST:
                raise P4Exception(f"invalid option for p4_options: `{p4_option}`")

            self.P4_OPTIONS = re.sub(re.compile(rf"(un|no){p4_option}", re.S), p4_option, self.P4_OPTIONS)

        if mode not in self.VIEW_MODE:
            raise P4Exception(f"invalid mode of add p4 mapping: `{mode}`")

        p4_client = self.p4.fetch_client(client)
        if root:
            p4_client["Root"] = root

        if stream:
            p4_client["Stream"] = stream

        p4_client["Options"] = self.P4_OPTIONS

        if view_list:
            if mode == self.OVERWRITE_VIEW:
                p4_client["View"] = self.completion_view(client, view_list)

            elif mode == self.APPEND_VIEW:
                for add_view in view_list:
                    current_view_list = p4_client.get("View") or []
                    if add_view not in current_view_list:
                        p4_client["View"] = current_view_list + self.completion_view(client, view_list)

        self.p4.save_client(p4_client)
        self.switch_client(client)

    def switch_client(self, client):
        p4_client = self.p4.fetch_client(client)
        self.p4.client = client
        self.client = client
        self.client_view = p4_client["View"]

    def add_view(self, client, view_list):
        self.modify_client(client, view_list, mode=self.APPEND_VIEW)

    @check_connection
    def get_clients_name(self):
        return self.p4.run_clients()

    @check_connection
    def update(self, file, version=None, force_sync=False):
        """
        update the specified version data from p4 server
        :param file: file to update
        :param version: specified version, default is latest(#head)
        :param force_sync:
        """
        if not version:
            version = self.DEFAULT_VERSION

        if version:
            version = version.lower()

        v = "#" + version
        if version != self.DEFAULT_VERSION:
            v = "@" + version

        file = f"{file}{v}"
        if not force_sync:
            self.p4.run_sync(file)
        else:
            self.p4.run_sync("-f", file)

    def update_all(self, version=None, force_sync=False, ergodic=False):
        if ergodic:
            for x in self.client_view:
                self.update(x.split(" ")[0], version, force_sync)

            return

        depots = set()
        for x in self.client_view:
            res = re.search(re.compile(r"^//[0-9a-zA-Z_\-]+/", re.S), x)
            if not res:
                # 跳过所有非法或者注释掉的映射
                # raise P4Exception(f"invalid view {x}")
                continue

            depots.add(res.group().strip())

        for x in depots:
            self.update("{}...".format(x), version, force_sync)

    @check_connection
    def submit(self, msg):
        return self.p4.run_submit("-d", msg)

    @check_connection
    def add(self, file: str):
        self.p4.run_add(file)

    @check_connection
    def edit(self, file: str):
        self.p4.run_edit(file)

    @check_connection
    def reconcile(self, params=None, file=None):
        if not file:
            file = f"//{self.client}/..."

        params = params if params is not None else []
        params.append(file)

        return self.p4.run_reconcile(*params)

    @check_connection
    def delete_workspace(self, client_name=None):
        """delete workspace"""
        self.p4.run("client", "-d", client_name or self.client)

    @check_connection
    def get_changes(self, params=None):
        args = ["changes"]
        if params:
            args += params

        change = self.p4.run(*args)
        return change

    def get_latest_change(self, file=None, local=False):
        params = ["-m1"]
        if file:
            if local:
                file += "#have"
            params.append(file)

        change = self.get_changes(params)
        return change

    def connect(self):
        if not self.p4.connected():
            self.p4.connect()

    def disconnect(self):
        """disconnect from p4 server if is connected"""
        if self.p4.connected():
            self.p4.disconnect()

    def get_checkout_files(self, remote_path: str) -> list:
        """
        获取路径下被 checkout 的文件，不包含当前 client checkout 的文件

        判断依据：
            他人迁出：otherOpen: ["zhangmiao@zhangmiao3295_0821"]
            被自己其他client迁出: otherOpen: ["sujiajun@cn_path_test"] && otherAction: ["edit"]
            自己迁出：action: "edit" && actionOwner: "sujiajun"
            无人迁出：没有 otherOpen、action、otherAction

        params:
            remote_path: p4 远端目录 如 //H3D_X51_res/X51_SourceBase/Test/中文路径测试/...

        """
        resp = self.p4.run("fstat", "-Olhp", remote_path)
        checkout_files = []

        file_count = 0
        for file in resp:
            if "dir" in file:
                continue
            file_count += 1
            # 先判断是否有他人迁出
            if "otherOpen" in file:
                other_open = file["otherOpen"]
                # 打开人数超过 1 人 -> 他人迁出
                if len(other_open) > 1:
                    checkout_files.append(file)
                    continue
                other_open = other_open[0]
                # 打开 username & client 不匹配 -> 他人迁出
                if other_open != f"{self.p4.user}@{self.client}":
                    checkout_files.append(file)
                    continue
        return checkout_files
