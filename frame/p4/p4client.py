# coding=utf-8
import os
import platform
from pathlib import Path
from typing import List, Optional

from P4 import P4, P4Exception
from tenacity import retry, stop_after_attempt, wait_fixed
from enum import Enum

from frame.advance.advance import advance
from frame.cmd.cmd import cmd
from frame.exception.exception import PyframeException
from frame.log.log import log
from frame.path_mgr.path_mgr import path_mgr


class Options:
    """
    p4 client中的Options
    """

    def __init__(self):
        self.allwrite = False
        self.clobber = True
        self.compress = False
        self.locked = False
        self.modtime = False
        self.rmdir = False

    def set_options(
        self,
        allwrite: bool = False,
        clobber: bool = True,
        compress: bool = False,
        locked: bool = False,
        modtime: bool = False,
        rmdir: bool = False,
    ):
        """
        参数释义同P4Mgr.set_options
        """
        self.allwrite = allwrite
        self.clobber = clobber
        self.compress = compress
        self.locked = locked
        self.modtime = modtime
        self.rmdir = rmdir

    def get_options(self) -> str:
        """
        获取options字符串，供p4使用

        Returns:
            options拼接字符串
        """
        opts = [self.allwrite, self.clobber, self.compress, self.locked, self.modtime, self.rmdir]
        opt_tpl = ["allwrite", "clobber", "compress", "locked", "modtime", "rmdir"]
        for i in range(len(opts)):
            if opt_tpl[i] == "locked":
                opt_tpl[i] = opt_tpl[i] if opts[i] else "unlocked"
            else:
                opt_tpl[i] = opt_tpl[i] if opts[i] else "no" + opt_tpl[i]
        return " ".join(opt_tpl)


class P4Client:
    P4USER = "pyframe"

    class LineEnd:
        LOCAL = "local"
        UNIX = "unix"
        MAC = "mac"
        WIN = "win"

    class Charset(Enum):
        """
        p4 help charset
        charset - set the character set for file names and file contents
        auto            (Guess a P4CHARSET based on client OS params
        none            (same as unsetting P4CHARSET)
        eucjp
        iso8859-1
        iso8859-2
        iso8859-5
        iso8859-7
        iso8859-15
        macosroman
        shiftjis
        koi8-r
        utf8            (UTF-8)
        utf8-bom        (UTF-8 with Byte-Order-Mark)
        utf16           (UTF-16 with client's byte ordering and Byte-OrderMark)
        utf16-nobom     (UTF-16 client's byte ordering without Byte-Order-Mark)
        utf16le         (UTF-16 with little endian byte ordering)
        utf16le-bom     (UTF-16 with little endian Byte-Order-Mark)
        utf16be         (UTF-16 with big endian byte ordering)
        utf16be-bom     (UTF-16 with big endian Byte-Order-Mark)
        utf32           (UTF-32 with client's byte ordering and Byte-OrderMark)
        utf32-nobom     (UTF-32 client's byte ordering without Byte-Order-Mark)
        utf32le         (UTF-32 with little endian byte ordering)
        utf32le-bom     (UTF-32 with little endian Byte-Order-Mark)
        utf32be         (UTF-32 with big endian byte ordering)
        utf32be-bom     (UTF-32 with big endian Byte-Order-Mark)
        cp850           (Windows code page 850)
        cp852           (Windows code page 852)
        cp858           (Windows code page 858)
        cp936           (Windows code page 936 - Simplified Chinese)
        cp949           (Windows code page 949 - Korean)
        cp950           (Windows code page 950 - Traditional Chinese)
        cp1250          (Windows code page 1250 - Central European)
        cp1251          (Windows code page 1251 - Cyrillic)
        winansi         (Windows code page 1252)
        cp1253          (Windows code page 1253 - Greek)
        """

        AUTO = "auto"
        NONE = "none"
        EUCJP = "eucjp"
        ISO8859_1 = "iso8859-1"
        ISO8859_2 = "iso8859-2"
        ISO8859_5 = "iso8859-5"
        ISO8859_7 = "iso8859-7"
        ISO8859_15 = "iso8859-15"
        MACOSROMAN = "macosroman"
        SHIFTJIS = "shiftjis"
        KOI8_R = "koi8-r"
        UTF8 = "utf8"
        UTF8_BOM = "utf8-bom"
        UTF16 = "utf16"
        UTF16_NOBOM = "utf16-nobom"
        UTF16LE = "utf16le"
        UTF16LE_BOM = "utf16le-bom"
        UTF16BE = "utf16be"
        UTF16BE_BOM = "utf16be-bom"
        UTF32 = "utf32"
        UTF32_NOBOM = "utf32-nobom"
        UTF32LE = "utf32le"
        UTF32LE_BOM = "utf32le-bom"
        UTF32BE = "utf32be"
        UTF32BE_BOM = "utf32be-bom"
        CP850 = "cp850"
        CP852 = "cp852"
        CP858 = "cp858"
        CP936 = "cp936"
        CP949 = "cp949"
        CP950 = "cp950"
        CP1250 = "cp1250"
        CP1251 = "cp1251"
        WINANSI = "winansi"
        CP1253 = "cp1253"

    def __init__(
        self,
        host: str,
        username: str,
        password: str,
        client: str,
        stream: str = None,
        encoding: str = "utf-8",
        charset: Charset = Charset.AUTO,
        logger=log,
    ):
        self.__host = host
        self.__username = username
        self.__password = password
        self.client = client
        self.__p4 = P4()
        self.__p4.port = host
        self.__p4.user = username
        self.__p4.password = password
        self.__p4.client = client
        # 设置编码
        if charset == P4Client.Charset.AUTO:
            log.debug("charset is auto")
            pass
        else:
            self.__p4.charset = charset.value
            log.debug(f"charset is {charset}")
        # self.__p4.stream = stream
        # self.__p4.encoding = encoding
        self.__options = Options()
        self.connect()
        self.login(password=password)
        self.config = self.fetch_client(client=client)
        if stream:
            self.config["Stream"] = stream

        self.save_client(config=self.config)
        if not self.is_exist_client(client):
            log.info(f"client {client} doesn't exist, auto create")
            log.debug(self)

        P4Client.P4USER = username
        self.__p4.exception_level = 1

    def __enter__(self):
        self.connect()
        return self

    def __del__(self):
        self.disconnect()
        pass

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()

    def __str__(self):
        config = self.__p4.format_spec("client", self.config)
        # log.info(client_spec)
        return config

    def connect(self):
        """
        连接p4
        """
        if self.__p4.connected():
            return
        try:
            self.__p4.connect()
        except P4Exception as e:
            log.warning(e)
            raise PyframeException(f"p4 connect失败, 请检查{self.__host}是否异常")

    def disconnect(self):
        """
        断开p4连接
        """
        if not self.__p4.connected():
            return
        try:
            print("p4 disconnect")
            self.__p4.disconnect()
        except P4Exception as e:
            print(e)
            raise PyframeException("p4 disconnect失败")

    def login(self, password: str):
        try:
            # log.info(f"p4 login ")
            self.__p4.run_login(password=password)
        except P4Exception as e:
            log.warning(e)
            raise PyframeException(f"p4 login失败, 请检查{self.__username}的账号密码是否正确, 异常: {e}")

    def clients(self) -> list:
        """
        获取所有的client

        Returns:
            client列表
        """
        try:
            ret = self.__p4.run("clients")
            return ret
        except P4Exception as e:
            log.warning(e)
            raise PyframeException("获取client失败")

    def is_contain_chinese(self):
        for line in self.config["View"]:
            for ch in line:
                if "\u4e00" <= ch <= "\u9fff":
                    return True
        return False

    def is_exist_client(self, client: str) -> bool:
        """
        判断client是否存在

        Args:
            client: client名称
        Returns:
            True: 存在，False: 不存在
        """
        ret = self.__p4.run("clients", "-e", client)
        return False if len(ret) == 0 else True

    def set_encoding(self, encoding: str = "utf-8"):
        """
        设置编码

        Args:
            encoding: p4编码，默认是utf-8
        """
        log.info("change p4 encoding from {} to {}".format(self.__p4.encoding, encoding))
        self.__p4.encoding = encoding

    def get_encoding(self) -> str:
        """
        获取编码
        Returns:
            编码
        """
        return self.__p4.encoding

    def set_options(
        self,
        allwrite: bool = False,
        clobber: bool = True,
        compress: bool = False,
        locked: bool = False,
        modtime: bool = False,
        rmdir: bool = False,
    ):
        """
        设置p4的options

        Args:
            allwrite: False拉取下来的文件只读，True拉取下来的文件可写。默认False
            clobber: False不要覆盖已经修改的文件，True覆盖已经修改的文件。默认True
            compress: False传输时不压缩，True传输时压缩。默认False
            locked: 暂不明白
            modtime: False拉取的文件时间是拉取时间，True拉取的文件是用户提交的时间。默认False
            rmdir: False不删除空文件夹，True删除空文件夹。默认False
        """
        self.__options.set_options(allwrite=allwrite, clobber=clobber, compress=compress, locked=locked, modtime=modtime, rmdir=rmdir)
        self.config["Options"] = self.__options.get_options()
        self.save_client(config=self.config)

    def get_options(self) -> str:
        return self.__options.get_options()

    def set_line_end(self, line_end: str = "local"):
        """
        设置换行符

        Args:
            line_end: 换行符，默认是local
        """
        self.config["LineEnd"] = line_end
        self.save_client(config=self.config)

    def set_charset(self, charset: Charset):
        """
        设置字符集

        Args:
            charset: 字符集，默认是utf8
        """
        log.info(f"change p4 charset from {self.__p4.charset} to {charset}")
        self.__p4.charset = charset.value
        # self.disconnect()
        # self.connect()

    def set_ignorefile(self, path: str):
        """
        设置p4 ignorefile的路径

        Args:
            path: ignore文件路径
        """
        if not os.path.exists(path):
            raise PyframeException(f"{path} does not exist")
        log.info(f"set p4 ignorefile {path}")
        self.__p4.ignore_file = path

    def fetch_client(self, client: str):
        log.info(f"p4 fetch client {client}")
        try:
            self.config = self.__p4.fetch_client(client)
        except P4Exception as e:
            raise PyframeException(f"p4 fetch client {client}失败, 异常: {e}")
        return self.config

    def save_client(self, config: dict):
        log.info(f"p4 save client {config['Client']}")
        try:
            self.__p4.save_client(config)
        except P4Exception as e:
            raise PyframeException(f"p4 save client {config}失败, 异常: {e}")

    def use_client(self, client: str):
        """
        使用client

        Args:
            client: client名称
        """
        if not self.is_exist_client(client):
            raise PyframeException(f"{client} does not exist")
        self.config = self.fetch_client(client=client)
        self.save_client(config=self.config)
        # log.debug(self)

    def print_client(self):
        """
        打印client
        """
        # log.info(self)
        log.info(self.__p4)

    def remove_host(self):
        """
        删除host
        """
        if "Host" in self.config:
            del self.config["Host"]
            self.save_client(config=self.config)

    def set_root(self, path: str):
        """
        设置当前workspace的根目录
        Args:
            path: p4根目录
        """
        path_mgr.mkdir(path=path)
        if Path(self.config["Root"]).absolute() != Path(path).absolute():
            log.debug("{} != {}".format(self.config["Root"], path))
            self.delete_client(self.client)
            self.config["Root"] = path
        self.save_client(config=self.config)

    def delete_client(self, client: str):
        """
        删除workspace
        Args:
            client: 待删除的client
        """
        if not self.is_exist_client(client):
            log.warning(f"delete p4 client {client} failed, it does not exist")
            return

        if self.config["Owner"] != self.__username:
            raise PyframeException(f"删除workspace: {client}失败, 原因: 它不是由{self.__username}创建")
        else:
            self.__p4.run("client", "-d", client)
            log.warning(f"delete client {client} by {self.__username}")

    def set_view(self, views: list):
        """
        设置view，该操作会覆盖原有映射

        Args:
            views: 映射列表
        """
        log.info(f"p4_port: {self.__host}")
        log.info(f"p4_user: {self.__username}")
        log.info(f"p4_workspace: {self.client}")
        for view in views:
            log.info(view)
        self.config["View"] = views
        # log.info(f"config: {self.config}")
        # self.save_client(config=self.config)
        # self.config = self.fetch_client(client=self.client)
        # log.debug(self)
        if self.is_contain_chinese():
            log.info("contain_chinese")
            self.view_gbk()
        else:
            log.info("not contain_chinese")
            self.save_client(config=self.config)

    def get_view(self):
        """
        获取view
        """
        self.config = self.fetch_client(client=self.client)
        return self.config["View"]

    # @retry(stop=stop_after_attempt(2), wait=wait_fixed(1))
    def view_gbk(self):
        filepath = "p4client_spec"
        with open(filepath, "wb+") as file:
            file.write(self.__p4.format_spec("client", self.config).encode("gbk"))
        cat = "type" if platform.system() == "Windows" else "cat"
        ret = cmd.run_shell(
            cmds=[" ".join([cat, filepath, "|", "p4", "client", "-i"])],
            environ={
                **os.environ,
                "P4PORT": self.__host,
                "P4USER": self.__username,
                "P4PASSWD": self.__password,
                "P4CLIENT": self.client,
                "P4CHARSET": self.__p4.charset,
            },
            encoding="gbk",
            return_output=True,
        )
        if ret[0] != 0:
            cmd.run_shell(cmds=["sh scripts/x51/install_perforce_centos6.sh"])
            raise PyframeException(f"p4设置中文映射失败, 异常: {ret[1]}")
        os.remove(filepath)

    def append_view(self, views: list):
        """
        追加映射，只追加

        Args:
            views: 映射列表
        """
        for view in views:
            self.config["View"].append(view)
        self.save_client(config=self.config)
        log.debug(self)
        if self.is_contain_chinese():
            log.info("contain_chinese")
            self.view_gbk()

    def sync_force(self, path: str, changelist: str = "head"):
        """
        强制拉取p4目录到本地

        Args:
            path: 待拉取的目录
            changelist: p4提交的changelist，如果更到最新，则用HEAD或者head
            force: False增量更新，True强制更新
        """
        self.sync(path=path, changelist=changelist, force=True)

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
    @advance.timeout(seconds=3600, exception_msg="从p4拉取资源超时")
    def sync(self, path: str, changelist: str = "head", force: bool = False):
        """
        拉取p4目录到本地

        Args:
            path: 待拉取的目录
            changelist: p4提交的changelist，如果更到最新，则用HEAD或者head
            force: False增量更新，True强制更新
        """
        self.connect()
        path, changelist = path.strip(), changelist.strip().lower()
        if path.endswith("/"):
            path += "..."
        if changelist == "head":
            arg = "{}#{}".format(path, changelist)
        else:
            arg = "{}@{}".format(path, changelist)
        if force:
            log.info(f"p4 sync -f {arg}")
            try:
                self.__p4.run_sync("-f", arg)
                # ret = self.__p4.run_sync("-f", arg)
                # log.info(ret)
            except P4Exception as e:
                log.warn(e)
                raise PyframeException(f"更新p4失败, {e}")
        else:
            log.info(f"p4 sync {arg}")
            try:
                self.__p4.run_sync(arg)
                # ret = self.__p4.run_sync(arg)
                # log.info(ret)
            except P4Exception as e:
                log.warning(e)
                raise PyframeException(f"更新p4失败, {e}")

    def sync_list(self, path_list: list, changelist: str = "head", force: bool = False):
        """
        拉取p4目录到本地

        Args:
            path_list: 待拉取的目录列表
            changelist: p4提交的changelist，如果更到最新，则用HEAD或者head
            force: False增量更新，True强制更新
        """
        for path in path_list:
            self.sync(path=path, changelist=changelist, force=force)

    def sync_all(self, changelist: str = "head", force: bool = False):
        """
        拉取整个p4目录到本地

        Args:
            changelist: p4提交的changelist，如果更到最新，则用HEAD或者head
            force: False增量更新，True强制更新
        """
        # self.sync(path="//...", changelist=changelist, force=force)
        try:
            for view in self.config["View"]:
                view = view.strip()
                path = view.split(" ")[0]
                if not path.startswith("-"):
                    self.sync(path=path, changelist=changelist, force=force)
                else:
                    log.warn(f"ignore {path}")
        except P4Exception as e:
            log.error(e)
            raise PyframeException("sync整个views失败")

    def submit(self, desc: str, revert_if_failed: bool = False):
        log.info("p4 submit -d {}".format(desc))
        try:
            ret = self.__p4.run_submit("-d", f"{desc}")
            log.debug(ret)
            return ret
        except P4Exception as e:
            if revert_if_failed:
                self.revert("//...")
            log.error(f"p4 submit失败, 异常: {e}")
            if "该文件已经被锁定，无法进行修改或者操作，请联系PM进行解锁." in str(e):
                # error是下面这样的掐头去尾提取路径
                # [error] x5mP4CoverError path 该文件已经被锁定，无法进行修改或者操作，请联系PM进行解锁. [x5mP4Cover]
                lock_path = ""
                for err in str(e).split("该文件已经被锁定，无法进行修改或者操作，请联系PM进行解锁."):
                    if "[error] x5mP4CoverError " in err:
                        lock_path += "\n" + err.strip().split("[error] x5mP4CoverError ")[1]
                raise PyframeException(f"p4 submit失败,以下文件被锁定,请联系PM解锁：{lock_path}")
            raise PyframeException(f"p4 submit失败")

    def add(self, path: str) -> List[str]:
        if path.endswith("/"):
            path += "..."
        log.info("p4 add {}".format(path))
        depot_files = []
        try:
            ret = self.__p4.run_add(path)
            log.info(f"ret: {ret}")
            for ret in ret:
                if isinstance(ret, dict):
                    action, depot_file = ret["action"], ret["depotFile"]
                    log.info(f"{action} {depot_file}")
                    depot_files.append(depot_file)
                elif isinstance(ret, str):
                    if "can't edit exclusive file already opened" in ret:
                        log.warning(ret)
                        raise PyframeException(f"p4 edit {path}失败, {ret}")
                    else:
                        log.debug(ret)
                else:
                    log.debug(ret)
            return depot_files
        except P4Exception as e:
            log.warning(e)
            raise PyframeException(f"p4 add {path}失败")

    def edit(self, path: str) -> List[str]:
        if path.endswith("/") or path.endswith("..."):
            log.warning("正在edit一个目录")
            # raise P4Exception("禁止edit一个目录")
        log.info("p4 edit {}".format(path))
        depot_files = []
        try:
            ret = self.__p4.run_edit(path)
            log.info("edit ret: %s", ret)
            for ret in ret:
                if isinstance(ret, dict):
                    action, depot_file = ret["action"], ret["depotFile"]
                    log.info(f"{action} {depot_file}")
                    depot_files.append(depot_file)
                elif isinstance(ret, str):
                    if "can't edit exclusive file already opened" in ret:
                        log.warning(ret)
                        raise PyframeException(f"p4 edit {path}失败, {ret}")
                    else:
                        log.debug(ret)
                else:
                    log.debug(ret)
            return depot_files
        except P4Exception as e:
            log.warning(e)
            raise PyframeException(f"p4 edit {path}失败")

    def clean(self, path: str, a: bool = False, d: bool = False):
        if path.endswith("/"):
            path += "..."
        try:
            if a:
                log.info("p4 clean -a {}".format(path))
                self.__p4.run_clean("-a", path)
            elif d:
                log.info("p4 clean -d {}".format(path))
                self.__p4.run_clean("-d", path)
            else:
                log.info("p4 clean {}".format(path))
                self.__p4.run_clean(path)
        except P4Exception as e:
            log.warning(e)
            raise PyframeException(f"p4 clean {path}失败")

    def opened(self, path: str, all_clients: bool = False, short: bool = False) -> list:
        """
        p4 opened, 默认情况下将会展示当前用户在当前workspace下锁定的文件
        Args:
            path: 指定opened命令操作的路径
            all_clients: 将会展示所有workspace下被锁定的文件
            short: short参数将会精简并优化all_clients的输出, 对于大存储仓库来说, short速度更快
        Returns:
            list: 检查到锁定的文件信息列表
            使用short, all_clients参数返回值如下:
            [
                {
                    "depotFile": "//x5_mobile/mr/art_release/art_src/chair/1040012001/Materials/twosidetransparent.mat",
                    "action": "add",  # 一般有值为add, edit, delete
                    "change": "default",  # 标识当前文件锁定时放在了哪个pending list中, 例子是放在了default中
                    "user": "liangchen",
                    "cleint": "192.168.5.15_work",
                }
            ]
        """
        if path.endswith("/"):
            path += "..."

        args = []
        if all_clients:
            args.append("-a")
        if short:
            args.append("-s")

        try:
            log.info("p4 opened {} {}".format(" ".join(args), path))
            result = self.__p4.run_opened(*args, path)
        except P4Exception as e:
            log.warning(e)
            raise PyframeException("p4 opened {} {}失败".format(*args, path))

        return result

    def reconcile(self, path: str, add: bool = True, edit: bool = True, delete: bool = False, preview: bool = False) -> list:
        """
        p4 reconcile
        Args:
            path: 待提交的文件或者目录名
            add: 是否add
            edit: 是否edit
            delete: 是否delete
            preview: 是否预览
        Returns:
            list: 变更的文件列表
        """
        args = []
        if add:
            args.append("-a")
        if edit:
            args.append("-e")
        if delete:
            args.append("-d")
        if preview:
            args.append("-n")

        if path.endswith("/"):
            path += "..."

        depot_files = []
        try:
            log.info("p4 reconcile {} {}".format(" ".join(args), path))
            results = self.__p4.run_reconcile(*args, path)
            for ret in results:
                if isinstance(ret, dict):
                    action, depot_file = ret["action"], ret["depotFile"]
                    log.info(f"{action} {depot_file}")
                    depot_files.append(ret["depotFile"])
                elif isinstance(ret, str):
                    if "can't edit exclusive file already opened" in ret:
                        log.warning(ret)
                        raise PyframeException(f"p4 reconcile {path}失败, {ret}")
                    else:
                        log.debug(ret)
                else:
                    log.debug(ret)
        except P4Exception as e:
            log.warning(e)
            raise PyframeException(f"p4 reconcile {path}失败, {e}")
        return depot_files

    def reconcile_with_extra_args(
        self, path: str, *extra_args, add: bool = True, edit: bool = True, delete: bool = False, preview: bool = False
    ) -> list:
        """
        p4 reconcile
        Args:
            extra_args: 额外的参数
            path: 待提交的文件或者目录名
            add: 是否add
            edit: 是否edit
            delete: 是否delete
            preview: 是否预览
        Returns:
            list: 变更的文件列表
        """
        args = []
        if add:
            args.append("-a")
        if edit:
            args.append("-e")
        if delete:
            args.append("-d")
        if preview:
            args.append("-n")
        for arg in extra_args:
            args.append(arg)

        if path.endswith("/"):
            path += "..."

        depot_files = []
        try:
            log.info("p4 reconcile {} {}".format(" ".join(args), path))
            results = self.__p4.run_reconcile(*args, path)
            for ret in results:
                if isinstance(ret, dict):
                    action, depot_file = ret["action"], ret["depotFile"]
                    log.info(f"{action} {depot_file}")
                    depot_files.append(ret["depotFile"])
                elif isinstance(ret, str):
                    if "can't edit exclusive file already opened" in ret:
                        log.warning(ret)
                        raise PyframeException(f"p4 reconcile {path}失败, {ret}")
                    else:
                        log.debug(ret)
                else:
                    log.debug(ret)
        except P4Exception as e:
            log.warning(e)
            raise PyframeException(f"p4 reconcile {path}失败, {e}")
        return depot_files

    def reconcile_preview(self, path: str) -> list:
        """
        p4 reconcile -n
        Args:
            path: 待提交的文件或者目录名
        Returns:
            list: 变更的文件列表
        """
        if path.endswith("/"):
            path += "..."
        depot_files = []
        try:
            log.info(f"p4 reconcile -n {path}")
            results = self.__p4.run_reconcile("-n", path)
            log.debug(results)  # 临时日志
            for ret in results:
                if isinstance(ret, dict):
                    action, depot_file = ret["action"], ret["depotFile"]
                    log.info(f"action: {action}, depot_file: {depot_file}")
                    depot_files.append(depot_file)
                else:
                    log.debug(ret)
        except P4Exception as e:
            log.warning(e)
            raise PyframeException(f"p4 reconcile -n {path}失败, {e}")
        return depot_files

    def reconcile_without_tmp(self, path: str, preview: bool = False) -> list:
        """
        p4 reconcile, 不包含临时文件
        Args:
            path: 待提交的文件或者目录名
        Returns:
            list: 变更的文件列表
        """
        depotFiles = self.reconcile_preview(path)
        depotFiles = [x for x in depotFiles if not x.endswith(".tmp")]
        for depotFile in depotFiles:
            self.reconcile(path=depotFile, preview=preview)
        return depotFiles

    def revert(self, path: str):
        if path.endswith("/"):
            path += "..."
        log.info("p4 revert {}".format(path))
        try:
            self.__p4.run_revert(path)
        except P4Exception as e:
            log.warning(e)
            raise PyframeException(f"p4 revert {path}失败")

    def fast_submit(self, files: list, desc: str = "fast_submit by {}".format(P4USER)):
        """
        快速提交一个文件列表

        Args:
            filelist: 待提交的文件列表
            description: 提交信息
        """
        log.info(f"fast_submit {files} {desc}")
        for file in files:
            # self.add(file=file)
            # self.edit(file=file)
            self.reconcile(file)
        self.submit(desc=desc)

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
    def dirs(self, path: str) -> list or None:
        """
        列举p4目录下的子目录，如p4.dirs(path="//depot/projects/*")

        具体参考： https://www.perforce.com/manuals/cmdref/Content/CmdRef/p4_dirs.html#p4_dirs
        Args:
            path: p4目录，如"//depot/projects/*"
        Returns:
            list: 返回列表
        """
        log.info(f"p4 dirs {path}")
        try:
            dirs = self.__p4.run_dirs(path)
            ret = []
            for path in dirs:
                ret.append(path["dir"])
            # log.debug(ret)
            return ret
        except P4Exception as e:
            log.error(e)
            raise PyframeException(f"p4 dirs {path}失败, {e}")

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
    def files(self, path: str):
        """
        p4 files
        """
        try:
            log.info(f"p4 files {path}")
            files = self.__p4.run("files", "-e", path)
            ret = []
            for file in files:
                ret.append(file["depotFile"])
            # log.info(ret)
            return ret
        except P4Exception as e:
            log.error(e)
            raise PyframeException(f"p4 files {path}失败, {e}")

    def get_changes(self, path: str, max: int) -> list:
        """
        获取变更记录
        Args:
            path: p4目录
            max: 最大返回条数
        Returns:
            list: 变更记录列表
        """
        if isinstance(max, int):
            max = str(max)
        log.info(f"p4 changes -m {max} -l {path}")
        try:
            changes = self.__p4.run("changes", "-m", max, "-l", path)
            return changes
        except P4Exception as e:
            log.error(e)
            raise PyframeException(f"p4 changes {path}失败, {e}")

    def get_latest_changes(self, path: str) -> Optional[dict]:
        """
        获取p4最新变更记录
        Args:
            path: p4路径

        Returns:
            p4 changes -m 1 path的返回值字典。示例：
            {
                'change': '1900010',
                'time': '1649690726',
                'user': 'zhangyan',
                'client': 'xw2_src',
                'status': 'submitted',
                'changeType': 'public',
                'path': '//depot/products/Project_X52/...',
                'desc': 'XW236950 测试\n'
            }
        """
        try:
            log.info(f"p4 changes -m 1 -l {path}")
            ret = self.__p4.run("changes", "-m", "1", "-l", path)
            log.info(ret)
            if ret:
                return ret[0]
            return {}
        except P4Exception as e:
            log.error(e)
            raise PyframeException(f"p4 get_latest_changes {path}失败, {e}")

    def get_current_workspace_latest_changes(self) -> dict or None:
        """
        获取当前workspace映射下的最新变更记录
        Args:
            path: p4路径

        Returns:
            示例：
            {
                'change': '1900010',
                'time': '1649690726',
                'user': 'zhangyan',
                'client': 'xw2_src',
                'status': 'submitted',
                'changeType': 'public',
                'path': '//depot/products/Project_X52/...',
                'desc': 'XW236950 测试\n'
            }
        """
        changes = []
        views = self.get_view()
        for view in views:
            path = view.strip().split(" ")[0]
            if path.startswith("//"):
                changes.append(self.get_latest_changes(path))
        if len(changes) == 0:
            log.error("当前workspace可能没有映射路径，或者没有提交记录")
            raise PyframeException("获取p4最新changlist失败, 当前workspace可能没有映射路径，或者没有提交记录")
        changes.sort(key=lambda x: x["change"])
        max_change = changes[0]
        for i in range(1, len(changes)):
            if int(changes[i]["change"]) > int(max_change["change"]):
                max_change = changes[i]
        return max_change

    # def get_current_workspace_latest_change_num(self) -> str or None:
    #     """
    #     获取当前workspace映射下的最新变更记录
    #     Args:
    #         path: p4路径
    #
    #     Returns:
    #         示例：
    #         {
    #             'change': '1900010',
    #             'time': '1649690726',
    #             'user': 'zhangyan',
    #             'client': 'xw2_src',
    #             'status': 'submitted',
    #             'changeType': 'public',
    #             'path': '//depot/products/Project_X52/...',
    #             'desc': 'XW236950 测试\n'
    #         }
    #     """
    #     changes = []
    #     views = self.get_view()
    #     for view in views:
    #         path = view.strip().split(" ")[0]
    #         if path.startswith("//"):
    #             changes.append(self.get_latest_changes(path)['change'])
    #     if len(changes) == 0:
    #         log.error("当前workspace可能没有映射路径，或者没有提交记录")
    #         raise PyframeException("获取p4最新changlist失败, 当前workspace可能没有映射路径，或者没有提交记录")
    #     max_change = self.get_max_num_str(num_str_list=changes)
    #     return max_change
    #
    # def get_max_num_str(self, num_str_list: list):
    #     max_len = len(max(num_str_list))
    #     max_list = []
    #     for num_str in num_str_list:
    #         if len(num_str) > max_len:
    #             max_len = len(num_str)
    #             max_list = []
    #         elif len(num_str) == max_len:
    #             max_list.append(num_str)
    #     for i in range(len(max_list)):
    #         for j in range(0, len(max_list)-1):
    #             if self.compare_two_num_str(max_list[i], max_list[j]) == -1:
    #                 max_list[i], max_list[j] = max_list[j], max_list[i]
    #     return max_list[-1]
    #
    # def compare_two_num_str(self, v1: str, v2: str) -> int:
    #     for _v1, _v2 in zip(v1, v2):
    #         _v1, _v2 = int(_v1), int(_v2)
    #         if v1 > v2:
    #             return 1
    #         elif v1 < v2:
    #             return -1
    #     return 0

    def print(self, path: str, to: str):
        """
        p4 print
        Args:
            path: p4路径
            to: 写入路径
        """
        log.info(f"p4 print {path} > {to}")
        try:
            self.__p4.run_print("-o", to, path)
        except P4Exception as e:
            log.error(e)
            raise PyframeException(f"p4 print {path}失败, {e}")

    def sync_with_files_and_dirs(self, path: str, changelist: str = "head", force: bool = False):
        """
        更新一级目录下的所有文件和子目录
        Args:
            path: p4路径
            changelist: 提交号
            force: 是否强制更新
        """
        path = path.strip()
        if path.endswith("..."):
            path = path.replace("...", "*")
        rets = self.files(path=path)
        if len(rets) != 0 and rets is not None:
            for p in rets:
                # log.debug("files: {}".format(p))
                self.sync(path=p, changelist=changelist, force=force)
        rets = self.dirs(path=path)
        if len(rets) != 0 and rets is not None:
            for p in rets:
                p = f"{p}/..."
                # log.debug("dirs: {}".format(p))
                self.sync(path=p, changelist=changelist, force=force)

    def filelog_latest(self, path: str) -> dict:
        """
        p4 filelog
        Args:
            path: p4路径
        Returns:
            dict: 返回字典
        """
        if path.endswith("/"):
            path += "..."
        log.info(f"p4 filelog -t {path}#head,#head")
        try:
            ret = self.__p4.run_filelog("-t", f"{path}#head,#head")
            latest_revision = ret[0].revisions[0]
            dic = {
                "depotFile": latest_revision.depotFile,
                "rev": latest_revision.rev,
                "change": latest_revision.change,
                "action": latest_revision.action,
                "type": latest_revision.type,
                "time": latest_revision.time,
                "user": latest_revision.user,
                "client": latest_revision.client,
                "desc": latest_revision.desc,
                "digest": latest_revision.digest,
                "fileSize": latest_revision.fileSize,
            }
            # log.debug(dic)
            return dic
        except P4Exception as e:
            log.error(e)
            raise PyframeException(f"p4 filelog {path}失败, {e}")

    def get_describe_by_changelist(self, changelist):
        log.info(f"p4 describe -s {changelist}")
        try:
            ret = self.__p4.run_describe(f"{changelist}")
            desc = ret[0].get("desc").strip()
        except Exception as e:
            log.error(e)
            raise PyframeException(f"p4 describe {changelist}失败, {e}")

        return desc

    def get_files_by_changelist(self, changelist: str, depotfile_only=True) -> list:
        """
        获取changelist中的文件列表
        Args:
            changelist:

        Returns:
            {"change": "xxx", "depotFile": ["xxx", "xxx"], "action": ["add"], "type": ["binary+l"]}
        """
        # log.info(f"p4 describe -s {changelist}")
        try:
            ret = self.__p4.run_describe(f"{changelist}")
            if depotfile_only:
                files = ret[0].get("depotFile")
            else:
                files = ret[0]
        except Exception as e:
            log.error(e)
            raise PyframeException(f"p4 describe {changelist}失败, {e}")

        return files

    def annotate(self, path: str, line: int, u: bool = True) -> dict:
        """
        查找提交人
        Args:
            path: p4路径
            line: 行号
            u: 是否显示用户
        Returns:
            dict: 返回字典
        """
        args = []
        if u:
            args.append("-u")
        args.append(path)
        log.info(f"p4 annotate {' '.join(args)}")
        try:
            ret = self.__p4.run_annotate(args)
            target_line = ret[line]
            # log.debug(target_line)
            dic = {
                "upper": target_line["upper"],
                "lower": target_line["lower"],
                "user": target_line["user"],
                "time": target_line["time"],
                "client": target_line["client"],
                "data": target_line["data"],
            }
            return dic
        except P4Exception as e:
            log.error(e)
            raise PyframeException(f"p4 find_submiter {path}失败, {e}")

    def blame_latest(self, path: str, line: int) -> dict:
        """
        p4 blame最新版本
        Args:
            path: p4路径
            line: 行号

        Returns:
            dict: 返回字典
        """
        try:
            annotate_ret = self.annotate(path=path, u=True, line=line)
            filelog_ret = self.filelog_latest(path=path)
            ret = {
                "upper": annotate_ret["upper"],
                "lower": annotate_ret["lower"],
                "user": annotate_ret["user"],
                "time": annotate_ret["time"],
                "client": annotate_ret["client"],
                "data": annotate_ret["data"],
                "depotFile": filelog_ret["depotFile"],
                "rev": filelog_ret["rev"],
                "change": filelog_ret["change"],
                "action": filelog_ret["action"],
                "type": filelog_ret["type"],
                "desc": filelog_ret["desc"],
                "digest": filelog_ret["digest"],
                "fileSize": filelog_ret["fileSize"],
            }
            return ret
        except P4Exception as e:
            log.error(e)
            raise PyframeException(f"p4 blame_latest {path}失败, {e}")

    def delete(self, path: str):
        if path.endswith("/"):
            path += "..."
        log.info("p4 delete {}".format(path))
        try:
            self.__p4.run("delete", path)
        except P4Exception as e:
            log.warning(e)
            raise PyframeException(f"p4 delete {path}失败")

    def diff2(self, path1: str, path2: str, q: bool):
        if path1.endswith("/"):
            path1 += "..."
        if path2.endswith("/"):
            path2 += "..."
        args = []
        if q:
            args.append("-q")
        args.append(path1)
        args.append(path2)
        log.info(f"p4 diff2 {' '.join(args)}")
        try:
            ret = self.__p4.run_diff2(args)
            return ret
        except P4Exception as e:
            log.error(e)
            raise PyframeException(f"p4 diff2 {path1} {path2}失败, {e}")

    def describe(self, changelist, s: bool):
        args = []
        if s:
            args.append("-s")
        args.append(changelist)
        log.info(f"p4 describe {' '.join(args)} {changelist}")
        try:
            ret = self.__p4.run_describe(args)
            return ret
        except P4Exception as e:
            log.error(e)
            raise PyframeException(f"p4 describe {changelist}")

    def copy(self, src: str, dst: str) -> list:
        """
        将src目录复制到一个新目录
        Args:
            src: 源路径
            dst: 目标路径
        Returns: 返回本次copy了哪些文件
        """
        if src.endswith("/"):
            src += "..."

        if dst.endswith("/"):
            dst += "..."
        if not self.dirs(src.replace("...", "*")):
            raise PyframeException(f"p4源路径{src}不存在")

        if self.dirs(dst.replace("...", "*")):
            raise PyframeException(f"p4分支路径{dst}已经存在")

        log.info(f"p4 copy {src} {dst}")
        try:
            result = self.__p4.run_copy(src, dst)
        except Exception as e:
            raise PyframeException(f"p4 copy {src} {dst}失败, {e.__str__()}")

        return result

    def populate(self, src: str, dst: str) -> list:
        """
        快速将文件添加到目标路径, 不需要使用workspace
        Args:
            src: 源路径
            dst: 目标路径
        Returns: populate changelist和文件数量
            [
                {
                    "change": "89",
                    "fileCount": "205",
                }
            ]
        """
        if src.endswith("/"):
            src += "..."

        if dst.endswith("/"):
            dst += "..."
        if not self.dirs(src.replace("...", "*")):
            raise PyframeException(f"p4源路径{src}不存在")

        log.info(f"p4 populate {src} {dst}")
        try:
            result = self.__p4.run_populate(src, dst)
        except Exception as e:
            raise PyframeException(f"p4 populate {src} {dst}失败, {e.__str__()}")

        return result

    def run(self, *args):
        """
        执行p4命令
        Args:
            *args: p4命令参数
        Returns: 返回p4命令结果
        """
        log.info(f"p4 {' '.join(args)}")
        try:
            result = self.__p4.run(*args)
        except Exception as e:
            raise PyframeException(f"p4 {' '.join(args)}失败, {e.__str__()}")
        return result
