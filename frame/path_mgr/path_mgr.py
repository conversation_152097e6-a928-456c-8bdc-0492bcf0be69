# coding=utf-8
import os
import shutil
import stat
from pathlib import Path, PosixPath, WindowsPath
from typing import Union

from frame.cmd.cmd import cmd
from frame.exception.exception import PyframeException
from frame.log.log import log


class PathMgr:
    def __init__(self):
        pass

    def mklink(self, src: str, dst: str):
        """
        创建链接
        """
        if not Path(src).exists():
            log.error(f"{src} doesn't exist")
            raise PyframeException(f"建立链接失败, {src}不存在")

        p = Path(src)
        p.symlink_to(dst)

    def unlink(self, path: str):
        """
        删除链接
        """
        p = Path(path)
        if not p.exists():
            log.warn(f"{path} doesn't exist")
            return
        if p.is_symlink():
            try:
                log.info(f"unlink {path}")
                p.unlink()
            except Exception as e:
                log.error(e)
                raise PyframeException(f"删除链接失败, 异常{e}")

    # @backup
    def soft_link(self, src: str, dst: str):
        """
        创建软链接
        Args:
            src: 原始路径
            dst: 目标路径
        """
        if not Path(src).exists():
            log.error(f"{src} doesn't exist")
            raise PyframeException(f"建立软链接失败, {src}不存在")

        if not Path(dst).parent.exists():
            self.mkdir(path=Path(dst).parent)

        if Path(dst).is_symlink():
            log.warn(f"unlink {dst}")
            Path(dst).unlink()
        # elif os.path.islink(dst):
        #     log.warn("os.path.islink(dst)")
        elif Path(dst).is_dir():
            log.warn(f"{dst} is dir")
            self.rm(path=dst)
        try:
            log.info(f"src: {src}")
            log.info(f"dst: {dst}")
            os.symlink(src, dst)
        except Exception as e:
            log.error(e)
            raise PyframeException(f"建立软链接失败, 异常{e}")

    # @backup
    def hard_link(self, src: str, dst: str):
        """
        创建硬链接
        Args:
            src: 原始路径
            dst: 目标路径
        """
        if not os.path.isfile(src):
            log.error("hard link must be a file")
            raise PyframeException("hard link must be a file")

        try:
            log.info(f"src: {src}")
            log.info(f"dst: {dst}")
            os.link(src, dst)
        except Exception as e:
            log.error(e)
            raise PyframeException(f"建立硬链接失败, 异常{e}")

    def get_absolute(self, path: str) -> str:
        """
        获取绝对路径
        """
        return ""

    def chmod(self, path: Union[str, os.PathLike], read: bool = False, write: bool = False, execute: bool = False, verbose: bool = False):
        """
        修改权限
        """
        log.info(f"chmod {path}, read: {read}, write: {write}, execute: {execute}, verbose: {verbose}")

        def change_file_mode(p: Path, read: bool = False, write: bool = False, execute: bool = False):
            mode = p.stat().st_mode
            if read:
                mode |= stat.S_IRUSR
            else:
                mode &= ~stat.S_IRUSR

            if write:
                mode |= stat.S_IWUSR
            else:
                mode &= ~stat.S_IWUSR

            if execute:
                mode |= stat.S_IXUSR
            else:
                mode &= ~stat.S_IXUSR
            p.chmod(mode)

        mode_str = ""
        if read:
            mode_str += "r"
        if write:
            mode_str += "w"
        if execute:
            mode_str += "x"

        p = Path(path)
        if p.is_file():
            if verbose:
                log.info(f"{p} {mode_str}")
            change_file_mode(p=p, read=read, write=write, execute=execute)
        elif p.is_dir():
            for i in p.iterdir():
                change_file_mode(p=i, read=read, write=write, execute=execute)
                if verbose:
                    log.info(f"{i} {mode_str}")
        else:
            log.warn(f"{p} isn't file or dir")

    def get_mode(self, path: Union[str, os.PathLike]) -> str:
        """
        获取权限
        Args:
            path: 路径
        Returns:
            权限字符串
        """
        mode = Path(path)
        mode_str = ""
        mode = mode.stat().st_mode
        if mode & stat.S_IRUSR:
            mode_str += "r"
        if mode & stat.S_IWUSR:
            mode_str += "w"

        if mode.is_file():
            if mode & stat.S_IXUSR:
                mode_str += "x"
        return mode_str
        # log.info(f"{path} mode: {s}")

    def iter_print_mode(self, path: Union[str, os.PathLike]):
        """
        遍历打印权限
        """
        p = Path(path)
        for i in p.iterdir():
            mode = self.get_mode(i)
            log.info(f"{i} mode: {mode}")

    def touch(self, path: str, filename: str):
        """
        创建文件
        """
        pass

    def move(self, src: str, dst: str):
        """
        移动文件或者目录
        移动文件：
            src, dst = "test.txt", "tests/test.txt"
        移动目录：
            src, dst = "test", "tests"        # 移动 test到 tests下 （单纯的移动）
            src, dst = "test", "tests/test"   # 移动 test到 tests下并命名为 test （移动+重命名）
        如果中间路径不存在 会默认创建
        Args:
            src: 待拷贝的文件或目录
            dst: 将要拷贝的目录
        """
        log.info(f"src: {src}")
        log.info(f"dst: {dst}")
        if not Path(src).exists():
            log.error(f"{src} doesn't exist")
            raise PyframeException(f"移动失败, {src}不存在")
        if Path(src).is_file():
            path_mgr.mkdir(path=os.path.dirname(dst))
        try:
            shutil.move(src=src, dst=dst)
        except Exception as e:
            log.error(e)
            raise PyframeException(f"移动失败, 异常{e}")

    def copy(self, src: str, dst: str, overwrite: bool = True, verbose: bool = False):
        """
        拷贝文件或者目录
            拷贝文件: src, dst = "readme.md", "path/readme.md"    # 拷贝加重命名
            拷贝文件夹: src, dst = r"zhangtao", r"tests/zhangtao"    # dst需要填上复制后的目录名 如果不填就会将src中包含的文件拷贝到dst目录下
        Args:
            src: 待拷贝的文件
            dst: 将要拷贝的目录
            overwrite: 是否覆盖
            verbose: 是否打印拷贝过程日志
        """
        if not Path(src).exists():
            log.error(f"{src} doesn't exist")
            raise PyframeException(f"{src}不存在")
        if not verbose:
            log.info(f"src: {src}")
            log.info(f"dst: {dst}")
        if self.is_file(path=src):
            self.__copy_file(src=src, dst=dst, overwrite=overwrite, verbose=verbose)
        elif self.is_dir(path=src):
            self.__copy_folder(src=src, dst=dst, overwrite=overwrite, verbose=verbose)
        else:
            log.error(f"src: {src} is not file or folder")
            raise PyframeException(f"拷贝失败, {src}既不文件也不是文件夹")

    def __copy_file(self, src: str, dst: str, overwrite: bool = True, verbose: bool = True):
        """
        拷贝文件
            src, dst = "readme.md", "path/readme.md"    # 拷贝加重命名
        Args:
            src: 待拷贝的文件
            dst: 将要拷贝的目录
            overwrite: 是否覆盖
            verbose: 是否打印拷贝过程日志
        """
        if not Path(src).exists():
            log.error(f"{src} doesn't exist")
            raise PyframeException(f"拷贝文件失败, src: {src}不存在")

        if not Path(src).is_file():
            log.error(f"src: {src} is not file")
            raise PyframeException(f"拷贝文件失败, src: {src}不是文件")

        if not Path(dst).exists() or overwrite:
            if verbose:
                log.info(f"src: {src}")
                log.info(f"dst: {dst}")
            try:
                os.makedirs(os.path.dirname(dst), exist_ok=True)
                shutil.copy2(src=src, dst=dst)
            except Exception as e:
                log.error(e)
                raise PyframeException(f"拷贝文件失败, src: {src}, dst: {dst}")
        else:
            log.warn(f"ignore {src} to {dst}")

    def __copy_folder(self, src: str, dst: str, overwrite: bool = True, verbose: bool = True):
        """
        拷贝目录
            src, dst = r"zhangtao", r"tests/zhangtao"    # dst需要填上复制后的目录名 如果不填就会将src中包含的文件拷贝到dst目录下
        Args:
            src: 待拷贝的目录
            dst: 将要拷贝的目录
            overwrite: 是否覆盖
            verbose: 是否显示拷贝过程
        """
        if not Path(src).exists():
            log.error(f"src: {src} doesn't exist")
            raise PyframeException(f"拷贝目录失败, src: {src}不存在")
        if not Path(src).is_dir():
            log.error(f"src: {src} is not dir")
            raise PyframeException(f"拷贝目录失败, src: {src}不是目录")

        def copytree_not_exists(src: str, dst: str, overwrite: bool, verbose: bool):
            src_file_list = os.listdir(src)
            for file in src_file_list:
                src_file_path = os.path.join(src, file)
                if Path(src_file_path).is_dir():
                    src_file_path_new = os.path.join(src, file)
                    dst_file_path_new = os.path.join(dst, file)
                    if not Path(dst_file_path_new).exists():
                        self.mkdir(path=dst_file_path_new)
                    copytree_not_exists(src=src_file_path_new, dst=dst_file_path_new, overwrite=overwrite, verbose=verbose)
                else:
                    self.__copy_file(src=src_file_path, dst=dst, overwrite=overwrite, verbose=verbose)

        if Path(dst).exists():
            copytree_not_exists(src=src, dst=dst, overwrite=overwrite, verbose=verbose)
            log.info(f"copy {src} to {dst} success!")
        else:
            shutil.copytree(src=src, dst=dst)
            log.info(f"copy {src} to {dst} success!")

    def mkdir(self, path: Union[str, Path]):
        """
        创建目录
        Args:
            path: 待创建的目录
        """
        if isinstance(path, Path):
            path = str(path)
        if not Path(path).exists():
            os.makedirs(path)
            log.info(f"mkdir {path}")

    def _rm_long_path(self, path: str) -> bool:
        """优化删除长路径场景"""
        # windows 路径长度限制
        absolute_path = Path(path).resolve().as_posix()
        # 注意：对于网络共享路径，应使用 '\\?\UNC\' 替代 '\\?\'
        if absolute_path.startswith('/'):
            absolute_path = absolute_path[1:] # 移除开头的 '/'
        long_path = Path(f"\\\\?\\{absolute_path}")
        try:
            shutil.rmtree(str(long_path))
        except Exception as e:
            log.error(e)
            return False
        log.info("retry delete long path success")
        return True

    def rm(self, path: str) -> bool:
        """
        删除路径，支持文件和文件夹
        Args:
            path: 待删除的路径
        Returns:
            bool: 是否成功删除
        """

        def remove_readonly(func, path, _):
            """
            Clear the readonly bit and reattempt the removal
            """
            os.chmod(path, stat.S_IWRITE)
            func(path)

        if isinstance(path, PosixPath) or isinstance(path, Path) or isinstance(path, WindowsPath):
            path = str(path)

        if path.lower().rstrip("/").rstrip("\\") in {"~", "", "/data", "c:", "d:", "e:", "f:"}:
            log.error(f"rm failed, delete {path} is forbidden")
            raise PyframeException(f"删除失败, 删除{path}是禁止的")

        p = Path(path)
        if not p.exists():
            log.warn(f"rm failed, {p.absolute()} doesn't exist")
            return False

        if p.is_fifo() or p.is_block_device() or p.is_socket() or p.is_char_device():
            log.error(f"rm failed, {p.absolute()} is not a file or directory")
            raise PyframeException(f"删除失败, {p.absolute()}可能是[fifo, block_device, socket, char_device]")

        if p.is_dir():
            try:
                log.warn(f"delete folder: {p.absolute()}")
                shutil.rmtree(p.absolute(), onerror=remove_readonly)
            except Exception as e:
                log.error(e)
                # NOTE: 尝试一次长路径
                if not self._rm_long_path(path):
                    raise PyframeException(f"删除失败, 异常: {e}")
        elif p.is_file():
            try:
                log.warn(f"delete file: {p.absolute()}")
                p.unlink()
            except Exception as e:
                log.error(e)
                raise PyframeException(f"删除失败, 异常: {e}")
        elif p.is_symlink():
            try:
                log.warn(f"delete symlink: {p.absolute()}")
                p.unlink()
            except Exception as e:
                log.error(e)
                raise PyframeException(f"删除失败, 异常: {e}")
        else:
            raise PyframeException("rm failed: unknown type")
        return True

    def glob(self, path: str, pattern: str) -> list:
        """
        glob文件
        Args:
            path: 待查找的目录
            pattern: glob pattern
        Returns:
            文件列表
        """
        ret = []
        p = Path(path)
        if not p.exists():
            log.info(f"{path} doesn't exist")
            return ret
        bp = p.glob(pattern)
        for b in bp:
            ret.append(str(b.absolute()))
        return ret

    def is_dir(self, path: str) -> bool:
        """
        判断path是否是目录
        Args:
            path: 路径
        Returns:
            bool: 是否是目录
        """
        if not self.exists(path=path):
            log.error(f"{path} doesn't exist")
        return Path(path).is_dir()

    def is_file(self, path: str) -> bool:
        """
        判断path是否是文件
        Args:
            path: 路径
        Returns:
            bool: 是否是文件
        """
        if not self.exists(path=path):
            log.error(f"{path} doesn't exist")
        return Path(path).is_file()

    def exists(self, path: str) -> bool:
        """
        判断path是否存在
        Args:
            path: 路径
        Returns:
            bool: 是否存在
        """
        return Path(path).exists()

    def to_std_windows_path(self, path: str) -> str:
        """
        转化为统一的windows路径
        Args:
            path: 路径
        Returns:

        """
        pass

    def xcopy(
        self,
        src: str,
        dst: str,
        dst_is_file: bool,
        overwrite: bool = True,
        verbose: bool = True,
        ignore_error: bool = True,
        quiet: bool = False,
        exclude: list = None,
    ):
        """
        xcopy 命令

        https://learn.microsoft.com/zh-cn/windows-server/administration/windows-commands/xcopy

        文件拷到目录
            拷贝单个文件：        src, dst = r"E:\test\one\1.txt", r"E:\test\test\two"
            拷贝一个目录下的多个文件：    src, dst = r"E:\test\one\*.sh", r"E:\test\test\two"
        文件拷到文件：
            src, dst = r"E:\test\one\1.txt", r"E:\test\test\two\1.txt"  参数 is_file = True
        目录拷到目录：
            src, dst = r"E:\test\one", r"E:\test\test\two"

        Args:
            src: 源路径
            dst: 目标路径
            dst_is_file: 目标路径是否是文件
            overwrite:
            verbose: 是否打印详细信息
            ignore_error: 是否忽略错误
            quiet:
            exclude: 排除的文件列表
                1. 直接写文件名字 + 后缀
                2. 写后缀, 例如.txt, 将会排除所有.txt文件的复制
        """
        log.info(f"src: {src}")
        log.info(f"dst: {dst}")
        if not self.exists(path=src):
            log.error(f"{src} doesn't exist")
            raise PyframeException(f"拷贝失败, {src}不存在")

        src = '"{}"'.format(src)
        dst = '"{}"'.format(dst)
        if dst_is_file:
            command = ["echo f|xcopy", src, dst]
        else:
            command = ["echo d|xcopy", src, dst]

        if overwrite:
            command.append("/y")
            command.append("/r")
        else:
            command.append("/d")

        if verbose:
            command.append("/f")

        if ignore_error:
            command.append("/c")

        if quiet:
            command.append("/q")

        if exclude:
            exclude_file_path = r"C:\.pyframe_pipeline_xcopy_exclude.txt"
            with open(exclude_file_path, "w", encoding="utf-8") as f:
                f.write("\n".join(exclude))

            command.append(f"/exclude:{exclude_file_path}")

        command.append("/b")
        command.append("/e")
        command.append("/h")

        ret = cmd.run_shell(cmds=[" ".join(command)])
        if self.exists(path=r"C:\.pyframe_pipeline_xcopy_exclude.txt"):
            self.rm(path=r"C:\.pyframe_pipeline_xcopy_exclude.txt")
        if ret[0] != 0:
            log.error("xcopy error")
            raise PyframeException(f"xcopy拷贝失败, {src} -> {dst}")

    def cp(self, src: str, dst: str, recursive: bool = False, verbose: bool = True, force: bool = True):
        """
        cp命令
        拷贝目录: cp -rf /data/test1 /data/test2
        拷贝文件: cp -f /data/test1/test.txt /data/test2/test.txt

        Args:
            verbose: 是否显示详细信息
            recursive: 是否递归拷贝
            force: 是否强制拷贝
        """
        log.info(f"src: {src}")
        log.info(f"dst: {dst}")
        if not self.exists(path=src):
            log.error(f"{src} doesn't exist")
            raise PyframeException(f"拷贝失败, {src}不存在")

        if dst.endswith("/"):
            path_mgr.mkdir(dst)
        command = ["\cp"]
        if verbose:
            command.append("-v")
        if recursive:
            command.append("-r")
        if force:
            command.append("-f")
        command.append(src)
        command.append(dst)
        ret = cmd.run_shell(
            cmds=[" ".join(command)],
        )
        if ret[0] != 0:
            log.error(f"{' '.join(command)} error")
            raise PyframeException(f"拷贝失败: {src} -> {dst}")

    def get_file_size(self, file_path: str) -> str:
        """
        获取文件大小
        Args:
            file_path: 文件路径
        Returns:
            文件大小
        """
        if not self.exists(file_path):
            log.error(f"{file_path} doesn't exist")
            raise PyframeException(f"{file_path} doesn't exist")

        file_size = os.path.getsize(file_path)
        if file_size < 1024:
            return str(file_size) + "B"
        elif file_size < 1024 * 1024:
            return str(round(file_size / 1024, 2)) + "KB"
        elif file_size < 1024 * 1024 * 1024:
            return str(round(file_size / 1024 / 1024, 2)) + "MB"
        else:
            return str(round(file_size / 1024 / 1024 / 1024, 2)) + "GB"

    def is_empty_dir(path: Union[str, list, Path]) -> bool:
        """
        判断一个或多个目录是否为空
        Args:
            path: 目录路径
        Returns:
            是否为空
        """
        empty = False
        if isinstance(path, str) or isinstance(path, Path):
            path = Path(path)
            if not path.is_dir():
                log.error(f"{path} is not a dir")
                raise PyframeException(f"{path} is not a dir")
            else:
                return len([path.glob("*")]) == 0
        if isinstance(path, list):
            for i in path:
                if not Path(i).is_dir():
                    log.error(f"{i} is not a dir")
                    raise PyframeException(f"{i} is not a dir")
            if all([len(os.listdir(i)) == 0 for i in path]):
                empty = True
        return empty

    def is_empty_file(path: Union[str, Path]) -> bool:
        """
        判断文件是否为空
        Args:
            path: 文件路径
        Returns:
            是否为空
        """
        if isinstance(path, str) or isinstance(path, Path):
            path = Path(path)
            if not path.is_file():
                log.error(f"{path} is not a file")
                raise PyframeException(f"{path} is not a file")
            else:
                return path.stat().st_size == 0

    def copy_new_file(self, src, dst):
        """
        拷贝新文件
        Args:
            src: 源文件
            dst: 目标文件
        """
        log.info(f"src: {src}, dst: {dst}")
        for item in os.listdir(src):
            src_path = os.path.join(src, item)
            dst_path = os.path.join(dst, item)
            if os.path.isdir(src_path):
                if not os.path.exists(dst_path):
                    os.makedirs(dst_path)

                self.copy_new_file(src_path, dst_path)
            else:
                if not os.path.exists(dst_path):
                    shutil.copy2(src_path, dst_path)
                    log.info(f"copy {src_path} to {dst_path}")


path_mgr = PathMgr()
