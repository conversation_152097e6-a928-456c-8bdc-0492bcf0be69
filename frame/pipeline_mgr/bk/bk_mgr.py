# coding=utf-8
import json
import time

import requests

from frame.env.env import env
from frame.log.log import log


class BkMgr:
    def __init__(self, project_name: str = "技术支持中心-流水线"):
        self.bk_host = "http://**************:21935/"
        self.api_gateway = "api/apigw/v3/projects/"
        self.uri_prefix = self.bk_host + self.api_gateway
        # TODO 个人账号信息后续可以申请公共账号，或者找腾讯要开放接口
        self.username = "<EMAIL>"
        self.password = "Yonghang1980!"
        self.bk_headers = {
            "X-DEVOPS-UID": "<EMAIL>",
            "content-type": "application/json",
        }
        self.project_name = project_name

    def _update_project_code(self):
        if not self.project_name:
            self.project_code = env.get("BK_CI_PROJECT_NAME")
        else:
            self.project_code = self.get_project_code_by_name(self.project_name)

    def stop_current_build(self):
        """
        停止流水线
        """
        project_id = env.get("BK_CI_PROJECT_NAME")
        pipeline_id = env.pipeline.pipeline_id()
        build_id = env.pipeline.build_id()
        url = self.bk_host + f"api/apigw/v3/projects/{project_id}/pipelines/{pipeline_id}/builds/{build_id}/stop"
        log.info(f"url: {url}")
        resp = requests.post(url=url, headers=self.bk_headers)
        log.info(f"resp: {resp}")
        time.sleep(10)

    def login(self):
        """
        登录获取授权信息
        Returns:

        """
        url = "https://bk.h3d.com.cn/login/?c_url=https://bk-devops.h3d.com.cn/console"
        resp = requests.get(url)
        bklogin_csrftoken = resp.cookies.get("bklogin_csrftoken")
        data = {"username": self.username, "password": self.password, "csrfmiddlewaretoken": bklogin_csrftoken}
        cookie = f"bklogin_csrftoken={bklogin_csrftoken}"
        headers = {
            "Cookie": cookie,
            "Referer": "https://bk.h3d.com.cn/login/?c_url=https://bk-devops.h3d.com.cn/console",
        }
        resp = requests.post(url=url, data=data, headers=headers, allow_redirects=False)
        bk_token = resp.cookies.get("bk_token")
        self.bk_headers.update({"Cookie": f"bk_token={bk_token};"})

    def get_all_projects(self) -> list:
        """
        获取所有项目信息
        Returns:

        """
        url = self.uri_prefix
        resp = requests.get(url=url, headers=self.bk_headers).json()
        return resp.get("data", [])

    def get_project_code_by_name(self, project_name: str) -> str:
        """
        根据项目名称获取项目代号
        Args:
            project_name: 项目名称
        Returns:
            str: 项目代号
        """
        projects = self.get_all_projects()
        for project in projects:
            if isinstance(project, dict):
                if project.get("projectName") == project_name:
                    return project.get("projectCode")
            else:
                log.error(f"project: {project} is not dict")
        return ""

    def get_pipeline_id_by_name(self, pipeline_name: str, page: int = 1, page_size: int = 500, sort_type: str = "CREATE_TIME") -> str:
        """
        通过流水线名称获取流水线ID
        Args:
            pipeline_name:
            page:
            page_size:
            sort_type:

        Returns:

        """
        self._update_project_code()
        self.login()  # 部分接口无法使用开放API 需要登录获取权限
        url = (
            "https://bk-devops.h3d.com.cn/ms/process/api/user/pipelines/projects/"
            + f"{self.project_code}/listViewPipelines?viewId=myPipeline&projectId={self.project_code}&page={page}&pageSize={page_size}&sortType={sort_type}"
        )
        log.info(f"通过流水线名称获取流水线ID的url: {url}")
        resp = requests.get(url=url, headers=self.bk_headers).json()
        if resp.get("status") != 0:
            return ""
        records = resp.get("data", {}).get("records", [])
        for record in records:
            if record.get("pipelineName") == pipeline_name:
                return record.get("pipelineId")
        return ""

    def get_pipeline_detail(self, pipeline_id: str) -> dict:
        """
        获取流水线全部内容，包括参数、stage以及task等
        Args:
            pipeline_id:

        Returns:

        """
        self._update_project_code()
        url = self.uri_prefix + f"{self.project_code}/pipelines/{pipeline_id}"
        resp = requests.get(url=url, headers=self.bk_headers).json()
        return resp.get("data", {})

    def get_pipeline_params(self, pipeline_id: str) -> list:
        """
        获取流水线参数
        Args:
            pipeline_id: 流水线ID

        Returns: 参数内容

        """
        data = self.get_pipeline_detail(pipeline_id)
        stages = data.get("stages", [])
        if not stages:
            return []

        stage = stages[0]
        containers = stage.get("containers", [])
        if not containers:
            return []

        container = containers[0]
        params = container.get("params", [])
        return params

    def change_pipeline_name(self, pipeline_id: str, new_name: str) -> bool:
        """
        修改流水线名称
        Args:
            pipeline_id: 流水线ID
            new_name: 流水线新名字

        Returns:

        """
        self._update_project_code()
        data = self.get_pipeline_detail(pipeline_id)
        if not data:
            return False

        data["name"] = new_name
        self.login()  # 部分接口无法使用开放API 需要登录获取权限
        url = f"https://bk-devops.h3d.com.cn/ms/process/api/user/pipelines/{self.project_code}/{pipeline_id}"
        resp = requests.put(url=url, headers=self.bk_headers, data=json.dumps(data)).json()
        return resp.get("data", False)

    def update_pipeline_params(self, pipeline_id: str, new_params: list) -> bool:
        """
        更新流水线参数
        Args:
            pipeline_id: 流水线ID
            new_params: 新的参数，类型为列表，且为全量参数

        Returns:

        """
        self._update_project_code()
        data = self.get_pipeline_detail(pipeline_id)
        if not data:
            return False

        data["stages"][0]["containers"][0]["params"] = new_params
        url = self.uri_prefix + f"{self.project_code}/pipelines/{pipeline_id}"
        resp = requests.put(url=url, headers=self.bk_headers, data=json.dumps(data)).json()
        return resp.get("data", False)

    def get_all_templates(self, page: str = 1, page_size: str = 10) -> dict:
        """
        获取项目下所有模板ID
        Returns:

        """
        self._update_project_code()
        url = self.uri_prefix + f"{self.project_code}/templates?page={page}&pageSize={page_size}"
        resp = requests.get(url=url, headers=self.bk_headers).json()
        return resp.get("data", {})

    def get_template_id_by_name(self, template_name: str) -> str:
        """
        根据template名称获取对应ID
        Args:
            template_name:

        Returns:

        """
        templates = self.get_all_templates()
        models = templates.get("models")
        for model in models:
            if model.get("name") == template_name:
                return model.get("templateId")
        return ""

    def get_template_params(self, template_id: str) -> list:
        """
        获取模板参数
        Args:
            template_id: 模板ID

        Returns:

        """
        self._update_project_code()
        url = self.uri_prefix + f"{self.project_code}/templates/{template_id}"
        resp = requests.get(url, headers=self.bk_headers).json()
        return resp.get("data", {}).get("params", [])

    def get_template_stages(self, template_id: str) -> list:
        """
        获取模板stages
        Args:
            template_id: 模板ID

        Returns:

        """
        self._update_project_code()
        url = self.uri_prefix + f"{self.project_code}/templates/{template_id}"
        resp = requests.get(url, headers=self.bk_headers).json()
        return resp.get("data", {}).get("template", {}).get("stages", [])

    def get_template_name_by_id(self, template_id: str) -> list:
        """
        获取模板名称
        Args:
            template_id: 模板ID

        Returns:

        """
        self._update_project_code()
        url = self.uri_prefix + f"{self.project_code}/templates/{template_id}"
        resp = requests.get(url, headers=self.bk_headers).json()
        return resp.get("data", {}).get("template", {}).get("name", "")

    def update_template_params(self, template_id: str, new_params: list, version_name: str = "init") -> bool:
        """
        更新模板
        Args:
            template_id:
            new_params:
            version_name:

        Returns:

        """
        self._update_project_code()
        stages = self.get_template_stages(template_id)
        name = self.get_template_name_by_id(template_id)
        if stages:
            containers = stages[0].get("containers", [])
            if containers:
                containers[0]["params"] = new_params
                url = self.uri_prefix + f"{self.project_code}/templates/{template_id}?versionName={version_name}"
                data = {"name": name, "stages": stages}
                resp = requests.put(url=url, data=json.dumps(data), headers=self.bk_headers).json()
                return resp.get("data", False)
        return False

    def init_instance(self, template_id: str, version: str, use_template_settings: str = "false"):
        """
        根据模板初始化实例
        Args:
            template_id: 模板ID
            version:
            use_template_settings: 是否使用模板里面的配置，默认否-false

        Returns:

        """
        ...

    def update_instance_params(self, template_id: str = None, params=None, use_template_settings: str = "false") -> bool:
        """
        更新模板实例化的流水线参数
        Args:

            template_id: 模板ID
            params:
            use_template_settings:

        Returns:

        """
        self._update_project_code()
        templates = self.get_all_templates()
        if not templates:
            return False
        models = templates.get("models", [])

        pipeline_ids = []
        version = None
        for model in models:
            if model.get("templateId") == template_id:
                pipeline_ids = model.get("associatePipelines")
                version = model.get("version")
                break
        self.login()  # 部分接口无法使用开放API 需要登录获取权限
        for pipeline_id in pipeline_ids:
            pipeline_id = pipeline_id.get("id")
            url = (
                "https://bk-devops.h3d.com.cn/ms/process/api/user/templateInstances/projects/"
                + f"{self.project_code}/templates/{template_id}/async/update?version={version}&useTemplateSettings={use_template_settings}"
            )
            pipeline_detail = self.get_pipeline_detail(pipeline_id)
            pipeline_name = pipeline_detail.get("name")
            data = [{"pipelineName": pipeline_name, "pipelineId": pipeline_id, "param": params}]
            data = json.dumps(data)
            resp = requests.put(url, data, headers=self.bk_headers).json()
            update_ret = resp.get("data", False)
            log.info(f"pipeline_id: {pipeline_id} update_ret: {update_ret}")
        return True

    def start_pipeline(self, pipeline_id: str, params: dict = None) -> bool:
        """
        启动流水线
        Args:
            pipeline_id: 流水线ID
            params: 流水线启动参数

        Returns:

        """
        self._update_project_code()
        self.login()  # 部分接口无法使用开放API 需要登录获取权限
        url = f"https://bk-devops.h3d.com.cn/ms/process/api/user/builds/{self.project_code}/{pipeline_id}"
        if params:
            data = json.dumps(params)
        else:
            data = json.dumps(params)
        resp = requests.put(url=url, headers=self.bk_headers, data=data).json()
        return resp.get("data", False)


bk_mgr = BkMgr()
