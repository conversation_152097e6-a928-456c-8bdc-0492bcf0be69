# coding=utf-8
import re
import time
from api4je<PERSON>ins import <PERSON>
from frame.env.env import env
from frame.log.log import log
from frame.common.common import common
import jenkins
from jenkins import JenkinsException
from urllib.parse import urlparse


class JenkinsMgr:
    def __init__(self):
        pass

    def connect(self, url: str, username: str, password: str):
        self.url = url
        self.username = username
        self.password = password
        self.jksrv = jenkins.<PERSON>(url=url, username=username, password=password)

    def stop_current_build(self, username: str = None, password: str = None):
        """
        停止流水线的当前构建
        """
        url = env.get("JENKINS_URL")
        if username is None and password is None:
            jen<PERSON> = <PERSON>(url=url)
        else:
            jen<PERSON> = <PERSON>(url=url, auth=(username, password))

        job_name = env.pipeline.job_name()
        job = jenkins.get_job(job_name)
        build_number = env.pipeline.build_num()
        build = job.get_build(int(build_number))
        build.stop()
        log.info(f"abort build {env.pipeline.build_url()}")
        time.sleep(10)

    def delete_current_build(self, username: str = None, password: str = None):
        """
        删除流水线的当前构建
        """
        url = env.get("JENKINS_URL")
        if username is None and password is None:
            jenkins = Jenkins(url=url)
        else:
            jenkins = Jenkins(url=url, auth=(username, password))
        job_name = env.pipeline.job_name()
        job = jenkins.get_job(full_name=job_name)
        number = env.pipeline.build_num()
        build = job.get_build(number)
        log.info(f"delete build {env.pipeline.build_url()}")
        build.delete()

    def get_running_builds(self):
        """
        获取正在运行的构建
        """
        builds = []
        nodes = self.jksrv.get_nodes()
        for node in nodes:
            # the name returned is not the name to lookup when
            # dealing with master :/
            if node["name"] == "master" or node["name"] == "Built-In Node":
                node_name = "(master)"
            else:
                node_name = node["name"]
            try:
                info = self.jksrv.get_node_info(node_name, depth=2)
            except JenkinsException as e:
                # Jenkins may 500 on depth >0. If the node info comes back
                # at depth 0 treat it as a node not running any jobs.
                if "[500]" in str(e) and self.jksrv.get_node_info(node_name, depth=0):
                    continue
                else:
                    raise
            for executor in info["executors"]:
                executable = executor["currentExecutable"]
                if executable and "number" in executable:
                    executor_number = executor["number"]
                    build_number = executable["number"]
                    url = executable["url"]
                    m = re.search(r"/job/([^/]+)/.*", urlparse(url).path)
                    job_name = m.group(1)
                    builds.append(
                        {
                            "name": job_name,
                            "number": build_number,
                            "url": url,
                            "node": node_name,
                            "executor": executor_number,
                        }
                    )
        return builds

    def get_timeout_builds(self, timeout: int = 3600) -> list:
        """
        获取超时的构建
        Args:
            timeout: 超时时间，单位秒
        Returns:
            list: 超时的构建列表
        """
        base_url = common.get_base_url(self.url)

        running_builds = self.get_running_builds()
        ret = []
        for build in running_builds:
            # 匹配http://jenkins-x5mobile.h3d.com.cn/job/art_package/job/timeline/40047/中的art_package和timeline
            build_url, node_name, build_number = build["url"], build["node"], build["number"]
            names = []
            for sub in re.finditer(pattern="job/", string=build_url):
                index = build_url.find("/", sub.end())
                names.append(build_url[sub.end() : index])
            job_name = "/".join(names)
            log.info(job_name)
            try:
                build_info = self.jksrv.get_build_info(job_name, build_number)
                # log.info(build_info)
                duration = time.time() - (build_info["timestamp"] / 1000)  # 秒
                log.info(f"duration: {duration}second")
                full_display_name = build_info["fullDisplayName"]
                log.info(f"duration: {duration}, timeout: {timeout}")
                if duration > timeout:
                    ret.append(
                        {
                            "job_name": job_name,
                            "build_number": build_number,
                            "full_display_name": full_display_name,
                            "duration": duration,
                            "build_url": build_url,
                            "node_name": node_name,
                            "node_url": common.join_url(base_url, "computer", node_name),
                        }
                    )
            except Exception as e:
                log.error(e)
        log.info(ret)
        return ret


jenkins_mgr = JenkinsMgr()
