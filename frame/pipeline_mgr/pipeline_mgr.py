# coding=utf-8
from frame.env.env import env
from frame.exception.exception import PyframeException
from frame.pipeline_mgr.bk.bk_mgr import bk_mgr
from frame.pipeline_mgr.jenkins.jenkins_mgr import jenkins_mgr
from frame.pipeline_mgr import JENKINS_AUTH


class PipelineMgr:
    def __init__(self):
        pass

    def is_jenkins(self) -> bool:
        """
        判断是否是jenkins
        """
        ret = False
        if env.get("PIPELINE_NAME") is not None:
            ret = True
        return ret

    def is_bk(self) -> bool:
        """
        判断是否是蓝盾
        """
        ret = False
        if env.get("BK_CI_PIPELINE_ID") is not None:
            ret = True
        return ret

    def _get_jenkins_user_and_pwd(self) -> tuple:
        # 拆分协议，然后补充为 `http://`
        _jenkins_url = env.get("JENKINS_URL")
        if not _jenkins_url:
            return None, None
        _url = f"http://{_jenkins_url.split('//')[-1].split('/')[0]}"
        if _url in JENKINS_AUTH:
            username = JENKINS_AUTH[_url]["username"]
            password = JENKINS_AUTH[_url]["password"]
            return username, password
        return None, None

    def stop_current_build(self, username: str = "<EMAIL>", password: str = "hmvvPOFjMwn3aEaIoqjU42paLd3EMCeJ"):
        """
        停止流水线的当前构建
        """
        # NOTE: 为防止有匹配不上的`jenkins`服务, 默认仍然使用******************/Lang5208作为用户名密码
        if self.is_jenkins():
            try:
                _username, _password = self._get_jenkins_user_and_pwd()
                # 停止当前构建
                jenkins_mgr.stop_current_build(username=_username or username, password=_password or password)
            except Exception as e:
                raise PyframeException(f"停止当前构建失败, 异常: {e}")
        elif self.is_bk():
            try:
                bk_mgr.stop_current_build()
            except Exception as e:
                raise PyframeException(f"停止当前构建失败, 异常: {e}")
        else:
            raise PyframeException("主动停止当前构建")

    # def delete_current_build(self, username: str = "<EMAIL>", password: str = "lang5208"):
    #     """
    #     删除流水线的当前构建，可能会导致jenkins服务器问题，暂不使用。
    #     """
    #     if self.is_jenkins():
    #         jenkins_mgr.delete_current_build(username=username, password=password)
    #     elif self.is_bk():
    #         pass
    #     else:
    #         raise PyframeException("主动删除当前构建")


pipeline_mgr = PipelineMgr()
