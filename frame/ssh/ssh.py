import paramiko


class Ssh:
    def __init__(self, host: str = "", port: int = 22, username: str = "root", password: str = ""):
        """
        Args:
            host: 目标机器的ip
            port: 目标机器的端口
            username: 用户名
            password: 密码
        """
        self.__host = host
        self.__port = port
        self.__username = username
        self.__password = password
        # self.__transport = paramiko.Transport((self.__host, self.__port))
        # self.__transport.connect(username=self.__username, password=self.__password)
        # self.ssh = paramiko.SSHClient()
        # self.ssh._transport = self.__transport

    def __del__(self):
        # self.ssh.transport.close()
        pass

    def connect(self, host: str, port: int = 22, username: str = "root", password: str = ""):
        """
        ssh连接

        Args:
            host: 目标机器的ip
            port: 目标机器的端口
            username: 用户名
            password: 密码

        Returns:

        """
        self.__host = host
        self.__port = port
        self.__username = username
        self.__password = password
        self.__transport = paramiko.Transport((self.__host, self.__port))
        self.__transport.connect(username=self.__username, password=self.__password)
        self.ssh = paramiko.SSHClient()
        self.ssh._transport = self.__transport

    def close(self):
        self.ssh.transport.close()

    def execute(self, cmd: str):
        stdin, stdout, stderr = self.ssh.exec_command(cmd)
        # print(stdout.read().decode('utf-8'))
        return stdin, stdout, stderr


# ssh = Ssh()
