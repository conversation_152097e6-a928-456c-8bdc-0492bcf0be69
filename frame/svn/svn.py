# coding=utf-8
from frame.cmd.cmd import cmd
from frame.exception.exception import PyframeException
from frame.log.log import log


class Svn:
    def __init__(self, url: str, username: str, password: str, root: str, target_path: str = "."):
        """
        svn
        Args:
            url: svn url
            username: svn username
            password: svn password
            root: svn root
            target_path: svn target path
        """
        self._url = url
        self._username = username
        self._password = password
        self._root = root
        self._target_path = target_path

    def checkout(self, revision: str = "HEAD", force: bool = False, depth: str = "infinity", quiet: bool = False):
        """
        svn checkout
        Args:
            revision: 版本号
                NUMBER       revision number3
                '{' DATE '}' revision at start of the date
                'HEAD'       latest in repository
                'BASE'       base rev of item's working copy
                'COMMITTED'  last commit at or before BASE
                'PREV'       revision just before COMMITTED
            force: 是否强制拉取
            depth: 拉取深度
            quiet: 是否静默
        """
        commands = ["svn", "checkout"]
        commands.append("--username")
        commands.append(self._username)
        commands.append("--password")
        commands.append(self._password)
        commands.append("--revision")
        commands.append(revision)
        if force:
            commands.append("--force")
        commands.append(self._url)
        if self._target_path != ".":
            commands.append(self._target_path)
        if depth not in ["infinity", "empty", "files", "immediates"]:
            log.error(f"svn checkout depth: {depth} is invalid")
            raise PyframeException(f"svn checkout depth: {depth} is invalid")
        else:
            commands.append("--depth")
            commands.append(depth)
        if quiet:
            commands.append("--quiet")

        ret = cmd.run_shell(cmds=[" ".join(commands)], workdir=self._root, return_output=True)
        if ret[0] != 0:
            log.error(f"svn checkout return {ret[0]}, hint: {ret[1]}")
            raise PyframeException(f"拉取svn代码失败, 返回值: {ret[0]}")

    def list(self, url: str):
        """
        svn list
        """
        commands = ["svn", "list"]
        commands.append(url)
        commands.append("--username")
        commands.append(self._username)
        commands.append("--password")
        commands.append(self._password)
        ret = cmd.run_shell(cmds=[" ".join(commands)], return_output=True)
        if ret[0] != 0:
            log.error(f"svn list return {ret[0]}, hint: {ret[1]}")
            raise PyframeException(f"svn list失败, 返回值: {ret[0]}")
        return ret[1]
