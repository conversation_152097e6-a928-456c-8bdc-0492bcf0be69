# coding = utf-8
import requests

from frame.log.log import log


class Multicast:
    def __init__(self):
        pass

    def send_multicast_msg(self, webhook: str, content: str, markdown: bool = True, mentioned_list: list = None) -> tuple:
        """
        发送群通知，多播

        Args:
            webhook: 机器人链接
            content: 消息内容
            markdown: True支持markdown格式消息，False支持text格式消息
            mentioned_list: @消息通知者，如["<EMAIL>"]，如果@所有人，则["all"]
        Returns:
            (int, str): 状态码，机器人返回的内容提示
        """
        if mentioned_list is None:
            mentioned_list = []

        content = content.rstrip("\n")

        headers = {
            "Content-Type": "application/json",
        }
        if markdown:
            for user in mentioned_list:
                content += "<@{}>".format(user)

            # 群通知长度限制4096
            # utf8字节长度设置为4008, 是1, 2, 3, 4的整数倍, 防止decode时失败
            content_bytes = content.encode("utf-8")
            if len(content_bytes) > 4008:
                content = content_bytes[:4008].decode("utf-8", errors="ignore")
                content += "\n...消息过长，已截断"
            log.debug(f"len(content_bytes)={len(content_bytes)}")
            json_data = {"msgtype": "markdown", "markdown": {"content": content}}
        else:
            # 群通知长度限制4096
            # utf8字节长度设置为4008, 是1, 2, 3, 4的整数倍, 防止decode时失败
            content_bytes = content.encode("utf-8")
            if len(content_bytes) > 4008:
                content = content_bytes[:4008].decode("utf-8")
                content += "\n...消息过长，已截断"
            log.debug(f"len(content)={len(content)}")
            json_data = {"msgtype": "text", "text": {"content": content, "mentioned_list": mentioned_list}}
        response = requests.post(webhook, headers=headers, json=json_data)
        log.info("send wechat msg: {} to {}, return status_code: {}, text: {}".format(json_data, webhook, response.status_code, response.text))
        return response.status_code, response.text


multicast = Multicast()
