# coding = utf-8
import json

import pymongo
import requests

from frame.log.log import log

"""
个人消息通知【流水线通知】应用
    企业ID：wwc538db03872b3de8
    AgentId：1000069
    Secret：QAOZ7JfXAQmsm6G16dg98vtvRn5H45DRxBHE2_gg8sk

个人消息通知【流水线异常通知】应用
    企业ID：wwc538db03872b3de8
    AgentId：1000073
    Secret：yVOgA7H9y3JHOG1oAANCVQMaDUSELoLFOP9yCfp7TZ4

获取access_token： curl "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=wwc538db03872b3de8&corpsecret=QAOZ7JfXAQmsm6G16dg98vtvRn5H45DRxBHE2_gg8sk"
获取access_token文档: https://developer.work.weixin.qq.com/document/path/91039
发送应用通知文档：https://developer.work.weixin.qq.com/document/path/90236
"""


class Unicast:
    def __init__(self):
        self.access_token = ""
        self.abnormal_access_token = ""
        self.mongo_ip = "************"
        self.mongo_port = 27017
        self.client = pymongo.MongoClient(f"mongodb://{self.mongo_ip}:{self.mongo_port}/")
        self.database = self.client["wechat"]
        self.collection = self.database["access_token"]
        self.__read_token()
        self.__read_abnormal_token()

    def send_unicast_msg(self, user_list: list, content: str, markdown: bool = True) -> tuple:
        """
        发送个人通知（单播消息，流水线通知），支持markdown和text两种格式

        Doc:
            https://developer.work.weixin.qq.com/document/path/90236

        Args:
            user_list: 用户列表，如["<EMAIL>"]
            content: 消息内容
            markdown: True支持markdown格式消息，False支持text格式消息
        Returns:
            (int, str): 状态码，机器人返回的内容提示
        """
        to_user = "|".join(user_list)
        if not to_user:
            to_user = None

        content = content.rstrip("\n")
        content = content.rstrip("\n")
        content_bytes = content.encode("utf-8")
        if len(content_bytes) > 4008:
            content = content_bytes[:4008].decode("utf-8")
            content += "\n...消息过长，已截断"
        log.debug(f"len(content)={len(content)}")

        if markdown:
            json_data = {
                "touser": to_user,
                "msgtype": "markdown",
                "markdown": {"content": content},
                "agentid": 1000069,
                "enable_duplicate_check": 0,
            }
        else:
            json_data = {
                "touser": to_user,
                "msgtype": "text",
                "text": {"content": content},
                "agentid": 1000069,
                "enable_duplicate_check": 0,
            }
        headers = {
            "Content-Type": "application/json",
        }
        count = 0
        while True:
            count += 1
            webhook = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.access_token}"
            response = requests.post(webhook, headers=headers, json=json_data, verify=False)
            log.info("send wechat msg: {} to {}, return status_code: {}, text: {}".format(json_data, user_list, response.status_code, response.text))
            resp = json.loads(response.text)
            if count > 3:
                raise Exception("send msg exceed 3 times")
            if resp["errcode"] == 0 or resp["errcode"] == "0":
                log.info("errcode == 0, break")
                break
            else:
                self.__get_token_from_tencent()
                self.__write_token()

        return response.status_code, response.text

    def send_abnormal_unicast_msg(self, user_list: list, content: str, markdown: bool = True) -> tuple:
        """
        发送个人通知（单播消息，流水线异常），支持markdown和text两种格式

        Doc:
            https://developer.work.weixin.qq.com/document/path/90236

        Args:
            user_list: 用户列表，如["<EMAIL>"]
            content: 消息内容
            markdown: True支持markdown格式消息，False支持text格式消息
        Returns:
            (int, str): 状态码，机器人返回的内容提示
        """
        to_user = "|".join(user_list)
        if not to_user:
            to_user = None

        content = content.rstrip("\n")
        content_bytes = content.encode("utf-8")
        if len(content_bytes) > 4008:
            content = content_bytes[:4008].decode("utf-8")
            content += "\n...消息过长，已截断"
        log.debug(f"len(content)={len(content)}")

        if markdown:
            json_data = {
                "touser": to_user,
                "msgtype": "markdown",
                "markdown": {"content": content},
                "agentid": 1000073,
                "enable_duplicate_check": 0,
            }
        else:
            json_data = {
                "touser": to_user,
                "msgtype": "text",
                "text": {"content": content},
                "agentid": 1000073,
                "enable_duplicate_check": 0,
            }
        headers = {
            "Content-Type": "application/json",
        }
        count = 0
        while True:
            count += 1
            webhook = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.abnormal_access_token}"
            response = requests.post(webhook, headers=headers, json=json_data, verify=False)
            log.info("send wechat msg: {} to {}, return status_code: {}, text: {}".format(json_data, user_list, response.status_code, response.text))
            resp = json.loads(response.text)
            print(resp)
            if count > 3:
                raise Exception("send msg exceed 3 times")
            if resp["errcode"] == 0:
                break
            else:
                self.__get_abnormal_token_from_tencent()
                self.__write_abnormal_token()

        return response.status_code, response.text

    def __get_token_from_tencent(self):
        """
        从腾讯获取单播的access_token，流水线通知

        Returns:
        """
        log.info("__get_token_from_tencent")
        params = (
            ("corpid", "wwc538db03872b3de8"),
            ("corpsecret", "QAOZ7JfXAQmsm6G16dg98vtvRn5H45DRxBHE2_gg8sk"),
        )

        response = requests.get("https://qyapi.weixin.qq.com/cgi-bin/gettoken", params=params)
        resp = json.loads(response.text)
        if resp["errcode"] == 0:
            self.access_token = resp["access_token"]

    def __get_abnormal_token_from_tencent(self):
        """
        从腾讯获取单播的access_token，流水线异常通知

        Returns:
        """
        params = (
            ("corpid", "wwc538db03872b3de8"),
            ("corpsecret", "yVOgA7H9y3JHOG1oAANCVQMaDUSELoLFOP9yCfp7TZ4"),
        )

        response = requests.get("https://qyapi.weixin.qq.com/cgi-bin/gettoken", params=params)
        resp = json.loads(response.text)
        if resp["errcode"] == 0:
            self.abnormal_access_token = resp["access_token"]
            log.info(self.abnormal_access_token)

    def __read_token(self):
        """
        从数据库读取单播的access_token，流水线通知
        """
        ret = self.collection.find_one({"_id": "access_token_id"})
        if ret is None:
            self.__get_token_from_tencent()
            self.collection.insert_one(
                {
                    "_id": "access_token_id",
                    "access_token": self.access_token,
                }
            )
        else:
            self.access_token = ret["access_token"]

    def __read_abnormal_token(self):
        """
        从数据库读取单播的access_token，流水线异常通知
        """
        ret = self.collection.find_one(
            {
                "_id": "abnormal_access_token_id",
            }
        )
        if ret is None:
            self.__get_abnormal_token_from_tencent()
            self.collection.insert_one(
                {
                    "_id": "abnormal_access_token_id",
                    "abnormal_access_token": self.access_token,
                }
            )
        else:
            self.abnormal_access_token = ret["abnormal_access_token"]

    def __write_token(self):
        """
        向数据库写入单播的access_token，流水线通知
        """
        log.info("__write_token")
        self.collection.update_one(
            {
                "_id": "access_token_id",
            },
            {
                "$set": {"access_token": self.access_token},
            },
        )

    def __write_abnormal_token(self):
        """
        向数据库写入单播的access_token，流水线异常通知
        """
        log.info("__write_abnormal_token")
        self.collection.update_one(
            {
                "_id": "abnormal_access_token_id",
            },
            {
                "$set": {"abnormal_access_token": self.abnormal_access_token},
            },
        )


unicast = Unicast()
