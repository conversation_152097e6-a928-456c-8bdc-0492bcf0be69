# coding = utf-8
from enum import Enum

from frame import MAINTAINER_LIST, PROJECT, MAINTAINER_ON_CALL_LIST
from frame.common.common import common
from frame.env.env import env
from frame.log.log import log
from frame.wechat.multicast import multicast
from frame.wechat.unicast import unicast

# [拯救流水线机器人] 用于在流水线执行失败时，第一时间发送消息给流水线运维团队
RESCUE_PIPELINE_WEBHOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=21472abe-94a1-45e5-a8e3-4b64b8f9dc02"


class PipelineStatus(Enum):
    """
    流水线的执行状态
    """

    SUCCESS = 1  # 成功
    FAILURE = 2  # 失败
    UNSTABLE = 3  # 不稳定
    CANCELED = 4  # 取消
    START = 5  # 开始
    DEBUG = 6  # 调试


class Wechat:
    def __init__(self):
        pass

    def send_multicast_msg(self, webhook: str, content: str, markdown: bool = True, mentioned_list: list = None) -> tuple:
        """
        发送群通知，多播

        Args:
            webhook: 机器人链接
            content: 消息内容
            markdown: True支持markdown格式消息，False支持text格式消息
            mentioned_list: @消息通知者，如["<EMAIL>"]，如果@所有人，则["all"]
        Returns:
            (int, str): 状态码，机器人返回的内容提示
        """
        status_code, text = multicast.send_multicast_msg(
            webhook=webhook,
            content=content,
            markdown=markdown,
            mentioned_list=mentioned_list,
        )
        return status_code, text

    def send_unicast_msg(self, user_list: list, content: str, markdown: bool = True) -> tuple:
        """
        发送个人通知（单播消息），支持markdown和text两种格式

        Args:
            user_list: 用户列表，如["<EMAIL>"]
            content: 消息内容
            markdown: True支持markdown格式消息，False支持text格式消息
        Returns:
            (int, str): 状态码，机器人返回的内容提示
        """
        status_code, text = unicast.send_unicast_msg(
            user_list=user_list,
            content=content,
            markdown=markdown,
        )
        return status_code, text

    def send_abnormal_unicast_msg(self, user_list: list, content: str, markdown: bool = True, add_maintainer: bool = True) -> tuple:
        """
        发送异常通知，单播

        Args:
            content: 消息内容
            markdown: True支持markdown格式消息，False支持text格式消息
        Returns:
            (int, str): 状态码，机器人返回的内容提示
        """
        if add_maintainer:
            user_list = list(set(MAINTAINER_LIST + user_list))
        status_code, text = unicast.send_abnormal_unicast_msg(
            user_list=user_list,
            content=content,
            markdown=markdown,
        )
        return status_code, text

    def send_multicast_post_success(self, webhook: str, content: str = "", mentioned_list: list = None):
        """
        成功后通知，群通知

        Args:
            webhook: 机器人链接
            content: 消息内容
            mentioned_list: @消息通知者，如["<EMAIL>"]，如果@所有人，则["all"]
        """
        prefix_msg = self.__get_template(status=PipelineStatus.SUCCESS)
        msg = prefix_msg + content
        self.send_multicast_msg(
            webhook=webhook,
            content=msg,
            markdown=True,
            mentioned_list=mentioned_list,
        )

    def send_multicast_post_failure(self, webhook: str, content: str = "", mentioned_list: list = [], rescue: bool = True):
        """
        失败后通知，群通知

        Args:
            webhook: 机器人链接
            content: 消息内容
            mentioned_list: @消息通知者，如["<EMAIL>"]，如果@所有人，则["all"]
            rescue: 是否通过企业微信的流水线失败报警群发送消息给流水线运维团队，默认发送
        """
        prefix_msg = self.__get_template(status=PipelineStatus.FAILURE)
        msg = prefix_msg + content

        # 处理异常自定义的@人员
        failure_mentioned_list = env.get_failure_mentioned_list()
        if failure_mentioned_list:
            mentioned_list = list(set(mentioned_list + failure_mentioned_list))

        self.send_multicast_msg(
            webhook=webhook,
            content=msg,
            markdown=True,
            mentioned_list=mentioned_list,
        )
        mentioned_list.extend(MAINTAINER_ON_CALL_LIST)
        if rescue:
            self.send_multicast_msg(
                webhook=RESCUE_PIPELINE_WEBHOOK,
                content=msg,
                markdown=True,
                mentioned_list=mentioned_list,
            )
            unicast.send_abnormal_unicast_msg(
                user_list=MAINTAINER_LIST,
                content=msg,
                markdown=True,
            )

    def send_multicast_post_unstable(self, webhook: str, content: str = "", mentioned_list: list = None):
        """
        不稳定后通知，群通知

        Args:
            webhook: 机器人链接
            content: 消息内容
            mentioned_list: @消息通知者，如["<EMAIL>"]，如果@所有人，则["all"]
        """
        prefix_msg = self.__get_template(status=PipelineStatus.UNSTABLE)
        msg = prefix_msg + content
        self.send_multicast_msg(
            webhook=webhook,
            content=msg,
            markdown=True,
            mentioned_list=mentioned_list,
        )

    def send_multicast_post_canceled(self, webhook: str, content: str = "", mentioned_list: list = None):
        """
        手动取消后通知，群通知

        Args:
            webhook: 机器人链接
            content: 消息内容
            mentioned_list: @消息通知者，如["<EMAIL>"]，如果@所有人，则["all"]
        """
        prefix_msg = self.__get_template(status=PipelineStatus.CANCELED)
        msg = prefix_msg + content
        self.send_multicast_msg(
            webhook=webhook,
            content=msg,
            markdown=True,
            mentioned_list=mentioned_list,
        )
    
    def send_multicast_post_debug(self, webhook: str, content: str = "", mentioned_list: list = None):
        """
        手动取消后通知，群通知

        Args:
            webhook: 机器人链接
            content: 消息内容
            mentioned_list: @消息通知者，如["<EMAIL>"]，如果@所有人，则["all"]
        """
        prefix_msg = self.__get_template(status=PipelineStatus.DEBUG)
        msg = prefix_msg + content
        # if not mentioned_list:
        #     mentioned_list = [env.pipeline.pipeline_operator_email()]
        # elif env.pipeline.pipeline_operator_email() not in mentioned_list:
        #     mentioned_list.append(env.pipeline.pipeline_operator_email())
        self.send_multicast_msg(
            webhook=webhook,
            content=msg,
            markdown=True,
            mentioned_list=mentioned_list,
        )

    def __get_template(self, status: PipelineStatus) -> str:
        """
        根据状态获取不同的消息模板

        Args:
            status: 流水线的状态，成功，失败，不稳定，取消
        """
        prefix_msg = ""
        if status == PipelineStatus.SUCCESS:
            # prefix_msg = "# <font color=#16982b>【{}】{}-{} 成功</font>\n"
            prefix_msg = "# <font color='info'>【{}】{}-{} 成功</font>\n"
        elif status == PipelineStatus.FAILURE:
            # prefix_msg = "# <font color=#dc143c>【{}】{}-{} 失败</font>\n"
            prefix_msg = "# <font color='warning'>【{}】{}-{} 失败</font>\n"
        elif status == PipelineStatus.UNSTABLE:
            # prefix_msg = "# <font color=#edb879>【{}】{}-{} 不稳定</font>\n"
            prefix_msg = "# <font color='comment'>【{}】{}-{} 不稳定</font>\n"
        elif status == PipelineStatus.CANCELED:
            # prefix_msg = "# <font color=#edb879>【{}】{}-{} 取消</font>\n"
            prefix_msg = "# <font color='comment'>【{}】{}-{} 取消</font>\n"
        elif status == PipelineStatus.START:
            # prefix_msg = "# <font color=black>【{}】{}-{} 开始</font>\n"
            prefix_msg = "# <font color='comment'>【{}】{}-{} 开始</font>\n"
        elif status == PipelineStatus.DEBUG:
            # prefix_msg = "# <font color=black>【{}】{}-{} 调试</font>\n"
            prefix_msg = "# <font color='comment'>【{}】{}-{} 调试</font>\n"
        pipeline_name = env.pipeline.pipeline_name()
        build_num = env.pipeline.build_num()
        prefix_msg = prefix_msg.format(PROJECT, pipeline_name, build_num)

        build_ip = common.get_host_ip()
        prefix_msg += f"**机器:** {build_ip}\n"

        operator = env.pipeline.pipeline_operator()
        if len(operator) != 0:
            prefix_msg += f"**触发:** {operator}\n"

        duration = env.pipeline.build_duration()
        if len(duration) != 0:
            prefix_msg += f"**耗时:** {duration}\n"

        url = env.pipeline.build_url()
        if len(url) != 0:
            prefix_msg += f"**详情:** [流水线日志]({url})\n"

        # prefix_msg += f"**磁盘剩余:** {common.get_disk_free_size()}G\n"
        prefix_msg += f"**时间:** {common.get_ftime()}\n"

        if status != PipelineStatus.SUCCESS:
            # 错误提示
            failure_msg = env.get("PYFRAME_EXCEPTION_MESSAGE")
            if failure_msg is not None:
                # 失败步骤
                temp_stage = env.get("PYFRAME_FAILURE_STAGE")
                failure_stage = temp_stage if temp_stage is not None else env.get("PYFRAME_STAGE")
                if failure_stage is not None:
                    prefix_msg += f"**失败步骤:** <font color='warning'>{failure_stage}</font>\n"
                    # env.set({"PYFRAME_STAGE": None, "PYFRAME_EXCEPTION_STAGE_BAK": failure_stage})
                    env.set({"PYFRAME_EXCEPTION_STAGE_BAK": failure_stage})
                prefix_msg += f"**错误提示:** <font color='warning'>{failure_msg}</font>\n"
                # env.set({"PYFRAME_EXCEPTION_MESSAGE": None, "PYFRAME_EXCEPTION_MESSAGE_BAK": failure_msg})
                env.set({"PYFRAME_EXCEPTION_MESSAGE_BAK": failure_msg})
        if status == PipelineStatus.DEBUG:
            prefix_msg += f"**调试信息:** \n"
        return prefix_msg

    def send_unicast_on_start(self, user_list: list = None, content: str = ""):
        """
        成功后通知，个人通知

        Args:
            user_list: 用户列表
            content: 消息内容
        """
        if user_list is None:
            user_list = []
        prefix_msg = self.__get_template(status=PipelineStatus.START)
        msg = prefix_msg + content
        pipeline_operator = env.pipeline.pipeline_operator()
        if pipeline_operator.endswith("h3d.com.cn"):
            user_list.append(env.pipeline.pipeline_operator())
        else:
            operator_email = env.pipeline.pipeline_operator_email()
            if operator_email.endswith("h3d.com.cn"):
                user_list.append(operator_email)
            else:
                log.warn("pipeline operator is not end with h3d.com.cn")
        user_list = list(set(user_list).union(set(MAINTAINER_LIST)))
        self.send_unicast_msg(
            user_list=user_list,
            content=msg,
            markdown=True,
        )

    def send_unicast_post_success(self, user_list: list = None, content: str = ""):
        """
        成功后通知，个人通知

        Args:
            user_list: 用户列表
            content: 消息内容
        """
        if user_list is None:
            user_list = []
        prefix_msg = self.__get_template(status=PipelineStatus.SUCCESS)
        msg = prefix_msg + content
        pipeline_operator = env.pipeline.pipeline_operator()
        log.debug(f"pipeline_operator : {pipeline_operator}")
        if pipeline_operator.endswith("h3d.com.cn"):
            user_list.append(env.pipeline.pipeline_operator())
        else:
            operator_email = env.pipeline.pipeline_operator_email()
            log.debug(f"operator_email: {operator_email}")
            if operator_email.endswith("h3d.com.cn"):
                user_list.append(operator_email)
            else:
                log.warn("pipeline operator is not end with h3d.com.cn")
        user_list = list(set(user_list).union(set(MAINTAINER_LIST)))
        log.debug(user_list)
        status_code, text = self.send_unicast_msg(
            user_list=user_list,
            content=msg,
            markdown=True,
        )
        log.debug("status_code: {}, text: {}".format(status_code, text))

    def send_unicast_post_failure(self, user_list: list = None, content: str = "", rescue: bool = True, to_admin: bool = True):
        """
        失败后通知，个人通知

        Args:
            user_list: 用户列表
            content: 消息内容
            rescue: 是否通过企业微信的流水线失败报警群发送消息给流水线运维团队，默认发送
            to_admin: 是否通过企业微信的流水线异常通知窗口发送消息给管理员，默认发送
        """
        if user_list is None:
            user_list = []
        prefix_msg = self.__get_template(status=PipelineStatus.FAILURE)
        msg = prefix_msg + content
        pipeline_operator = env.pipeline.pipeline_operator()
        if pipeline_operator.endswith("h3d.com.cn"):
            user_list.append(env.pipeline.pipeline_operator())
        else:
            operator_email = env.pipeline.pipeline_operator_email()
            if operator_email.endswith("h3d.com.cn"):
                user_list.append(operator_email)
            else:
                log.warn("pipeline operator is not end with h3d.com.cn")

        # 流水线团队
        if to_admin:
            user_list = list(set(user_list).union(set(MAINTAINER_LIST)))
        else:
            user_list = list(set(user_list))

        # 处理异常时指定的人
        failure_mentioned_list = env.get_failure_mentioned_list()
        if failure_mentioned_list:
            user_list = list(set(user_list + failure_mentioned_list))

        unicast.send_abnormal_unicast_msg(
            user_list=user_list,
            content=msg,
            markdown=True,
        )
        if rescue:
            self.send_multicast_msg(
                webhook=RESCUE_PIPELINE_WEBHOOK,
                content=msg,
                markdown=True,
                mentioned_list=MAINTAINER_ON_CALL_LIST,
            )

    def send_unicast_post_unstable(self, user_list: list = None, content: str = ""):
        """
        不稳定后通知，个人通知

        Args:
            user_list: 用户列表
            content: 消息内容
        """
        if user_list is None:
            user_list = []
        prefix_msg = self.__get_template(status=PipelineStatus.UNSTABLE)
        msg = prefix_msg + content
        pipeline_operator = env.pipeline.pipeline_operator()
        if pipeline_operator.endswith("h3d.com.cn"):
            user_list.append(env.pipeline.pipeline_operator())
        else:
            operator_email = env.pipeline.pipeline_operator_email()
            if operator_email.endswith("h3d.com.cn"):
                user_list.append(operator_email)
            else:
                log.warn("pipeline operator is not end with h3d.com.cn")
        user_list = list(set(user_list).union(set(MAINTAINER_LIST)))
        self.send_unicast_msg(
            user_list=user_list,
            content=msg,
            markdown=True,
        )

    def send_unicast_post_canceled(self, user_list: list = None, content: str = ""):
        """
        不稳定后通知，个人通知

        Args:
            user_list: 用户列表
            content: 消息内容
        """
        if user_list is None:
            user_list = []
        prefix_msg = self.__get_template(status=PipelineStatus.CANCELED)
        msg = prefix_msg + content
        pipeline_operator = env.pipeline.pipeline_operator()
        if pipeline_operator.endswith("h3d.com.cn"):
            user_list.append(env.pipeline.pipeline_operator())
        else:
            operator_email = env.pipeline.pipeline_operator_email()
            if operator_email.endswith("h3d.com.cn"):
                user_list.append(operator_email)
            else:
                log.warn("pipeline operator is not end with h3d.com.cn")
        user_list = list(set(user_list).union(set(MAINTAINER_LIST)))
        self.send_unicast_msg(
            user_list=user_list,
            content=msg,
            markdown=True,
        )


wechat = Wechat()
