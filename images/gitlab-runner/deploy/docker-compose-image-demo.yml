version: '3'
services:
  gitlab-runner-pyframe:
    image: registry.h3d.com.cn/dev-productivity/gitlab-runner:python3.9.12
    environment:
      - GITLAB_CI_URL=https://gitlab.h3d.com.cn/
      - GITLAB_CI_TOKEN=6ZG4QEFSB7v7MLPnyPn-
      - GITLAB_RUNNER_NAME=192.168.12.248
      - GITLAB_RUNNER_TAG_LIST=full_codecheck,incr_codecheck
      - GITLAB_RUNNER_CONCURRENT=4
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      # - ./builds:/builds
