version: '3'
services:
  jenkins_team:
    image: "registry.h3d.com.cn/dev-productivity/jenkins:2.375.1-lts-centos7"
    ports:
      - '172.17.60.103:80:8080'
      - '172.17.60.103:50000:50000'
    volumes:
      - /data/jenkins/data_tac_team:/var/jenkins_home
    environment:
      TZ: "Asia/Shanghai"
      LANG: "en_US.utf8"
      JAVA_OPTS: "-server -Xms6G -Xmx12G -Dsun.jnu.encoding=UTF-8 -Dfile.encoding=UTF-8 -Dorg.jenkinsci.plugins.gitclient.Git.timeOut=30 -Dhudson.model.WorkspaceCleanupThread.disabled=true -Dorg.jenkinsci.plugins.gitclient.GitClient.quietRemoteBranches=true"
    network_mode: bridge
    restart: always
  backup:
    image: registry.h3d.com.cn/tac/backuper:v0.3.0
    network_mode: bridge
    volumes:
      - /data/jenkins/data_tac_team:/var/jenkins_home
    environment:
      BACKUPER_CONFIG: |-
        jobs:
        - name: "jenkins-tac-team"
          time: "50 2 * * *"
          tasks:
          - type: tar
            config:
              paths:
              - "/var/jenkins_home/backups"
              archive: "jenkins-tac-team"
          upload:
            type: s3
            config:
              bucket: jenkins
              folder_prefix: "jenkins-tac-team-h3d/backup"
          report:
            type: weixin
            config:
              robot_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=67f17655-9fad-4b90-b219-a01df5cf430c"
