version: '3'
services:
  jenkins_x51:
    image: "registry.h3d.com.cn/dev-productivity/jenkins:2.387.1-lts-centos7"
    ports:
      - '172.17.60.177:80:8080'
      - '172.17.60.177:50000:50000'
    volumes:
      - /data/jenkins/data_x51:/var/jenkins_home
    environment:
      TZ: "Asia/Shanghai"
      LANG: "en_US.utf8"
      JAVA_OPTS: "-server -Xms6G -Xmx12G -Dsun.jnu.encoding=UTF-8 -Dfile.encoding=UTF-8 -Dorg.jenkinsci.plugins.gitclient.Git.timeOut=30 -Dhudson.model.WorkspaceCleanupThread.disabled=true -Dorg.jenkinsci.plugins.gitclient.GitClient.quietRemoteBranches=true"
    network_mode: bridge
    restart: always
