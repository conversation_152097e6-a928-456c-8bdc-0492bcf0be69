mkdir -p /data/jenkins/data_x51
chown -R 1000:1000 /data/jenkins/data_x51
# 把docker-compose.yml放入/data/jenkins目录下
cd /data/jenkins
docker-compose up -d

# 换源
sed -i 's/https:\/\/updates.jenkins.io\/update-center.json/http:\/\/mirrors.tuna.tsinghua.edu.cn\/jenkins\/updates\/update-center.json/g' /data/jenkins/data_x51/hudson.model.UpdateCenter.xml
sed -i 's/https:\/\/updates.jenkins.io\/download/http:\/\/mirrors.tuna.tsinghua.edu.cn\/jenkins/g' /data/jenkins/data_x51/updates/default.json
sed -i 's/www.google.com/www.baidu.com/g' /data/jenkins/data_x51/updates/default.json