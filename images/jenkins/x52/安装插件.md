除了安装jenkins推荐的插件之外，还需要安装：

- [ ] Locale
- [ ] P4
- [ ] Blue Ocean
- [ ] Configuration as Code
- [ ] build user vars
- [ ] GitLab
- [ ] Generic Webhook Trigger
- [ ] Gitlab API
- [ ] GitLab Authentication
- [ ] GitLab Branch Source
- [ ] GitLab Logo
- [ ] xUnit
- [ ] Lockable Resources
- [ ] SSH Agent
- [ ] Pipeline: Multibranch with defaults
- [ ] Role-based Authorization Strategy
- [ ] Throttle Concurrent Builds
- [ ] Purge Build Queue
- [ ] Active Choices
