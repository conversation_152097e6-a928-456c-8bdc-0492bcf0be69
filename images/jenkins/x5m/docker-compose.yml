version: "2.4"

services:
  jenkins_x5mobile:
    image: registry.h3d.com.cn/library/jenkins:2.235.4-lts-centos7
    ports:
    - "172.17.200.216:80:8080"
    - "172.17.200.216:50000:50000"
    volumes:
    - /data/jenkins/data_x5mobile:/var/jenkins_home
    - /data/jenkins/data_x5mobile/jenkins:/usr/share/jenkins/ref/init.groovy.d
    environment:
      TZ: "Asia/Shanghai"
      LANG: "en_US.utf8"
      JAVA_OPTS: "-server -Xms16G -Xmx30G"
    network_mode: bridge
    restart: always
  backup:
    image: registry.h3d.com.cn/tac/backuper:v0.3.0
    network_mode: bridge
    volumes:
    - /data/jenkins/data_x5mobile:/var/jenkins_home
    environment:
      BACKUPER_CONFIG: |-
        jobs:
        - name: "jenkins-x5mobile"
          time: "50 3 * * *"
          tasks:
          - type: tar
            config:
              paths:
              - "/var/jenkins_home/backups"
              archive: "jenkins-x5mobile"
          upload:
            type: s3
            config:
              bucket: jenkins
              folder_prefix: "jenkins-x5mobile-h3d/backup"
          report:
            type: weixin
            config:
              robot_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=67f17655-9fad-4b90-b219-a01df5cf430c"
