version: '3'
services:
  mysql:
    # image: registry.h3d.com.cn/dev-productivity/mysql-centos8:5.7
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - GITLAB_CI_URL=https://gitlab.h3d.com.cn/
      - GITLAB_CI_TOKEN=6ZG4QEFSB7v7MLPnyPn-
      - GITLAB_RUNNER_NAME=pyframe
      - GITLAB_RUNNER_TAG_LIST=full_codecheck,incr_codecheck
      - GITLAB_RUNNER_CONCURRENT=4
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./builds:/builds
