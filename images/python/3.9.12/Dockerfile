FROM registry.h3d.com.cn/dev-productivity/centos:8.4.2105

RUN sed -e "s|^mirrorlist=|#mirrorlist=|g" \
    -e "s|^#baseurl=http://mirror.centos.org/\$contentdir/\$releasever|baseurl=https://mirrors.tuna.tsinghua.edu.cn/centos-vault/8.4.2105|g" \
    -i.bak /etc/yum.repos.d/CentOS-*.repo
RUN yum clean all && yum makecache &&  \
    yum install git zlib-devel bzip2-devel openssl-devel ncurses-devel sqlite-devel readline-devel tk-devel libffi-devel gcc make wget -y &&  \
    wget http://npm.taobao.org/mirrors/python/3.9.12/Python-3.9.12.tgz && pwd && \
    tar -zxvf Python-3.9.12.tgz && \
    cd Python-3.9.12 && ./configure --prefix=/usr && make -j 4 && make install && \
    python3 -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn &&  \
    python3 -m pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    cd .. && \rm Python-3.9.12.tgz && \rm -r Python-3.9.12 && yum clean all && python3 -m pip install poetry && ln -s /usr/bin/python3 /usr/bin/python

ENV TZ=Asia/Shanghai
