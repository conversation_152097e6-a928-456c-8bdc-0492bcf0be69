# coding=utf-8
from operator import methodcaller

from frame import *


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--force", default="", help="是否强更p4")
def build_ue5_client(job: str, force: str):
    """
    build_ue5_client流水线 
    示例：
        python m2.py build_ue5_client --job=sync_p4
    """
    kw = {
        "job": job,
        "force": force,
    }
    log.info("params: " + str(kw))
    from project.m2.bak.build_ue5_client import build_ue5_client_main

    methodcaller(job, **kw)(build_ue5_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def git_to_p4(job: str):
    """
    git_to_p4流水线
    示例：

    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.m2.bak.git_to_p4 import git_to_p4_main

    methodcaller(job, **kw)(git_to_p4_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def ue5_client_build(job: str):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.m2.bak.ue5_client_build import ue5_client_build_main

    methodcaller(job, **kw)(ue5_client_build_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def ue5_client_prebuild(job: str):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.m2.bak.ue5_client_build import ue5_client_prebuild_main

    methodcaller(job, **kw)(ue5_client_prebuild_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def ue5_client_build_predemo(job: str):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.m2.bak.ue5_client_build_predemo import ue5_client_build_predemo_main

    methodcaller(job, **kw)(ue5_client_build_predemo_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def build_m2_server(job: str):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.m2.build_m2_server import build_m2_server_main

    methodcaller(job, **kw)(build_m2_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def deploy_public_server(job: str):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.m2.deploy_public_server import deploy_public_server_main

    methodcaller(job, **kw)(deploy_public_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def build_ts(job: str):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.m2.build_ts import build_ts_main

    # python m2.py build_ts --job=update_client
    methodcaller(job, **kw)(build_ts_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def build_incubator_game(job: str):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.m2.build_incubator_game import build_incubator_game_main

    methodcaller(job, **kw)(build_incubator_game_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def create_new_branch(job: str):
    from project.m2.create_new_branch import create_new_branch_main

    methodcaller(job)(create_new_branch_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def auto_create_mr(job: str):
    from project.m2.auto_create_mr import auto_create_mr_main

    methodcaller(job)(auto_create_mr_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def package_apk(job: str):
    from project.m2.package_apk import package_apk_main

    methodcaller(job)(package_apk_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def package_ipa(job: str):
    from project.m2.package_ipa import package_ipa_main

    methodcaller(job)(package_ipa_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def build_xls(job: str):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.m2.build_xls import build_xls_main

    # python m2.py build_xls --job=update_client
    methodcaller(job, **kw)(build_xls_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def publish_all(job: str):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.m2.publish_all import publish_all_main
    methodcaller(job, **kw)(publish_all_main)


if __name__ == "__main__":
    cli()
