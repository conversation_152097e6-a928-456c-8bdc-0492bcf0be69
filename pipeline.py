# coding=utf-8
from operator import methodcaller

from frame import cli, click, log


@cli.command()
@click.option("--job", default="", help="步骤")
def create_pyframe_merge_request(job: str):
    """
    创建pyframe合并请求流水线
    示例：
        python pipeline.py create_pyframe_merge_request --job=do_first_job
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.pipeline.create_pyframe_merge_request import (
        create_pyframe_merge_request_main,
    )

    methodcaller(job, **kw)(create_pyframe_merge_request_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def deploy_pyframe_service(job: str):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.pipeline.deploy_pyframe_server import (
        deploy_pyframe_server_main,
    )

    methodcaller(job, **kw)(deploy_pyframe_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def jenkins_duration_alarm(job: str):
    """
    jenkins流水线构建超时提醒
    流水线地址：
    示例：
        python pipeline.py jenkins_duration_alarm --job=jenkins_duration_alarm
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.pipeline.jenkins_duration_alarm import (
        jenkins_duration_alarm_main,
    )

    methodcaller(job, **kw)(jenkins_duration_alarm_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def pipeline_data_report(job: str):
    """
    数据报表流水线
    流水线地址：
    示例：
        python pipeline.py pipeline_data_report --job=x5m_monthly_report
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.pipeline.pipeline_data_report import pipeline_data_report_main

    methodcaller(job, **kw)(pipeline_data_report_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def cron_notify(job: str):
    """
    数据报表流水线
    流水线地址：
    示例：
        python pipeline.py cron_notify --job=
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.pipeline.cron_notify import cron_notify_main

    methodcaller(job, **kw)(cron_notify_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def check_pyframe_not_master(job: str):
    """
    测试
    示例：
        python pipeline.py check_pyframe_not_master --job=do_first_job
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.pipeline.check_pyframe_not_master import (
        check_pyframe_not_master_main,
    )

    methodcaller(job, **kw)(check_pyframe_not_master_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def lixin_test(job: str):
    """
    测试
    示例：
        python pipeline.py lixin_test --job=do_first_job
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.pipeline.lixin_test import lixin_test_main

    methodcaller(job, **kw)(lixin_test_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def liutongqing_test(job: str):
    """
    测试脚本
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.pipeline.liutongqing_test import liutongqing_test_main

    methodcaller(job, **kw)(liutongqing_test_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def pyframe_ci(job: str):
    """
    测试脚本
    """
    log.info("job: " + str(job))
    from project.pipeline.pyframe_ci import pyframe_ci_main

    methodcaller(job)(pyframe_ci_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def pymug_ci(job: str):
    """
    pymug 项目自动化测试
    """
    log.info("job: " + str(job))

    from project39.pipeline.pymug_ci import pymug_ci_main

    methodcaller(job)(pymug_ci_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def deploy_timer_service(job: str):
    """
    timer_service部署流水线
    """
    log.info("job: " + str(job))
    from project.pipeline.deploy_timer_service import deploy_timer_service_main

    methodcaller(job)(deploy_timer_service_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def pipeline_statistics(job: str):
    """
    流水线统计
    python pipeline.py pipeline_statistics --job=pipeline_statistics
    """
    log.info("job: " + str(job))
    from project.pipeline.pipeline_statistics import pipeline_statistics_main

    methodcaller(job)(pipeline_statistics_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def auto_create_mr(job: str):
    """
    自动创建mr
    """
    from project.pipeline.auto_create_merge_request import auto_create_merge_request_main

    methodcaller(job)(auto_create_merge_request_main)


if __name__ == "__main__":
    cli()
