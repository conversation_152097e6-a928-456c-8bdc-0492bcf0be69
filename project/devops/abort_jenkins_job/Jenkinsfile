node {
    label "manual_package_trigger"
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline{
    agent {
        node {
            label "manual_package_trigger"
        }
    }
    parameters {
        text(name: 'full_name', defaultValue: 'discard/demos/timeline_package_test', description: '需要终止的job名称')
        text(name: 'build_number', defaultValue: '0', description: '构建号, 0代表全都')
    }
    options {
        disableConcurrentBuilds()
        timeout(time: 60, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: '60')
    }

    stages {
        stage("拉取脚本") {
            steps {
                bat label: '拉取脚本', script:
                '''
set branch="niushuaibing-abort-jen<PERSON>-job"

if exist pyframe-pipeline (
    pushd pyframe-pipeline
    git config pull.rebase false
    git reset --hard
    git clean -xdf -e logs
    for /f "delims=" %%i in ('git branch --show-current') do set local_branch=%%i
	echo "%local_branch%"
    if %branch%=="%local_branch%" (
        git pull
    ) else (
        git checkout %branch%
		git pull
    )
) else (
	git clone -b %branch% https://gitlab.h3d.com.cn/dev-productivity/pyframe-pipeline.git
    pushd pyframe-pipeline
)
python3 -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
python3 -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
'''
            }
        }
        stage("关闭构建") {
            steps {
                dir("pyframe-pipeline") {
                    bat label: 'abort building', script:
                    """
                    python3 devops.py abort_building_job --job abort_building --job_name ${params.full_name} --build_number ${params.build_number}
                    """
                }
            }
        }
    }
}
