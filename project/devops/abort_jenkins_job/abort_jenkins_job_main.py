from frame import advance
from frame.pipeline_mgr import JENKINS_AUTH

from project.devops.abort_jenkins_job.abort_jobs import AbortJenkinsJob


@advance.stage(stage="执行groovy脚本, 停止正在构建的job")
def abort_building(**kwargs):
    job_name = kwargs.get("job_name")
    build_number = kwargs.get("build_number")
    url = kwargs.get("url") or "http://jenkins-x5mobile.h3d.com.cn/"
    _url = f"http://{url.split('//')[-1].split('/')[0]}"
    _username, _password = None, None
    if _url in JENKINS_AUTH:
        _username = JENKINS_AUTH[_url]["username"]
        _password = JENKINS_AUTH[_url]["password"]

    username = kwargs.get("username") or _username
    password = kwargs.get("password") or _password
    jenkins = AbortJenkinsJob(url, username, password)
    jenkins.abort_job(job_name, int(build_number))
