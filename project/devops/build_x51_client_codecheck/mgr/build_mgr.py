# coding=utf-8
from frame import *


class BuildMgr:
    def __init__(self, branch: str) -> None:
        self.__workdir = os.path.join(env.pipeline.workspace(), branch, "src/star")

    def build(self):
        log_name = f"Build_{env.pipeline.build_num()}.log"
        env.set({"log": log_name})
        ret = cmd.run_shell(
            cmds=[f'BuildConsole.exe build_client.sln /prj="*" /rebuild /cfg="Debug|Win32" /Log={log_name} /All'],
            workdir=self.__workdir,
            return_output=True,
        )
        if ret[0] != 0:
            log.error(f"build return {ret[0]}")
            raise PyframeException(f"build失败, 返回值: {ret[0]}")

    def upload_log(self):
        log_name = env.get("log")
        if log_name:
            log_path = os.path.join(self.__workdir, log_name)
            nexus_url = advance.upload_pipeline_log(path=log_path)
            env.set({"log_url": nexus_url})
