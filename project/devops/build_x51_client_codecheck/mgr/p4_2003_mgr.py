# coding=utf-8
from frame import *
from project.x51 import config


class P42003Mgr:
    def __init__(self, branch: str, root: str):
        self.__branch = branch
        ip = common.get_host_ip()
        client = f"x51-client-codecheck-{ip}"
        self.p4 = P4Client(
            host=config.P4_2003_ART_READ_CONFIG.get("host"),
            username=config.P4_2003_ART_READ_CONFIG.get("username"),
            password=config.P4_2003_ART_READ_CONFIG.get("password"),
            client=client,
        )
        views = [
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/debug_bin/... //{client}/exe/debug_bin/...",
        ]
        self.p4.set_view(views=views)
        self.p4.set_root(path=root)
        self.p4.set_options(allwrite=True)

    def sync_all(self, changelist: str, force: bool):
        """
        拉取p4
        """
        self.p4.sync_with_files_and_dirs(path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/debug_bin/...", changelist=changelist, force=force)

    def get_p4_branch(self, branch: str):
        main_versions = self.p4.dirs("//H3D_X51_res/QQX5_Mainland/*")
        branches = []
        for main_version in main_versions:
            if "branch" in main_version:
                branches.append(main_version)
        branches.sort(reverse=True)
        log.info(branches)
        for b in branches:
            sub_versions = self.p4.dirs(f"{b}/*")
            for sub_version in sub_versions:
                if branch in sub_version:
                    log.info(sub_version)
                    return sub_version.lstrip("//H3D_X51_res/QQX5_Mainland/")
