# coding=utf-8


class Config:
    # TODO 个人账号密码
    # jenkins 账号
    CONFIG_JENKINS_X5M = {
        "url": "http://jenkins-x5mobile.h3d.com.cn",
        "username": "<EMAIL>",
        "password": "VJQ5vIGNLY6nioX49L44Q48zbrkybFRZ",
    }
    CONFIG_JENKINS_TESTDEV = {
        "url": "http://jenkins-testdev.h3d.com.cn/",
        "username": "<EMAIL>",
        "password": "XJRdZTi4hyPI4H7xPEdmcLvxANrHOC61",
    }
    CONFIG_JENKINS_TEAM1 = {"url": "http://jenkins-team1.h3d.com.cn/", "username": "<EMAIL>", "password": "Lang5208"}
    CONFIG_JENKINS_TEAM = {
        "url": "http://jenkins-tac-team.h3d.com.cn/",
        "username": "<EMAIL>",
        "password": "7LQP3b3AhNIfB3QK2ulQNJbDKrHwOUKv",
    }
    CONFIG_JENKINS_FRAMW = {"url": "http://jenkins-framw.h3d.com.cn/", "username": "<EMAIL>", "password": "Lang5208"}
    CONFIG_JENKINS_M2 = {"url": "http://jenkins-m2.h3d.com.cn/", "username": "<EMAIL>", "password": "yXawwNdPPeXdbiBj81yGiF1FgyVg8hcq"}

    # gitlab的token
    CONFIG_GITLAB_PYFRAME_PIPELINE = {
        "url": "https://gitlab.h3d.com.cn",
        "project": "dev-productivity/pyframe-pipeline",
        "token": "noss-BLmME-DFNqCTw9Z",
    }

    # nexus制品库账号
    NEXUS_CONFIG = {"username": "productivity-robot", "password": "productivity-robot"}

    # gitlab账号
    GITLAB_MAINTAINER = {
        "url": "http://x5mobile-gitlab.h3d.com.cn/",
        "username": "<EMAIL>",
        "password": "maintainer123",
        "token": "o-yNgwVADiQ8CicYdt_4",
    }

    GITLAB_DGM_DEVELOPER = {"url": "http://x5mobile-gitlab.h3d.com.cn/", "token": "********************"}

    # vika的gitlab通用账号
    GITLAB_VIKA = {"url": "https://gitlab.h3d.com.cn/", "token": "********************"}

    # yangzhenyue的gitlab测试账号
    # GITLAB_YANGZHENYUE = {"url": "https://gitlab.h3d.com.cn/", "token": "********************"}


config = Config()
