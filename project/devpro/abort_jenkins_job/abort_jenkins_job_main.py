from frame import *
from frame.pipeline_mgr import JENKINS_AUTH

from project.devpro.abort_jenkins_job.abort_jobs import Abort<PERSON><PERSON>kins<PERSON>ob


@advance.stage(stage="终止jenkins构建")
def abort_building(**kwargs):
    job_name = kwargs.get("job_name")
    build_number = kwargs.get("build_number")
    env.set({"job_name": job_name, "build_number": [str(build_number)]})
    url = kwargs.get("url") or "http://jenkins-x5mobile.h3d.com.cn/"
    _url = f"http://{url.split('//')[-1].split('/')[0]}"
    _username, _password = None, None
    if _url in JENKINS_AUTH:
        _username = JENKINS_AUTH[_url]["username"]
        _password = JENKINS_AUTH[_url]["password"]
    username = kwargs.get("username") or _username
    password = kwargs.get("password") or _password
    jenkins = Abort<PERSON><PERSON><PERSON>Job(url, username, password)
    jenkins.abort_job(job_name, int(build_number))


def __get_msg():
    job_name = env.get("job_name")
    build_number = env.get("build_number")
    build_number = [str(i) for i in build_number]
    msg = f"**流水线**: {job_name}\n"
    msg += f"**构建号**: {', '.join(build_number)}"
    return msg


def on_success(**kwargs):
    wechat.send_unicast_post_success(
        user_list=[],
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(
        user_list=[],
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_failure()


def on_unstable(**kwargs):
    wechat.send_unicast_post_unstable(
        user_list=[],
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_unstable()
