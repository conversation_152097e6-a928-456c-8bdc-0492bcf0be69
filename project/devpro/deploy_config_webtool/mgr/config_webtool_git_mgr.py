# coding=utf-8
from pathlib import Path

from frame import *


class ConfigWebtoolMgr:
    def __init__(self, branch: str):
        self.branch = branch
        self.git_mgr = GitMgr(workdir="/root", project_name="x5m-config-webtool")
        self.WORKDIR = "/root/x5m-config-webtool"

    def update(self):
        """
        更新代码
        """
        if not Path(self.WORKDIR).exists():
            self.git_mgr.clone(url="https://x5mobile-gitlab.h3d.com.cn/dev-productivity/x5m-config-webtool.git", branch=self.branch)
        else:
            self.git_mgr.reset()
            self.git_mgr.clean()
            self.git_mgr.advance_pull(branch=self.branch)

    def whether_build_image(self) -> bool:
        """
        检查requirements是否更新，用于是否重新构建镜像
        Returns:
            bool: 布尔值
        """
        if not self.git_mgr.exist():
            return True

        current_branch = self.git_mgr.get_current_branch()

        if self.branch != current_branch:
            log.warn(f"current branch is {current_branch}")
            return True
        self.git_mgr.fetch(branch=self.branch)

        ret = cmd.run_shell(
            cmds=[f"git diff --compact-summary {self.branch} origin/{self.branch}"],
            workdir=self.WORKDIR,
            return_output=True,
        )
        log.info(ret)
        if ret[0] != 0:
            log.error("return {}".format(ret[0]))
            raise PyframeException("git diff失败, 请联系管理员")

        for change in ret[1]:
            log.debug(change)
            if "requirements.txt" in change:
                return True
        return False
