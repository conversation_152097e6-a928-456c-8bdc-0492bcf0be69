# coding=utf-8
from pathlib import Path

from frame import *

WORKDIR = "/root"


@advance.stage(stage="准备")
def prepare(**kwargs):
    """
    准备工作,创建项目依赖的本地目录，杀死原有进程
    """
    path_mgr.mkdir("liangce")
    os.chdir("liangce")
    path_mgr.mkdir("logs")
    path_mgr.mkdir("tasks")
    path_mgr.mkdir("temp")
    path_mgr.mkdir("data")
    proc_mgr.kill_proc("gunicorn")


@advance.stage(stage="更新liangce-server")
def update_server(**kwargs):
    """
    更新接口代码
    """
    deploy_mode = kwargs.get("deploy_mode")
    branch = "version2"
    if deploy_mode == "test":
        branch = "version2"
    elif deploy_mode == "prod":
        branch = "master"
    else:
        log.error(f"unknown deploy_mode: {deploy_mode}")
        raise PyframeException(f"未知的deploy_mode: {deploy_mode}")
    server_git_mgr = GitMgr(workdir=WORKDIR, project_name="liangce-server")
    if not Path(os.path.join(WORKDIR, "liangce-server")).exists():
        server_git_mgr.clone(url="https://gitlab.h3d.com.cn/dev-productivity/liangce-server.git", branch=branch)
    else:
        server_git_mgr.reset()
        server_git_mgr.clean(exclude="gunicorn.pid")
        server_git_mgr.advance_pull(branch=branch)


# def run_mongo_and_nginx(**kwargs):
#     """
#     运行mongo和nginx容器
#     """
#     containers_path = "/root/pyframe-pipeline/project/devpro/deploy_liangce_server"
#     docker_compose = DockerCompose(workdir=containers_path, file="docker-compose.yml")
#     docker_compose.down()
#     docker_compose.up(detach=True)


@advance.stage(stage="启动liangce-server")
def run_server(**kwargs):
    """
    运行接口
    """
    ret = cmd.run_shell(
        cmds=["poetry add uvloop && poetry add gunicorn && poetry add httptools"],
        workdir=os.path.join(WORKDIR, "liangce-server"),
    )
    if ret[0] != 0:
        log.error("return {}".format(ret[0]))
        raise PyframeException("安装依赖失败, 请检查日志, 返回码: {}".format(ret[0]))

    if proc_mgr.exists_proc_by_params(params=["/root/liangce-server/", "main:app"]):
        log.debug("liangce-server is running")
        ret = cmd.run_shell(
            cmds=["python3.8 run.py -r"],
            workdir=os.path.join(WORKDIR, "liangce-server"),
            dot_kill_me=True,
        )
        if ret[0] != 0:
            log.error("return {}".format(ret[0]))
            raise PyframeException("重启liangce-server失败, 请检查日志, 返回码: {}".format(ret[0]))
    else:
        log.debug("liangce-server is not running")
        ret = cmd.run_shell(
            cmds=["python3.8 run.py -p"],
            workdir=os.path.join(WORKDIR, "liangce-server"),
            dot_kill_me=True,
        )
        if ret[0] != 0:
            log.error("return {}".format(ret[0]))
            raise PyframeException("启动liangce-server失败, 请检查日志, 返回码: {}".format(ret[0]))


def on_success(**kwargs):
    wechat.send_unicast_post_success(user_list=["<EMAIL>"])
    wechat.send_multicast_post_success(webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dac91f4a-9e70-4640-adf1-ad6626b5176c")
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(user_list=["<EMAIL>"])
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dac91f4a-9e70-4640-adf1-ad6626b5176c", rescue=False
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(user_list=["<EMAIL>"])
    wechat.send_multicast_post_unstable(webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dac91f4a-9e70-4640-adf1-ad6626b5176c")
    advance.insert_pipeline_history_on_canceled()
