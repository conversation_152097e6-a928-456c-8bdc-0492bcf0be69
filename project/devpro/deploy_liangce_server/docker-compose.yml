version: '3'
services:
 mongo:
    build:
      context: ./
      dockerfile: Dockerfile
    container_name: liangce-mongo
    ports:
      - "27017:27017"
    volumes:
      - /liangce/data:/data
    environment:
      MONGO_INITDB_ROOT_USERNAME: "admin"
      MONGO_INITDB_ROOT_PASSWORD: "123456"
    network_mode: bridge
    restart: always
 nginx:
   build:
     context: ./
     dockerfile: Dockerfile
   container_name: liangce-nginx

   ports:
     - "80:80"
     - "443:443"
   volumes:
     - /root/liangce-client/dist:/usr/share/nginx/html
     - /root/liangce-server/nginx/nginx.conf:/etc/nginx/nginx.conf
     - /root/liangce-server/nginx/certs:/etc/nginx/certs
   network_mode: bridge
   restart: always
