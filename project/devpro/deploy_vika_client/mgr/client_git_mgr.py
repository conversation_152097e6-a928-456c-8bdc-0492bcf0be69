# coding=utf-8

import re


from frame import *
from project.devpro import config


class ClientGitMgr:
    def __init__(self, workdir: str, branch: str):
        self.__workdir = workdir
        self.__branch = branch
        self.client_git_mgr = GitMgr(workdir=self.__workdir, project_name="liangce-vika-inject")

        self.client_gitlab_mgr = GitlabMgr(
            url=config.GITLAB_VIKA["url"], token=config.GITLAB_VIKA["token"], project="dev-productivity/liangce-vika-inject"
        )

    def update(self):
        if not Path(os.path.join(self.__workdir, "liangce-vika-inject")).exists():
            self.client_git_mgr.clone_with_oauth2(
                url="https://gitlab.h3d.com.cn/dev-productivity/liangce-vika-inject.git", branch=self.__branch, oauth2=config.GITLAB_VIKA["token"]
            )
        else:
            self.client_git_mgr.reset()
            self.client_git_mgr.clean()
            self.client_git_mgr.advance_pull(branch=self.__branch)

    def __check_tag_is_increase(self, tag_list: list):
        """
        检查最新tag是否是递增的
        """
        # 检查最新tag是否是x.x.x格式

        valid_tags = []
        for tag in tag_list:
            if re.match(r"^\d.\d.\d$", tag):
                valid_tags.append(re.match(r"^\d.\d.\d$", tag).group())
        valid_tags.sort()

        if len(valid_tags) <= 1:
            return

        # 判断是否递增
        if common.increase_x_x_x(valid_tags[-2]) != valid_tags[-1]:
            raise PyframeException(f"标签{tag_list[0]}没有正确的递增")
        # 判断最新tag是否是最大
        if tag_list[0] != valid_tags[-1]:
            raise PyframeException(f"标签{tag_list[0]}小于之前的版本号")

    def check_tag(self):
        """
        检查tag是否符合要求
        """
        tags = self.client_gitlab_mgr.get_tag_list()

        self.__check_tag_is_increase(tag_list=tags)
