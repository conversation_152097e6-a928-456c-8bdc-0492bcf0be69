FROM registry.h3d.com.cn/dev-productivity/liangce-client-nvm:14.17.2 as builder
WORKDIR /data/workspace
RUN #rm /bin/sh && ln -s /bin/bash /bin/sh &&
RUN yum install python36 -y
RUN git clone https://gitlab.h3d.com.cn/dev-productivity/liangce-client.git && \
    cd liangce-client && source ~/.bashrc && npm install && npm run build
FROM nginx
COPY --from=builder /data/workspace/liangce-client/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
COPY certs /etc/nginx/certs