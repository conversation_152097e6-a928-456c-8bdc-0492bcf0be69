version: '3'
services:
 cockpit:
    build:
      context: ./
      dockerfile: Dockerfile
    image: "cockpit_collect:v${BK_CI_BUILD_NUM}"
    container_name: cockpit_collect
    volumes:
      - /data/workspace/cockpit-collect/logs/:/data/workspace/skyeye/logs
    network_mode: bridge
    restart: always
    # 设置log文件大小
    logging:
        driver: "json-file"
        options:
          max-size: "2g"
          max-file: "3"
