from pathlib import Path

from frame import *

WORKDIR = "project/devpro/docker_deploy_skyeye_cockpit_collect"


def prepare(**kwargs):
    """
    准备工作
    """
    cockpit_path = "/data/workspace/cockpit-collect/logs/"
    if not Path(cockpit_path).exists():
        path_mgr.mkdir(cockpit_path)


def down(**kwargs):
    """
    运行docker-compose
    """
    docker_compose = DockerCompose(workdir=WORKDIR, yml="docker-compose.yml")
    docker_compose.down()


def up(**kwargs):
    """
    运行docker-compose
    """
    docker_compose = DockerCompose(workdir=WORKDIR, yml="docker-compose.yml")
    docker_compose.up(detach=True)


def build(**kwargs):
    """
    运行docker-compose
    """
    # docker = Docker(
    #     registry="registry.h3d.com.cn",
    #     username="dev-productivity",
    #     password="Productivity123"
    # )
    docker_compose = DockerCompose(workdir=WORKDIR, yml="docker-compose.yml")
    docker_compose.build(no_cache=True)


def on_success(**kwargs):
    wechat.send_unicast_post_success(user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"])
    wechat.send_multicast_post_success(webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dac91f4a-9e70-4640-adf1-ad6626b5176c")
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"])
    wechat.send_multicast_post_failure(webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dac91f4a-9e70-4640-adf1-ad6626b5176c")
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"])
    wechat.send_multicast_post_canceled(webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dac91f4a-9e70-4640-adf1-ad6626b5176c")
    advance.insert_pipeline_history_on_canceled()
