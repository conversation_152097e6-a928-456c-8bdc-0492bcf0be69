# coding=utf-8
import os
import pandas
import requests
from frame import log, path_mgr, env


class BkMgr:
    def __init__(self):
        self.bk_host = "http://**************:21935/"
        self.bk_host_1 = "https://bk-devops.h3d.com.cn/"
        # TODO 个人账号密码
        self.bk_headers = {"X-DEVOPS-UID": "<EMAIL>", "content-type": "application/json"}
        self.bk_project_ids = {"炫舞手游": "dgm", "炫舞项目": "x51", "炫舞时代": "x52", "研发效能": "dev-productivity", "测开": "tac-td"}
        self.file_path = r"/data/agent_info.xlsx"

    def __get_project_agent_list(self, project_id: str):
        """
        获取项目agent列表
        Args:
            project_id: 项目ID
        """
        url = self.bk_host + f"api/apigw-user/v3/projects/{project_id}/environment/thirdPartAgent/nodeList"
        log.info(f"url: {url}")
        resp = requests.get(url=url, headers=self.bk_headers)
        return resp.json()

    def __get_cookies(self):
        """
        获取实时cookie
        """
        url = "https://bk.h3d.com.cn/login/?c_url=https://bk-devops.h3d.com.cn/console"
        ret = requests.get(url)
        bklogin_csrftoken = ret.cookies.get("bklogin_csrftoken")
        # TODO 个人账号密码
        login_email = "<EMAIL>"
        login_password = "Lang5208"
        data = {"username": login_email, "password": login_password, "csrfmiddlewaretoken": bklogin_csrftoken}
        cookie = f"bklogin_csrftoken={bklogin_csrftoken}"
        headers = {
            "Cookie": cookie,
            "Referer": "https://bk.h3d.com.cn/login/?c_url=https://bk-devops.h3d.com.cn/console",
        }
        resp = requests.post(url=url, data=data, headers=headers, allow_redirects=False)
        bk_token = resp.cookies.get("bk_token")
        env.set({"bk_token": bk_token})

    def __get_project_agent_info(self, project_id: str, agent_id: str):
        """
        获取agent信息
        Args:
            project_id: 项目ID
            agent_id: agent ID
        """
        url = self.bk_host_1 + f"ms/environment/api/user/environment/thirdPartyAgent/projects/{project_id}/nodes/{agent_id}/thirdPartyAgentDetail"
        log.info(f"url: {url}")
        bk_token = env.get("bk_token")
        headers = {"Cookie": f"bk_token={bk_token}"}
        resp = requests.get(url=url, headers=headers)
        log.info(f"resp: {resp}")
        return resp.json()

    def __get_project_agent_ids(self, project_id: str):
        """
        获取agent ID列表
        """
        agent_hashids = []
        out = self.__get_project_agent_list(project_id=project_id)
        status = out.get("status")
        if status == 0:
            for i in out.get("data"):
                agent_hashids.append(i.get("nodeHashId"))
        log.info(agent_hashids)
        return agent_hashids

    def __create(self, data: list, project: str):
        file_path = self.file_path
        pf = pandas.DataFrame(data, columns=data[0])
        if not os.path.exists(file_path):
            pf.to_excel(file_path, sheet_name=project, index=False)

        else:
            writer = pandas.ExcelWriter(file_path, mode="a")
            # writer = pandas.ExcelWriter(file_path, mode="a", engine="openpyxl")
            pf.to_excel(writer, sheet_name=project, index=False)
            writer.save()
            writer.close()

    def get_project_agent(self):
        """
        获取节点信息
        """
        path_mgr.rm(path=self.file_path)
        projects = ["炫舞手游", "炫舞项目", "炫舞时代", "研发效能", "测开"]
        self.__get_cookies()
        for project in projects:
            if project not in self.bk_project_ids.keys():
                log.error(f"项目{project}不存在")
                continue
            num = True
            project_id = self.bk_project_ids.get(project)
            node_hashids = self.__get_project_agent_ids(project_id=project_id)
            data = []
            for agent_id in node_hashids:
                ret = self.__get_project_agent_info(project_id=project_id, agent_id=agent_id)
                status = ret.get("status")
                if status == 0 and ret.get("data"):
                    agent_info = ret.get("data")
                    head = []
                    values = []
                    for key in agent_info:
                        head.append(key)
                        values.append(agent_info.get(key))
                    log.info(f"head: {head}")
                    log.info(f"values: {values}")
                    log.info(f"num: {num}")
                    if num:
                        data.append(head)
                        num = False
                    data.append(values)
            self.__create(data=data, project=project)


bk_mgr = BkMgr()
