"""
获取Jenkins 节点信息
"""
from frame import *
from project.devpro.get_jenkins_agent_info.mgr.jenkins_mgr import JenkinsMgr
from project.devpro.get_jenkins_agent_info.mgr.nexus_mgr import NexusMgr


@advance.stage(stage="获取节点信息")
def get_agent_info(**kwargs):
    jenkins = JenkinsMgr()
    jenkins.get_nodes_summary()


@advance.stage(stage="上传制品库")
def upload_nexus(**kwargs):
    nexus_mgr = NexusMgr()
    nexus_mgr.upload_nexus()


def __get_msg():
    # 结点信息agent表
    agent_xlsx_url = env.get("agent_xlsx_url")
    msg = f"\n**jenkins节点信息表**: [Jenkins_agent_info.xlsx]({agent_xlsx_url})"
    return msg


def on_success(**kwargs):
    log.info("构建成功")
    wechat.send_unicast_post_success(user_list=[], content=__get_msg())
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    log.info("构建失败")
    wechat.send_unicast_post_failure(user_list=[], content="")
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    log.info("构建取消")
    wechat.send_unicast_post_canceled(user_list=[], content="")
    advance.insert_pipeline_history_on_canceled()
