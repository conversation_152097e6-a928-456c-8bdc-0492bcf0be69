import xml.etree.ElementTree as ET

import jenkins
import pandas
from api4jenkins import <PERSON>

from frame import *


class JenkinsMgr:
    def __init__(self):
        # TODO 个人账号密码
        self.username = "<EMAIL>"
        self.password = "VJQ5vIGNLY6nioX49L44Q48zbrkybFRZ"
        self.server = jenkins.<PERSON>("http://jenkins-x5mobile.h3d.com.cn", self.username, self.password)
        self.j = <PERSON>("http://jenkins-x5mobile.h3d.com.cn", auth=(self.username, self.password))
        self.file_path = r"/data/jenkins_agent_info.xlsx"
        self.project = "炫舞手游"

    def __get_node_label(self, server, node_name):
        node_config = server.get_node_config(node_name)
        with open("/data/node_config.xml", "w", encoding="utf-8") as f:
            f.write(node_config)
            f.write("\n")
        tree = ET.parse(
            "/data/node_config.xml",
        )
        root = tree.getroot()
        try:
            node_label = root.find("label").text
        except:
            node_label = "NA"
        return node_label

    def __get_node_ip_and_node_description(self, node_name):
        node = self.j.nodes.get(node_name)
        node_description = node.description
        script = f"""
            import hudson.model.Computer.ListPossibleNames
            def node = jenkins.model.Jenkins.instance.getNode("{node_name}")
            println(node.computer.getChannel().call(new ListPossibleNames())[-1])
        """
        node_ip = self.j.system.run_script(script).rstrip()
        return node_description, node_ip

    def __create(self, data: list, project: str):
        file_path = self.file_path
        pf = pandas.DataFrame(data[1:], columns=data[0])
        if not os.path.exists(file_path):
            pf.to_excel(file_path, sheet_name=project, index=False)

        else:
            writer = pandas.ExcelWriter(file_path, mode="a")
            # writer = pandas.ExcelWriter(file_path, mode="a", engine="openpyxl")
            pf.to_excel(writer, sheet_name=project, index=False)
            writer.save()
            writer.close()

    def get_nodes_summary(self):
        # 获取nodes节点的name、label、ip、status（连接状态）
        path_mgr.rm(path=self.file_path)
        nodes = self.server.get_nodes()
        data = []
        head = ["节点名称", "节点标签", "节点描述", "节点所在IP", "节点状态", "节点安装路径", "节点执行器数量"]
        data.append(head)
        log.info(f"head: {head}\n")
        for count in range(1, len(nodes)):
            node_name = nodes[count]["name"]
            node_status = "掉线" if nodes[count]["offline"] else "在线"
            node_label = self.__get_node_label(self.server, node_name)
            node_info = self.server.get_node_info(node_name)
            if not nodes[count]["offline"]:
                node_path = node_info["absoluteRemotePath"]
                node_numexecutors = node_info["numExecutors"]
                node_description, node_ip = self.__get_node_ip_and_node_description(node_name)
                values = [
                    f"{node_name}",
                    f"{node_label}",
                    f"{node_description}",
                    f"{node_ip}",
                    f"{node_status}",
                    f"{node_path}",
                    f"{node_numexecutors}",
                ]
                log.info(f"values: {values}\n")
                data.append(values)

            else:
                values = [f"{node_name}", f"{node_label}", "", "", f"{node_status}"]
                log.info(f"values: {values}\n")
                data.append(values)

        self.__create(data=data, project=self.project)
