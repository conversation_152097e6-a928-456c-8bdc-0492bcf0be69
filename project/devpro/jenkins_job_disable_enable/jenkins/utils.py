import os
import pathlib


def check_status(func):
    current_status = func.__name__

    def wrapper(*args, **kwargs):
        self = args[0]
        if not os.path.exists(self.PROGRAM_STATUS):
            pathlib.Path(self.PROGRAM_STATUS).touch()

        with open(self.PROGRAM_STATUS, "r+", encoding="utf-8") as f:
            status = f.read()
            if current_status == status and current_status == "disable_jobs":
                raise Exception("禁止执行两次`{}`操作".format(current_status))

            result = func(*args, **kwargs)
            f.seek(0)
            f.truncate()
            f.write(current_status)

        return result

    return wrapper
