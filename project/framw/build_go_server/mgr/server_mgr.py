# coding=utf-8

from frame import *

WORKDIR = os.path.join(env.pipeline.workspace(), "server_go")


class ServerMgr:
    def __init__(self):
        pass

    def stop_server(self):
        proc_mgr.kill_proc(name="server")

    def install_depend(self):
        ret = cmd.run_shell(
            cmds=["go env -w GO111MODULE=on", "go env -w GOPROXY='https://goproxy.io,direct'", "go mod tidy"],
            workdir=WORKDIR,
        )
        if ret[0] != 0:
            log.error("go install depend failed")
            raise PyframeException("安装依赖失败")

    def build(self):
        ret = cmd.run_shell(cmds=["go build -o bin ./..."], workdir=WORKDIR)
        if ret[0] != 0:
            log.error("go build failed")
            raise PyframeException("编译go项目失败, 请检查代码错误")

    def run_tests(self):
        run_tests_log_path = f"logs/run_tests_{env.pipeline.build_num()}.log"
        ret = cmd.run_shell(
            cmds=[
                # "go test server_go/pkg/app/test -run TestZoneSetupServers/FromConfig"
                "sh run_test.sh"
            ],
            workdir=os.path.join(WORKDIR, "ci"),
            return_output=True,
            log_to_file=run_tests_log_path,
        )
        if ret[0] != 0:
            log.error("run tests failed")
            errors = []
            for line in Path(run_tests_log_path).read_text().splitlines():
                if " FAIL: " in line:
                    errors.append(line.strip())
            raise PyframeException("运行测试用例失败: \n{}".format("\n".join(errors)))


server_mgr = ServerMgr()
