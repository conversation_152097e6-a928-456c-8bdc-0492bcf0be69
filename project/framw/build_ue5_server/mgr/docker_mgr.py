# coding=utf-8
from frame import *
from project.framw.build_unity_server import config


class DockerMgr:
    def __init__(self):
        # 可以写进配置文件
        self.docker = Docker(**config.RESISTRY_CONFIG)
        self.__branch = env.get("jenkins_file_path")
        self.server_repository = "registry.h3d.com.cn/framw/ue4-ts-svc"
        self.server_image = "registry.h3d.com.cn/framw/ue4-ts-svc:latest"
        self.db_repository = "registry.h3d.com.cn/framw/ue4_ts-dbmigrater"
        self.db_image = "registry.h3d.com.cn/framw/ue4_ts-dbmigrater:latest"

    def build_server_image(self):
        """
        构建server镜像
        """
        self.docker.build(
            workdir=os.path.join(env.pipeline.workspace(), "Games/server/exe"), file="Dockerfile", repository=self.server_repository, tag="latest"
        )
        self.docker.push(image=self.server_image)

    def build_db_migration_image(self):
        """
        构建db_migration镜像
        """
        self.docker.build(
            workdir=os.path.join(env.pipeline.workspace(), "Games/server/db-migrations/"),
            file="Dockerfile",
            repository=self.db_repository,
            tag="latest",
        )
        self.docker.push(image=self.db_image)

    def clean_dangling_images(self):
        """
        删除dangling images
        """
        self.docker.rmi_dangling_images()

    def clean_cores(self):
        """
        删除cores
        """
        path_mgr.rm(os.path.join(env.pipeline.workspace(), "Games/server/exe/core"))
