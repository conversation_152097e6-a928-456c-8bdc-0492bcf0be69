# coding=utf-8
from frame import *
from project.framw.build_ue5_server import config


class P4COM2011:
    def __init__(self):
        # self.stream = config.P4_2011_CONFIG.get("stream")
        jenkins_file_path = env.get("jenkins_file_path")
        self.stream = jenkins_file_path.split("/Games")[0]
        log.info(f"stream: {self.stream}")
        self.p4 = P4Client(
            host=config.P4_2011_CONFIG.get("host"),
            username=config.P4_2011_CONFIG.get("username"),
            password=config.P4_2011_CONFIG.get("password"),
            client=config.P4_2011_CONFIG.get("client"),
            stream=self.stream,
        )
        self.p4.set_options(allwrite=True)
        self.p4.set_root(path=config.P4_2011_CONFIG.get("root"))

    def sync_all(self, force: bool):
        """
        拉取p4全路径
        """
        self.p4.sync_all(force=force)

    def sync_list(self, path_list: list, force: bool):
        """
        拉取p4指定路径
        """
        self.p4.sync_list(path_list=path_list, force=force)

    def get_latest_user_changelist(self, view: str):
        """
        获取最后一次提交人和提交号
        """
        user, changelist = None, None
        try:
            change = self.p4.get_latest_changes(path=view)
            user = change.get("user")
            changelist = change.get("change")
        except Exception as e:
            log.error(e)

        return user, changelist
