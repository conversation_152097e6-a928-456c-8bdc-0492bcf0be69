# coding=utf-8
from frame import advance
from project.framw.build_ue5_ugs.scripts.build.build_stage import *


@advance.stage(stage="iOS-拉取p4")
def sync_p4(**kwargs):
    sync_p4_stage()


@advance.stage("iOS-删除旧的构建")
def clear_old_builds(**kwargs):
    clear_old_builds_stage()


@advance.stage("iOS-更新打包配置")
def update_build_xml(**kwargs):
    build_vs_stage()


@advance.stage(stage="iOS-编译引擎和ts")
def build_vs(**kwargs):
    build_vs_stage()


@advance.stage(stage="iOS-打包")
def build_client(**kwargs):
    build_client_stage()


@advance.stage(stage="iOS-上传P4")
def upload_p4(**kwargs):
    upload_p4_stage()


@advance.stage(stage="iOS-上传制品")
def upload_nexus(**kwargs):
    upload_nexus_stage()


@advance.stage(stage="iOS-上传日志")
def upload_logs(**kwargs):
    upload_logs_stage()


def on_unstable(**kwargs):
    on_unstable_stage()


def on_success(**kwargs):
    on_success_stage()


def on_failure(**kwargs):
    on_failure_stage()
