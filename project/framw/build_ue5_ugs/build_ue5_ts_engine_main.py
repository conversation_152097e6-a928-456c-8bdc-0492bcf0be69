# coding=utf-8
from frame import advance
from project.framw.build_ue5_ugs.scripts.build.build_stage import *


@advance.stage(stage="拉取p4")
def sync_p4(**kwargs):
    sync_p4_stage()


@advance.stage("引擎源码编译")
def build_code(**kwargs):
    build_code_stage()


@advance.stage(stage="上传日志")
def upload_logs(**kwargs):
    upload_logs_stage()


def on_unstable(**kwargs):
    on_unstable_stage()


def on_success(**kwargs):
    on_success_stage()


def on_failure(**kwargs):
    on_failure_stage()
