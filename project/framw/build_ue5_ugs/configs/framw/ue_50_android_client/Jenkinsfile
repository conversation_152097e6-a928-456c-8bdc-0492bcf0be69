node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

def clone_pyframe_pipeline(branch) {
    if (!fileExists("pyframe-pipeline/.git")) {
        bat """
        git clone -b $branch https://gitlab.h3d.com.cn/dev-productivity/pyframe-pipeline.git
        git config --local user.name "<EMAIL>"
        git config --local user.email "<EMAIL>"
        """
    }else{
        dir('pyframe-pipeline') {
            bat """
            git config pull.rebase false
            git clean -xdf -e logs
            git reset --hard HEAD
            git fetch --all
            git checkout $branch
            git pull origin $branch --quiet
            """
        }
    }
    dir('pyframe-pipeline') {
        bat """
        python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
        python -m pip install -r requirements.txt -i  http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
        """
    }
}

pipeline {
    agent {
        node {
            label "framw-ue5-android-${DEPLOY_ENV}"
        }
    }

    parameters {
        booleanParam(name: 'force', defaultValue: false, description: '是否强更p4')
        extendedChoice(
            name: 'language',
            defaultValue: 'zh',
            description: '语言',
            multiSelectDelimiter: ',',
            quoteValue: false,
            saveJSONParameterToFile: false,
            type: 'PT_CHECKBOX',
            value:'zh,tw,jp,kr,us',
            visibleItemCount: 5
        )
        choice(choices: ['Debug', 'Release'], description: '出包类型', name: 'debug_mode')
        choice(choices: ['国外正式服', '测试服', '本地', '国内正式'], description: 'FTP资源服务器', name: 'ftp_index')
        choice(choices: ['apk包_全包', 'pak资源包', '空包', 'pak资源包和空包'], description: '平台版本', name: 'platform_info')
    }

    options {
        disableConcurrentBuilds()
        // timeout(time:1, unit: 'HOURS')
    }
    // triggers {
    //     cron('H/30 10-23 * * *')
    // }

    environment {
       SCRIPT_CONFIG = 'framw/ue_50_android_client'
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("下载pyframe脚本") {
            steps {
                script {
                    if ("${params.pyframe_branch}" == "null"){
                        env.PYFRAME_BRANCH = "master"
                    }else {
                        env.PYFRAME_BRANCH = "${params.pyframe_branch}"
                    }
                    clone_pyframe_pipeline(env.PYFRAME_BRANCH)
                }
            }
        }
        stage("更新p4") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(script: "python framw.py build_ue5_android_client --job=sync_p4 --force=${params.force}")
                    }
                }
            }
        }
        stage("清理旧的构建") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(script: "python framw.py build_ue5_android_client --job=clear_old_builds")
                    }
                }
            }
        }
        stage("拷贝配置") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(script: "python framw.py build_ue5_android_client --job=update_build_xml")
                    }
                }
            }
        }
        stage("编译引擎和ts") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(script: "python framw.py build_ue5_android_client --job=build_vs")
                    }
                }
            }
        }
        stage("编译客户端") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(script: "python framw.py build_ue5_android_client --job=build_client")
                    }
                }
            }
        }
        stage("上传制品") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(script: "python framw.py build_ue5_android_client --job=upload_nexus")
                    }
                }
            }
        }
        stage("上传p4") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(script: "python framw.py build_ue5_android_client --job=upload_p4")
                    }
                }
            }
        }
    }
    post {
        always {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python framw.py build_ue5_android_client --job=upload_logs")
                }
            }
        }
        unstable {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python framw.py build_ue5_android_client --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python framw.py build_ue5_android_client --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python framw.py build_ue5_android_client --job=on_failure")
                }
            }
        }
    }
}