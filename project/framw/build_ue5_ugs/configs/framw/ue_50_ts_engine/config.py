import os
import re

from frame import env


class BuildSourceConfigBase:
    class P4:
        def __init__(self, project_root, build_class, build_type):
            self.port = "p4.com:2011"
            self.username = "framw_test"
            self.password = "framw123"
            self.charset = "utf8"
            # self.client = f"framw_{build_class}_ue5_ugs_{build_type}"
            self.client = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_{env.get('NODE_NAME')}")
            self.root = project_root
            self.stream = "//UE_TS/main/UE5.0_TS_m"
            self.view = None
            self.abs_paths = []

    class Nexus:
        def __init__(self):
            self.upload_path = f"http://nexus.h3d.com.cn/repository/dev-productivity/framw/{env.pipeline.pipeline_name()}/{env.pipeline.build_num()}"
            self.user = "productivity-robot"
            self.password = "productivity-robot"
            self.log_name = "Log.txt"

    def __init__(self):
        self.project_root_path = env.pipeline.workspace()
        self.build_class = "source"
        self.project_client_path = os.path.join(self.project_root_path, "UE5", "Games", "client")
        self.project_u_project = os.path.join(self.project_root_path, "UE5", "Games", "client", "client.uproject")
        self.win_ue5_engine_path = os.path.join(self.project_root_path, "UE5", "Engine")
        self.win_ue5_batch_path = os.path.join(self.win_ue5_engine_path, "Build", "BatchFiles")
        self.win_ue5_run_uat = os.path.join(self.win_ue5_batch_path, "RunUAT.bat")
        self.win_ue5_graph_path = os.path.join(self.win_ue5_engine_path, "Build", "Graph")
        self.win_ue5_build_xml = os.path.join(self.win_ue5_graph_path, "Examples", "BuildEditorAndTools.xml")
        self.win_ue5_log_path = os.path.join(self.win_ue5_engine_path, "Programs", "AutomationTool", "Saved", "Logs")
        self.editor_target = "clientEditor"
        self.archive_name = "clientEditor"
        self.archive_stream = "//Release/Framw/DemoBinaries"
        self.licensee = "true"
        self.target_platforms = "Win64"
        self.build_client_encoding = None
        self.nexus = self.Nexus()


class BuildSourceConfigDev(BuildSourceConfigBase):
    def __init__(self):
        super().__init__()
        self.build_type = "dev"  # 测试环境
        self.p4 = self.P4(self.project_root_path, self.build_type, self.build_class)


class BuildSourceConfigProd(BuildSourceConfigBase):
    def __init__(self):
        super().__init__()
        self.build_type = "prod"  # 生产环境
        self.p4 = self.P4(self.project_root_path, self.build_type, self.build_class)


def init_config():
    if os.getenv("DEPLOY_ENV", "dev") == "dev":
        return BuildSourceConfigDev

    return BuildSourceConfigProd
