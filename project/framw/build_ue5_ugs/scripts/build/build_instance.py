# coding=utf-8

import datetime
import re

from frame import *
from project.framw.build_ue5_ugs import config
from project.framw.build_ue5_ugs.scripts.p4 import P4ClientBuildTrunk


class BaseBuild:
    def __init__(self):
        self.config = config
        self.nexus = Nexus(self.config.nexus.user, self.config.nexus.password)
        self.package = os.path.join(self.config.project_platform_builds_path, self.config.nexus.app_raw_name)
        self.build_client_encoding = self.config.build_client_encoding

    def clear_old_builds(self):
        if path_mgr.exists(self.config.project_builds_path):
            rm_ret = path_mgr.rm(self.config.project_builds_path)
        else:
            rm_ret = True
        return rm_ret

    def update_build_xml(self):
        path_mgr.mkdir(self.config.win_ue5_graph_path)
        path_mgr.xcopy(src=self.config.project_builds_xml, dst=self.config.win_ue5_build_xml, dst_is_file=True)

    def build_vs(self, gen_manifest=True):
        """
        编译引擎及相关内容
        """
        self.build_engine()

        ret = self.gen_dts()
        if ret[0] != 0:
            raise PyframeException("生成DTS失败, 请检查代码错误")
        ret = self.build_ts()
        if ret[0] != 0:
            raise PyframeException("Ts转Js失败, 请检查代码错误")
        if gen_manifest:
            ret = self.gen_manifest()
            if ret[0] != 0:
                raise PyframeException("生成manifest失败, 请检查代码错误")

    def update_binaries(self):
        """
        更新二进制文件
        """
        source_zip = os.path.join(f"{self.config.p4.root}", "++UE_TS+main+UE5.0_TS_v_ugs_pipeline-clientEditor.zip")
        tar.decompress(source_zip, self.config.p4.root)
        src = os.path.join(self.config.p4.root, "Games", "client")
        dst = self.config.project_client_path
        path_mgr.xcopy(src=src, dst=dst, dst_is_file=False)

    def gen_dts(self):
        """
        生成dts
        """
        ret = cmd.run_shell(
            cmds=[f"call {self.config.win_ue5_editor_cmd} {self.config.project_client} -run=GenDTS"],
            workdir=self.config.project_client_path,
            encoding=self.build_client_encoding,
        )
        return ret

    def build_ts(self):
        """
        编译ts
        """
        ret = cmd.run_shell(
            cmds=[f'call "{self.config.node_js}" {self.config.h3d_compile_js} {self.config.h3d_manifest_json}'],
            workdir=self.config.project_client_path,
            encoding=self.build_client_encoding,
        )
        return ret

    def gen_manifest(self):
        """
        生成manifest
        """
        ret = cmd.run_shell(
            cmds=[f"call {self.config.win_ue5_editor_cmd} {self.config.project_client} -run=GenerateManifest"],
            workdir=self.config.project_client_path,
            encoding=self.build_client_encoding,
        )
        return ret

    def build_engine(self):
        """
        编译引擎
        """
        cmd_str = f"{self.config.win_ue5_run_uat} BuildGraph "
        cmd_str += f"-Script={self.config.win_ue5_engine_script} "
        cmd_str += f'-Target="Compile clientEditor Win64" '
        cmd_str += f"-set:EditorTarget={self.config.editor_target} "
        cmd_str += f"-set:TargetPlatforms={self.config.target_platforms} "
        cmd_str += f"-set:Licensee={self.config.licensee} "
        cmd_str += f"-set:UProjectPath={self.config.project_client} "

        ret = cmd.run_shell(
            cmds=[cmd_str],
            workdir=os.path.join(self.config.project_root_path, "UE5"),
            return_output=True,
        )
        if ret[0] != 0:
            self.__analysis_error()
        else:
            log.info("编译引擎成功")

    def __analysis_error(self):
        code_errors = self.__analysis_log()
        if code_errors:
            error_msg = ""
            for code_error in code_errors:
                error_msg += code_error
            if error_msg:
                raise PyframeException(f"源码预编译失败, 请检查代码错误:{error_msg}")

    def __analysis_log(self):
        code_error_pattern = re.compile(r"^BUILD FAILED: (.*?.)$")
        code_errors = []
        log_path = os.path.join(self.config.win_ue5_log_path, "Log.txt")
        if not path_mgr.exists(log_path):
            return code_errors
        with open(log_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
            for line in lines:
                ret = re.findall(code_error_pattern, line)
                if ret:
                    code_errors.extend(ret)
        log.info(f"code_errors: {code_errors}")
        return code_errors

    def upload_logs(self):
        src = self.config.win_ue5_client_log
        log.info(f"src: {src}")
        if path_mgr.exists(src):
            dst = advance.upload_pipeline_log(src)
            env.set({"ARTIFACT_DISTRIBUTE_LOG": dst})
        else:
            log.warn("日志文件不存在，无法上传")

    @staticmethod
    def __convert_debug_mode(debug_mode: str) -> str:
        debug_mode_map = {"Debug": "true", "Release": "false"}
        return debug_mode_map.get(debug_mode)

    @staticmethod
    def __convert_ftp_index(ftp_index: str) -> int:
        ftp_index_map = {
            "国外正式服": 1,
            "测试服": 2,
            "本地": 3,
            "国内正式": 4,
        }
        return ftp_index_map.get(ftp_index)

    def __convert_platform_info(self, platform_info: str) -> str:
        build_class = self.config.build_class
        if build_class.lower() == "android":
            platform_info_map = {
                "apk包_全包": "AndroidApkFull",
                "pak资源包": "Android",
                "空包": "AndroidApk",
                "pak资源包和空包": "AndroidPakAndApk",
            }
        elif build_class.lower() == "ios":
            platform_info_map = {
                "apk包_全包": "IOSIpaFull",
                "pak资源包": "IOS",
                "空包": "IOSIpa",
                "pak资源包和空包": "IOSPakAndApk",
            }
        else:
            platform_info_map = {}
        return platform_info_map.get(platform_info)

    def build_client(self):
        """
        构建客户端
        """
        # 获取相关参数
        res_version_path = os.path.join(env.pipeline.workspace(), "res_version.txt")
        language = env.get("BUILD_LANGUAGE", "zh")
        if not path_mgr.exists(res_version_path):
            with open(res_version_path, "w", encoding="utf-8") as f:
                f.write("1")

        with open(res_version_path, "r+", encoding="utf-8") as f:
            res_version = f.read()
            f.seek(0)
            f.truncate()
            f.write(str(int(res_version) + 1))

        debug_mode = env.get("DEBUG_MODE")  # 出包的编译配置 true Debug包; false Release包
        debug_mode = self.__convert_debug_mode(debug_mode)
        if not debug_mode:
            raise PyframeException("经检出包类型参数是否正确填写")
        ftp_index = env.get("FTP_INDEX")  # FTP资源服务器参数: 1 国外正式服; 2 测试服; 3 本地; 4 国内正式
        ftp_index = self.__convert_ftp_index(ftp_index)
        if not ftp_index:
            raise PyframeException("经检FTP资源服务器参数是否正确填写")
        # AndroidApkFull apk包_全包;Android pak资源包;AndroidApk 空包; AndroidPakAndApk pak资源包+空包
        # IOSIpaFull apk包_全包;IOS pak资源包;IOSIpa 空包; IOSPakAndApk pak资源包+空包
        platform_info = env.get("PLATFORM_INFO")
        platform_info = self.__convert_platform_info(platform_info)
        if not platform_info:
            raise PyframeException("经检查平台版本参数是否正确填写")

        # 组织命令行参数
        cmd_str = f"call {self.config.win_ue5_editor_cmd} {self.config.project_client} "
        cmd_str += "-run=BuildPak "
        cmd_str += f"-{self.config.build_pack_type}=true "
        cmd_str += f"-language='{language}' "
        cmd_str += f"-resVersion={res_version} "
        cmd_str += f"-debugMode={debug_mode} "
        cmd_str += f"-FtpIndex={ftp_index} "
        cmd_str += f"-{platform_info}=true "

        # 如果是ios平台的release包，需要先打debug包，然后更新code cache后，再打release包
        if debug_mode == "false" and self.config.build_class.lower() == "ios":
            debug_cmd_str = cmd_str.replace("-debugMode=false ", "-debugMode=true ")
            self.pack_ios_debug(debug_cmd_str)

        # 调用命令行
        ret = cmd.run_shell(
            cmds=[cmd_str],
            workdir=self.config.project_client_path,
            encoding=self.config.build_client_encoding,
        )
        return ret

    def __run_shell(self, cmd_str: str, error_msg: str):
        ret = cmd.run_shell(
            cmds=[cmd_str],
            workdir=self.config.project_client_path,
            encoding=self.config.build_client_encoding,
        )
        if ret[0] != 0:
            raise PyframeException(error_msg)

    def pack_ios_debug(self, cmd_str: str):
        """
        iOS release类型包需要js code cache进包
        Windows或者Mac平台生成的js code cache在iOS上无法正常运行
        因此需要在iOS上预先生成，此步骤便是在iOS上预先运行debug包，然后将生成的js code cache传回工程内，再进行打包
        详情参见：https://wiki.h3d.com.cn/pages/viewpage.action?pageId=83072338
        """

        # 先打debug包
        ret = cmd.run_shell(cmds=[cmd_str], workdir=self.config.project_client_path, encoding=self.config.build_client_encoding)
        if ret[0] != 0:
            raise PyframeException("打debug包失败")

        self.__wait_for_device()  # 等待十分钟，如果没有iOS连接此打包机，会超时并抛出异常，如果已经连接或者成功连接，正常走后续流程

        code_cache_dst = os.path.join(self.config.project_client_path, "Publish")
        cmd_map = {
            f"tidevice install {self.package}": "安装IPA失败",
            f"tidevice launch {self.config.game_package_name}": "运行游戏失败",
            f"ping 127.0.0.1 -n {self.config.game_run_time}": "游戏运行过程中发生异常",
            f'tidevice fsync -B {self.config.game_package_name} pull "/Documents/client/Saved/Code Cache" {code_cache_dst}': "拷贝code cache到工程目录失败",
            f"tidevice kill {self.config.game_package_name}": "游戏关闭失败",
            f"tidevice uninstall {self.config.game_package_name}": "游戏卸载失败",
        }

        for cmd_str, error_msg in cmd_map.items():
            self.__run_shell(cmd_str, error_msg)

    @advance.timeout(seconds=600, exception_msg="未检测到iOS设备连接到此电脑，请连接后再重试")
    def __wait_for_device(self):
        cmd_str = "tidevice wait-for-device"
        ret = cmd.run_shell(
            cmds=[cmd_str],
            workdir=self.config.project_client_path,
            encoding=self.config.build_client_encoding,
        )
        log.info(f"检测iOS设备结果: {ret}")

    @staticmethod
    def get_log_url():
        return env.get("ARTIFACT_DISTRIBUTE_LOG")

    @staticmethod
    def get_p4_version():
        return env.get("CHANGELIST")

    @staticmethod
    def get_p4_user():
        return env.get("USER")

    @staticmethod
    def __get_now_time():
        return datetime.datetime.now().strftime("%Y-%m-%d_%H:%M:%S")

    def __get_env_or_config(self, params):
        upper_params = params.upper()
        lower_params = params.lower()
        log.info(f"upper_params: {upper_params} lower_params: {lower_params}")
        env_params = env.get(lower_params) or env.get(upper_params)
        if env_params and env_params != "null":
            env_params = env_params
        else:
            env_params = f"{self.config}.{lower_params}"
        log.info(f"env_params: {env_params}")
        return env_params

    def __get_suffix(self):
        return self.package.split(".")[-1]

    def __get_app_name(self):
        # 从流水线入参中获取，如果未获取到相关参数使用配置文件中的默认参数
        client_name = env.get("CLIENT_NAME")
        if client_name and client_name != "null":
            client_name = client_name
        else:
            client_name = self.config.client_name
        player_build_version = env.get("PLAYER_BUILD_VERSION")
        if player_build_version and player_build_version != "null":
            player_build_version = player_build_version
        else:
            player_build_version = self.config.player_build_version
        player_build_number = env.get("PLAYER_BUILD_NUMBER")
        if player_build_number and player_build_number != "null":
            player_build_number = player_build_number
        else:
            player_build_number = self.config.player_build_number
        if not client_name or not player_build_number or not player_build_version:
            raise PyframeException(
                f"请检查环境变量: CLIENT_NAME:{client_name} "
                f"PLAYER_BUILD_VERSION:{player_build_version} "
                f"PLAYER_BUILD_NUMBER:{player_build_number} 是否正确"
            )
        p4_version = self.get_p4_version()
        p4_user = self.get_p4_user()
        if not p4_version or not p4_user:
            raise PyframeException(f"获取P4信息失败: p4_version:{p4_version}, p4_user:{p4_user}")
        now_time = self.__get_now_time()
        suffix = self.__get_suffix()
        app_name = f"{client_name}_{player_build_version}_{player_build_number}_{now_time}_p4:{p4_version}_{p4_user}.{suffix}"
        log.info(f"app_name: {app_name}")
        return app_name

    def upload_nexus(self):
        app_name = self.__get_app_name()
        src = self.package
        dst = f"{self.config.nexus.upload_path}/{app_name}"
        self.nexus.upload(src, dst)
        env.set({"APP_NAME": app_name, "NEXUS_URL": dst})

    @staticmethod
    def get_app_name():
        return env.get("APP_NAME")

    @staticmethod
    def get_nexus_url():
        return env.get("NEXUS_URL")

    @staticmethod
    def get_user_list():
        user_list = []

        user = env.get("USER")
        if user:
            user_list.append(f"{user}@h3d.com.cn")

        build_user = env.get("BUILD_USER_EMAIL")
        if build_user:
            user_list.append(build_user)
        return user_list

    @staticmethod
    def get_errors():
        return env.get("ERRORS")

    @staticmethod
    def get_debug_mode():
        return env.get("DEBUG_MODE")

    @staticmethod
    def get_ftp_index():
        return env.get("FTP_INDEX")

    @staticmethod
    def get_platform_info():
        return env.get("PLATFORM_INFO")

    @staticmethod
    def get_app_language():
        return env.get("LANGUAGE")

    def get_msg(self):
        content = ""
        app_name = self.get_app_name()
        nexus_url = self.get_nexus_url()
        content += f"**下载地址:** [{app_name}]({nexus_url})\n" if app_name and nexus_url else ""
        language = self.get_app_language()
        content += f"**语言版本:** {language}\n" if language else ""
        debug_mode = self.get_debug_mode()
        content += f"**出包类型:** {debug_mode}\n" if debug_mode else ""
        ftp_index = self.get_ftp_index()
        content += f"**FTP资源服务器:** {ftp_index}\n" if ftp_index else ""
        platform_info = self.get_platform_info()
        content += f"**平台版本:** {platform_info}\n" if platform_info else ""
        log_url = self.get_log_url()
        content += f"**日志地址:** [编译日志]({log_url})\n" if log_url else ""
        p4_version = self.get_p4_version()
        content += f"**P4提交号:** {p4_version}\n" if p4_version else ""
        p4_user = self.get_p4_user()
        content += f"**提交人:** {p4_user}\n" if p4_user else ""
        errors = self.get_errors()
        content += f"**错误提示:** {errors}\n" if errors else ""
        if p4_version and errors:
            content += f"**p4 swarm:** [源码详情](http://swarm-frame.h3d.com.cn/changes/{p4_version})"
        return content


class BuildAndroid(BaseBuild):
    def __init__(self):
        super().__init__()


class BuildIOS(BaseBuild):
    def __init__(self):
        super().__init__()


class BuildTS(BaseBuild):
    def __init__(self):
        super().__init__()

    def run_client(self):
        cmd_str = f"{self.config.win_ue5_editor} "
        cmd_str += f"{self.config.project_client} "
        cmd_str += '-Game client -ExecCmds="automation quit"'
        ret = cmd.run_shell(cmds=[cmd_str], workdir=self.config.project_client_path, encoding=self.config.build_client_encoding)
        return ret

    def upload_logs(self):
        src = self.config.win_ue5_client_log
        log.info(f"src: {src}")
        if path_mgr.exists(src):
            # 日志重命名后再上传
            new_src = f"{self.config.win_ue5_log_path}/{env.pipeline.build_num()}_Log.txt"
            path_mgr.move(src, new_src)
            dst = advance.upload_pipeline_log(new_src)
            env.set({"ARTIFACT_DISTRIBUTE_LOG": dst})
            self.__analysis_log()
        else:
            log.warn("日志文件不存在，无法上传")

    def __analysis_log(self):
        pattern = re.compile(r"^.*?Error:.*?\[File:(.*)] \[Line:.*?(\d+)]")
        errors = []
        log_name = f"{self.config.win_ue5_log_path}/{env.pipeline.build_num()}_Log.txt"
        if not path_mgr.exists(log_name):
            return
        with open(log_name, "r", encoding="utf-8") as f:
            lines = f.readlines()
            for line in lines:
                ret = re.findall(pattern, line)
                if ret:
                    errors.extend(ret)
        log.info(f"errors: {errors}")
        rets = []
        for error in errors:
            path, line = error
            p4 = P4ClientBuildTrunk()
            ret = p4.blame(path, int(line))
            rets.append(ret)
        log.info(f"rets: {rets}")
        final_error = ""
        for ret in rets:
            depot_file = ret.get("depotFile")
            user = ret.get("user")
            final_error += f"{depot_file}: {user}: {line}\n"
        env.set({"ERRORS": final_error})

    @staticmethod
    def get_errors():
        return env.get("ERRORS")


class BuildSource:
    def __init__(self):
        self.config = config
        self.nexus = Nexus(self.config.nexus.user, self.config.nexus.password)

    @staticmethod
    def get_p4_user():
        return env.get("USER")

    @staticmethod
    def get_log_url():
        return env.get("ARTIFACT_DISTRIBUTE_LOG")

    @staticmethod
    def get_p4_version():
        return env.get("CHANGELIST")

    @staticmethod
    def get_errors():
        return env.get("ERRORS")

    @staticmethod
    def get_user_list():
        user_list = []

        user = env.get("USER")
        if user:
            user_list.append(f"{user}@h3d.com.cn")

        build_user = env.get("BUILD_USER_EMAIL")
        if build_user:
            user_list.append(build_user)
        return user_list

    def get_msg(self) -> str:
        log_url = self.get_log_url()
        content = f"**日志地址:** [编译日志]({log_url})\n" if log_url else ""
        p4_version = self.get_p4_version()
        content += f"**P4提交号:** {p4_version}\n" if p4_version else ""
        p4_user = self.get_p4_user()
        content += f"**提交人:** {p4_user}\n" if p4_user else ""
        errors = self.get_errors()
        content += f"**错误提示:** {errors}\n" if errors else ""
        if p4_version and errors:
            content += f"**p4 swarm:** [源码详情](http://swarm-frame.h3d.com.cn/changes/{p4_version})"
        return content

    def build_code(self):
        cmd_str = f"{self.config.win_ue5_run_uat} BuildGraph "
        cmd_str += f"-Script={self.config.win_ue5_build_xml} "
        cmd_str += f'-Target="Submit To Perforce for UGS" '
        cmd_str += f"-set:EditorTarget={self.config.editor_target} "
        cmd_str += f"-set:TargetPlatforms={self.config.target_platforms} "
        cmd_str += f"-set:Licensee={self.config.licensee} "
        cmd_str += f"-set:ArchiveStream={self.config.archive_stream} "
        cmd_str += f"-set:ArchiveName={self.config.archive_name} "
        cmd_str += f"-set:UProjectPath={self.config.project_u_project} "
        cmd_str += f"-p4port={self.config.p4.port} "
        cmd_str += f"-p4user={self.config.p4.username} "
        cmd_str += f"-p4pass={self.config.p4.password} "
        cmd_str += f"-p4client={self.config.p4.client} "
        cmd_str += f"-p4 -buildmachine -submit "
        ret = cmd.run_shell(
            cmds=[cmd_str],
            workdir=self.config.win_ue5_batch_path,
            encoding=self.config.build_client_encoding,
        )
        # TODO 临时处理，当编译完成上传P4时，忽略 ERROR: Change submission seemed to succeed, but did not look like it.类似错误
        if ret[0] != 0:
            self.__analysis_error()
            ret = (0, 0)
        return ret

    def upload_logs(self, log_name=None):
        if not log_name:
            log_name = self.config.nexus.log_name

        src = f"{self.config.win_ue5_log_path}/{log_name}"
        if not path_mgr.exists(src):
            log.warn("日志文件不存在，不需要上传")
        else:
            # 日志重命名后再上传
            new_src = f"{self.config.win_ue5_log_path}/{env.pipeline.build_num()}_{log_name}"
            path_mgr.move(src, new_src)
            dst = advance.upload_pipeline_log(new_src)
            env.set({"ARTIFACT_DISTRIBUTE_LOG": dst})

    def __analysis_error(self):
        p4_errors, _, detail_errors = self.__analysis_log()
        if detail_errors:
            error_msg = ""
            for detail_error in detail_errors:
                error_msg += f"{detail_error}\n"
                if len(error_msg) >= 2500:
                    break
            env.set({"ERRORS": error_msg})
            if error_msg:
                raise PyframeException(f"源码预编译失败, 请检查代码错误:\n{error_msg}")
        if p4_errors:
            error_msg = ""
            for p4_error in p4_errors:
                if "submission seemed to succeed, but did not look like it." in p4_error:
                    continue
                error_msg += p4_error
            if error_msg:
                raise PyframeException(f"上传编译结果到P4失败:{error_msg}")

    def __analysis_log(self):
        p4_error_pattern = re.compile(r"^ERROR: Change (.*?.)$")
        code_error_pattern = re.compile(r"^BUILD FAILED: (.*?.)$")
        detail_error_pattern = re.compile(r"^.*?.cpp\(\d+\): error.*?$")  # 代码详细错误
        p4_errors = []
        code_errors = []
        detail_errors = []
        log_path = os.path.join(self.config.win_ue5_log_path, "Log.txt")
        if not path_mgr.exists(log_path):
            return p4_errors, code_errors
        with open(log_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
            for line in lines:
                ret = re.findall(p4_error_pattern, line)
                if ret:
                    p4_errors.extend(ret)
                ret = re.findall(code_error_pattern, line)
                if ret:
                    code_errors.extend(ret)
                ret = re.findall(detail_error_pattern, line)
                if ret:
                    detail_errors.extend(ret)

        log.info(f"p4_errors: {p4_errors} code_errors: {code_errors} detail_errors: {detail_errors}")
        return p4_errors, code_errors, detail_errors
