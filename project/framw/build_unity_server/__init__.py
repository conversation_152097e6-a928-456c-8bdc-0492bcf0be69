# coding=utf-8
from frame import *


class Config:
    P4_2011_CONFIG = {
        "host": "p4.com:2011",
        "username": "framw_test",
        "password": "framw123",
        "client": f"framw-unity-server-{common.get_host_ip()}-{env.pipeline.job_name()}".replace("/", "-"),
        "root": env.pipeline.workspace(),
        # "views": [
        #     f"//h3d_mobile/unity_lua/trunk/server/...  //{_p4_client}/...",
        # ]
    }
    NEXUS_CONFIG = {"username": "framw-robot", "password": "framw-robot"}
    RESISTRY_CONFIG = {"registry": "registry.h3d.com.cn", "username": "tac", "password": "Tac12345"}


class ConfigDev:
    P4_2011_CONFIG = {
        "host": "p4.com:2011",
        "username": "framw_test",
        "password": "framw123",
        "client": f"framw-unity-server-{common.get_host_ip()}-{env.pipeline.build_num()}",
        "root": env.pipeline.workspace(),
    }
    NEXUS_CONFIG = {"username": "framw-robot", "password": "framw-robot"}
    RESISTRY_CONFIG = {"registry": "registry.h3d.com.cn", "username": "tac", "password": "Tac12345"}


config = Config()
if os.getenv("DEPLOY_ENV") == "dev":
    config = ConfigDev()
