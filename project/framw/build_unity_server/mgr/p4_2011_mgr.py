# coding=utf-8
from frame import *
from project.framw.build_unity_server import config


class P4COM2011:
    def __init__(self, branch: str):
        self.p4 = P4Client(
            host=config.P4_2011_CONFIG.get("host"),
            username=config.P4_2011_CONFIG.get("username"),
            password=config.P4_2011_CONFIG.get("password"),
            client=config.P4_2011_CONFIG.get("client"),
            stream=None,
        )
        self.branch = branch
        self.view = [f"{branch}  //{self.p4.client}/server/..."]
        self.p4.set_view(views=self.view)
        self.p4.set_options(allwrite=True)
        self.p4.set_root(path=config.P4_2011_CONFIG.get("root"))

    def sync_all(self, force: bool):
        """
        拉取p4
        """
        self.p4.sync_all(force=force)

    def sync_list(self, force: bool):
        """
        拉取p4指定路径
        """
        path = [self.branch]
        self.p4.sync_list(path_list=path, force=force)

    def get_latest_user_changelist(self, view: str):
        """
        获取最后一次提交人和提交号
        """
        user, changelist = None, None
        try:
            change = self.p4.get_latest_changes(path=view)
            user = change.get("user")
            changelist = change.get("change")
        except Exception as e:
            log.error(e)

        return user, changelist
