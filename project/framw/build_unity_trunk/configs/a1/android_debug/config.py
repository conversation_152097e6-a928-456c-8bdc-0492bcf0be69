import os

from frame import env


class BuildTrunkConfigDev:
    class P4:
        def __init__(self, project_root):
            self.port = "p4.com:2002"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "a1_android_debug_test"
            self.root = os.path.join(project_root, "client")
            self.view = [f"//h3d_mobile/products/A1/trunk/client/... //{self.client}/..."]

            self.abs_path = "//h3d_mobile/unity_lua/trunk/client/baiye.keystore"

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/dev-productivity/test/a1_debug"
            self.user = "productivity-robot"
            self.password = "productivity-robot"

    def __init__(self):
        self.project_root_path = env.pipeline.workspace()
        self.build_class = "BuildA1Android"
        self.unity_path = r"C:\Program Files\Unity2018436\Editor\Unity.exe"
        self.build_version = "1.0.0"
        self.target_name = r"Android\framw_0.0.1_100100.apk"
        self.unity_build_pipe_name = "framw-unity-android-debug"
        self.build_type = "Debug"

        self.checkout_package_path = os.path.join(self.project_root_path, "client", "build")

        self.p4 = self.P4(self.project_root_path)
        self.nexus = self.Nexus()


class BuildTrunkConfigProd:
    class P4:
        def __init__(self, project_root):
            self.port = "p4.com:2011"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "products-A1-android-debug"
            self.root = os.path.join(project_root, "client")
            self.view = [f"//h3d_mobile/products/A1/trunk/client/... //{self.client}/..."]

            self.abs_path = "//h3d_mobile/products/A1/trunk/client/PublishResources/autoabs/Android/..."

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/framw/unity-lua-trunk/products/A1/android/debug"
            self.user = "framw-robot"
            self.password = "framw-robot"

    def __init__(self):
        self.project_root_path = env.pipeline.workspace()
        self.build_class = "BuildA1Android"
        self.unity_path = r"F:\Program Files\2018.4.36f1\Editor\Unity.exe"
        self.build_version = "1.0.0"
        self.target_name = r"Android\framw_0.0.1_100100.apk"
        self.unity_build_pipe_name = "framw-unity-android-debug"
        self.build_type = "Debug"

        self.checkout_package_path = os.path.join(self.project_root_path, "client", "build")

        self.p4 = self.P4(self.project_root_path)
        self.nexus = self.Nexus()


def init_config():
    if os.getenv("DEPLOY_ENV", "dev") == "dev":
        return BuildTrunkConfigDev

    return BuildTrunkConfigProd
