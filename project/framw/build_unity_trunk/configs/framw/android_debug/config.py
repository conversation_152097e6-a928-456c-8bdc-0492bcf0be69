import os

from frame import env


class BuildTrunkConfigDev:
    class P4:
        def __init__(self, project_root):
            self.port = "p4.com:2002"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "framw_android_debug_test"
            self.root = rf"{project_root}\client"
            self.view = [f"//h3d_mobile/unity_lua/trunk/client/... //{self.client}/..."]

            self.abs_path = "//h3d_mobile/unity_lua/trunk/client/baiye.keystore"
            self.local_abs_path = "//h3d_mobile/unity_lua/trunk/client/PublishResources/autoabs/Android/..."

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/dev-productivity/test/unity-framw/debug"
            self.user = "productivity-robot"
            self.password = "productivity-robot"

    def __init__(self):
        self.project_root_path = env.pipeline.workspace()
        self.build_class = "BuildAndroid"
        self.unity_path = r"C:\Program Files\Unity 2021.3.2f1\Editor\Unity.exe"
        self.build_version = "1.0.0"
        self.target_name = "framw.apk"
        self.unity_build_pipe_name = "*parallel*framw-android-debug"
        self.build_type = "Debug"

        # android
        self.key_store_rel_path = "baiye.keystore"
        self.java_path = r"C:\Android\OpenJDK\bin\java.exe"
        self.gradle_path = r"C:\Android\gradle-6.5.1-bin\gradle-6.5.1\lib\gradle-launcher-6.5.1.jar"

        self.checkout_package_path = rf"{self.project_root_path}\client\build\build-android-output"

        self.p4 = self.P4(self.project_root_path)
        self.nexus = self.Nexus()


class BuildTrunkConfigProd:
    pass


def init_config():
    if os.getenv("DEPLOY_ENV", "dev") == "dev":
        return BuildTrunkConfigDev

    return BuildTrunkConfigProd
