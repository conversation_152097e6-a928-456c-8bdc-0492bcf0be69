import os

from frame import env


class BuildTrunkConfigDev:
    class P4:
        def __init__(self, project_root):
            self.port = "p4.com:2002"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "framw_win64_debug_test"
            self.root = rf"{project_root}\client"
            self.view = [f"//h3d_mobile/unity_lua/trunk/client/... //{self.client}/..."]

            self.abs_path = "//h3d_mobile/unity_lua/trunk/client/baiye.keystore"

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/dev-productivity/test/unity-framw/win64/debug"
            self.user = "productivity-robot"
            self.password = "productivity-robot"

    def __init__(self):
        self.project_root_path = env.pipeline.workspace()
        self.build_class = "BuildWin64"
        self.unity_path = r"C:\Program Files\Unity 2021.3.2f1\Editor\Unity.exe"
        self.build_version = "1.0.0"
        self.target_name = "framw.zip"
        self.unity_build_pipe_name = "*parallel*framw-windows-debug"
        self.build_type = "Debug"

        self.checkout_package_path = rf"{self.project_root_path}\client\build\build-win64-output"

        self.p4 = self.P4(self.project_root_path)
        self.nexus = self.Nexus()


class BuildTrunkConfigProd:
    class P4:
        def __init__(self, project_root):
            self.port = "p4.com:2011"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "jenkins-framw-unity-win64-119-trunk-debug-parallel"
            self.root = rf"{project_root}\client"
            self.view = [f"//h3d_mobile/unity_lua/trunk/client/... //{self.client}/..."]

            self.abs_path = "//h3d_mobile/unity_lua/trunk/client/PublishResources/autoabs/StandaloneWindows64/..."
            self.local_abs_path = "//h3d_mobile/unity_lua/trunk/client/PublishResources/autoabs/StandaloneWindows64/..."

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/framw/unity-lua-trunk/win64/debug"
            self.user = "framw-robot"
            self.password = "framw-robot"

    def __init__(self):
        self.project_root_path = env.pipeline.workspace()
        self.build_class = "BuildWin64"
        self.unity_path = "E:\\Unity2021.3.2f1\\Editor\\Unity.exe"
        self.build_version = "1.0.0"
        self.target_name = "framw.zip"
        self.unity_build_pipe_name = "*parallel*framw-windows-debug"
        self.build_type = "Debug"

        self.checkout_package_path = rf"{self.project_root_path}\client\build\build-win64-output"

        self.p4 = self.P4(self.project_root_path)
        self.nexus = self.Nexus()


def init_config():
    if os.getenv("DEPLOY_ENV", "dev") == "dev":
        return BuildTrunkConfigDev

    return BuildTrunkConfigProd
