from frame import PyframeException, log

from project.framw.build_unity_trunk import config
from project.framw.build_unity_trunk.scripts.build.build_instance import a1_build
from project.framw.build_unity_trunk.scripts.build.build_instance import framw_build


class BuildFactory:
    """
    工厂类
    根据配置文件动态初始化类
    """

    def __new__(cls, *args, **kwargs):
        build_class = config.build_class
        log.info(f"build class: {build_class}")
        if build_class == "BuildAndroid":
            return framw_build.BuildAndroid()
        elif build_class == "BuildIOS":
            return framw_build.BuildIOS()
        elif build_class == "BuildWin64":
            return framw_build.BuildWin64()
        elif build_class == "BuildA1Android":
            return a1_build.BuildA1Android()
        elif build_class == "BuildA1IOS":
            return a1_build.BuildA1IOS()
        elif build_class == "BuildA1Win64":
            return a1_build.BuildA1Win64()
        else:
            log.error(f"build class: {build_class} not found")
            raise PyframeException(f"build_class: {build_class}未定义")
