# coding=utf-8
import abc
import time

from frame import *
from project.framw.build_unity_trunk import config
from project.framw.build_unity_trunk.scripts.log.build_log_tail import BuildLogTail


class Platform:
    # the process to close
    _ProcessList = []

    def kill_process(self):
        proc_mgr.kill_procs(names=self._ProcessList)


class Windows(Platform):
    _ProcessList = ["Unity.exe", "Unity.Licensing.Client.exe"]


class Unix(Platform):
    _ProcessList = ["Unity", "Unity.Licensing.Client"]


class BuildBase(metaclass=abc.ABCMeta):
    # link
    _SettingsDir = "ProjectSettings"
    _PackagesDir = "Packages"
    _BuildDir = "build"
    _PackConfigDir = "PublishResources/app_config"
    _EditorDir = "Assets/Editor"
    _EditorDataDir = "Assets/EditorData"
    _EngineDir = "Assets/Engine"
    _FrameworkDir = "Assets/Framework"
    _PluginsDir = "Assets/Plugins"
    _ScenesDir = "Assets/Scenes"
    _TMPDir = "Assets/TextMesh Pro"
    _ProductDemoScriptsDir = "Assets/ProductDemo/Scripts"
    _URPAssetsDir = "Assets/URP"
    _UWADir = "Assets/UWA"
    _IFixToolKitDir = "IFixToolKit"

    # build
    _UnityMethodName = "H3DBuild.ContentPipeline.BuildPlayerCmd"
    _BuildSharedFileFromUnity = "UnityBuildSharedFile"

    PLATFORM = BUILD_TARGET = None

    def __init__(self):
        self.trunk_config = config

        output_dir = f"build-{self.PLATFORM}-output"
        self.res_project_path = f"{self.trunk_config.project_root_path}/client"
        self.app_project_path = f"{self.trunk_config.project_root_path}/clientTempForPureAPP"

        self.build_res_log_path = f"{self.res_project_path}/build/{output_dir}/BuildRes_{os.getenv('BUILD_NUMBER')}.log"
        self.build_app_log_path = f"{self.app_project_path}/build/{output_dir}/BuildApp_{os.getenv('BUILD_NUMBER')}.log"

        self.old_res_in_app_path = os.path.join(self.app_project_path, "Assets", "StreamingAssets")
        self.temp_save_res_path = os.path.join(self.res_project_path, "TempStreamingAssets")

        self.target_output_abs_path = f"{self.res_project_path}/build/{output_dir}/{self.trunk_config.target_name}"

        self.begin_build_time = time.strftime("%Y-%m-%d_%H_%M_%S", time.localtime())
        self.nexus = Nexus(self.trunk_config.nexus.user, self.trunk_config.nexus.password)

    def _make_link(self, relative_path):
        path_mgr.soft_link(src="{}/{}".format(self.res_project_path, relative_path), dst="{}/{}".format(self.app_project_path, relative_path))

    def make_link(self):
        self._make_link(self._SettingsDir)
        self._make_link(self._PackagesDir)
        self._make_link(self._PackConfigDir)
        # asset目录考虑绕开资源目录建立链接
        self._make_link(self._EditorDir)
        self._make_link(self._EditorDataDir)
        self._make_link(self._EngineDir)
        self._make_link(self._FrameworkDir)
        self._make_link(self._PluginsDir)
        self._make_link(self._ScenesDir)
        self._make_link(self._TMPDir)
        self._make_link(self._ProductDemoScriptsDir)
        self._make_link(self._URPAssetsDir)
        self._make_link(self._UWADir)
        self._make_link(self._IFixToolKitDir)

    def assets_copy(self, asset_path):
        for root, dirs, files in os.walk(self.temp_save_res_path):
            for d in dirs:
                path_mgr.move(os.path.join(root, d), asset_path)
            for f in files:
                # if os.path.exists(os.path.join(asset_path, f)):
                #     path_mgr.rm(os.path.join(asset_path, f))

                path_mgr.move(os.path.join(root, f), os.path.join(asset_path, f))

    def build_app(self, queue=None):
        path_mgr.rm(self.old_res_in_app_path)
        path_mgr.rm(self.build_app_log_path)

        log_tail = BuildLogTail(self.build_app_log_path, "[BuildApp]")
        log_tail.start()

        command = f'"{self.trunk_config.unity_path}" -quit -batchmode -logfile {self.build_app_log_path} -projectPath {self.app_project_path} -executeMethod {self._UnityMethodName} -buildName {self.trunk_config.unity_build_pipe_name} -buildTarget {self.BUILD_TARGET}'
        ret = cmd.run_shell(cmds=[command], verbose=cmd.Verbose.LOG)
        log.info(f"build app ret {ret}")

        log_tail.stop()
        if ret[0] != 0 and queue:
            queue.put("app")
        return ret[0]

    def build_res(self, queue=None):
        path_mgr.rm(self.build_res_log_path)
        path_mgr.rm(self.target_output_abs_path)

        log_tail = BuildLogTail(self.build_res_log_path, "[BuildRes]")
        log_tail.start()

        command = f'"{self.trunk_config.unity_path}" -quit -batchmode -logfile {self.build_res_log_path} -projectPath {self.res_project_path} -executeMethod {self._UnityMethodName} -buildName {self.trunk_config.unity_build_pipe_name} -buildTarget {self.BUILD_TARGET}'
        ret = cmd.run_shell(cmds=[command], verbose=cmd.Verbose.LOG)
        log.info(f"build res ret {ret}")

        log_tail.stop()
        if ret[0] != 0 and queue:
            queue.put("资源")
        return ret[0]

    @abc.abstractmethod
    def merge_app_and_res(self):
        pass

    def checkout_package(self):
        check_file = os.path.join(self.trunk_config.checkout_package_path, self.trunk_config.target_name)
        if not path_mgr.exists(check_file):
            log.info(f"{check_file} not exists")
            raise PyframeException(f"{check_file}不存在")

        log.info(f"{check_file} exists")

    @abc.abstractmethod
    def upload(self):
        pass

    def get_artifact_prefix(self):
        changelist, author = env.get("changelist"), env.get("user")
        artifact_prefix = f"client_{self.trunk_config.build_version}_{self.begin_build_time}_p4_{changelist}_{author}"
        return artifact_prefix

    def upload_to_nexus(self, artifact_suffix):
        artifact_prefix = self.get_artifact_prefix()
        artifact_name = f"{artifact_prefix}{artifact_suffix}"
        self.nexus.upload(src=self.target_output_abs_path, dst=f"{self.trunk_config.nexus.upload_path}/{artifact_name}")
        env.set({"upload_url": f"{self.trunk_config.nexus.upload_path}/{artifact_name}"})

    def upload_logs(self):
        app_remote_log_url = f"{self.trunk_config.nexus.upload_path}/log/BuildApp_{os.getenv('BUILD_NUMBER')}.log"
        res_remote_log_url = f"{self.trunk_config.nexus.upload_path}/log/BuildRes_{os.getenv('BUILD_NUMBER')}.log"
        if os.path.exists(self.build_app_log_path):
            self.nexus.upload(self.build_app_log_path, app_remote_log_url)
            env.set({"app_log_url": app_remote_log_url})

        if os.path.exists(self.build_res_log_path):
            self.nexus.upload(self.build_res_log_path, res_remote_log_url)
            env.set({"res_log_url": res_remote_log_url})
