# import os
#
# from frame.log.log import log
# from frame.p4.p4 import Perforce
# from project.framw.build_unity_trunk import config
#
#
# def ignore_file(path):
#     ignore_list = []
#     input_abs_path = os.path.abspath(path)
#     for node in os.listdir(input_abs_path):
#         abs_node_data = os.path.join(input_abs_path, node)
#         if os.path.isfile(abs_node_data):
#             ignore_list.append(abs_node_data)
#             continue
#
#         ignore_list.append(os.path.join(abs_node_data, node))
#         ignore_list.append(os.path.join(abs_node_data, node + ".manifest"))
#
#     return ignore_list
#
#
# def check_ab(path):
#     ab_files = {}
#     ignore_files = ignore_file(path)
#     input_abs_path = os.path.abspath(path)
#     for root, _, files in os.walk(input_abs_path):
#         for file in files:
#             abs_file_path = os.path.join(root, file)
#             file, suffix = os.path.splitext(abs_file_path)
#             if abs_file_path in ignore_files or file in ignore_files:
#                 continue
#
#             if suffix in [".manifest", ".h3d"]:
#                 if not ab_files.get(file):
#                     ab_files[file] = [abs_file_path]
#                     continue
#
#                 ab_files[file].append(abs_file_path)
#                 if os.path.exists(file):
#                     ab_files[file].append(file)
#
#                 # 去除完整的ab包
#                 if len(ab_files.get(file)) == 3:
#                     ab_files.pop(file)
#
#     return ab_files
#
#
# def sync_ab(ab_dict):
#     p4_config = config.p4
#     p4 = Perforce(
#         port=p4_config.port,
#         user=p4_config.username,
#         password=p4_config.password,
#         client=p4_config.client,
#         logger=log
#     )
#     p4.login()
#     for local_ab in ab_dict.keys():
#         _, ab_name = os.path.split(local_ab)
#         files_in_depot = p4.p4.run_files("-e", f"{p4_config.abs_path}/{ab_name}*")
#         for ab in files_in_depot:
#             p4.update(ab.get("depotFile"), force_sync=True)
