# coding=utf-8

from frame import *
from project.framw.build_unity_trunk import config


class P4Mgr:
    def __init__(self):
        self.p4 = P4Client(host=config.p4.port, username=config.p4.username, password=config.p4.password, client=config.p4.client)
        self.p4.set_view(views=config.p4.view)
        self.p4.set_root(config.p4.root)

    def get_latest_changes(self):
        """
        获取资源分支的最新changelist
        """
        changes = []
        for view in config.p4.view:
            path = view.split(" ")[0]
            ret = self.p4.get_latest_changes(path=path)
            changes.append(ret)
        # 对changes按照changelist进行排序
        changes.sort(key=lambda x: int(x["change"]), reverse=True)
        log.info(f"latest change: {changes[0]}")
        return changes[0]

    def sync_all(self, changelist: str, force: bool):
        """
        拉取p4
        """
        self.p4.sync_all(changelist=changelist, force=force)
