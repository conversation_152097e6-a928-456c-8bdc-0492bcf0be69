node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "a1-android_arm64"
        }
    }

    parameters {
        booleanParam(name: 'force', defaultValue: false, description: '是否强更p4')
    }

    options {
        disableConcurrentBuilds()
        timeout(time:1, unit: 'HOURS')
    }

    stages {
        stage("初始化python环境") {
            steps {
                script {
                    // 脚本中需要用到该环境变量, 用来初始化配置文件
                    env.SCRIPT_CONFIG = "a1/android_arm64"
                    bat(
                        script: """
                            python3 -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn
                        """
                    )
                }
            }
        }
        stage("sync p4 data") {
            steps {
                script {
                    bat(script: "python3 framw.py to_lua --job=sync_p4 --force=${params.force}")
                }
            }
        }

        stage("build") {
            steps {
                script {
                    bat(script: "python3 framw.py to_lua --job=build")
                }
            }
        }
        stage("upload to nexus") {
            steps {
                script {
                    bat(script: "python3 framw.py to_lua --job=upload")
                }
            }
        }
    }
    post {
        success {
            script {
                bat label: '成功后通知', script:
                '''
                python3 framw.py to_lua --job=on_success
                '''
            }
        }
        failure {
            script {
                bat label: '失败后通知', script:
                '''
                python3 framw.py to_lua --job=on_failure
                '''
            }
        }
        unstable {
            script {
                bat label: '不稳定通知', script:
                '''
                python3 framw.py to_lua --job=on_unstable
                '''
            }
        }
    }
}