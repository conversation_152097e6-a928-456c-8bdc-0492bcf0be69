import os

from frame import env


class ToLuaConfigDev:
    class P4:
        def __init__(self, build_path):
            self.port = "p4.com:2002"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "a1_android_armv7_test"
            self.root = build_path
            self.view = [f"//h3d_mobile/products/A1/tools/tolua_runtime/... //{self.client}/..."]

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/dev-productivity/test/unity-framw/to_lua/a1_android_armv7"
            self.user = "productivity-robot"
            self.password = "productivity-robot"

    def __init__(self):
        self.build_path = env.pipeline.workspace()
        self.build_class = "BuildAndroidArmV7"
        self.ndk_path = r"C:\Android\android-ndk-r21d-windows-unity2021\android-ndk-r21d\ndk-build.cmd"

        self.p4 = self.P4(self.build_path)
        self.nexus = self.Nexus()


class ToLuaConfigProd:
    class P4:
        def __init__(self, build_path):
            self.port = "p4.com:2011"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "a1_android_armv7_prod"
            self.root = build_path
            self.view = [f"//h3d_mobile/products/A1/tools/tolua_runtime/... //{self.client}/..."]

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/framw/products-a1/tolua/client/arm"
            self.user = "framw-robot"
            self.password = "framw-robot"

    def __init__(self):
        self.build_path = env.pipeline.workspace()
        self.build_class = "BuildAndroidArmV7"
        self.ndk_path = "D:/android-ndk-r10e/ndk-build.cmd"

        self.p4 = self.P4(self.build_path)
        self.nexus = self.Nexus()


def init_config():
    if os.getenv("DEPLOY_ENV", "dev") == "dev":
        return ToLuaConfigDev

    return ToLuaConfigProd
