import os

from frame import env


class ToLuaConfigDev:
    class P4:
        def __init__(self, build_path):
            self.port = "p4.com:2002"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "framw_android_arm64_test"
            self.root = build_path
            self.view = [f"//h3d_mobile/unity_lua/tools/tolua_runtime/... //{self.client}/..."]

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/dev-productivity/test/unity-framw/to_lua/android_arm64"
            self.user = "productivity-robot"
            self.password = "productivity-robot"

    def __init__(self):
        self.build_path = env.pipeline.workspace()
        self.build_class = "BuildAndroidArm64"
        self.ndk_path = r"C:\Android\android-ndk-r21d-windows-unity2021\android-ndk-r21d\ndk-build.cmd"

        self.p4 = self.P4(self.build_path)
        self.nexus = self.Nexus()


class ToLuaConfigProd:
    class P4:
        def __init__(self, build_path):
            self.port = "p4.com:2011"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "framw_android_arm64_prod"
            self.root = build_path
            self.view = [f"//h3d_mobile/unity_lua/tools/tolua_runtime/... //{self.client}/..."]

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/framw/tolua/client/arm64"
            self.user = "framw-robot"
            self.password = "framw-robot"

    def __init__(self):
        self.build_path = env.pipeline.workspace()
        self.build_class = "BuildAndroidArm64"
        self.ndk_path = "D:/android-ndk-r10e/ndk-build.cmd"

        self.p4 = self.P4(self.build_path)
        self.nexus = self.Nexus()


def init_config():
    if os.getenv("DEPLOY_ENV", "dev") == "dev":
        return ToLuaConfigDev

    return ToLuaConfigProd
