import os

from frame import env


class ToLuaConfigDev:
    class P4:
        def __init__(self, build_path):
            self.port = "p4.com:2002"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "framw_mac_test"
            self.root = build_path
            self.view = [f"//h3d_mobile/unity_lua/tools/tolua_runtime/... //{self.client}/..."]

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/dev-productivity/test/unity-framw/to_lua/framw_mac"
            self.user = "productivity-robot"
            self.password = "productivity-robot"

    def __init__(self):
        self.build_path = env.pipeline.workspace()
        self.build_class = "BuildMac"

        self.p4 = self.P4(self.build_path)
        self.nexus = self.Nexus()
        self.target_path = "~/Library/Developer/Xcode/DerivedData/libtolua-duybbcxcjzenexcoawbcqxeqsiej/Build/Products"


class ToLuaConfigProd:
    class P4:
        def __init__(self, build_path):
            self.port = "p4.com:2011"
            self.username = "framw_test"
            self.password = "framw123"
            self.client = "framw_mac_prod"
            self.root = build_path
            self.view = [f"//h3d_mobile/unity_lua/tools/tolua_runtime/... //{self.client}/..."]

    class Nexus:
        def __init__(self):
            self.upload_path = "http://nexus.h3d.com.cn/repository/framw/tolua/client/mac"
            self.user = "framw-robot"
            self.password = "framw-robot"

    def __init__(self):
        self.build_path = env.pipeline.workspace()
        self.build_class = "BuildMac"

        self.p4 = self.P4(self.build_path)
        self.nexus = self.Nexus()
        self.target_path = "~/Library/Developer/Xcode/DerivedData/libtolua-grltvqjbzoynofecqsxrgdznjtuk/Build/Products"


def init_config():
    if os.getenv("DEPLOY_ENV", "dev") == "dev":
        return ToLuaConfigDev

    return ToLuaConfigProd
