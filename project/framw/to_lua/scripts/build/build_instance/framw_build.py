from frame import cmd

from project.framw.to_lua.scripts.build.build import ToLuaBuild, Windows, Mac


class BuildAndroidArm64(ToLuaBuild, Windows):
    _LOCAL_NAME = _UPLOAD_NAME = "libtolua.so"
    _COMPRESS_DIR = "Plugins/Android/libs/arm64-v8a"

    def build(self):
        cmd.run_shell(
            cmds=[f"SET NDK_BUILD={self.to_lua_config.ndk_path}", f"SET BUILD_PATH={self.to_lua_config.build_path}", "call build_android_arm64.bat"],
            workdir=self._SCRIPT_WORKDIR,
        )


class BuildAndroidArmV7(ToLuaBuild, Windows):
    _LOCAL_NAME = _UPLOAD_NAME = "libtolua.so"
    _COMPRESS_DIR = "Plugins/Android/libs/armeabi-v7a"

    def build(self):
        cmd.run_shell(
            cmds=[f"SET NDK_BUILD={self.to_lua_config.ndk_path}", f"SET BUILD_PATH={self.to_lua_config.build_path}", "call build_android_arm.bat"],
            workdir=self._SCRIPT_WORKDIR,
        )


class BuildAndroidX86(ToLuaBuild, Windows):
    _LOCAL_NAME = _UPLOAD_NAME = "libtolua.so"
    _COMPRESS_DIR = "Plugins/Android/libs/x86"

    def build(self):
        cmd.run_shell(
            cmds=[f"SET NDK_BUILD={self.to_lua_config.ndk_path}", f"SET BUILD_PATH={self.to_lua_config.build_path}", "call build_android_x86.bat"],
            workdir=self._SCRIPT_WORKDIR,
        )


class BuildAndroidX8664(ToLuaBuild, Windows):
    _LOCAL_NAME = _UPLOAD_NAME = "libtolua.so"
    _COMPRESS_DIR = "Plugins/Android/libs/x86_64"

    def build(self):
        cmd.run_shell(
            cmds=[f"SET NDK_BUILD={self.to_lua_config.ndk_path}", f"SET BUILD_PATH={self.to_lua_config.build_path}", "call build_android_x86_64.bat"],
            workdir=self._SCRIPT_WORKDIR,
        )


class BuildWin64(ToLuaBuild, Windows):
    _LOCAL_NAME = "x64"
    _UPLOAD_NAME = f"tolua.{_LOCAL_NAME}"
    _COMPRESS_DIR = "sln/tolua"

    def build(self):
        cmd.run_shell(
            cmds=[
                f"SET BUILD_PATH={self.to_lua_config.build_path}",
                f'SET DEVENV="{self.to_lua_config.build.devenv_path}"',
                f'SET PALAT="{self.to_lua_config.build.target}"',
                "call build_win64_vs.bat",
            ],
            workdir=self._SCRIPT_WORKDIR,
        )


class BuildIOS(ToLuaBuild, Mac):
    _LOCAL_NAME = "libtolua.a"
    _COMPRESS_DIR = "build_ios/tolua"

    def build(self):
        cmd.run_shell(
            cmds=[
                f"export BUILD_PATH={self.to_lua_config.build_path}",
                f"export TARGET_PATH={self.to_lua_config.target_path}",
                "bash ./build_ios_xcode.sh",
            ],
            workdir=self._SCRIPT_WORKDIR,
        )


class BuildMac(ToLuaBuild, Mac):
    _LOCAL_NAME = "tolua.bundle"
    _COMPRESS_DIR = "build_mac/libtolua"

    def build(self):
        cmd.run_shell(
            cmds=[
                f"export BUILD_PATH={self.to_lua_config.build_path}",
                f"export TARGET_PATH={self.to_lua_config.target_path}",
                "bash ./build_mac_xcode.sh",
            ],
            workdir=self._SCRIPT_WORKDIR,
        )
