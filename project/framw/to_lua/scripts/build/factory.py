from frame import PyframeException, log

from project.framw.to_lua.scripts import to_lua_config
from project.framw.to_lua.scripts.build.build_instance import framw_build


class ToLuaFactory:
    def __new__(cls, *args, **kwargs):
        # 显式实现。好处: 1. 方便阅读 2. IDE支持跳转 3. 错误异常处理
        build_class = to_lua_config.build_class
        log.info(f"build class: {build_class}")
        if build_class == "BuildAndroidArm64":
            return framw_build.BuildAndroidArm64()
        elif build_class == "BuildIOS":
            return framw_build.BuildIOS()
        elif build_class == "BuildAndroidArmV7":
            return framw_build.BuildAndroidArmV7()
        elif build_class == "BuildAndroidX86":
            return framw_build.BuildAndroidX86()
        elif build_class == "BuildAndroidX8664":
            return framw_build.BuildAndroidX8664()
        elif build_class == "BuildIOS":
            return framw_build.BuildIOS()
        elif build_class == "BuildMac":
            return framw_build.BuildMac()
        elif build_class == "BuildWin64":
            return framw_build.BuildWin64()
        else:
            log.error(f"build class: {build_class} not found")
            raise PyframeException(f"build_class: {build_class}未定义")
