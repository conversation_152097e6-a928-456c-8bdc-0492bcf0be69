@echo on

chcp 65001 

cd %BUILD_PATH%

echo ����Ŀ¼
SET BASE_DIR=%cd%

echo android����Ŀ¼
cd android/jni

echo alias
SET ndk_build=%NDK_BUILD%

echo ABI
SET ABI="x86"

echo ����Ŀ¼
SET TARGET_DIR=x86

echo ����Դ�ļ���ͬһ��Ŀ¼
rm ./src  -rf
mkdir src
cp %BASE_DIR%/lua-5.4.4/src/* ./src -rf
cp %BASE_DIR%/lua_dc/src ./src/lua_dc -rf

cp %BASE_DIR%/tolua.c ./src -rf
cp %BASE_DIR%/tolua.h ./src -rf

cp %BASE_DIR%/encry ./src -rf 

cp %BASE_DIR%/cjson ./src -rf 

call %ndk_build% clean APP_ABI=%ABI%

call %ndk_build% ^
    NDK_PROJECT_PATH=.  ^
    APP_BUILD_SCRIPT=Android.mk ^
    NDK_APPLICATION_MK=Application.mk ^
    APP_PLATFORM=android-21 ^
    APP_ABI=%ABI%

echo ������Ŀ��Ŀ¼
cp  libs/%TARGET_DIR%/libtolua.so  %BASE_DIR%/Plugins/Android/libs/%TARGET_DIR% -f

echo �����ֳ�
call %ndk_build% clean APP_ABI=%ABI%
rm ./src -rf
rm ./obj -rf
rm ./libs -rf

cd %BASE_DIR%
