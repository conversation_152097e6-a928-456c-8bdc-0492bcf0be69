#!/bin/bash

set -v

cd $BUILD_PATH

XPROJ=libtolua.xcodeproj
PLATFORM=arm64
BUILD_TYPE=Release
SDK=macosx
SCHEME=libtolua

TARGET_PATH=${TARGET_PATH}/$BUILD_TYPE
TARGET_NAME=tolua.bundle

WORK_PATH=build_mac/libtolua

rm -Rf $WORK_PATH/libtolua/lua_dc
rm -Rf $WORK_PATH/libtolua/lua-5.3.5
rm -Rf $WORK_PATH/libtolua/encry
rm -Rf $WORK_PATH/libtolua/cjson
rm -Rf $WORK_PATH/libtolua/tolua.h
rm -Rf $WORK_PATH/libtolua/tolu.c


cp -r lua-5.3.5 $WORK_PATH/libtolua
cp -r lua_dc    $WORK_PATH/libtolua
cp -r encry     $WORK_PATH/libtolua
cp -r cjson     $WORK_PATH/libtolua
cp -f tolua.h   $WORK_PATH/libtolua
cp -f tolua.c   $WORK_PATH/libtolua


cd $WORK_PATH

xcodebuild clean -project $XPROJ

xcodebuild build -project $XPROJ -scheme $SCHEME -configuration $BUILD_TYPE -sdk $SDK 

if [ ! -d $TARGET_PATH/$TARGET_NAME ]; then
    echo "build err! no $TARGET_PATH/$TARGET_NAME"
    exit 1
fi


rm -Rf $TARGET_NAME 
cp -r  $TARGET_PATH/$TARGET_NAME ./




