from frame import wechat, advance
from project.framw.to_lua.scripts.build.factory import ToLuaFactory
from project.framw.to_lua.scripts.p4 import ToLuaP4


def sync_p4(**kwargs):
    force = kwargs.get("force")
    p4 = ToLuaP4()
    p4.update(force == "true")


def build(**kwargs):
    builder = ToLuaFactory()
    builder.build()


def upload(**kwargs):
    builder = ToLuaFactory()
    builder.upload()


def on_success(**kwargs):
    wechat.send_unicast_post_success()
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=62667eef-8463-48ac-827b-7948a708d573",
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure()
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dd9ba437-6c28-4d00-8130-303455d789ee",
    )
    advance.insert_pipeline_history_on_failure()


def on_unstable(**kwargs):
    wechat.send_unicast_post_unstable()
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dd9ba437-6c28-4d00-8130-303455d789ee",
    )
    advance.insert_pipeline_history_on_unstable()
