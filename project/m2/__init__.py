class Config:
    P4_2005_CONFIG = {"host": "m2.p4.com:2005", "username": "m2_ci", "password": "m2ci123456"}
    P4_WZ_TEST_CONFIG = {"host": "p4.com:2005", "username": "wang<PERSON>", "password": "<PERSON>zhen1022"}
    P4_2005_MUGIBUE_CONFIG = {"host": "p4.com:2005", "username": "mugib_ci", "password": "mugibci123456"}
    P4_2005_READ_CONFIG = {"host": "m2.p4.com:2005", "username": "m2_ci_read", "password": "m2ciread123456"}
    P4_2005_MUGIBUE_READ_CONFIG = {"host": "p4.com:2005", "username": "mugib_ci_read", "password": "mugibciread123456"}
    # 能够访问gitlab的账号
    GITLAB_MAINTAINER = {
        "username": "m2_read",
        "password": "Mugm2_read",
        "url": "https://gitlab.h3d.com.cn/mug_m2/m2_server.git",
    }

    NEXUS_CONFIG = {"username": "m2-pipeline", "password": "kWIHFRY9"}
    GITLAB_MGR_CONFIG = {
        "url": "https://gitlab.h3d.com.cn",
        "token": "-tCSngeCD4YKiByqh1E4",
        "project": "mug_m2/m2_client",
    }

    # 项目配置
    UE_ENGINE = {
        "url": "https://gitlab.h3d.com.cn",
        "token": "********************",
        "project": "1792",
    }
    # SMB配置
    SMB = {
        "username": "guest",
        "password": "",
        "server_ip": "f.h3d.com.cn",
        "port": 445,
        "server_name": "h3d",
        "domain": "WORKGROUP",
        "use_ntlm_v2": True,
        "is_direct_tcp": True,
    }
    # job_names = {
    #     # "client/xls_config",
    #     "client/mul_build_ts",
    # }
    # job_need_wait = {
    #     "master",
    #     "branch_2024/v_03",
    # }
    submit_client_branches = {
        "branch_2024/v_03",
    }
    jenkins_info = {
        "jenkins_domain": "http://jenkins-m2.h3d.com.cn/",
        "jenkins_user": "<EMAIL>",
        "jenkins_password": "yXawwNdPPeXdbiBj81yGiF1FgyVg8hcq",
    }


config = Config()
