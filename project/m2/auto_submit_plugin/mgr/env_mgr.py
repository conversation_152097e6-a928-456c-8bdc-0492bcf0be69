from frame import *

class EnvMgr:

    @staticmethod
    def set_plugin_tag(plugin_tag: str):
        env.set({"PLUGIN_TAG": plugin_tag})
    
    @staticmethod
    def get_plugin_tag():
        return env.get("PLUGIN_TAG")

    @staticmethod
    def set_merge_requests_url(mr_url: str):
        env.set({"MERGE_REQUEST_URL": mr_url})
    
    @staticmethod
    def get_merge_requests_url():
        return env.get("MERGE_REQUEST_URL")
    

class JenkinsEnvMgr:

    @staticmethod
    def get_target_branch():
        return env.get("TARGET_CLIENT_BRANCH")
    @staticmethod
    def get_plugin_changelist():
        return env.get("PLUGIN_CHANGELIST")
    @staticmethod
    def get_p4_force():
        return common.str2bool(env.get("P4_FORCE", "false"))

env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
