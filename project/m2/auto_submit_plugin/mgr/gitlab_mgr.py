# coding=utf-8
import platform
from frame import *
from project.m2 import config
from project.m2.auto_submit_plugin.mgr.env_mgr import  jenkins_env_mgr, env_mgr

class TsGitMgr:
    def __init__(self):
        self._workdir = env.pipeline.workspace()
        self._git_branch = jenkins_env_mgr.get_target_branch()
        self._plugin_branch = "Engine_Plugin"
        self._m2_git_mgr = GitMgr(workdir=self._workdir, project_name="M2_Client")
        self._client_git_url = "https://gitlab.h3d.com.cn/mug_m2/m2_client.git"

    def update_client(self):
        if not Path(os.path.join(self._workdir, "M2_Client", ".git")).exists():
            self._m2_git_mgr.clone_with_oauth2(url=self._client_git_url, branch=self._plugin_branch, oauth2="SC4FNLuCnkNkmA4knNij")
        else:
            self._m2_git_mgr.checkout(self._plugin_branch)
            self._m2_git_mgr.reset(commit_id="HEAD")
            try:
                self._m2_git_mgr.pull(branch=self._plugin_branch)
            except PyframeException:
                ret, output = cmd.run_shell(
                    cmds=[f"git clean TS_workspace/TsProj -xdf && git reset --hard origin/{self._git_branch} && git pull origin {self._git_branch}"],
                    workdir=os.path.join(self._workdir, "M2_Client"),
                )
                if ret != 0:
                    raise PyframeException(f"更新git代码失败: {output}")
    
    def upload_engine_plugins(self):
        des = f"M0000 [m2 ci] 更新引擎插件, engine_plugin changelist: {jenkins_env_mgr.get_plugin_changelist()}, tag:{env_mgr.get_plugin_tag()}"
        title = f"M0000 [m2_ci] Merge {self._plugin_branch} to {self._git_branch}"
        
        self._m2_git_mgr.pull()
        self._m2_git_mgr.add("Plugins") 
        self._m2_git_mgr.commit(des)
        self._m2_git_mgr.push()

        gitlab_mgr = GitlabMgr(**config.GITLAB_MGR_CONFIG)
        merge_requests_url = gitlab_mgr.create_merge_request(
            source_branch=self._plugin_branch, target_branch=self._git_branch, create_if_none_commit=True, title=title, description=des
        )
        env_mgr.set_merge_requests_url(merge_requests_url)
        if not merge_requests_url:
            log.warn("merge_requests_url is None")
            PyframeException("Create MR Fail")
        # _, merge_result = gitlab_mgr.merge(
        #     source_branch=self._plugin_branch,
        #     target_branch=self._git_branch,
        #     title=f"M0000 [m2 ci] merge {self._plugin_branch} to {self._git_branch}",
        #     merge_when_pipeline_succeeds=False,
        # )
        # log.info(f"merge_result: {merge_result}")

git_mgr = TsGitMgr()
