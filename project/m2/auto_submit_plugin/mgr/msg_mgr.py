from project.m2.auto_submit_plugin.mgr.env_mgr import env_mgr, jenkins_env_mgr
 

class MsgMgr:
    def __init__(self):
        self.plugin_tag = env_mgr.get_plugin_tag()
        self.plugin_changelist = jenkins_env_mgr.get_plugin_changelist()
        self.target_branch = jenkins_env_mgr.get_target_branch()
        self.mr_url = env_mgr.get_merge_requests_url()
        pass
    def get_msg(self) -> str:
        msg = "**项目**: M2_Client\n"
        msg += f"**Engine Plugin tag**: {self.plugin_tag}\n" if self.plugin_tag else ""
        msg += f"**Engine Plugin changelist**: {self.plugin_changelist}\n" if self.plugin_changelist else ""
        msg += f"**合并目标分支**: {self.target_branch}\n" if self.target_branch else ""
        msg += f"**MR 地址**:[plugin_merge_requests]({self.mr_url})\n" if self.mr_url  else ""
        return msg
    @staticmethod
    def get_user_list() -> list:
        return ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

msg_mgr = MsgMgr()