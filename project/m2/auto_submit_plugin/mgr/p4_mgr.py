# coding=utf-8
import re
from frame import *
from project.m2 import config
from project.m2.auto_submit_plugin.mgr.env_mgr import jenkins_env_mgr, env_mgr


class P4MgrBase:
    def __init__(self):
        self.workspace = env.pipeline.workspace()
        self.p4_port = config.P4_2005_CONFIG.get("host", "")
        self.p4_user = config.P4_2005_CONFIG.get("username", "")
        self.p4_password = config.P4_2005_CONFIG.get("password", "")


class ClientP4Mgr(P4MgrBase):
    def __init__(self):
        super().__init__()
        self.client = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_client_{common.get_host_ip()}")
        log.info(f"client : {self.client}, user: {self.p4_port}, port: {self.p4_user}")
        self.p4_client = P4Client(self.p4_port, self.p4_user, self.p4_password, self.client)
        # self.p4_env = "testdepot"
        self.p4_env = "H3D_M2"
        views = [
            f"//{self.p4_env}/M2_Product/MVComposerProject/Plugins/... //{self.client}/M2_Client/Plugins/...",
            f"-//{self.p4_env}/M2_Product/MVComposerProject/Plugins/mv_composer_updated //{self.client}/M2_Client/Plugins/mv_composer_updated",
            ]
        self.p4_client.set_charset(charset=self.p4_client.Charset.UTF8)
        self.p4_client.set_view(views)
        self.p4_client.set_options(allwrite=True)
        self.p4_client.set_root(self.workspace)

    def sync_engine_plugin(self):
        """
        同步p4 
        """
        changelist = jenkins_env_mgr.get_plugin_changelist()
        if not changelist:
            log.info("没有指定changelist, 停止")
            # pipeline_mgr.stop_current_build()
            raise PyframeException("没有指定changelist")
        force = jenkins_env_mgr.get_p4_force()
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/MVComposerProject/Plugins/...",changelist=f"{changelist}", force=force)
        tag_group = re.search(re.compile(r"tag:(v\d+\.\d+\.\d+)", re.S), self.p4_client.get_describe_by_changelist(changelist))
        if tag_group:
            tag = tag_group.group(1)
            env_mgr.set_plugin_tag(tag)
        else:
            raise PyframeException(f"can not find tag from changelist: {changelist}")
        # 获取到所有目录, 复制到Client Plugins 目录下   修改映射不许呀复制了
        # src_path = os.path.join(self.workspace, "engine_plugins", "Plugins")
        # dst_path = os.path.join(self.workspace, "M2_Client", "Plugins")
        # directories = [d for d in os.listdir(src_path) if os.path.isdir(os.path.join(src_path, d))]
        # for dir in directories:
        #     path_mgr.rm(os.path.join(dst_path, dir))
        #     path_mgr.copy(src=os.path.join(src_path, dir), dst=os.path.join(dst_path, dir))         


client_p4_mgr = ClientP4Mgr()
