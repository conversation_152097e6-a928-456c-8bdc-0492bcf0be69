# coding=utf-8

from frame import *
from project.m2.bak.build_ue5_client.scripts.build import M2Builder
from project.m2.bak.build_ue5_client.scripts.p4 import M2P4Client


@advance.stage(stage="更新p4")
def sync_p4(**kwargs):
    force = kwargs.get("force") == "true"
    try:
        p4 = M2P4Client()
        p4.update(force)
    except Exception as e:
        raise PyframeException(f"P4更新失败: {e}")
    changelist, user = p4.get_latest_changelist()
    env.set(
        {
            "CHANGELIST": changelist,
            "USER": user,
        }
    )


@advance.stage(stage="引擎源码编译")
def build_code(**kwargs):
    m2_builder = M2Builder()
    ret = m2_builder.build()
    log.info(f"编译结果: {ret}")
    if ret[0] != 0:
        raise PyframeException("编译失败, 请检查代码错误")


def __get_msg():
    msg = ""
    user = env.get("USER")
    if user:
        msg += f"**p4提交人:** {user}"
    changelist = env.get("CHANGELIST")
    if changelist:
        msg += f"**p4提交号:** {changelist}"
    return msg


def on_success(**kwargs):
    wechat.send_unicast_post_success(content=__get_msg())
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(content=__get_msg())
    advance.insert_pipeline_history_on_failure()


def on_unstable(**kwargs):
    wechat.send_unicast_post_failure(content=__get_msg())
    advance.insert_pipeline_history_on_unstable()
