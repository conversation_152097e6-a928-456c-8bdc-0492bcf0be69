import os

from frame import env


class P4Config:
    def __init__(self, port, username, password):
        self.port = port
        self.username = username
        self.password = password
        self.stream = "//M2/main"
        self.root = env.pipeline.workspace()

        job_name = os.getenv("JOB_NAME", "").replace("/", "_")
        node_name = os.getenv("NODE_NAME", "").replace(".", "_")
        self.client = f"jenkins_m2_{job_name}_{node_name}"


class ConfigDev:
    def __init__(self):
        self.p4 = P4Config(
            port="************:1667",
            username="test",
            password="test",
        )


class ConfigProd:
    def __init__(self):
        self.p4 = P4Config(
            port="************:1667",
            username="test",
            password="test",
        )


def config():
    if os.getenv("DEPLOY_ENV", "dev") == "dev":
        return ConfigDev

    return ConfigProd
