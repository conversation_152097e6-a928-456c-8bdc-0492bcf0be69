import os

from frame import cmd, env
from project.m2.bak.build_ue5_client.config.config import config


class M2Builder:
    def __init__(self):
        self.p4_config = config()().p4

    def build(self):
        cmds = [
            r"Engine\Build\BatchFiles\RunUAT.bat "
            "BuildGraph "
            "-Script=Engine/Build/Graph/Examples/BuildEditorAndTools.xml "
            '-Target="Submit To Perforce for UGS" '
            "-set:EditorTarget=PredemoEditor "
            "-set:ArchiveStream=//M2/binaries "
            f"-p4port={self.p4_config.port} "
            f"-p4user={self.p4_config.username} "
            f"-p4pass={self.p4_config.password} "
            f"-p4client={self.p4_config.client} "
            "-p4 -buildmachine "
            "-submit "
            "-set:ArchiveName=PredemoEditor "
            rf"-set:UProjectPath={self.p4_config.root}\ue5\Games\Predemo\Predemo.uproject"
        ]
        ret = cmd.run_shell(
            cmds=cmds,
            workdir=os.path.join(env.pipeline.workspace(), "ue5"),
            environ={"uebp_BuildRoot_P4": "//M2/main/M2Games"},
        )
        return ret
