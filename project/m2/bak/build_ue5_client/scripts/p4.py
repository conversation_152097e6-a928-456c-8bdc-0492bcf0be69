from frame.p4.p4 import Perforce
from frame.log.log import log
from project.m2.bak.build_ue5_client.config.config import config


class M2P4Client:
    def __init__(self):
        self.p4_config = config()().p4
        self.p4 = Perforce(port=self.p4_config.port, user=self.p4_config.username, password=self.p4_config.password, charset="utf8", logger=log)

    def update(self, force: bool):
        self.p4.login()
        self.p4.set_workspace(
            client=self.p4_config.client, view=None, options=["clobber", "rmdir"], root=self.p4_config.root, stream=self.p4_config.stream
        )
        self.p4.update_all(force_sync=force)

    def get_latest_changelist(self):
        result = self.p4.get_latest_change("//...", local=True)[0]
        return result.get("change"), result.get("user")
