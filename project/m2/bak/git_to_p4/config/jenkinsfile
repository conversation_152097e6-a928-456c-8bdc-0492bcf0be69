node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "m2_git_to_p4_${DEPLOY_ENV}"
        }
    }

//     parameters {
//         booleanParam(name: 'force', defaultValue: false, description: '是否强更p4')
//     }

    options {
        disableConcurrentBuilds()
//        timeout(time:1, unit: 'HOURS')
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(
                            script: """
                                python3 -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python3 -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("更新p4") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(script: "python3 m2.py git_to_p4 --job=sync_p4")
                    }
                }
            }
        }
        stage("更新git") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(script: "python3 m2.py git_to_p4 --job=sync_git")
                    }
                }
            }
        }
        stage("提交p4") {
            steps {
                dir("${env.GIT_NAME}"){
                    script {
                        bat(script: "python3 m2.py git_to_p4 --job=submit_to_p4")
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python3 m2.py git_to_p4 --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python3 m2.py git_to_p4 --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python3 m2.py git_to_p4 --job=on_failure")
                }
            }
        }
    }
}