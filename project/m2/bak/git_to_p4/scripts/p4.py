from frame import *
from frame.p4.p4 import Perforce
from frame.log.log import log
from project.m2.bak.git_to_p4.config.config import config


class M2P4Client:
    def __init__(self):
        ignore_file = r"C:\p4ignore.txt"
        with open(ignore_file, "w", encoding="utf-8") as f:
            f.write(".git")

        self.p4_config = config()().p4
        self.p4 = Perforce(
            port=self.p4_config.port,
            user=self.p4_config.username,
            password=self.p4_config.password,
            ignore_file=ignore_file,
            charset="utf8",
            logger=log,
        )
        self.p4.login()
        self.p4.set_workspace(
            client=self.p4_config.client, view=None, options=["clobber", "rmdir"], root=self.p4_config.root, stream=self.p4_config.stream
        )

    def update(self):
        self.p4.p4.run_sync("-k", "//...")

    def clean(self):
        return self.p4.p4.run_clean("//...")

    def submit(self):
        res = self.p4.p4.run_reconcile("//...")
        if res:
            self.p4.submit(f"[jenkins auto submit]{env.pipeline.build_url()}")
