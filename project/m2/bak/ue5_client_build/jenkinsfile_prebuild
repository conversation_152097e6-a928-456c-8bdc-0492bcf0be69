node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "m2_ue5_client_build"
            customWorkspace "D:/j/m2_ue5_client_prebuild"
        }
    }
    parameters {
        booleanParam(name: 'force', defaultValue: false, description: '是否强更p4')
        string(name: 'git_branch', defaultValue: "master", description: 'm2代码仓库分支', trim: true)
    }
    options {
        disableConcurrentBuilds()
    }
    // triggers {
    //     cron('H 0-3,5-23/2 * * *')
    // }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("更新m2项目代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py ue5_client_prebuild --job=get_m2_code")
                    }
                }
            }
        }
        stage("更新引擎二进制") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py ue5_client_prebuild --job=get_ue5_engine")
                    }
                }
            }
        }
        stage("建立git仓库链接") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py ue5_client_prebuild --job=make_link")
                    }
                }
            }
        }
        stage("编译") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py ue5_client_prebuild --job=ue5_client_build")
                    }
                }
            }
        }
        stage("提交p4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py ue5_client_prebuild --job=submit_to_p4")
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py ue5_client_prebuild --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py ue5_client_prebuild --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py ue5_client_prebuild --job=on_failure")
                }
            }
        }
    }
}