from frame import *


class EnvMgr:
    """
    自定义环境变量管理器
    """

    def set_latest_commit_id(self, commit_id):
        env.set({"LATEST_COMMIT_ID": commit_id})

    def get_latest_commit_id(self):
        return env.get("LATEST_COMMIT_ID")

    def set_latest_commit_msg(self, commit_msg):
        env.set({"LATEST_COMMIT_MSG": commit_msg})

    def get_latest_commit_msg(self):
        return env.get("LATEST_COMMIT_MSG")

    def set_build_log_url(self, log_url):
        env.set({"BUILD_LOG_URL": log_url})

    def get_build_log_url(self):
        return env.get("BUILD_LOG_URL") or ""

    @staticmethod
    def set_latest_changelist(changelist):
        env.set({"LATEST_CHANGELIST": changelist})

    @staticmethod
    def get_latest_changelist():
        return env.get("LATEST_CHANGELIST")

    @staticmethod
    def set_engine_tag(tag):
        env.set({"ENGINE_TAG": tag})

    @staticmethod
    def get_engine_tag():
        return env.get("ENGINE_TAG")


class JenkinsEnvMgr:
    """
    Jenkins环境变量管理器
    """

    @staticmethod
    def get_git_branch():
        return "master"

    @staticmethod
    def get_p4_sync_param():
        return env.get("FORCE")


env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
