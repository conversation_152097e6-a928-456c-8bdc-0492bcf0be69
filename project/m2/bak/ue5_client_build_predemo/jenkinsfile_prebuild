node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "m2_ue5_client_prebuild_predemo"
            customWorkspace "D:/j/m2_prebuild_predemo"
        }
    }
    parameters {
        booleanParam(name: 'force', defaultValue: false, description: '是否强更p4')
    }
    options {
        disableConcurrentBuilds()
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("更新引擎二进制和predemo代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py ue5_client_prebuild_predemo --job=get_code")
                    }
                }
            }
        }
        stage("编译") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py ue5_client_prebuild_predemo --job=ue5_client_build")
                    }
                }
            }
        }
        stage("提交p4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py ue5_client_prebuild_predemo --job=submit_to_p4")
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py ue5_client_prebuild_predemo --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py ue5_client_prebuild_predemo --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py ue5_client_prebuild_predemo --job=on_failure")
                }
            }
        }
    }
}