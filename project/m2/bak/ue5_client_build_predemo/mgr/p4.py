import re

from project.m2.bak.ue5_client_build_predemo.mgr.env_mgr import *
from frame import *


class M2P4ClientBase:
    __slots__ = ["p4_client", "client_name", "p4_root"]

    def __init__(self):
        self.p4_client = None
        self.p4_root = None
        self.client_name = None

    def update(self):
        self.p4_client.clean(f"//{self.client_name}/Predemo/...")
        self.p4_client.sync_all(force=jenkins_env_mgr.get_p4_sync_param() == "true")

    def submit(self):
        depot_files = []
        depot_files += self.p4_client.reconcile(f"//{self.client_name}/Predemo/Art_Src/Binaries/...")
        depot_files += self.p4_client.reconcile(f"//{self.client_name}/Predemo/Art_Src/Config/...")
        # depot_files += self.p4_client.reconcile(f"//{self.client_name}/Predemo/Art_Src/Plugins/...")
        depot_files += self.p4_client.reconcile(f"//{self.client_name}/Predemo/Art_Src/Predemo.uproject")

        self.p4_client.submit("[jenkins]auto commit")

    def edit_or_add(self, file_path):
        result = self.p4_client.files(file_path)
        if not result:
            self.p4_client.add(file_path)
            return

        self.p4_client.edit(file_path)


class M2BuildPreDemoP4Client(M2P4ClientBase):
    def __init__(self):
        self.p4_port = "m1.p4.com:2005"
        self.p4_user = "m2_ci"
        self.p4_pass = "m2ci123456"
        self.client_name = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_{common.get_host_ip()}")
        self.p4_client = P4Client(self.p4_port, self.p4_user, self.p4_pass, self.client_name)
        self.p4_client.set_encoding(encoding="gbk")

        views = [
            f"//H3D_M2/M2_Exe/UE_5_1_0/Windows/... //{self.client_name}/Windows/...",
            f"//H3D_M2/M2_Exe/Predemo/... //{self.client_name}/Predemo/...",
        ]
        self.p4_client.set_view(views)
        self.p4_client.set_options(allwrite=True)

        self.p4_root = os.path.join(env.pipeline.workspace(), "ue5_engine")
        self.p4_client.set_root(self.p4_root)


class M2PreBuildPreDemoP4Client(M2P4ClientBase):
    def __init__(self):
        self.p4_port = "************:1667"
        self.p4_user = "test"
        self.p4_pass = "test"
        self.client_name = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_{env.get('NODE_NAME')}")
        self.p4_client = P4Client(self.p4_port, self.p4_user, self.p4_pass, self.client_name)
        self.p4_client.set_encoding(encoding="gbk")

        views = [
            f"//M2/M2_Exe/UE_5_1_0/Windows/... //{self.client_name}/Windows/...",
            f"//M2/M2_Exe/Predemo/... //{self.client_name}/Predemo/...",
        ]
        self.p4_client.set_view(views)
        self.p4_client.set_options(allwrite=True)

        self.p4_root = os.path.join(env.pipeline.workspace(), "ue5_engine")
        self.p4_client.set_root(self.p4_root)

    def update(self):
        self.p4_client.clean(f"//{self.client_name}/Predemo/...")
        self.p4_client._P4Client__p4.sync("-k", "//...")
