from typing import AnyStr, Any
from dataclasses import dataclass

from project.m2.build_incubator_game.mgr.env_mgr import jenkins_mgr
from frame import common
from project.m2 import config as m2_config

demo_name: AnyStr = jenkins_mgr.get_demo_name()
env: AnyStr = jenkins_mgr.get_build_env()


@dataclass
class P4Config:
    host: AnyStr = "p4.com:2005"
    username: AnyStr = ""
    password: AnyStr = ""
    client: AnyStr = f"m2_build_incubator_game_{common.get_host_ip()}_{demo_name}_{env}"


class DevConfig:
    m2_root: AnyStr = "//H3D_MUG_QE/m2_dev"
    work_dir: AnyStr = r"E:\m2\build_incubator_game_dev"
    # TODO 个人账号密码 测试环境，忽略
    p4 = P4Config(username="zhang<PERSON><PERSON>", password="Aa123456")
    ue_cmd: AnyStr = "C:/Program Files/Epic Games/UE_5.1/Engine/Build/BatchFiles/Build.bat"
    vs_cmd: AnyStr = "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/devenv.com"
    robot_url: AnyStr = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0579ea0e-4ec2-435b-9f63-9f561fdeaf9a"
    rescue: Any = False


class ProdConfig:
    m2_root: AnyStr = "//H3D_MUGIBUE"
    work_dir: AnyStr = r"E:\m2\build_incubator_game_prod"
    p4 = P4Config(username=m2_config.P4_2005_MUGIBUE_CONFIG.get("username"), password=m2_config.P4_2005_MUGIBUE_CONFIG.get("password"))
    ue_cmd: AnyStr = "C:/Program Files/Epic Games/UE_5.1/Engine/Build/BatchFiles/Build.bat"
    vs_cmd: AnyStr = "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/devenv.com"
    robot_url: AnyStr = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1f4b9fde-a26e-4e1a-89eb-9ca5dfd401af"
    rescue: Any = True


class Config:
    c = ProdConfig if "PROD" in env.upper() else DevConfig
    exe_dir: AnyStr = f"IB_Exe/{demo_name}"
    build_dir: AnyStr = f"IB_Build/{demo_name}"
    exe_absolute_dir: AnyStr = f"{c.m2_root}/IB_Exe/{demo_name}"
    build_absolute_dir: AnyStr = f"{c.m2_root}/IB_Build/{demo_name}"
    plugin_dir: AnyStr = f"{c.work_dir}/IB_Exe/{demo_name}/Plugins"
    exe_binary_dir: AnyStr = f"{c.work_dir}/IB_Exe/{demo_name}/Binaries"
    build_binary_dir: AnyStr = f"{c.work_dir}/IB_Build/{demo_name}/Binaries"
    exe_plugin_binary_dir: AnyStr = f"{c.work_dir}/IB_Exe/{demo_name}/Plugins/" + "{plugin_name}/Binaries"
    build_plugin_binary_dir: AnyStr = f"{c.work_dir}/IB_Build/{demo_name}/Plugins/" + "{plugin_name}/Binaries"
    gen_sln_cmd: AnyStr = f'"{c.ue_cmd}" -projectfiles -project="{c.work_dir}/IB_Exe/{demo_name}/{demo_name}.uproject" -game -rocket -progress'
    build_sln_cmd: AnyStr = f'"{c.vs_cmd}" {c.work_dir}/IB_Exe/{demo_name}/{demo_name}.sln /REBUILD "Development Editor|Win64"'


config = Config()
