from frame import *
from project.m2.build_m2_server.mgr.build_mgr import BuildMgr

build_mgr = BuildMgr()


@advance.stage("清理上次构建")
def clean_old_build(**kwargs):
    build_mgr.clean_old_build()


@advance.stage("更新gitlab代码")
def update_gitlab_code(**kwargs):
    build_mgr.update_gitlab_code()


@advance.stage("更新P4代码")
def update_p4_code(**kwargs):
    build_mgr.update_p4_code()


@advance.stage("建立软链接")
def create_soft_link(**kwargs):
    build_mgr.create_soft_link()


@advance.stage("编译服务器")
def build_server(**kwargs):
    build_mgr.build_server()


@advance.stage("上传制品")
def upload_nexus(**kwargs):
    build_mgr.upload_nexus()


@advance.stage("部署服务器")
def deploy_server(**kwargs):
    build_mgr.deploy_server()


@advance.stage("上传编译日志")
def upload_logs(**kwargs):
    build_mgr.upload_logs()


def on_success(**kwargs):
    content = build_mgr.get_msg()
    wechat.send_unicast_post_success(user_list=[], content=content)
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f047e930-d518-4f5c-b7db-1ba6489ed16a",
        content=content,
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    content = build_mgr.get_msg()
    wechat.send_unicast_post_failure(user_list=["<EMAIL>"], content=content, rescue=False, to_admin=False)
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f047e930-d518-4f5c-b7db-1ba6489ed16a",
        content=content,
    )
    advance.insert_pipeline_history_on_failure()


def on_unstable(**kwargs):
    content = build_mgr.get_msg()
    wechat.send_unicast_post_unstable(user_list=[], content=content)
    wechat.send_multicast_post_unstable(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f047e930-d518-4f5c-b7db-1ba6489ed16a",
        content=content,
    )
    advance.insert_pipeline_history_on_unstable()
