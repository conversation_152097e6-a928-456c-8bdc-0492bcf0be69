from frame import *


class EnvMgr:
    """
    自定义环境变量管理器
    """

    @staticmethod
    def set_pipeline_log(pipeline_log: str):
        return env.set({"PIPELINE_LOG": pipeline_log})

    @staticmethod
    def get_pipeline_log():
        return env.get("PIPELINE_LOG")

    @staticmethod
    def set_nexus_url(nexus_url: str):
        return env.set({"NEXUS_URL": nexus_url})

    @staticmethod
    def get_nexus_url():
        return env.get("NEXUS_URL")


class JenkinsEnvMgr:
    @staticmethod
    def get_rebuild():
        return common.str2bool(env.get("REBUILD", "false"))

    # @staticmethod
    # def get_deploy():
    #     return common.str2bool(env.get("DEPLOY", "false"))

    @staticmethod
    def get_deploy_server():
        deploy_server = env.get("DEPLOY_SERVER")
        if deploy_server:
            deploy_server = deploy_server.strip()
        return deploy_server

    @staticmethod
    def get_branch_name():
        return env.get("BRANCH_NAME")

    @staticmethod
    def get_p4_force():
        return common.str2bool(env.get("P4_FORCE", "false"))


env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
