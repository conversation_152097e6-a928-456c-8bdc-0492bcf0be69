node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "**************"
            customWorkspace "D:\\j\\m2_ts\\$params.m2_git_branch"
        }
    }
    parameters {
        booleanParam(name: 'p4_force', defaultValue: false, description: '是否强更p4')
        string(name: 'p4_branch', defaultValue: "trunk", description: 'p4分支', trim: true)
        string(name: 'm2_git_branch', defaultValue: "master", description: 'm2代码仓库分支', trim: true)
        string (name: 'deploy_environment', defaultValue: 'prod', description: '部署环境: dev or prod')
    }
    options {
        disableConcurrentBuilds()
    }
    triggers {
        cron('H/20 0-3,5-23 * * *')
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("更新git") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_ts --job=update_git")
                    }
                }
            }
        }
        stage("更新p4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_ts --job=update_p4")
                    }
                }
            }
        }
        stage("软链接") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_ts --job=soft_link")
                    }
                }
            }
        }
        stage("编译C++") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_ts --job=build_ue5_cpp")
                    }
                }
            }
        }
        stage("C++导出ts") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_ts --job=gen_dts")
                    }
                }
            }
        }
        stage("ts导出js") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_ts --job=compile_ts")
                    }
                }
            }
        }
        // stage("生成manifest") {
        //     steps {
        //         dir("${env.GIT_NAME}") {
        //             script {
        //                 bat(script: "python m2.py build_ts --job=gen_resource_manifest")
        //             }
        //         }
        //     }
        // }
        stage("语言表检查") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_ts --job=check_xls_config")
                    }
                }
            }
        }
        stage("提交p4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_ts --job=submit_to_p4")
                    }
                }
            }
        }
    }
    post {
        always{
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py build_ts --job=on_always")
                }
            }
        }
        unstable {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py build_ts --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py build_ts --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py build_ts --job=on_failure")
                }
            }
        }
    }
}