# coding=utf-8

from frame import *
from project.m2.build_ts import P4_2005_CONFIG, WORKDIR
from project.m2.build_ts.mgr.env_mgr import jenkins_env_mgr


class P4:
    def __init__(self, chl):
        self.port = chl.p4_host
        self.username = chl.p4_username
        self.password = chl.p4_password
        self.deploy_env = jenkins_env_mgr.get_deploy_env()
        self.git_branch = jenkins_env_mgr.get_git_branch()
        self.p4_branch = jenkins_env_mgr.get_p4_branch()
        self.root = WORKDIR
        self.__p4_workspace = f'jenkins-{env.pipeline.function_name()}-{self.deploy_env}-{common.get_host_ip()}-{self.git_branch.replace("/", "-")}'
        self.client = self.__p4_workspace
        self.views = [
            f"//H3D_M2/M2_Product/{self.p4_branch}/... //{self.client}/...",
            f"//H3D_M2/M2_Product/Engine/trunk/... //{self.client}/store_ue_engine/...",
        ]


class TsConfig:
    def __init__(self):
        self.p4_host = P4_2005_CONFIG.get("host")
        self.p4_username = P4_2005_CONFIG.get("username")
        self.p4_password = P4_2005_CONFIG.get("password")

    @property
    def p4(self):
        return P4(self)


class TsConfigProd(TsConfig):
    def __init__(self):
        super(TsConfigProd, self).__init__()


class TsConfigDev(TsConfig):
    def __init__(self):
        super(TsConfigDev, self).__init__()


def init_config():
    if jenkins_env_mgr.get_deploy_env() == "dev":
        return TsConfigDev
    return TsConfigProd
