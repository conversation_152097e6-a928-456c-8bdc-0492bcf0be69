# coding=utf-8
from project.m2.build_ts import *
from project.m2.build_ts.mgr.env_mgr import jenkins_env_mgr


class LinkMgr:
    def __init__(self):
        self.__workdir = WORKDIR
        self.__p4_branch = jenkins_env_mgr.get_p4_branch()
        self.__git_branch = jenkins_env_mgr.get_git_branch()

    def link_git_to_p4(self):
        # git_path = os.path.join(self.__workdir, self.__git_branch, "M2_Client")
        # work_path = os.path.join(self.__workdir, self.__git_branch, "client")
        git_path = os.path.join(self.__workdir, "M2_Client")
        work_path = os.path.join(self.__workdir, "client")
        path_mgr.soft_link(src=os.path.join(git_path, "Source"), dst=os.path.join(work_path, "Source"))
        path_mgr.soft_link(src=os.path.join(git_path, "TS_workspace"), dst=os.path.join(work_path, "TS_workspace"))
        # path_mgr.soft_link(src=os.path.join(git_path, "Config"), dst=os.path.join(work_path, "Config"))
        path_mgr.soft_link(src=os.path.join(git_path, "Plugins"), dst=os.path.join(work_path, "Plugins"))


link_mgr = LinkMgr()
