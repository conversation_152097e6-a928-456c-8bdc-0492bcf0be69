# coding=utf-8
import pathlib
import re
from datetime import datetime

from project.m2.build_ts import *
from project.m2.build_ts.mgr.env_mgr import jenkins_env_mgr, env_mgr


class P4Mgr:
    def __init__(self):
        self.p4_config = config.p4
        self.__client = self.p4_config.client
        self.__git_branch = jenkins_env_mgr.get_git_branch()
        self.__root = os.path.join(self.p4_config.root)
        self.p4 = P4Client(
            host=self.p4_config.port,
            username=self.p4_config.username,
            password=self.p4_config.password,
            client=self.__client,
            charset=P4Client.Charset.CP936,
        )
        self.p4.set_view(views=self.p4_config.views)
        self.p4.set_root(path=self.__root)
        self.p4.set_options(allwrite=True)

    def sync_client(self):
        self.p4.sync(path=f"//H3D_M2/M2_Product/{jenkins_env_mgr.get_p4_branch()}/...", force=jenkins_env_mgr.get_p4_force())
        # 获取本地已经拉下来的changelist
        changes = self.p4.get_latest_changes(path=f"//{self.__client}/...#have")
        log.debug(f"changes: {changes}")
        changelist = changes.get("change")
        desc = changes.get("desc")
        submitter = changes.get("user")
        time = int(changes.get("time"))
        time = datetime.fromtimestamp(time).strftime("%Y-%m-%d %H:%M:%S")
        env_mgr.set_latest_changelist(latest_changelist=changelist)
        env_mgr.set_latest_desc(latest_desc=desc)
        env_mgr.set_latest_submitter(latest_submitter=submitter)
        env_mgr.set_latest_time(latest_time=time)
        # 查看client 目录是否有更新，若更新了需要提交一下. 若 binaries 目录时间在client 目录时间之后，则需要提交一下
        client_changes = self.p4.get_latest_changes(path=f"//H3D_M2/M2_Product/{jenkins_env_mgr.get_p4_branch()}/client/...#have")
        binaries_changes = self.p4.get_latest_changes(path=f"//H3D_M2/M2_Product/{jenkins_env_mgr.get_p4_branch()}/binaries/...#have")
        # 暂时把这个恢复回来, 分支要用
        engine_changelist = self.p4.get_latest_changes(path=f"//H3D_M2/M2_Product/{jenkins_env_mgr.get_p4_branch()}/ue_engine/...#have").get("change")
        env_mgr.set_engine_changelist(engine_changelist=engine_changelist)
        # 获取引擎tag信息
        describe = self.p4.get_describe_by_changelist(engine_changelist)
        tag_group = re.search(re.compile(r"tag:(v\d+\.\d+\.\d+)", re.S), describe)
        if tag_group:
            tag = tag_group.group(1)
            env_mgr.set_engine_tag(engine_tag=tag)
        if len(binaries_changes) > 0:
            binaries_time = binaries_changes.get("time")
            if int(binaries_time) < int(client_changes.get("time")):
                log.info(f"binaries_time:{binaries_time},client_changes:{client_changes.get('time')}")
                file_path = pathlib.Path(self.__root).joinpath("client", "client_updated")
                if not os.path.exists(file_path):
                    file_path.touch()
                with open(file_path, "r+", encoding="utf-8") as f:
                    client_changelist = client_changes.get("change")
                    f.write(f"client_changelist:{client_changelist},git_last_commit:{env_mgr.get_latest_commit_id()},")  # 这个文件可以用来判断是否需要编译

    def update_engine(self):
        # 看仓库是否有更新
        engine_store_changelist = self.p4.get_latest_changes(path=f"//H3D_M2/M2_Product/Engine_Store/trunk/ue_engine/...#have").get("change")
        new_tag_group = re.search(re.compile(r"tag:(v\d+\.\d+\.\d+)", re.S), self.p4.get_describe_by_changelist(engine_store_changelist))
        if new_tag_group and new_tag_group.group(1) != env_mgr.get_engine_tag():
            # 拉取ue_engine
            self.p4.sync(path=f"//H3D_M2/M2_Product/Engine_Store/trunk/ue_engine/...", force=jenkins_env_mgr.get_p4_force())
            tag = new_tag_group.group(1)
            # 复制 引擎产物仓库的 ue_engine 到客户端本地
            path_mgr.rm(os.path.join(self.__root, "ue_engine"))
            path_mgr.copy(
                src=os.path.join(self.__root, "store_ue_engine"),
                dst=os.path.join(self.__root, "ue_engine"),
                overwrite=True,
            )
            env_mgr.set_engine_tag(engine_tag=tag)
            env_mgr.set_need_update(need_update=True)

    @staticmethod
    def find_all_parents(directory: Path, end_parent: str) -> list:
        path = Path(directory)
        parents = path.parents
        all_parents = []
        for parent in parents:
            if parent.name == end_parent:
                break
            all_parents.append(parent)
        return all_parents

    def find_no_delete(self, directory: Path) -> list:
        directory_sub_dir = Path(directory.absolute()).rglob("*")
        no_delete = []
        for i in directory_sub_dir:
            if i.name == "ThirdParty":
                no_delete.append(i.absolute())
                no_delete.extend(self.find_all_parents(i, "Plugins"))
                third_party_sub_dir = Path(i.absolute()).rglob("*.dll")
                for j in third_party_sub_dir:
                    no_delete.append(j)
                    no_delete.extend(self.find_all_parents(j.absolute(), "Plugins"))
        return no_delete

    def copy(self):
        """
        将编译好的文件拷贝到指定目录下
        """
        src_base_path = os.path.join(self.__root, "client")
        dst_base_path = os.path.join(self.__root, "binaries")

        # 拷贝前，将binary目录先清空
        if path_mgr.exists(dst_base_path):
            path_mgr.rm(dst_base_path)
        path_mgr.mkdir(dst_base_path)

        path_mgr.copy(
            src=os.path.join(src_base_path, "Content", "Javascript", "Generated"),
            dst=os.path.join(dst_base_path, "Content", "Javascript", "Generated"),
        )
        if os.path.exists(os.path.join(src_base_path, "client_updated")):
            path_mgr.copy(
                src=os.path.join(src_base_path, "client_updated"),
                dst=os.path.join(dst_base_path, "client_updated"),
            )
        path_mgr.copy(
            src=os.path.join(src_base_path, "Binaries"),
            dst=os.path.join(dst_base_path, "Binaries"),
        )
        # 这个文件不用了，注释掉
        # path_mgr.mkdir(path=os.path.join(dst_base_path, "Generated"))
        # path_mgr.copy(
        #     src=os.path.join(src_base_path, "Generated", "resindex"),
        #     dst=os.path.join(dst_base_path, "Generated", "resindex"),
        # )
        path_mgr.copy(
            src=os.path.join(src_base_path, "Content", "Javascript", "Generated", "xls_config"),
            dst=os.path.join(dst_base_path, "Content", "Javascript", "Generated", "xls_config"),
        )
        # 拷贝Plugins，忽略插件目录下的Intermediate和Source目录
        path_mgr.copy(
            src=os.path.join(src_base_path, "Plugins"),
            dst=os.path.join(dst_base_path, "Plugins"),
        )
        plugins_path = Path(os.path.join(dst_base_path, "Plugins")).glob("*")
        for plugin in plugins_path:
            if not plugin.is_dir():
                continue
            for p in plugin.iterdir():
                if p.is_dir():
                    if p.name in ["Intermediate"]:
                        # log.debug(f"rm {p.absolute()}")
                        path_mgr.rm(p.absolute())

                    # 包含ThirdParty的目录不需要删除，Source相关的其他目录需要删除
                    if p.name in ["Source"]:
                        no_delete = self.find_no_delete(p)
                        source_sub_dir = Path(p.absolute()).rglob("*")
                        for i in source_sub_dir:
                            if i in no_delete:
                                continue
                            path_mgr.rm(i.absolute())
                        if not no_delete:
                            path_mgr.rm(p.absolute())

    def submit(self):
        """
        提交到p4
        """
        if env_mgr.get_need_update():
            # 更新引擎
            engine_new_files = self.p4.reconcile(
                f"//H3D_M2/M2_Product/{jenkins_env_mgr.get_p4_branch()}/ue_engine/...", preview=False, delete=True, add=True, edit=True
            )
            if engine_new_files:
                tag = env_mgr.get_engine_tag()
                self.p4.submit(
                    desc=f"[ci submit]update engine to tag:{tag}",
                    revert_if_failed=False,
                )
        depot_files = []
        dst_base_path = os.path.join(self.__root, "binaries", "...")
        depot_files += self.p4.reconcile(dst_base_path, preview=False, delete=True)
        if not depot_files:
            log.warn("没有文件需要提交，跳过")
            pipeline_mgr.stop_current_build()
            return
        else:
            tag = env_mgr.get_engine_tag()
            branch_changelist = env_mgr.get_latest_changelist()
            engine_change_list = env_mgr.get_engine_changelist()
            git_commit = env_mgr.get_latest_commit_id()
            git_branch = jenkins_env_mgr.get_git_branch()
            self.p4.submit(
                desc=f"[ci submit]git_commit:{git_commit},git_branch:{git_branch},engine_tag:{tag},engine_change_list:{engine_change_list},branch_change_list:{branch_changelist}",
                revert_if_failed=True,
            )


p4_mgr = P4Mgr()
