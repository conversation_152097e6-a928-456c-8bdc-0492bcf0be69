node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "win10_192.168.13.129"
            customWorkspace "D:/xls_to_js"
        }
    }
    options {
        disableConcurrentBuilds()
    }
    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("更新p4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_xls --job=update_p4")
                    }
                }
            }
        }
        stage("语言表检查") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_xls --job=check_xls_config")
                    }
                }
            }
        }
        stage("提交p4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py build_xls --job=submit_to_p4")
                    }
                }
            }
        }
    }
    post {
        always{
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py build_xls --job=on_always")
                }
            }
        }
        unstable {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py build_xls --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py build_xls --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python m2.py build_xls --job=on_failure")
                }
            }
        }
    }
}
