# coding=utf-8
import os

from frame import *
from project.m2.build_xls import *
from project.m2.build_xls.mgr.env_mgr import jenkins_env_mgr


class P4Mgr:
    def __init__(self):
        self.p4_config = config.p4
        self.__client = self.p4_config.client
        self.p4_branch = jenkins_env_mgr.get_p4_branch()
        # self.__git_branch = jenkins_env_mgr.get_git_branch()
        self.__root = os.path.join(self.p4_config.root)
        self.p4 = P4Client(
            host=self.p4_config.port,
            username=self.p4_config.username,
            password=self.p4_config.password,
            client=self.__client,
            charset=P4Client.Charset.CP936,
        )
        self.p4.set_view(views=self.p4_config.views)
        self.p4.set_root(path=self.__root)
        self.p4.set_options(allwrite=True)
        self._client_path = os.path.join(self.__root, r"client\Content\JavaScript\Generated\xls_config")
        self._server_shard_path = os.path.join(self.__root, r"server\exe\resources\config\shared")
        self._server_server_path = os.path.join(self.__root, r"server\exe\resources\config\server")

    def sync_all(self):
        # 先将本地资源更新到最新
        self.p4.sync_force(path=f"//H3D_M2/M2_Product/{self.p4_branch}/client/Content/JavaScript/Generated/xls_config/...")
        self.p4.sync_force(path=f"//H3D_M2/M2_Product/{self.p4_branch}/server/exe/resources/config/server/...")
        self.p4.sync_force(path=f"//H3D_M2/M2_Product/{self.p4_branch}/server/exe/resources/config/shared/...")

        # 更新p4前先将本地文件先删除掉
        if os.path.exists(self._client_path):
            is_true = path_mgr.rm(self._client_path)
            print(self._client_path, is_true)
            if not is_true:
                pipeline_mgr.stop_current_build()
                return

        if os.path.exists(self._server_shard_path):
            is_true = path_mgr.rm(self._server_shard_path)
            print(self._server_shard_path, is_true)
            if not is_true:
                pipeline_mgr.stop_current_build()
                return

        if os.path.exists(self._server_server_path):
            is_true = path_mgr.rm(self._server_server_path)
            print(self._server_server_path, is_true)
            if not is_true:
                pipeline_mgr.stop_current_build()
                return

        self.p4.sync_force(path=f"//H3D_M2/M2_Product/{self.p4_branch}/common/xls_config/...")

    def submit(self):
        """
        提交到p4: 待确定提交的文件
        """
        depot_files = []
        dst_base_path = [self._client_path, self._server_shard_path, self._server_server_path]
        for d in dst_base_path:
            print(f"待reconcile目录:{d}")
            depot_files += self.p4.reconcile(d + "/...")
            print(f"reconcile结果:{depot_files}")
        if not depot_files:
            log.warn("没有文件需要提交，跳过")
            pipeline_mgr.stop_current_build()
            return
        else:
            self.p4.submit(
                desc=f"[ci submit]监听xls自动js]",
                revert_if_failed=True,
            )


p4_mgr = P4Mgr()
