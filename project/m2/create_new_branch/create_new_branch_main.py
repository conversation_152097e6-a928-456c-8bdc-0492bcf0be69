import re

from frame import advance, wechat, PyframeException

from project.m2.create_new_branch.mgr.env_mgr import JenkinsEnvMgr, EnvMgr
from project.m2.create_new_branch.mgr.gitlab_mgr import GitlabMgr
from project.m2.create_new_branch.mgr.p4_mgr import P4Mgr
from project.m2.create_new_branch.config import config


@advance.stage(stage="分支名称校验")
def check_branch_name():
    branch_name = JenkinsEnvMgr.get_branch_name()
    if not branch_name:
        raise PyframeException(f"分支名不能为空")

    if not re.match(re.compile(r"^branch_\d{4}/v_\S+$", re.S), branch_name):
        raise PyframeException(f"分支名{branch_name}格式不正确")


@advance.stage(stage="创建gitlab m2_client新分支")
def create_gitlab_m2_client_branch():
    """
    从master分支创建新分支
    """
    project = config.Gitlab.projects.get("m2_client")
    git_branch = JenkinsEnvMgr.get_branch_name()
    gitlab_mgr = GitlabMgr(project)
    gitlab_mgr.create_branch(git_branch)


@advance.stage(stage="创建gitlab m2_server新分支")
def create_gitlab_m2_server_branch():
    """
    从master分支创建新分支
    """
    project = config.Gitlab.projects.get("m2_server")
    git_branch = JenkinsEnvMgr.get_branch_name()
    gitlab_mgr = GitlabMgr(project)
    gitlab_mgr.create_branch(git_branch)


@advance.stage(stage="创建gitlab m2_tools新分支")
def create_gitlab_m2_tools_branch():
    """
    从master分支创建新分支
    """
    project = config.Gitlab.projects.get("m2_tools")
    git_branch = JenkinsEnvMgr.get_branch_name()
    gitlab_mgr = GitlabMgr(project)
    gitlab_mgr.create_branch(git_branch)


@advance.stage(stage="创建p4新分支")
def create_p4_branch():
    """
    从trunk创建新分支
    """
    p4_branch = JenkinsEnvMgr.get_branch_name()
    p4_mgr = P4Mgr()
    p4_mgr.create_branch(p4_branch)


def __get_msg():
    msg = "**创建分支信息如下**\n"
    git_projects = config.Gitlab.projects.values()
    for project in git_projects:
        git_branch_info = EnvMgr.get_git_created_branch(project)
        if "/" in git_branch_info:
            git_branch_info = git_branch_info.split("/")[-1]
        msg += f"**{project}**: {git_branch_info}\n"

    p4_branch_info = EnvMgr.get_p4_created_branch()
    msg += f"**p4**: {p4_branch_info}\n"
    return msg


def on_success():
    wechat.send_unicast_post_success(content=__get_msg())
    wechat.send_multicast_post_success(
        webhook=config.Notification.webhook,
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_success()


def on_failure():
    wechat.send_unicast_post_failure(content=__get_msg())
    wechat.send_multicast_post_failure(
        webhook=config.Notification.webhook,
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_failure()


def on_unstable():
    wechat.send_multicast_post_unstable(content=__get_msg())
    wechat.send_multicast_post_unstable(
        webhook=config.Notification.webhook,
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_unstable()
