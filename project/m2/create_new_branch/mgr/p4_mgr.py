import re

from frame import P4Client, common, env, PyframeException, log
from project.m2.create_new_branch.config import config
from project.m2.create_new_branch.mgr.env_mgr import EnvMgr


class P4Mgr:
    def __init__(self):
        self.prefix = "//H3D_M2/M2_Product/"
        self.config = config.P4
        self.p4_client = P4Client(
            host=self.config.port,
            username=self.config.user,
            password=self.config.password,
            client=re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_{common.get_host_ip()}"),
        )
        self.p4_client.set_root(env.pipeline.workspace())

    def create_branch(self, p4_branch: str):
        try:
            self.p4_client.populate(self.config.source_branch, self.prefix + p4_branch + "/...")
        except PyframeException as e:
            log.warn(f"p4创建分支{p4_branch}失败, {e.__str__()}")
            EnvMgr.set_p4_created_branch("创建分支失败或者已经分支已经存在")
            return

        EnvMgr.set_p4_created_branch(p4_branch)
