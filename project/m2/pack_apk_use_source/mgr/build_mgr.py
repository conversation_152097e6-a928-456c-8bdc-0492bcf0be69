# coding=utf-8
from datetime import datetime
import platform
import time
from enum import Enum

from frame import *
from project.m2 import config
from project.m2.pack_apk_use_source.mgr.env_mgr import env_mgr, jenkins_env_mgr


class BuildLogType(Enum):
    LOG = "log.txt"
    UBT_UNREAL_EDITOR_WIN64_DEBUG_GAME = "UBT-UnrealEditor-Win64-DebugGame.txt"
    UBT_UNREAL_EDITOR_WIN64_DEVELOPMENT = "UBT-UnrealEditor-Win64-Development.txt"
    UBT_UNREAL_HEADER_TOOL_MAC_DEVELOPMENT = "UBT-UnrealHeaderTool-Mac-Development.txt"
    UBT_UNREAL_HEADER_TOOL_WIN64_DEVELOPMENT = "UBT-UnrealHeaderTool-Win64-Development.txt"


class BuildMgr:
    def __init__(self) -> None:
        self.workspace = os.path.join(env.pipeline.workspace())
        self.build_type_list = jenkins_env_mgr.get_build_type() 
        self.ue_engine = os.path.join(self.workspace, "ue_engine")
        self.client_built_path = os.path.join(self.workspace, "binaries")
        # 客户端相关的
        self._client_p4_branch = jenkins_env_mgr.get_p4_branch()
        self._client_git_branch = jenkins_env_mgr.get_client_git_branch()

        self.ubt_exe = os.path.join(self.ue_engine,"Windows", "Engine", "Binaries", "DotNET", "UnrealBuildTool", "UnrealBuildTool.exe")
        self.build_project_log = os.path.join(self.workspace, "logs", "build-project-log.txt")

        self._log_path = os.path.join(self.workspace, "client", "logs")
        self._build_log_path = os.path.join(self._log_path, "build_logs")
        if not self._build_log_path:
            path_mgr.mkdir(self._build_log_path)
        self._gen_dts_log_path = os.path.join(self._log_path, "gen_dts_logs")
        if not self._gen_dts_log_path:
            path_mgr.mkdir(self._gen_dts_log_path)
        self._ts_to_js_path = os.path.join(self.workspace, "ts_to_js_logs")
        if not path_mgr.exists(self._ts_to_js_path):
            path_mgr.mkdir(self._ts_to_js_path)
        self._gen_path = os.path.join(self._log_path, "gen_logs")
        if not path_mgr.exists(self._gen_path):
            path_mgr.mkdir(self._gen_path)
        self._pack_log_path = os.path.join(self._log_path, "pack_logs")
        if not path_mgr.exists(self._pack_log_path):
            path_mgr.mkdir(self._pack_log_path)
        # self._build_log = os.path.join(self._build_log_path, f"build_ue5_log_{env.pipeline.build_num()}.log")
        self._ts_to_js_log = os.path.join(self._ts_to_js_path, f"ts_to_js_{env.pipeline.build_num()}.log")
        self._gen_dts_log = os.path.join(self._gen_dts_log_path, f"gen_dts_{env.pipeline.build_num()}.log")
        self._gen_config_log = os.path.join(self._gen_path, f"gen_config_{env.pipeline.build_num()}.log")
        # self._unreal_editor_cmd = os.path.join(self.workspace, r"ue_engine\Windows\Engine\Binaries\Win64\UnrealEditor-Cmd.exe")
        self._unreal_editor_cmd = os.path.join(self.workspace, r"UE_5.1_T3\Engine\Binaries\Win64\UnrealEditor-Cmd.exe")
        self._uproject_path = os.path.join(self.workspace, r"client\M2_Client.uproject")
        self._build_shell_path = os.path.join(self.workspace, r"client\build_shell")
        self._build_bat = os.path.join(self.workspace, r"UE_5.1_T3\Engine\Build\BatchFiles\Build.bat")
        
        self._m2_project = os.path.join(self.workspace, "client")
        self._xls_config_path = os.path.join(self.workspace, r"common\xls_config")
        self.switcher = r"C:\Program Files (x86)\Epic Games\Launcher\Engine\Binaries\Win64\UnrealVersionSelector.exe"
        self._script_path = os.path.join(self.workspace, r"pyframe-pipeline\project\team3\build_branches\scripts")
        self._pack_project_log = os.path.join(self._pack_log_path, f"pack_project_log_{env.pipeline.build_num()}.txt")
        
        self.archive_dir = os.path.join(self.workspace, "client", r"Publish\BuildApp")
        self.archive_cache = os.path.join(self.workspace, "client", r"Publish\BuildCache")
        self.android_cache = os.path.join(self.workspace,"client", r"Binaries\Android")
        self.windows_engine = os.path.join(self.workspace, "ue_engine", "Windows")
        # self.bat_dir = os.path.join(self.windows_engine,"Engine","Build", "BatchFiles")
        self.bat_dir = os.path.join(self.workspace,"UE_5.1_T3","Engine","Build", "BatchFiles")
        ubt_log_name = f"UBT-{jenkins_env_mgr.get_client_git_branch()}-Android-{jenkins_env_mgr.get_build_type()}.txt"
        suffix = self.windows_engine.replace(":\\", "+").replace("\\", "+")
        self.ubt_log = os.path.join(r"C:\Users\<USER>\AppData\Roaming\Unreal Engine\AutomationTool\Logs", suffix, ubt_log_name)
        self.nexus = Nexus(**config.NEXUS_CONFIG)
        self.crash_sight_path = os.path.join(self.workspace, "client","build_shell", "crashsight")
        self.crash_symbol_path = os.path.join(self.archive_dir, "Android", "M2_Client_Symbols_v1", "M2_Clientarm64")


    def get_log_path(self, log_type: BuildLogType) -> str:
        """
        获取日志路径
        """
        log_path = os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Programs", "AutomationTool", "Saved", "Logs", log_type.value)
        return log_path

    def raise_build_exception(self, log_path: str):
        """
        解析编译错误
        """
        log.info("start parse build log")
        if not path_mgr.exists(log_path):
            log.warn("log.txt not exists")
            return
        errors = []
        encoding = "gb2312" if platform.system() == "Windows" else "utf-8"
        f = open(log_path, "r", encoding=encoding)
        line = f.readline()
        while line:
            line = line.strip()
            if "A conflicting instance of AutomationTool is already running." in line:
                raise PyframeException("编译ue5.1引擎失败, A conflicting instance of AutomationTool is already running.")

            if "Unable to find valid certificate/mobile provision pair." in line:
                raise PyframeException("编译ue5.1引擎失败, Unable to find valid certificate/mobile provision pair.")

            if "Host key verification failed" in line:
                raise PyframeException("编译ue5.1引擎失败, Host key verification failed, 请检查ssh key是否正确")

            if "Platform Android is not a valid platform to build. Check that the SDK is installed properly." in line:
                raise PyframeException("编译ue5.1引擎失败, Platform Android is not a valid platform to build. Check that the SDK is installed properly.")

            if "Unable to find mobile provision for UnrealGame" in line:
                raise PyframeException("编译ue5.1引擎失败, Unable to find mobile provision for UnrealGame")

            if "A conflicting instance of UnrealBuildTool is already running." in line:
                raise PyframeException("编译ue5.1引擎失败, A conflicting instance of UnrealBuildTool is already running.")

            if "Failed to load profile" in line:
                errors.append(line)

            if "MSBUILD : error" in line:
                errors.append(line)

            if "ERROR:" in line:
                errors.append(line)

            if ": error" in line:
                errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"编译ue5.1引擎失败: \n{errors_str}")
        else:
            raise PyframeException("编译ue5.1引擎失败, 未知错误")
    def clean_cache(self):
        if jenkins_env_mgr.get_clean_cache():
            # 需要清理, 目录包括 client\Saved, client\Intermediate 和 client\Plugins 目录下的所有 Intermediate 目录
            path_mgr.rm(os.path.join(self.workspace, "client", "Saved"))
            path_mgr.rm(os.path.join(self.workspace, "client", "Intermediate"))
            plugin_path = Path(os.path.join(self.workspace, "client", "Plugins"))
            # 删除 client\Plugins 目录下的所有 Intermediate 目录 
            for dirpath, dirnames, filenames in os.walk(plugin_path, topdown=False):
                for dirname in dirnames:
                    if dirname == 'Intermediate':
                        dir_to_delete = os.path.join(dirpath, dirname)
                        log.info(f'Deleting Client Plugin Intermediate: {dir_to_delete}')
                        path_mgr.rm(dir_to_delete)
        if jenkins_env_mgr.get_clean_engine_cache():
            path_mgr.rm(os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Saved"))
            path_mgr.rm(os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Intermediate"))
            engine_plugin_path = Path(os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Plugins"))
            for dirpath, dirnames, filenames in os.walk(engine_plugin_path, topdown=False):
                for dirname in dirnames:
                    if dirname == 'Intermediate':
                        dir_to_delete = os.path.join(dirpath, dirname)
                        log.info(f'Deleting Engine Plugin Intermediate: {dir_to_delete}')
                        path_mgr.rm(dir_to_delete)




    def build_ue5_cpp(self):
        """
        编译C++
        """
        # 生成本次使用的appversion
        self.__gen_appversion()     
        self.clean_cache()
        win64 = "true" if "WithWin64" in self.build_type_list else "false"
        android = "true" if "WithAndroid" in self.build_type_list else "false"
        ios = "true" if "WithIOS" in self.build_type_list else "false"
        mac = "true" if "WithMac" in self.build_type_list else "false"
        need_copy = "false" 
        path_mgr.copy(src=os.path.join(self._script_path, "BuildEngineAndClient.xml"), dst=os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Build"), overwrite=True)   
        scripts = f"-Script=Engine/Build/BuildEngineAndClient.xml"
        if platform.system() == "Windows":
            cmd_prefix = f'RunUAT.bat BuildGraph {scripts} -target="Make Installed Build Win64" '
        else:
            cmd_prefix = f'./RunUAT.command BuildGraph {scripts} -target="Make Installed Build Mac" '
        commands = [
            f"{cmd_prefix} -set:WithWin64={win64} "
            "-set:WithWin32=false "
            f"-set:WithMac={mac} "
            f"-set:WithAndroid={android} "
            f"-set:WithIOS={ios} "
            "-set:WithTVOS=false "
            "-set:WithLinux=false "
            "-set:WithLinuxAArch64=false "
            "-set:WithLinuxArm64=false "
            "-set:WithLumin=false "
            "-set:WithHoloLens=false "
            "-set:WithDDC=false "
            # "-set:GameConfigurations=Development "
            f"-set:NeedCopyEngine={need_copy} "
            f"-set:BuiltDirectory={self.ue_engine} "
            # f"-set:ClientBuiltPath={self.client_built_path} "
            rf'-set:ClientProjectPath="{self._m2_project}\M2_Client.uproject" '
        ]
            
        start_time = time.time()
        ret = cmd.run_shell(
            cmds=commands,
            workdir=os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Build", "BatchFiles"),
            return_output=True,
            errors="ignore",
            encoding="gb2312" if platform.system() == "Windows" else "utf-8",
            log_to_file="ue5.1_build.log",
        )
        if ret[0] != 0:
            self.__raise_build_exception(log_path=self.get_log_path(BuildLogType.LOG))

    def switch_engine(self, is_source = False):
        """
        切换引擎版本
        """
        cmd_lines = f'"{self.switcher}"  /switchversionsilent  {self._uproject_path}  '
        if is_source:
            cmd_lines += f' {os.path.join(self.workspace, "UE_5.1_T3")} '
        else:
            cmd_lines += f' {os.path.join(self.ue_engine,"Windows")} '
        ret = cmd.run_shell(cmds=[cmd_lines])
        if ret[0] != 0:
            raise PyframeException(f"引擎版本切换异常，请流水线组排查原因")

    def __raise_build_exception(self, log_path: str = None):
        """
        解析编译错误
        """
        log.info("start parse build log")
        errors = []
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            line = line.strip()
            if "The process cannot access the file because it is being used by another process" in line:
                raise PyframeException("编译C++失败, 同一时间只能有一个项目进行编译")

            if "Maximum number of concurrent builds reached" in line:
                raise PyframeException("编译C++失败, 达到并发构建的最大数量")

            if "exception" in line or ": error" in line or ": fatal error" in line or ": Error: " in line:
                errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"编译C++失败, 请程序检查代码问题\n{errors_str}")
        else:
            raise PyframeException("编译C++失败, 未知错误")

    def gen_config(self):
        """
        生成配置
        """
        command = f"{self._unreal_editor_cmd} {self._uproject_path} -run=ExportMusicLevel ABSLOG={self._gen_config_log}"
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self._build_shell_path,
            encoding="utf-8",
            errors="ignore",
        )
        if ret[0] != 0:
            log.error("ExportMusicLevel fail")
            raise PyframeException("ExportMusicLevel 失败")

    def gen_dts(self):
        """
        生成dts
        """
        command = f"{self._unreal_editor_cmd} {self._uproject_path} -run=GenDTS ABSLOG={self._gen_dts_log}"
        command += f" -fastexit -nosplash -Unattended -nopause"
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self._build_shell_path,
            encoding="utf-8",
            errors="ignore",
        )
        if ret[0] != 0:
            log.error("gen_dts fail")
            self._raise_gen_dts_exception(log_path=self._gen_dts_log)
            raise PyframeException("gen_dts失败")

    def _raise_gen_dts_exception(self, log_path: str = None):
        """
        解析生成dts错误
        """
        log.info("start parse gen dts log")
        errors = []
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            line = line.strip()
            if "Error:" in line:
                index = line.find("Error:")
                line = line[index:]
                if line not in errors:
                    errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"生成dts失败, 请程序检查代码问题\n{errors_str}")
        else:
            raise PyframeException("生成dts失败, 未知错误")

    def compile_ts(self):
        """
        编译ts
        """
        js_path = os.path.join(self.workspace, r"client\TS_workspace\H3dCompiler\actions\H3dCompile.js")
        ret = cmd.run_shell(
            cmds=[f"node.exe {js_path} -fromJenkins"],
            workdir=self._build_shell_path,
            log_to_file=self._ts_to_js_log
        )
        if ret[0] != 0:
            log.error("compile_ts fail")
            raise PyframeException("compile_ts失败")

    def xls_config(self):
        """
        语言表检查
        """
        start_bat = path_mgr.exists(path=os.path.join(self._xls_config_path, "start.bat"))
        if not start_bat:
            raise PyframeException("语言表检查失败, start.bat不存在")
        ret = cmd.run_shell(cmds=["start.bat"], workdir=self._xls_config_path, verbose=cmd.Verbose.LOG)
        if ret[0] != 0:
            log.error("language_xlx_check fail")
            raise PyframeException("语言表检查失败, 请程序检查问题")

    def gen_resource_manifest(self):
        """
        生成资源清单
        """
        ret = cmd.run_shell(
            cmds=[f"{self._unreal_editor_cmd} {self._uproject_path} -run=GenerateManifest"],
            workdir=self._build_shell_path,
        )
        if ret[0] != 0:
            log.error(ret)
            raise PyframeException("gen_resource_manifest失败")

    def pack_android(self):
        """
        打包客户端
        """
        # 打包前先清理上次构建的客户端以及缓存和日志
        self.__clean_pack_cache()
        extra_param_str = jenkins_env_mgr.get_extra_pack_param()
        extra_param = []
        if extra_param_str is not None and len(extra_param_str) > 0:
            extra_param = extra_param_str.split(",")
        appversion = env_mgr.get_app_version()     
        # 打包
        cmd_lines = ""
        cmd_lines += rf"{self._unreal_editor_cmd}  {self._uproject_path} -run=BuildApp -android -BuildTarget={jenkins_env_mgr.get_pack_target()} -M2CommandLine=\"-AppVersion={appversion} "
        for i in range(len(extra_param)):
            cmd_lines += f"-{extra_param[i]} "
        cmd_lines += rf"\" "
        cmd_lines += f"ABSLOG={self._pack_project_log}"
        log.info(f"cmd_lines: {cmd_lines}")
        _, output = cmd.run_shell(cmds=[cmd_lines], workdir=self.bat_dir, encoding="latin-1")

        is_success = self.__check_package_reuslt()
        if not is_success:
            advance.unreal_engine.raise_automation_tool_log_exception(log_path={self._pack_project_log})
            raise Exception(f"打包客户端失败: {output}")

    def upload_pdb(self):
        """
        上传pdb
        """
        # 上传pdb
        cmd_lines = ""
        cmd_lines += f"java -jar crashSightSymbolTool.jar -i {self.crash_symbol_path} -version {env_mgr.get_app_version() } -p aos "
        cmd_lines += f"-m win -u -url https://api.crashsight.qq.com/openapi/file/upload/symbol -id d5e6aed23a -key 176c29a3-ea4e-49c5-ac87-87feb503890c"
        ret = cmd.run_shell(cmds=[cmd_lines], workdir=self.crash_sight_path)
        if ret[0] != 0:
            raise PyframeException(f"上传符号表失败，请程序检查错误")       

    # 上传客户端
    def upload_android(self):
        """
        上传客户端
        """
        android_dir = os.path.join(self.archive_dir, "Android")
        if not path_mgr.exists(android_dir):
            raise PyframeException(f"未找到{android_dir}目录，打包可能存在异常，请检查后重新打包")

        # 组织客户端目录名称
        build_target = jenkins_env_mgr.get_pack_target().lower() if jenkins_env_mgr.get_pack_target() else ""
        zip_name = f"client_{jenkins_env_mgr.get_client_git_branch()}_android_git_{env_mgr.get_client_git_latest_commit_id()}_p4_{env_mgr.get_p4_latest_changelist()}_{build_target}_appversion_{env_mgr.get_app_version()}_{int(time.time())}.zip"
        # zip_name = f"m2_{jenkins_env_mgr.get_engine_branch()}_client_{jenkins_env_mgr.get_client_git_branch()}_android_{build_target}.zip"
        android_zip = os.path.join(self.archive_dir, zip_name)

        # 压缩后上传
        tar.compress(self.archive_dir, android_zip)
        dst = f"http://nexus.h3d.com.cn/repository/m2-pipeline/m2/client/m2_client/{zip_name}"
        self.nexus.upload(android_zip, dst, 600)
        env_mgr.set_app_url(dst)   
        # 记录appversion      # 有时间就不用记录序号了
        # global_env_mgr.set_app_version(env_mgr.get_app_version())

    def __clean_pack_cache(self):
        """
        清理上次构建的客户端缓存
        """
        if path_mgr.exists(self.archive_cache):
            path_mgr.rm(self.archive_cache)
        if path_mgr.exists(self.android_cache):
            path_mgr.rm(self.android_cache)
        # 清理上次构建的客户端
        if path_mgr.exists(self.archive_dir):
            path_mgr.rm(self.archive_dir)

    def __check_package_reuslt(self) -> bool:
        """
        检查打包结果
        """
        success_info = "FBuildAppModule::BuildFullApp, All finished.. Success!! !!"
        with open(self._pack_project_log, "r", encoding="utf-8") as f:
            log_content = f.read()
            if success_info in log_content:
                return True
            return False

    def __gen_appversion(self):
        now = datetime.now()
        version = jenkins_env_mgr.get_client_git_branch().replace("/", "_")
        version += "_android_"
        version += jenkins_env_mgr.get_pack_target()
        version += "." + now.strftime("%y.%m.%d.%H.%M") 
        env_mgr.set_app_version(version) 
        # old_version = global_env_mgr.get_app_version()
        # if old_version:    
        #     str_list = old_version.split(".")
        #     if len(str_list) < 7:
        #         PyframeException("gen app version error, format error")
        #     record_date = str_list[1] + "." + str_list[2] + "." + str_list[3]
        #     if record_date == now.strftime("%y.%m.%d"):
        #         version += "." + str(int(str_list[4]) + 1)
        #         return version           
        # version += ".1"
        return version

    def upload_build_log(self):
        """
        上传编译日志
        """
        log_path = self.get_log_path(BuildLogType.LOG)
        if path_mgr.exists(log_path):
            build_log_url = advance.upload_pipeline_log(path=log_path)
            env_mgr.set_build_log_url(log_url=build_log_url)
    def upload_ts_to_js_log(self):
        """
        上传ts转js日志
        """
        if path_mgr.exists(path=self._ts_to_js_log):
            log_url = advance.upload_pipeline_log(path=self._ts_to_js_log)
            env_mgr.set_ts_to_js_log_url(ts_to_js_log_url=log_url)
        else:
            log.warn(f"build log not exists: {self._gen_dts_log}")
    def upload_gen_dts_log(self):
        """
        上传生成dts日志
        """
        if path_mgr.exists(path=self._gen_dts_log):
            log_url = advance.upload_pipeline_log(path=self._gen_dts_log)
            env_mgr.set_gen_dts_log_url(gen_dts_log_url=log_url)
        else:
            log.warn(f"build log not exists: {self._gen_dts_log}")

    def upload_pack_log(self):
        """
        上传打包日志
        """
        if path_mgr.exists(self._pack_project_log):
            log_url = advance.upload_pipeline_log(self._pack_project_log)
            env_mgr.set_pack_log_url(log_url=log_url)

        log.info(f"ubt log: {self.ubt_log}")
        if path_mgr.exists(self.ubt_log):
            log_url = advance.upload_pipeline_log(self.ubt_log)
            env_mgr.set_ubt_log_url(log_url=log_url)

build_mgr = BuildMgr()
