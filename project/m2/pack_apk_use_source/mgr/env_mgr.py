from frame import env, common


class EnvMgr:

    @staticmethod
    def set_build_log_url(log_url: str):
        env.set({"ENGINE_BUILD_LOG_URL": log_url})

    @staticmethod
    def get_build_log_url():
        return env.get("ENGINE_BUILD_LOG_URL") or ""
    @staticmethod
    def get_engine_tag():
        return env.get("ENGINE_TAG")

    @staticmethod
    def set_engine_tag(tag: str):
        env.set({"ENGINE_TAG": tag})
    
    @staticmethod
    def get_plugin_tag():
        return env.get("PLUGIN_TAG")

    @staticmethod
    def set_plugin_tag(plugin_tag: str):
        env.set({"PLUGIN_TAG": plugin_tag})
    
    @staticmethod
    def get_ue_plugin_changelist():
        return env.get("UE_PLUGIN_CHANGELIST")

    @staticmethod
    def set_ue_plugin_changelist(plugin_changelist: str):
        env.set({"UE_PLUGIN_CHANGELIST": plugin_changelist})
    
    @staticmethod
    def get_client_git_latest_commit_time():
        return env.get("CLIENT_GIT_LATEST_COMMIT_TIME")

    @staticmethod
    def set_client_git_latest_commit_time(latest_commit_time: str):
        env.set({"CLIENT_GIT_LATEST_COMMIT_TIME": latest_commit_time})
   
    @staticmethod
    def get_client_git_latest_commit_id():
        return env.get("CLIENT_GIT_LATEST_COMMIT_ID")

    @staticmethod
    def set_client_git_latest_commit_id(latest_commit_id: str):
        env.set({"CLIENT_GIT_LATEST_COMMIT_ID": latest_commit_id})

    @staticmethod
    def get_client_git_latest_commit_msg():
        return env.get("CLIENT_GIT_LATEST_COMMIT_MSG")

    @staticmethod
    def set_client_git_latest_commit_msg(latest_commit_msg: str):
        env.set({"CLIENT_GIT_LATEST_COMMIT_MSG": latest_commit_msg})
    
    @staticmethod
    def set_client_git_latest_committer_name(latest_committer_name: str):
        env.set({"CLIENT_GIT_LATEST_COMMIT_NAME": latest_committer_name})

    @staticmethod
    def get_client_git_latest_committer_name():
        return env.get("CLIENT_GIT_LATEST_COMMIT_NAME")

    @staticmethod
    def set_client_git_latest_committer_email(latest_committer_email: str):
        env.set({"CLIENT_GIT_LATEST_COMMIT_EMAIL": latest_committer_email})

    @staticmethod
    def get_client_git_latest_committer_email():
        return env.get("CLIENT_GIT_LATEST_COMMIT_EMAIL")
    
    @staticmethod
    def get_p4_latest_changelist():
        return env.get(f"P4_LATEST_CHANGELIST")

    @staticmethod
    def set_p4_latest_changelist(latest_changelist: str):
        env.set({f"P4_LATEST_CHANGELIST": latest_changelist})
    
    @staticmethod
    def get_p4_latest_time():
        return env.get("P4_LATEST_TIME")

    @staticmethod
    def set_p4_latest_time(latest_time: str):
        env.set({"P4_LATEST_TIME": latest_time})

    @staticmethod
    def set_p4_latest_desc(latest_desc: str):
        env.set({"P4_LATEST_DESC": latest_desc})

    @staticmethod
    def get_p4_latest_desc():
        return env.get("P4_LATEST_DESC")
    
    @staticmethod
    def set_p4_latest_submitter(latest_submitter: str):
        env.set({"P4_LATEST_SUBMITTER": latest_submitter})

    @staticmethod
    def get_p4_latest_submitter():
        return env.get("P4_LATEST_SUBMITTER")
    @staticmethod
    def get_gen_dts_log_url():
        return env.get("GEN_DTS_LOG_URL")

    @staticmethod
    def set_gen_dts_log_url(gen_dts_log_url: str):
        env.set({"GEN_DTS_LOG_URL": gen_dts_log_url})
    @staticmethod
    def get_ts_to_js_log_url():
        return env.get("ts_to_js_log_url")

    @staticmethod
    def set_ts_to_js_log_url(ts_to_js_log_url: str):
        env.set({"ts_to_js_log_url": ts_to_js_log_url})

    @staticmethod
    def set_pack_log_url(log_url: str):
        env.set({"PACK_LOG_URL": log_url})

    @staticmethod
    def get_pack_log_url():
        return env.get("PACK_LOG_URL")

    @staticmethod
    def set_ubt_log_url(log_url: str):
        env.set({"UBT_LOG_URL": log_url})

    @staticmethod
    def get_ubt_log_url():
        return env.get("UBT_LOG_URL")

    @staticmethod
    def set_app_version(app_version: str):
        env.set({"APP_VERSION": app_version})
    @staticmethod
    def get_app_version():
        return env.get("APP_VERSION")
    @staticmethod
    def set_app_url(app_url: str):
        env.set({"APP_URL": app_url})

    @staticmethod
    def get_app_url():
        return env.get("APP_URL")

class JenkinsEnvMgr:
    """
    Jenkins环境变量管理器
    """

    @staticmethod
    def get_build_type():
        build_type = env.get("BUILD_TYPE")
        return build_type.split(",")

    @staticmethod
    def get_clean_cache():
        return common.str2bool(env.get("CLEAN_CACHE", "false"))

    @staticmethod
    def get_clean_engine_cache():
        return common.str2bool(env.get("CLEAN_ENGINE", "false"))

    @staticmethod
    def get_engine_branch():
        return env.get("ENGINE_BRANCH")
    
    @staticmethod
    def get_engine_plugin_tag():
        return env.get("ENGINE_PLUGIN_TAG")

    @staticmethod
    def get_p4_force():
        return common.str2bool(env.get("P4_FORCE", "false"))

    @staticmethod
    def get_p4_branch():
        return "trunk" if env.get("M2_GIT_BRANCH") == "master" else env.get("M2_GIT_BRANCH")  

    @staticmethod
    def get_client_git_branch():
        return env.get("M2_GIT_BRANCH")

    @staticmethod
    def get_pack_target():
        return env.get("PACK_TARGET")

    @staticmethod
    def get_extra_pack_param():
        return env.get("EXTRA_PACK_PARAM")


env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
