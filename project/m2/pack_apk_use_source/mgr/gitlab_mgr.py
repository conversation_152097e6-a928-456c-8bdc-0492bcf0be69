# coding=utf-8
import json
import re
import requests
import platform
from frame import *
from project.m2.pack_apk_use_source.mgr.env_mgr import env_mgr, jenkins_env_mgr
from project.m2.pack_apk_use_source.mgr.p4_mgr import client_p4_mgr

class TsGitMgr:
    def __init__(self):
        self._workdir = env.pipeline.workspace()
        self._client_git_branch = jenkins_env_mgr.get_client_git_branch()
        self._m2_git_mgr = GitMgr(workdir=self._workdir, project_name="M2_Client")
        self._client_git_url = "https://gitlab.h3d.com.cn/mug_m2/m2_client.git"
        # 引擎 git 相关参数
        self._engine_git_mgr = GitMgr(workdir=self._workdir, project_name="UE_5.1_T3")
        self._engine_branch = jenkins_env_mgr.get_engine_branch()
        self._engine_git_url = "https://gitlab.h3d.com.cn/team3/unreal-engine/UE_5.1_T3.git"

    def update_client(self):
        if not Path(os.path.join(self._workdir, "M2_Client", ".git")).exists():
            self._m2_git_mgr.clone_with_oauth2(url=self._client_git_url, branch=self._client_git_branch, oauth2="yZtE613Z2fPMvurzJF3r")
        else:
            try:
                self._m2_git_mgr.pull(branch=self._client_git_branch)
            except PyframeException:
                ret, output = cmd.run_shell(
                    cmds=[f"git clean TS_workspace/TsProj -xdf && git reset --hard origin/{self._client_git_branch} && git pull origin {self._client_git_branch}"],
                    workdir=os.path.join(self._workdir, "M2_Client"),
                )
                if ret != 0:
                    raise PyframeException(f"更新git代码失败: {output}")

        commit_id = self._m2_git_mgr.get_local_latest_commit_id(short=True)
        env_mgr.set_client_git_latest_commit_id(latest_commit_id=commit_id)
        commit_msg = self._m2_git_mgr.get_local_latest_commit_msg()
        env_mgr.set_client_git_latest_commit_msg(latest_commit_msg=commit_msg)
        committer_name = self._m2_git_mgr.get_local_latest_committer_name()
        env_mgr.set_client_git_latest_committer_name(latest_committer_name=committer_name)
        committer_email = self._m2_git_mgr.get_local_latest_committer_email()
        env_mgr.set_client_git_latest_committer_email(latest_committer_email=committer_email)

        commit_time = self._m2_git_mgr.get_local_latest_commit_time()
        env_mgr.set_client_git_latest_commit_time(latest_commit_time=commit_time)
    
    def update_engine(self):
        """
        更新引擎代码
        """
        # 先拉取 binaries, 用来判断该拉哪个tag
        client_p4_mgr.sync_binaries()
        last_change = client_p4_mgr.p4_client.get_latest_changes(path=f"//{client_p4_mgr.p4_env}/M2_Product/{jenkins_env_mgr.get_p4_branch()}/binaries/...#have")
        description = last_change["desc"]
        engine_tag_group = re.search(re.compile(r"engine_tag:(v\d+\.\d+\.\d+)", re.S), description)
        tag = ""
        if engine_tag_group:
            tag = engine_tag_group.group(1)
            env_mgr.set_engine_tag(tag)
        # 首次执行拉取默认 master
        if not self._engine_git_mgr.exist():
            self._engine_git_mgr.clone(url=self._engine_git_url, branch=self._engine_branch)

        self._engine_git_mgr.fetch(all=True)
        try:
            if tag:                
                log.info(f"tag: {tag}")
                self._engine_git_mgr.checkout(tag)
        except Exception as e:
            log.warn(f"切换分支报错: {e}")
            self.download_cache()
        # committer_email = self._engine_git_mgr.get_local_latest_committer_email()
        # env_mgr.set_engine_latest_committer_email(committer_email)
        # env_mgr.set_engine_git_content(content=self._engine_git_mgr.get_local_latest_commit_msg().strip("'"))

    def download_cache(self):
        """
        下载缓存
        """
        cache_params = f"--cache={self.get_cache_dir()} --force"
        commands = [f"Setup.bat {cache_params}" if platform.system() == "Windows" else f"./Setup.sh {cache_params}"]
        ret = cmd.run_shell(
            cmds=commands,
            workdir=os.path.join(self._workdir, "UE_5.1_T3"),
        )
        if ret[0] != 0:
            raise PyframeException(f"设置ue5.1缓存失败, 错误码为{ret[0]}")
    
    def get_cache_dir(self):
        return os.path.join(self._workdir, "unrealengine5_1_0_cache")
    

git_mgr = TsGitMgr()
