# coding=utf-8
import os
from frame import env, path_mgr


class LinkMgr:
    def __init__(self):
        self.workspace = os.path.join(env.pipeline.workspace())

    def link_git_to_p4(self):
        git_path = os.path.join(self.workspace, "M2_Client")
        work_path = os.path.join(self.workspace, "client")
        path_mgr.soft_link(src=os.path.join(git_path, "Source"), dst=os.path.join(work_path, "Source"))
        path_mgr.soft_link(src=os.path.join(git_path, "TS_workspace"), dst=os.path.join(work_path, "TS_workspace"))
        path_mgr.soft_link(src=os.path.join(git_path, "Plugins"), dst=os.path.join(work_path, "Plugins"))

    def link_p4_to_engine(self):
        built_engine = os.path.join(self.workspace, "UE_5.1_T3", "LocalBuilds", "Engine")
        ue_engine = os.path.join(self.workspace, "ue_engine")
        path_mgr.copy(src=built_engine, dst=ue_engine, overwrite=True)


link_mgr = LinkMgr()
