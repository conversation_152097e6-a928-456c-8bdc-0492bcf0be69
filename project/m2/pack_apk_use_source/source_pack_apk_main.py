from frame import *
from project.m2.pack_apk_use_source.mgr.build_mgr import build_mgr
from project.m2.pack_apk_use_source.mgr.gitlab_mgr import git_mgr
from project.m2.pack_apk_use_source.mgr.link_mgr import link_mgr
from project.m2.pack_apk_use_source.mgr.msg_mgr import Msg
from project.m2.pack_apk_use_source.mgr.p4_mgr import client_p4_mgr

@advance.stage(stage="更新引擎代码")
def update_engine(**kwargs):
    git_mgr.update_engine()


@advance.stage(stage="缓存引擎依赖")
def download_engine_cache(**kwargs):
    cache_dir = git_mgr.get_cache_dir()
    if not path_mgr.exists(cache_dir):
        git_mgr.download_cache()
    else:
        log.info(f"{cache_dir} 已存在，跳过下载引擎依赖")


@advance.stage(stage="更新客户端工程")
def update_client(**kwargs):
    git_mgr.update_client()
    # log.info("pass")

@advance.stage(stage="更新P4相关")
def update_p4(**kwargs):
    client_p4_mgr.sync_all()

@advance.stage(stage="创建软链接")
def create_symbolic_link(**kwargs):
    link_mgr.link_git_to_p4()

# 切换引擎
@advance.stage(stage="切换引擎")
def switch_engine(**kwargs):
    build_mgr.switch_engine(True)

@advance.stage(stage="编译源码")
def ue51_build(**kwargs):
    build_mgr.build_ue5_cpp()

@advance.stage(stage="导出配置")
def gen_config(**kwargs):
    build_mgr.gen_config()

@advance.stage(stage="导出ts")
def export_ts(**kwargs):
    build_mgr.gen_dts()


@advance.stage(stage="导出js")
def export_js(**kwargs):
    build_mgr.compile_ts()


@advance.stage(stage="语言表检查")
def check_xls_config(**kwargs):
    build_mgr.xls_config()

@advance.stage(stage="打安卓包")
def pack_android(**kwargs):
    build_mgr.pack_android()

# 上传符号表
@advance.stage(stage="上传符号表")
def upload_pdb(**kwargs):
    # log.info("pass")
    build_mgr.upload_pdb()

@advance.stage(stage="上传安卓包")
def upload_android(**kwargs):
    build_mgr.upload_android()



msg = Msg()
content = msg.get_engine_message()


def on_always(**kwargs):
    build_mgr.upload_build_log()
    build_mgr.upload_gen_dts_log()
    build_mgr.upload_ts_to_js_log()
    build_mgr.upload_pack_log()

def on_success():
    wechat.send_unicast_post_success(content=content)
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=998e2c2c-953e-4e1a-b9a7-53636701516c",
        content=content,
    )
    advance.insert_pipeline_history_on_success()


def on_failure():
    wechat.send_unicast_post_failure(content=content)
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=998e2c2c-953e-4e1a-b9a7-53636701516c",
        content=content,
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled():
    wechat.send_unicast_post_canceled(content=content)
    wechat.send_multicast_post_unstable(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=998e2c2c-953e-4e1a-b9a7-53636701516c",
        content=content,
    )
    advance.insert_pipeline_history_on_unstable()
