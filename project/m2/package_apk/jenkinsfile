node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "*************"
            customWorkspace "D:\\jen<PERSON>\\pack_apk"
        }
    }
    parameters {
        booleanParam(name: 'P4_FORCE', defaultValue: false, description: '是否强更p4，默认false')
        string(name: 'M2_GIT_BRANCH', defaultValue: "master", description: 'm2代码仓库分支', trim: true)
        choice(choices: ['Development', 'DebugGame', 'Debug','Shipping'], description: '编译版本', name: 'BUILD_TARGET')
        string(name: 'EXTRA_PACK_PARAM', defaultValue: "", description: '打包额外参数 A1=a,B1=b', trim: true)
    }
    options {
        disableConcurrentBuilds()
    }
    triggers {
        cron('H 6 * * *')
    }
    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        // 清理上次日志和制品
        stage("清理上次日志和制品") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=clean")
                    }
                }
            }
        }
        stage("更新GIT") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=update_git")
                    }
                }
            }
        }
        stage("更新P4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=update_p4")
                    }
                }
            }
        }
        stage("软链接") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=soft_link")
                    }
                }
            }
        }
        stage("切换引擎") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=switch_engine")
                    }
                }
            }
        }
        stage("编译C++") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=build_cpp")
                    }
                }
            }
        }
        stage("C++导出ts") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=gen_dts")
                    }
                }
            }
        }
        stage("语言表检查") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=check_xls_config")
                    }
                }
            }
        }
        stage("ts导出js") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=compile_ts")
                    }
                }
            }
        }
        // stage("生成manifest") {
        //     steps {
        //         dir("${env.GIT_NAME}") {
        //             script {
        //                 bat(script: "python m2.py package_apk --job=gen_resource_manifest")
        //             }
        //         }
        //     }
        // }
        stage("打包客户端") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=package_client")
                    }
                }
            }
        }
        stage("上传客户端") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py package_apk --job=upload_client")
                    }
                }
            }
        }
    }
    post {
       always {
           dir("${env.GIT_NAME}") {
               script {
                   bat(script: "python m2.py package_apk --job=on_always")
               }
           }
       }
        unstable {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python m2.py package_apk --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python m2.py package_apk --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python m2.py package_apk --job=on_failure")
                }
            }
        }
    }
}
