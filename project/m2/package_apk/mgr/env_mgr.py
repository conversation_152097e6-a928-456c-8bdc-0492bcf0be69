from frame import *


class JenkinsEnvMgr:
    @staticmethod
    def get_p4_force():
        return common.str2bool(env.get("P4_FORCE", "false"))

    @staticmethod
    def get_git_branch():
        return env.get("M2_GIT_BRANCH")

    @staticmethod
    def get_build_target():
        return env.get("BUILD_TARGET", "Development")

    @staticmethod
    def get_extra_pack_param():
        return env.get("EXTRA_PACK_PARAM")


class EnvMgr:
    @staticmethod
    def set_app_url(app_url: str):
        env.set({"APP_URL": app_url})

    @staticmethod
    def get_app_url():
        return env.get("APP_URL")

    @staticmethod
    def set_p4_latest_changelist(p4_latest_changelist: str):
        env.set({"P4_LATEST_CHANGELIST": p4_latest_changelist})

    @staticmethod
    def get_p4_latest_changelist():
        return env.get("P4_LATEST_CHANGELIST")

    # git_latest_commit_id
    @staticmethod
    def set_git_latest_commit_id(git_latest_commit_id: str):
        env.set({"GIT_LATEST_COMMIT_ID": git_latest_commit_id})

    @staticmethod
    def get_git_latest_commit_id():
        return env.get("GIT_LATEST_COMMIT_ID")

    @staticmethod
    def set_client_log_url(log_url: str):
        env.set({"LOG_URL": log_url})

    @staticmethod
    def get_client_log_url():
        return env.get("LOG_URL")

    @staticmethod
    def set_ubt_log_url(log_url: str):
        env.set({"UBT_LOG_URL": log_url})

    @staticmethod
    def get_ubt_log_url():
        return env.get("UBT_LOG_URL")


jenkins_env_mgr = JenkinsEnvMgr()
env_mgr = EnvMgr()
