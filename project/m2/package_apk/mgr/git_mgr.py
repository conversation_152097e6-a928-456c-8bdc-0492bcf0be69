from frame import *
from project.m2.package_apk.mgr.env_mgr import env_mgr, jenkins_env_mgr


class PackageGitMgr:
    def __init__(self):
        self.workspace = env.pipeline.workspace()
        self.git_branch = jenkins_env_mgr.get_git_branch()
        self.git_mgr = GitMgr(workdir=self.workspace, project_name="M2_Client")
        self.git_dir = os.path.join(self.workspace, "M2_Client")
        self.git_file = os.path.join(self.git_dir, ".git")
        self.git_url = "https://gitlab.h3d.com.cn/mug_m2/m2_client.git"
        self.pes_api_framework_loader = os.path.join(self.workspace, r"client\Plugins\Puerts\Source\JsEnv\Private\PesapiFrameworkLoader.mm")

    # 删除多余的git文件
    def __delete_git_file(self):
        """
        删除多余的git文件
        """
        if path_mgr.exists(self.pes_api_framework_loader):
            path_mgr.rm(self.pes_api_framework_loader)

    def update_client(self):
        if not path_mgr.exists(self.git_file):
            self.git_mgr.clone_with_oauth2(url=self.git_url, branch=self.git_branch, oauth2="yZtE613Z2fPMvurzJF3r")
        else:
            try:
                self.git_mgr.reset(commit_id="HEAD")
                self.git_mgr.advance_pull(branch=self.git_branch)
            except PyframeException:
                ret, output = cmd.run_shell(
                    cmds=[f"git clean TS_workspace/TsProj -xdf && git reset --hard origin/{self.git_branch} && git pull origin {self.git_branch}"],
                    workdir=self.git_dir,
                )
                if ret != 0:
                    raise PyframeException(f"更新git代码失败: {output}")
        commit_id = self.git_mgr.get_local_latest_commit_id(short=True)
        env_mgr.set_git_latest_commit_id(commit_id)
        self.__delete_git_file()


git_mgr = PackageGitMgr()
