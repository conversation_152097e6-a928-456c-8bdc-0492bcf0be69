# coding=utf-8
from frame import *


class LinkMgr:
    def __init__(self):
        self.workspace = env.pipeline.workspace()
        self.git_dir = os.path.join(self.workspace, "M2_Client")
        self.client_dir = os.path.join(self.workspace, "client")

    def link_git_to_p4(self):
        path_mgr.soft_link(src=os.path.join(self.git_dir, "Source"), dst=os.path.join(self.client_dir, "Source"))
        path_mgr.soft_link(src=os.path.join(self.git_dir, "TS_workspace"), dst=os.path.join(self.client_dir, "TS_workspace"))
        path_mgr.soft_link(src=os.path.join(self.git_dir, "Plugins"), dst=os.path.join(self.client_dir, "Plugins"))


link_mgr = LinkMgr()
