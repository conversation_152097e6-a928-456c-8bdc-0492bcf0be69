from frame import *
from project.m2 import config
from project.m2.package_apk.mgr.env_mgr import jenkins_env_mgr, env_mgr


class PackMgr:
    def __init__(self):
        self.workspace = env.pipeline.workspace()
        self.windows_engine = os.path.join(self.workspace, "ue_engine", "Windows")
        self.engine_workspace = os.path.join(self.windows_engine, "Engine")
        self.engine_cmd = os.path.join(self.engine_workspace, "Binaries", "Win64", "UnrealEditor-Cmd.exe")
        self.bat_dir = os.path.join(self.engine_workspace, "Build", "BatchFiles")
        self.uat_bat = os.path.join(self.bat_dir, "RunUAT.bat")
        self.ubt_exe = os.path.join(self.engine_workspace, "Binaries", "DotNET", "UnrealBuildTool", "UnrealBuildTool.exe")
        self.client_dir = os.path.join(self.workspace, "client")
        self.build_shell_dir = os.path.join(self.client_dir, "build_shell")
        # self.archive_dir = os.path.join(self.client_dir, "ArchivedBuilds")
        self.archive_dir = os.path.join(self.client_dir, r"Publish\BuildApp")
        self.archive_cache = os.path.join(self.client_dir, r"Publish\BuildCache")
        self.android_cache = os.path.join(self.client_dir, r"Binaries\Android")
        self.uproject_file = os.path.join(self.client_dir, "M2_Client.uproject")
        self.nexus = Nexus(**config.NEXUS_CONFIG)
        self.src_plugins_dir = os.path.join(self.workspace, r"binaries\Plugins")
        self.dst_plugins_dir = os.path.join(self.client_dir, r"Plugins")
        self.xls_config_dir = os.path.join(self.workspace, r"common\xls_config")
        self.xls_start_bat = os.path.join(self.xls_config_dir, "start.bat")
        self.compile_js = os.path.join(self.client_dir, r"TS_workspace\H3dCompiler\actions\H3dCompile.js")
        self.switcher = r"C:\Program Files (x86)\Epic Games\Launcher\Engine\Binaries\Win64\UnrealVersionSelector.exe"
        self.build_project_log = os.path.join(self.workspace, "logs", "build-project-log.txt")
        self.gen_dts_log = os.path.join(self.workspace, "logs", "gen-dts-log.txt")
        self.build_target = jenkins_env_mgr.get_build_target()
        self.build_client_log = os.path.join(self.workspace, "logs", "build-client-log.txt")
        suffix = self.windows_engine.replace(":\\", "+").replace("\\", "+")
        ubt_log_name = f"UBT-M2_Client-Android-{self.build_target}.txt"
        self.ubt_log = os.path.join(r"C:\Users\<USER>\AppData\Roaming\Unreal Engine\AutomationTool\Logs", suffix, ubt_log_name)

    def copy_plugins(self):
        """
        拷贝插件
        """
        path_mgr.xcopy(self.src_plugins_dir, self.dst_plugins_dir, dst_is_file=False)

    def switch_engine(self):
        """
        切换引擎版本
        """
        cmd_lines = f'"{self.switcher}"  /switchversionsilent  {self.uproject_file} {self.windows_engine}'
        ret = cmd.run_shell(cmds=[cmd_lines])
        if ret[0] != 0:
            raise PyframeException(f"引擎版本切换异常，请流水线组排查原因")

    def build_cpp(self):
        """
        编译C++
        """
        # 先生成解决方案
        cmd_lines = ""
        cmd_lines += f"{self.ubt_exe} -projectfiles -project={self.uproject_file} "
        cmd_lines += f"-log={self.build_project_log} -WaitMutex -FromMsBuild "
        ret, output = cmd.run_shell(cmds=[cmd_lines])
        if ret != 0:
            raise Exception(f"编译C++报错: {output}")

        # 然后再进行编译
        cmd_lines = f"{self.ubt_exe} M2_ClientEditor Win64 Development -Project={self.uproject_file}"
        ret = cmd.run_shell(cmds=[cmd_lines])
        if ret[0] != 0:
            raise PyframeException(f"编译C++报错，请程序检查错误")

    def _raise_gen_dts_exception(self, log_path: str = None):
        """
        解析生成dts错误
        """
        log.info("start parse gen dts log")
        errors = []
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            line = line.strip()
            if "Error:" in line:
                index = line.find("Error:")
                line = line[index:]
                if line not in errors:
                    errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"生成dts失败, 请PM协调相关同事解决问题\n{errors_str}")
        else:
            raise PyframeException("生成dts失败, 未知错误")

    def gen_dts(self):
        """
        C++导出ts
        """
        command = f"{self.engine_cmd} {self.uproject_file} -run=GenDTS ABSLOG={self.gen_dts_log}"
        command += f" -fastexit -nosplash -Unattended -nopause"
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self.build_shell_dir,
            encoding="utf-8",
            errors="ignore",
        )
        if ret[0] != 0:
            log.error("gen_dts fail")
            self._raise_gen_dts_exception(log_path=self.gen_dts_log)
            raise PyframeException(f"{command}失败")

    # ts 导出js
    def compile_ts(self):
        """
        ts导出js
        """

        ret = cmd.run_shell(
            cmds=[f"node.exe {self.compile_js}"],
            workdir=self.build_shell_dir,
        )
        if ret[0] != 0:
            log.error("compile_ts fail")
            raise PyframeException("compile_ts失败")

    def xls_config(self):
        """
        语言表检查
        """
        start_bat = path_mgr.exists(self.xls_start_bat)
        if not start_bat:
            raise PyframeException("语言表检查失败, start.bat不存在")
        ret, output = cmd.run_shell(cmds=["start.bat"], workdir=self.xls_config_dir)
        if ret != 0:
            log.error("language_xlx_check fail")
            raise PyframeException(f"语言表检查失败, 请程序检查问题: {output}")

    def gen_resource_manifest(self):
        """
        生成资源清单
        """
        ret = cmd.run_shell(
            cmds=[f"{self.engine_cmd} {self.uproject_file} -run=GenerateManifest"],
            workdir=self.build_shell_dir,
        )
        if ret[0] != 0:
            log.error(ret)
            raise PyframeException("gen_resource_manifest失败")

    # 清理上次构建的客户端
    def clean_client(self):
        """
        清理上次构建的客户端
        """
        if path_mgr.exists(self.archive_dir):
            path_mgr.rm(self.archive_dir)

    def __clean_cache(self):
        """
        清理上次构建的客户端缓存
        """
        if path_mgr.exists(self.archive_cache):
            path_mgr.rm(self.archive_cache)
        if path_mgr.exists(self.android_cache):
            path_mgr.rm(self.android_cache)

    def clean_log(self):
        """
        清理上次打包的日志
        """
        if path_mgr.exists(self.build_client_log):
            path_mgr.rm(self.build_client_log)
        if path_mgr.exists(self.ubt_log):
            path_mgr.rm(self.ubt_log)

    def __check_package_reuslt(self) -> bool:
        """
        检查打包结果
        """
        success_info = "FBuildAppModule::BuildFullApp, All finished.. Success!! !!"
        with open(self.build_client_log, "r", encoding="utf-8") as f:
            log_content = f.read()
            if success_info in log_content:
                return True
            return False

    def package_client(self):
        """
        打包客户端
        """
        # 打包前先清理上次构建的客户端以及缓存和日志
        # self.__clean_client()
        self.__clean_cache()
        # self.__clean_log()
        extra_param_str = jenkins_env_mgr.get_extra_pack_param()
        extra_param = []
        if extra_param_str is not None and len(extra_param_str) > 0:
            extra_param = extra_param_str.split(",")
        # 打包
        cmd_lines = ""
        # cmd_lines += f"RunUAT.bat BuildCookRun -project={self.uproject_file} -clean -nop4 -nocompileeditor "
        # cmd_lines += f"-build -cook -stage -package -platform=Android -cookflavor=ASTC -clientconfig=Development "
        # cmd_lines += f"-pak -prereqs -nodebuginfo -utf8output  -archive "
        cmd_lines += f"{self.engine_cmd}  {self.uproject_file} -run=BuildApp -android -BuildTarget={self.build_target} "
        for i in range(len(extra_param)):
            cmd_lines += f"-{extra_param[i]} "
        cmd_lines += f"ABSLOG={self.build_client_log}"
        log.info(f"cmd_lines: {cmd_lines}")
        _, output = cmd.run_shell(cmds=[cmd_lines], workdir=self.bat_dir, encoding="latin-1")

        is_success = self.__check_package_reuslt()
        if not is_success:
            raise Exception(f"打包客户端失败: {output}")
        # if ret != 0:
        #    raise Exception(f"打包客户端失败: {output}")

    def upload_log(self):
        """
        上传打包日志
        """
        if path_mgr.exists(self.build_client_log):
            log_url = advance.upload_pipeline_log(self.build_client_log)
            env_mgr.set_client_log_url(log_url=log_url)

        log.info(f"ubt log: {self.ubt_log}")
        if path_mgr.exists(self.ubt_log):
            log_url = advance.upload_pipeline_log(self.ubt_log)
            env_mgr.set_ubt_log_url(log_url=log_url)

    # 上传客户端
    def upload_client(self):
        """
        上传客户端
        """
        android_dir = os.path.join(self.archive_dir, "Android")
        if not path_mgr.exists(android_dir):
            raise PyframeException(f"未找到{android_dir}目录，打包可能存在异常，请检查后重新打包")

        # 组织客户端目录名称
        p4_changelist = env_mgr.get_p4_latest_changelist()
        git_branch = jenkins_env_mgr.get_git_branch()
        git_commit_id = env_mgr.get_git_latest_commit_id()
        build_target = self.build_target.lower() if self.build_target else ""
        zip_name = f"client_{git_branch}_git_{git_commit_id}_p4_{p4_changelist}_{build_target}.zip"
        android_zip = os.path.join(self.archive_dir, zip_name)

        # 压缩后上传
        tar.compress(self.archive_dir, android_zip)
        dst = f"http://nexus.h3d.com.cn/repository/m2-pipeline/m2/client/m2_client/{zip_name}"
        self.nexus.upload(android_zip, dst, 600)
        env_mgr.set_app_url(dst)

    @staticmethod
    def get_msg():
        msg = ""
        p4_force = jenkins_env_mgr.get_p4_force()
        msg += f"**是否强制更新P4**: {p4_force}\n"
        git_branch = jenkins_env_mgr.get_git_branch()
        msg += f"**M2代码仓库分支**: {git_branch}\n"
        p4_changelist = env_mgr.get_p4_latest_changelist()
        msg += f"**P4最新提交**: {p4_changelist}\n"
        git_latest_commit_id = env_mgr.get_git_latest_commit_id()
        msg += f"**Git最新提交**: {git_latest_commit_id}\n"
        app_url = env_mgr.get_app_url()
        if app_url:
            # app_name = app_url.split("/")[-1]
            msg += f"**APK下载地址**: [{app_url}#target=out]({app_url}#target=out)\n"

        client_log_url = env_mgr.get_client_log_url()
        if client_log_url:
            log_name = client_log_url.split("/")[-1]
            msg += f"**打包日志**: [{log_name}]({client_log_url})\n"

        ubt_log_url = env_mgr.get_ubt_log_url()
        if ubt_log_url:
            log_name = ubt_log_url.split("/")[-1]
            msg += f"**UBT 日志**: [{log_name}]({ubt_log_url})\n"

        return msg


pack_mgr = PackMgr()
