from frame import *

from project.m2.package_apk.mgr.git_mgr import git_mgr
from project.m2.package_apk.mgr.link_mgr import link_mgr
from project.m2.package_apk.mgr.p4_mgr import p4_mgr
from project.m2.package_apk.mgr.pack_mgr import pack_mgr


# 清理上次日志和制品
@advance.stage(stage="清理上次日志和制品")
def clean(**kwargs):
    pack_mgr.clean_log()
    pack_mgr.clean_client()


# 获取工程
@advance.stage(stage="更新git")
def update_git(**kwargs):
    git_mgr.update_client()


# 获取引擎和工程
@advance.stage(stage="更新p4")
def update_p4(**kwargs):
    p4_mgr.sync_all()


@advance.stage(stage="软链接")
def soft_link(**kwargs):
    link_mgr.link_git_to_p4()
    # pack_mgr.copy_plugins()


# 切换引擎
@advance.stage(stage="切换引擎")
def switch_engine(**kwargs):
    pack_mgr.switch_engine()


@advance.stage(stage="编译C++")
def build_cpp(**kwargs):
    pack_mgr.build_cpp()


# C++ 导出ts
@advance.stage(stage="C++导出ts")
@advance.timeout(seconds=60 * 60 * 1, exception_msg="C++导出ts超时")
def gen_dts(**kwargs):
    pack_mgr.gen_dts()


# ts 导出js
@advance.stage(stage="ts导出js")
@advance.timeout(seconds=600, exception_msg="ts导出js超时")
def compile_ts(**kwargs):
    pack_mgr.compile_ts()


# 生成manifest
@advance.stage(stage="生成manifest")
@advance.timeout(seconds=600, exception_msg="生成manifest超时")
def gen_resource_manifest(**kwargs):
    pack_mgr.xls_config()
    pack_mgr.gen_resource_manifest()


# 语言表检查
@advance.stage(stage="语言表检查")
def check_xls_config(**kwargs):
    pack_mgr.xls_config()


# 打包客户端
@advance.stage(stage="打包客户端")
def package_client(**kwargs):
    pack_mgr.package_client()


# 上传APK
@advance.stage(stage="上传APK")
def upload_client(**kwargs):
    pack_mgr.upload_client()


def on_always():
    pack_mgr.upload_log()


def on_success():
    content = pack_mgr.get_msg()
    wechat.send_unicast_post_success(content=content)
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=998e2c2c-953e-4e1a-b9a7-53636701516c",
        content=content,
    )
    advance.insert_pipeline_history_on_success()


def on_failure():
    content = pack_mgr.get_msg()
    wechat.send_unicast_post_failure(content=content)
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=998e2c2c-953e-4e1a-b9a7-53636701516c",
        content=content,
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled():
    content = pack_mgr.get_msg()
    wechat.send_unicast_post_canceled(content=content)
    wechat.send_multicast_post_unstable(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=998e2c2c-953e-4e1a-b9a7-53636701516c",
        content=content,
    )
    advance.insert_pipeline_history_on_unstable()
