from frame import *
from project.m2 import config
from project.m2.package_ipa.mgr.env_mgr import jenkins_env_mgr, env_mgr


class PackMgr:
    def __init__(self):
        self.workspace = env.pipeline.workspace()
        self.engine_dir = os.path.join(self.workspace, "UE_5.1_T3/Engine")
        self.bat_dir = os.path.join(self.engine_dir, "Build/BatchFiles")
        self.gen_sh = os.path.join(self.bat_dir, "Mac/GenerateProjectFiles.sh")
        self.ubt_sh = os.path.join(self.bat_dir, "RunUBT.sh")
        self.engine_editor = os.path.join(self.engine_dir, "Binaries/Mac/UnrealEditor.app/Contents/MacOS/UnrealEditor")
        self.client_dir = os.path.join(self.workspace, "client")
        self.build_shell_dir = os.path.join(self.client_dir, "build_shell")
        self.archive_dir = os.path.join(self.client_dir, "Publish/BuildApp")
        self.archive_cache = os.path.join(self.client_dir, "Publish/BuildCache")
        self.uproject_file = os.path.join(self.client_dir, "M2_Client.uproject")
        self.nexus = Nexus(**config.NEXUS_CONFIG)
        self.src_plugins_dir = os.path.join(self.workspace, "binaries/Plugins")
        self.dst_plugins_dir = os.path.join(self.client_dir, "Plugins")
        self.xls_config_dir = os.path.join(self.workspace, "common/xls_config")
        self.xls_start_bat = os.path.join(self.xls_config_dir, "start.sh")
        self.compile_js = os.path.join(self.client_dir, "TS_workspace/H3dCompiler/actions/H3dCompile.js")
        self.build_project_log = os.path.join(self.workspace, "logs", "build-project-log.txt")
        self.gen_dts_log = os.path.join(self.workspace, "logs", "gen-dts-log.txt")
        self.build_target = jenkins_env_mgr.get_build_target()
        self.build_client_log = os.path.join(self.workspace, "logs", "build-client-log.txt")
        self.ubt_plugin_csproj = os.path.join(
            self.client_dir, "Plugins/Puerts/Source/CSharpParamDefaultValueMetas/CSharpParamDefaultValueMetas.ubtplugin.csproj"
        )
        self.ubt_log = os.path.join(self.engine_dir, "Programs/UnrealBuildTool/Log.txt")
        self.ubt_gpf_log = os.path.join(self.engine_dir, "Programs/UnrealBuildTool/Log_GPF.txt")
        # suffix = self.windows_engine.replace(":\\", "+").replace("\\", "+")
        # ubt_log_name = f"UBT-M2_Client-Android-{self.build_target}.txt"
        # self.ubt_log = os.path.join(r"C:\Users\<USER>\AppData\Roaming\Unreal Engine\AutomationTool\Logs", suffix, ubt_log_name)

    def build_engine(self):
        cmd_lines = f"./RunUAT.command BuildGraph -Script=Engine/Build/InstalledEngineBuild.xml "
        cmd_lines += '-target="Make Installed Build Mac" '
        cmd_lines += "-set:WithWin64=false -set:WithWin32=false -set:WithMac=true -set:WithAndroid=false "
        cmd_lines += "-set:WithIOS=false -set:WithTVOS=false -set:WithLinux=false -set:WithLinuxAArch64=false "
        cmd_lines += "-set:WithLinuxArm64=false -set:WithLumin=false -set:WithDDC=false "
        ret, output = cmd.run_shell(cmds=[cmd_lines], workdir=self.bat_dir)
        if ret != 0:
            raise PyframeException(f"编译引擎失败: {output}")

    def __kill_dotnet(self):
        ret, output = cmd.run_shell(cmds=["pkill -9 dotnet"])
        log.info(f"ret: {ret} output: {output}")

    def gen_ue_project(self):
        """
        生成UE项目
        """
        # 开始前先清理dotnet进程
        self.__kill_dotnet()

        cmd_lines = f"sh {self.gen_sh} -game -projectfiles -project={self.uproject_file} -engine"
        ret, output = cmd.run_shell(cmds=[cmd_lines])
        if ret != 0:
            raise Exception(f"生成解决方案报错: {output}")

    def __delete_ubtplugins(self):
        """
        删除UBTPlugins
        """
        if path_mgr.exists(self.ubt_plugin_csproj):
            path_mgr.rm(self.ubt_plugin_csproj)

    def build_cpp(self):
        """
        编译C++
        """
        self.__kill_dotnet()
        # self.__delete_ubtplugins()
        cmd_lines = f"sh {self.ubt_sh} M2_ClientEditor Mac Development -Project={self.uproject_file} "
        cmd_lines += f"-architecture=x86_64 -SkipUBTBuild "
        ret = cmd.run_shell(cmds=[cmd_lines])
        if ret[0] != 0:
            raise PyframeException(f"编译C++报错，请程序检查错误")

    def _raise_gen_dts_exception(self, log_path: str = None):
        """
        解析生成dts错误
        """
        log.info("start parse gen dts log")
        errors = []
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            line = line.strip()
            if "Error:" in line:
                index = line.find("Error:")
                line = line[index:]
                if line not in errors:
                    errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"生成dts失败, 请PM协调相关同事解决问题\n{errors_str}")
        else:
            raise PyframeException("生成dts失败, 未知错误")

    def gen_dts(self):
        """
        C++导出ts
        """
        self.__kill_dotnet()
        command = f"{self.engine_editor} {self.uproject_file} -run=GenDTS ABSLOG={self.gen_dts_log}"
        command += f" -fastexit -nosplash -Unattended -nopause"
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self.build_shell_dir,
            encoding="utf-8",
            errors="ignore",
        )
        if ret[0] != 0:
            log.error("gen_dts fail")
            self._raise_gen_dts_exception(log_path=self.gen_dts_log)
            raise PyframeException(f"{command}失败")

    # ts 导出js
    def compile_ts(self):
        """
        ts导出js
        """

        ret = cmd.run_shell(
            cmds=[f"node {self.compile_js}"],
            workdir=self.build_shell_dir,
        )
        if ret[0] != 0:
            log.error("compile_ts fail")
            raise PyframeException("compile_ts失败")

    def xls_config(self):
        """
        语言表检查
        """
        start_bat = path_mgr.exists(self.xls_start_bat)
        if not start_bat:
            raise PyframeException("语言表检查失败, start.bat不存在")
        ret, output = cmd.run_shell(cmds=[f"{self.xls_start_bat}"], workdir=self.xls_config_dir)
        if ret != 0:
            log.error("language_xlx_check fail")
            raise PyframeException(f"语言表检查失败, 请程序检查问题: {output}")

    # 清理上次构建的客户端
    def clean_client(self):
        """
        清理上次构建的客户端
        """
        if path_mgr.exists(self.archive_dir):
            path_mgr.rm(self.archive_dir)

    def __clean_cache(self):
        """
        清理上次构建的客户端缓存
        """
        if path_mgr.exists(self.archive_cache):
            path_mgr.rm(self.archive_cache)

    def clean_log(self):
        """
        清理上次打包的日志
        """
        if path_mgr.exists(self.build_client_log):
            path_mgr.rm(self.build_client_log)
        if path_mgr.exists(self.ubt_log):
            path_mgr.rm(self.ubt_log)
        if path_mgr.exists(self.ubt_gpf_log):
            path_mgr.rm(self.ubt_gpf_log)
        if path_mgr.exists(self.gen_dts_log):
            path_mgr.rm(self.gen_dts_log)

    def __check_package_reuslt(self) -> bool:
        """
        检查打包结果
        """
        success_info = "FBuildAppModule::BuildFullApp, All finished.. Success!! !!"
        with open(self.build_client_log, "r", encoding="utf-8") as f:
            log_content = f.read()
            if success_info in log_content:
                return True
            return False

    def package_client(self):
        """
        打包客户端
        """
        self.__clean_cache()
        self.__kill_dotnet()
        # 打包
        cmd_lines = ""
        cmd_lines += f"{self.engine_editor}  {self.uproject_file} -run=BuildApp -ios -BuildTarget={self.build_target} "
        cmd_lines += f"ABSLOG={self.build_client_log}"
        log.info(f"cmd_lines: {cmd_lines}")
        _, output = cmd.run_shell(cmds=[cmd_lines], workdir=self.bat_dir, encoding="latin-1")

        is_success = self.__check_package_reuslt()
        if not is_success:
            raise Exception(f"打包客户端失败: {output}")

    def upload_log(self):
        """
        上传打包日志
        """
        if path_mgr.exists(self.build_client_log):
            log_url = advance.upload_pipeline_log(self.build_client_log)
            env_mgr.set_client_log_url(log_url=log_url)

        log.info(f"ubt log: {self.ubt_log}")
        if path_mgr.exists(self.ubt_log):
            log_url = advance.upload_pipeline_log(self.ubt_log)
            env_mgr.set_ubt_log_url(log_url=log_url)

        log.info(f"ubt gpf log: {self.ubt_gpf_log}")
        if path_mgr.exists(self.ubt_gpf_log):
            log_url = advance.upload_pipeline_log(self.ubt_gpf_log)
            env_mgr.set_ubt_gpf_log_url(log_url=log_url)

    # 上传客户端
    # def upload_client(self):
    #     """
    #     上传客户端
    #     """
    #     ios_dir = os.path.join(self.archive_dir, "IOS")
    #     if not path_mgr.exists(ios_dir):
    #         raise PyframeException(f"未找到{ios_dir}目录，打包可能存在异常，请检查后重新打包")

    #     # 组织客户端目录名称
    #     p4_changelist = env_mgr.get_p4_latest_changelist()
    #     git_branch = jenkins_env_mgr.get_m2_git_branch()
    #     git_commit_id = env_mgr.get_git_latest_commit_id()
    #     build_target = self.build_target.lower() if self.build_target else ""
    #     zip_name = f"client_{git_branch}_git_{git_commit_id}_p4_{p4_changelist}_{build_target}.zip"
    #     ios_zip = os.path.join(self.archive_dir, zip_name)

    #     # 压缩后上传
    #     tar.compress(self.archive_dir, ios_zip)
    #     dst = f"http://nexus.h3d.com.cn/repository/m2-pipeline/m2/client/m2_client/{zip_name}"
    #     env_mgr.set_app_url(dst)
    #     self.nexus.upload(ios_zip, dst)

    # 计算包大小
    def __get_size(self):
        """
        计算包大小
        """
        ipa = os.path.join(self.archive_dir, "IOS", "M2_Client.ipa")
        if path_mgr.exists(ipa):
            size = os.path.getsize(ipa)
            size_gb = size / 1024 / 1024 / 1024
            size_gb = round(size_gb, 2)
            env_mgr.set_app_size(size_gb)

    # 上传共享
    def upload_smb(self, ios_zip: str, zip_name: str):
        with SMBClient(username="guest", password="", server_ip="f.h3d.com.cn") as c:
            c.upload(ios_zip, "M2", f"手机体验包/iOS/{zip_name}")
        dst = f"\/\/f.h3d.com.cn\M2\手机体验包\iOS\{zip_name}"
        env_mgr.set_share_add(dst)

    # 上传nexus
    def upload_nexus(self, ios_zip: str, zip_name: str):
        dst = f"http://nexus.h3d.com.cn/repository/m2-pipeline/m2/client/m2_client/{zip_name}"
        self.nexus.upload(ios_zip, dst)
        env_mgr.set_app_url(dst)

    def upload_client(self):
        """
        上传客户端
        """
        ios_dir = os.path.join(self.archive_dir, "IOS")
        if not path_mgr.exists(ios_dir):
            raise PyframeException(f"未找到{ios_dir}目录，打包可能存在异常，请检查后重新打包")

        # 组织客户端目录名称
        p4_changelist = env_mgr.get_p4_latest_changelist()
        git_branch = jenkins_env_mgr.get_m2_git_branch()
        git_commit_id = env_mgr.get_git_latest_commit_id()
        build_target = self.build_target.lower() if self.build_target else ""
        zip_name = f"client_{git_branch}_git_{git_commit_id}_p4_{p4_changelist}_{build_target}.zip"
        ios_zip = os.path.join(self.archive_dir, zip_name)

        # 压缩后上传
        tar.compress(self.archive_dir, ios_zip)

        # 计算包大小
        self.__get_size()
        # 根据包大小选择上传方式
        app_size = env_mgr.get_app_size()
        if app_size > 2:
            self.upload_smb(ios_zip, zip_name)
        else:
            self.upload_nexus(ios_zip, zip_name)

    @staticmethod
    def get_msg():
        msg = ""
        p4_force = jenkins_env_mgr.get_p4_force()
        msg += f"**是否强制更新P4**: {p4_force}\n"
        git_branch = jenkins_env_mgr.get_m2_git_branch()
        msg += f"**M2代码仓库分支**: {git_branch}\n"
        p4_changelist = env_mgr.get_p4_latest_changelist()
        msg += f"**P4最新提交**: {p4_changelist}\n"
        git_latest_commit_id = env_mgr.get_git_latest_commit_id()
        msg += f"**Git最新提交**: {git_latest_commit_id}\n"
        app_url = env_mgr.get_app_url()
        if app_url:
            app_name = app_url.split("/")[-1]
            msg += f"**IPA下载地址**: [{app_name}]({app_url})\n"

        share_add = env_mgr.get_share_add()
        if share_add:
            msg += f"**IPA共享地址**: {share_add}\n"

        app_size = env_mgr.get_app_size()
        if app_size:
            msg += f"**IPA大小**: {app_size}G\n"

        ubt_log_url = env_mgr.get_ubt_log_url()
        if ubt_log_url:
            log_name = ubt_log_url.split("/")[-1]
            msg += f"**UBT 日志**: [{log_name}]({ubt_log_url})\n"

        ubt_gpf_log_url = env_mgr.get_ubt_gpf_log_url()
        if ubt_gpf_log_url:
            log_name = ubt_gpf_log_url.split("/")[-1]
            msg += f"**UBT GPF 日志**: [{log_name}]({ubt_gpf_log_url})\n"

        gen_dts_log_url = env_mgr.get_gen_dts_log_url()
        if gen_dts_log_url:
            log_name = gen_dts_log_url.split("/")[-1]
            msg += f"**生成dts日志**: [{log_name}]({gen_dts_log_url})\n"

        client_log_url = env_mgr.get_client_log_url()
        if client_log_url:
            log_name = client_log_url.split("/")[-1]
            msg += f"**打包日志**: [{log_name}]({client_log_url})\n"
        return msg


pack_mgr = PackMgr()
