node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}
def clone_pyframe_pipeline() {
    def branch = "master"
    if (!fileExists("pyframe-pipeline/.git")) {
        bat """
        git clone -b $branch https://gitlab.h3d.com.cn/dev-productivity/pyframe-pipeline.git
        cd pyframe-pipeline
        git config --local user.name "<EMAIL>"
        git config --local user.email "<EMAIL>"
        """
    }else{
        dir('pyframe-pipeline') {
            bat """
            git config --local user.name "<EMAIL>"
            git config --local user.email "<EMAIL>"
            git config pull.rebase false
            git clean -xdf -e logs
            git reset --hard HEAD
            git pull origin $branch --quiet
            """
        }
    }
    dir('pyframe-pipeline') {
        bat """
        python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn 
        python -m pip install -r requirements.txt -i  http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn
        """
    }
}

def p4_auto_branch = "$env.BRANCH_NAME" == 'master' ? 'trunk' : "$env.BRANCH_NAME"

pipeline {
    agent {
        node {
            label "m2_publish_all"
            customWorkspace "D://j//publish/$env.BRANCH_NAME"
        }
    }
    parameters {
        booleanParam(name: 'P4_FORCE', defaultValue: false, description: '是否强更p4')
        string(name: 'ENGINE_GIT_TAG', defaultValue: "", description: '引擎代码仓库tag', trim: true)
        string(name: 'ENGINE_PLUGIN_TAG', defaultValue: "", description: 'engine_plugin_tag', trim: true)
        string(name: 'ENGINE_TARGET_BRANCH', defaultValue: "5.3master", description: '引擎代码仓库合并的目标分支', trim: true)
        string(name: 'DEPLOY_ENVIRONMENT', defaultValue: 'prod', description: '部署环境: dev or prod')
        hidden(name: 'P4_BRANCH', defaultValue: "$p4_auto_branch", description: 'p4分支')
        hidden(name: 'M2_GIT_BRANCH', defaultValue: "$env.BRANCH_NAME", description: 'm2代码仓库分支')
        // extendedChoice(
        //     name: 'BUILD_TYPE',
        //     defaultValue: 'WithWin64,WithAndroid',
        //     description: '编译类型',
        //     multiSelectDelimiter: ',',
        //     quoteValue: false,
        //     saveJSONParameterToFile: false,
        //     type: 'PT_CHECKBOX',
        //     value:'WithWin64,WithAndroid',
        //     visibleItemCount: 5
        // )
    }
    options {
        disableConcurrentBuilds()
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
		stage("下载pyframe脚本") {
            steps {
                clone_pyframe_pipeline()
            }

        }	
        stage("更新引擎代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=update_engine")
                    }
                }
            }
        }
        stage("缓存引擎依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=download_engine_cache")
                    }
                }
            }
        }
        stage("更新客户端工程") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=update_client")
                    }
                }
            }
        }
        stage("更新P4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=update_p4")
                    }
                }
            }
        }
        stage("创建软链接") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=create_symbolic_link")
                    }
                }
            }
        }
        stage("编译源码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=ue51_build")
                    }
                }
            }
        }
        stage("C++导出ts") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=export_ts")
                    }
                }
            }
        }
        stage("语言表检查") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=check_xls_config")
                    }
                }
            }
        }
        stage("ts导出js") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=export_js")
                    }
                }
            }
        }
        stage("拷贝结果") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=copy_result")
                    }
                }
            }
        }
        stage("提交p4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=submit_p4")
                    }
                }
            }
        }
        stage("合并引擎代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python m2.py publish_all --job=merge_engine")
                    }
                }
            }
        }
        

    }
    post {
        always {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python m2.py publish_all --job=on_always")
                }
            }
        }
        unstable {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python m2.py publish_all --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python m2.py publish_all --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python m2.py publish_all --job=on_failure")
                }
            }
        }
    }
}
