# coding=utf-8
import platform
import time
from enum import Enum

from frame import *
from project.m2 import config
from project.m2.publish_all.mgr.env_mgr import env_mgr, jenkins_env_mgr, global_env_mgr


class BuildLogType(Enum):
    LOG = "log.txt"
    UBT_UNREAL_EDITOR_WIN64_DEBUG_GAME = "UBT-UnrealEditor-Win64-DebugGame.txt"
    UBT_UNREAL_EDITOR_WIN64_DEVELOPMENT = "UBT-UnrealEditor-Win64-Development.txt"
    UBT_UNREAL_HEADER_TOOL_MAC_DEVELOPMENT = "UBT-UnrealHeaderTool-Mac-Development.txt"
    UBT_UNREAL_HEADER_TOOL_WIN64_DEVELOPMENT = "UBT-UnrealHeaderTool-Win64-Development.txt"


class BuildMgr:
    def __init__(self) -> None:
        self.workspace = os.path.join(env.pipeline.workspace())
        self.build_type_list = jenkins_env_mgr.get_build_type() #注释了, 源码编译之后, 没有这个参数了, 产物是否一致得再观察观察
        self.ue_engine = os.path.join(self.workspace, "ue_engine")
        # 客户端相关的
        self._client_p4_branch = jenkins_env_mgr.get_p4_branch()
        self._client_git_branch = jenkins_env_mgr.get_client_git_branch()

        self._log_path = os.path.join(self.workspace, "client", "logs")
        self._build_log_path = os.path.join(self._log_path, "build_logs")
        if not self._build_log_path:
            path_mgr.mkdir(self._build_log_path)
        self._gen_dts_log_path = os.path.join(self._log_path, "gen_dts_logs")
        if not self._gen_dts_log_path:
            path_mgr.mkdir(self._gen_dts_log_path)
        self._ts_to_js_path = os.path.join(self.workspace, "ts_to_js_logs")
        if not path_mgr.exists(self._ts_to_js_path):
            path_mgr.mkdir(self._ts_to_js_path)
        self._build_log = os.path.join(self._build_log_path, f"build_ue5_log_{env.pipeline.build_num()}.log")
        self._ts_to_js_log = os.path.join(self._ts_to_js_path, f"ts_to_js_{env.pipeline.build_num()}.log")
        self._gen_dts_log = os.path.join(self._gen_dts_log_path, f"gen_dts_{env.pipeline.build_num()}.log")
        self._unreal_editor_cmd = os.path.join(self.workspace, r"UE_5.1_T3\Engine\Binaries\Win64\UnrealEditor-Cmd.exe")
        self._uproject_path = os.path.join(self.workspace, r"client\M2_Client.uproject")
        self._build_shell_path = os.path.join(self.workspace, r"client\build_shell")
        # self._build_bat = os.path.join(self.workspace, r"UE_5.1_T3\Engine\Build\BatchFiles\Build.bat")
        self._build_bat = os.path.join(self.workspace, r"ue_engine\Windows\Engine\Build\BatchFiles\Build.bat")
        self._m2_project = os.path.join(self.workspace, "client")
        self._client_project = os.path.join(self._m2_project, r"client\M2_Client.uproject")
        self._xls_config_path = os.path.join(self.workspace, r"common\xls_config")

    def get_log_path(self, log_type: BuildLogType) -> str:
        """
        获取日志路径
        """
        log_path = os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Programs", "AutomationTool", "Saved", "Logs", log_type.value)
        return log_path

    def raise_build_exception(self, log_path: str):
        """
        解析编译错误
        """
        log.info("start parse build log")
        if not path_mgr.exists(log_path):
            log.warn("log.txt not exists")
            return
        errors = []
        encoding = "gb2312" if platform.system() == "Windows" else "utf-8"
        f = open(log_path, "r", encoding=encoding)
        line = f.readline()
        while line:
            line = line.strip()
            if "A conflicting instance of AutomationTool is already running." in line:
                raise PyframeException("编译ue5.1引擎失败, A conflicting instance of AutomationTool is already running.")

            if "Unable to find valid certificate/mobile provision pair." in line:
                raise PyframeException("编译ue5.1引擎失败, Unable to find valid certificate/mobile provision pair.")

            if "Host key verification failed" in line:
                raise PyframeException("编译ue5.1引擎失败, Host key verification failed, 请检查ssh key是否正确")

            if "Platform Android is not a valid platform to build. Check that the SDK is installed properly." in line:
                raise PyframeException("编译ue5.1引擎失败, Platform Android is not a valid platform to build. Check that the SDK is installed properly.")

            if "Unable to find mobile provision for UnrealGame" in line:
                raise PyframeException("编译ue5.1引擎失败, Unable to find mobile provision for UnrealGame")

            if "A conflicting instance of UnrealBuildTool is already running." in line:
                raise PyframeException("编译ue5.1引擎失败, A conflicting instance of UnrealBuildTool is already running.")

            if "Failed to load profile" in line:
                errors.append(line)

            if "MSBUILD : error" in line:
                errors.append(line)

            if "ERROR:" in line:
                errors.append(line)

            if ": error" in line:
                errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"编译ue5.1引擎失败: \n{errors_str}")
        else:
            raise PyframeException("编译ue5.1引擎失败, 未知错误")

    # def build_engine(self):
    #     """
    #     编译引擎
    #     """

    #     win64 = "true" if "WithWin64" in self.build_type_list else "false"
    #     android = "true" if "WithAndroid" in self.build_type_list else "false"
    #     ios = "true" if "WithIOS" in self.build_type_list else "false"
    #     mac = "true" if "WithMac" in self.build_type_list else "false"

    #     # 根据平台不同组织不同的命令
    #     scripts = "-Script=Engine/Build/InstalledEngineBuild.xml"
    #     if platform.system() == "Windows":
    #         cmd_prefix = f'RunUAT.bat BuildGraph {scripts} -target="Make Installed Build Win64" '
    #     else:
    #         cmd_prefix = f'./RunUAT.command BuildGraph {scripts} -target="Make Installed Build Mac" '
    #     commands = [
    #         f"{cmd_prefix} -set:WithWin64={win64} "
    #         "-set:WithWin32=false "
    #         f"-set:WithMac={mac} "
    #         f"-set:WithAndroid={android} "
    #         f"-set:WithIOS={ios} "
    #         "-set:WithTVOS=false "
    #         "-set:WithLinux=false "
    #         "-set:WithLinuxAArch64=false "
    #         "-set:WithLinuxArm64=false "
    #         "-set:WithLumin=false "
    #         "-set:WithHoloLens=false "
    #         "-set:WithDDC=false "
    #         "-set:GameConfigurations=Development "
    #         f"-set:BuiltDirectory={self.ue_engine} "
    #     ]
    #     start_time = time.time()
    #     ret = cmd.run_shell(
    #         cmds=commands,
    #         workdir=os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Build", "BatchFiles"),
    #         return_output=True,
    #         errors="ignore",
    #         encoding="gb2312" if platform.system() == "Windows" else "utf-8",
    #         log_to_file="ue5.1_build.log",
    #     )

    #     # 检查是否有编译实例在运行，此时返回值是0
    #     if time.time() - start_time < 60 * 2:
    #         self.raise_build_exception(log_path="ue5.1_build.log")

    #     if ret[0] != 0:
    #         # 解析错误
    #         self.raise_build_exception(log_path="ue5.1_build.log")
    #         raise PyframeException(f"编译ue5.1引擎失败, 错误码为{ret[0]}")

    def build_ue5_cpp(self):
        """
        编译C++
        """
        # 先编译引擎
        if jenkins_env_mgr.get_engine_git_tag():
            win64 = "true" if "WithWin64" in self.build_type_list else "false"
            android = "true" if "WithAndroid" in self.build_type_list else "false"
            ios = "true" if "WithIOS" in self.build_type_list else "false"
            mac = "true" if "WithMac" in self.build_type_list else "false"

            # 根据平台不同组织不同的命令
            scripts = "-Script=Engine/Build/InstalledEngineBuild.xml"
            if platform.system() == "Windows":
                cmd_prefix = f'RunUAT.bat BuildGraph {scripts} -target="Make Installed Build Win64" '
            else:
                cmd_prefix = f'./RunUAT.command BuildGraph {scripts} -target="Make Installed Build Mac" '
            commands = [
                f"{cmd_prefix} -set:WithWin64={win64} "
                "-set:WithWin32=false "
                f"-set:WithMac={mac} "
                f"-set:WithAndroid={android} "
                f"-set:WithIOS={ios} "
                "-set:WithTVOS=false "
                "-set:WithLinux=false "
                "-set:WithLinuxAArch64=false "
                "-set:WithLinuxArm64=false "
                "-set:WithLumin=false "
                "-set:WithHoloLens=false "
                "-set:WithDDC=false "
                "-set:GameConfigurations=Development "
                f"-set:BuiltDirectory={self.ue_engine} "
            ]
            start_time = time.time()
            ret = cmd.run_shell(
                cmds=commands,
                workdir=os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Build", "BatchFiles"),
                return_output=True,
                errors="ignore",
                encoding="gb2312" if platform.system() == "Windows" else "utf-8",
                log_to_file="ue5.1_build.log",
            )

            # 检查是否有编译实例在运行，此时返回值是0
            if time.time() - start_time < 60 * 2:
                self.raise_build_exception(log_path="ue5.1_build.log")

            if ret[0] != 0:
                # 解析错误
                self.raise_build_exception(log_path="ue5.1_build.log")
                raise PyframeException(f"编译ue5.1引擎失败, 错误码为{ret[0]}")

        cmd_line = ""
        cmd_line += f"{self._build_bat} -log={self._build_log} "
        cmd_line += f"M2_ClientEditor Win64 Development "
        cmd_line += rf'-Project="{self._m2_project}\M2_Client.uproject" '
        cmd_line += f"-WaitMutex -FromMsBuild"
        ret = cmd.run_shell(
            cmds=[cmd_line],
            workdir=self._m2_project,
            log_to_file=f"build_ue5_cpp_{env.pipeline.build_num()}.log",
        )
        if ret[0] != 0:
            self.__raise_build_exception(log_path=self._build_log)

    def __raise_build_exception(self, log_path: str = None):
        """
        解析编译错误
        """
        log.info("start parse build log")
        errors = []
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            line = line.strip()
            if "The process cannot access the file because it is being used by another process" in line:
                raise PyframeException("编译C++失败, 同一时间只能有一个项目进行编译")

            if "Maximum number of concurrent builds reached" in line:
                raise PyframeException("编译C++失败, 达到并发构建的最大数量")

            if "exception" in line or ": error" in line or ": fatal error" in line or ": Error: " in line:
                errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"编译C++失败, 请程序检查代码问题\n{errors_str}")
        else:
            raise PyframeException("编译C++失败, 未知错误")

    def gen_dts(self):
        """
        生成dts
        """
        command = f"{self._unreal_editor_cmd} {self._uproject_path} -run=GenDTS ABSLOG={self._gen_dts_log}"
        command += f" -fastexit -nosplash -Unattended -nopause"
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self._build_shell_path,
            encoding="utf-8",
            errors="ignore",
        )
        if ret[0] != 0:
            log.error("gen_dts fail")
            self._raise_gen_dts_exception(log_path=self._gen_dts_log)
            raise PyframeException("gen_dts失败")

    def _raise_gen_dts_exception(self, log_path: str = None):
        """
        解析生成dts错误
        """
        log.info("start parse gen dts log")
        errors = []
        f = open(log_path, "r", encoding="utf-8")
        line = f.readline()
        while line:
            line = line.strip()
            if "Error:" in line:
                index = line.find("Error:")
                line = line[index:]
                if line not in errors:
                    errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"生成dts失败, 请程序检查代码问题\n{errors_str}")
        else:
            raise PyframeException("生成dts失败, 未知错误")

    def compile_ts(self):
        """
        编译ts
        """
        js_path = os.path.join(self.workspace, r"client\TS_workspace\H3dCompiler\actions\H3dCompile.js")
        ret = cmd.run_shell(
            cmds=[f"node.exe {js_path}"],
            workdir=self._build_shell_path,
            log_to_file=self._ts_to_js_log
        )
        if ret[0] != 0:
            log.error("compile_ts fail")
            raise PyframeException("compile_ts失败")

    def xls_config(self):
        """
        语言表检查
        """
        start_bat = path_mgr.exists(path=os.path.join(self._xls_config_path, "start.bat"))
        if not start_bat:
            raise PyframeException("语言表检查失败, start.bat不存在")
        ret = cmd.run_shell(cmds=["start.bat"], workdir=self._xls_config_path, verbose=cmd.Verbose.LOG)
        if ret[0] != 0:
            log.error("language_xlx_check fail")
            raise PyframeException("语言表检查失败, 请程序检查问题")

    def gen_resource_manifest(self):
        """
        生成资源清单
        """
        ret = cmd.run_shell(
            cmds=[f"{self._unreal_editor_cmd} {self._uproject_path} -run=GenerateManifest"],
            workdir=self._build_shell_path,
        )
        if ret[0] != 0:
            log.error(ret)
            raise PyframeException("gen_resource_manifest失败")

    def upload_build_log(self):
        """
        上传编译日志
        """
        log_path = self.get_log_path(BuildLogType.LOG)
        if path_mgr.exists(log_path):
            build_log_url = advance.upload_pipeline_log(path=log_path)
            env_mgr.set_build_log_url(log_url=build_log_url)
    def upload_ts_to_js_log(self):
        """
        上传ts转js日志
        """
        if path_mgr.exists(path=self._ts_to_js_log):
            log_url = advance.upload_pipeline_log(path=self._ts_to_js_log)
            env_mgr.set_ts_to_js_log_url(ts_to_js_log_url=log_url)
        else:
            log.warn(f"build log not exists: {self._gen_dts_log}")
    def upload_gen_dts_log(self):
        """
        上传生成dts日志
        """
        if path_mgr.exists(path=self._gen_dts_log):
            log_url = advance.upload_pipeline_log(path=self._gen_dts_log)
            env_mgr.set_gen_dts_log_url(gen_dts_log_url=log_url)
        else:
            log.warn(f"build log not exists: {self._gen_dts_log}")

build_mgr = BuildMgr()
