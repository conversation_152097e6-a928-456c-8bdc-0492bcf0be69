from frame import env, common


class EnvMgr:
    @staticmethod
    def set_engine_latest_commit_msg(commit_msg: str):
        env.set({"ENGINE_LATEST_COMMIT_MSG": commit_msg})

    @staticmethod
    def get_engine_latest_commit_msg():
        return env.get("ENGINE_LATEST_COMMIT_MSG")

    @staticmethod
    def set_build_log_url(log_url: str):
        env.set({"ENGINE_BUILD_LOG_URL": log_url})

    @staticmethod
    def get_build_log_url():
        return env.get("ENGINE_BUILD_LOG_URL") or ""

    @staticmethod
    def set_engine_latest_committer_email(email: str):
        env.set({"ENGINE_LATEST_COMMITTER_EMAIL": email})

    @staticmethod
    def get_engine_latest_committer_email():
        return env.get("ENGINE_LATEST_COMMITTER_EMAIL")

    @staticmethod
    def set_git_tag_content(content: str):
        return env.set({"GIT_TAG_CONTENT": content})

    @staticmethod
    def get_git_tag_content():
        return env.get("GIT_TAG_CONTENT")
    
  
    @staticmethod
    def get_engine_tag():
        return env.get("ENGINE_TAG")

    @staticmethod
    def set_engine_tag(engine_tag: str):
        env.set({"ENGINE_TAG": engine_tag})
    
    @staticmethod
    def get_plugin_tag():
        return env.get("PLUGIN_TAG")

    @staticmethod
    def set_plugin_tag(plugin_tag: str):
        env.set({"PLUGIN_TAG": plugin_tag})

    @staticmethod
    def get_engine_changelist():
        return env.get("ENGINE_CHANGELIST")

    @staticmethod
    def set_engine_changelist(engine_changelist: str):
        env.set({"ENGINE_CHANGELIST": engine_changelist})
    
    @staticmethod
    def get_ue_plugin_changelist():
        return env.get("UE_PLUGIN_CHANGELIST")

    @staticmethod
    def set_ue_plugin_changelist(plugin_changelist: str):
        env.set({"UE_PLUGIN_CHANGELIST": plugin_changelist})
    
    @staticmethod
    def get_client_git_latest_commit_time():
        return env.get("CLIENT_GIT_LATEST_COMMIT_TIME")

    @staticmethod
    def set_client_git_latest_commit_time(latest_commit_time: str):
        env.set({"CLIENT_GIT_LATEST_COMMIT_TIME": latest_commit_time})
   
    @staticmethod
    def get_client_git_latest_commit_id():
        return env.get("CLIENT_GIT_LATEST_COMMIT_ID")

    @staticmethod
    def set_client_git_latest_commit_id(latest_commit_id: str):
        env.set({"CLIENT_GIT_LATEST_COMMIT_ID": latest_commit_id})

    @staticmethod
    def get_client_git_latest_commit_msg():
        return env.get("CLIENT_GIT_LATEST_COMMIT_MSG")

    @staticmethod
    def set_client_git_latest_commit_msg(latest_commit_msg: str):
        env.set({"CLIENT_GIT_LATEST_COMMIT_MSG": latest_commit_msg})
    
    @staticmethod
    def set_client_git_latest_committer_name(latest_committer_name: str):
        env.set({"CLIENT_GIT_LATEST_COMMIT_NAME": latest_committer_name})

    @staticmethod
    def get_client_git_latest_committer_name():
        return env.get("CLIENT_GIT_LATEST_COMMIT_NAME")

    @staticmethod
    def set_client_git_latest_committer_email(latest_committer_email: str):
        env.set({"CLIENT_GIT_LATEST_COMMIT_EMAIL": latest_committer_email})

    @staticmethod
    def get_client_git_latest_committer_email():
        return env.get("CLIENT_GIT_LATEST_COMMIT_EMAIL")
    
    @staticmethod
    def get_p4_latest_changelist():
        return env.get(f"P4_LATEST_CHANGELIST")

    @staticmethod
    def set_p4_latest_changelist(latest_changelist: str):
        env.set({f"P4_LATEST_CHANGELIST": latest_changelist})
    
    @staticmethod
    def get_p4_latest_time():
        return env.get("P4_LATEST_TIME")

    @staticmethod
    def set_p4_latest_time(latest_time: str):
        env.set({"P4_LATEST_TIME": latest_time})

    @staticmethod
    def set_p4_latest_desc(latest_desc: str):
        env.set({"P4_LATEST_DESC": latest_desc})

    @staticmethod
    def get_p4_latest_desc():
        return env.get("P4_LATEST_DESC")
    
    @staticmethod
    def set_p4_latest_submitter(latest_submitter: str):
        env.set({"P4_LATEST_SUBMITTER": latest_submitter})

    @staticmethod
    def get_p4_latest_submitter():
        return env.get("P4_LATEST_SUBMITTER")
    @staticmethod
    def get_gen_dts_log_url():
        return env.get("GEN_DTS_LOG_URL")

    @staticmethod
    def set_gen_dts_log_url(gen_dts_log_url: str):
        env.set({"GEN_DTS_LOG_URL": gen_dts_log_url})
    @staticmethod
    def get_ts_to_js_log_url():
        return env.get("ts_to_js_log_url")

    @staticmethod
    def set_ts_to_js_log_url(ts_to_js_log_url: str):
        env.set({"ts_to_js_log_url": ts_to_js_log_url})
    @staticmethod
    def get_is_new_branch():
        return env.get("is_new_branch")

    @staticmethod
    def set_is_new_branch(new_branch: str):
        env.set({"is_new_branch": new_branch})

class JenkinsEnvMgr:
    """
    Jenkins环境变量管理器
    """

    @staticmethod
    def get_build_type():
        build_type = env.get("BUILD_TYPE")
        return build_type.split(",")

    @staticmethod
    def get_clean_cache():
        return common.str2bool(env.get("CLEAN_CACHE", "false"))

    @staticmethod
    def get_engine_git_tag():
        return env.get("ENGINE_GIT_TAG")
    
    @staticmethod
    def get_engine_plugin_tag():
        return env.get("ENGINE_PLUGIN_TAG")

    @staticmethod
    def get_engine_target_branch():
        return env.get("ENGINE_TARGET_BRANCH")

    @staticmethod
    def get_p4_force():
        return common.str2bool(env.get("P4_FORCE", "false"))

    @staticmethod
    def get_p4_branch():
        return env.get("P4_BRANCH")
    
    @staticmethod
    def get_deploy_env():
        return env.get("DEPLOY_ENVIRONMENT")

    @staticmethod
    def get_client_git_branch():
        return env.get("M2_GIT_BRANCH")

class GlobalEnvMgr:

    @staticmethod
    def get_last_fail_plugin_changelist():
        return env.get_global("LAST_FAIL_PLUGIN_CHANGELIST")

    @staticmethod
    def set_last_fail_plugin_changelist(last_changelist: str):
        env.set_global({"LAST_FAIL_PLUGIN_CHANGELIST": last_changelist})

    @staticmethod
    def get_last_success_engine_tag():
        return env.get_global("LAST_SUCCESS_ENGINE_TAG")

    @staticmethod
    def set_last_success_engine_tag(last_tag: str):
        env.set_global({"LAST_SUCCESS_ENGINE_TAG": last_tag})
    @staticmethod
    def get_last_success_plugin_tag():
        return env.get_global("LAST_SUCCESS_PLUGIN_TAG")

    @staticmethod
    def set_last_success_plugin_tag(last_tag: str):
        env.set_global({"LAST_SUCCESS_PLUGIN_TAG": last_tag})
    
    @staticmethod
    def get_last_success_engine_changelist():
        return env.get_global("LAST_SUCCESS_ENGINE_CHANGELIST") or 0

    @staticmethod
    def set_last_success_engine_changelist(last_changelist: str):
        env.set_global({"LAST_SUCCESS_ENGINE_CHANGELIST": last_changelist})
    
    @staticmethod
    def get_last_success_plugin_changelist():
        return env.get_global("LAST_SUCCESS_PLUGIN_CHANGELIST") or 0

    @staticmethod
    def set_last_success_plugin_changelist(last_changelist: str):
        env.set_global({"LAST_SUCCESS_PLUGIN_CHANGELIST": last_changelist}) 

    @staticmethod
    def set_last_publish_fail(last_fail_tag: str):
        env.set_global({"LAST_FAIL_TAG": last_fail_tag}) 

    @staticmethod
    def get_last_publish_fail():
        return env.get_global("LAST_FAIL_TAG") 

env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
global_env_mgr = GlobalEnvMgr()
