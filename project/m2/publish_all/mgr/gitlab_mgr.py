# coding=utf-8
import platform
from frame import *
from project.m2 import config
from project.m2.publish_all.mgr.env_mgr import env_mgr, jenkins_env_mgr, global_env_mgr

class TsGitMgr:
    def __init__(self):
        self._workdir = env.pipeline.workspace()
        self._git_branch = jenkins_env_mgr.get_client_git_branch()
        self._m2_git_mgr = GitMgr(workdir=self._workdir, project_name="M2_Client")
        self._client_git_url = "https://gitlab.h3d.com.cn/mug_m2/m2_client.git"
        # 引擎 git 相关参数
        self._engine_git_mgr = GitMgr(workdir=self._workdir, project_name="UE_5.1_T3")
        self._engine_target_branch = jenkins_env_mgr.get_engine_target_branch()
        # engine tag: 发版时取页面输入的engine_git_tag, 页面没有输入取上次成功的tag, 上次成功的tag 会在上次发版提交ue_engine成功后更新, 取不到则使用 5.3master
        self._engine_git_tag = jenkins_env_mgr.get_engine_git_tag() if jenkins_env_mgr.get_engine_git_tag() else global_env_mgr.get_last_success_engine_tag()
        self._engine_git_branch = jenkins_env_mgr.get_engine_git_branch() 
        self._engine_git_url = "https://gitlab.h3d.com.cn/team3/unreal-engine/UE_5.1_T3.git"

    def update_client(self):
        if not Path(os.path.join(self._workdir, "M2_Client", ".git")).exists():
            self._m2_git_mgr.clone_with_oauth2(url=self._client_git_url, branch=self._git_branch, oauth2="yZtE613Z2fPMvurzJF3r")
        else:
            self._m2_git_mgr.reset(commit_id="HEAD")
            try:
                self._m2_git_mgr.pull(branch=self._git_branch)
            except PyframeException:
                ret, output = cmd.run_shell(
                    cmds=[f"git clean TS_workspace/TsProj -xdf && git reset --hard origin/{self._git_branch} && git pull origin {self._git_branch}"],
                    workdir=os.path.join(self._workdir, "M2_Client"),
                )
                if ret != 0:
                    raise PyframeException(f"更新git代码失败: {output}")

        commit_id = self._m2_git_mgr.get_local_latest_commit_id(short=True)
        env_mgr.set_client_git_latest_commit_id(latest_commit_id=commit_id)
        commit_msg = self._m2_git_mgr.get_local_latest_commit_msg()
        env_mgr.set_client_git_latest_commit_msg(latest_commit_msg=commit_msg)
        committer_name = self._m2_git_mgr.get_local_latest_committer_name()
        env_mgr.set_client_git_latest_committer_name(latest_committer_name=committer_name)
        committer_email = self._m2_git_mgr.get_local_latest_committer_email()
        env_mgr.set_client_git_latest_committer_email(latest_committer_email=committer_email)

        commit_time = self._m2_git_mgr.get_local_latest_commit_time()
        env_mgr.set_client_git_latest_commit_time(latest_commit_time=commit_time)
    
    def update_engine(self):
        """
        更新引擎代码
        """
        if not self._engine_git_mgr.exist():
            self._engine_git_mgr.clone(url=self._engine_git_url, branch=self._engine_git_branch)

        self._engine_git_mgr.fetch(all=True)
        try:
            self._engine_git_mgr.checkout(self._engine_git_tag)
        except Exception as e:
            log.warn(f"切换分支报错: {e}")
            self.download_cache()
        latest_commit_msg = self._engine_git_mgr.get_local_latest_commit_msg()
        env_mgr.set_engine_latest_commit_msg(latest_commit_msg)
        committer_email = self._engine_git_mgr.get_local_latest_committer_email()
        env_mgr.set_engine_latest_committer_email(committer_email)
        # env_mgr.set_git_tag_content(content=self._engine_git_mgr.get_content_by_tag(self._engine_git_tag).strip("'"))
        env_mgr.set_git_tag_content(content=self._engine_git_mgr.get_local_latest_commit_msg().strip("'"))

    def download_cache(self):
        """
        下载缓存
        """
        cache_params = f"--cache={self.get_cache_dir()} --force"
        commands = [f"Setup.bat {cache_params}" if platform.system() == "Windows" else f"./Setup.sh {cache_params}"]
        ret = cmd.run_shell(
            cmds=commands,
            workdir=os.path.join(self._workdir, "UE_5.1_T3"),
        )
        if ret[0] != 0:
            raise PyframeException(f"设置ue5.1缓存失败, 错误码为{ret[0]}")
    
    def get_cache_dir(self):
        return os.path.join(self._workdir, "unrealengine5_1_0_cache")
    
    def merge_engine(self):
        """
        合并引擎代码
        """
        if not jenkins_env_mgr.get_engine_git_tag():
            log.info("没有指定引擎tag，不进行合并")
            return
        gitlab_mgr = GitlabMgr(**config.UE_ENGINE)
        title = f"[ci create] Merge {self._engine_git_tag} to master"
        log.info(f"merge title: {title}")

        # 先通过tag创建分支
        turn_result = gitlab_mgr.turn_tag_to_branch(tag=self._engine_git_tag)
        log.info(f"turn_result: {turn_result}")

        # 创建成功，合并到master
        if turn_result:
            # TODO 测试
            _, merge_result = gitlab_mgr.merge(
                source_branch=self._engine_git_tag,
                target_branch=self._engine_target_branch,
                title=title,
                merge_when_pipeline_succeeds=False,
            )
            log.info(f"merge_result: {merge_result}")

git_mgr = TsGitMgr()
