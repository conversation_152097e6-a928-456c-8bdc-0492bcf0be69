from project.m2.publish_all.mgr.env_mgr import  EnvMgr, JenkinsEnvMgr, GlobalEnvMgr
from frame import *

class Msg:
    def __init__(self):
        self.engine_git_tag = JenkinsEnvMgr.get_engine_git_tag()
        self.git_tag_content = EnvMgr.get_git_tag_content()
        self.plugin_tag = JenkinsEnvMgr.get_engine_plugin_tag() if JenkinsEnvMgr.get_engine_plugin_tag() else GlobalEnvMgr.get_last_success_plugin_tag()
        self.plugin_changelist = EnvMgr.get_ue_plugin_changelist() if EnvMgr.get_ue_plugin_changelist() else GlobalEnvMgr.get_last_success_plugin_changelist()
        # self.engine_build_type = JenkinsEnvMgr.get_build_type()
        self.client_git_branch = JenkinsEnvMgr.get_client_git_branch()
        self.client_git_committer = EnvMgr.get_client_git_latest_committer_name()
        self.client_build_log_url = EnvMgr.get_build_log_url()
        self.client_gen_dts_log_url = EnvMgr.get_gen_dts_log_url()
        self.client_p4_submitter = EnvMgr.get_p4_latest_submitter()
        self.client_commit_id = EnvMgr.get_client_git_latest_commit_id()
        self.client_git_msg = EnvMgr.get_client_git_latest_commit_msg()
        self.client_git_commit_time = EnvMgr.get_client_git_latest_commit_time()
        self.client_p4_changelist = EnvMgr.get_p4_latest_changelist()
        self.client_p4_time = EnvMgr.get_p4_latest_time()
        self.client_p4_desc = EnvMgr.get_p4_latest_desc()
        self.p4_force = JenkinsEnvMgr.get_p4_force()
        self.p4_branch = JenkinsEnvMgr.get_p4_branch()
        self.client_ts_to_js_log_url = EnvMgr.get_ts_to_js_log_url()

    def get_engine_message(self) -> str:
        """
        组织通知消息
        """
        message = ""
        message += f"**engine tag**: {self.engine_git_tag}\n" if self.engine_git_tag else ""
        message += f"**engine tag msg**: {self.git_tag_content}\n" if self.git_tag_content else ""
        message += f"**plugin tag**: {self.plugin_tag}\n" if self.plugin_tag else ""
        message += f"**plugin changelist**: {self.plugin_changelist}\n" if self.plugin_changelist else ""
        # message += f"**编译类型**: {self.engine_build_type}\n" if self.engine_build_type else ""
        message += f"**客户端分支**: {self.client_git_branch}@{self.client_commit_id}\n" if self.client_git_branch else ""
        message += f"**客户端Git提交人**: {self.client_git_committer} {self.client_git_commit_time}\n" if self.client_git_committer else ""
        message += f"**客户端Git提交信息**: {self.client_git_msg}\n" if self.client_git_msg else ""
        message += f"**P4**: {self.p4_branch}@{self.client_p4_changelist}\n" if self.p4_force else ""
        message += f"**强更P4**: {self.p4_force}\n" if self.p4_force else ""
        message += f"**P4提交人**: {self.client_p4_submitter} {self.client_p4_time}\n" if self.client_p4_submitter else ""
        message += f"**P4提交信息**: {self.client_p4_desc}\n" if self.client_p4_desc else ""
        message += f"**UE5编译日志**: {self.client_build_log_url}\n" if self.client_build_log_url else ""
        message += f"**C++导出dts日志**:  [{common.get_basename(self.client_gen_dts_log_url)}]({self.client_gen_dts_log_url})\n" if self.client_gen_dts_log_url else ""
        message += f"**Ts转Js日志**: [{common.get_basename(self.client_ts_to_js_log_url)}]({self.client_ts_to_js_log_url})\n" if self.client_ts_to_js_log_url else ""
        return message

    @staticmethod
    def get_user_list() -> list:
        """ """
        return ["<EMAIL>","<EMAIL>"]
