# coding=utf-8
import re
from datetime import datetime
import subprocess
import threading, pathlib
from frame import *
from project.m2 import config
from project.m2.publish_all.mgr.env_mgr import jenkins_env_mgr, env_mgr, global_env_mgr


class P4MgrBase:
    def __init__(self):
        self.workspace = env.pipeline.workspace()
        self.p4_port = config.P4_WZ_TEST_CONFIG.get("host", "")
        self.p4_user = config.P4_WZ_TEST_CONFIG.get("username", "")
        self.p4_password = config.P4_WZ_TEST_CONFIG.get("password", "")


class ClientP4Mgr(P4MgrBase):
    def __init__(self):
        super().__init__()
        self.client = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_client_{common.get_host_ip()}")
        self.p4_client = P4Client(self.p4_port, self.p4_user, self.p4_password, self.client)
        self.p4_env = "testdepot"
        # self.p4_env = "H3D_M2"
        views = [
            f"//{self.p4_env}/M2_Product/trunk/... //{self.client}/...",
            f"//{self.p4_env}/M2_Product/MVComposerProject/Plugins/... //{self.client}/M2_Client/Plugins/...",
            ]
        self.p4_client.set_charset(charset=self.p4_client.Charset.UTF8)
        self.p4_client.set_view(views)
        self.p4_client.set_options(allwrite=True)
        self.p4_client.set_root(self.workspace)

    def sync_all(self):
        """
        同步p4
        除了 ue_plugin 目录需要拉取上次成功的提交, 其余拉取最新
        """
        branch = jenkins_env_mgr.get_p4_branch()
        force = jenkins_env_mgr.get_p4_force()
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/binaries/...", force=force)
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/client/...", force=force)
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/client_wwise/...", force=force)
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/common/...", force=force)
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/server/...", force=force)           
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/ue_engine/...", force=force)
        if not jenkins_env_mgr.get_engine_git_tag() and not jenkins_env_mgr.get_engine_plugin_tag():
            # 如果非引擎发版 拉取上次成功     
            # last_success_engine = global_env_mgr.get_last_success_engine_changelist()
            # self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/ue_engine/...@{last_success_engine}", force=force)
            last_success_plugin = global_env_mgr.get_last_success_plugin_changelist()
            if last_success_plugin == 0:
                self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/MVComposerProject/Plugins/...", force=force)
                plugin_changes = self.p4_client.get_latest_changes(path=f"//{self.p4_env}/M2_Product/MVComposerProject/Plugins/...#have")
                if len(plugin_changes) > 0:
                    env_mgr.set_plugin_tag(plugin_tag="v0.0.0")
                    env_mgr.set_ue_plugin_changelist(plugin_changelist=plugin_changes.get("change"))
            else:
                self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/MVComposerProject/Plugins/...",changelist=f"{last_success_plugin}", force=force)
                env_mgr.set_plugin_tag(plugin_tag=global_env_mgr.get_last_success_plugin_tag())
                env_mgr.set_ue_plugin_changelist(plugin_changelist=global_env_mgr.get_last_success_plugin_changelist())
        else:
            # 如果是引擎发版 拉取最后一次tag
            last_tag_changelist = self.__get_latest_plugin_tag_changelist(global_env_mgr.get_last_success_plugin_changelist())
            self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/MVComposerProject/Plugins/...",changelist=f"{last_tag_changelist}", force=force)
            env_mgr.set_plugin_tag(plugin_tag=jenkins_env_mgr.get_engine_plugin_tag())
            env_mgr.set_ue_plugin_changelist(plugin_changelist=last_tag_changelist)
            env_mgr.set_engine_tag(engine_tag=jenkins_env_mgr.get_engine_git_tag())
        changes = self.p4_client.get_latest_changes(path=f"//{self.p4_env}/M2_Product/trunk/...#have")
        env_mgr.set_p4_latest_changelist(latest_changelist=changes.get("change"))
        env_mgr.set_p4_latest_desc(latest_desc=changes.get("desc"))
        env_mgr.set_p4_latest_submitter(latest_submitter=changes.get("user"))
        env_mgr.set_p4_latest_time(latest_time=datetime.fromtimestamp(int(changes.get("time"))).strftime("%Y-%m-%d %H:%M:%S"))

    def __get_latest_plugin_tag_changelist(self, start_change = 0):
        all_changes = self.p4_client.get_changes(path=f"//{self.p4_env}/M2_Product/MVComposerProject/Plugins/...@{start_change},now", max=10000)
        for change in all_changes:
            description = change["desc"]
            tag_group = re.search(re.compile(r"tag:(v\d+\.\d+\.\d+)", re.S), description)
            if tag_group:
                tag = tag_group.group(1)
                if tag == jenkins_env_mgr.get_engine_plugin_tag():
                    return change["change"]
        raise PyframeException(f"没有找到最新的插件tag, 请检查输入tag:{jenkins_env_mgr.get_engine_plugin_tag()} 是否正确")

    def copy(self):
        """
        将编译好的客户端二进制拷贝到P4目录下
        """
        src_base_path = os.path.join(self.workspace, "client")  
        dst_base_path = os.path.join(self.workspace, "binaries")
        if not os.path.exists(os.path.join(dst_base_path, "Content", "Javascript", "Generated")):
            os.makedirs(os.path.join(dst_base_path, "Content", "Javascript", "Generated"))
        path_mgr.copy(
            src=os.path.join(src_base_path, "Content", "Javascript", "Generated"),
            dst=os.path.join(dst_base_path, "Content", "Javascript", "Generated"),
        )
        if not os.path.exists(os.path.join(dst_base_path, "Binaries")):
            os.makedirs(os.path.join(dst_base_path, "Binaries"))
        path_mgr.copy(
            src=os.path.join(src_base_path, "Binaries"),
            dst=os.path.join(dst_base_path, "Binaries"),
        )
        if not os.path.exists(os.path.join(dst_base_path, "Content", "Javascript", "Generated", "xls_config")):
            os.makedirs(os.path.join(dst_base_path, "Content", "Javascript", "Generated", "xls_config"))
        path_mgr.copy(
            src=os.path.join(src_base_path, "Content", "Javascript", "Generated", "xls_config"),
            dst=os.path.join(dst_base_path, "Content", "Javascript", "Generated", "xls_config"),
        )
        if not os.path.exists(os.path.join(dst_base_path, "Plugins")):
            os.makedirs(os.path.join(dst_base_path, "Plugins"))
        # 拷贝Plugins，忽略插件目录下的Intermediate和Source目录
        path_mgr.copy(
            src=os.path.join(src_base_path, "Plugins"),
            dst=os.path.join(dst_base_path, "Plugins"),
        )
        plugins_path = Path(os.path.join(dst_base_path, "Plugins")).glob("*")
        for plugin in plugins_path:
            if not plugin.is_dir():
                continue
            for p in plugin.iterdir():
                if p.is_dir():
                    if p.name in ["Intermediate", "Source"]:
                        path_mgr.rm(p.absolute())

    def copy_engine(self):
        """
        将编译 engine 产物复制到 ue_engine 目录
        """
        if not jenkins_env_mgr.get_engine_plugin_tag() and not jenkins_env_mgr.get_engine_git_tag(): # 一个tag都没输入 ，直接跳过 认为不是发版
            return
        # 先处理pdb文件 并发处理, 考虑可以先把所有pdb mv 到某个临时目录下, 再并发上传和删除
        src_base_path = os.path.join(self.workspace, "UE_5.1_T3")        
        dst_base_path = os.path.join(self.workspace, "ue_engine", "Windows")
        self.__move_pdb()
        # upload_thread = threading.Thread(target=self.__start_upload)
        # upload_thread.start()
        path_mgr.rm(dst_base_path)
        os.makedirs(dst_base_path)
        path_mgr.copy(
            src=os.path.join(src_base_path, "Engine"),
            dst=os.path.join(dst_base_path, "Engine"),
        )
        path_mgr.copy(
            src=os.path.join(src_base_path, "FeaturePacks"),
            dst=os.path.join(dst_base_path, "FeaturePacks"),
        )
        path_mgr.copy(
            src=os.path.join(src_base_path, "Samples"),
            dst=os.path.join(dst_base_path, "Samples"),
        )
        path_mgr.copy(
            src=os.path.join(src_base_path, "Templates"),
            dst=os.path.join(dst_base_path, "Templates"),
        )


    def __move_pdb(self):
        ue_engine_dir = os.path.join(self.workspace, "UE_5.1_T3")
        pdb_list = path_mgr.glob(ue_engine_dir, "**/*.pdb")
        # self.pdb_files = []
        dst_path = os.path.join(self.workspace, "pdb_tmp")
        for i in range(0, 10):
            if(path_mgr.exists(dst_path)):
                dst_path = os.path.join(self.workspace, "pdb_tmp_" + str(i))
            else:
                break
        if(path_mgr.exists(dst_path)):
            raise PyframeException(f"超过十次的 pdb 目录没有删除完成, 请等待一会再试, 或者联系流水线同学处理")
        path_mgr.mkdir(dst_path)
        for pdb in pdb_list:
            if path_mgr.exists(os.path.join(dst_path, os.path.basename(pdb))):
                path_mgr.rm(pdb)
                continue
            path_mgr.move(pdb, dst_path)
            # self.pdb_files.append(os.path.join(dst_path, os.path.basename(pdb)))   
        subprocess.Popen(["python", "project/m2/publish_all/mgr/upload_pdb.py", dst_path])     

    def __start_upload(self):
        """
        启动并发上传
        """
        with SMBClient(username=config.SMB.get("username"), password=config.SMB.get("password"), server_ip=config.SMB.get("server_ip")) as c:
            for pdb in self.pdb_files:
                c.upload(pdb, "TAC", f"symbol/m2/{os.path.basename(pdb)}")
        if self.pdb_files:
            path_mgr.rm(os.path.dirname(self.pdb_files[0]))

    def submit_p4(self):
        """
        提交p4
        """
        ## 非引擎发版时处理开始
        if jenkins_env_mgr.get_engine_git_tag()  or jenkins_env_mgr.get_engine_plugin_tag() :
            # 处理 pdb
            self.__move_pdb()
            # 提交引擎
            reconcile_result = self.p4_client.reconcile_with_extra_args(
                f"//{self.p4_env}/M2_Product/{jenkins_env_mgr.get_p4_branch()}/ue_engine/...",
                "-I",
                f"//{self.p4_env}/M2_Product/{jenkins_env_mgr.get_p4_branch()}/ue_engine/Windows/Engine/DerivedDataCache/...",
                add=True,
                edit=True,
                delete=True,
            )
            if reconcile_result:
                commit_msg = env_mgr.get_engine_latest_commit_msg()
                git_tag = jenkins_env_mgr.get_engine_git_tag()
                submit_desc = f"[ci submit]tag:{git_tag},commit_message:{commit_msg}"
                log.info(f"engine submit_desc: {submit_desc}")
                submit_ret = self.p4_client.submit(submit_desc, revert_if_failed=True)
                engine_changelist = submit_ret[0].get("change")
                file_path = pathlib.Path(self.workspace).joinpath("client", "client_updated")
                if not os.path.exists(file_path):
                    file_path.touch()
                with open(file_path, "r+", encoding="utf-8") as f:
                    f.write(f"engine updated to tag: {git_tag}, engine_changelist: {engine_changelist}")  
        ## 引擎发版时处理结束

        # 设置一下提交描述中的 engine 信息
        engine_changes = self.p4_client.get_latest_changes(path=f"//{self.p4_env}/M2_Product/{jenkins_env_mgr.get_p4_branch()}/ue_engine/...#have")
        engine_change_list = engine_changes.get("change")
        tag_group = re.search(re.compile(r"tag:(v\d+\.\d+\.\d+)", re.S), self.p4_client.get_describe_by_changelist(engine_change_list))
        tag = "v0.0.0"
        if tag_group:
            tag = tag_group.group(1)
            env_mgr.set_engine_tag(tag)
        # 在binaries 目录提交后 client 目录有提交，需要提交一下binaries 目录 (部署工具使用新的提交号)
        binaries_changes = self.p4_client.get_latest_changes(path=f"//{self.p4_env}/M2_Product/{jenkins_env_mgr.get_p4_branch()}/binaries/...#have")
        if len(binaries_changes) > 0:
            binaries_change = binaries_changes.get("change")
            client_changes = self.p4_client.get_changes(path=f"//{self.p4_env}/M2_Product/{jenkins_env_mgr.get_p4_branch()}/client/...@{int(binaries_change)},now", max=100)
            log.info(f"client_changes: {client_changes}" )
            if client_changes:
                file_path = pathlib.Path(self.workspace).joinpath("client", "client_updated")
                if not os.path.exists(file_path):
                    file_path.touch()
                with open(file_path, "r+", encoding="utf-8") as f:
                    client_changelist = client_changes[-1].get("change")
                    f.write(f"client_changelist:{client_changelist},git_last_commit:{env_mgr.get_client_git_latest_commit_id()},")
        # 再提交客户端
        reconcile_result = self.p4_client.reconcile_with_extra_args(
            f"//{self.p4_env}/M2_Product/{jenkins_env_mgr.get_p4_branch()}/binaries/...", delete=True, add=True, edit=True
        )
        log.info(f"reconcile_result: {reconcile_result}")
        if reconcile_result:
            branch_changelist = env_mgr.get_p4_latest_changelist()
            git_commit = env_mgr.get_client_git_latest_commit_id()
            git_branch = jenkins_env_mgr.get_client_git_branch()
            plugin_changelist = env_mgr.get_ue_plugin_changelist()
            submit_desc = f"[ci submit]git_commit:{git_commit},git_branch:{git_branch},engine_tag:{tag},engine_change_list:{engine_change_list},plugin_changelist:{plugin_changelist},branch_change_list:{branch_changelist}"
            log.info(f"client submit_desc: {submit_desc}")
            self.p4_client.submit(submit_desc, revert_if_failed=True)
        # 记录一下成功的tag 和 plugin changelist
        global_env_mgr.set_last_success_plugin_changelist(env_mgr.get_ue_plugin_changelist())
        global_env_mgr.set_last_success_plugin_tag(env_mgr.get_plugin_tag())
        global_env_mgr.set_last_success_engine_tag(env_mgr.get_engine_tag())    

client_p4_mgr = ClientP4Mgr()
