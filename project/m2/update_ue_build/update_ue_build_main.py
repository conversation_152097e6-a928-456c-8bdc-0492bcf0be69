from frame import *
from project.m2.update_ue_build.mgr.build_mgr import build_mgr
from project.m2.update_ue_build.mgr.gitlab_mgr import git_mgr
from project.m2.update_ue_build.mgr.link_mgr import link_mgr
from project.m2.update_ue_build.mgr.msg_mgr import Msg
from project.m2.update_ue_build.mgr.p4_mgr import client_p4_mgr
import threading

@advance.stage(stage="更新引擎代码")
def update_engine(**kwargs):
    git_mgr.update_engine()


@advance.stage(stage="缓存引擎依赖")
def download_engine_cache(**kwargs):
    cache_dir = git_mgr.get_cache_dir()
    if not path_mgr.exists(cache_dir):
        git_mgr.download_cache()
    else:
        log.info(f"{cache_dir} 已存在，跳过下载引擎依赖")


@advance.stage(stage="更新客户端工程")
def update_client(**kwargs):
    git_mgr.update_client()
    # log.info("pass")

@advance.stage(stage="更新P4相关")
def update_p4(**kwargs):
    client_p4_mgr.sync_all()

@advance.stage(stage="创建软链接")
def create_symbolic_link(**kwargs):
    link_mgr.link_git_to_p4()

# 切换引擎
@advance.stage(stage="切换引擎")
def switch_engine(**kwargs):
    build_mgr.switch_engine()

@advance.stage(stage="编译源码")
def ue51_build(**kwargs):
    build_mgr.build_ue5_cpp()


@advance.stage(stage="导出ts")
def export_ts(**kwargs):
    build_mgr.gen_dts()


@advance.stage(stage="导出js")
def export_js(**kwargs):
    build_mgr.compile_ts()


@advance.stage(stage="语言表检查")
def check_xls_config(**kwargs):
    build_mgr.xls_config()

@advance.stage(stage="拷贝结果")
def copy_result(**kwargs):
    # threads = []
    # thread = threading.Thread(target=client_p4_mgr.copy)
    # threads.append(thread)
    # thread.start()
    # thread = threading.Thread(target=client_p4_mgr.copy_engine)
    # threads.append(thread)
    # thread.start()
    # for t in threads:
    #     t.join()
    client_p4_mgr.copy()
    # client_p4_mgr.copy_engine() # 先注释了, 编译出来观察观察

@advance.stage(stage="提交p4")
def submit_p4(**kwargs):
    client_p4_mgr.submit_p4()
    # log.info("pass") # 自测时跳过


@advance.stage(stage="合并引擎代码")
def merge_engine(**kwargs):
    git_mgr.merge_engine()
    # git_mgr.upload_engine_plugins() # 先注释了
    # log.info("pass") # 自测时跳过


msg = Msg()
users = msg.get_user_list()
content = msg.get_engine_message()


def on_always(**kwargs):
    build_mgr.upload_build_log()
    build_mgr.upload_gen_dts_log()
    build_mgr.upload_ts_to_js_log()

def send_branch_success(msg_content):
    log.info(f"通知消息: {msg_content} {users}")
    wechat.send_unicast_post_success(content=msg_content, user_list=users)
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=998e2c2c-953e-4e1a-b9a7-53636701516c",
        content=msg_content,
    )


def on_success(**kwargs):
    log.info(f"通知消息: {content} {users}")
    wechat.send_unicast_post_success(content=content, user_list=users)
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=998e2c2c-953e-4e1a-b9a7-53636701516c",
        content=content,
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(content=content, user_list=users)
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=998e2c2c-953e-4e1a-b9a7-53636701516c",
        content=content,
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_unstable(**kwargs):
    wechat.send_unicast_post_unstable(content=content, user_list=users)
    wechat.send_multicast_post_unstable(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=998e2c2c-953e-4e1a-b9a7-53636701516c",
        content=content,
    )
    advance.insert_pipeline_history_on_unstable()
