node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "*************"
            customWorkspace "/data/workspace/auto_create_mr"
        }
    }
    options {
        disableConcurrentBuilds()
    }
    parameters {
        choice(
            name: 'PROJECT',
            choices: [
                'timer_service',
                'pyframe-pipeline',
                'pymug',
                'x51_workbench_client',
                'x51_workbench_server',
                'x51_workbench_front',
                'git_test',
            ],
            description: '项目名称',
        )
    }
    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        sh(
                            script: """
                                python3 -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python3 -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("创建MR") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        sh(script: "python3 pipeline.py auto_create_mr --job=create_mr")
                    }
                }
            }
        }
        stage("合并稳定分支代码到其它分支") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        sh(script: "python3 pipeline.py auto_create_mr --job=merge_master_to_others")
                    }
                }
            }
        }
    }
    post {
        success {
            dir("${env.GIT_NAME}") {
                script {
                    sh(script: "python3 pipeline.py auto_create_mr --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}") {
                script {
                    sh(script: "python3 pipeline.py auto_create_mr --job=on_failure")
                }
            }
        }
    }
}