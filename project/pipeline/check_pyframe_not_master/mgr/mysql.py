# coding=utf-8

from frame import *


class Mysql:
    def __init__(self) -> None:
        self.mysql_mgr = MysqlClient(host="*************", port="6603", username="root", password="Horizon#d")

    def _get_all_databases(self) -> list:
        """
        显示所有数据库
        Returns:
            list: 数据库列表
        """
        ret = self.mysql_mgr.select_all("show databases")
        databases = []
        for row in ret:
            db = row["Database"]
            if db not in ["information_schema", "performance_schema", "sys", "mysql"]:
                databases.append(db)
        return databases

    def _get_pipeline_info(self, db: str, table: str) -> dict:
        """
        检查pyframe分支
        Args:
            db (str): 数据库
            table (str): 表
        Returns:
            dict: 流水线信息
        """
        select_sql = f"""
        SELECT pyframe_branch, pipeline_name, function_name, build_url
        FROM {db}.`{table}`
        WHERE create_time > DATE_SUB(NOW(), INTERVAL 2 DAY)
        ORDER BY create_time DESC
        LIMIT 1
        """
        ret = self.mysql_mgr.select_one(select_sql)
        return ret

    def not_master_pipelines(self) -> list:
        """
        未使用主支的流水线
        Returns:
            list: 流水线列表
        """
        black_list = ["hot_detail"]
        pipelines = []
        dbs = self._get_all_databases()
        for db in dbs:
            for table in self.mysql_mgr.get_all_tables(database=db):
                if table in black_list:
                    log.warning(f"{db}.{table} 在黑名单中， 跳过检查")
                    continue
                info = self._get_pipeline_info(db, table)
                if not info:
                    log.info(f"{db}.{table} 无数据")
                    continue
                if info["pyframe_branch"] != "master" and info["pyframe_branch"] != "" and info["pyframe_branch"] != "cuizemin":
                    pipelines.append(info)
        return pipelines


mysql = Mysql()
