# coding=utf-8

import datetime

import chinese_calendar

from frame import *
from project.devpro.cron_notify.mgr.env_mgr import env_mgr


# python pipeline.py cron_notify --job=tac_ta_pm_weekly_report
def tac_ta_pm_weekly_report(**kwargs):
    """
    TA组每周五jira提醒
    发送时间: 每周五，下午2点
    """
    # 判断是否是工作日
    date = datetime.datetime.now()
    if date.hour == 14 and chinese_calendar.is_workday(date=date):
        wechat.send_multicast_msg(
            webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e6c23bf2-e653-4a53-8903-72a55c26d622",
            content="大家可以更新jira周度数据了，没有请假情况同学每日的工作登记时间是8小时哈~3点拉数据🌹\nTA组链接：https://jira.h3d.com.cn/secure/RapidBoard.jspa?rapidView=184&projectKey=TETA&view=detail&selectedIssue=TETA-644",
            markdown=False,
            mentioned_list=["@all"],
        )
        env_mgr.set_msg(msg="TA组jira提醒")
    else:
        log.info(f"{date} is not working day")


# python pipeline.py cron_notify --job=tac_x5m_support_pm_weekly_report
def tac_x5m_support_pm_weekly_report(**kwargs):
    """
    手游支持组每周五jira提醒
    发送时间: 每周五，下午2点
    """
    # 判断是否是工作日
    date = datetime.datetime.now()
    if date.hour == 14 and chinese_calendar.is_workday(date=date):
        wechat.send_multicast_msg(
            webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e58e4143-2420-43b8-8206-17d12f3a5c86",
            content="大家可以更新jira周度数据了，没有请假情况同学每日的工作登记时间是8小时哈~3点拉数据🌹\n炫舞手游支持组链接：https://jira.h3d.com.cn/secure/RapidBoard.jspa?rapidView=200&projectKey=TACTEAM4&view=detail&selectedIssue=TACTEAM4-89",
            markdown=False,
            mentioned_list=["@all"],
        )
        env_mgr.set_msg(msg="手游支持组jira提醒")
    else:
        log.info(f"{date} is not working day")


def __get_msg():
    return env_mgr.get_msg()


def on_success(**kwargs):
    wechat.send_unicast_post_success(content=__get_msg())
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(content=__get_msg())
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(content=__get_msg())
    advance.insert_pipeline_history_on_canceled()
