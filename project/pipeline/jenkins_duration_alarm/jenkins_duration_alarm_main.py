from frame import *
from project.pipeline.jenkins_duration_alarm.mgr.framw_jenkins_mgr import framw_jenkins_mgr
from project.pipeline.jenkins_duration_alarm.mgr.m2_jenkins_mgr import m2_jenkins_mgr
from project.pipeline.jenkins_duration_alarm.mgr.team1_jenkins_mgr import team1_jenkins_mgr
from project.pipeline.jenkins_duration_alarm.mgr.team_jenkins_mgr import team_jenkins_mgr
from project.pipeline.jenkins_duration_alarm.mgr.testdev_jenkins_mgr import testdev_jenkins_mgr
from project.pipeline.jenkins_duration_alarm.mgr.x5m_jenkins_mgr import x5m_jenkins_mgr


# python pipeline.py jenkins_duration_alarm --job=alarm_testdev
@advance.stage(stage="测开jenkins超时监控")
def alarm_testdev(**kwargs: dict):
    testdev_jenkins_mgr.alarm()


# python pipeline.py jenkins_duration_alarm --job=alarm_x5m
@advance.stage(stage="炫舞手游jenkins超时监控")
def alarm_x5m(**kwargs: dict):
    x5m_jenkins_mgr.alarm()


@advance.stage(stage="team1 jenkins超时监控")
def alarm_team1(**kwargs: dict):
    team1_jenkins_mgr.alarm()


@advance.stage(stage="team234 jenkins超时监控")
def alarm_team(**kwargs: dict):
    team_jenkins_mgr.alarm()


@advance.stage(stage="框架jenkins超时监控")
def alarm_framw(**kwargs: dict):
    framw_jenkins_mgr.alarm()


@advance.stage(stage="m2 jenkins超时监控")
def alarm_m2(**kwargs: dict):
    m2_jenkins_mgr.alarm()


def on_success(**kwargs: dict):
    # wechat.send_unicast_post_success()
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs: dict):
    wechat.send_unicast_post_failure()
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs: dict):
    wechat.send_unicast_post_canceled()
    advance.insert_pipeline_history_on_canceled()
