# coding=utf-8
import datetime
import json

import requests

from frame import common, jenkins_mgr, log, wechat
from project.devpro import config
from project.testdev.x5m_whitebox.mgr.report_mgr import ReportMgr


class TestdevJekninsMgr:
    def __init__(self):
        pass

    def __send_request(self, duration: int, url: str, username: str, password: str):
        response = requests.get(url=common.join_url(url, "api/json"), auth=(username, password), stream=True)
        d = json.loads(response.text)
        run_id = None
        for i in range(len(d["actions"])):
            if "parameters" in d["actions"][i]:
                for n in range(len(d["actions"][i]["parameters"])):
                    if d["actions"][i]["parameters"][n]["name"] == "run_id":
                        log.info(d["actions"][i]["parameters"][n]["name"])
                        log.info(d["actions"][i]["parameters"][n]["value"])
                        run_id = d["actions"][i]["parameters"][n]["value"]
                        log.info(f"run_id: {run_id}")
        duration = str(datetime.timedelta(seconds=duration))
        h = duration.split(":")[0]
        m = duration.split(":")[1]
        if h == "0":
            msg = f"执行耗时已经超过{m}分钟"
        else:
            msg = f"执行耗时已经超过{h}小时{m}分钟"
        if run_id:
            report_mgr = ReportMgr()
            report_mgr.init_request_by_param(runId=run_id, jenkinsState="timeout", errors=msg)
            report_mgr.send_request()

    def __send_wechat_msg(self, duration: int, full_display_name: str, build_url: str, node_name: str, node_url: str):
        from project.testdev.x5m_whitebox import config

        duration = str(datetime.timedelta(seconds=duration))
        h = duration.split(":")[0]
        m = duration.split(":")[1]
        msg = "## 测开jenkins\n"
        if h == "0":
            msg += f"> [{full_display_name}]({build_url}) 已执行{m}分钟, [{node_name}]({node_url})\n"
        else:
            msg += f"> [{full_display_name}]({build_url}) 已执行{h}小时{m}分钟, [{node_name}]({node_url})\n"
        wechat.send_unicast_post_failure(user_list=config.TESTDEV_X5M_DEVELOPER, content=msg, to_admin=True)

    def alarm(self):
        username = config.CONFIG_JENKINS_TESTDEV.get("username")
        password = config.CONFIG_JENKINS_TESTDEV.get("password")
        jenkins_mgr.connect(
            url=config.CONFIG_JENKINS_TESTDEV.get("url"),
            username=username,
            password=password,
        )
        timeout_builds = jenkins_mgr.get_timeout_builds(timeout=3600)
        if timeout_builds:
            for build in timeout_builds:
                full_display_name = build["full_display_name"]
                duration = build["duration"]
                build_url = build["build_url"]
                node_name = build["node_name"]
                node_url = build["node_url"]
                log.debug(f"full_display_name: {full_display_name}")
                log.debug(f"duration: {duration}")
                log.debug(f"build_url: {build_url}")
                log.debug(f"node_name: {node_name}")
                log.debug(f"node_url: {node_url}")
                if "whitebox_probability_check" not in build_url:
                    self.__send_wechat_msg(
                        duration=duration, full_display_name=full_display_name, build_url=build_url, node_name=node_name, node_url=node_url
                    )
                    self.__send_request(duration=duration, url=build_url, username=username, password=password)
                else:
                    if duration > 7200:
                        self.__send_wechat_msg(
                            duration=duration, full_display_name=full_display_name, build_url=build_url, node_name=node_name, node_url=node_url
                        )
                        self.__send_request(duration=duration, url=build_url, username=username, password=password)


testdev_jenkins_mgr = TestdevJekninsMgr()
