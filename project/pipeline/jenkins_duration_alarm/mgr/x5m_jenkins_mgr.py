# coding=utf-8
import datetime
from frame import *
from project.devpro import config


class X5mJekninsMgr:
    def __init__(self):
        pass

    def alarm(self):
        jenkins_mgr.connect(
            url=config.CONFIG_JENKINS_X5M.get("url"),
            username=config.CONFIG_JENKINS_X5M.get("username"),
            password=config.CONFIG_JENKINS_X5M.get("password"),
        )
        timeout_builds = jenkins_mgr.get_timeout_builds(timeout=3600)
        if timeout_builds:
            msg = "## 炫舞手游jenkins\n"
            for build in timeout_builds:
                full_display_name = build["full_display_name"]
                duration = build["duration"]
                build_url = build["build_url"]
                node_name = build["node_name"]
                node_url = build["node_url"]
                duration = str(datetime.timedelta(seconds=duration))
                h = duration.split(":")[0]
                m = duration.split(":")[1]
                if h == "0":
                    msg += f"> [{full_display_name}]({build_url}) 已执行{m}分钟, [{node_name}]({node_url})\n"
                else:
                    msg += f"> [{full_display_name}]({build_url}) 已执行{h}小时{m}分钟, [{node_name}]({node_url})\n"
            wechat.send_unicast_post_failure(user_list=[], content=msg, to_admin=True)


x5m_jenkins_mgr = X5mJekninsMgr()
