# coding=utf-8
import datetime
from typing import Dict

from frame import *
from project.devpro import config


class NewX5mJekninsMgr:
    def __init__(self):
        pass

    def alarm(self, **kwargs: Dict):
        jenkins_mgr.connect(
            url=config.CONFIG_JENKINS_X5M_NEW.get("url"),
            username=config.CONFIG_JENKINS_X5M_NEW.get("username"),
            password=config.CONFIG_JENKINS_X5M_NEW.get("password"),
        )
        exclude_job_names = kwargs.get("exclude_job_names", [])
        one_hour_job_names = kwargs.get("one_hour_job_names", [])
        timeout_builds = jenkins_mgr.get_timeout_builds(timeout=1800)
        if timeout_builds:
            msg = "## (新版)炫舞手游jenkins\n"
            _msg = ""
            for build in timeout_builds:
                full_display_name = build["full_display_name"]
                if full_display_name in exclude_job_names:
                    continue
                duration = build["duration"]
                build_url = build["build_url"]
                node_name = build["node_name"]
                node_url = build["node_url"]
                duration = str(datetime.timedelta(seconds=duration))
                h = duration.split(":")[0]
                m = duration.split(":")[1]
                # 如果名称在one_hour_job_names中，且小时数小于1，则不报警
                if full_display_name.split(" ")[0] in one_hour_job_names and int(h) < 1:
                    continue

                if h == "0":
                    _msg += f"> [{full_display_name}]({build_url}) 已执行{m}分钟, [{node_name}]({node_url})\n"
                else:
                    _msg += f"> [{full_display_name}]({build_url}) 已执行{h}小时{m}分钟, [{node_name}]({node_url})\n"
            if _msg:
                msg += _msg
                wechat.send_unicast_post_failure(user_list=[], content=msg, to_admin=True)


new_x5m_jenkins_mgr = NewX5mJekninsMgr()
