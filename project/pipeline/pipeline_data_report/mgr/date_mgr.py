import calendar
import datetime
import re
import time

import pendulum
from typing import Tuple


class DateMgr:
    @staticmethod
    def get_end_time_of_today() -> str:
        times = datetime.datetime.now()
        format_time = times.strftime("%Y-%m-%d 23:59:59")

        return format_time

    @staticmethod
    def get_start_end_time_of_day(day: str) -> Tuple[str, str]:
        """
        获取一天的开始时间和结束时间, 如“2023-02-04 00:00:00”， “2023-02-04 23:59:59”
        args:
            day:某天的日期,格式为yyyy-MM-dd，如“2023-02-04”
        """
        start_time = ""
        end_time = ""
        date_match = (
            r"(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|"
            + "((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|"
            + "((0[48]|[2468][048]|[3579][26])00))-02-29)$"
        )

        if re.match(date_match, day):
            start_time = day + " 00:00:00"
            end_time = day + " 23:59:59"

        return start_time, end_time

    @staticmethod
    def get_the_number_of_days_in_this_month():
        """
        获取这个月的天数
        """
        day_now = time.localtime()
        _, month_days = calendar.monthrange(day_now.tm_year, day_now.tm_mon)

        return month_days

    @staticmethod
    def get_dates_in_last_days(day: int) -> list:
        """
        获取过去指定天数的日期
        """
        dates_days_ago = []
        now_date = datetime.datetime.now()
        # now_date = datetime.datetime.strptime("2023-03-05 12:00:00", "%Y-%m-%d %H:%M:%S")

        for days_ago in range(0, day):
            date_ago = (now_date - datetime.timedelta(days=days_ago)).strftime("%Y-%m-%d")
            dates_days_ago.append(date_ago)
        dates_days_ago.reverse()

        return dates_days_ago

    @staticmethod
    def get_first_day_in_this_month() -> str:
        """
        获取这个月第一天的日期
        """
        now_date = datetime.datetime.now()
        date_a_month_ago = now_date.strftime("%Y-%m-01 00:00:00")

        return date_a_month_ago

    @staticmethod
    def get_week_date(day: str) -> int:
        """
        获取某天是星期几
        args:
            day:某天的日期，格式为yyyy-MM-dd，如“2023-02-04”
        return:
            week_date:返回数字的星期几，如星期一返回1
        """
        date_match = (
            r"(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|"
            + "((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|"
            + "((0[48]|[2468][048]|[3579][26])00))-02-29)$"
        )

        if re.match(date_match, day):
            week_date = pendulum.parse(day).day_of_week
            return week_date
