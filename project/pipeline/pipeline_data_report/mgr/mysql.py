# coding=utf-8

from frame import *


class Mysql:
    def __init__(self) -> None:
        self.mysql_mgr = MysqlClient(host="*************", port="6603", username="root", password="Horizon#d")

    def show_databases(self) -> list:
        """
        显示所有数据库
        Returns:
            list: 数据库列表
        """
        ret = self.mysql_mgr.select_all("show databases")
        databases = []
        for row in ret:
            db = row["Database"]
            if db not in ["information_schema", "performance_schema", "sys", "mysql"]:
                databases.append(db)
        return databases

    def show_tables(self, db: str) -> list:
        """
        显示所有表
        Args:
            db (str): 数据库
        Returns:
            list: 表列表
        """
        ret = self.mysql_mgr.select_all(f"show tables from {db}")
        tables = []
        for row in ret:
            table = row[f"Tables_in_{db}"]
            tables.append(table)
        return tables

    def sum_build_duration(self, db: str, table: str, start_time: str, end_time: str) -> int:
        """
        统计构建时长
        Args:
            db (str): 数据库
            table (str): 表
        Returns:
            int: 构建时长
        """
        ret = self.mysql_mgr.select_one(
            f"select sum(build_duration) from {db}.`{table}` where create_time>'{start_time}' and create_time<'{end_time}' and build_duration>0 and build_duration<1000000"
        )
        build_duration = ret["sum(build_duration)"]
        return build_duration

    def sum_build_count(self, db: str, table: str, start_time: str, end_time: str) -> int:
        """
        统计构建次数
        Args:
            db (str): 数据库
            table (str): 表
        Returns:
            int: 构建次数
        """
        ret = self.mysql_mgr.select_one(f"select count(*) from {db}.`{table}` where create_time between '{start_time}' and '{end_time}'")
        build_count = ret["count(*)"]
        return build_count

    def sum_success_count(self, db: str, table: str, start_time: str, end_time: str) -> int:
        """
        统计成功数
        Args:
            db (str): 数据库
            table (str): 表
        Returns:
            int: 成功数
        """
        success_ret = self.mysql_mgr.select_one(
            f"select count(status) from {db}.`{table}` where create_time>'{start_time}' and create_time<'{end_time}' and status='success'"
        )
        success_count = success_ret["count(status)"]
        return success_count

    def sum_failure_count(self, db: str, table: str, start_time: str, end_time: str) -> int:
        """
        统计失败数
        Args:
            db (str): 数据库
            table (str): 表
        Returns:
            int: 失败数
        """
        fail_ret = self.mysql_mgr.select_one(
            f"select count(status) from {db}.`{table}` where create_time>'{start_time}' and create_time<'{end_time}' and status='failure'"
        )
        fail_count = fail_ret["count(status)"]
        return fail_count

    def pyframe_pipeline_not_in_master_count(self, db: str, table: str, start_time: str, end_time: str):
        """
        统计pyframe中不是使用主支的流水线
        Args:
            db (str): 数据库
            table (str): 表
        Returns:
        """
        ret = self.mysql_mgr.select_one(
            f"select pipeline_name from {db}.`{table}` where create_time>'{start_time}' and create_time<'{end_time}' and pyframe_branch!='master'"
        )
        log.info(ret)
        return ret

    def create_week_table(self, db_name: str, table_name: str):
        if not self.mysql_mgr.exists_table(db_name, table_name):
            sql = f"""
                create table `{table_name}`
                (
                    `date`           DATETIME       DEFAULT CURRENT_TIMESTAMP COMMENT '日期',
                    `build_count`    int            DEFAULT 0 COMMENT '构建次数',
                    `duration_time`  float          DEFAULT 0 COMMENT '构建时长',
                    `success_rate`   float          DEFAULT 0 COMMENT '成功率'
                );
            """

            if not self.mysql_mgr.create_table(sql=sql, db_name=db_name):
                content = f"**提示:** 创建数据表{table_name}失败，请及时查看"
                return wechat.send_unicast_post_failure(content=content)

    def insert_table(self, db_name, table_name: str, date: str, build_count: int, duration_time: float, success_rate: float):
        sql = f"""
            insert into `{db_name}`.`{table_name}`(date, build_count, duration_time, success_rate) values('{date}', {build_count}, {duration_time}, {success_rate});
        """
        self.mysql_mgr.insert_one(sql=sql)
