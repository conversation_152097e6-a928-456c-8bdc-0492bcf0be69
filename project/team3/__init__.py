class Config:
    # 项目配置
    UE_ENGINE = {
        "url": "https://gitlab.h3d.com.cn",
        "token": "********************",
        "project": "1792",
    }
    # M2 P4配置
    M2_P4 = {
        "port": "m1.p4.com:2005",
        "user": "m2_ci",
        "password": "m2ci123456",
    }

    # SMB配置
    SMB = {
        "username": "guest",
        "password": "",
        "server_ip": "f.h3d.com.cn",
        "port": 445,
        "server_name": "h3d",
        "domain": "WORKGROUP",
        "use_ntlm_v2": True,
        "is_direct_tcp": True,
    }
    # job_names = {
    #     # "client/xls_config",
    #     "client/mul_build_ts",
    # }
    # job_need_wait = {
    #     "master",
    #     "branch_2024/v_03",
    # }
    submit_client_branches = {
        "branch_2024/v_03",
    }
    jenkins_info = {
        "jenkins_domain": "http://jenkins-m2.h3d.com.cn/",
        "jenkins_user": "<EMAIL>",
        "jenkins_password": "yXawwNdPPeXdbiBj81yGiF1FgyVg8hcq",
    }


config = Config()
