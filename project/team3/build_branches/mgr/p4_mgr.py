# coding=utf-8
import re
from datetime import datetime
import subprocess
import threading, pathlib
from frame import *
from project.team3 import config
from project.team3.build_branches.mgr.env_mgr import jenkins_env_mgr, env_mgr, global_env_mgr


class P4MgrBase:
    def __init__(self):
        self.workspace = env.pipeline.workspace()
        self.p4_port = config.M2_P4.get("port", "")
        self.p4_user = config.M2_P4.get("user", "")
        self.p4_password = config.M2_P4.get("password", "")


class ClientP4Mgr(P4MgrBase):
    def __init__(self):
        super().__init__()
        self.client = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_client_{common.get_host_ip()}")
        log.info(f"client : {self.client}, user: {self.p4_port}, port: {self.p4_user}")
        self.p4_client = P4Client(self.p4_port, self.p4_user, self.p4_password, self.client)
        # self.p4_env = "testdepot"
        self.p4_env = "H3D_M2"
        views = [
            f"//{self.p4_env}/M2_Product/{jenkins_env_mgr.get_p4_branch()}/... //{self.client}/...",
            ]
        self._composer_path = f"//{self.p4_env}/M2_Product/MVComposerProject/Plugins"
        # 主支的 engine plugin 目录, 分支目录名再看 最好是 //{self.p4_env}/M2_Product/{branch}/MVComposerProject/Plugins/... 后面再定
        if jenkins_env_mgr.get_p4_branch() != "trunk": 
            self._composer_path = f"//{self.p4_env}/M2_Product/{jenkins_env_mgr.get_p4_branch()}/MVComposerProject/Plugins"
        views.extend([
        f"{self._composer_path}/... //{self.client}/M2_Client/Plugins/...",
        f"-{self._composer_path}/mv_composer_updated //{self.client}/M2_Client/Plugins/mv_composer_updated",])
        self.p4_client.set_charset(charset=self.p4_client.Charset.UTF8)
        self.p4_client.set_view(views)
        self.p4_client.set_options(allwrite=True)
        self.p4_client.set_root(self.workspace)

    def sync_all(self):
        """
        *   同步p4                                                    
        *   除了 mvcomposer 目录需要拉取上次成功的提交, 其余拉取最新      
        *   不需要拉 ue_engine 
        """
        branch = jenkins_env_mgr.get_p4_branch()
        force = jenkins_env_mgr.get_p4_force()
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/binaries/...", force=force)
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/client/...", force=force)
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/client_wwise/...", force=force)
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/common/...", force=force)
        self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/server/...", force=force) 
        # self.p4_client.sync(path=f"//{self.p4_env}/M2_Product/{branch}/ue_engine/...", force=force)
        # 拉取plugin branch_2024/v_03 版本 还没有 engine plugin, 不需要拉取
        if not re.search(re.compile(r"branch_2024/v_03", re.S), jenkins_env_mgr.get_p4_branch()):
            last_change = self.p4_client.get_latest_changes(path=f"//{self.p4_env}/M2_Product/{branch}/binaries/...#have")
            description = last_change["desc"]
            plugin_changelist_group = re.search(re.compile(r"plugin_changelist:(\d+),", re.S), description)
            changelist = 0
            if plugin_changelist_group:
                changelist = plugin_changelist_group.group(1) 
            if not jenkins_env_mgr.get_engine_plugin_tag():
                # plugin 未主动输入: 拉取客户端对应分支中上次成功的      
                plugin_tag_group = re.search(re.compile(r"plugin_tag:(v\d+\.\d+\.\d+)", re.S), description)          
                if plugin_tag_group:
                    env_mgr.set_plugin_tag(plugin_tag_group.group(1))
                env_mgr.set_ue_plugin_changelist(changelist)    
            else:
                # plugin 主动输入: 拉取最后一次匹配的tag, 放到 M2_Client/Plugins 目录
                last_tag_changelist = self.__get_latest_plugin_tag_changelist(changelist)
                log.info(f"***** plugin last tag changelist: {last_tag_changelist} *****")
                if not last_tag_changelist:
                    raise PyframeException(f"Plugin 发版失败, 上次发版提交号: {changelist}, 至今没有描述包含 'tag:' 的提交")
                self.p4_client.sync(path=f"{self._composer_path}/...",changelist=f"{last_tag_changelist}", force=force)     
                env_mgr.set_plugin_tag(plugin_tag=jenkins_env_mgr.get_engine_plugin_tag())
                env_mgr.set_ue_plugin_changelist(plugin_changelist=last_tag_changelist)   
            self.p4_client.sync(path=f"{self._composer_path}/...",changelist=f"{env_mgr.get_ue_plugin_changelist()}", force=force)     
        # 这些都只是通知里面用 
        changes = self.p4_client.get_latest_changes(path=f"//{self.p4_env}/M2_Product/{jenkins_env_mgr.get_p4_branch()}/...#have")
        env_mgr.set_p4_latest_changelist(latest_changelist=changes.get("change"))
        env_mgr.set_p4_latest_desc(latest_desc=changes.get("desc"))
        env_mgr.set_p4_latest_submitter(latest_submitter=changes.get("user"))
        env_mgr.set_p4_latest_time(latest_time=datetime.fromtimestamp(int(changes.get("time"))).strftime("%Y-%m-%d %H:%M:%S"))

    def __get_latest_plugin_tag_changelist(self, start_change = 0):
        all_changes = self.p4_client.get_changes(path=f"{self._composer_path}/...@{start_change},now", max=10000)
        for change in all_changes:
            description = change["desc"]
            tag_group = re.search(re.compile(r"tag:(v\d+\.\d+\.\d+)", re.S), description)
            if tag_group:
                tag = tag_group.group(1)
                if tag == jenkins_env_mgr.get_engine_plugin_tag():
                    return change["change"]
        raise PyframeException(f"没有找到最新的插件tag, 请检查输入tag:{jenkins_env_mgr.get_engine_plugin_tag()} 是否正确")


client_p4_mgr = ClientP4Mgr()
