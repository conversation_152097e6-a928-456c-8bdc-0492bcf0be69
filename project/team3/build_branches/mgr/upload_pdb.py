import os, sys
current_path = os.getcwd()
sys.path.append(current_path)

from project.m2 import config
from frame import *
def __start_upload(file_path):
    """
    启动并发上传
    """
    with SMBClient(username=config.SMB.get("username"), password=config.SMB.get("password"), server_ip=config.SMB.get("server_ip")) as c:
        for root, _, files in os.walk(file_path):
            for pdb in files:
                c.upload(os.path.join(root, pdb), "TAC", f"symbol/m2/{pdb}")
    path_mgr.rm(file_path)

if __name__ == "__main__":

    if len(sys.argv) != 2:
        log.error("参数错误: python script.py 参数")
        sys.exit(1)
    arg = sys.argv[1]
    log.info(f"arg: {arg}")
    __start_upload(arg)
    # t1 = multiprocessing.Process(target=__start_upload, args=(arg,))
    # t1.daemon = True  # 将线程t1设置为后台线程
    # t1.start()
    # print("主线程正在运行……")
    # #time.sleep(5)
    # print("主线程正在退出")