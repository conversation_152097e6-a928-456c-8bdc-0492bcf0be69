import platform
import time
from enum import Enum

from frame import *

from project.team3.build_render_benchmark.mgr.env_mgr import env_mgr, jenkins_env_mgr


class BuildLogType(Enum):
    LOG = "log.txt"
    UBT_UNREAL_EDITOR_WIN64_DEBUG_GAME = "UBT-UnrealEditor-Win64-DebugGame.txt"
    UBT_UNREAL_EDITOR_WIN64_DEVELOPMENT = "UBT-UnrealEditor-Win64-Development.txt"
    UBT_UNREAL_HEADER_TOOL_MAC_DEVELOPMENT = "UBT-UnrealHeaderTool-Mac-Development.txt"
    UBT_UNREAL_HEADER_TOOL_WIN64_DEVELOPMENT = "UBT-UnrealHeaderTool-Win64-Development.txt"


class BuildMgr:
    def __init__(self) -> None:
        self.workspace = env.pipeline.workspace()
        self.engine_path = os.path.join(self.workspace, "UE_5.1_T3", "Engine")
        self.batch_files_path = os.path.join(self.engine_path, "Build", "BatchFiles")

    @staticmethod
    def get_log_path(log_type: BuildLogType) -> str:
        """
        获取日志路径
        """
        return os.path.join(env.pipeline.workspace(), "UE_5.1_T3", "Engine", "Programs", "AutomationTool", "Saved", "Logs", log_type.value)

    @staticmethod
    def get_cache_dir() -> str:
        """
        获取缓存路径
        """
        return os.path.join(env.pipeline.workspace(), "unrealengine5_1_0_cache")

    def download_cache(self):
        """
        下载缓存
        """
        cache_params = f"--cache={self.get_cache_dir()}"
        commands = [f"Setup.bat {cache_params}" if platform.system() == "Windows" else f"./Setup.command {cache_params}"]
        ret = cmd.run_shell(
            cmds=commands,
            workdir=os.path.join(env.pipeline.workspace(), "UE_5.1_T3"),
        )
        if ret[0] != 0:
            raise PyframeException(f"设置ue5.1缓存失败, 错误码为{ret[0]}")

    @staticmethod
    def raise_build_exception(log_path: str):
        """
        解析编译错误
        """
        log.info("start parse build log")
        if not path_mgr.exists(log_path):
            log.warn("log.txt not exists")
            return
        errors = []
        f = open(log_path, "r", encoding="utf-8", errors="ignore")
        line = f.readline()
        while line:
            line = line.strip()
            if "A conflicting instance of AutomationTool is already running." in line:
                raise PyframeException("编译ue5.1引擎失败, A conflicting instance of AutomationTool is already running.")

            if "Unable to find valid certificate/mobile provision pair." in line:
                raise PyframeException("编译ue5.1引擎失败, Unable to find valid certificate/mobile provision pair.")

            if "Host key verification failed" in line:
                raise PyframeException("编译ue5.1引擎失败, Host key verification failed, 请检查ssh key是否正确")

            if "Platform Android is not a valid platform to build. Check that the SDK is installed properly." in line:
                raise PyframeException("编译ue5.1引擎失败, Platform Android is not a valid platform to build. Check that the SDK is installed properly.")

            if "Unable to find mobile provision for UnrealGame" in line:
                raise PyframeException("编译ue5.1引擎失败, Unable to find mobile provision for UnrealGame")

            if "A conflicting instance of UnrealBuildTool is already running." in line:
                raise PyframeException("编译ue5.1引擎失败, A conflicting instance of UnrealBuildTool is already running.")

            if "Failed to load profile" in line:
                errors.append(line)

            if "MSBUILD : error" in line:
                errors.append(line)

            if "ERROR:" in line:
                errors.append(line)

            if ": error" in line:
                errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"编译ue5.1引擎失败: \n{errors_str}")
        else:
            raise PyframeException("编译ue5.1引擎失败, 未知错误")

    def build_engine(self):
        """
        编译引擎
        """
        build_type_list = jenkins_env_mgr.get_build_type()
        win64 = "true" if "WithWin64" in build_type_list else "false"
        android = "true" if "WithAndroid" in build_type_list else "false"

        # 根据平台不同组织不同的命令
        scripts = "-Script=Engine/Build/InstalledEngineBuild.xml"
        cmd_prefix = f'RunUAT.bat BuildGraph {scripts} -target="Make Installed Build Win64" '

        commands = [
            f"{cmd_prefix} -set:WithWin64={win64} "
            "-set:WithWin32=false "
            f"-set:WithMac=false "
            f"-set:WithAndroid={android} "
            f"-set:WithIOS=false "
            "-set:WithTVOS=false "
            "-set:WithLinux=false "
            "-set:WithLinuxAArch64=false "
            "-set:WithLinuxArm64=false "
            "-set:WithLumin=false "
            f"-set:WithHoloLens=false "
            "-set:WithDDC=false "
            "-set:GameConfigurations=Development "
        ]
        start_time = time.time()
        ret = cmd.run_shell(
            cmds=commands,
            workdir=self.batch_files_path,
            return_output=True,
            errors="ignore",
            encoding="gb2312" if platform.system() == "Windows" else "utf-8",
            log_to_file="ue5.1_build.log",
        )

        # 检查是否有编译实例在运行，此时返回值是0
        if time.time() - start_time < 60 * 2:
            self.raise_build_exception(log_path="ue5.1_build.log")

        if ret[0] != 0:
            # 解析错误
            self.raise_build_exception(log_path="ue5.1_build.log")
            raise PyframeException(f"编译ue5.1引擎失败, 错误码为{ret[0]}")

    def upload_build_log(self):
        """
        上传编译日志
        """
        log_path = self.get_log_path(BuildLogType.LOG)
        if path_mgr.exists(log_path):
            build_log_url = advance.upload_pipeline_log(path=log_path)
            env_mgr.set_build_log_url(log_url=build_log_url)


build_mgr = BuildMgr()
