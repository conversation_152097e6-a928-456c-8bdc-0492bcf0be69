from frame import env, common


class EnvMgr:
    @classmethod
    def set_latest_commit_id(cls, commit_id: str):
        env.set({"LATEST_COMMIT_ID": commit_id})

    @classmethod
    def get_latest_commit_id(cls):
        return env.get("LATEST_COMMIT_ID")

    @classmethod
    def set_latest_commit_msg(cls, commit_msg: str):
        env.set({"LATEST_COMMIT_MSG": commit_msg})

    @classmethod
    def get_latest_commit_msg(cls):
        return env.get("LATEST_COMMIT_MSG")

    @classmethod
    def set_latest_commit_time(cls, commit_time: str):
        env.set({"LATEST_COMMIT_TIME": commit_time})

    @classmethod
    def get_latest_commit_time(cls):
        return env.get("LATEST_COMMIT_TIME")

    @classmethod
    def set_build_log_url(cls, log_url: str):
        env.set({"BUILD_LOG_URL": log_url})

    @classmethod
    def get_build_log_url(cls):
        return env.get("BUILD_LOG_URL") or ""

    @classmethod
    def set_latest_committer_name(cls, name: str):
        env.set({"LATEST_COMMITTER_NAME": name})

    @classmethod
    def get_latest_committer_name(cls):
        return env.get("LATEST_COMMITTER_NAME")

    @classmethod
    def set_latest_committer_email(cls, email: str):
        env.set({"LATEST_COMMITTER_EMAIL": email})

    @classmethod
    def get_latest_committer_email(cls):
        return env.get("LATEST_COMMITTER_EMAIL")

    @classmethod
    def set_git_tag_content(cls, content: str):
        return env.set({"GIT_TAG_CONTENT": content})

    @classmethod
    def get_git_tag_content(cls):
        return env.get("GIT_TAG_CONTENT")

    @classmethod
    def set_latest_p4_changelist(cls, changelist: str):
        return env.set({"LATEST_P4_CHANGELIST", changelist})

    @classmethod
    def get_latest_p4_changelist(cls):
        return env.get("LATEST_P4_CHANGELIST")

    @classmethod
    def set_app_url(cls, url: str):
        return env.set({"APP_URL": url})

    @classmethod
    def get_app_url(cls):
        return env.get("APP_URL") or ""


class JenkinsEnvMgr:
    """
    Jenkins环境变量管理器
    """

    @classmethod
    def get_engine_git_branch(cls):
        return env.get("ENGINE_GIT_BRANCH")

    @classmethod
    def get_p4_force(cls):
        return common.str2bool(env.get("P4_FORCE"))

    @classmethod
    def get_build_type(cls):
        build_type = env.get("BUILD_TYPE") or env.get("build_type")
        return build_type.split(",")

    @classmethod
    def get_engine_git_tag(cls):
        return env.get("ENGINE_GIT_TAG")

    @classmethod
    def get_build_cpp(cls):
        return common.str2bool(env.get("BUILD_CPP", "false"))

    @classmethod
    def get_build_client(cls):
        return common.str2bool(env.get("BUILD_CLIENT", "false"))


env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
