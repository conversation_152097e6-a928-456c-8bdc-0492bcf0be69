from frame import advance, GitMgr, env, log, wechat, path_mgr
from project.team3.build_ue51_engine.mgr.build_mgr import build_mgr
from project.team3.build_ue51_engine.mgr.env_mgr import jenkins_env_mgr, env_mgr


@advance.stage(stage="更新引擎代码")
def get_ci_engine_code(**kwargs):
    git_branch = jenkins_env_mgr.get_engine_git_branch()
    git_mgr = GitMgr(workdir=env.pipeline.workspace(), project_name="UE_5.1_T3")
    if not git_mgr.exist():
        git_mgr.clone(url="https://gitlab.h3d.com.cn/team3/unreal-engine/UE_5.1_T3.git", branch=git_branch)

    # git_mgr.reset()
    # git_mgr.clean()
    git_mgr.advance_pull(git_branch)

    commit_id = git_mgr.get_local_latest_commit_id(short=True)
    env_mgr.set_latest_commit_id(commit_id)
    commit_msg = git_mgr.get_local_latest_commit_msg()
    env_mgr.set_latest_commit_msg(commit_msg)
    committer_name = git_mgr.get_local_latest_committer_name()
    env_mgr.set_latest_committer_name(committer_name)
    committer_email = git_mgr.get_local_latest_committer_email()
    env_mgr.set_latest_committer_email(committer_email)
    commit_time = git_mgr.get_local_latest_commit_time()
    env_mgr.set_latest_commit_time(commit_time)


@advance.stage(stage="下载engine编译缓存")
def download_engine_cache(**kwargs):
    cache_dir = build_mgr.get_cache_dir()
    if not path_mgr.exists(cache_dir):
        build_mgr.download_cache()
    else:
        log.info(f"{cache_dir} already exist, skip")


@advance.stage(stage="编译engine")
def ue51_build(**kwargs):
    build_mgr.build_engine()


@advance.stage(stage="上传引擎编译日志")
def upload_build_log(**kwargs):
    build_mgr.upload_build_log()


def __get_msg():
    branch = jenkins_env_mgr.get_engine_git_branch()
    build_type = jenkins_env_mgr.get_build_type()
    committer_email = env_mgr.get_latest_committer_email()
    commit_id = env_mgr.get_latest_commit_id()
    commit_msg = env_mgr.get_latest_commit_msg()
    commit_time = env_mgr.get_latest_commit_time()
    build_log_url = env_mgr.get_build_log_url()
    build_log_name = build_log_url.split("/")[-1]
    msg = ""
    if build_type:
        if isinstance(build_type, list):
            build_type = ", ".join(build_type)
            msg += f"**编译类型**: {build_type}\n"
        else:
            msg += f"**编译类型**: {build_type}\n"
    msg += f"**分支**: {branch}" if branch else ""
    msg += f"@{commit_id}" if commit_id else ""
    msg += f" ({commit_time})\n" if commit_time else "\n"
    msg += f"**最新提交人**: {committer_email}\n" if committer_email else ""
    msg += f"**commit msg**: {commit_msg}\n" if commit_msg else ""
    msg += f"**编译日志**: [{build_log_name}]({build_log_url})\n" if build_log_url else ""

    return msg


def __get_userlist():
    return ["<EMAIL>", "<EMAIL>"]


def on_always(**kwargs):
    if not env_mgr.get_build_log_url():
        build_mgr.upload_build_log()


def on_success(**kwargs):
    wechat.send_unicast_post_success(content=__get_msg(), user_list=__get_userlist())
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(content=__get_msg(), user_list=__get_userlist())
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=__get_msg(),
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_unstable(**kwargs):
    wechat.send_unicast_post_unstable(content=__get_msg(), user_list=__get_userlist())
    wechat.send_multicast_post_unstable(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_unstable()
