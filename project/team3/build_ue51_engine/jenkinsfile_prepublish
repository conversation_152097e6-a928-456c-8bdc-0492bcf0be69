node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "team3_ue51_engine_client_build_ci"
            customWorkspace "D:/jenkins/build_ue51_prepublish"
        }
    }
    parameters {
        booleanParam(name: 'force', defaultValue: false, description: '是否强更p4')
        string(name: 'engine_git_tag', defaultValue: "", description: '引擎代码仓库tag', trim: true)
        extendedChoice(
            name: 'build_type',
            defaultValue: 'WithWin64,WithAndroid,WithIOS,WithHoloLens',
            description: '编译类型',
            multiSelectDelimiter: ',',
            quoteValue: false,
            saveJSONParameterToFile: false,
            type: 'PT_CHECKBOX',
            value:'WithWin64,WithMac,WithAndroid,WithIOS,WithHoloLens',
            visibleItemCount: 5
        )
    }
    options {
        disableConcurrentBuilds()
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("更新引擎代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py build_ue51_engine_prepublish --job=get_publish_engine_code")
                    }
                }
            }
        }
        stage("下载缓存") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py build_ue51_engine_prepublish --job=download_engine_cache")
                    }
                }
            }
        }
        stage("编译") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py build_ue51_engine_prepublish --job=ue51_build")
                    }
                }
            }
        }
        stage("下载p4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py build_ue51_engine_prepublish --job=p4_sync")
                    }
                }
            }
        }
        stage("上传p4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py build_ue51_engine_prepublish --job=submit_to_p4")
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py build_ue51_engine_prepublish --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py build_ue51_engine_prepublish --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py build_ue51_engine_prepublish --job=on_failure")
                }
            }
        }
    }
}