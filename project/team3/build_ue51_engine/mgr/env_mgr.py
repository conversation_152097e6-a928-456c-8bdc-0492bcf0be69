from frame import env, common


class EnvMgr:
    def set_latest_commit_id(self, commit_id: str):
        env.set({"LATEST_COMMIT_ID": commit_id})

    def get_latest_commit_id(self):
        return env.get("LATEST_COMMIT_ID")

    def set_latest_commit_msg(self, commit_msg: str):
        env.set({"LATEST_COMMIT_MSG": commit_msg})

    def get_latest_commit_msg(self):
        return env.get("LATEST_COMMIT_MSG")

    def set_latest_commit_time(self, commit_time: str):
        env.set({"LATEST_COMMIT_TIME": commit_time})

    def get_latest_commit_time(self):
        return env.get("LATEST_COMMIT_TIME")

    def set_build_log_url(self, log_url: str):
        env.set({"BUILD_LOG_URL": log_url})

    def get_build_log_url(self):
        return env.get("BUILD_LOG_URL") or ""

    def set_latest_committer_name(self, name: str):
        env.set({"LATEST_COMMITTER_NAME": name})

    def get_latest_committer_name(self):
        return env.get("LATEST_COMMITTER_NAME")

    def set_latest_committer_email(self, email: str):
        env.set({"LATEST_COMMITTER_EMAIL": email})

    def get_latest_committer_email(self):
        return env.get("LATEST_COMMITTER_EMAIL")

    def set_git_tag_content(self, content: str):
        return env.set({"GIT_TAG_CONTENT": content})

    def get_git_tag_content(self):
        return env.get("GIT_TAG_CONTENT")


class JenkinsEnvMgr:
    """
    Jenkins环境变量管理器
    """

    @staticmethod
    def get_engine_git_branch():
        return env.get("ENGINE_GIT_BRANCH", "dev")

    @staticmethod
    def get_p4_force():
        return common.str2bool(env.get("FORCE", "false"))

    @staticmethod
    def get_build_type():
        build_type = env.get("BUILD_TYPE")
        return build_type.split(",")

    @staticmethod
    def get_engine_git_tag():
        return env.get("ENGINE_GIT_TAG")


env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
