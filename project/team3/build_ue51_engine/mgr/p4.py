import os
import re

from frame import log, P4Client, env, common

from project.team3.build_ue51_engine.mgr.env_mgr import env_mgr, jenkins_env_mgr


class Team3P4ClientBase:
    __slots__ = ["p4_client", "p4_password", "p4_user", "p4_port", "client"]

    def sync_all(self, force=False):
        """
        拉取p4
        Args:
            force: 是否强制拉取
        """
        self.p4_client.sync_all(force=force)

    def submit(self):
        """
        提交p4
        """
        reconcile_result = self.p4_client.reconcile(f"//{self.client}/PDB/...", delete=True, add=True, edit=True)
        reconcile_result += self.p4_client.reconcile(f"//{self.client}/Windows/...", delete=True, add=True, edit=True)
        if not reconcile_result:
            log.warn("no files to reconcile")
            return
        # TODO 临时注释
        submit_result = self.p4_client.submit(f"[ci submit]tag:{jenkins_env_mgr.get_engine_git_tag()},commit_message:{env_mgr.get_git_tag_content()}")
        log.info(f"submit result: {submit_result}")

        # commands = [
        #     f'echo {self.p4_password}|p4 -u {self.p4_user} -p {self.p4_port} login',
        #     f'{os.path.join(env.pipeline.workspace(), "UE_5.1_T3", "Engine", "Build", "BatchFiles", "RunUAT.bat")} '
        #     'BuildGraph '
        #     '-target="Submit To Perforce" '
        #     f'-script={os.path.join(env.pipeline.workspace(), "UE_5.1_T3", "Engine", "Build", "InstalledEngineBuild.xml")} '
        #     f'-set:Description="[ci submit]tag:{jenkins_env_mgr.get_engine_git_tag()},commit_message:{env_mgr.get_git_tag_content()}" '
        #     f'-p4port={self.p4_port} '
        #     f'-p4user={self.p4_user} '
        #     f'-p4pass={self.p4_password} '
        #     f'-p4client={self.client} '
        #     '-P4 '
        #     '-Submit'
        # ]
        # ret = cmd.run_shell(
        #     cmds=commands,
        #     errors="ignore"
        # )
        # if ret[0] != 0:
        #     raise PyframeException(f"提交p4失败, 错误码为{ret[0]}")


class Team3BuildP4Client(Team3P4ClientBase):
    def __init__(self):
        self.p4_port = "m1.p4.com:2005"
        self.p4_user = "m2_ci"
        self.p4_password = "m2ci123456"
        self.client = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_{common.get_host_ip()}")
        self.p4_client = P4Client(self.p4_port, self.p4_user, self.p4_password, self.client)

        # views = [
        #     f"//H3D_M2/M2_Exe/UE_5_1_0/... //{self.client}/...",
        #     f"//H3D_M2/M2_Exe/UE_5_1_0/UE5_PDB/... //{self.client}/Engine/PDB/...",
        #     f"//H3D_M2/M2_Exe/UE_5_1_0/UE5/... //{self.client}/Engine/Windows/...",
        # ]

        # TODO 目前先写死主支，后续需要改成动态获取
        views = [
            f"//H3D_M2/M2_Product/trunk/ue_engine/PDB/... //{self.client}/PDB/...",
            f"//H3D_M2/M2_Product/trunk/ue_engine/Windows/... //{self.client}/Windows/...",
        ]
        self.p4_client.set_view(views)
        self.p4_client.set_options(allwrite=True)
        self.p4_client.set_root(os.path.join(env.pipeline.workspace(), "UE_5.1_T3", "LocalBuilds", "Engine"))


class Team3PreBuildP4Client(Team3P4ClientBase):
    def __init__(self):
        self.p4_port = "m1.p4.com:2005"
        self.p4_user = "m2_ci"
        self.p4_password = "m2ci123456"
        self.client = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_{common.get_host_ip()}")
        self.p4_client = P4Client(self.p4_port, self.p4_user, self.p4_password, self.client)

        views = [
            f"//H3D_M2/M2_Product/branch_2023/v_test/ue_engine/... //{self.client}/...",
            f"//H3D_M2/M2_Product/branch_2023/v_test/ue_engine/UE5_PDB/... //{self.client}/Engine/PDB/...",
            f"//H3D_M2/M2_Product/branch_2023/v_test/ue_engine/UE5/... //{self.client}/Engine/Windows/...",
        ]
        self.p4_client.set_view(views)
        self.p4_client.set_options(allwrite=True)
        self.p4_client.set_root(os.path.join(env.pipeline.workspace(), "UE_5.1_T3", "LocalBuilds", "Engine"))
