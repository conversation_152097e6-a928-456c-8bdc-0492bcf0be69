node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "**************"
            customWorkspace "E:/pack_render_benchmark"
        }
    }
    parameters {
        booleanParam(name: 'P4_FORCE', defaultValue: false, description: '是否强更p4，默认false')
        booleanParam(name: 'CLEAN_CACHE', defaultValue: true, description: '是否清理缓存目录，包含DerivedDataCache，Intermediate，Saved，默认false')
        booleanParam(name: 'BUILD_CPP', defaultValue: true, description: '是否编译C++，默认true')
        booleanParam(name: 'BUILD_CLIENT', defaultValue: true, description: '是否打包APP，默认true')
    }
    options {
        disableConcurrentBuilds()
    }
    triggers {
        cron('H 7 * * *')
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("清理缓存") {
            when {
                expression {
                    return "${params.CLEAN_CACHE}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py pack_ue51_client_render_benchmark --job=clean_cache")
                    }
                }
            }
        }
        stage("更新P4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py pack_ue51_client_render_benchmark --job=p4_sync")
                    }
                }
            }
        }
        stage("切换引擎") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py pack_ue51_client_render_benchmark --job=switch_engine")
                    }
                }
            }
        }
        stage("编译C++") {
            when {
                expression {
                    return "${params.BUILD_CPP}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py pack_ue51_client_render_benchmark --job=build_cpp")
                    }
                }
            }
        }
        stage("上传二进制") {
            when {
                expression {
                    return "${params.BUILD_CPP}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py pack_ue51_client_render_benchmark --job=submit_bin_to_p4")
                    }
                }
            }
        }
        stage("打包客户端") {
            when {
                expression {
                    return "${params.BUILD_CLIENT}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py pack_ue51_client_render_benchmark --job=build_client")
                    }
                }
            }
        }
        stage("上传客户端") {
            when {
                expression {
                    return "${params.BUILD_CLIENT}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py pack_ue51_client_render_benchmark --job=upload_client")
                    }
                }
            }
        }
        stage("提交uproject及其他") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py pack_ue51_client_render_benchmark --job=submit_uproject_others")
                    }
                }
            }
        }
    }
    post {
        always {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py pack_ue51_client_render_benchmark --job=on_always")
                }
            }
        }
        unstable {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py pack_ue51_client_render_benchmark --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py pack_ue51_client_render_benchmark --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py pack_ue51_client_render_benchmark --job=on_failure")
                }
            }
        }
    }
}