from frame import *
from project.m2 import config
from project.team3.pack_render_benchmark.mgr.env_mgr import env_mgr


class BuildMgr:
    def __init__(self):
        self.workspace = env.pipeline.workspace()
        self.windows_engine = os.path.join(self.workspace, "RenderBenchmarkEngine", "Windows")
        self.engine_path = os.path.join(self.windows_engine, "Engine")
        self.unreal_build_tool = os.path.join(self.engine_path, "binaries", "DotNET", "UnrealBuildTool", "UnrealBuildTool.exe")
        self.build_project_log = os.path.join(self.workspace, "logs", "build-project-log.txt")
        self.benchmark_project_path = os.path.join(self.workspace, "RenderBenchmark")
        self.project_path = os.path.join(self.benchmark_project_path, "RenderBenchmark.uproject")
        self.archive_dir = os.path.join(self.benchmark_project_path, "archive_app")
        self.batch_files_path = os.path.join(self.engine_path, "Build", "BatchFiles")
        self.switcher = r"C:\Program Files (x86)\Epic Games\Launcher\Engine\Binaries\Win64\UnrealVersionSelector.exe"
        self.cache_dirs = ["DerivedDataCache", "Intermediate", "Saved"]

    def clean_cache(self):
        """
        清理缓存目录，DerivedDataCache、Intermediate、Saved
        """
        for cache_dir in self.cache_dirs:
            ab_cache_dir = os.path.join(self.benchmark_project_path, cache_dir)
            if path_mgr.exists(ab_cache_dir):
                path_mgr.rm(ab_cache_dir)

    def switch_engine(self):
        """
        切换引擎版本
        """
        cmd_lines = f'"{self.switcher}"  /switchversionsilent  {self.project_path} {self.windows_engine}'
        ret = cmd.run_shell(cmds=[cmd_lines])
        if ret[0] != 0:
            raise PyframeException(f"引擎版本切换异常，请流水线组排查原因")

    def build_cpp(self):
        """
        编译C++
        """
        # 先生成解决方案
        cmd_lines = ""
        cmd_lines += f"{self.unreal_build_tool} -projectfiles -project={self.project_path} "
        cmd_lines += f"-log={self.build_project_log} -WaitMutex -FromMsBuild "
        ret = cmd.run_shell(cmds=[cmd_lines])
        if ret[0] != 0:
            raise PyframeException(f"生成解决方案报错，请程序检查错误")

        # 然后再进行编译
        cmd_lines = f"{self.unreal_build_tool} RenderBenchmarkEditor Win64 Development -Project={self.project_path}"
        ret = cmd.run_shell(cmds=[cmd_lines])
        if ret[0] != 0:
            raise PyframeException(f"编译C++报错，请程序检查错误")

    def package_client(self):
        """
        打包客户端
        """
        # 打包前先清理
        if path_mgr.exists(self.archive_dir):
            path_mgr.rm(self.archive_dir)
        path_mgr.mkdir(self.archive_dir)

        cmd_str = ""
        cmd_str += f"RunUAT.bat BuildCookRun -project={self.project_path} -clean -nop4 -nocompileeditor "
        cmd_str += f"-build -cook -stage -package -platform=Android -cookflavor=ASTC -clientconfig=Development "
        cmd_str += f"-pak -prereqs -nodebuginfo -utf8output  -archive -archivedirectory={self.archive_dir} "
        ret = cmd.run_shell(
            cmds=[cmd_str],
            workdir=self.batch_files_path,
            encoding="utf-8",
            log_to_file=f"build-client-log-{env.pipeline.build_num()}.log",
        )

        if ret[0] != 0:
            # self._raise_package_client_exception(log_path=f"build-client-log-{env.pipeline.build_num()}.log")
            advance.unreal_engine.raise_automation_tool_log_exception(log_path=f"build-client-log-{env.pipeline.build_num()}.log")
            raise PyframeException(f"打包客户端失败，请程序排查问题")

    # def _raise_package_client_exception(self, log_path: str):
    #     log.info("start parse package client log")
    #     errors = []
    #     f = open(log_path, "r", encoding="utf-8")
    #     line = f.readline()
    #     while line:
    #         line = line.strip()
    #         if "AutomationTool exiting with ExitCode" in line:
    #             index = line.find("AutomationTool exiting with ExitCode")
    #             line = line[index:]
    #             if line not in errors:
    #                 errors.append(line)

    #         line = f.readline()
    #     f.close()
    #     if len(errors) > 0:
    #         errors_str = "\n".join(errors)
    #         raise PyframeException(f"打包客户端失败, AutomationTool异常退出, 请程序检查代码问题\n{errors_str}")
    def upload_obb(self):
        """
        上传客户端obb文件
        """
        # 从指定目录获取obb
        n = Nexus(**config.NEXUS_CONFIG)
        astc_path = os.path.join(self.archive_dir, "Android_ASTC")
        obbs = path_mgr.glob(astc_path, "*.obb")
        if not obbs:
            raise PyframeException("未找到obb文件")

        obb_urls = []
        for obb in obbs:
            app_name = os.path.basename(obb)
            p4_changelist = env_mgr.get_latest_p4_changelist()
            app_name = f"obb_benchmark_p4_{p4_changelist}_{app_name}"
            src_parent = os.path.dirname(obb)
            new_src = os.path.join(src_parent, app_name)
            path_mgr.move(obb, new_src)

            # 上传obb TODO 暂时没有team3的制品库，先上传到m2
            dst = f"http://nexus.h3d.com.cn/repository/m2-pipeline/m2/client/benchmark/{app_name}"
            n.upload(new_src, dst)
            obb_urls.append(dst)
        env_mgr.set_obb_urls(obb_urls)

    def upload_client(self):
        """
        上传客户端apk文件
        """
        # 从指定目录获取apk
        n = Nexus(**config.NEXUS_CONFIG)
        astc_path = os.path.join(self.archive_dir, "Android_ASTC")
        src = path_mgr.glob(astc_path, "*.apk")
        if not src:
            raise PyframeException("未找到apk文件")

        src = src[0]
        # 重命名apk
        p4_changelist = env_mgr.get_latest_p4_changelist()
        src_parent = os.path.dirname(src)
        app_name = f"client_benchmark_p4_{p4_changelist}.apk"
        new_src = os.path.join(src_parent, app_name)
        path_mgr.move(src, new_src)

        # 上传apk TODO 暂时没有team3的制品库，先上传到m2
        dst = f"http://nexus.h3d.com.cn/repository/m2-pipeline/m2/client/benchmark/{app_name}"
        env_mgr.set_app_url(dst)
        n.upload(new_src, dst)

    def upload_build_log(self):
        """
        上传打包日志
        """
        if path_mgr.exists(self.build_project_log):
            build_log_url = advance.upload_pipeline_log(path=self.build_project_log)
            env_mgr.set_build_log_url(build_log_url)


build_mgr = BuildMgr()
