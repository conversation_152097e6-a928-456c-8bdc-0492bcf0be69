from frame import env, common


class EnvMgr:
    @classmethod
    def set_latest_p4_changelist(cls, changelist: str):
        return env.set({"LATEST_P4_CHANGELIST": changelist})

    @classmethod
    def get_latest_p4_changelist(cls):
        return env.get("LATEST_P4_CHANGELIST")

    @classmethod
    def set_app_url(cls, url: str):
        return env.set({"APP_URL": url})

    @classmethod
    def get_app_url(cls):
        return env.get("APP_URL") or ""

    @classmethod
    def set_obb_urls(cls, urls: list):
        return env.set({"OBB_URLS": urls})

    @classmethod
    def get_obb_urls(cls):
        return env.get("OBB_URLS") or ""

    @classmethod
    def get_build_log_url(cls):
        return env.get("BUILD_LOG_URL") or ""

    @classmethod
    def set_build_log_url(cls, url: str):
        return env.set({"BUILD_LOG_URL": url})


class JenkinsEnvMgr:
    """
    Jenkins环境变量管理器
    """

    @classmethod
    def get_p4_force(cls):
        return common.str2bool(env.get("P4_FORCE", "false"))

    @classmethod
    def get_build_cpp(cls):
        return common.str2bool(env.get("BUILD_CPP", "false"))

    @classmethod
    def get_build_client(cls):
        return common.str2bool(env.get("BUILD_CLIENT", "false"))

    @classmethod
    def get_clean_cache(cls):
        return common.str2bool(env.get("CLEAN_CACHE", "false"))


env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
