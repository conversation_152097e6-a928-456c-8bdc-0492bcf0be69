import re

from frame import *
from project.team3 import config
from project.team3.pack_render_benchmark.mgr.env_mgr import env_mgr, jenkins_env_mgr


class PackRenderBenchmarkP4Client:
    def __init__(self):
        self.workspace = env.pipeline.workspace()
        self.p4_port = config.M2_P4.get("port", "")
        self.p4_user = config.M2_P4.get("user", "")
        self.p4_password = config.M2_P4.get("password", "")
        self.client = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_project_{common.get_host_ip()}")
        self.p4_client = P4Client(self.p4_port, self.p4_user, self.p4_password, self.client)
        self.p4_client.set_charset(self.p4_client.Charset.UTF8_BOM)
        self.p4_force = jenkins_env_mgr.get_p4_force()

        views = [
            f"//H3D_M2/M2_Product/RenderBenchmark/... //{self.client}/RenderBenchmark/...",
            f"//H3D_M2/M2_Product/RenderBenchmarkEngine/... //{self.client}/RenderBenchmarkEngine/...",
        ]
        self.p4_client.set_view(views)
        self.p4_client.set_options(allwrite=True)
        self.p4_client.set_root(self.workspace)

    def sync_all(self):
        """
        拉取p4

        Returns:

        """
        # 记录当前workspace最新的changelist
        ret = self.p4_client.get_current_workspace_latest_changes()
        log.info(f"get_current_workspace_latest_changes: {ret}")
        changelist = ret.get("change", "")
        env_mgr.set_latest_p4_changelist(changelist)

        # 拉取p4
        self.p4_client.sync_all(force=self.p4_force)

    # def get_reconcile_dirs(self) -> list:
    #     """
    #     获取需要reconcile的目录
    #     """
    #     plugin_dirs = os.path.join(self.workspace, "RenderBenchmark", "Plugins")
    #     plugins = path_mgr.glob(plugin_dirs, "*")
    #     reconcile_dirs = []
    #     for plugin in plugins:
    #         binaries_dir = os.path.join(plugin, "binaries")
    #         if path_mgr.exists(binaries_dir):
    #             reconcile_dirs.append(binaries_dir)
    #     return reconcile_dirs

    # 重写get_reconcile_dirs方法，获取binaries目录
    def get_reconcile_dirs(self) -> list:
        """
        获取需要reconcile的目录
        """
        plugin_dirs = os.path.join(self.workspace, "RenderBenchmark", "Plugins")
        plugins = Path(plugin_dirs).rglob("binaries")
        reconcile_dirs = []
        for plugin in plugins:
            binaries_dir = str(plugin)
            log.info(f"binaries_dir: {binaries_dir}")
            reconcile_dirs.append(binaries_dir)
        return reconcile_dirs

    def submit(self):
        """
        提交p4
        """
        # 提交二进制
        reconcile_result = self.p4_client.reconcile(f"//{self.client}/RenderBenchmark/Binaries/Win64/...", delete=True)
        binaries_dirs = self.get_reconcile_dirs()
        for binaries_dir in binaries_dirs:
            log.info(f"reconcile {binaries_dir}")
            reconcile_result += self.p4_client.reconcile(f"{binaries_dir}/...", delete=True)

        if not reconcile_result:
            log.warn("no files to reconcile")
            return

        submit_result = self.p4_client.submit(f"[ci submit]")
        log.info(f"submit result: {submit_result}")

    # 更新uproject工程文件
    def submit_uproject(self):
        # 根据
        reconcile_result = self.p4_client.reconcile(f"//H3D_M2/M2_Product/RenderBenchmark/RenderBenchmark.uproject")
        if not reconcile_result:
            log.warn("no files to reconcile")
            return
        submit_result = self.p4_client.submit(f"[ci submit]")
        log.info(f"submit result: {submit_result}")


p4_client = PackRenderBenchmarkP4Client()
