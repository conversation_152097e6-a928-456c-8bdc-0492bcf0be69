# coding=utf-8
import platform
import time
from enum import Enum

from frame import *
from project.team3 import config
from project.team3.publish_ue_engine.mgr.env_mgr import engine_env_mgr as env_mgr
from project.team3.publish_ue_engine.mgr.env_mgr import engine_jenkins_env_mgr as jenkins_env_mgr


class BuildLogType(Enum):
    LOG = "log.txt"
    UBT_UNREAL_EDITOR_WIN64_DEBUG_GAME = "UBT-UnrealEditor-Win64-DebugGame.txt"
    UBT_UNREAL_EDITOR_WIN64_DEVELOPMENT = "UBT-UnrealEditor-Win64-Development.txt"
    UBT_UNREAL_HEADER_TOOL_MAC_DEVELOPMENT = "UBT-UnrealHeaderTool-Mac-Development.txt"
    UBT_UNREAL_HEADER_TOOL_WIN64_DEVELOPMENT = "UBT-UnrealHeaderTool-Win64-Development.txt"


class BuildMgr:
    def __init__(self) -> None:
        self.workspace = os.path.join(env.pipeline.workspace())
        self.build_type_list = jenkins_env_mgr.get_build_type()
        self.target_branch = jenkins_env_mgr.get_engine_target_branch()
        if not self.target_branch:
            raise PyframeException("引擎代码合并的目标分支不能为空")
        self.git_tag = jenkins_env_mgr.get_engine_git_tag()
        if not self.git_tag:
            raise PyframeException("引擎代码仓库tag不能为空")
        self.git_branch = jenkins_env_mgr.get_engine_git_branch()
        self.git_url = "https://gitlab.h3d.com.cn/team3/unreal-engine/UE_5.1_T3.git"
        self.ue_engine = os.path.join(self.workspace, "ue_engine")

    def update_engine(self):
        """
        更新引擎代码
        """
        git_mgr = GitMgr(workdir=self.workspace, project_name="UE_5.1_T3")
        if not git_mgr.exist():
            git_mgr.clone(url=self.git_url, branch=self.git_branch)

        git_mgr.fetch(all=True)
        try:
            git_mgr.checkout(self.git_tag)
        except Exception as e:
            log.warn(f"切换分支报错: {e}")
            self.download_cache()
        latest_commit_msg = git_mgr.get_local_latest_commit_msg()
        env_mgr.set_latest_commit_msg(latest_commit_msg)
        committer_email = git_mgr.get_local_latest_committer_email()
        env_mgr.set_latest_committer_email(committer_email)
        env_mgr.set_git_tag_content(content=git_mgr.get_content_by_tag(self.git_tag).strip("'"))

    def get_log_path(self, log_type: BuildLogType) -> str:
        """
        获取日志路径
        """
        log_path = os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Programs", "AutomationTool", "Saved", "Logs", log_type.value)
        return log_path

    def get_cache_dir(self) -> str:
        """
        获取缓存路径
        """
        return os.path.join(self.workspace, "unrealengine5_1_0_cache")

    def download_cache(self):
        """
        下载缓存
        """
        cache_params = f"--cache={self.get_cache_dir()} --force"
        commands = [f"Setup.bat {cache_params}" if platform.system() == "Windows" else f"./Setup.sh {cache_params}"]
        ret = cmd.run_shell(
            cmds=commands,
            workdir=os.path.join(self.workspace, "UE_5.1_T3"),
        )
        if ret[0] != 0:
            raise PyframeException(f"设置ue5.1缓存失败, 错误码为{ret[0]}")

    def raise_build_exception(self, log_path: str):
        """
        解析编译错误
        """
        log.info("start parse build log")
        if not path_mgr.exists(log_path):
            log.warn("log.txt not exists")
            return
        errors = []
        encoding = "gb2312" if platform.system() == "Windows" else "utf-8"
        f = open(log_path, "r", encoding=encoding)
        line = f.readline()
        while line:
            line = line.strip()
            if "A conflicting instance of AutomationTool is already running." in line:
                raise PyframeException("编译ue5.1引擎失败, A conflicting instance of AutomationTool is already running.")

            if "Unable to find valid certificate/mobile provision pair." in line:
                raise PyframeException("编译ue5.1引擎失败, Unable to find valid certificate/mobile provision pair.")

            if "Host key verification failed" in line:
                raise PyframeException("编译ue5.1引擎失败, Host key verification failed, 请检查ssh key是否正确")

            if "Platform Android is not a valid platform to build. Check that the SDK is installed properly." in line:
                raise PyframeException("编译ue5.1引擎失败, Platform Android is not a valid platform to build. Check that the SDK is installed properly.")

            if "Unable to find mobile provision for UnrealGame" in line:
                raise PyframeException("编译ue5.1引擎失败, Unable to find mobile provision for UnrealGame")

            if "A conflicting instance of UnrealBuildTool is already running." in line:
                raise PyframeException("编译ue5.1引擎失败, A conflicting instance of UnrealBuildTool is already running.")

            if "Failed to load profile" in line:
                errors.append(line)

            if "MSBUILD : error" in line:
                errors.append(line)

            if "ERROR:" in line:
                errors.append(line)

            if ": error" in line:
                errors.append(line)

            line = f.readline()
        f.close()
        if len(errors) > 0:
            errors_str = "\n".join(errors)
            raise PyframeException(f"编译ue5.1引擎失败: \n{errors_str}")
        else:
            raise PyframeException("编译ue5.1引擎失败, 未知错误")

    def build_engine(self):
        """
        编译引擎
        """

        win64 = "true" if "WithWin64" in self.build_type_list else "false"
        android = "true" if "WithAndroid" in self.build_type_list else "false"
        ios = "true" if "WithIOS" in self.build_type_list else "false"
        mac = "true" if "WithMac" in self.build_type_list else "false"

        # 根据平台不同组织不同的命令
        scripts = "-Script=Engine/Build/InstalledEngineBuild.xml"
        if platform.system() == "Windows":
            cmd_prefix = f'RunUAT.bat BuildGraph {scripts} -target="Make Installed Build Win64" '
        else:
            cmd_prefix = f'./RunUAT.command BuildGraph {scripts} -target="Make Installed Build Mac" '
        commands = [
            f"{cmd_prefix} -set:WithWin64={win64} "
            "-set:WithWin32=false "
            f"-set:WithMac={mac} "
            f"-set:WithAndroid={android} "
            f"-set:WithIOS={ios} "
            "-set:WithTVOS=false "
            "-set:WithLinux=false "
            "-set:WithLinuxAArch64=false "
            "-set:WithLinuxArm64=false "
            "-set:WithLumin=false "
            "-set:WithHoloLens=false "
            "-set:WithDDC=false "
            "-set:GameConfigurations=Development "
            f"-set:BuiltDirectory={self.ue_engine} "
        ]
        start_time = time.time()
        ret = cmd.run_shell(
            cmds=commands,
            workdir=os.path.join(self.workspace, "UE_5.1_T3", "Engine", "Build", "BatchFiles"),
            return_output=True,
            errors="ignore",
            encoding="gb2312" if platform.system() == "Windows" else "utf-8",
            log_to_file="ue5.1_build.log",
        )

        # 检查是否有编译实例在运行，此时返回值是0
        if time.time() - start_time < 60 * 2:
            self.raise_build_exception(log_path="ue5.1_build.log")

        if ret[0] != 0:
            # 解析错误
            self.raise_build_exception(log_path="ue5.1_build.log")
            raise PyframeException(f"编译ue5.1引擎失败, 错误码为{ret[0]}")

    def upload_build_log(self):
        """
        上传编译日志
        """
        log_path = self.get_log_path(BuildLogType.LOG)
        if path_mgr.exists(log_path):
            build_log_url = advance.upload_pipeline_log(path=log_path)
            env_mgr.set_build_log_url(log_url=build_log_url)

    def merge_engine(self):
        """
        合并引擎代码
        """
        gitlab_mgr = GitlabMgr(**config.UE_ENGINE)
        title = f"[ci create] Merge {self.git_tag} to master"
        log.info(f"merge title: {title}")

        # 先通过tag创建分支
        turn_result = gitlab_mgr.turn_tag_to_branch(tag=self.git_tag)
        log.info(f"turn_result: {turn_result}")

        # 创建成功，合并到master
        if turn_result:
            # TODO 测试
            _, merge_result = gitlab_mgr.merge(
                source_branch=self.git_tag,
                target_branch=self.target_branch,
                title=title,
                merge_when_pipeline_succeeds=False,
            )
            log.info(f"merge_result: {merge_result}")


engine_mgr = BuildMgr()
