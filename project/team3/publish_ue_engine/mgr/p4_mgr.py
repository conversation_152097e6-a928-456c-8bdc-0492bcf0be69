# coding=utf-8
import re
from datetime import datetime

import threading

from frame import *
from project.team3 import config
from project.team3.publish_ue_engine.mgr.env_mgr import client_jenkins_env_mgr, client_env_mgr, engine_jenkins_env_mgr, engine_env_mgr


class P4MgrBase:
    def __init__(self):
        self.workspace = env.pipeline.workspace()
        self.p4_port = config.M2_P4.get("port", "")
        self.p4_user = config.M2_P4.get("user", "")
        self.p4_password = config.M2_P4.get("password", "")


class ClientP4Mgr(P4MgrBase):
    def __init__(self):
        super().__init__()
        self.client = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_client_{common.get_host_ip()}")
        self.p4_client = P4Client(self.p4_port, self.p4_user, self.p4_password, self.client)
        views = [
            f"//H3D_M2/M2_Product/trunk/... //{self.client}/...",
            f"-//H3D_M2/M2_Product/trunk/ue_engine/... //{self.client}/client_ue_engine/...",
            f"-//H3D_M2/M2_Product/trunk/binaries/... //{self.client}/binaries/...",
            f"//H3D_M2/M2_Product/Engine_Store/trunk/ue_engine/... //{self.client}/ue_engine/...",
        ]
        self.p4_client.set_charset(charset=self.p4_client.Charset.CP936)
        self.p4_client.set_view(views)
        self.p4_client.set_options(allwrite=True)
        self.p4_client.set_root(self.workspace)
        self.pdb_files = []

    def sync_all(self):
        """
        同步p4
        为了测试, 拉取客户端除 ue_engine 之外的其他目录, 拉取 产物仓库中ue_engine
        """
        force = client_jenkins_env_mgr.get_p4_force()
        path_mgr.rm(os.path.join(self.workspace, "binaries"))  # 把binaries 目录删除
        # self.p4_client.sync(path=f"//H3D_M2/M2_Product/trunk/binaries/...", force=force) # 引擎发版中 binaries 目录不需要管
        self.p4_client.sync_all(force=force)
        changes = self.p4_client.get_latest_changes(path=f"//H3D_M2/M2_Product/trunk/...#have")
        changelist = changes.get("change")
        desc = changes.get("desc")
        submitter = changes.get("user")
        time = datetime.fromtimestamp(int(changes.get("time"))).strftime("%Y-%m-%d %H:%M:%S")
        client_env_mgr.set_latest_changelist(latest_changelist=changelist)
        client_env_mgr.set_latest_desc(latest_desc=desc)
        client_env_mgr.set_latest_submitter(latest_submitter=submitter)
        client_env_mgr.set_latest_time(latest_time=time)

    def __move_pdb(self):
        ue_engine_dir = os.path.join(self.workspace, "ue_engine")
        pdb_list = path_mgr.glob(ue_engine_dir, "**/*.pdb")
        self.pdb_files = []
        dst_path = os.path.join(self.workspace, "pdb_tmp")
        path_mgr.rm(dst_path)
        path_mgr.mkdir(dst_path)
        for pdb in pdb_list:
            if path_mgr.exists(os.path.join(dst_path, os.path.basename(pdb))):
                path_mgr.rm(pdb)
                continue
            path_mgr.move(pdb, dst_path)
            self.pdb_files.append(os.path.join(dst_path, os.path.basename(pdb)))

    def __start_upload(self):
        """
        启动并发上传
        """
        with SMBClient(username=config.SMB.get("username"), password=config.SMB.get("password"), server_ip=config.SMB.get("server_ip")) as c:
            for pdb in self.pdb_files:
                c.upload(pdb, "TAC", f"symbol/m2/{os.path.basename(pdb)}")

    def submit_p4(self):
        """
        提交p4
        """
        # 提交前，先处理pdb文件 需要改成并发处理, 考虑可以先把所有pdb mv 到某个临时目录下, 再并发上传和删除
        self.__move_pdb()
        upload_thread = threading.Thread(target=self.__start_upload)
        upload_thread.start()
        # 只需要提交引擎
        reconcile_result = self.p4_client.reconcile_with_extra_args(
            f"//H3D_M2/M2_Product/Engine_Store/trunk/ue_engine/...",
            "-I",
            f"//H3D_M2/M2_Product/Engine_Store/trunk/ue_engine/Windows/Engine/DerivedDataCache/...",
            add=True,
            edit=True,
            delete=True,
        )
        if reconcile_result:
            commit_msg = engine_env_mgr.get_latest_commit_msg()
            git_tag = engine_jenkins_env_mgr.get_engine_git_tag()
            submit_desc = f"[ci submit]tag:{git_tag},commit_message:{commit_msg}"
            log.info(f"engine submit_desc: {submit_desc}")
            self.p4_client.submit(submit_desc, revert_if_failed=True)
        upload_thread.join()
        path_mgr.rm(os.path.join(self.workspace, "pdb_tmp"))


client_p4_mgr = ClientP4Mgr()
