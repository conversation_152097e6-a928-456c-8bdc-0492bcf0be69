from frame import *
from project.team3.publish_ue_engine.mgr.client_mgr import client_mgr
from project.team3.publish_ue_engine.mgr.engine_mgr import engine_mgr
from project.team3.publish_ue_engine.mgr.gitlab_mgr import ts_git_mgr
from project.team3.publish_ue_engine.mgr.link_mgr import link_mgr
from project.team3.publish_ue_engine.mgr.msg_mgr import Msg
from project.team3.publish_ue_engine.mgr.p4_mgr import client_p4_mgr


@advance.stage(stage="更新引擎代码")
def update_engine(**kwargs):
    engine_mgr.update_engine()


@advance.stage(stage="缓存引擎依赖")
def download_engine_cache(**kwargs):
    cache_dir = engine_mgr.get_cache_dir()
    if not path_mgr.exists(cache_dir):
        engine_mgr.download_cache()
    else:
        log.info(f"{cache_dir} 已存在，跳过下载引擎依赖")


@advance.stage(stage="编译引擎源码")
def ue51_build(**kwargs):
    engine_mgr.build_engine()


@advance.stage(stage="更新客户端工程")
def update_client(**kwargs):
    ts_git_mgr.update_client()


@advance.stage(stage="更新P4相关")
def update_p4(**kwargs):
    client_p4_mgr.sync_all()


@advance.stage(stage="创建软链接")
def create_symbolic_link(**kwargs):
    link_mgr.link_git_to_p4()


@advance.stage(stage="编译C++")
def build_cpp(**kwargs):
    client_mgr.build_ue5_cpp()


@advance.stage(stage="导出ts")
def export_ts(**kwargs):
    client_mgr.gen_dts()


@advance.stage(stage="导出js")
def export_js(**kwargs):
    client_mgr.compile_ts()


@advance.stage(stage="语言表检查")
def check_xls_config(**kwargs):
    client_mgr.xls_config()


@advance.stage(stage="生成manifest")
def generate_manifest(**kwargs):
    client_mgr.xls_config()
    client_mgr.gen_resource_manifest()


@advance.stage(stage="提交ue_engine")
def submit_p4(**kwargs):
    client_p4_mgr.submit_p4()


@advance.stage(stage="合并引擎代码")
def merge_engine(**kwargs):
    engine_mgr.merge_engine()


msg = Msg()
users = msg.get_user_list()
content = msg.get_message()


def on_always(**kwargs):
    engine_mgr.upload_build_log()


def send_branch_success(msg_content):
    log.info(f"通知消息: {msg_content} {users}")
    wechat.send_unicast_post_success(content=msg_content, user_list=users)
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=msg_content,
    )


def on_success(**kwargs):
    log.info(f"通知消息: {content} {users}")
    wechat.send_unicast_post_success(content=content, user_list=users)
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=content,
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(content=content, user_list=users)
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=content,
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_unstable(**kwargs):
    wechat.send_unicast_post_unstable(content=content, user_list=users)
    wechat.send_multicast_post_unstable(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=content,
    )
    advance.insert_pipeline_history_on_unstable()
