node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "*************"
            customWorkspace "D:/jenkins/sync_ue_engine_source"
        }
    }
    options {
        disableConcurrentBuilds()
    }
    triggers {
        cron('H 5 * * *')
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("获取Github分支") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py sync_ue_engine_source --job=get_github_branches")
                    }
                }
            }
        }
        stage("更新分支代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        retry(3) {
                                bat(script: "python team3.py sync_ue_engine_source --job=pull_github_branches")
                            }
                    }
                }
            }
        }
        stage("同步分支代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py sync_ue_engine_source --job=push_gitlab_branches")
                    }
                }
            }
        }
        stage("检查更新结果") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py sync_ue_engine_source --job=check_update_result")
                    }
                }
            }
        }
    }
    post {
//        always {
//            dir("${env.GIT_NAME}") {
//                script {
//                    bat(script: "python team3.py sync_ue_engine_source --job=on_always")
//                }
//            }
//        }
        success {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py sync_ue_engine_source --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py sync_ue_engine_source --job=on_failure")
                }
            }
        }
        unstable {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py sync_ue_engine_source --job=on_unstable")
                }
            }
        }
    }
}