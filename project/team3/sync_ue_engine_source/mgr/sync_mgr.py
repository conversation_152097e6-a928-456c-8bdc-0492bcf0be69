import re

import requests

from frame import *
from project.team3.sync_ue_engine_source.mgr.env_mgr import env_mgr


class SyncMgr:
    def __init__(self):
        self.github_access_token = "****************************************"
        self.gitlab_access_token = "********************"
        self.github_api_url = "https://api.github.com/repos/EpicGames/UnrealEngine/branches"
        self.github_api_headers = {"Authorization": f"Bearer {self.github_access_token}", "Accept": "application/vnd.github+json"}
        self.github_api_params = {"per_page": 100}
        self.workspace = env.pipeline.workspace()
        self.ue_engine_code_dir = os.path.join(self.workspace, "UnrealEngine")
        self.ue_engine_git_dir = os.path.join(self.ue_engine_code_dir, ".git")
        self.ue_engine_github_url = f"https://oauth2:{self.github_access_token}@github.com/EpicGames/UnrealEngine.git"
        self.gitlab_remote_url = f"https://oauth2:{self.gitlab_access_token}@gitlab.h3d.com.cn/team3/unreal-engine/UnrealEngineEpic.git"
        self.http_proxy = "http://*************:8118"
        self.https_proxy = "http://*************:8118"

    @staticmethod
    def __filter_branch(branches: list) -> list:
        """
        根据正则过滤分支信息，然后排序后，取出前五个
        """
        filter_branches = []
        pattern = re.compile(r"^\d+.\d+$")
        for branch in branches:
            branch_name = branch.get("name")
            if not re.match(pattern, branch_name):
                continue
            # 早期版本直接忽略
            if branch_name < "5.2":
                continue
            filter_branches.append(branch)
        log.info(f"filter_branches: {filter_branches}")
        sorted_branches = sorted(filter_branches, key=lambda x: x["name"], reverse=True)
        return sorted_branches

    def get_github_branches(self):
        """
        获取所有分支信息
        """
        branches = []
        page = 1
        self.github_api_params["page"] = page
        while True:
            try:
                branch_data = requests.get(url=self.github_api_url, headers=self.github_api_headers, params=self.github_api_params).json()
                branches.extend(branch_data)
                if len(branch_data) < self.github_api_params["per_page"]:
                    break
            except Exception as e:
                err_msg = f"获取github分支信息出错: {e}"
                log.error(err_msg)
                raise PyframeException(err_msg)
        log.info(f"branches: {branches}")
        filtered_branches = self.__filter_branch(branches)
        env_mgr.set_branch_info(filtered_branches)

    def pull_github_branches(self):
        """
        更新分支代码
        """

        if not path_mgr.exists(self.ue_engine_git_dir):
            ret, output = cmd.run_shell(
                cmds=[
                    f"git config --global http.proxy {self.http_proxy} && git config --global https.proxy {self.https_proxy} &&  git clone {self.ue_engine_github_url}"
                ],
                workdir=self.workspace,
            )
            if ret != 0:
                raise PyframeException(f"克隆UnrealEngine失败: {output}")
            # 克隆完成后设置新增remote
            ret, output = cmd.run_shell(cmds=[f"git remote add gitlab {self.gitlab_remote_url}"], workdir=self.ue_engine_code_dir)
            if ret != 0:
                raise PyframeException(f"设置UnrealEngine仓库remote失败: {output}")

        else:
            branches = env_mgr.get_branch_info()
            pull_success_branches = []
            for branch in branches:
                branch_name = branch.get("name")
                ret, output = cmd.run_shell(
                    cmds=[f"git clean . -xdf && git fetch origin {branch_name} && git checkout {branch_name} && git pull origin {branch_name}"],
                    workdir=self.ue_engine_code_dir,
                )
                if ret != 0:
                    log.error(f"更新UnrealEngine代码{branch_name}分支失败: {output}")
                    continue
                pull_success_branches.append(branch)
            env_mgr.set_pull_success_branch(pull_success_branches)

    def push_gitlab_branches(self):
        """
        同步分支代码
        """
        branches = env_mgr.get_pull_success_branch()
        push_success_branches = []
        for branch in branches:
            branch_name = branch.get("name")
            if branch_name == "release":
                gitlab_branch_name = "master"
            else:
                gitlab_branch_name = branch_name
            ret, output = cmd.run_shell(cmds=[f"git checkout {branch_name} && git push gitlab {gitlab_branch_name}"], workdir=self.ue_engine_code_dir)
            if ret != 0:
                log.error(f"同步UnrealEngine代码{branch_name}分支失败: {output}")
                continue
            push_success_branches.append(branch)
        env_mgr.set_push_success_branch(branches)

    @staticmethod
    def check_update_result():
        """
        检查更新结果
        """
        branches = env_mgr.get_branch_info()
        if branches:
            push_success_branches = env_mgr.get_push_success_branch()
            if not push_success_branches:
                raise PyframeException("全部更新失败")
            elif len(branches) != len(push_success_branches):
                raise PyframeException("部分更新失败")
            else:
                log.info("全部更新成功")

    @staticmethod
    def get_msg() -> str:
        """
        组织通知信息
        """
        msg = ""
        success_branches = env_mgr.get_push_success_branch()
        success_branch_names = [branch.get("name") for branch in success_branches]
        msg += f"**同步成功的分支:** {','.join(success_branch_names)}\n" if success_branch_names else ""
        all_branches = env_mgr.get_branch_info()
        all_branch_names = [branch.get("name") for branch in all_branches]
        failed_branch_names = list(set(all_branch_names) - set(success_branch_names))
        msg += f"**同步失败的分支:** {','.join(failed_branch_names)}\n" if failed_branch_names else ""
        return msg


sync_mgr = SyncMgr()
