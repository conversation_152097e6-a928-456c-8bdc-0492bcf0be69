from frame import *
from project.team3.sync_ue_engine_source.mgr.sync_mgr import sync_mgr


@advance.stage(stage="获取所有分支信息")
def get_github_branches(**kwargs):
    sync_mgr.get_github_branches()


@advance.stage(stage="更新分支代码")
def pull_github_branches(**kwargs):
    sync_mgr.pull_github_branches()


@advance.stage(stage="同步分支代码")
def push_gitlab_branches(**kwargs):
    sync_mgr.push_gitlab_branches()


@advance.stage(stage="检查更新结果")
def check_update_result(**kwargs):
    sync_mgr.check_update_result()


def on_success(**kwargs):
    content = sync_mgr.get_msg()
    wechat.send_unicast_post_success(content=content)
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=content,
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    content = sync_mgr.get_msg()
    wechat.send_unicast_post_failure(content=content)
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=content,
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    content = sync_mgr.get_msg()
    wechat.send_unicast_post_canceled(content=content)
    wechat.send_multicast_post_unstable(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=content,
    )
    advance.insert_pipeline_history_on_unstable()
