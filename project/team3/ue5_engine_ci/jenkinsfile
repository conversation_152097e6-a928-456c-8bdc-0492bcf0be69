node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "win10_192.168.13.130"
            customWorkspace "E:/ci"
        }
    }
    // parameters {
    //     booleanParam(name: 'force', defaultValue: false, description: '是否强更p4')
    //     string(name: 'engine_git_tag', defaultValue: "", description: '引擎代码仓库tag', trim: true)
    //     extendedChoice(
    //         name: 'build_type',
    //         defaultValue: 'WithWin64',
    //         description: '编译类型',
    //         multiSelectDelimiter: ',',
    //         quoteValue: false,
    //         saveJSONParameterToFile: false,
    //         type: 'PT_CHECKBOX',
    //         value:'WithWin64,WithAndroid',
    //         visibleItemCount: 5
    //     )
    // }
    options {
        disableConcurrentBuilds()
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("更新引擎代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py ue5_engine_ci --job=clone_ue5")
                    }
                }
            }
        }
        stage("下载缓存") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py ue5_engine_ci --job=setup_cache")
                    }
                }
            }
        }
        stage("生成sln") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py ue5_engine_ci --job=generate_sln")
                    }
                }
            }
        }
        stage("编译引擎") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py ue5_engine_ci --job=build_ue5")
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py ue5_engine_ci --job=on_canceled")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py ue5_engine_ci --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py ue5_engine_ci --job=on_failure")
                }
            }
        }
    }
}