node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "win10_192.168.13.130_mvcomposer"
            customWorkspace "E:/utility_pack_mv_composer"
        }
    }
    parameters {
        booleanParam(name: 'P4_FORCE', defaultValue: false, description: '是否强更p4，默认false')
        string(name: 'P4_FORCE_BRANCH_PATH', defaultValue: '/M2_Product/branch_2024/v_08_final/MVComposerProject', description: '仓库基于H3D_M2的分支路径分支')
        booleanParam(name: 'CLEAN_CACHE', defaultValue: true, description: '是否清理缓存目录，包含DerivedDataCache，Intermediate，Saved，默认true')
        booleanParam(name: 'BUILD_CPP', defaultValue: true, description: '是否编译C++，默认true')
        booleanParam(name: 'PACK_CLIENT', defaultValue: true, description: '是否打包APP，默认true')
        booleanParam(name: 'PUBLISH_PLUGIN', defaultValue: false, description: '是否触发发版，默认false')
//      http://jenkins-m2.h3d.com.cn//generic-webhook-trigger/invoke?token=m2_build_by_source
        string(name: 'NEXT_PIPELINE_HOOK_URL', defaultValue: 'http://jenkins-m2.h3d.com.cn//generic-webhook-trigger/invoke?token=m2_build_by_source_branch_2024_v_08_final', description: '下一个需要触发的web hook任务的url')
    }
    options {
        disableConcurrentBuilds()
    }
    triggers {
        cron('H 7 * * *')
        GenericTrigger(
            genericVariables: [
                [key: 'P4_FORCE', value: '$.P4_FORCE'],
                [key: 'P4_FORCE_BRANCH_PATH', value: '$.P4_FORCE_BRANCH_PATH'],
                [key: 'NEXT_PIPELINE_HOOK_URL', value: '$.NEXT_PIPELINE_HOOK_URL'],
                [key: 'CLEAN_CACHE', value: '$.CLEAN_CACHE'],
                [key: 'BUILD_CPP', value: '$.BUILD_CPP'],
                [key: 'PACK_CLIENT', value: '$.PACK_CLIENT'],
                [key: 'PUBLISH_PLUGIN', value: '$.PUBLISH_PLUGIN'],
            ],
            // 用于前端定位执行哪一条流水线
            token: 'team3packmvcomposer',
            printContributedVariables: true,
            printPostContent: true,
        )
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("清理缓存") {
            when {
                expression {
                    return "${params.CLEAN_CACHE}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py utility_mv_composer --job=clean_cache")
                    }
                }
            }
        }
        stage("更新P4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py utility_mv_composer --job=p4_sync")
                    }
                }
            }
        }
        stage("切换引擎") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py utility_mv_composer --job=switch_engine")
                    }
                }
            }
        }
        stage("编译C++") {
            when {
                expression {
                    return "${params.BUILD_CPP}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py utility_mv_composer --job=build_cpp")
                    }
                }
            }
        }
        stage("上传二进制") {
            when {
                expression {
                    return "${params.BUILD_CPP}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py utility_mv_composer --job=submit_bin_to_p4")
                    }
                }
            }
        }
        stage("打包客户端") {
            when {
                expression {
                    return "${params.PACK_CLIENT}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py utility_mv_composer --job=build_client")
                    }
                }
            }
        }
        stage("上传客户端") {
            when {
                expression {
                    return "${params.PACK_CLIENT}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py utility_mv_composer --job=upload_client")
                    }
                }
            }
        }
        stage("触发发版") {
            when {
                expression {
                    return "${params.PUBLISH_PLUGIN}" == "true"
                }
            }
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python team3.py utility_mv_composer --job=publish_plugin")
                    }
                }
            }
        }
    }
    post {
        always {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py utility_mv_composer --job=on_always")
                }
            }
        }
        unstable {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py utility_mv_composer --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py utility_mv_composer --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python team3.py utility_mv_composer --job=on_failure")
                }
            }
        }
    }
}