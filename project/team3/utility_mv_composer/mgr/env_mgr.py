from frame import env, common


class EnvMgr:
    @classmethod
    def set_app_url(cls, url: str):
        return env.set({"APP_URL": url})

    @classmethod
    def get_app_url(cls):
        return env.get("APP_URL") or ""

    @classmethod
    def get_build_log_url(cls):
        return env.get("BUILD_LOG_URL") or ""

    @classmethod
    def set_build_log_url(cls, url: str):
        return env.set({"BUILD_LOG_URL": url})
    
    @classmethod
    def get_is_timer_pack(cls):
        return env.get("IS_TIMER_PACK")

    @classmethod
    def set_is_timer_pack(cls, timer_pack: bool):
        return env.set({"IS_TIMER_PACK": timer_pack})
    
    @classmethod
    def get_new_tag(cls):
        return env.get("NEW_TAG")

    @classmethod
    def set_new_tag(cls, new_tag: str):
        return env.set({"NEW_TAG": new_tag})
    
    @classmethod
    def get_composer_changelist(cls):
        return env.get("MVCOMPOSER_CHANGELIST")

    @classmethod
    def set_composer_changelist(cls, changelist: str):
        return env.set({"MVCOMPOSER_CHANGELIST": changelist})

class GlobalEnvMgr:
    @staticmethod
    def get_last_success_timer_pack():
        return env.get_global("LAST_SUCCESS_TIMER_PACK")

    @staticmethod
    def set_last_success_timer_pack(last_timer_pack_day: str):
        env.set_global({"LAST_SUCCESS_TIMER_PACK": last_timer_pack_day})

    @staticmethod
    def get_last_success_plugin_tag():
        return env.get_global("LAST_SUCCESS_PLUGIN_TAG")

    @staticmethod
    def set_last_success_plugin_tag(last_tag: str):
        env.set_global({"LAST_SUCCESS_PLUGIN_TAG": last_tag})
    
    @staticmethod
    def get_last_success_plugin_changelist():
        return env.get_global("LAST_SUCCESS_PLUGIN_CHANGELIST")

    @staticmethod
    def set_last_success_plugin_changelist(last_changelist: str):
        env.set_global({"LAST_SUCCESS_PLUGIN_CHANGELIST": last_changelist})

class JenkinsEnvMgr:
    """
    Jenkins环境变量管理器
    """

    @classmethod
    def get_p4_force(cls):
        return common.str2bool(env.get("P4_FORCE", "false"))
    @classmethod
    def get_p4_force_branch_path(cls):
        return env.get("P4_FORCE_BRANCH_PATH")  
    @classmethod
    def get_next_pipeline_hook_url(cls):
        return env.get("NEXT_PIPELINE_HOOK_URL")

    @classmethod
    def get_build_cpp(cls):
        return common.str2bool(env.get("BUILD_CPP", "false"))

    @classmethod
    def get_pack_client(cls):
        return common.str2bool(env.get("PACK_CLIENT", "false"))

    @classmethod
    def get_clean_cache(cls):
        return common.str2bool(env.get("CLEAN_CACHE", "false"))

    @classmethod
    def get_publish_plugin(cls):
        return common.str2bool(env.get("PUBLISH_PLUGIN", "false"))


env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
global_env_mgr = GlobalEnvMgr()
