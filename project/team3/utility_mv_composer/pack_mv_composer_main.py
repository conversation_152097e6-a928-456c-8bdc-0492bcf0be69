import json
import requests
from numpy.matlib import empty

from frame import *
from project.team3.utility_mv_composer.mgr.build_mgr import build_mgr
from project.team3.utility_mv_composer.mgr.env_mgr import env_mgr, global_env_mgr, jenkins_env_mgr
from project.team3.utility_mv_composer.mgr.p4_mgr import p4_client
from datetime import datetime

# 清理缓存
@advance.stage(stage="清理缓存")
def clean_cache():
    build_mgr.clean_cache()


@advance.stage(stage="更新P4")
def p4_sync():
    p4_client.sync_all()


@advance.stage(stage="切换引擎")
def switch_engine():
    build_mgr.switch_engine()


@advance.stage(stage="编译C++")
def build_cpp():
    build_mgr.build_cpp()


@advance.stage(stage="提交二进制到P4")
def submit_bin_to_p4():
    p4_client.submit()


@advance.stage(stage="打包客户端")
def build_client():
    build_mgr.package_client()


@advance.stage(stage="上传客户端到制品库")
def upload_client():
    build_mgr.upload_client()

@advance.stage(stage="触发发版")
def publish_plugin():
    url = jenkins_env_mgr.get_next_pipeline_hook_url()
    if url is None or url is empty():
        log.info(f"next_pipeline_hook_url is empty")
    else:
        headers = {"Content-Type": "application/json"}
        tag = env_mgr.get_new_tag()
        if not tag:
            tag = global_env_mgr.get_last_success_plugin_tag()
        data = {
            "ref": "master",
            "ENGINE_PLUGIN_TAG": f"v{tag}",
        }
        resp = requests.post(url, headers=headers, data=json.dumps(data))
        log.info(f"response:{resp}")

def __get_msg():
    build_log_url = env_mgr.get_build_log_url()
    build_log_name = build_log_url.split("/")[-1]
    msg = ""
    p4_force = jenkins_env_mgr.get_p4_force()
    msg += f"**是否强更**: {p4_force}\n"
    p4_force_branch_path = jenkins_env_mgr.get_p4_force_branch_path()
    msg += f"**分支路径**: {p4_force_branch_path}\n"
    next_pipeline_hook_url = jenkins_env_mgr.get_next_pipeline_hook_url()
    msg += f'**触发下一个流水线webhook url**: {next_pipeline_hook_url}\n'
    cc = jenkins_env_mgr.get_clean_cache()
    msg += f"**是否清理缓存目录**: {cc}\n"
    built_cpp = jenkins_env_mgr.get_build_cpp()
    msg += f"**是否编译C++**: {built_cpp}\n"
    built_client = jenkins_env_mgr.get_pack_client()
    msg += f"**是否打包客户端**: {built_client}\n"
    publish_plugin = jenkins_env_mgr.get_publish_plugin()
    msg += f"**是否发布plugin**: {publish_plugin}\n"
    msg += f"**编译日志**: [{build_log_name}]({build_log_url})\n" if build_log_url else ""
    client_url = env_mgr.get_app_url()
    client_name = client_url.split("/")[-1]
    msg += f"**客户端apk**: [{client_name}]({client_url})\n" if client_url else ""

    return msg


def __get_users():
    return ["<EMAIL>"]


def on_always():
    if not env_mgr.get_build_log_url():
        build_mgr.upload_build_log()


def on_success():
    if env_mgr.get_is_timer_pack():
        global_env_mgr.set_last_success_timer_pack(datetime.now().strftime("%Y-%m-%d"))
    wechat.send_unicast_post_success(content=__get_msg(), user_list=__get_users())
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_success()


def on_failure():
    wechat.send_unicast_post_failure(content=__get_msg(), user_list=__get_users())
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=__get_msg(),
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_unstable():
    wechat.send_unicast_post_unstable(content=__get_msg(), user_list=__get_users())
    wechat.send_multicast_post_unstable(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f0c6589-5652-4a88-8f71-f26c8064e0f8",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_unstable()
