node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}
pipeline{
    agent {
        node {
            label "win10_192.168.4.197"
        }
    }

    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 60, unit: 'MINUTES')
        buildDiscarder logRotator(numToKeepStr: env.numToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
        retry(1)
    }
    stages {
        stage('Arttrunk-CI'){
            matrix {
                agent {
                    node {
                        label "win10_192.168.4.197_arttrunk_test_${PLATFORM}"
                        customWorkspace "F:\\arttrunk_ci\\${PLATFORM}"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'android','ios'
                    }
                }
                stages {
                    stage('更新arttrunk'){
                        steps {
                            dir('pyframe-pipeline') {
                                bat """
                                    python team4.py arttrunk_ci --job=update_arttrunk
                                """
                            }
                        }
                    }
                    stage('获取git提交信息'){
                        steps {
                            dir('pyframe-pipeline') {
                                bat """
                                    python team4.py arttrunk_ci --job=get_commit
                                """
                            }
                        }
                    }
                    stage('更新p4资源'){
                        steps {
                            dir('pyframe-pipeline') {
                                bat """
                                    python team4.py arttrunk_ci --job=update_p4
                                """
                            }
                        }
                    }
                    stage('打包前准备'){
                        steps {
                            dir('pyframe-pipeline') {
                                bat """
                                    python team4.py arttrunk_ci --job=prepare
                                """
                            }
                        }
                    }
                    stage('打包shader'){
                        steps {
                            dir('pyframe-pipeline') {
                                bat """
                                    python team4.py arttrunk_ci --job=package_shader
                                """
                            }
                        }
                    }
                    stage('打包timeline'){
                        steps {
                            dir('pyframe-pipeline') {
                                bat """
                                    python team4.py arttrunk_ci --job=package_timeline
                                """
                            }
                        }
                    }
                   // stage('merge'){
                   //     steps {
                   //         dir('pyframe-pipeline') {
                   //             bat """
                   //                 python team4.py arttrunk_ci --job=merge
                   //             """
                   //         }
                   //     }
                   // }
                }
            }
        }
    }
    post{
        success{
            dir('pyframe-pipeline') {
                bat """
                    python team4.py arttrunk_ci --job=on_success
                """
            }
        }
        failure{
            dir('pyframe-pipeline') {
                bat """
                    python team4.py arttrunk_ci --job=on_failure
                """
            }
        }
    }
}