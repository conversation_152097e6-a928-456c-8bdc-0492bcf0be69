#! coding:utf-8
import time

from frame import *
from project.team4.arttrunk_ci.config import config
from project.team4.arttrunk_ci.mgr.clothTest_mgr import clothTest_mgr
from project.team4.arttrunk_ci.mgr.env_mgr import env_mgr
from project.team4.arttrunk_ci.mgr.p4_mgr import p4_mgr
from project.team4.arttrunk_ci.mgr.shader_mgr import shader_mgr
from project.team4.arttrunk_ci.mgr.timeline_mgr import timeline_mgr


@advance.stage("更新arttrunk")
def update_arttrunk(**kwargs):
    # 更新arttrunk代码
    project_name = "arttrunk"
    shader_arttrunk_workspace = os.path.join(env.pipeline.workspace(), "shader")
    if not path_mgr.exists(shader_arttrunk_workspace):
        path_mgr.mkdir(shader_arttrunk_workspace)

    shader_arttrunk_git = os.path.join(env.pipeline.workspace(), "shader", "arttrunk", ".git")
    git_mgr = GitMgr(workdir=shader_arttrunk_workspace, project_name=project_name)
    if not os.path.exists(shader_arttrunk_git):
        git_mgr.clone(url=config.url, branch="master")
    else:
        # 更新代码前还原本地修改
        git_mgr.reset(commit_id="origin/master")
        try:
            git_mgr.pull(branch="master")
        except PyframeException as e:
            log.error(e)
            cmd.run_shell(
                cmds=["git clean mobile_dancer/arttrunk/client/Assets/Editor -xdf && git pull origin master"],
                workdir=os.path.join(shader_arttrunk_workspace, "arttrunk"),
            )

    timeline_arttrunk_workspace = os.path.join(env.pipeline.workspace(), "timeline")
    if not path_mgr.exists(timeline_arttrunk_workspace):
        path_mgr.mkdir(timeline_arttrunk_workspace)

    timeline_arttrunk_git = os.path.join(env.pipeline.workspace(), "timeline", "arttrunk", ".git")
    git_mgr = GitMgr(
        workdir=timeline_arttrunk_workspace,
        project_name=project_name,
    )
    if not os.path.exists(timeline_arttrunk_git):
        git_mgr.clone(url=config.url, branch="master")
    else:
        try:
            git_mgr.pull(branch="master")
        except PyframeException as e:
            log.error(e)
            cmd.run_shell(
                cmds=["git clean mobile_dancer/arttrunk/client/Assets/Editor -xdf && git pull origin master"],
                workdir=os.path.join(timeline_arttrunk_workspace, "arttrunk"),
            )


@advance.stage("获取git提交信息")
def get_commit(**kwargs):
    git_obj = GitlabMgr(url=config.root_url, token=config.token, project=config.arttrunk_project_id)
    commit_id = git_obj.get_newest_commit_id(branch="master")
    commit = git_obj.get_commit_by_commit_id(branch="master", commit_id=commit_id)
    env_mgr.set_committer(committer=commit.committer_name)
    env_mgr.set_commit_message(commit_message=commit.message)
    commit_date = commit.committed_date
    timestamp = time.mktime(time.strptime(commit_date.split(".")[0], "%Y-%m-%dT%H:%M:%S"))
    commit_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp))
    env_mgr.set_commit_time(commit_time=commit_date)


@advance.stage("更新p4资源")
def update_p4(**kwargs):
    p4_mgr.sync_shader(shader_id=shader_mgr.shader_id)
    p4_mgr.sync_timeline()
    p4_mgr.sync_cloth()


@advance.stage("打包前准备")
def prepare(**kwargs):
    shader_mgr.prepare()
    shader_mgr.unzip()
    shader_mgr.link()
    timeline_mgr.prepare()


@advance.stage("打包shader")
def package_shader(**kwargs):
    shader_mgr.package()


@advance.stage("打包timeline")
def package_timeline(**kwargs):
    timeline_mgr.write_resources()
    timeline_mgr.package()

@advance.stage("换装测试")
def cloth_Test(**kwargs):
    clothTest_mgr.clothTest()

def __merge():
    try:
        git_obj = GitlabMgr(url=config.root_url, token=config.token, project=config.arttrunk_project_id)
        branches = git_obj.get_all_branches()
        if "release" not in branches:
            git_obj.create_branch(source_branch="master", target_branch="release")
            time.sleep(30)  # 30秒左右调用get_all_branches 才显示创建的release分支
        git_obj.merge(
            source_branch="master",
            target_branch="release",
            title="merge master to release",
            description="arttrunk 单元测试通过",
        )
    except Exception as e:
        raise PyframeException(f"master合并到release分支失败, {e}")


def _get_msg():
    committer = env_mgr.get_committer()
    commit_message = env_mgr.get_commit_message()
    commit_time = env_mgr.get_commit_time()
    log_url = env_mgr.get_file_log_url()
    msg = ""
    if committer:
        msg += f"**提交人**: {committer}\n"
    if commit_message:
        msg += f"**提交信息**: {commit_message}\n"
    if commit_time:
        msg += f"**提交时间**: {commit_time}\n"
    if log_url:
        msg += f"**打包日志**: [打包日志]({log_url})\n"
    return msg


def on_success(**kwargs):
    __merge()
    wechat.send_multicast_post_success(webhook=config.webhook, content=_get_msg())
    user: str = env_mgr.get_committer()
    if not user.endswith("@h3d.com.cn"):
        user += "@h3d.com.cn"
    wechat.send_unicast_post_success(user_list=[user], content=_get_msg())
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_multicast_post_failure(
        webhook=config.webhook,
        content=_get_msg()  # ,
        # mentioned_list=["<EMAIL>"],
        # rescue=False,
    )
    user: str = env_mgr.get_committer()
    if not user.endswith("@h3d.com.cn"):
        user += "@h3d.com.cn"
    wechat.send_unicast_post_failure(user_list=[user], content=_get_msg())
    advance.insert_pipeline_history_on_failure()
