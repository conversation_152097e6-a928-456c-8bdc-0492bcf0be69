from project.x5m import config

# p4配置
p4_host = config.P4_CONFIG_JENKINS.get("host")
p4_username = config.P4_CONFIG_JENKINS.get("username")
p4_password = config.P4_CONFIG_JENKINS.get("password")
p4_client = "arttrunk_unittest"

# git 账号密码
url = "http://x5mobile-gitlab.h3d.com.cn/dgm/arttrunk.git"
token = "********************"
root_url = "https://x5mobile-gitlab.h3d.com.cn"
arttrunk_project_id = "34"

# p4 timeline resources.txt打包资源
resources = ["assets/resources/art/timeline/shuangfei/14309341\n"]

# 企微通知
webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=462ee488-4dc3-4cec-9910-c73fd91c4046"
