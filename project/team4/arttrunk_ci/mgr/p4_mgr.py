from frame import *
from frame.p4.p4client import P4Client
from project.team4.arttrunk_ci.config import config
from project.team4.arttrunk_ci.mgr.env_mgr import jenkins_env_mgr


class P4Mgr:
    def __init__(self):
        self.platform = jenkins_env_mgr.get_platform()
        self.p4 = P4Client(
            host=config.p4_host,
            username=config.p4_username,
            password=config.p4_password,
            client=f"{config.p4_client}_{self.platform}",
        )
        self.p4.set_root(path=env.pipeline.workspace())
        self.p4_force = jenkins_env_mgr.get_p4_force()

    def sync_shader(self):
        """
        同步shader
        """
        shader_views = [f"//x5_mobile/mr/art_release/shader/0.84896/... //{self.p4.client}/mr/art_release/shader/0.84896/..."]
        self.p4.set_view(views=shader_views)
        self.p4.sync_all(force=True)

    def sync_timeline(self):
        """
        同步timeline
        """
        timeline_views = [
            f"//x5mplan/resmg/timeline/... //{self.p4.client}/timeline/timeline_config/...",
            f"//x5_mobile/mr/art_release/art_src/timeline/common/... //{self.p4.client}/timeline/arttrunk/mobile_dancer/arttrunk/client/assets/resources/art/timeline/common/...",
            # 公共资源
            f"//x5_mobile/mr/art_release/art_src/role/cloth_show/... //{self.p4.client}/timeline/arttrunk/mobile_dancer/arttrunk/client/assets/resources/art/role/cloth_show/...",
            # 公共资源
            f"//x5m/res/cdn/cooked/android/assetbundles/art/timeline/... //{self.p4.client}/timeline/arttrunk/mr/art_release/cs/android/assetbundles/art/timeline/...",
            # 公共资源
            f"//x5m/res/cdn/cooked/android/assetbundles/art/role/actions/actions_anim/... //{self.p4.client}/timeline/arttrunk/mr/art_release/cs/android/assetbundles/art/timeline/actions_anim/...",
            # 公共资源
            f"//x5m/res/cdn/cooked/android/assetbundles/art/role/actions/actions_anim_expression/... //{self.p4.client}/timeline/arttrunk/mr/art_release/cs/android/assetbundles/art/timeline/actions_anim_expression/...",
            # 公共资源
            f"//x5m/res/cdn/cooked/android/assetbundles/art/role/link/... //{self.p4.client}/timeline/arttrunk/mr/art_release/cs/android/assetbundles/art/timeline/link/...",
            # 公共资源
            f"//x5_mobile/mr/art_release/art_src/timeline/shuangfei/daifei_common_action/... //{self.p4.client}/timeline/arttrunk/mobile_dancer/arttrunk/client/assets/resources/art/timeline/shuangfei/daifei_common_action/...",
            # 测试的资源
            f"//x5_mobile/mr/art_release/art_src/timeline/shuangfei/14309341/... //{self.p4.client}/timeline/arttrunk/mobile_dancer/arttrunk/client/assets/resources/art/timeline/shuangfei/14309341/..."
            # 测试的资源
        ]
        self.p4.set_view(views=timeline_views)
        self.p4.sync_all(force=self.p4_force)


p4_mgr = P4Mgr()
