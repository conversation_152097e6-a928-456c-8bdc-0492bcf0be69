from frame import *
from project.team4.arttrunk_ci.mgr.env_mgr import env_mgr, jenkins_env_mgr


class ShaderMgr:
    def __init__(self) -> None:
        self.platform = jenkins_env_mgr.get_platform()
        self.workspace = env.pipeline.workspace()
        self.shader_workspace = os.path.join(self.workspace, "shader")
        self.build_target = "Android" if self.platform == "android" else "iOS"

    def prepare(self):
        clean_dirs = [
            os.path.join(self.shader_workspace, "arttrunk\\mobile_dancer\\arttrunk\\client\\Assets\\resources\\shaders"),
            os.path.join(self.shader_workspace, "arttrunk\\mobile_dancer\\arttrunk\\client\\Assets\\ExternPlugins\\NGUI\\Resources\\Shaders"),
        ]
        for path in clean_dirs:
            path_mgr.rm(path=path)

    def unzip(self):
        shader_compress_path = os.path.join(self.workspace, "mr\\art_release\\shader\\0.84896\\shader.zip")
        shader_decompress_path = os.path.join(self.shader_workspace, "mr\\art_release\\shader\\0.84896")
        tar.decompress(src=shader_compress_path, dst=shader_decompress_path)
        shader_md5_file = os.path.join(shader_decompress_path, "shader\\shader.md5")
        try:
            with open(shader_md5_file, "r", encoding="utf-8") as fr:
                lines = fr.readlines()
                for line in lines:
                    if line.strip():
                        path, md5_val = line.split("|")
                        file = os.path.join(shader_decompress_path, "shader", *path.split("/"))
                        md5 = common.compute_md5(file=file)
                        if md5 != md5_val.strip():
                            raise PyframeException(f"{file} md5值校验失败 原始值{md5_val.strip()} 计算值{md5}")
        except Exception as e:
            raise PyframeException(f"{file} md5校验失败 {e}")

    def link(self):
        shader_resource_path = os.path.join(self.shader_workspace, "mr\\art_release\\shader\\0.84896\\shader")
        path_mgr.soft_link(
            src=os.path.join(shader_resource_path, "Assets\\resources\\shaders"),
            dst=os.path.join(self.shader_workspace, "arttrunk\\mobile_dancer\\arttrunk\\client\\Assets\\resources\\shaders"),
        )
        path_mgr.soft_link(
            src=os.path.join(shader_resource_path, "Assets\\ExternPlugins\\NGUI\\Resources\\Shaders"),
            dst=os.path.join(self.shader_workspace, "arttrunk\\mobile_dancer\\arttrunk\\client\\Assets\\ExternPlugins\\NGUI\\Resources\\Shaders"),
        )

    def package(self):
        out_path = os.path.join(self.shader_workspace, rf"mr\art_release\shader\0.84896\{self.platform}\assetbundles")
        unity = r"D:\Program Files\Unity2018\Editor\Unity.exe"
        package_cmd = (
            # f"G:\\unity\\install\\Unity\\Editor\\Unity.exe -quit -batchmode -logFile shader_package_{self.platform}.log -projectPath "
            f'"{unity}" -quit -batchmode -logFile shader_package_{self.platform}.log -projectPath '
            + os.path.join(self.shader_workspace, "arttrunk\\mobile_dancer\\arttrunk\\client")
            + f" -executeMethod H3DABTool.ShaderABBuilder.GenerateShaderAb -buildTarget {self.build_target} platform={self.platform}"
            + f" outputPath={out_path}"
        )
        ret, _ = cmd.run_shell(
            cmds=[package_cmd],
            workdir=self.shader_workspace,
        )

        file_log = os.path.join(self.shader_workspace, f"shader_package_{self.platform}.log")
        if os.path.exists(file_log):
            log_url = advance.upload_pipeline_log(path=file_log)
            env_mgr.set_file_log_url(url=log_url)
        else:
            log.warning(f"未生成打包日志shader_package_{self.platform}.log")

        if ret != 0:
            advance.unity.raise_unity_log_exception(log_path=os.path.join(self.shader_workspace, f"shader_package_{self.platform}.log"))
            raise PyframeException(f"打包失败，请程序检查代码问题")

        generator_shader_file = os.path.join(out_path, "global_dependeencies", "shaders")
        if not path_mgr.exists(path=generator_shader_file):
            raise PyframeException(f"未生成ab资源 {generator_shader_file}")


shader_mgr = ShaderMgr()
