from frame import *
from project.team4.arttrunk_ci.config import config
from project.team4.arttrunk_ci.mgr.env_mgr import env_mgr, jenkins_env_mgr


class TimelineMgr:
    def __init__(self):
        self.platform = jenkins_env_mgr.get_platform()
        self.workspace = env.pipeline.workspace()
        self.build_target = "Android" if self.platform == "android" else "iOS"

    def prepare(self):
        # copy arttrunk资源给timeline使用
        timeline_path = os.path.join(self.workspace, "timeline")
        # path_mgr.rm(timeline_path)
        path_mgr.mkdir(timeline_path)

        # project_name = "arttrunk"
        # path_mgr.copy(
        #     src=os.path.join(self.timeline_workspace, project_name),
        #     dst=os.path.join(timeline_path, project_name),
        # )
        # 删除旧timeline资源目录
        old_timeline_path = os.path.join(self.workspace, "timeline\\arttrunk\\mobile_dancer\\arttrunk\\client\\Assets\\resources\\Art\\timeline")
        path_mgr.rm(path=old_timeline_path)

        path_mgr.xcopy(
            src=os.path.join(self.workspace, "timeline\\timeline_config\\newmulti_dependence.xlsx"),
            dst=os.path.join(self.workspace, "timeline\\arttrunk\\mobile_dancer\\arttrunk\\client\\Assets\\resources\\Art\\timeline"),
            dst_is_file=False,
        )

    def write_resources(self):
        # 打包资源写入mobile_dancer/arttrunk/client/AssetBundleTool/resources.txt
        resource_dir = os.path.join(self.workspace, "timeline\\arttrunk\\mobile_dancer\\arttrunk\\client\\AssetBundleTool")
        path_mgr.mkdir(resource_dir)
        resource_file = os.path.join(resource_dir, "resources.txt")
        with open(resource_file, "w", encoding="utf-8") as fw:
            for r in config.resources:
                fw.write(r)

    def package(self):
        out_path = os.path.join(self.workspace, "timeline\\arttrunk\\mr\\art_release\\cs\\android\\assetbundles")
        unity = r"D:\Program Files\Unity2018\Editor\Unity.exe"
        package_cmd = (
            f'"{unity}" -quit -batchmode -logFile package_{self.platform}_timeline.log -projectPath '
            + os.path.join(self.workspace, "timeline\\arttrunk\\mobile_dancer\\arttrunk\\client")
            + f" -executeMethod H3DBuildTools.BuildTimeline -buildTarget {self.platform}"
            + f" out_path={out_path}"
        )
        ret, _ = cmd.run_shell(
            cmds=[package_cmd],
            workdir=self.workspace,
        )

        file_log = os.path.join(self.workspace, f"package_{self.platform}_timeline.log")
        if os.path.exists(file_log):
            log_url = advance.upload_pipeline_log(path=file_log)
            env_mgr.set_file_log_url(url=log_url)
        else:
            log.warning(f"未生成打包日志package_{self.platform}_timeline.log")

        if ret != 0:
            advance.unity.raise_unity_log_exception(log_path=os.path.join(self.workspace, f"package_{self.platform}_timeline.log"))
            raise PyframeException(f"打包失败，请程序检查代码问题")

        generator_timeline_file = os.path.join(out_path, "art\\timeline\\14309341")
        if not path_mgr.exists(path=generator_timeline_file):
            raise PyframeException(f"未生成ab资源 {generator_timeline_file}")


timeline_mgr = TimelineMgr()
