# coding=utf-8


class Config:
    TESTDEV_DEVELOPER = ["<EMAIL>", "<EMAIL>", "ji<PERSON><PERSON><PERSON><PERSON>@h3d.com.cn", "<EMAIL>"]
    # TODO 个人账号密码
    P4_2002_CONFIG = {"host": "p4.com:2002", "username": "autobuild", "password": "gogogo"}
    P4_2004_CONFIG = {"host": "p4.com:2004", "username": "autobuild", "password": "gogogo"}
    P4_2003_CONFIG = {"host": "p4.com:2003", "username": "ci_x51_test_rw", "password": "ESsj6fF7ufMpfQYu"}
    P4_2005_CONFIG = {"host": "p4.com:2005", "username": "x51_testerbin", "password": "qe_p4_test_pass"}
    REGISTRY_TAC_CONFIG = {"registry": "registry.h3d.com.cn", "username": "tac", "password": "Tac12345"}
    REGISTRY_TACTD_CONFIG = {"registry": "registry.h3d.com.cn", "username": "tac-td", "password": "Tactd123"}
    CONFIG_JENKINS_TESTDEV = {"url": "http://jenkins-testdev.h3d.com.cn/", "username": "<EMAIL>", "password": "XJRdZTi4hyPI4H7xPEdmcLvxANrHOC61"}
    # x5m gitlab
    GITLAB_MAINTAINER = {
        "url": "https://x5mobile-gitlab.h3d.com.cn/",
        "username": "<EMAIL>",
        "password": "maintainer123",
        "token": "o-yNgwVADiQ8CicYdt_4",
    }
    # x5m yangzhenyue fork gitlab
    # GITLAB_YANGZHENYUE = {
    #     "url": "https://x5mobile-gitlab.h3d.com.cn/",
    #     "username": "<EMAIL>",
    #     "password": "Yzy135678",
    #     "token": "CGapcsXgfNsfP2DwybAi",
    # }
    # ************* FTP配置
    FTP_CONFIG_150_30 = {"ip": "*************", "port": 21, "username": "administrator", "password": "dgm123!#*"}


config = Config()
