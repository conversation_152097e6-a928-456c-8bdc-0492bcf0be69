# coding=utf-8
from frame import *
from project.testdev import config
from project.testdev.build_x51_mysql_image.mgr.p4_2003_mgr import P4COM2003


@advance.stage(stage="拉取p4")
def sync_p4(**kwargs):
    """
    拉取p4上文件
    Args:
        kwargs:
            branch: branch_2022/QQX5_Mainland_5.8.2_05 or trunc
    """
    # 检查磁盘空间是否满足执行流水线
    advance.check_disk_size(threshold=5)
    # 拉取P4
    p4_com_2003_mgr = P4COM2003()
    p4_com_2003_mgr.sync_all(force=True)


def __get_image_tag(branch: str):
    """
    根据分支名获取镜像的tag
    Args:
        branch: 分支名，形如trunc, branch_2022/QQX5_Mainland_5.8.2_05

    Returns:
        tag: 镜像的tag
    """
    if branch == "trunc":
        tag = branch
    else:
        tag = branch.split("_")[-2]
    return tag


@advance.stage(stage="dump mysql")
def dump_mysql(**kwargs):
    """
    mysql_dump，获取最新表结构
    """
    mysql_path = "/data/workspace/docker_files/mysql"
    path_mgr.mkdir(path=mysql_path)
    x51_mysql_host = "**************"
    ret = cmd.run_shell(cmds=["sh prepare_mysql_file.sh {}".format(x51_mysql_host)], workdir=mysql_path)
    if ret[0] != 0:
        env.set({"wechat_hint": "dump mysql失败"})
        raise PyframeException("dump mysql失败")


@advance.stage(stage="构建mysql镜像")
def build_x51_mysql_docker_image(**kwargs):
    """
    构建mysql镜像
    """
    branch = kwargs.get("branch")
    workdir = "/data/workspace/docker_files/mysql"
    docker = Docker(**config.REGISTRY_TACTD_CONFIG)
    repository = "registry.h3d.com.cn/tac-td/x51mysql"
    tag = __get_image_tag(branch=branch)
    docker.build(workdir=workdir, file="Dockerfile", repository=repository, tag=tag)


@advance.stage(stage="推送mysql镜像")
def push_x51_mysql_docker_image(**kwargs):
    """
    推送x51 mysql镜像
    """
    branch = kwargs.get("branch")
    docker = Docker(**config.REGISTRY_TACTD_CONFIG)
    tag = __get_image_tag(branch=branch)
    image = "registry.h3d.com.cn/tac-td/x51mysql:{}".format(tag)
    docker.push(image=image)


def on_success(**kwargs):
    branch = kwargs.get("branch")
    msg = "\n**分支:** {}".format(branch)
    wechat.send_unicast_post_success(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    branch = kwargs.get("branch")
    msg = "\n**分支:** {}".format(branch)
    wechat.send_unicast_post_failure(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    branch = kwargs.get("branch")
    msg = "\n**分支:** {}".format(branch)
    wechat.send_unicast_post_canceled(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_canceled()
