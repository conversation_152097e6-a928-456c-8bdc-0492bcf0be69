# coding=utf-8
from frame import *
from project.testdev import config


class P4COM2003:
    def __init__(self):
        client = f"testdev_build_x51_mysql_{common.get_host_ip()}"
        self.p4 = P4Client(
            host=config.P4_2003_CONFIG.get("host"),
            username=config.P4_2003_CONFIG.get("username"),
            password=config.P4_2003_CONFIG.get("password"),
            client=client,
        )
        views = [
            f"//H3D_X51_res/X51_SourceBase/doc/developer/game/whitwbox/docker_files/...  //{client}/docker_files/...",
        ]
        self.p4.set_view(views=views)
        root = "/data/workspace/"
        self.p4.set_root(path=root)

    def sync_all(self, force: bool):
        """
        拉取p4
        """
        self.p4.sync(path="//H3D_X51_res/X51_SourceBase/doc/developer/game/whitwbox/docker_files/...", force=force)
