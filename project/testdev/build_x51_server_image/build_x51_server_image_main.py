# coding=utf-8
from frame import *
from project.testdev import config
from project.testdev.build_x51_server_image.mgr.linux_bin_mgr import LinuxBinMgr
from project.testdev.build_x51_server_image.mgr.p4_2003_mgr import P4COM2003

WORKSPACE = "/data/workspace"


def __get_image_tag(branch: str):
    """
    根据分支名获取镜像的tag
    Args:
        branch: 分支名，形如trunc, branch_2022/QQX5_Mainland_5.8.2_05

    Returns:
        tag: 镜像的tag
    """
    if branch == "trunc":
        tag = branch
    else:
        tag = branch.split("_")[-2]
        # str_list = branch.split('/')
        # tag = str_list[1]
    return tag


def __rmi_none_images():
    """
    删除tag为none的镜像
    """
    pass
    # ret = cmd.run_shell(
    #     cmds=[
    #         "docker rmi $(docker images | grep \"none\" | awk '{print $3}')"
    #     ]
    # )
    # if ret[0] != 0:
    #     log.error("docker rmi none failed")
    #     exit(-1)


def prepare(**kwargs):
    """
    准备
    """
    # 检查磁盘空间是否满足执行流水线
    advance.check_disk_size(threshold=5)
    # path_mgr.mkdir(WORKSPACE)
    # cmd.run_shell(cmds=["alias rm=rm", "rm * -rvf"], workdir=WORKSPACE)
    path_mgr.mkdir("/data/mockp4/x51")
    path_mgr.mkdir("/data/workspace/x51")
    path_mgr.mkdir("/data/workspace/logs")
    # path_mgr.mkdir(WORKSPACE)
    command = "mount.cifs -o username=age,password=111,dir_mode=0777,file_mode=0777 //172.17.100.6/release/x51 /data/mockp4/x51"
    ret = cmd.run_shell(cmds=[command])
    if ret[0] != 0:
        cmd.run_shell(cmds=["yum install nfs-utils cifs-utils -y", command])

    branch = kwargs.get("branch")
    if branch != "trunc":
        y = branch.split("/")[0]
        tag = branch.split("_")[-2]
        server_branch = f"{y}/{tag}"
        svn_branch = server_branch.replace("_", "es")
    else:
        svn_branch = branch
        tag = branch
    env.set({"svn_branch": svn_branch, "server_tag": tag})


def sync_p4(**kwargs):
    """
    拉取p4上文件
    Args:
        kwargs:
            branch: branch_2022/QQX5_Mainland_5.8.2_05 or trunc
    """
    branch = kwargs.get("branch")
    force_update = kwargs.get("force_update")
    p4_com_2003_mgr = P4COM2003(branch=branch)
    changelist = p4_com_2003_mgr.get_latest_changes()
    env.set({"changelist": changelist})
    log.info("changelist = {}".format(changelist))
    # if not Path("/data/workspace/x51/{}".format(branch)).exists():
    #     force_update = True
    log.info(force_update)
    p4_com_2003_mgr.sync_all(changelist=changelist, force=True)


def download_decompress_linux_bin(**kwargs):
    """
    下载linux_bin，并解压
    """
    branch = kwargs.get("branch")
    changelist = env.get("changelist")
    linux_bin_mgr = LinuxBinMgr(branch=branch)
    linux_bin_version = linux_bin_mgr.match_bin_version(changelist=changelist)
    if linux_bin_version is None:
        hint = "<font color=red>p4 changelist: {}可能太老，制品库上已无该制品。请填写较新版本的changelist。</font>".format(changelist)
        env.set({"wechat_hint": hint})
        raise PyframeException(f"p4 changelist: {changelist}可能太老，制品库上已无该制品。请填写较新版本的changelist。")

    local_bin_version = linux_bin_mgr.read_local_bin_version()
    if local_bin_version == linux_bin_version:
        log.info("local_bin_version: {} doesn't need to re download".format(local_bin_version))
        return
    else:
        linux_bin_mgr.download_linux_bin(version=linux_bin_version)
        linux_bin_mgr.decompress_linux_bin()
        linux_bin_mgr.chmod()
        linux_bin_mgr.write_local_bin_version(version=linux_bin_version)


def download_svn_script(**kwargs):
    branch = kwargs.get("branch")
    # path_mgr.rm("/data/workspace/x51/{}/src/star".format(branch))
    branch_path = "/data/workspace/x51/{}".format(branch)
    star_path = "/data/workspace/x51/{}/src/star/".format(branch)
    svn_path = "/data/workspace/x51/{}/svn/".format(branch)
    path_mgr.mkdir(star_path)
    path_mgr.mkdir(svn_path)
    # TODO 个人账号密码
    command = "svn checkout --force --depth=files http://*************/svn/starx52/trunc/ ./svn --username songmin --password Lang5208"
    cmd.run_shell(cmds=[command], workdir=branch_path)
    cmd.run_shell(cmds=["cp -rvf *.* {}".format(star_path)], workdir=svn_path)
    cmd.run_shell(cmds=["cp settings.inc.template settings.inc", "chmod +x *.*"], workdir=star_path)


def build_x51_server_docker_image(**kwargs):
    """
    构建服务器镜像
    """
    branch = kwargs.get("branch")
    branch_path = "/data/workspace/x51/{}/".format(branch)
    bin_path = "{}exe/bin/".format(branch_path)
    # star_path = "{}src/star/"
    cmd.run_shell(cmds=["cp -rvf * {}".format(branch_path)], workdir="/data/workspace/docker_files/server/")
    cmd.run_shell(
        cmds=[
            "chmod +x *",
        ],
        workdir=branch_path,
    )
    cmd.run_shell(
        cmds=[
            "chmod +x *",
        ],
        workdir=bin_path,
    )
    docker = Docker(**config.REGISTRY_TAC_CONFIG)
    repository = "registry.h3d.com.cn/tac-td/x51server"
    tag = __get_image_tag(branch=branch)
    docker.build(workdir=branch_path, file="Dockerfile", repository=repository, tag=tag)


def build_x51_server_docker_image_copy(**kwargs):
    """ "
    构建服务器镜像
    """
    branch = kwargs.get("branch")
    branch_path = "/data/workspace/docker_files/server"
    docker = Docker(**config.REGISTRY_TAC_CONFIG)
    repository = "registry.h3d.com.cn/tac-td/x51server"
    tag = __get_image_tag(branch=branch)
    docker.build(workdir=branch_path, file="Dockerfile", repository=repository, tag=tag)


def dump_mysql(**kwargs):
    """
    mysql_dump，获取最新表结构
    """
    x51_mysql_host = "**************"
    ret = cmd.run_shell(cmds=["sh prepare_mysql_file.sh {}".format(x51_mysql_host)], workdir="/data/workspace/docker_files/mysql")
    if ret[0] != 0:
        env.set({"wechat_hint": "dump mysql失败"})
        exit(-1)


def build_x51_mysql_docker_image(**kwargs):
    """
    构建mysql镜像
    """
    branch = kwargs.get("branch")
    workdir = "/data/workspace/docker_files/mysql"
    docker = Docker(**config.REGISTRY_TAC_CONFIG)
    repository = "registry.h3d.com.cn/tac-td/x51mysql"
    tag = __get_image_tag(branch=branch)
    docker.build(workdir=workdir, file="Dockerfile", repository=repository, tag=tag)


def pull_x51_mysql_docker_image(**kwargs):
    """
    拉取x51 mysql镜像
    """
    branch = kwargs.get("branch")
    docker = Docker(**config.REGISTRY_TAC_CONFIG)
    tag = __get_image_tag(branch=branch)
    image = "registry.h3d.com.cn/tac-td/x51mysql:{}".format(tag)
    docker.pull(image=image)


def dump_redis(**kwargs):
    """
    导出redis数据
    """
    redis_host = "**************"
    redis_port = "10004"
    ret = cmd.run_shell(cmds=[f"redis-cli -h {redis_host} -p {redis_port} -a 111111 --rdb ./dump.rdb"], workdir="/data/workspace/docker_files/redis")
    if ret[0] != 0:
        log.error("redis dump failed")
        exit(-1)


def build_x51_redis_docker_image(**kwargs):
    """
    构建redis镜像
    """
    branch = kwargs.get("branch")
    workdir = "/data/workspace/docker_files/redis"
    docker = Docker(**config.REGISTRY_TAC_CONFIG)
    repository = "registry.h3d.com.cn/tac-td/x51redis"
    tag = __get_image_tag(branch=branch)
    docker.build(workdir=workdir, file="Dockerfile", repository=repository, tag=tag)


def on_success(**kwargs):
    __rmi_none_images()
    branch = kwargs.get("branch")
    msg = "\n**分支:** {}".format(branch)
    wechat.send_unicast_post_success(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    __rmi_none_images()
    branch = kwargs.get("branch")
    msg = "\n**分支:** {}".format(branch)
    hint = env.get("wechat_hint")
    if hint is None:
        msg += hint
    wechat.send_unicast_post_failure(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    __rmi_none_images()
    branch = kwargs.get("branch")
    msg = "\n**分支:** {}".format(branch)
    wechat.send_unicast_post_canceled(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_canceled()


def from_104_32_copy_files(**kwargs):
    branch = env.get("svn_branch")
    # print(f"branch:{branch}")
    # print("rsync exe_p4")
    ret = cmd.run_shell(
        cmds=[
            f"rsync --progress -avt -I root@*************:/data/workspace/daily/{branch}/p4_inuse/ --exclude=bin/logs/ docker_files/server/exe > /dev/null"
        ],
        workdir=WORKSPACE,
    )
    if ret[0] != 0:
        log.error("rsync exe_p4 error")
        exit(-1)

    # print("rsync exe")
    ret = cmd.run_shell(
        cmds=[
            f"rsync --progress -avt -I root@*************:/data/workspace/daily/{branch}/exe/ --exclude=bin/logs/ docker_files/server/exe > /dev/null"
        ],
        workdir=WORKSPACE,
    )
    if ret[0] != 0:
        log.error("rsync exe error")
        exit(-1)

    # print("rsync src")
    path_mgr.mkdir("/data/workspace/docker_files/server/src")
    ret = cmd.run_shell(
        cmds=[
            f"rsync --progress -avt -I root@*************:/data/workspace/daily/{branch}/src/star/* --exclude=*/ docker_files/server/src/star/ > /dev/null"
        ],
        workdir=WORKSPACE,
    )
    if ret[0] != 0:
        log.error("rsync src error")
        exit(-1)

    cmd.run_shell(cmds=["chmod +x *.sh"], workdir="/data/workspace/docker_files/server")

    cmd.run_shell(cmds=["cp settings.inc.template settings.inc"], workdir="/data/workspace/docker_files/server/src/star")

    cmd.run_shell(cmds=["chmod +x *.sh"], workdir="/data/workspace/docker_files/server/src/star")

    cmd.run_shell(cmds=["chmod +x *.so app_box admin_proxy service_box* ktv_* console_admin"], workdir="/data/workspace/docker_files/server/exe/bin")


# 关闭扔开启的虚机
def stop_started_vm(**kwargs):
    ret = cmd.run_shell(cmds=["docker-compose down -v"], workdir="/app/x51_server_mgr/config/")
    if ret[0] != 0:
        log.error("stop_started_vm")
        exit(-1)


# 删除悬空的虚机镜像
def remove_dangling_images(**kwargs):
    ret = cmd.run_shell(cmds=['docker rmi $(docker images -f "dangling=true" -q)'], workdir=WORKSPACE)
    if ret[0] != 0:
        log.error("remove_dangling_images_1 error")

    ret = cmd.run_shell(cmds=["docker volume rm $(docker volume ls -qf dangling=true)"], workdir=WORKSPACE)
    if ret[0] != 0:
        log.error("remove_dangling_images_2 error")
