version: '3'

networks:
  default:
    ipam:
      driver: default
      config:
        - subnet: "*********/16"

services:
  redis:
    container_name: x51-redis
    image: registry.h3d.com.cn/dev-productivity/x51redis:trunc
    ports:
      - "10004:6379"

  mysql:
    container_name: x51-mysql
    image: registry.h3d.com.cn/dev-productivity/x51mysql:trunc
    depends_on:
      - redis
    ports:
      - "3306:3306"

  server:
    container_name: x51-server
    image: registry.h3d.com.cn/dev-productivity/x51server:trunc
    depends_on:
      - mysql
    entrypoint: bash -c "while true;do echo hello docker;sleep 1m;done"
    ports:
      - "27661:27661"
      - "31414:31414"
      - "31514:31514"
      - "31419:31419"
      - "31435:31435"
      - "31415:31415"
      - "31417:31417"
      - "31433:31433"
      - "31432:31432"
      - "9981:9981"
      - "9983:9983"
      - "31434:31434"
      - "31436:31436"
      - "31536:31536"
      - "26672:26672"
      - "26680:26680"
      - "10014:10014"
      - "33001:33001"
      - "10017:10017"
      - "9972:9972"
      - "9973:9973"
      - "33000:33000"
      - "33002:33002"
      - "10040:10040"
      - "19988:19988"
