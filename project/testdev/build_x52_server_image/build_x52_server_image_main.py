# coding=utf-8
from frame import *
from project.testdev import config
from project.testdev.build_x52_server_image.mgr.env_mgr import bk_env_mgr
from project.testdev.build_x52_server_image.mgr.p4_2002_mgr import P4COM2002
from project.testdev.build_x52_server_image.mgr.p4_2004_mgr import P4COM2004


@advance.stage(stage="准备")
def prepare(**kwargs):
    """
    启动前准备
    """
    # 检查磁盘空间是否满足执行流水线
    advance.check_disk_size(threshold=5)
    # 如果branch是分支，那么branch是带 . 的分支，例如2.5.2，branch_name是带 _ 的分支，例如2_5_2
    branch = kwargs.get("branch_name")
    if branch == "trunk":
        branch_name = branch
    elif "." in branch:
        branch_name = branch.replace(".", "_")
        if "v" in branch:
            branch_name = branch_name.replace("v", "")
            branch = branch.replace("v", "")
    elif "_" in branch:
        branch_name = branch
        branch = branch.replace("_", ".")
        if "v" in branch:
            branch_name = branch_name.replace("v", "")
            branch = branch.replace("v", "")
    else:
        log.error("The branch {} is error".format(branch))
        exit(-1)
    env.set({"branch": branch})
    env.set({"branch_name": branch_name})
    # 停止服务
    cmd.run_shell(cmds=["killall svc_launch"])
    path_mgr.rm("/data/x52Source/x52")
    path_mgr.rm("/data/landun/workspace/x52_build_image")
    # 挂载目录
    path_mgr.mkdir("/data/mockp4/x52/")
    ret = cmd.run_shell(
        cmds=[
            "umount /data/mockp4/x52",
        ]
    )
    if ret[0] != 0:
        log.error("umount /data/mockp4/x52 failed")
        # exit(-1)

    ret = cmd.run_shell(cmds=["mount.cifs -o username=age,password=111,dir_mode=0777,file_mode=0777 //172.17.100.6/release/x52 /data/mockp4/x52"])
    if ret[0] != 0:
        log.error("mount.cifs failed")
        exit(-1)

    # 创建拉取代码的路径
    file_path = "/data/workspace/x52/x52server/exe/bin11/"
    path_mgr.mkdir(file_path)


# def get_linux_bin(**kwargs):
#     """
#     获取Linux_bin
#     """
#     resource_branch = env.get("branch")
#     resource_version = kwargs.get("resource_version")
#     code_version = kwargs.get("code_version")
#     base_path = "/data/mockp4/x52/"
#     if resource_branch == "trunk":
#         path = os.path.join(base_path, "Vacation_binary")
#     else:
#         src_binary_path = "branches_binary/version_{}_binary".format(resource_branch)
#         path = os.path.join(base_path, src_binary_path)
#     files = os.listdir(path)
#     files.sort(reverse=True)
#     print(files)
#     for file in files:
#         arr = file.split("_")
#         print(arr)
#         if len(arr) != 4:
#             continue
#         if resource_version != "HEAD":
#             # 根据p4号寻找
#             if int(arr[1]) <= int(resource_version):
#                 src_path = os.path.join(path, file)
#                 dst_path = "/data/workspace/x52/x52server/exe/bin11"
#                 print(src_path, dst_path)
#                 os.system("cp -rf {} {}".format(src_path, dst_path))
#                 break
#         elif code_version != "HEAD":
#             # 根据svn号寻找
#             if int(arr[3]) <= int(code_version):
#                 src_path = os.path.join(path, file)
#                 dst_path = "/data/workspace/x52/x52server/exe/bin11"
#                 print(src_path, dst_path)
#                 os.system("cp -rf {} {}".format(src_path, dst_path))
#                 break
#         else:
#             # 获取最新
#             src_path = os.path.join(path, file)
#             dst_path = "/data/workspace/x52/x52server/exe/bin11"
#             print(src_path, dst_path)
#             os.system("cp -rf {} {}".format(src_path, dst_path))
#             break


@advance.stage("从FTP下载linux_bin")
def get_linux_bin_from_ftp(**kwargs):
    branch_name = bk_env_mgr.get_branch_name()
    dot_branch_name = branch_name.replace("_", ".").replace("v", "")
    if branch_name == "trunk":
        dot_branch_name = "trunc"
    ftp = FtpMgr(ip="**************", port=21, username="administrator", password="Admin123")
    zips = ftp.dirs(path=common.join_url("/x52/server", dot_branch_name))
    zips.sort(reverse=True)
    for zip in zips:
        log.info(f"zip: {zip}")
    # 每次下载最新的
    ftp.download_file(
        src=common.join_url("/x52/server", dot_branch_name, zips[0]),
        dst=os.path.join("/data/workspace/x52/x52server/exe/bin11", zips[0]),
    )


@advance.stage("解压Linux_bin")
def tar_linux_bin(**kwargs):
    """
    解压Linux_bin
    """
    workdir = "/data/workspace/x52/x52server/exe/bin11"
    path_mgr.mkdir(workdir)
    cmd.run_shell(
        cmds=[
            "tar -zxvf *.tar.gz",
        ],
        workdir=workdir,
    )
    cmd.run_shell(cmds=["rm *.tar.gz"], workdir=workdir)


@advance.stage("同步p4 2002")
def sync_p4_2002(**kwargs):
    """
    拉取p4资源
    """
    branch = env.get("branch_name")
    p4_com_2002_mgr = P4COM2002(branch=branch)
    changelist = p4_com_2002_mgr.get_latest_changes()
    p4_com_2002_mgr.sync_all(changelist=changelist, force=True)


@advance.stage("同步p4 2004")
def sync_p4_2004(**kwargs):
    """
    获取p4资源
    """
    branch = env.get("branch")
    p4_com_2004_mgr = P4COM2004(branch=branch)
    changelist = p4_com_2004_mgr.get_latest_changes()
    p4_com_2004_mgr.sync_all(changelist=changelist, force=True)


@advance.stage("dump mysql")
def dump_mysql(**kwargs):
    """
    dump mysql
    """
    branch = env.get("branch_name")
    db_dump = "/data/workspace/x52/db_dump"
    sql_file_name = os.listdir(path=db_dump)[-1]
    if branch != "trunk":
        temp = "x52_dump_{}.sql".format(branch)
        if temp in os.listdir(path=db_dump):
            sql_file_name = temp
    cmd.run_shell(cmds=["cp db_dump/{} mysql/x52_dump.sql".format(sql_file_name), "ls -l ./mysql"], workdir="/data/workspace/x52")


def __get_image_tag(**kwargs):
    """
    获取镜像打包tag名称
    """
    branch_name = env.get("branch_name")
    tag = branch_name
    if branch_name != "trunk":
        tag = "v{}".format(branch_name)
    return tag


@advance.stage("构建x52 mysql镜像")
def build_x52_mysql_image(**kwargs):
    """
    构建mysql镜像
    """
    workdir = "/data/workspace/x52/mysql"
    docker = Docker(**config.REGISTRY_TAC_CONFIG)
    repository = "registry.h3d.com.cn/x52/mysql"
    tag = __get_image_tag()
    docker.build(workdir=workdir, file="Dockerfile", repository=repository, tag=tag)


@advance.stage("构建x52 redis镜像")
def build_x52_redis_image(**kwargs):
    """
    构建redis镜像打包
    """
    workdir = "/data/workspace/x52/redis"
    docker = Docker(**config.REGISTRY_TAC_CONFIG)
    repository = "registry.h3d.com.cn/x52/redis"
    tag = __get_image_tag()
    docker.build(workdir=workdir, file="Dockerfile", repository=repository, tag=tag)


@advance.stage("构建x52 server镜像")
def build_x52_server_image(**kwargs):
    """
    构建server镜像打包
    """
    workdir = "/data/workspace/x52/x52server"
    docker = Docker(**config.REGISTRY_TAC_CONFIG)
    repository = "registry.h3d.com.cn/x52/server"
    tag = __get_image_tag()
    docker.build(workdir=workdir, file="Dockerfile", repository=repository, tag=tag)


def on_success(**kwargs):
    branch = kwargs.get("branch")
    msg = "\n**分支:** {}".format(branch)
    wechat.send_unicast_post_success(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    branch = kwargs.get("branch")
    msg = "\n**分支:** {}".format(branch)
    hint = env.get("wechat_hint")
    if hint is None:
        msg += hint
    wechat.send_unicast_post_failure(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    branch = kwargs.get("branch")
    msg = "\n**分支:** {}".format(branch)
    wechat.send_unicast_post_canceled(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_canceled()
