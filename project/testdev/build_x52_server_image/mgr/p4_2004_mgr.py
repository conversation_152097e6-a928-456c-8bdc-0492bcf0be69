# coding=utf-8
from frame import *
from project.testdev import config


class P4COM2004:
    def __init__(self, branch: str):
        self.__branch = branch
        client = "testdev_build_x52_server_{}_{}".format(self.__branch.replace("/", "_"), common.get_host_ip())
        self.p4 = P4Client(
            host=config.P4_2004_CONFIG.get("host"),
            username=config.P4_2004_CONFIG.get("username"),
            password=config.P4_2004_CONFIG.get("password"),
            client=client,
        )
        if branch == "trunk":
            views = [
                f"//H3D_X52_res/QQX5-2_Exe/Vacation/resources/config/... //{client}/exe/resources/config/...",
                f"//H3D_X52_res/QQX5-2_Exe/Vacation/resources/level/... //{client}/exe/resources/level/...",
                f"//H3D_X52_res/QQX5-2_Exe/Vacation/resources/server/... //{client}/exe/resources/server/...",
                f"//H3D_X52_res/QQX5-2_Exe/Vacation/bin/%%1.sh //{client}/exe/bin11/%%1.sh",
            ]
        else:
            views = [
                f"//H3D_X52_res/QQX5-2_Exe/Branchs/version_{branch}/resources/config/... //{client}/exe/resources/config/...",
                f"//H3D_X52_res/QQX5-2_Exe/Branchs/version_{branch}/resources/level/... //{client}/exe/resources/level/...",
                f"//H3D_X52_res/QQX5-2_Exe/Branchs/version_{branch}/bin/%%1.sh //{client}/exe/bin11/%%1.sh",
            ]
        self.p4.set_view(views=views)
        root = "/data/workspace/x52/x52server"
        self.p4.set_root(root)

    def get_latest_changes(self):
        """
        获取资源分支的最新changelist
        """
        if self.__branch == "trunk":
            ret = self.p4.get_latest_changes("//H3D_X52_res/QQX5-2_Exe/Vacation/...".format(self.__branch))
        else:
            ret = self.p4.get_latest_changes("//H3D_X52_res/QQX5-2_Exe/Branchs/version_{}/...".format(self.__branch))
        try:
            changelist = ret.get("change")
        except Exception as e:
            log.error(e)
            changelist = "head"
        return changelist

    def sync_all(self, changelist: str, force: bool):
        """
        拉取p4
        """
        self.p4.sync_all(changelist=changelist, force=force)
