# coding=utf-8
import os.path

from frame import *
from project.testdev import config


class ServerMgr:
    def __init__(self):
        self.workdir = env.pipeline.workspace()

    def download_ftp_server_package(self, server_package: str):
        """
        下载安装包
        """
        ftp_paths = [
            "/version_test/server_pack/online",
            "/version_test/server_pack/release",
            "/version_test/server_pack/resource",
            "/version_test/server_pack/branch",
            "/version_test/server_pack/trunk",
            "/version_test/server_pack/special_branch",
        ]
        ftp = FtpMgr(**config.FTP_CONFIG_150_30)
        for server_package_path in ftp_paths:
            if ftp.exists_file(path=os.path.join(server_package_path, server_package)):
                src = os.path.join(server_package_path, server_package)
                dst = os.path.join(self.workdir, "x5_mobile/mobile_dancer/trunk/exe/temp_server_pack.zip")
                ftp.download_file(src=src, dst=dst, process_bar=True)
                return
        raise PyframeException(f"未在FTP上找到服务器包: {server_package}")

    def unzip_server_package(self):
        """
        解压服务器包
        """
        src = os.path.join(self.workdir, "x5_mobile/mobile_dancer/trunk/exe/temp_server_pack.zip")
        dst = os.path.join(self.workdir, "x5_mobile/mobile_dancer/trunk/exe")
        tar.decompress(src=src, dst=dst)


server_mgr = ServerMgr()
