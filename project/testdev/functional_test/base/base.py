# coding=utf-8

import abc


class FunctionalTestBase(metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def prepare(self, **kwargs):
        pass

    @abc.abstractmethod
    def sync_code(self, **kwargs):
        pass

    @abc.abstractmethod
    def build(self, **kwargs):
        pass

    @abc.abstractmethod
    def modify_config(self, **kwargs):
        pass

    @abc.abstractmethod
    def run_testcases(self, **kwargs):
        pass

    @abc.abstractmethod
    def create_report(self, **kwargs):
        pass
