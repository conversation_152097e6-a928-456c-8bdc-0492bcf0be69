# coding=utf-8
from frame import *
from project.testdev import config
from project.testdev.functional_test.x51 import WORKSPACE

activity_configs = {
    "eightsound": [
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/概率白盒测试/测试框架配置/...",
        # "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/概率白盒测试/待测概率配置/...",
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/八音抽奖/客户端配置/csv_resource_configs/weekly_configs/holiday_setting/...",
    ],
    "probability": [
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/概率白盒测试/测试框架配置/...",
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/概率白盒测试/待测概率配置/...",
    ],
    "login": [
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/登录正确性测试/测试配置/test_config/...",
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/登录正确性测试/测试配置/check_config/...",
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/登录正确性测试/单服务器/trunc/...",
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/登录正确性测试/测试配置/frame_config/...",
    ],
}


# 将activity_configs 提出来，作为参数传入，将方法提取出来
class P4COM2003:
    def __init__(self, branch: str, activity: str):
        self.__branch = branch
        self.__activity = activity
        self.__client = "testdev_activity_test_{}_{}".format(self.__branch.replace("/", "_"), common.get_host_ip())
        self.p4 = P4Client(
            host=config.P4_2003_CONFIG.get("host"),
            username=config.P4_2003_CONFIG.get("username"),
            password=config.P4_2003_CONFIG.get("password"),
            client=self.__client,
        )
        self.p4.set_encoding(encoding="GBK")
        root = WORKSPACE
        self.p4.set_root(root)
        self.p4.set_options(allwrite=True)
        self.__set_view(activity=activity)

    def __set_view(self, activity: str):
        configs = activity_configs[activity]
        views = []
        for c in configs:
            views.append("{}  //{}/{}\n".format(c, self.__client, c.lstrip("//")))
        server_views = [
            # f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/server/config/...  //{self.__client}/H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/server/config/...\n",
            # f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/level/inventory/global_cbd/...  //{self.__client}/H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/level/inventory/global_cbd/...\n",
        ]
        views.extend(server_views)
        self.p4.set_view(views=views)

    def sync_all(self):
        self.p4.sync_all(changelist="head", force=True)
