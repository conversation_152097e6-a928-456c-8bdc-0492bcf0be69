# coding=utf-8
from frame import *

from project.testdev.functional_test.x51 import WORKSPACE


class SvnMgr:
    def __init__(self, branch: str):
        self.__branch = branch

    def checkout_code(self):
        ret = cmd.run_shell(
            cmds=[
                f"svn checkout --force --quiet http://172.17.100.22/svn/starx52/{self.__branch}@HEAD ./{self.__branch} --username ji<PERSON><PERSON><PERSON><PERSON> --password 5n8rwi",
            ],
            workdir=WORKSPACE,
        )
        if ret[0] != 0:
            log.error("checkout_code error")
            exit(-1)
        ret = cmd.run_shell(cmds=[f"svn patch whitebox/projs/x51/proj.patch {self.__branch}"], workdir=WORKSPACE)
        if ret[0] != 0:
            log.error("checkout_code error")
            exit(-1)
