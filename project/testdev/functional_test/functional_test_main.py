# coding-utf-8
from operator import methodcaller

from frame import *
from project.testdev import config
from project.testdev.functional_test.x51.anniversary.anniversary import Anniversary
from project.testdev.functional_test.x51.eightsound.eightsound import Eightsound
from project.testdev.functional_test.x51.login.login import Login
from project.testdev.functional_test.x51.probability.probability import Probability
from project.testdev.functional_test.x52.draw_lottery.draw_lottery import DrawLottery
from project.testdev.functional_test.x52.magic_tea.magic_tea import MagicTea
from project.testdev import config
from project.testdev.functional_test_controller.mgr.machine_mgr import machine_mgr
from project.testdev.functional_test_controller.functional_test_controller_main import get_available_server_client


def x51(**kwargs):
    activity = kwargs.get("activity")
    job = kwargs.get("job")
    activities = {
        "anniversary": lambda: Anniversary(),
        "probability": lambda: Probability(),
        "eightsound": lambda: Eightsound(),
        "login": lambda: Login(),
    }
    if activities.get(activity) is None:
        log.error(f"error testcase name: {activity}")
        raise PyframeException(f"不存在的测试用例: {activity}")
    case = activities.get(activity)()
    methodcaller(job, **kwargs)(case)


def x52(**kwargs):
    activity = kwargs.get("activity")
    job = kwargs.get("job")
    activities = {
        "magic_tea": lambda: MagicTea(),
        "draw_lottery": lambda: DrawLottery(),
    }
    if activities.get(activity) is None:
        log.error(f"error testcase name: {activity}")
        raise PyframeException(f"不存在的测试用例: {activity}")
    case = activities.get(activity)()
    methodcaller(job, **kwargs)(case)


# 调度：寻找空闲机器，执行流水线
def pipeline_dispatch(**kwargs):
    ret = get_available_server_client()
    server_ip = ret[0]
    client_ip = ret[1]
    env.set({"SERVER_IP": server_ip, "CLIENT_IP": client_ip})
    server_ip = env.get("SERVER_IP")
    client_ip = env.get("CLIENT_IP")
    log.info(f"'server_ip:{server_ip} client_ip:{client_ip}")


def on_success(**kwargs):
    project = kwargs.get("project")
    branch = kwargs.get("branch")
    server_ip = env.get("SERVER_IP")
    client_ip = env.get("CLIENT_IP")
    if server_ip is not None and client_ip is not None:
        machine_mgr.return_machine(server_ip=server_ip, client_ip=client_ip)
    msg = f"**项目**: {project}\n**分支**: {branch}\n**服务端**: {server_ip}\n**客户端**: {client_ip}"
    wechat.send_unicast_post_success(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_success()
    # 归还机器
    # 记录历史
    #     machine_mgr.return_machine(server_ip=server_ip, client_ip=client_ip)
    #     task_history_mgr.update_record_on_finish(uid=env.get("UUID"), status=2)


def on_failure(**kwargs):
    project = kwargs.get("project")
    branch = kwargs.get("branch")
    server_ip = env.get("SERVER_IP")
    client_ip = env.get("CLIENT_IP")
    if server_ip is not None and client_ip is not None:
        machine_mgr.return_machine(server_ip=server_ip, client_ip=client_ip)
    msg = f"**项目**: {project}\n**分支**: {branch}\n**服务端**: {server_ip}\n**客户端**: {client_ip}"
    wechat.send_unicast_post_failure(user_list=config.TESTDEV_DEVELOPER, content=msg, rescue=False)
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    project = kwargs.get("project")
    branch = kwargs.get("branch")
    server_ip = env.get("SERVER_IP")
    client_ip = env.get("CLIENT_IP")
    if server_ip is not None and client_ip is not None:
        machine_mgr.return_machine(server_ip=server_ip, client_ip=client_ip)
    msg = f"**项目**: {project}\n**分支**: {branch}\n**服务端**: {server_ip}\n**客户端**: {client_ip}"
    wechat.send_unicast_post_canceled(user_list=config.TESTDEV_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_canceled()


#
# def start_test(**kwargs):
#     """
#     开始测试，主要用来发通知
#     """
#     server_ip = env.get("SERVER_IP")
#     client_ip = env.get("CLIENT_IP")
#     msg = "**服务端**：{}\n**客户端**：{}".format(server_ip, client_ip)
#     wechat.send_unicast_on_start(user_list=["<EMAIL>"], content=msg)
#     task_history_mgr.update_record_on_start(uid=env.get("UUID"), url=env.pipeline.get_build_url())
#
# def upload_report(**kwargs):
#     """
#     上传测试报告
#     """
#     project = kwargs.get("project")
#     log.info("{} 上传报告".format(project))
#     if project == "x51":
#         x51_functional_test.upload_report()
#     elif project == "x52":
#         x52_functional_test.upload_report()
