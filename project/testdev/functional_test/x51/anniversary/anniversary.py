# coding=utf-8
from frame import *
from project.testdev.functional_test.base.base import *
from project.testdev.functional_test.x51.anniversary.mgr.config_mgr import ConfigMgr
from project.testdev.functional_test.x51.anniversary.mgr.gitlab_mgr import GitlabMgr
from project.testdev.functional_test.x51.anniversary.mgr.p4_com_2003 import P4COM2003
from project.testdev.functional_test.x51.anniversary.mgr.report_mgr import ReportMgr
from project.testdev.functional_test.x51.anniversary.mgr.svn_mgr import SvnMgr

WORKSPACE = r"D:/workspace/x51_whitebox"


class Anniversary(FunctionalTestBase):
    def __clone_robotexecutor(self, **kwargs):
        """
        从gitlab拉取robotexecutor，(测试代码)
        """
        g = GitlabMgr()
        g.git_clone_robotexecutor()

    def __sync_p4_config(self, **kwargs):
        """
        拉取p4(配置)
        """
        branch = kwargs.get("branch")
        activity = kwargs.get("activity")
        p4_2003 = P4COM2003(branch=branch, activity=activity)
        p4_2003.sync_all()

    def __checkout_svn(self, **kwargs):
        """
        拉取svn(项目代码)
        """
        svn_branch = env.get("svn_branch")
        svn = SvnMgr(branch=svn_branch)
        svn.checkout_code()

    def __build_client(self, **kwargs):
        """
        编译
        """
        svn_branch = env.get("svn_branch")
        build_path = os.path.join(WORKSPACE, "whitebox/.build")
        path_mgr.mkdir(build_path)
        ret = cmd.run_shell(
            cmds=[
                f'cmake -G "Visual Studio 15 2017" -A Win32 -DPROJ=x51 -DFRAME_PROJ=functional -DPROJ_SERVER_DIR_NAME={svn_branch} -DDEVELOPMENT=off -DCMAKE_BUILD_TYPE=RelWithDebInfo ..',
            ],
            workdir=build_path,
        )
        if ret[0] != 0:
            log.error("build_client error")
            exit(-1)
        ret = cmd.run_shell(cmds=[f"cmake --build . --target functional_test --config RelWithDebInfo"], workdir=build_path)
        if ret[0] != 0:
            log.error("build_client error")
            exit(-1)

    def prepare(self, **kwargs):
        # 检查磁盘空间是否满足执行流水线
        advance.check_disk_size(threshold=5)
        # 清理工作空间
        is_clean = env.get("is_clean")
        if common.str2bool(is_clean):
            path_mgr.rm(WORKSPACE)
        path_mgr.mkdir(WORKSPACE)
        branch = kwargs.get("branch")
        if branch != "trunc":
            y = branch.split("/")[0]
            tag = branch.split("_")[-2]
            server_branch = f"{y}/{tag}"
            svn_branch = server_branch.replace("_", "es")
        else:
            svn_branch = branch
            tag = branch
        env.set({"svn_branch": svn_branch, "server_tag": tag})

    def sync_code(self, **kwargs):
        self.__clone_robotexecutor(**kwargs)
        self.__sync_p4_config(**kwargs)
        self.__checkout_svn(**kwargs)

    def build(self, **kwargs):
        self.__build_client(**kwargs)

    def modify_config(self, **kwargs):
        branch = kwargs.get("branch")
        server_ip = env.get("SERVER_IP")
        summary_play_round = env.get("SUMMARY_PLAY_ROUND")
        config_mgr = ConfigMgr(branch=branch, server_ip=server_ip, summary_play_round=summary_play_round)
        config_mgr.copy_config()
        config_mgr.modify_config()

    def run_testcases(self, **kwargs):
        build_path = os.path.join(WORKSPACE, "whitebox/.build")
        ret = cmd.run_shell(cmds=["functional_test.exe -c config\config.yml"], workdir=build_path)
        if ret[0] != 0:
            log.error("run_testcases error")
            exit(-1)

    def create_report(self, **kwargs):
        report_mgr = ReportMgr()
        report_mgr.create_test_report()
        report_mgr.compress_report()
