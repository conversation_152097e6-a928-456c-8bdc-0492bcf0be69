# coding=utf-8
from frame import *
from project.testdev import config
from project.testdev.functional_test.x51 import WORKSPACE

activity_configs = {
    "anniversary": [
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/周年庆/服务器配置/server_env_config/...",
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/周年庆/客户端配置/frame_config/...",
        "//H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/周年庆/客户端配置/resource/...",
    ]
}


class P4COM2003:
    def __init__(self, branch: str, activity: str):
        self.__branch = branch
        self.__activity = activity
        self.__client = "testdev_activity_test_{}_{}".format(self.__branch.replace("/", "_"), common.get_host_ip())
        self.p4 = P4Client(
            host=config.P4_2003_CONFIG.get("host"),
            username=config.P4_2003_CONFIG.get("username"),
            password=config.P4_2003_CONFIG.get("password"),
            client=self.__client,
        )
        self.p4.set_encoding(encoding="GBK")
        root = WORKSPACE
        self.p4.set_root(root)
        self.p4.set_options(allwrite=True)
        self.__set_view(activity=activity)

    def __set_view(self, activity: str):
        configs = activity_configs[activity]
        views = []
        for c in configs:
            views.append("{}  //{}/{}\n".format(c, self.__client, c.lstrip("//")))
        server_views = [
            f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/server/config/...  //{self.__client}/H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/server/config/...\n",
            f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/level/inventory/global_cbd/...  //{self.__client}/H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/level/inventory/global_cbd/...\n",
        ]
        views.extend(server_views)
        self.p4.set_view(views=views)

    def sync_all(self):
        self.p4.sync_all(changelist="head", force=True)
