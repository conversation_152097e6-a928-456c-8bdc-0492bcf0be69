# coding=utf-8
from frame import *
from project.testdev.functional_test.x51 import WORKSPACE


class ReportMgr:
    def __init__(self):
        pass

    def create_test_report(self):
        path_mgr.mkdir(os.path.join(WORKSPACE, "whitebox/.build/report/testreport"))
        ret = cmd.run_shell(
            cmds=[
                "python3 whitebox/localscripts/GTestResultParser.py whitebox/.build/report/gtest whitebox/.build/report/testreport whitebox/localscripts/case_name_mapping.xml svnversion.log"
            ],
            workdir=WORKSPACE,
        )
        if ret[0] != 0:
            log.error("create_test_report error")
            exit(-1)

    def compress_report(self):
        output_directory = os.path.join(WORKSPACE, "whitebox/.build/report/testreport")
        # 获取config配置的压缩目录
        need_compress_config = os.path.join(WORKSPACE, "whitebox/.build/config")
        # 进行压缩
        tar.compress(need_compress_config, f"{output_directory}/config.zip")
        # 获取resource配置的压缩目录
        need_compress_config = os.path.join(WORKSPACE, "whitebox/.build/resource")
        # 进行压缩
        tar.compress(need_compress_config, f"{output_directory}/resource.zip")
        # 获取resource配置的压缩目录
        need_compress_config = os.path.join(WORKSPACE, "whitebox/.build/report/gtest")
        # 进行压缩
        tar.compress(need_compress_config, f"{output_directory}/gtest.zip")
        # 获取resource配置的压缩目录
        need_compress_config = os.path.join(WORKSPACE, "whitebox/.build/report/test_cases")
        # 进行压缩
        tar.compress(need_compress_config, f"{output_directory}/test_cases.zip")

    def upload_report(self):
        pass
