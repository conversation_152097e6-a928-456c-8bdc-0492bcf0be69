# coding=utf-8
from frame import *
from project.testdev.functional_test.x51 import WORKSPACE


class ReportMgr:
    def __init__(self):
        pass

    def create_test_report(self):
        path_mgr.mkdir(os.path.join(WORKSPACE, "whitebox/.build/report/testreport"))
        ret = cmd.run_shell(
            cmds=[
                r"python whitebox\localscripts\GTestResultParser.py whitebox\.build\report\gtest whitebox\.build\report\testreport whitebox\localscripts\case_name_mapping.xml svnversion.log"
            ],
            workdir=WORKSPACE,
        )
        if ret[0] != 0:
            log.error("create_test_report error")
            exit(-1)

    def compress_report(self):
        output_directory = os.path.join(WORKSPACE, "whitebox/.build/report/testreport")
        # 获取需要压缩的配置目录
        need_compress_config = f"{WORKSPACE}/H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/登录正确性测试/测试配置/test_config"
        # 进行压缩
        tar.compress(need_compress_config, f"{need_compress_config}/test_config.zip")

        need_compress_config = os.path.join(WORKSPACE, "whitebox/.build/report/gtest")
        # 进行压缩
        tar.compress(need_compress_config, f"{output_directory}/gtest.zip")

    def upload_report(self):
        pass
