# coding=utf-8
from frame import *
from project.testdev.functional_test.x51 import WORKSPACE


class ConfigMgr:
    def __init__(self, branch: str, server_ip: str, target_lottery_name: str, robot_cnt: str, target_cnt: str, target_id: str):
        self.__branch = branch
        self.server_ip = server_ip
        self.target_lottery_name = target_lottery_name
        self.robot_cnt = robot_cnt
        self.target_cnt = target_cnt
        self.target_id = target_id
        self.test_config_path = "H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/概率白盒测试/待测概率配置"
        self.frame_config_path = "H3D_X51_res/X51_SourceBase/doc/程序/游戏侧/功能白盒/概率白盒测试/测试框架配置"

    def copy_config(self):
        # zones_path = os.path.join(WORKSPACE, "whitebox/.build/resource/zones")
        # testcase_config_path = os.path.join(WORKSPACE, "whitebox/.build/resource/testcase_config")
        # config_path = os.path.join(WORKSPACE, "whitebox/.build/resource/")
        path_mgr.mkdir(os.path.join(WORKSPACE, "whitebox/.build/config"))
        path_mgr.mkdir(os.path.join(WORKSPACE, "whitebox/.build/resource"))
        path_mgr.mkdir(os.path.join(WORKSPACE, "whitebox/.build/resource/zones"))
        # xcopy两个目录必须加双引号
        ret = cmd.run_shell(cmds=[f'xcopy /e/y/i/f "{self.frame_config_path}" "whitebox/.build"'], workdir=WORKSPACE)
        if ret[0] != 0:
            log.error("exec xcopy error")
            exit(-1)

    def check_zones(self):
        # 检查zones下有没有对应ip的文件夹
        zones_path = r"{}\whitebox\.build\resource\zones".format(WORKSPACE)
        server_ip = env.get("SERVER_IP")
        ret = cmd.run_shell(cmds=[f"python3 ../../localscripts/X51_check_zones_server_config.py {zones_path} {server_ip}"], workdir=WORKSPACE)
        if ret[0] != 0:
            log.error("not exist zones-server!!!")
            exit(-1)

    def modify_config(self):
        build_path = os.path.join(WORKSPACE, "whitebox/.build")
        server_tag = env.get("server_tag")
        server_ip = self.server_ip
        ret = cmd.run_shell(
            cmds=[rf"python3 .\whitebox\localscripts\ConfigTagUpdater.py {server_tag} {build_path}/config/config.yml {server_ip}"], workdir=WORKSPACE
        )
        if ret[0] != 0:
            log.error("exec ConfigTagUpdater.py error")
            exit(-1)
        ret = cmd.run_shell(
            cmds=[
                rf"python3 .\whitebox\localscripts\X51_LotteryProbability_CaseFilterAndCopy.py "
                rf"{self.target_lottery_name} {self.robot_cnt} {self.target_cnt} {self.target_id} {self.test_config_path}"
            ],
            workdir=WORKSPACE,
        )
        if ret[0] != 0:
            log.error("exec X51_LotteryProbability_CaseFilterAndCopy.py error")
            exit(-1)
        ret = cmd.run_shell(
            cmds=[
                rf"python3 .\whitebox\localscripts\X51_LotteryProbability_ConfigDateChanger_csv.py all {self.target_id} {self.target_lottery_name}"
            ],
            workdir=WORKSPACE,
        )
        if ret[0] != 0:
            log.error("exec X51_LotteryProbability_ConfigDateChanger_csv.py error")
            exit(-1)
