# coding=utf-8
from pathlib import Path

from frame import *
from project.testdev.functional_test.x51 import WORKSPACE


class GitlabMgr:
    def __init__(self):
        pass

    def git_clone_robotexecutor(self):
        if Path(os.path.join(WORKSPACE, "whitebox")).exists():
            ret = cmd.run_shell(cmds=["git clean -xdf", "git reset --hard", "git pull"], workdir=os.path.join(WORKSPACE, "whitebox"))
            if ret[0] != 0:
                log.error("git pull error")
                exit(-1)
        else:
            ret = cmd.run_shell(
                cmds=[
                    "git clone -b x51functioncases https://gitlab.h3d.com.cn/whitebox/robot_v4/robotexecutor.git",
                ],
                workdir=WORKSPACE,
            )
            if ret[0] != 0:
                log.error("git clone error")
                exit(-1)
            path_mgr.move(src=os.path.join(WOR<PERSON><PERSON><PERSON>, "robotexecutor"), dst=os.path.join(WORKSPACE, "whitebox"))
