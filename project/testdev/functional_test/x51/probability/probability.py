# coding=utf-8
from frame import *
from project.testdev.functional_test.base.base import *
from project.testdev.functional_test.x51.probability.mgr.config_mgr import ConfigMgr
from project.testdev.functional_test.x51.probability.mgr.gitlab_mgr import GitlabMgr
from project.testdev.functional_test.x51.probability.mgr.p4_com_2003 import P4COM2003
from project.testdev.functional_test.x51.probability.mgr.report_mgr import ReportMgr
from project.testdev.functional_test.base.yaml_handler import YamlHelper

WORKSPACE = r"D:/workspace/x51_whitebox"
yaml_path = r"D:\workspace\PipelineConfig"


class Probability(FunctionalTestBase):
    def __clone_robotexecutor(self, **kwargs):
        """
        从gitlab拉取robotexecutor，(测试代码)
        """
        g = GitlabMgr()
        g.git_clone_robotexecutor()

    def __sync_p4_config(self, **kwargs):
        """
        拉取p4(配置)
        """
        branch = kwargs.get("branch")
        activity = kwargs.get("activity")
        p4_2003 = P4COM2003(branch=branch, activity=activity)
        p4_2003.sync_all()

    def __checkout_svn(self, **kwargs):
        """
        拉取svn(项目代码)
        """
        svn_branch = env.get("svn_branch")
        svn = SvnMgr(branch=svn_branch)
        svn.checkout_code()

    def __build_client(self, **kwargs):
        """
        编译
        """
        svn_branch = env.get("svn_branch")
        build_path = os.path.join(WORKSPACE, "whitebox/.build")
        path_mgr.mkdir(build_path)
        ret = cmd.run_shell(
            cmds=[
                f'cmake -G "Visual Studio 15 2017" -A Win32 -DPROJ=x51 -DFRAME_PROJ=functional -DPROJ_SERVER_DIR_NAME={svn_branch} -DDEVELOPMENT=off -DCMAKE_BUILD_TYPE=RelWithDebInfo ..',
            ],
            workdir=build_path,
        )
        if ret[0] != 0:
            log.error("build_client error")
            exit(-1)
        # ret = cmd.run_shell(
        #     cmds=[
        #         f"cmake --build . --target functional_test --config RelWithDebInfo"
        #     ],
        #     workdir=build_path
        # )
        ret = cmd.run_shell(cmds=['BuildConsole.exe functional_test.sln /CFG="RelWithDebInfo|win32" /BUILD /MAXCPUS=40'], workdir=build_path)
        if ret[0] != 0:
            log.error("build_client error")
            exit(-1)

    # 从git上获取流水线变量配置文件，并且将读取到的变量设置成流水线变量
    def get_pipeline_var_from_git_set(self, **kwargs):
        if not path_mgr.exists(yaml_path):
            path_mgr.mkdir(yaml_path)

        ret = cmd.run_shell(
            cmds=[
                f"git clone ",  # 待定
            ],
            workdir=yaml_path,
        )
        if ret[0] != 0:
            log.error("git clone pipeline var error")
            exit(-1)

        self.handle_pipeline_config()

    def prepare(self, **kwargs):
        # 检查磁盘空间是否满足执行流水线
        advance.check_disk_size(threshold=5)
        # 清理工作空间
        is_clean = env.get("IS_CLEAN")
        if common.str2bool(is_clean):
            path_mgr.rm(WORKSPACE)
        path_mgr.mkdir(WORKSPACE)
        branch = kwargs.get("branch")
        if branch != "trunc":
            y = branch.split("/")[0]
            tag = branch.split("_")[-2]
            server_branch = f"{y}/{tag}"
            svn_branch = server_branch.replace("_", "es")
        else:
            svn_branch = branch
            tag = branch
        env.set({"svn_branch": svn_branch, "server_tag": tag})  # branches2022/6.0.6  # 6.0.6
        # 判断Windows机器的IncrediBuild是否启动
        # cmd.run_shell(
        #     cmds=["BuildTrayIcon"],
        #     workdir=WORKSPACE
        # )

    def sync_code(self, **kwargs):
        is_clean = env.get("IS_CLEAN")
        if common.str2bool(is_clean):
            log.info("About to pull git code")
            self.__clone_robotexecutor(**kwargs)
            log.info("About to pull p4 code")
            self.__sync_p4_config(**kwargs)
            log.info("About to pull svn code")
            self.__checkout_svn(**kwargs)
        else:
            log.info("About to pull p4 code")
            self.__sync_p4_config(**kwargs)
        # pull_code = env.get("PULL_CODE")
        # log.info(rf"PULL_CODE is {pull_code}")
        # if pull_code is None:
        #     return log.info("Code does not need to be pulled again")
        # if "git" in pull_code:
        #     self.__clone_robotexecutor(**kwargs)
        # if "p4" in pull_code:
        #     self.__sync_p4_config(**kwargs)
        # if "svn" in pull_code:
        #     self.__checkout_svn(**kwargs)

    def clean_code(self, **kwargs):
        ret = cmd.run_shell(cmds=["python3 .\whitebox\localscripts\X51_RunPreparation_RemoveUnusedActions.py default"], workdir=WORKSPACE)
        if ret[0] != 0:
            log.error("run_testcases error")
            exit(-1)

    def build(self, **kwargs):
        self.__build_client(**kwargs)

    def del_oldconfig(self, **kwargs):
        path_mgr.rm(os.path.join(WORKSPACE, "whitebox/.build/config"))
        path_mgr.rm(os.path.join(WORKSPACE, "whitebox/.build/resource"))
        path_mgr.rm(os.path.join(WORKSPACE, "whitebox/.build/report"))

    def modify_config(self, **kwargs):
        branch = kwargs.get("branch")
        server_ip = env.get("SERVER_IP")
        target_lottery_name = env.get("TARGET_LOTTERY_NAME")
        robot_cnt = env.get("ROBOT_CNT")
        target_cnt = env.get("TARGET_CNT")
        target_id = env.get("TARGET_ID")
        config_mgr = ConfigMgr(
            branch=branch,
            server_ip=server_ip,
            target_lottery_name=target_lottery_name,
            robot_cnt=robot_cnt,
            target_cnt=target_cnt,
            target_id=target_id,
        )
        config_mgr.copy_config()
        config_mgr.check_zones()
        config_mgr.modify_config()

    def run_testcases(self, **kwargs):
        build_path = os.path.join(WORKSPACE, "whitebox/.build")
        ret = cmd.run_shell(cmds=["functional_test.exe -c config\config.yml"], workdir=build_path)
        if ret[0] != 0:
            log.error("run_testcases error")
            exit(-1)

    def create_report(self, **kwargs):
        report_mgr = ReportMgr()
        report_mgr.create_test_report()
        report_mgr.compress_report()

    # 将前端传入的数据 数据库 --> 生成yaml文件 --> 从yaml文件中读取配置到流水线变量中
    def handle_pipeline_config(self):
        pipeline_config_path_name = f"{yaml_path}/ProbabilityPipelineConfig.yml"
        yaml_handler = YamlHelper(pipeline_config_path_name)
        content = yaml_handler.read_yaml()
        X51_SERVER_BRANCH = content["run_info"]["X51_SERVER_BRANCH"]
        X51_SERVER_TAG = content["run_info"]["X51_SERVER_TAG"]
        X51_BUILD_IMAGE = content["run_info"]["X51_BUILD_IMAGE"]
        NEW_RUN = content["run_info"]["NEW_RUN"]
        TARGET_LOTTERY_NAME = content["run_info"]["TARGET_LOTTERY_NAME"]
        TARGET_ID = content["run_info"]["TARGET_ID"]
        TARGET_CNT = content["run_info"]["TARGET_CNT"]
        ROBOT_CNT = content["run_info"]["ROBOT_CNT"]
        SERVER_IP = content["run_info"]["SERVER_IP"]
        CLIENT_IP = content["run_info"]["CLIENT_IP"]

        env.set(
            {
                "X51_SERVER_BRANCH": X51_SERVER_BRANCH,
                "X51_SERVER_TAG": X51_SERVER_TAG,
                "X51_BUILD_IMAGE": X51_BUILD_IMAGE,
                "NEW_RUN": NEW_RUN,
                "TARGET_LOTTERY_NAME": TARGET_LOTTERY_NAME,
                "TARGET_ID": TARGET_ID,
                "TARGET_CNT": TARGET_CNT,
                "ROBOT_CNT": ROBOT_CNT,
                "SERVER_IP": SERVER_IP,
                "CLIENT_IP": CLIENT_IP,
            }
        )

    def get_config_to_yaml(self, **kwargs):
        # 基本信息
        # 流水线url
        # 流水线名称
        # 运行信息
        # X51_SERVER_BRANCH，根据情况修改年份和版本号
        # X51_SERVER_TAG，需要测试镜像的Tag，需要和X51_SERVER_BRANCH保持一致
        # X51_BUILD_IMAGE，是否制作新镜像，保持false
        # NEW_RUN，是否是一个新的运行。若Branch修改或者更换了测试机，则需要选择TRUE。若确认测试机当前环境可以继续使用，则可以选择FALSE。
        # TARGET_LOTTERY_NAME，计划运行的抽奖名称。选择即可。
        # TARGET_ID，测试的活动ID，一般都有。
        # TARGET_CNT，计划测试的轮数，或者最大次数。
        # ROBOT_CNT，计划使用的机器人数量。
        # SERVER_IP，镜像服务器的IP，由调度系统提供
        # CLIENT_IP，测试机的IP，由调度系统提供
        if not path_mgr.exists(yaml_path):
            path_mgr.mkdir(yaml_path)

        yamlhandler = YamlHelper(f"{yaml_path}/ProbabilityPipelineConfig2.yml")
        PipelineUrl = env.pipeline.build_url()
        PipelineName = env.pipeline.pipeline_name()
        X51_SERVER_BRANCH = env.get("svn_branch")
        X51_SERVER_TAG = env.get("server_tag")
        X51_BUILD_IMAGE = env.get("X51_BUILD_IMAGE")
        NEW_RUN = env.get("NEW_RUN")
        TARGET_LOTTERY_NAME = env.get("TARGET_LOTTERY_NAME")
        TARGET_ID = env.get("TARGET_ID")
        TARGET_CNT = env.get("TARGET_CNT")
        ROBOT_CNT = env.get("ROBOT_CNT")
        SERVER_IP = env.get("SERVER_IP")
        CLIENT_IP = env.get("CLIENT_IP")
        content = {
            "basic_info": {"PipelineUrl": PipelineUrl, "PipelineName": PipelineName},
            "run_info": {
                "X51_SERVER_BRANCH": X51_SERVER_BRANCH,
                "X51_SERVER_TAG": X51_SERVER_TAG,
                "X51_BUILD_IMAGE": X51_BUILD_IMAGE,
                "NEW_RUN": NEW_RUN,
                "TARGET_LOTTERY_NAME": TARGET_LOTTERY_NAME,
                "TARGET_ID": TARGET_ID,
                "TARGET_CNT": TARGET_CNT,
                "ROBOT_CNT": ROBOT_CNT,
                "SERVER_IP": SERVER_IP,
                "CLIENT_IP": CLIENT_IP,
            },
        }
        yamlhandler.write_yaml(content)
