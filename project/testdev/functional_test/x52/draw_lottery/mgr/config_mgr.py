# coding=utf-8
import re

from frame import *
from project.testdev.functional_test.x52 import WORKSPACE


class ConfigMgr:
    def __init__(self, server_ip: str, branch_name: str, project_name: str):
        self.server_ip = server_ip
        self.branch_name = branch_name
        self.project_name = project_name

    def copy_config(self):
        workdir = os.path.join(WORKSPACE, self.project_name)
        log.info("拷贝测试用例代码")
        cmd.run_shell(
            cmds=[
                'xcopy /e/y/i/f "x52_gitlab_code/{}/Code/_for_bot" "x52_whitebox/{}/depot/products/Project_X52/_for_bot"'.format(
                    self.project_name, self.branch_name
                ),
            ],
            workdir=workdir,
        )
        cmd.run_shell(
            cmds=[
                'xcopy /e/y/i/f "x52_gitlab_code/{}/Config/test" "x52_whitebox/{}/exe/resources/config/test"'.format(
                    self.project_name, self.branch_name
                )
            ],
            workdir=workdir,
        )
        log.info("更新测试架构底层文件")
        cmd.run_shell(
            cmds=[
                rf'xcopy /e/y/i/f ".\x52_gitlab_code\Update\base_fixture.cpp" ".\x52_whitebox\{self.branch_name}\depot\products\Project_X52\_for_bot\server_logic_test_framework\fixture\base_fixture.cpp"',
            ],
            workdir=workdir,
        )
        cmd.run_shell(
            cmds=[
                rf'xcopy /e/y/i/f ".\x52_gitlab_code\Update\x5_dance_meet_player.cpp" ".\x52_whitebox\{self.branch_name}\depot\products\Project_X52\components\dance_meet_2021\core\x5_dance_meet_player.cpp"',
            ],
            workdir=workdir,
        )
        cmd.run_shell(
            cmds=[
                rf'xcopy /e/y/i/f ".\x52_gitlab_code\Update\x5_dance_meet_player.h" ".\x52_whitebox\{self.branch_name}\depot\products\Project_X52\components\dance_meet_2021\core\x5_dance_meet_player.h"',
            ],
            workdir=workdir,
        )

    def modify_config(self):
        # 更新env版本
        workdir = os.path.join(WORKSPACE, self.project_name)
        log.info("更新env版本")
        cmd.run_shell(
            cmds=[
                "python3 x52_gitlab_code/Script/X52_DockerEnvUpdater.py x52_whitebox/{}/exe/resources/config/test/remote_configs/.env {}".format(
                    self.branch_name, self.branch_name
                )
            ],
            workdir=workdir,
        )
        log.info("清理之前的报告")
        cmd.run_shell(
            cmds=[
                "cd x52_whitebox/{}/exe/bin".format(self.branch_name),
                "del /f /s /q wbox_test_result*.xml",
                "cd ../../depot/products/Project_X52/_for_bot/report",
                "del /f /s /Q *",
            ],
            workdir=workdir,
        )

    def update_server_ip(self):
        """
        修改test_config中的服务器IP
        D:\workspace\x52_whitebox\magic_tea\x52_whitebox\trunk\exe\resources\config\test\test_config.xml
        """
        log.info("修改连接的服务器IP")
        workdir = os.path.join(WORKSPACE, "{}/x52_whitebox/{}/exe/resources/config/test/test_config.xml".format(self.project_name, self.branch_name))
        file_path = workdir
        with open(file_path, "r+", encoding="utf8") as f:
            file_data = f.read()
            file_data = re.sub(r'host=.*?".*?"', f'host="{self.server_ip}"', file_data)
            file_data = re.sub(r'Gateway ip=.*?".*?"', f'Gateway ip="{self.server_ip}"', file_data)
        with open(file_path, "w+", encoding="utf8") as f:
            f.write(file_data)
        log.info(rf"当前连接的服务器IP被修改为{self.server_ip}")
