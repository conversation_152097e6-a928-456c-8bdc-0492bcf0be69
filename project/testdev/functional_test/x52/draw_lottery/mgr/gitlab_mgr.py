# coding=utf-8
from pathlib import Path

from frame import *
from project.testdev.functional_test.x52 import WORKSPACE


class GitlabMgr:
    def __init__(self):
        pass

    def git_clone_x52_gitlab_code(self, project_name: str):
        workdir = os.path.join(WORKSPACE, project_name)
        if Path(os.path.join(workdir, "x52_gitlab_code")).exists():
            cmd.run_shell(cmds=["git clean -xdf", "git reset --hard", "git pull"], workdir=os.path.join(workdir, "x52_gitlab_code"))
        else:
            cmd.run_shell(
                cmds=[
                    "git clone  https://gitlab.h3d.com.cn/test-dev/x52.git",
                ],
                workdir=workdir,
            )
            path_mgr.move(src=os.path.join(workdir, "x52"), dst=os.path.join(workdir, "x52_gitlab_code"))

    def git_clone_robotexecutor(self, project_name: str):
        workdir = os.path.join(WORKSPACE, project_name)
        if Path(os.path.join(workdir, "x51_gitlab")).exists():
            cmd.run_shell(cmds=["git clean -xdf", "git reset --hard", "git pull"], workdir=os.path.join(workdir, "x51_gitlab"))
        else:
            cmd.run_shell(
                cmds=[
                    "git clone -b x51functioncases https://gitlab.h3d.com.cn/whitebox/robot_v4/robotexecutor.git",
                ],
                workdir=workdir,
            )
            path_mgr.move(src=os.path.join(workdir, "robotexecutor"), dst=os.path.join(workdir, "x51_gitlab"))

    def server_git_clone_config(self, project_name: str):
        workdir = os.path.join("/root/x52_whitebox_test")
        if Path(workdir).exists():
            cmd.run_shell(cmds=["git clean -xdf", "git reset --hard", "git pull"], workdir=workdir)
        else:
            cmd.run_shell(
                cmds=[
                    "git clone  https://gitlab.h3d.com.cn/zhangtao02/x52_whitebox_test.git",
                ],
                workdir="/root",
            )
