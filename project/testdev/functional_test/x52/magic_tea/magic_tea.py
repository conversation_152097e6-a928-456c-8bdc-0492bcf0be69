# coding=utf-8
import os.path
import re
import shutil
from datetime import datetime

from frame import *
from project.testdev.functional_test.base.base import *
from project.testdev.functional_test.x52 import WORKSPACE
from project.testdev.functional_test.x52.magic_tea.mgr.gitlab_mgr import GitlabMgr
from project.testdev.functional_test.x52.magic_tea.mgr.p4_com_2002 import P4COM2002
from project.testdev.functional_test.x52.magic_tea.mgr.p4_com_2004 import P4COM2004


class MagicTea(FunctionalTestBase):
    def __get_ip_password(self) -> str:
        """
        根据服务器IP  对应登录密码
        """
        server_ip = env.get("SERVER_IP")
        ip_password = {
            "**************": "Centos8.4",
            "**************": "Centos8.4",
            "**************": "Centos8.4",
        }
        password = ip_password[server_ip]
        return password

    def __sync_p4_2002(self, branch: str, project_name: str):
        """
        从p4.com:2002拉取代码
        Args:
            branch: 默认trunc
            project_name: 项目名
        """
        log.info("Start from p4 Com:2002 pull code")
        p4_com_2002_mgr = P4COM2002(branch=branch, project_name=project_name)
        changelist = p4_com_2002_mgr.get_latest_changes()
        p4_com_2002_mgr.sync_all(changelist=changelist, force=True)
        log.info("From p4 Com:2002 pull code end")

    def __sync_p4_2004(self, branch: str, project_name: str):
        """
        从p4.com:2004拉取资源和配置文件
        Args:
            branch: 默认trunc
            project_name: 项目名
        """
        log.info("Start from p4 Com:2004 pull resources and configuration files")
        p4_com_2004_mgr = P4COM2004(branch=branch, project_name=project_name)
        changelist = p4_com_2004_mgr.get_latest_changes()
        p4_com_2004_mgr.sync_all(changelist=changelist, force=True)
        log.info("From p4 Com:2004 pull resource and configuration file end")

    # 上传到git仓库  下载也从git仓库下载
    def __git_config(self):
        project_name = env.get("PROJECT_NAME")
        g = GitlabMgr()
        g.server_git_clone_config(project_name=project_name)

    def __sync_gitlab(self, project_name: str):
        """
        从git拉取测试用例代码
        Args:
            project_name: 活动名
        """
        log.info("Start pull test case code from git")
        g = GitlabMgr()
        g.git_clone_x52_gitlab_code(project_name=project_name)
        g.git_clone_robotexecutor(project_name=project_name)
        log.info("Pull test case code from git end")

    def prepare(self, **kwargs):
        """
        准备工作
        """
        # 检查磁盘空间是否满足执行流水线
        advance.check_disk_size(threshold=5)
        # 清理工作目录
        # project_name = env.get("PROJECT_NAME")
        test_fixtures = env.get("TEST_FIXTURES")
        branch_name = env.get("BRANCH_NAME")
        # cmd.run_shell(
        #     cmds=["rd /s /Q {}".format(project_name)],
        #     workdir=WORKSPACE
        # )
        # 计算资源版本
        if branch_name != "trunk":
            if "v" in branch_name and branch_name.count("_") == 2:
                resource_branch = branch_name.replace("_", ".").replace("v", "version_")
                env.set({"ResourceBranch": resource_branch})
        # 更新备注信息
        if test_fixtures is None:
            log.info("pass")
            # exit(-1)
        else:
            log.info(test_fixtures)
            bk_ci_build_remark = test_fixtures
            log.info(bk_ci_build_remark)
            env.set({"BK_CI_BUILD_REMARK": bk_ci_build_remark})

    def sync_code(self, **kwargs):
        """
        拉取相关代码、资源等
        """
        branch = env.get("BRANCH_NAME")
        project_name = env.get("PROJECT_NAME")
        # 从p4.com:2002拉取代码
        self.__sync_p4_2002(branch=branch, project_name=project_name)
        # 从p4.com:2004拉取资源和配置文件
        self.__sync_p4_2004(branch=branch, project_name=project_name)
        # # 从git拉取配置文件、脚本代码和测试用例代码
        self.__sync_gitlab(project_name=project_name)

    def build(self, **kwargs):
        """
        编译前准备以及编译测试框架
        """
        project_name = env.get("PROJECT_NAME")
        branch_name = env.get("BRANCH_NAME")
        # target_seasons = env.get("TARGET_SEASONS")
        # main_poll_draw = env.get("MAIN_POOL_DRAW")
        # sub_pool_draw = env.get("SUB_POOL_DRAW")
        build_type = env.get("BUILD_TYPE")
        workdir = os.path.join(WORKSPACE, project_name)
        # 拷贝测试用例代码
        log.info("拷贝测试用例代码")
        cmd.run_shell(
            cmds=[
                'xcopy /e/y/i/f "x52_gitlab_code/MagicTeaPartyLottery/Code/_for_bot" "x52_whitebox/{}/depot/products/Project_X52/_for_bot"'.format(
                    branch_name
                ),
                'xcopy /e/y/i/f "x52_gitlab_code/MagicTeaPartyLottery/Config/test" "x52_whitebox/{}/exe/resources/config/test"'.format(branch_name),
            ],
            workdir=workdir,
        )
        # 更新env版本
        log.info("更新env版本")
        cmd.run_shell(
            cmds=[
                "python3 x52_gitlab_code/Script/X52_DockerEnvUpdater.py x52_whitebox/{}/exe/resources/config/test/remote_configs/.env {}".format(
                    branch_name, branch_name
                )
            ],
            workdir=workdir,
        )
        # 更新测试赛季信息
        # cmd.run_shell(
        #     cmds=["python3 x51_gitlab/localscripts/X52_SeasonUpdater.py ./x52_whitebox/{}/exe/resources/config/test/remote_configs/robot_test_case_config/deepsea_lottery/deepsea_lottery_test_case_config.xml {} {} {}".format(branch_name, target_seasons, main_poll_draw, sub_pool_draw)],
        #     workdir=workdir
        # )
        # 清理之前的报告
        log.info("清理之前的报告")
        cmd.run_shell(
            cmds=[
                "cd x52_whitebox/{}/exe/bin".format(branch_name),
                "del /f /s /q wbox_test_result*.xml",
                "cd ../../depot/products/Project_X52/_for_bot/report",
                "del /f /s /Q *",
            ],
            workdir=workdir,
        )
        # 编译功能测试框架
        log.info("编译功能测试框架")
        cmd.run_shell(
            cmds=[
                "cd x52_whitebox/{}/exe/bin".format(branch_name),
                "call 去除只读属性.bat",
                'BuildConsole.exe ../../depot/products/Project_X52/_for_bot/wbox.sln /CFG="Release|x86" /{} /MAXCPUS=40'.format(build_type),
            ],
            workdir=workdir,
        )

    def __update_server_ip(self, **kwargs):
        """
        修改test_config中的服务器IP
        D:\workspace\x52_whitebox\magic_tea\x52_whitebox\trunk\exe\resources\config\test\test_config.xml
        """
        server_ip = env.get("SERVER_IP")
        branch_name = env.get("BRANCH_NAME")
        project_name = env.get("PROJECT_NAME")
        workdir = os.path.join(WORKSPACE, "{}/x52_whitebox/{}/exe/resources/config/test/test_config.xml".format(project_name, branch_name))
        file_path = workdir
        with open(file_path, "r+", encoding="utf8") as f:
            file_data = f.read()
            file_data = re.sub(r'host=.*?".*?"', f'host="{server_ip}"', file_data)
            file_data = re.sub(r'Gateway ip=.*?".*?"', f'Gateway ip="{server_ip}"', file_data)
        with open(file_path, "w+", encoding="utf8") as f:
            f.write(file_data)

    def modify_config(self, **kwargs):
        """
        处理配置问题
        """
        project_name = env.get("PROJECT_NAME")
        branch_name = env.get("BRANCH_NAME")
        # username = env.get("BK_CI_START_USER_NAME")
        # item_case_run_user = env.get('ITEM_CASE_RUN_USERS')
        workdir = os.path.join(WORKSPACE, project_name)
        # projectId = 'x52'
        # piplineId = 'p-6908b595b90647b989c1009e76299fb1.p-57643f5d453d4bd5aa27575fd13dc78c'
        # ii = int(item_case_run_user) / 200
        # 修改服务器ip
        log.info("修改服务器ip")
        self.__update_server_ip()
        # 复制配置文件
        log.info("复制配置文件")
        shutil.copy(
            workdir + "/x52_whitebox/{}/exe/resources/config/server/bonus/activity/magic_tea_party.xml".format(branch_name),
            workdir + "/x52_whitebox/{}/depot/products/Project_X52/_for_bot/report/".format(branch_name),
        )
        # # 启动服务器互斥检查
        # log.info("启动服务器互斥检查")
        # cmd.run_shell(
        #     cmds=[
        #         f"python3 x52_gitlab_code\Script\PiplineStatusHelper.py {username} {projectId} {piplineId}"
        #     ],
        #     workdir=workdir
        # )
        # 更新docker配置文件
        log.info("更新docker配置文件")
        cmd.run_shell(
            cmds=[
                "server_logic_test.exe ServerOperationTest.startServer",
                "server_logic_test.exe CX52MagicTeaPartyLottery.UpdateConfig",
                "server_logic_test.exe ServerOperationTest.stopServer",
            ],
            workdir=os.path.join(workdir, "x52_whitebox/{}/exe/bin".format(branch_name)),
        )
        # # 获取道具
        # for _ in (1, 1, ii):
        #     cmd.run_shell(
        #         cmds=['server_logic_test.exe ServerOperationTest.startServer'],
        #         workdir=os.path.join(workdir, 'x52_whitebox/{}/exe/bin'.format(branch_name))
        #     )
        #     for _ in (1, 1, 20):
        #         cmd.run_shell(
        #             cmds=['server_logic_test.exe CX52MagicTeaPartyLottery.FirstGetAwardsNum2'],
        #             workdir=os.path.join(workdir, 'x52_whitebox/{}/exe/bin'.format(branch_name))
        #         )
        #     cmd.run_shell(
        #         cmds=['server_logic_test.exe ServerOperationTest.stopServer'],
        #         workdir=os.path.join(workdir, 'x52_whitebox/{}/exe/bin')
        #     )

    def upload_config(self, **kwargs):
        """
        拷贝指定文件到服务器
        """
        project_name = env.get("PROJECT_NAME")
        branch_name = env.get("BRANCH_NAME")
        server_ip = env.get("SERVER_IP")
        workdir = os.path.join(WORKSPACE, project_name)
        # test_configs_path = "x52_whitebox/{}/exe/resources/config/test".format(branch_name)
        # test_cfg_path = os.path.join(workdir, test_configs_path)
        # base_path = os.path.join(test_cfg_path, "remote_configs")
        # target_path = "/data/workspace/x52_whitebox_test/{}".format(project_name)
        target_path = "/root/x52_whitebox_test"
        if not os.path.exists(target_path):
            os.makedirs(target_path)
        # fixture_resource_path = os.path.join(base_path, "resources")
        password = self.__get_ip_password()
        filr_paths = [
            f"D:/workspace/x52_whitebox/{project_name}/x52_whitebox/{branch_name}/exe/resources/config/test/remote_configs/.env",
            f"D:/workspace/x52_whitebox/{project_name}/x52_whitebox/{branch_name}/exe/resources/config/test/remote_configs/docker-compose.yml",
            f"D:/workspace/x52_whitebox/{project_name}/x52_whitebox/{branch_name}/exe/resources/config/test/remote_configs/start_all_servers.sh",
            f"D:/workspace/x52_whitebox/{project_name}/x52_whitebox/{branch_name}/exe/resources/config/test/remote_configs/stop_all_servers.sh",
            f"D:/workspace/x52_whitebox/{project_name}/x52_whitebox/{branch_name}/exe/resources/config/test/remote_configs/resources/config/server/gpk/activity_gpk_config.xml",
            f"D:/workspace/x52_whitebox/{project_name}/x52_whitebox/{branch_name}/exe/resources/config/test/remote_configs/resources/config/server/h3d/servant/dynamic_card.xml",
            f"D:/workspace/x52_whitebox/{project_name}/x52_whitebox/{branch_name}/exe/resources/config/test/remote_configs/resources/config/server/h3d/servant/server_config.xml",
            f"D:/workspace/x52_whitebox/{project_name}/x52_whitebox/{branch_name}/exe/resources/config/test/remote_configs/resources/config/shared/servant/servant_global.xml",
            f"D:/workspace/x52_whitebox/{project_name}/x52_whitebox/{branch_name}/exe/resources/config/test/remote_configs/CX52MagicTeaPartyLottery/resources/config/server/bonus/activity/magic_tea_party.xml",
        ]
        for file_path in filr_paths:
            cmd.run_shell(cmds=["pscp -l root -pw {} {} {}:{}".format(password, file_path, server_ip, target_path)], workdir=workdir)

    def pull_config(self, **kwargs):
        """
        服务器拉取git上 上传的配置资源
        """
        # project_name = env.get("PROJECT_NAME")
        # branch_name = env.get("BRANCH_NAME")
        # target_path = "/data/workspace/x52_whitebox_test"
        # if not target_path.endswith("/"):
        #     target_path = target_path + "/"
        self.__git_config()

    def run_testcases(self, **kwargs):
        """
        跑测试用例
        """
        # project_name = env.get("PROJECT_NAME")
        test_fixtures = env.get("TEST_FIXTURES")
        # workdir = os.path.join(WORKSPACE, project_name)
        # start_server_test = env.get('InputStartServerTestFixture')
        # start_server = env.get("InputStartServerTestCase")
        # stop_server_test = env.get("InputStopServerTestFixture")
        # stop_server = env.get("InputStopServerTestCase")
        # 执行启动服务器测试用例
        exe_path = "D:/workspace/x52_whitebox/magic_tea/x52_whitebox/trunk/exe/bin"
        tests = test_fixtures.split(",")
        for test_fixture in tests:
            k = test_fixture.split(".")
            print("test_fixture:" + test_fixture)
            print("test_fixtures:" + test_fixtures)
            print(k)
            ret = cmd.run_shell(
                cmds=[
                    "server_logic_test.exe --gtest_filter=ServerOperationTest.startServer",
                ],
                workdir=exe_path,
            )
            print(ret)
            if ret != 0:
                log.error("startServer fail")
            ret = cmd.run_shell(
                cmds=[
                    "server_logic_test.exe --gtest_output=xml:wbox_test_result_ServerOperationTest_{}.xml".format(
                        datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
                    )
                ],
                workdir=exe_path,
            )
            print(ret)
            if ret != 0:
                log.error("gtest_output xml fail")
            # 执行fixture对应的测试用例
            ret = cmd.run_shell(
                cmds=[
                    "server_logic_test.exe --gtest_filter={}".format(test_fixture),
                ],
                workdir=exe_path,
            )
            if ret != 0:
                log.error("执行测试用例失败")
            ret = cmd.run_shell(
                cmds=[
                    "server_logic_test.exe --gtest_output=xml:wbox_test_result_{}_{}.xml".format(k[0], datetime.now().strftime("%Y_%m_%d_%H_%M_%S"))
                ],
                workdir=exe_path,
            )
            if ret != 0:
                log.error("输出报告失败")
            # 执行关服的测试用例
            ret = cmd.run_shell(
                cmds=[
                    "server_logic_test.exe --gtest_filter=ServerOperationTest.stopServer",
                ],
                workdir=exe_path,
            )
            if ret != 0:
                log.error("测试用例失败")
            ret = cmd.run_shell(
                cmds=[
                    "server_logic_test.exe --gtest_output=xml:wbox_test_result_ServerOperationTest_{}.xml".format(
                        datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
                    )
                ],
                workdir=exe_path,
            )
            if ret[0] != 0:
                log.error("测试用例失败")
            print("----------------------------------------------------------------------------------------------------")

    def create_report(self, **kwargs):
        """
        跑完测试用例之后的报告处理等操作
        """
        branch_name = env.get("BRANCH_NAME")
        project_name = env.get("PROJECT_NAME")
        test_fixtures = env.get("TEST_FIXTURES")
        bk_ci_pipeline_name = env.get("BK_CI_PIPELINE_NAME")
        workdir = os.path.join(WORKSPACE, project_name)
        # 生成HTML测试报告
        log.info("生成HTML测试报告")
        cmd.run_shell(
            cmds=[
                "python3 x52_gitlab_code/Script/X52_TestResultParser.py x52_whitebox/{}/exe/bin x52_whitebox/{}/depot/products/Project_X52/_for_bot/report  x52_whitebox/{}/depot/products/Project_X52/_for_bot/report x52_gitlab_code/Script/case_name_mapping.xml {} {}".format(
                    branch_name, branch_name, branch_name, bk_ci_pipeline_name, test_fixtures
                )
            ],
            workdir=workdir,
        )
