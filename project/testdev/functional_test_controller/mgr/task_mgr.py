# coding=utf-8
from frame import MysqlClient, log


class TaskMgr:
    def __init__(self):
        self.__mysql = MysqlClient(host="************", port="3306", username="root", password="123456")

    def enqueue_task(self, uid: str, project: str, task: str):
        """
        任务入队列
        Args:
            uid: uuid
            project: 项目代码: x51, x52, x5m
            task: 任务
        """
        sql = "insert into `testdev`.`task_queue` (uuid, project, params) values ('{}', '{}', '{}');".format(uid, project, task)
        ret = self.__mysql.insert_one(sql)
        log.info(ret)
        return ret

    def dequeue_task(self) -> dict:
        """
        任务出队列

        Returns:
            str: 任务
        """
        sql = "select uuid, project, params from `testdev`.`task_queue` order by create_time;"
        ret = self.__mysql.select_one(sql)
        if not ret:
            log.info("task is empty")
            return {}
        sql = "delete from `testdev`.`task_queue` where uuid='{}';".format(ret["uuid"])
        r = self.__mysql.delete_one(sql)
        log.info(r)
        return ret


task_mgr = TaskMgr()
