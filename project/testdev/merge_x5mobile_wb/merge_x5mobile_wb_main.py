# coding=utf-8
from frame import *
from project.testdev.merge_x5mobile_wb.mgr.env_mgr import env_mgr
from project.testdev.merge_x5mobile_wb.mgr.branch_mgr import branch_mgr
from project.testdev import config


@advance.stage(stage="获取需要合并的分支")
def get_branch(**kwargs):
    # 获取最新的三个分支, 6.03.0, 6.02.0, 6.01.0
    # 拼接最新的三个白盒分支, 6.03.0_wb, 6.02.0_wb, 6.01.0_wb
    # 检查三个白盒分支是否存在，如果不存在则抛异常
    three_latest_branches, three_latest_wb_branches = branch_mgr.get_need_branches()

    env_mgr.three_latest_branches = three_latest_branches
    env_mgr.three_latest_wb_branches = three_latest_wb_branches


@advance.stage(stage="合并分支")
def merge_branches(**kwargs):
    # 合并次次新白盒分支到次新白盒分支
    branch_mgr.merge_pre_pre_wb_to_pre_wb()
    # 合并次新分支到次新白盒分支
    branch_mgr.merge_pre_to_pre_wb()
    # 合并次新白盒分支到最新白盒分支
    branch_mgr.merge_pre_wb_to_cur_wb()
    # 合并最新分支到最新白盒分支
    branch_mgr.merge_cur_to_cur_wb()
    # 判断是否合并失败
    if not (env_mgr.merge_pre_pre_wb_to_pre_wb and env_mgr.merge_pre_to_pre_wb and env_mgr.merge_pre_wb_to_cur_wb and env_mgr.merge_cur_to_cur_wb):
        raise PyframeException("分支合并失败")


def __get_msg() -> str:
    # 有bug
    three_latest_branches = env_mgr.three_latest_branches
    three_latest_wb_branches = env_mgr.three_latest_wb_branches
    merge_pre_pre_wb_to_pre_wb = env_mgr.merge_pre_pre_wb_to_pre_wb
    merge_pre_to_pre_wb = env_mgr.merge_pre_to_pre_wb
    merge_pre_wb_to_cur_wb = env_mgr.merge_pre_wb_to_cur_wb
    merge_cur_to_cur_wb = env_mgr.merge_cur_to_cur_wb
    if three_latest_wb_branches:
        pre_pre_wb = three_latest_wb_branches[-1]
        pre_wb = three_latest_wb_branches[1]
        cur_wb = three_latest_wb_branches[0]
    if three_latest_branches:
        pre = three_latest_branches[1]
        cur = three_latest_branches[0]

    msg = "**分支合并情况**:\n"
    if merge_pre_pre_wb_to_pre_wb:
        msg += f"合并{pre_pre_wb}到{pre_wb}成功\n"
    elif merge_pre_pre_wb_to_pre_wb == False:
        msg += f"<font color='warning'>合并{pre_pre_wb}到{pre_wb}失败, 可能存在冲突, 请手动解决冲突</font>\n"

    if merge_pre_to_pre_wb:
        msg += f"合并{pre}到{pre_wb}成功\n"
    elif merge_pre_to_pre_wb == False:
        msg += f"<font color='warning'>合并{pre}到{pre_wb}失败, 可能存在冲突, 请手动解决冲突</font>\n"

    if merge_pre_wb_to_cur_wb:
        msg += f"合并{pre_wb}到{cur_wb}成功\n"
    elif merge_pre_wb_to_cur_wb == False:
        msg += f"<font color='warning'>合并{pre_wb}到{cur_wb}失败, 可能存在冲突, 请手动解决冲突</font>\n"

    if merge_cur_to_cur_wb:
        msg += f"合并{cur}到{cur_wb}成功\n"
    elif merge_cur_to_cur_wb == False:
        msg += f"<font color='warning'>合并{cur}到{cur_wb}失败, 可能存在冲突, 请手动解决冲突</font>"

    return msg


def on_success(**kwargs):
    wechat.send_unicast_post_success(user_list=config.TESTDEV_DEVELOPER, content=__get_msg())
    # wechat.send_multicast_post_success(
    #     webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=14457620-a0c0-45ce-9220-584c19b1dfa7",
    #     content=__get_successful_msg()
    # )
    # advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(user_list=config.TESTDEV_DEVELOPER, content=__get_msg())
    # wechat.send_multicast_post_failure(
    #     webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=14457620-a0c0-45ce-9220-584c19b1dfa7",
    #     content=__get_msg()
    # )
    # advance.insert_pipeline_history_on_failure()


def on_unstable(**kwargs):
    wechat.send_unicast_post_unstable(user_list=config.TESTDEV_DEVELOPER, content=__get_msg())
    # wechat.send_multicast_post_unstable(
    #     webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=14457620-a0c0-45ce-9220-584c19b1dfa7",
    #     content=__get_msg()
    # )
    # advance.insert_pipeline_history_on_unstable()
