from frame import *


class EnvMgr:
    @property
    def three_latest_branches(self):
        return env.get("three_latest_branches")

    @three_latest_branches.setter
    def three_latest_branches(self, three_latest_branches: list):
        env.set({"three_latest_branches": three_latest_branches})

    @property
    def three_latest_wb_branches(self):
        return env.get("three_latest_wb_branches")

    @three_latest_wb_branches.setter
    def three_latest_wb_branches(self, three_latest_wb_branches: list):
        env.set({"three_latest_wb_branches": three_latest_wb_branches})

    @property
    def merge_pre_pre_wb_to_pre_wb(self):
        return env.get("merge_pre_pre_wb_to_pre_wb")

    @merge_pre_pre_wb_to_pre_wb.setter
    def merge_pre_pre_wb_to_pre_wb(self, merge_pre_pre_wb_to_pre_wb: bool):
        env.set({"merge_pre_pre_wb_to_pre_wb": merge_pre_pre_wb_to_pre_wb})

    @property
    def merge_pre_to_pre_wb(self):
        return env.get("merge_pre_to_pre_wb")

    @merge_pre_to_pre_wb.setter
    def merge_pre_to_pre_wb(self, merge_pre_to_pre_wb: bool):
        env.set({"merge_pre_to_pre_wb": merge_pre_to_pre_wb})

    @property
    def merge_pre_wb_to_cur_wb(self):
        return env.get("merge_pre_wb_to_cur_wb")

    @merge_pre_wb_to_cur_wb.setter
    def merge_pre_wb_to_cur_wb(self, merge_pre_wb_to_cur_wb: bool):
        env.set({"merge_pre_wb_to_cur_wb": merge_pre_wb_to_cur_wb})

    @property
    def merge_cur_to_cur_wb(self):
        return env.get("merge_cur_to_cur_wb")

    @merge_cur_to_cur_wb.setter
    def merge_cur_to_cur_wb(self, merge_cur_to_cur_wb: bool):
        env.set({"merge_cur_to_cur_wb": merge_cur_to_cur_wb})


class JenkinsMgr:
    def __init__(self) -> None:
        pass


env_mgr = EnvMgr()
