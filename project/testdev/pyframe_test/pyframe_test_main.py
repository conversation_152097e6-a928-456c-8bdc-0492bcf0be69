# coding-utf-8

from frame import *


def do_first_job(**kwargs):
    log.info("do_first_job start")
    # 使用之前先提取所需参数
    param1, param2 = kwargs["param1"], kwargs["param1"]
    log.info("param1: {}, param2: {}".format(param1, param2))


def do_second_job(**kwargs):
    log.info("do_second_job start")
    # 使用之前先提取所需参数
    param1, param2 = kwargs["param1"], kwargs["param1"]
    log.info(kwargs)
    log.info("param1: {}, param2: {}".format(param1, param2))
