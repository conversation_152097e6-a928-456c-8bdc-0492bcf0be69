node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "win10_**************"
        }
    }

    options {
        disableConcurrentBuilds()
    }

    parameters {
        string(name: 'BRAN<PERSON>', defaultValue: 'branch_2023/QQX5_Mainland_6.5.6_07', description: '游戏分支')
        string(name: 'CHANGELIST', defaultValue: '1387026', description: '客户端资源P4版本')
        string(name: 'LOCAL_PATH', defaultValue: 'D:\\x51_client', description: '客户端存放位置')
        string(name: 'SERVER_IP', defaultValue: '*************', description: '服务器IP')
        string(name: 'CLIENT_IP', defaultValue: '**************', description: '客户端IP')
        booleanParam(name: 'FORCE_UPDATE', defaultValue: false, description: '强制更新p4')
        string(name: 'LOGIN_QQ', defaultValue: '', description: '登录QQ')
        booleanParam(name: 'TEST_ENV', defaultValue: true, description: '是否为测试环境')
    }
    environment {
        // PYFRAME_PYTHON = 'C:\\ProgramData\\miniconda3\\envs\\pyf368\\python.exe'
        PYFRAME_PYTHON = 'python'
    }

    stages {
        stage("安装python依赖") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            $env.PYFRAME_PYTHON -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("准备") {
            steps {
                script {
                    bat(script: "$env.PYFRAME_PYTHON testdev.py x51_smoke_deploy_client --job=prepare")
                }
            }
        }
        stage("更新P4") {
            steps {
                script {
                    bat(script: "$env.PYFRAME_PYTHON testdev.py x51_smoke_deploy_client --job=sync_p4")
                }
            }
        }
        stage("修改配置") {
            steps {
                script {
                    bat(script: "$env.PYFRAME_PYTHON testdev.py x51_smoke_deploy_client --job=modify_config")
                }
            }
        }
        stage("启动客户端") {
            steps {
                script {
                    bat(script: "$env.PYFRAME_PYTHON testdev.py x51_smoke_deploy_client --job=start_client")
                }
            }
        }
    }
    post {
        success {
            script {
                bat(script: "$env.PYFRAME_PYTHON testdev.py x51_smoke_deploy_client --job=on_success")
            }
        }
        failure {
            script {
                bat(script: "$env.PYFRAME_PYTHON testdev.py x51_smoke_deploy_client --job=on_failure")
            }
        }
        aborted {
            script {
                bat(script: "$env.PYFRAME_PYTHON testdev.py x51_smoke_deploy_client --job=on_canceled")
            }
        }
    }
}
