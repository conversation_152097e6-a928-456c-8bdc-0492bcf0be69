# coding=utf-8
from pathlib import Path
from typing import Union

from frame import FtpMgr, PyframeException, cmd, os
from project.testdev.x51_smoke_test.deploy_server.mgr.env_mgr import (
    jenkins_env_mgr,
)


class LinuxBinMgr:
    def __init__(self, branch: str):
        self._branch = branch
        self._remote_linux_bin_path = f"/data/mockp4/x51/{self._branch}/exe/linux_bin"
        self._local_bin_path = f"/data/workspace/x51/{self._branch}/exe/bin/"
        self._bin_version_path = f"/data/workspace/x51/{self._branch}/exe/bin/bin_version.txt"

    def match_bin_version(self, changelist: str) -> Union[str, None]:
        """
        匹配linux_bin的版本
        Args:
            changelist:

        Returns:
            str: linux_bin_version
        """
        versions = os.listdir(self._remote_linux_bin_path)
        versions.sort(reverse=True)
        for version in versions:
            arr = version.split("_")
            if len(arr) != 4:
                continue
            # 根据p4号寻找
            if int(arr[3]) <= int(changelist):
                return version
        return None

    def read_local_bin_version(self) -> Union[str, None]:
        """
        读取本地bin版本
        Returns:
            str: 本地bin版本
        """
        p = Path(self._bin_version_path)
        if p.exists():
            return p.read_text(encoding="utf-8")
        return None

    def write_local_bin_version(self, version: str):
        """
        写入本地bin版本
        Args:
            version: 版本号
        """
        p = Path(self._bin_version_path)
        p.write_text(data=version, encoding="utf-8")

    def download_linux_bin(self):
        """
        下载linux_bin
        Args:
            version:
        """
        ftp_mgr = FtpMgr(ip="**************", port=21, username="administrator", password="Admin123")
        remote_folder = f"/x51/server/{jenkins_env_mgr.branch}/svn_{jenkins_env_mgr.svn_version}_p4_{jenkins_env_mgr.changelist}"
        if not ftp_mgr.exists_folder(remote_folder):
            raise PyframeException(f"svn {jenkins_env_mgr.svn_version} p4 {jenkins_env_mgr.changelist} 对应的服务器包还不存在")
        ftp_mgr.download_folder(
            src=remote_folder,
            dst=self._local_bin_path,
        )

    def decompress_linux_bin(self):
        """
        解压linux_bin
        """
        ret = cmd.run_shell(
            cmds=["pzstd -r -d --force --rm *.zst -p 28"],
            workdir=self._local_bin_path,
        )
        if ret[0] != 0:
            raise PyframeException(
                message="解压linux_bin失败，请联系流水线管理员",
                mentioned_list=["<EMAIL>"],
            )
