# coding=gbk

from frame import *
from project.x51 import config


class P4COM2003:
    def __init__(self, branch: str):
        self._branch = branch
        self._client = "x51_deploy_server_{}_{}".format(self._branch.replace("/", "_"), common.get_host_ip())
        self.p4 = P4Client(
            host=config.P4_2003_CONFIG.get("host"),
            username=config.P4_2003_CONFIG.get("username"),
            password=config.P4_2003_CONFIG.get("password"),
            client=self._client,
        )
        client = self._client
        views = [
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/config/... //{client}/x51/{branch}/exe/bin/config/...",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/stringfilter/... //{client}/x51/{branch}/exe/bin/stringfilter/...",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/mail/... //{client}/x51/{branch}/exe/bin/mail/...",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/boardcast/... //{client}/x51/{branch}/exe/bin/boardcast/...",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/rank/... //{client}/x51/{branch}/exe/bin/rank/...",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/resources/... //{client}/x51/{branch}/exe/bin/resources/...",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/libktv_service.so //{client}/x51/{branch}/exe/bin/libktv_service.so",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/libsvc_ktv_center.so //{client}/x51/{branch}/exe/bin/libsvc_ktv_center.so",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/libsvc_ktv_server.so //{client}/x51/{branch}/exe/bin/libsvc_ktv_server.so",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/ktv_center //{client}/x51/{branch}/exe/bin/ktv_center",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/ktv_server //{client}/x51/{branch}/exe/bin/ktv_server",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/libktv_service64.so //{client}/x51/{branch}/exe/bin/libktv_service64.so",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/antisvrimport_conf.xml //{client}/x51/{branch}/exe/bin/antisvrimport_conf.xml",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/lib64/mysql_for_db64/... //{client}/x51/{branch}/exe/bin/lib64/mysql_for_db64/...",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/lib64/libtss_sdk.so //{client}/x51/{branch}/exe/bin/lib64/libtss_sdk.so",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/resources/level/... //{client}/x51/{branch}/exe/resources/level/...",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/resources/strings/... //{client}/x51/{branch}/exe/resources/strings/...",
            f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/resources/media/audio/ktv/... //{client}/x51/{branch}/exe/resources/media/audio/ktv/...",
            f"//H3D_X51_res/X51_SourceBase/测试用配置/server/config/weekly/Shop.xml //{client}/x51/{branch}/exe/bin/config/weekly/Shop.xml",
        ]
        self.p4.set_view(views=views)
        self._root = "/data/workspace/"
        self.p4.set_root(self._root)
        self.p4.set_options(clobber=True)

    def get_latest_changes(self):
        """
        获取最新changes
        """
        ret = self.p4.get_latest_changes(f"//H3D_X51_res/QQX5_Mainland/{self._branch}/exe/...")
        try:
            changelist = ret.get("change")
        except Exception as e:
            log.error(e)
            changelist = "head"
        return changelist

    def sync_all(self, changelist: str, force: bool):
        """
        更新p4
        """
        # self.p4.sync(path="//H3D_X51_res/X51_SourceBase/测试用配置/server/config/weekly/Shop.xml", changelist=changelist, force=force)
        self.p4.sync_all(changelist=changelist, force=force)

    def sync_weekly_shop_config(self):
        """
        替换shop.xml
        """
        self.p4.set_encoding(encoding="GBK")
        self.p4.print(path="//H3D_X51_res/X51_SourceBase/测试用配置/server/config/weekly/Shop.xml", to="Shop.xml")
        path_mgr.move(
            src="Shop.xml",
            # dst=f"{self._root}x51/{self._branch}/exe/bin/config/weekly/Shop.xml",
            dst=os.path.join(self._root, f"x51/{self._branch}/exe/bin/config/weekly/Shop.xml"),
        )
