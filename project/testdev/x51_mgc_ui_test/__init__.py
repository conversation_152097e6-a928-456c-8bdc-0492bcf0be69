# coding=utf-8
import os
from frame import env


class Config:
    TESTDEV_X51_DEVELOPER = ["ji<PERSON><PERSON><PERSON><PERSON>@h3d.com.cn", "<EMAIL>"]
    X51_PYFRAME_WORKSPACE = env.pipeline.workspace()
    # X51_LANDUN_WORKSPACE = X51_PYFRAME_WORKSPACE if env.get("ios_client_id", "")=="" else env.get("LOCAL_PATH")
    X51_LANDUN_WORKSPACE = X51_PYFRAME_WORKSPACE
    GIT_URL = {
        "*********************": "https://gitlab.h3d.com.cn/test-dev/x51_group/*********************.git",
    }

    # vika的gitlab通用账号
    GITLAB_MGC_TOKEN = "********************"

    TIME_FORMAT = "%Y-%m-%d %H:%M"


config = Config()
