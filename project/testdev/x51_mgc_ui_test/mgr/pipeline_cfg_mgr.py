# coding=utf-8

import json
import requests

from frame import *


class X51MgcPipelineCfgMgr:
    def __init__(self):
        pass

    def get_pipeline_cfg_info_from_env(self):
        self.pipeline_cfg = {
            "ios_client_id": env.get("ios_client_id", ""),
            "ios_anchor_id": env.get("ios_anchor_id", ""),
            "wda_package_name": env.get("wda_package_name", ""),
            "itunes_exe_path": env.get("itunes_exe_path", ""),
            "simulator_exe_path": env.get("simulator_exe_path", ""),
            "host_server": env.get("SERVER_IP", ""),
            "remote_apk_path": env.get("remote_apk_path", ""),
            "remote_ipa_path": env.get("remote_ipa_path", ""),
            "target_testset_name": env.get("target_testset_name", ""),
            "target_cases_platforms": env.get("target_cases_platforms", ""),
            "target_cases_name_keyword": env.get("target_cases_name_keyword", ""),
            "execute_debug_mode": env.get("execute_debug_mode", ""),
            "PERFORCE_BRANCH": env.get("PERFORCE_BRANCH", ""),
            "ios_anchor_actual_account": env.get("ios_anchor_actual_account", ""),
            # "runnername": env.get("BK_CI_START_USER_NAME", ""),
        }
        return self.pipeline_cfg

    def output_cfg_file(self):
        para_file_path = os.path.join(os.getcwd(), "pipeline_paras.txt")
        output_lines = []
        # 打开文本
        with open(para_file_path, "w", encoding="gb2312") as f:
            for item_key, item_value in self.pipeline_cfg.items():
                output_lines.append("{}:::{} \n".format(item_key, item_value))
        if os.path.exists(para_file_path) == False:
            log.error("Error: 参数配置文件 {} 不存在，生成失败！".format(para_file_path))
            exit(-1)
