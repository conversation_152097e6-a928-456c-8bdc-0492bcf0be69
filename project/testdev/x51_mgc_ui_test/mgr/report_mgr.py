# coding=utf-8

import json
import requests

from frame import *
from project.testdev.x5m_whitebox import config
from project.testdev.x5m_whitebox.mgr.msg_mgr import MsgMgr


class ReportMgr:
    def __init__(self):
        pass

    def init_request_by_env(self):
        self.__request = {
            "ios_client_id": env.get("ios_client_id", ""),
            "ios_anchor_id": env.get("ios_anchor_id", ""),
            "wda_package_name": env.get("wda_package_name", ""),
            "itunes_exe_path": env.get("itunes_exe_path", ""),
            "simulator_exe_path": env.get("simulator_exe_path", ""),
            "host_server": env.get("SERVER_IP", ""),
            "remote_apk_path": env.get("remote_apk_path", ""),
            "remote_ipa_path": env.get("remote_ipa_path", ""),
            "target_testset_name": env.get("target_testset_name", ""),
            "target_cases_platforms": env.get("target_cases_platforms", ""),
            "target_cases_name_keyword": env.get("target_cases_name_keyword", ""),
            "execute_debug_mode": env.get("execute_debug_mode", ""),
            "PERFORCE_BRANCH": env.get("PERFORCE_BRANCH", ""),
            "ios_anchor_actual_account": env.get("ios_anchor_actual_account", ""),
            # "runnername": env.get("BK_CI_START_USER_NAME", ""),
        }
        return self.__request

    # def init_request_by_param(self, runId: str, jenkinsState: str, errors: str):
    #     self.__request = {
    #         "ios_client_id": "",
    #         "ios_anchor_id": "",
    #         "wda_package_name": "",
    #         "itunes_exe_path": "",
    #         "simulator_exe_path": "",
    #         "host_server": "",
    #         "remote_apk_path": "",
    #         "remote_ipa_path": "",
    #         "target_testset_name": "",
    #         "target_cases_platforms": "",
    #         "target_cases_name_keyword": "",
    #         "execute_debug_mode": "",
    #         "PERFORCE_BRANCH": "",
    #         "ios_anchor_actual_account": "",
    #     }

    # def send_request(self):
    #     # runId为0是手动触发的,不触发回调
    #     if self.__request.get("runId", "0") == "0":
    #         return
    #     url = "http://{}:{}/{}".format(*config.GO_PROXY_URI.values())
    #     log.info(f"url: {url}")
    #     headers = {
    #         "Content-Type": "application/json",
    #     }
    #     try:
    #         response = requests.post(
    #             url=url,
    #             headers=headers,
    #             json=self.__request,
    #             timeout=(config.Go_CONNECT_CONFIG["connect_timeout"], config.Go_CONNECT_CONFIG["read_timeout"]),
    #         )
    #     except requests.exceptions.Timeout as e:
    #         raise PyframeException(f"send request to {url} timeout, error msg: {str(e)}")
    #     except requests.exceptions.RequestException as e:
    #         raise PyframeException(f"send request to {url} other error, error msg: {str(e)}")
    #     except:
    #         raise PyframeException(f"send request to {url} other error, error msg: `other unknown error`")
    #     if response.status_code != 200:
    #         msg = MsgMgr.get_requests_msg(url, response)
    #         MsgMgr.send_request_error(msg)
    #         raise PyframeException(
    #             "send requst to go_proxy {} error, return status_code: {}, response text: {}, request: {}".format(
    #                 url, response.status_code, response.text, self.__request
    #             )
    #         )

    #     response_json = json.loads(response.text)
    #     # 等稳定后在解析
    #     if response_json["code"] != 0:
    #         msg = MsgMgr.get_requests_msg(url, response)
    #         MsgMgr.send_request_error(msg)
    #         raise PyframeException(
    #             "send requst to go_proxy {} error,  return error_code: {}, error_msg: {}, text: {}, request: {}".format(
    #                 url, response_json["code"], response_json["error_msg"], response.text, self.__request
    #             )
    #         )
    #     log.info("send requst to go_proxy {} , request: {}, response text: {}".format(url, self.__request, response.text))
    #     response.close()
