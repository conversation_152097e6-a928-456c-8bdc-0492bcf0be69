# coding=utf-8

from frame import *
from project.testdev.x51_mgc_ui_test import config


class X51MgcGitMgr:
    def __init__(self, workdir: str = config.X51_LANDUN_WORKSPACE, branch: str = "master") -> None:
        self.__workdir = workdir
        self.__branch = branch
        self.git_url = config.GIT_URL["mgcautomationprojects"]

    def pull_test_files_from_git(self):
        branch_name = "master"
        group_name = "mgcautomationprojects"
        log.debug("current workspace: " + self.__workdir)
        git_mgr = GitMgr(workdir=self.__workdir, project_name="AirtestProj")
        if not Path(os.path.join(self.__workdir, "AirtestProj")).exists():
            log.debug("clone projects")
            git_mgr.clone_with_oauth2(url=self.git_url, branch=self.__branch, oauth2=config.GITLAB_MGC_TOKEN)
        else:
            log.debug("pull code")
            git_mgr.reset()
            git_mgr.clean()
            git_mgr.advance_pull(branch=branch_name)
