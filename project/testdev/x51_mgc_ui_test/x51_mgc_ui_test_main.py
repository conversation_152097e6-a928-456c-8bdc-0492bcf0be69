# coding=utf-8
# import shutil
import re
from frame import *

from project.testdev.x51_mgc_ui_test import config
from project.testdev.x51_mgc_ui_test.mgr.pipeline_cfg_mgr import X51MgcPipelineCfgMgr
from project.testdev.x51_mgc_ui_test.mgr.x51_mgc_git_mgr import X51MgcGitMgr


# @advance.stage(stage="开始测试")
# def start_test():
#     """
#     开始测试,主要用来发通知
#     """
#     report_mgr = ReportMgr()
#     report_mgr.init_request_by_env()
#     __param_check()


@advance.stage(stage="准备")
def prepare(**kw):
    branch = kw.get("perforce_branch", "")
    server_ip = kw.get("server_ip", "")
    client_ip = kw.get("client_ip", "")
    runnername = kw.get("runnername", "")
    env.set({"runnername": runnername})
    env.set({"perforce_branch": branch})
    env.set({"server_ip": server_ip})
    env.set({"client_ip": client_ip})
    log.info(branch)
    client = common.get_host_ip()
    print(__get_msg())


@advance.stage(stage="更新gitlab")
def update_gitlab(**kw):
    print("-更新gitlab--")
    x5m_git_mgr = X51MgcGitMgr()
    x5m_git_mgr.pull_test_files_from_git()


# @advance.stage(stage="生成流水线配置文件")
# def generate_pipeline_cfg_info_file(**kw):
#     pipeline_cfg_mgr = X51MgcPipelineCfgMgr()
#     pipeline_cfg_mgr.get_pipeline_cfg_info_from_env()
#     pipeline_cfg_mgr.output_cfg_file()


@advance.stage(stage="修改流水线配置文件")
def update_test_cfg_file(**kw):
    print("config.X51_PYFRAME_WORKSPACE=" + config.X51_PYFRAME_WORKSPACE)
    print("config.X51_LANDUN_WORKSPACE=" + config.X51_LANDUN_WORKSPACE)
    target_file_path = os.path.join(config.X51_LANDUN_WORKSPACE, "AirtestProj/DeployScripts/update_paras_cfg_from_pipelines.py")
    if os.path.exists(target_file_path) == False:
        log.error("执行文件不存在：" + target_file_path)
        return
    ret = cmd.run_shell(cmds=["python AirtestProj/DeployScripts/update_paras_cfg_from_pipelines.py"], workdir=config.X51_LANDUN_WORKSPACE)
    if ret[0] != 0:
        log.error("return {}".format(ret[1]))
        raise PyframeException("修改流水线配置文件失败, 请检查日志, 返回码: {}".format(ret[0]))
    # ret = cmd.run_shell(cmds=["pip install -r requirements.txt"], workdir=os.path.join(config.X51_LANDUN_WORKSPACE, "AirtestProj", "PyCaseRunner"))
    # if ret[0] != 0:
    #     log.error("return {}".format(ret[1]))
    #     raise PyframeException("安装requirements.txt失败, 请检查日志, 返回码: {}".format(ret[0]))


@advance.stage(stage="准备测试相关环境")
def prepare_mgc_test_files(**kw):
    username = env.get("runnername")
    if username == "" or username == None:
        runnername = kw.get("runnername")
    else:
        runnername = username
    env.set({"runnername": runnername})
    ret = cmd.run_shell(
        cmds=["python deploy_mgc_automation_script.py --manual false --runnername {}".format(runnername)],
        workdir=os.path.join(config.X51_LANDUN_WORKSPACE, "AirtestProj", "DeployScripts"),
    )
    if ret[0] != 0:
        log.error("return {}".format(ret[1]))
        raise PyframeException("准备测试相关环境失败, 请检查日志, 返回码: {}".format(ret[0]))


@advance.stage(stage="运行测试用例")
def run_testcase(**kw):
    run_cmd_file = workdir = os.path.join(config.X51_LANDUN_WORKSPACE, "AirtestProj", "PyCaseRunner", "run_case.cmd")
    if os.path.exists(run_cmd_file) == False:
        raise FileNotFoundError("run_case.cmd未找到，请检测步骤")
    cmd_list = []
    with open(run_cmd_file, "r", encoding="gb2312") as f:
        for ann in f.readlines():
            cmd_list.append(ann)
    ret = cmd.run_shell(cmds=[cmd_list[-1]], workdir=os.path.join(config.X51_LANDUN_WORKSPACE, "AirtestProj", "PyCaseRunner"))
    if ret[0] != 0:
        log.error("return {}".format(ret[1]))
        raise PyframeException("运行测试用例失败, 请检查日志, 返回码: {}".format(ret[0]))


# @advance.stage(stage="处理结果")
# def make_report_zip():
#     branch = env.get("BRANCH_NAME")
#     zip_mgr = ZipMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch)
#     zip_mgr.copy_converseFile()
#     zip_mgr.copy_requestFile_and_buildlogFile()
#     zip_mgr.make_report_zip()

# @advance.stage(stage="发送报告")
# def make_report_zip():
#     branch = env.get("BRANCH_NAME")
#     zip_mgr = ZipMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch)
#     zip_mgr.copy_converseFile()
#     zip_mgr.copy_requestFile_and_buildlogFile()
#     zip_mgr.make_report_zip()


def on_success(**kw):
    msg = __get_msg()
    wechat.send_unicast_post_success(user_list=config.TESTDEV_X51_DEVELOPER, content=msg)
    advance.insert_pipeline_history_on_success()


def on_unstable(**kw):
    # env.set({"JenkinsState": "unstable"})
    wechat.send_unicast_post_unstable(user_list=config.TESTDEV_X51_DEVELOPER, content=__get_msg())
    advance.insert_pipeline_history_on_unstable()


def on_failure(**kw):
    # env.set({"JenkinsState": "failure"})
    wechat.send_unicast_post_failure(user_list=config.TESTDEV_X51_DEVELOPER, content=__get_msg())
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kw):
    # env.set({"JenkinsState": "canceled"})
    wechat.send_unicast_post_canceled(user_list=config.TESTDEV_X51_DEVELOPER, content=__get_msg())
    advance.insert_pipeline_history_on_canceled()


def __get_msg():
    branch = env.get("perforce_branch", "")
    server_ip = env.get("server_ip", "")
    client_ip = env.get("client_ip", "")
    runnername = env.get("runnername", "")

    msg = f"**分支**: {branch}\n" if branch else ""
    msg += f"**服务器**: {server_ip}\n" if server_ip else ""
    msg += f"**客户端**: {client_ip}\n" if client_ip else ""
    msg += f"**用户**: {runnername}\n" if runnername else ""
    return msg
