from frame import env


class EnvMgr:
    def __init__(self):
        pass

    def set_diff_cores(self, diff_cores: list):
        env.set({"diff_cores": diff_cores})

    def get_diff_cores(self):
        return env.get("diff_cores")

    def set_gdb_url(self, gdb_url: list):
        env.set({"gdb_url": gdb_url})

    def get_gdb_url(self):
        return env.get("gdb_url")

    def set_max_pipeline_id(self, max_pipeline_id: str):
        env.set({"max_pipeline_id": max_pipeline_id})

    def get_max_pipeline_id(self):
        return env.get("max_pipeline_id")

    def set_is_special_branch(self, is_special_branch: bool):
        env.set({"is_special_branch": is_special_branch})

    def get_is_special_branch(self):
        return env.get("is_special_branch")

    def set_special_branch(self, special_branch: str):
        env.set({"special_branch": special_branch})

    def get_special_branch(self):
        return env.get("special_branch")

    def set_main_db_name(self, main_db_name: str):
        env.set({"main_db_name": main_db_name})

    def get_main_db_name(self):
        return env.get("main_db_name")

    def set_global_db_name(self, global_db_name: str):
        env.set({"global_db_name": global_db_name})

    def get_global_db_name(self):
        return env.get("global_db_name")

    def set_sub_db_name(self, sub_db_name: str):
        env.set({"sub_db_name": sub_db_name})


class BkEnvMgr:
    def __init__(self):
        pass

    def get_week_config(self):
        return env.get("week_config")

    def get_config_path(self):
        return env.get("config_path")

    def get_server_slave(self):
        return env.get("server_slave")

    def get_db_name(self):
        return env.get("db_name")

    def get_server_package(self):
        return env.get("server_package")

    def get_private_server_ip(self):
        return env.get("private_server_ip")

    def get_artifacts_branch(self):
        return env.get("artifacts_branch")

    def get_sub_db_name(self):
        return env.get("sub_db_name")

    def set_sub_db_name(self, sub_db_name: str):
        env.set({"sub_db_name": sub_db_name})


env_mgr = EnvMgr()
bk_env_mgr = BkEnvMgr()
