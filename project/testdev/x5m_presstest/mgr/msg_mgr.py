# coding=utf-8
import json
import time
from frame import *
from project.testdev.x5m_presstest.mgr.env_mgr import env_mgr
from project.testdev.x5m_whitebox import config


class MsgMgr:
    @staticmethod
    def get_jenkins_msg() -> str:
        try:
            msg = f"**压测用例组**: {env.get('SEL_FLOW')}\n"
            msg += f"**版本分支**: {env.get('BRANCH_NAME')}\n"
            msg += f"**压测进程数量**: {env.get('PROCESS_COUNT')}\n"
            msg += f"**单进程机器人数量**: {env.get('ROBOT_COUNT_PER_PROCESS')}\n"
            msg += f"**周更分支**: {env.get('VERSION')}\n"
        except Exception as e:
            msg = f"**拼接消息失败**: {str(e)}"
        msg += MsgMgr.__get_msg()
        return msg

    @staticmethod
    def send_request_error(msg) -> None:
        wechat.send_unicast_post_failure(user_list=config.TESTDEV_X5M_DEVELOPER, content=msg, to_admin=False, rescue=False)

    @staticmethod
    def __get_msg():
        max_pipeline_id = env_mgr.get_max_pipeline_id()
        diff_cores = env_mgr.get_diff_cores()
        gdb_url = env_mgr.get_gdb_url()
        msg = "\n**服务器包**: dgm_server_{}.zip".format(max_pipeline_id)
        msg += "\n**脚本包**: dgm_sh_{}.zip".format(max_pipeline_id)
        if diff_cores:
            diff_data = []
            for d in diff_cores:
                create_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime(Path(d).stat().st_ctime))
                d = "{}-{}".format(d, create_time)
                diff_data.append(d)
            msg += "\n**本次部署有core文件产生** \n"
            msg += "**core文件解析:** \n"
            for d in range(len(diff_cores)):
                msg += "[{}]({})\n".format(diff_data[d], gdb_url[d])
        return msg
