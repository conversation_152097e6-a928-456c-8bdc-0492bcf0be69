# coding=utf-8

import shutil
import zipfile
import xml.dom.minidom
from frame import *
from project.testdev.x5m_presstest import config
from project.testdev.x5m_whitebox.mgr.ssh_mgr import Ssh
import os
import multiprocessing


# def __start_presstest_cmd(cmdStr, bin_path):
#     cmd.run_shell(
#         cmds=[cmdStr],
#         workdir=bin_path,
#     )


class TestMgr:
    def __init__(self, workdir: str, branch: str, config_branch: str):
        self.__workdir = workdir
        self.__branch = branch
        self.__config_branch = config_branch
        self.__whitebox_exe_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe")
        self.__gokit_path = config.GOKIT_PATH
        self.__nunit3_dir = os.path.join(
            self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/client/packages/NUnit.ConsoleRunner.3.11.1/tools/nunit3-console.exe"
        )
        self.__whitebox_dir = os.path.join(self.__whitebox_exe_path, "bin/PressTestMain.exe")
        # self.__test_run_plan_dir = os.path.join(self.__whitebox_exe_path, "bin/test_run_plan")

    def run_whitebox_logintest(self):
        # self.__get_encoding()
        self.__connect_linux_server()
        self.__clean_log_file()
        # self.__getandupload_config()
        self.__run_white_box_login_test()

    def start_presstest(self):
        self.__modify_presstest_config()
        self.__start_presstest_clients()

    def close_presstest_clients(self):
        proc_mgr.kill_proc(name="PressTestMain.exe")
        # cmd.run_shell(
        #     cmds=["taskkill /IM PressTestMain.exe"],
        # )

    def __modify_presstest_config(self):
        """
        修改压测配置 press_test_config.xml
        """
        config_path = os.path.join(self.__whitebox_exe_path, "press_test")
        # 读取version_control_android_app.xml version_control_android_res.xml对应配置
        version_control_android_app_path = os.path.join(self.__whitebox_exe_path, "resources/config/server/h3d/version_control_android_app.xml")
        app_version = self.__get_app_version(version_control_android_app_path, "App", "Version")
        log.info("app version in version_control_android_app.xml is {}".format(app_version))

        version_control_android_res_path = os.path.join(self.__whitebox_exe_path, "resources/config/server/h3d/version_control_android_res.xml")
        res_version = self.__get_app_version(version_control_android_res_path, "Resource", "Version")
        log.info("res version in version_control_android_res.xml is {}".format(res_version))

        cmd.run_shell(
            cmds=[
                "attrib -r /S /D",
            ],
            workdir=config_path,
        )
        cmd.run_shell(
            cmds=[
                f"{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/Client --attr-name id_base --attr-value \"{env.get('PSTID')}\" -i",
                f"{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/Client --attr-name clientCount --attr-value \"{env.get('ROBOT_COUNT_PER_PROCESS')}\" -i",
                f"{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/version_list/version --attr-name version_ip --attr-value \"{env.get('SERVER')}\" -i",
                f'{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/version --attr-name app_version --attr-value "{app_version}" -i',
                f'{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/version --attr-name res_version --attr-value "{res_version}" -i',
                f"{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/test_cases --attr-name sel_flow --attr-value \"{env.get('SEL_FLOW')}\" -i",
            ],
            workdir=config_path,
        )

    def __get_app_version(self, xml_path: str, node_name: str, attri_name: str):
        """
        获取version_control_android_app.xml version_control_android_res.xml对应配置
        """
        with open(xml_path, "r", encoding="utf-8") as f:
            domtree = xml.dom.minidom.parse(xml_path)
            rootnode = domtree.documentElement
            return rootnode.getElementsByTagName(node_name)[0].getAttribute(attri_name)

    def __start_presstest_clients(self):
        bin_path = os.path.join(self.__whitebox_exe_path, "bin")
        process_count = int(env.get("PROCESS_COUNT"))
        robot_count_per_process = int(env.get("ROBOT_COUNT_PER_PROCESS"))
        pstid = int(env.get("PSTID"))
        sel_flow = int(env.get("SEL_FLOW"))
        processes = []
        for i in range(process_count):
            cmd = f'start PressTestMain.exe "" {sel_flow} {i*robot_count_per_process + pstid} {robot_count_per_process}'
            process = multiprocessing.Process(target=self.start_presstest_cmd, args=(cmd, bin_path))
            process.daemon = True
            process.start()
            processes.append(process)
        for process in processes:
            process.join(10)

    def start_presstest_cmd(self, cmdStr, bin_path):
        cmd.run_shell(cmds=[cmdStr], workdir=bin_path, dot_kill_me=True)

    def __start_presstest_server(self):
        """
        启动linux server
        """
        cmd.run_shell(
            cmds=[
                f"ssh -i id_rsa_160 root@{env.get('SERVER')} -t \"sh  {self.__whitebox_exe_path}/start_global_server.sh\"",
                f"ssh -i id_rsa_160 root@{env.get('SERVER')} -t \"sh  {self.__whitebox_exe_path}/start_allserver.sh\"",
            ],
            workdir=self.__workdir,
        )

    def __connect_linux_server(self):
        """
        连接服务器
        """
        bin_path = os.path.join(self.__whitebox_exe_path, "bin")
        # 写一个ssh秘钥文件,访问服务器使用
        id_rsa_160_filepath = os.path.join(bin_path, "id_rsa_160")
        if not os.path.exists(id_rsa_160_filepath):
            with open(id_rsa_160_filepath, "w") as file:
                file.write(config.SSHKEYFILE)
        # 修改reportportal的配置,将debug模式设为false
        cmd.run_shell(
            cmds=[f'{self.__gokit_path} json set ReportPortal.config.json "launch.debugMode" false -i'],
            workdir=bin_path,
            encoding="utf-8",
            errors="ignore",
        )
        # 只保留Administrators的写入 读取 执行权限
        cmd.run_shell(
            cmds=["start WhiteboxPretreatment.exe -t ClearFileSecurityAccessControl -a id_rsa_160"],
            workdir=bin_path,
            errors="ignore",
        )
        # 无需拷贝配置
        # if not common.str2bool(env.get("ISK8SSERVER")):
        #     cmd.run_shell(
        #         cmds=[
        #             f"scp -i id_rsa_160 -r {self.__workdir}/x5mobile/{self.__branch}/mobile_dancer/trunk/whitebox/* root@{env.get('SERVER')}:/data/white-box-new",
        #             f"scp -i id_rsa_160 -r {self.__workdir}/x5mobile/{self.__branch}/mobile_dancer/trunk/server/mysql/* root@{env.get('SERVER')}:/data/white-box-new/run/run-data/mysql-run-data",
        #             f"ssh -i id_rsa_160 root@{env.get('SERVER')} \"dos2unix /data/white-box-new/*.sh\"",
        #         ],
        #         workdir=bin_path,
        #     )

    def __clean_log_file(self):
        """
        清理之前运行产生的日志等文件
        """
        # self.__safe_clean(os.path.join(self.__whitebox_exe_path, "logs"), True)
        # self.__safe_clean(os.path.join(self.__whitebox_exe_path, "robot_test/output"), True)
        # self.__safe_clean(os.path.join(self.__whitebox_exe_path, "robot_test/robot_test_config.local.xml"))
        # self.__safe_clean(os.path.join(self.__whitebox_exe_path, "robot_test/checkData/*.*"))
        # self.__safe_clean(os.path.join(self.__whitebox_exe_path, "*.zip"))

    def __safe_clean(self, file_path: str, is_dir: bool = False):
        if is_dir:
            if not os.path.exists(file_path):
                os.makedirs(file_path)
            shutil.rmtree(file_path)
            os.makedirs(file_path)
            return
        cmd.run_shell(cmds=[f"del /q {os.path.basename(file_path)}"], workdir=os.path.dirname(file_path))

    def __getandupload_config(self):
        """
        获取服务端配置包并上传
        """
        if not common.str2bool(env.get("IS_TEST_JENKINS")):
            return
        # 无论怎么样都会获取一份ftp的包,用来对比远程打出来的包和本地压缩的配置包
        has_ftp_config = self.__get_ftp_config()
        if common.str2bool(env.get("GETCONFIGFROMLOCAL")):
            # 压缩cdn、周更、热更配置(放在根目录 configTemp/local 中),并上传到对应服务器
            self.__zip_file()
            self.__upload_config()
        elif common.str2bool(env.get("GETCONFIGFROMLOCALFULL")):
            # 压缩exe/resources/config下的配置(放在根目录 configTemp/local 中),并上传到对应服务器
            self.__zip_full_file()
            self.__upload_config()
        elif has_ftp_config:
            # 从ftp获取热更资源(放在根目录 configTemp/ftp 中),并上传到对应服务器
            self.__upload_config(False)

    def __zip_file(self):
        """
        压缩热更周更cdn
        """
        dir = os.path.join(self.__workdir, "configTemp/local/test/resources")
        self.__safe_clean(dir, True)
        log.info("拷贝cdn配置")
        cdn_cfg_path = os.path.join(self.__workdir, "x5mconfig/cdn")
        self.__safe_xcopy(
            src=os.path.join(cdn_cfg_path, "config/server"),
            dst=os.path.join(dir, "config/server"),
        )
        self.__safe_xcopy(
            src=os.path.join(cdn_cfg_path, "config/shared"),
            dst=os.path.join(dir, "config/shared"),
        )
        log.info("拷贝热更配置")
        path = os.path.join(self.__workdir, "x5mconfig/hotfix/onlineupdate", self.__config_branch)
        if os.path.exists(path):
            for i in os.listdir(path):
                content = os.path.join(path, i)
                client = os.path.join(content, "client/crossplatform/config")
                server = os.path.join(content, "server/resources/config")
                if os.path.exists(server) and os.path.exists(dir):
                    self.__safe_xcopy(
                        src=server,
                        dst=os.path.join(dir, "config"),
                    )
                if os.path.exists(client):
                    for subdir in os.listdir(client):
                        if subdir == "shared":
                            self.__safe_xcopy(
                                src=os.path.join(client, subdir),
                                dst=os.path.join(dir, "config", subdir),
                            )
                            break
        log.info("拷贝周更配置")
        week_cfg_path = os.path.join(self.__workdir, "x5mweek", env.get("VERSION"))
        self.__safe_xcopy(
            src=os.path.join(week_cfg_path, "config/server"),
            dst=os.path.join(dir, "config/server"),
        )
        self.__safe_xcopy(
            src=os.path.join(week_cfg_path, "config/shared"),
            dst=os.path.join(dir, "config/shared"),
        )
        version_control = os.path.join(self.__workdir, "configTemp/local/test/resources/config/server/h3d")
        if not os.path.exists(version_control):
            # 如果热更中没有版本配置文件，就去版本配置中拷一份
            self.__safe_xcopy(
                src=os.path.join(self.__workdir, f"x5mconfig/{self.__config_branch}/config/server/h3d"),
                dst=version_control,
            )
        log.info("压缩配置")
        try:
            zip = zipfile.ZipFile(os.path.join(self.__workdir, "configTemp/local/temp_server_pack.zip"), "w", zipfile.ZIP_DEFLATED)
            for path, _, filenames in os.walk(dir):
                fpath = path.replace(dir, "test/resources")
                for filename in filenames:
                    zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
            zip.close()
        except:
            raise PyframeException("压缩配置文件失败")

    def __safe_xcopy(self, src, dst):
        if os.path.exists(src):
            path_mgr.xcopy(src=src, dst=dst, dst_is_file=False)
        else:
            log.warning(f"源路径 {src} 不存在")

    def __zip_full_file(self):
        """
        压缩exe/resources/config下的配置
        """
        log.info("压缩(exe/resources/config)配置文件")
        try:
            dir = f"{self.__workdir}/x5mobile/{self.__branch}/mobile_dancer/trunk/exe/resources/config"
            if not os.path.exists(dir):
                raise PyframeException(f"{dir} 目标目录不存在")
            if not os.path.exists(f"{self.__workdir}/configTemp/local"):
                os.makedirs(f"{self.__workdir}/configTemp/local")
            zip = zipfile.ZipFile(f"{self.__workdir}/configTemp/local/temp_server_pack.zip", "w", zipfile.ZIP_DEFLATED)
            for dirname in ["server", "shared"]:
                src = os.path.join(dir, dirname)
                for path, _, filenames in os.walk(src):
                    fpath = path.replace(src, f"test/resources/config/{dirname}")
                    for filename in filenames:
                        if filename.endswith(".bytes"):
                            continue
                        if filename == "server_globals.xml" or filename == "zone_globals.xml":
                            continue
                        zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
            zip.close()
        except:
            raise PyframeException("压缩配置文件失败")

    def __get_ftp_config(self):
        """
        获取ftp配置包
        """
        ftp_mgr = FtpMgr(
            ip=config.FTP_CONFIG.get("ip"),
            port=config.FTP_CONFIG.get("port"),
            username=config.FTP_CONFIG.get("user"),
            password=config.FTP_CONFIG.get("password"),
        )
        dir_name = env.get("VERSION")
        if dir_name == "resources":
            dir_name = "source"
        """
        # # 获取对应wb分支下最新的包
        # dir_name += "_wb"
        # target_dir = config.FTP_CONFIG.get("dir").format(dir_name)
        # config_list = ftp_mgr.dirs(target_dir)
        # if len(config_list) == 0:
        #     raise PyframeException(f"ftp error, remote directory {target_dir} has no files")
        # elif len(config_list) > 1:
        #     raise PyframeException(f"ftp error, remote directory {target_dir} has too many files")
        # config_filename = config_list[0]
        # # 校验远程目录版本号
        # if config_filename.split("_")[1][:3] != "".join(self.__branch.split(".")[:2]):
        #     log.error(f"ftp error, 版本号告警,远程目录版本号{config_filename.split('_')[1][:3]},本地分支{''.join(self.__branch.split('.')[:2])}")
        #     return
        """

        target_dir = config.FTP_CONFIG.get("dir").format(dir_name)
        config_list = ftp_mgr.dirs(target_dir)
        version_str = "".join(self.__branch.split(".")[:2])
        log.info(f"branch version is {version_str}")
        file_list = [file for file in config_list if file.split("_")[1][:3] == version_str]
        if len(file_list) == 0:
            log.warn(f"ftp error, remote directory {target_dir} version str {version_str} has no files")
            # raise PyframeException(f"ftp error, remote directory {target_dir} version str {version_str} has no files")
            return False
        file_list.sort(reverse=True)
        config_filename = file_list[0]
        log.info(f"find remote zip file [{config_filename}] by local branch version {version_str}")
        ftp_mgr.download_file(
            src=os.path.join(target_dir, config_filename), dst=os.path.join(self.__workdir, "configTemp/ftp/temp_server_pack.zip"), process_bar=True
        )
        return True

    def __upload_config(self, local: bool = True):
        """
        上传服务器配置包
        local:
            True  上传本地配置包
            False 上传ftp远程配置包
        """
        with Ssh(workdir=self.__workdir, hostname=env.get("SERVER"), branch=self.__branch) as ssh:
            log.info("上传配置压缩包")
            log.info("删除以前遗留的配置包")
            ssh.run_cmd("rm /data/white-box-new/run/upload-dir/temp_server_pack.zip")
            log.info("上传配置压缩包")
            ssh.upload_file(
                f"{self.__workdir}/configTemp/{'local' if local else 'ftp'}/temp_server_pack.zip",
                "/data/white-box-new/run/upload-dir/temp_server_pack.zip",
            )
            log.info("删除以前遗留的配置目录")
            ssh.run_cmd("rm -r -f /data/white-box-new/run/upload-dir/temp_server_pack")
            log.info("删除备份的core、日志和配置")
            ssh.run_cmd("rm -r -f /data/white-box-new/run/run-data/back_data_dir")
            log.info("创建配置解压结果目录,防止如果没有配置,docker启动拷贝错误")
            ssh.run_cmd("mkdir -p /data/white-box-new/run/upload-dir/temp_server_pack/test/resources")
            log.info("解压下载的配置包（如果没有则会出错,但是没关系）")
            ssh.run_cmd("unzip -o /data/white-box-new/run/upload-dir/temp_server_pack.zip -d" " /data/white-box-new/run/upload-dir/temp_server_pack")
            log.info("清理upload-dir/resources目录")
            ssh.run_cmd("rm -r -f /data/white-box-new/run/upload-dir/resources")
            log.info("清理docker")
            ssh.run_cmd("docker system prune -a -f")

    def __run_white_box_login_test(self):
        """
        运行白盒
        """
        self.__run_case("ConvertToBinaryFiles")
        self.__run_case("LoginOnlyOne")

    def __run_case(self, testname):
        """
        执行白盒测试用例
        """
        log_name = "TestResult" + testname + ".xml"
        # 把测试结果也保存到log中，到时候可以一并打包，方便反查定位
        log_dir = os.path.join(self.__whitebox_exe_path, "robot_test/output/LoginOnlyOne", log_name)

        cmd.run_shell(
            cmds=[f'{self.__nunit3_dir} /result:{log_dir} {self.__whitebox_dir} --where "name == {testname}"'],
            encoding="utf-8",
            errors="ignore",
        )

        try:
            domtree = xml.dom.minidom.parse(log_dir)
            rootnode = domtree.documentElement
            res = rootnode.getElementsByTagName("test-case")[0].getAttribute("result")
            if res == "Passed":
                log.info("{}, Passed".format(testname))
            else:
                raise PyframeException("{} 运行失败, 日志请查看 {}".format(testname, log_dir))
        except:
            raise PyframeException("{} 运行失败, 日志请查看 {}".format(testname, log_dir))

    # def __get_encoding(self):
    #     self.__encoding = "utf-8"
    #     if env.get("CLIENT") == "192.168.7.197":
    #         self.__encoding = "gb18030"
