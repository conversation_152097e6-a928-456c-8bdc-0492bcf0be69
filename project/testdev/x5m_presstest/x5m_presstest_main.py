# coding=utf-8
# import shutil
import re
import time
from frame import *

from project.testdev.x5m_presstest import config, linux_config
from project.testdev.x5m_presstest.mgr.test_mgr import TestMgr
from project.testdev.x5m_presstest.mgr.msg_mgr import MsgMgr
from project.testdev.x5m_whitebox.mgr.x5m_git_mgr import X5mGitMgr
from project.testdev.x5m_whitebox.mgr.p4_mgr import P4COM1666
from project.testdev.x5m_whitebox.mgr.makelink_mgr import MakeLinkMgr
from project.testdev.x5m_whitebox.mgr.config_mgr import ConfigMgr
from project.testdev.x5m_whitebox.mgr.build_mgr import BuildMgr
from project.testdev.x5m_presstest.mgr.cdn_mgr import cdn_mgr
from project.testdev.x5m_presstest.mgr.config_mgr import config_mgr
from project.testdev.x5m_presstest.mgr.core_mgr import CoreMgr
from project.testdev.x5m_presstest.mgr.deploy_install_mgr import deploy_install_mgr
from project.testdev.x5m_presstest.mgr.env_mgr import env_mgr, bk_env_mgr
from project.testdev.x5m_presstest.mgr.hotfix_mgr import hot_fix_mgr
from project.testdev.x5m_presstest.mgr.prepare_environment_mgr import prepare_environment_mgr
from project.testdev.x5m_presstest.mgr.server_package_mgr import server_package_mgr

# from project.testdev.x5m_whitebox.mgr.report_mgr import ReportMgr
# from project.testdev.x5m_whitebox.mgr.zip_mgr import ZipMgr
# from project.testdev.x5m_whitebox.mgr.result_mgr import ResultMgr

"""
以下代码为客户端部署机相关
"""


@advance.stage(stage="开始测试")
def start_test():
    """
    开始测试,主要用来发通知
    """
    # report_mgr = ReportMgr()
    # report_mgr.init_request_by_env()
    # report_mgr.send_request()
    __param_check()


@advance.stage(stage="准备")
def prepare():
    branch = env.get("BRANCH_NAME")
    log.info(branch)
    client = common.get_host_ip()
    server = config.CLIENT_SERVER_MAP[client]

    env.set(
        {
            "CONFIG_BRANCH_NAME": branch.split("_")[0],
            "CLIENT": client,
            "SERVER": server,
            "WORKSPACE": config.X5M_JENKINS_WORKSPACE,
            "JenkinsState": "start",
        }
    )


def __param_check():
    # test_level = env.get("TEST_LEVEL")
    # right_str = {"0", "1", "2", "3", "|"}
    # for item in test_level:
    #     if item not in right_str:
    #         raise PyframeException(f"test_level has `{item}` not in `{'、'.join(right_str)}`")

    weekly_branch = env.get("VERSION")
    if weekly_branch not in {"resources", "release", "online"}:
        raise PyframeException("weekly branch should be one of 'resources'、'release'、'online'")


@advance.stage(stage="关闭压测进程")
def close_presstest_clients():
    branch = env.get("BRANCH_NAME")
    config_branch = env.get("CONFIG_BRANCH_NAME")
    test_mgr = TestMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch, config_branch=config_branch)
    test_mgr.close_presstest_clients()


@advance.stage(stage="更新gitlab")
def update_gitlab():
    branch = env.get("BRANCH_NAME")
    x5m_git_mgr = X5mGitMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch)
    x5m_git_mgr.update_x5mobile()
    x5m_git_mgr.update_x5mconfig()
    x5m_git_mgr.update_x5mweek()
    x5m_git_mgr.set_max_pipeline_id()


@advance.stage(stage="更新p4")
def update_p4():
    config_branch = env.get("CONFIG_BRANCH_NAME")
    p4_com_1666 = P4COM1666(branch=config_branch, workdir=config.X5M_JENKINS_WORKSPACE)
    p4_com_1666.sync_all(False)


@advance.stage(stage="拷贝配置")
def copy_and_makelink_config():
    branch = env.get("BRANCH_NAME")
    config_branch = env.get("CONFIG_BRANCH_NAME")
    make_link_mgr = MakeLinkMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch, config_branch=config_branch)
    make_link_mgr.make_link()
    weekly_branch = env.get("VERSION")
    config_mgr = ConfigMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch, config_branch=config_branch, weekly_branch=weekly_branch)
    config_mgr.copy_config()


@advance.stage(stage="编译")
def build_whitebox():
    branch = env.get("BRANCH_NAME")
    build_mgr = BuildMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch)
    build_mgr.build_press_test_main()


@advance.stage(stage="登录测试用例")
def run_login_case():
    branch = env.get("BRANCH_NAME")
    config_branch = env.get("CONFIG_BRANCH_NAME")
    test_mgr = TestMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch, config_branch=config_branch)
    test_mgr.run_whitebox_logintest()


@advance.stage(stage="启动压测客户端")
def start_presstest_clients():
    branch = env.get("BRANCH_NAME")
    config_branch = env.get("CONFIG_BRANCH_NAME")
    test_mgr = TestMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch, config_branch=config_branch)
    test_mgr.start_presstest()


# @advance.stage(stage="生成报告")
# def make_report_zip():
#     branch = env.get("BRANCH_NAME")
#     zip_mgr = ZipMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch)
#     zip_mgr.copy_converseFile()
#     zip_mgr.copy_requestFile_and_buildlogFile()
#     zip_mgr.make_report_zip()


def on_success():
    # workdir = config.X5M_JENKINS_WORKSPACE
    # branch = env.get("BRANCH_NAME")
    # result_mgr = ResultMgr(workdir=workdir, branch=branch)
    # if env.get("TESTCASE_ID", "").split(",")[0] == "0|100|42":
    #     result_mgr.delete_whitebox_result()
    # # 暂时不检查白盒结果文件是否存在
    # # result_mgr.check_whitebox_result()
    # result_mgr.get_white_box_check_result()
    # result_mgr.update_tested_file_data()
    env.set({"JenkinsState": "success"})
    wechat.send_unicast_post_success(user_list=[env.get("BUILD_USER_EMAIL", "")], content=MsgMgr.get_jenkins_msg())
    advance.insert_pipeline_history_on_success()


def on_unstable():
    env.set({"JenkinsState": "unstable"})
    wechat.send_unicast_post_unstable(
        user_list=[env.get("BUILD_USER_EMAIL", ""), "<EMAIL>", "<EMAIL>"], content=MsgMgr.get_jenkins_msg()
    )
    advance.insert_pipeline_history_on_unstable()


def on_failure():
    env.set({"JenkinsState": "failure"})
    wechat.send_unicast_post_failure(
        user_list=[env.get("BUILD_USER_EMAIL", ""), "<EMAIL>", "<EMAIL>"],
        content=MsgMgr.get_jenkins_msg(),
        to_admin=True,
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled():
    env.set({"JenkinsState": "canceled"})
    wechat.send_unicast_post_canceled(user_list=[env.get("BUILD_USER_EMAIL", "")], content=MsgMgr.get_jenkins_msg())
    advance.insert_pipeline_history_on_canceled()


def end_test():
    """
    测试结束,包括结果的解析和日志、报告链接的发送
    """
    # report_mgr = ReportMgr()
    # report_mgr.init_request_by_env()
    # report_mgr.send_request()
    # __clear_workspace()


def __clear_workspace():
    """
    清理工作目录
    """
    for filename in os.listdir(config.X5M_JENKINS_WORKSPACE):
        if filename.endswith("@tmp") and "pyframe-pipeline" not in filename:
            path_mgr.rm(os.path.join(config.X5M_JENKINS_WORKSPACE, filename[:-4]))
            path_mgr.rm(os.path.join(config.X5M_JENKINS_WORKSPACE, filename))
    if path_mgr.exists(os.path.join(config.X5M_JENKINS_WORKSPACE, "configTemp")):
        path_mgr.rm(os.path.join(config.X5M_JENKINS_WORKSPACE, "configTemp"))
    if path_mgr.exists(os.path.join(config.X5M_JENKINS_WORKSPACE, "configConversion")):
        path_mgr.rm(os.path.join(config.X5M_JENKINS_WORKSPACE, "configConversion"))
    # 只保留最近两个分支
    x5_mobile_mr_b_path = os.path.join(config.X5M_JENKINS_WORKSPACE, "x5_mobile/mr/b")
    x5mconfig_path = os.path.join(config.X5M_JENKINS_WORKSPACE, "x5mconfig")
    x5mobile_path = os.path.join(config.X5M_JENKINS_WORKSPACE, "x5mobile")
    __clear_unused_folder(x5_mobile_mr_b_path)
    __clear_unused_folder(x5mconfig_path)
    __clear_unused_folder(x5mobile_path)


def __clear_unused_folder(root: str, save_count: int = 2) -> None:
    """
    清理旧的分支，包括代码(x5mobile)、配置(x5mconfig)和资源(x5_mobile)
    默认保留两个分支

    Args:
        root (str): 根目录
        save_count (int): 要保留的文件夹数量
    """
    file_list = []
    for file in os.listdir(root):
        if re.search("^[0-9]\.[0-9][0-9]\.[0-9]$", file) or re.search("^[0-9]\.[0-9][0-9]\.[0-9](_wb)$", file):
            file_list.append(file)
    file_list.sort()
    if len(file_list) > save_count:
        delete_file_list = file_list[: len(file_list) - save_count]
        for filename in delete_file_list:
            path = os.path.join(root, filename)
            cmd.run_shell(['rd /S /Q "{}"'.format(path)])


"""
以下代码为linux服务器相关
"""


@advance.stage(stage="关服")
def stop_all_server(**kwargs):
    ret = cmd.run_shell(cmds=["bash ./stop_allserver.sh"], workdir=linux_config.LINUX_WORKDIR)
    if ret[0] != 0:
        log.error("stop all server failed!")
        raise PyframeException("关服失败")


@advance.stage(stage="清理log目录和temp目录")
def clean_server_logs(**kwargs):
    log_dir = os.path.join(linux_config.LINUX_WORKDIR, "logs")
    ret = cmd.run_shell(cmds=["rm -r -f *.log"], workdir=log_dir)
    if ret[0] != 0:
        log.error("clean server logs failed!")
    is_delete = path_mgr.rm("/data/depot")
    if not is_delete:
        log.error("删除目录/data/depot失败!")


@advance.stage(stage="解析参数")
def analytic_parameter(**kwargs):
    env.set({"db_name": linux_config.db_name})
    env.set({"sub_db_name": linux_config.sub_db_name})

    # todo: test code
    # env_mgr.set_max_pipeline_id(max_pipeline_id=104871)

    db_name = bk_env_mgr.get_db_name()
    sub_db_name = bk_env_mgr.get_sub_db_name()
    prepare_environment_mgr.analytic_parameter(db_name=db_name, sub_db_name=sub_db_name)
    # server_package = bk_env_mgr.get_server_package()
    server_package = env.get("SERVER_PACKAGE", "")
    if not server_package.startswith("dgm_server") and server_package:
        env_mgr.set_is_special_branch(is_special_branch=True)
    else:
        env_mgr.set_is_special_branch(is_special_branch=False)


@advance.stage(stage="下载并解压服务器包和脚本包")
def download_server_package_from_nexus(**kwargs):
    # artifacts_branch = bk_env_mgr.get_artifacts_branch()
    # 默认值是branch
    artifacts_branch = linux_config.artifacts_branch
    # server_package = bk_env_mgr.get_server_package()
    server_package = env.get("SERVER_PACKAGE", "")
    if "dgm_server" in server_package:
        max_pipeline_id = server_package.split("_")[-1].split(".")[0]
        special_branch = server_package.split("_dgm")[0]
    else:
        max_pipeline_id = server_package_mgr.get_max_pipeline_id(artifacts_branch=artifacts_branch)
        special_branch = ""
    log.info(max_pipeline_id)
    env_mgr.set_max_pipeline_id(max_pipeline_id=max_pipeline_id)
    env_mgr.set_special_branch(special_branch=special_branch)
    # 下载服务器包和脚本包
    server_package_mgr.download_server_package(max_pipeline_id=max_pipeline_id, artifacts_branch=artifacts_branch)
    server_package_mgr.download_script_package(max_pipeline_id=max_pipeline_id, artifacts_branch=artifacts_branch)
    # 解压脚本文件
    server_package_mgr.unzip_script_package(max_pipeline_id=max_pipeline_id)


@advance.stage(stage="修改数据库")
def change_db(**kwargs):
    global_name = env_mgr.get_global_db_name()
    main_name = env_mgr.get_main_db_name()
    sub_name = bk_env_mgr.get_sub_db_name()
    # private_ip = bk_env_mgr.get_private_server_ip()
    private_ip = env.get("SERVER")
    # 修改数据库
    config_mgr.update_db_config(global_name=global_name, main_name=main_name, sub_name=sub_name, private_ip=private_ip)


@advance.stage(stage="构造环境")
def construct_environment(**kwargs):
    prepare_environment_mgr.construct_environment()


def __move_config():
    """
    覆盖ip_config.csv和region_list_config.xml文件
    """
    # 删除原文件
    path_mgr.rm(path="/data/depot/make_dev_files/ip_config.csv")
    path_mgr.rm(path="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/ip_config.csv")
    path_mgr.rm(path="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/resources/config/server/op/region_list_config.xml")
    # 复制新文件到所需位置
    path_mgr.copy(src="/data/workspace/ip_config.csv", dst="/data/depot/make_dev_files/ip_config.csv")
    path_mgr.copy(src="/data/workspace/ip_config.csv", dst="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/ip_config.csv")
    path_mgr.copy(
        src="/data/workspace/region_list_config.xml",
        dst="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/resources/config/server/op/region_list_config.xml",
    )


@advance.stage(stage="部署安装")
def deploy_install(**kwargs):
    # 替换配置文件
    if common.get_host_ip() == "*************" or common.get_host_ip() == "*************":
        __move_config()

    max_pipeline_id = env_mgr.get_max_pipeline_id()
    # week_config = bk_env_mgr.get_week_config()
    week_config = env.get("version")
    deploy_install_mgr.deploy_install(max_pipeline_id=max_pipeline_id, week_config=week_config)


@advance.stage(stage="分发热更文件")
def download_ftp_hotfix(**kwargs):
    # config_path = bk_env_mgr.get_config_path()
    config_path = env.get("version")
    hot_fix_mgr.download_ftp_hotfix(config_path=config_path)


@advance.stage(stage="现网版本刷热更")
def current_version_brush_hotfix(**kwargs):
    # config_path = bk_env_mgr.get_config_path()
    config_path = env.get("version")
    # config_path = "release"
    hot_fix_mgr.current_version_brush_hotfix(config_path=config_path)


@advance.stage(stage="刷cdn配置")
def download_ftp_cdn_config(**kwargs):
    # 下载CDN CONFIG
    cdn_mgr.download_cdn_config()
    # 解压 更新CDN_CONFIG
    cdn_mgr.update_cdn_config()


@advance.stage(stage="修改gcloud配置")
def update_gcloud_config(**kwargs):
    config_mgr.update_gcloud_config()


@advance.stage(stage="关闭支付开关")
def close_pay(**kwargs):
    config_mgr.close_pay()


@advance.stage(stage="修改心跳")
def modify_heart_config(**kwargs):
    config_mgr.modify_heart_config()


@advance.stage(stage="同步数据库")
def sync_db(**kwargs):
    # 替换配置文件
    if common.get_host_ip() == "*************" or common.get_host_ip() == "*************":
        __move_config()

    script_path = ""
    exe_path = linux_config.LINUX_WORKDIR + "/"
    # main_deploy_ip = bk_env_mgr.get_private_server_ip()
    main_deploy_ip = env.get("SERVER")
    sub_deploy_ip = ""
    config_mgr.sync_db_config(
        script_path=script_path, exe_path=exe_path, main_deploy_ip=main_deploy_ip, sub_deploy_ip=sub_deploy_ip, branch_name="empty"
    )


@advance.stage(stage="开启server all")
def start_server_all(**kwargs):
    # server_slave = bk_env_mgr.get_server_slave()
    server_slave = linux_config.server_slave
    ret = cmd.run_shell(
        cmds=["bash ./start_allserver.sh"],
        workdir=linux_config.LINUX_WORKDIR,
        dot_kill_me=True,
    )
    if ret[0] != 0:
        log.error("start_allserver.sh failed")
        raise PyframeException("start_allserver.sh执行失败")

    ret = cmd.run_shell(
        cmds=["bash ./start_global_server.sh"],
        workdir=linux_config.LINUX_WORKDIR,
        dot_kill_me=True,
    )
    if ret[0] != 0:
        log.error("start_global_server.sh failed")
        raise PyframeException("start_global_server.sh执行失败")

    if server_slave == "联服":
        ret = cmd.run_shell(cmds=["bash ./start_allserver_slave.sh"], workdir=linux_config.LINUX_WORKDIR)
        if ret[0] != 0:
            log.error("start_allserver_slave.sh failed")
            raise PyframeException("start_allserver_slave.sh执行失败")


@advance.stage(stage="检查进行运行")
def check_process(**kwargs):
    # 等120秒再检查服务器
    time.sleep(120)
    if proc_mgr.exists_proc_by_param("./svc_launch -type lobby -id 1"):
        log.info("lobby is running")
    else:
        log.error("lobby is not running")
        # raise PyframeException("lobby is not running")


def check_core(**kwargs):
    core_mgr = CoreMgr()
    core_mgr.monitor()
