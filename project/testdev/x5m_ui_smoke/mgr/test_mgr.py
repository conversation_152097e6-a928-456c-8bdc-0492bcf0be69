# coding=utf-8

import shutil
import zipfile
import xml.dom.minidom
from frame import *
from project.testdev.x5m_presstest import config
from project.testdev.x5m_whitebox.mgr.ssh_mgr import Ssh
import os
import multiprocessing


class TestMgr:
    def __init__(self, workdir: str, branch: str, config_branch: str):
        self.__workdir = workdir
        self.__branch = branch
        self.__config_branch = config_branch
        # self.__whitebox_exe_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe")
        self.__gokit_path = config.GOKIT_PATH
        # self.__nunit3_dir = os.path.join(
        #     self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/client/packages/NUnit.ConsoleRunner.3.11.1/tools/nunit3-console.exe"
        # )
        # self.__whitebox_dir = os.path.join(self.__whitebox_exe_path, "bin/PressTestMain.exe")

    def close_clients(self):
        proc_mgr.kill_proc(name="PressTestMain.exe")

    def modify_ui_frame_config(self):
        # log.info("config.X51_LANDUN_WORKSPACE=" + jenkins_env_mgr.get_local_path())
        target_file = "UiA/DeployScripts/x51_smoke/x51_win_smoke_update_paras_cfg_from_pipelines.py"
        target_file_path = os.path.join(self.__workdir, target_file)
        if os.path.exists(target_file_path) == False:
            raise PyframeException("执行文件不存在：" + target_file_path)
        ret = cmd.run_shell(cmds=["python {}".format(target_file)], workdir=self.__workdir, return_output=True)
        if ret[0] != 0:
            log.error("return {}".format(ret[1]))
            raise PyframeException("修改流水线配置文件失败, 请检查日志, 返回码: {}".format(ret[0]))

        ret = cmd.run_shell(
            cmds=["python x51_win_smoke_deploy_mgc_automation_script.py --manual false"],
            workdir=os.path.join(self.__workdir, "ui_auto_frame", "DeployScripts", "x51_smoke"),
        )
        if ret[0] != 0:
            log.error("return {}".format(ret[1]))
            raise PyframeException("准备测试相关环境失败, 请检查日志, 返回码: {}".format(ret[0]))

    def start_ui_frame_smoke(self):
        run_cmd_file = os.path.join(self.__workdir, "ui_auto_frame", "PyCaseRunner", "run_case.cmd")
        if os.path.exists(run_cmd_file) == False:
            raise FileNotFoundError("run_case.cmd未找到, 请检测步骤")
        cmd_list = []
        with open(run_cmd_file, "r", encoding="gb2312") as f:
            for ann in f.readlines():
                cmd_list.append(ann)
        ret = cmd.run_shell(cmds=[cmd_list[-1]], workdir=os.path.join(self.__workdir, "ui_auto_frame", "PyCaseRunner"))
        if ret[0] != 0:
            log.error("return {}".format(ret[1]))
            raise PyframeException("运行测试用例失败, 请检查日志, 返回码: {}".format(ret[0]))

    # def __modify_presstest_config(self):
    #     """
    #     修改压测配置 press_test_config.xml
    #     """
    #     config_path = os.path.join(self.__whitebox_exe_path, "press_test")
    #     # 读取version_control_android_app.xml version_control_android_res.xml对应配置
    #     version_control_android_app_path = os.path.join(self.__whitebox_exe_path, "resources/config/server/h3d/version_control_android_app.xml")
    #     app_version = self.__get_app_version(version_control_android_app_path, "App", "Version")
    #     log.info("app version in version_control_android_app.xml is {}".format(app_version))

    #     version_control_android_res_path = os.path.join(self.__whitebox_exe_path, "resources/config/server/h3d/version_control_android_res.xml")
    #     res_version = self.__get_app_version(version_control_android_res_path, "Resource", "Version")
    #     log.info("res version in version_control_android_res.xml is {}".format(res_version))

    #     cmd.run_shell(
    #         cmds=[
    #             "attrib -r /S /D",
    #         ],
    #         workdir=config_path,
    #     )
    #     cmd.run_shell(
    #         cmds=[
    #             f"{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/Client --attr-name id_base --attr-value \"{env.get('PSTID')}\" -i",
    #             f"{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/Client --attr-name clientCount --attr-value \"{env.get('ROBOT_COUNT_PER_PROCESS')}\" -i",
    #             f"{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/version_list/version --attr-name version_ip --attr-value \"{env.get('SERVER')}\" -i",
    #             f'{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/version --attr-name app_version --attr-value "{app_version}" -i',
    #             f'{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/version --attr-name res_version --attr-value "{res_version}" -i',
    #             f"{self.__gokit_path} xml set press_test_config.xml //PressTestConfig/test_cases --attr-name sel_flow --attr-value \"{env.get('SEL_FLOW')}\" -i",
    #         ],
    #         workdir=config_path,
    #     )

    def __get_app_version(self, xml_path: str, node_name: str, attri_name: str):
        """
        获取version_control_android_app.xml version_control_android_res.xml对应配置
        """
        with open(xml_path, "r", encoding="utf-8") as f:
            domtree = xml.dom.minidom.parse(xml_path)
            rootnode = domtree.documentElement
            return rootnode.getElementsByTagName(node_name)[0].getAttribute(attri_name)

    # def __start_presstest_clients(self):
    #     bin_path = os.path.join(self.__whitebox_exe_path, "bin")
    #     process_count = int(env.get("PROCESS_COUNT"))
    #     robot_count_per_process = int(env.get("ROBOT_COUNT_PER_PROCESS"))
    #     pstid = int(env.get("PSTID"))
    #     sel_flow = int(env.get("SEL_FLOW"))
    #     processes = []
    #     for i in range(process_count):
    #         cmd = f'start PressTestMain.exe "" {sel_flow} {i*robot_count_per_process + pstid} {robot_count_per_process}'
    #         process = multiprocessing.Process(target=self.start_presstest_cmd, args=(cmd, bin_path))
    #         process.daemon = True
    #         process.start()
    #         processes.append(process)
    #     for process in processes:
    #         process.join(10)

    # def start_presstest_cmd(self, cmdStr, bin_path):
    #     cmd.run_shell(cmds=[cmdStr], workdir=bin_path, dot_kill_me=True)

    def __start_presstest_server(self):
        """
        启动linux server
        """
        cmd.run_shell(
            cmds=[
                f"ssh -i id_rsa_160 root@{env.get('SERVER')} -t \"sh  {self.__whitebox_exe_path}/start_global_server.sh\"",
                f"ssh -i id_rsa_160 root@{env.get('SERVER')} -t \"sh  {self.__whitebox_exe_path}/start_allserver.sh\"",
            ],
            workdir=self.__workdir,
        )

    def __clean_log_file(self):
        """
        清理之前运行产生的日志等文件
        """
        # self.__safe_clean(os.path.join(self.__whitebox_exe_path, "logs"), True)
        # self.__safe_clean(os.path.join(self.__whitebox_exe_path, "robot_test/output"), True)
        # self.__safe_clean(os.path.join(self.__whitebox_exe_path, "robot_test/robot_test_config.local.xml"))
        # self.__safe_clean(os.path.join(self.__whitebox_exe_path, "robot_test/checkData/*.*"))
        # self.__safe_clean(os.path.join(self.__whitebox_exe_path, "*.zip"))

    def __safe_clean(self, file_path: str, is_dir: bool = False):
        if is_dir:
            if not os.path.exists(file_path):
                os.makedirs(file_path)
            shutil.rmtree(file_path)
            os.makedirs(file_path)
            return
        cmd.run_shell(cmds=[f"del /q {os.path.basename(file_path)}"], workdir=os.path.dirname(file_path))

    # def __get_encoding(self):
    #     self.__encoding = "utf-8"
    #     if env.get("CLIENT") == "*************":
    #         self.__encoding = "gb18030"
