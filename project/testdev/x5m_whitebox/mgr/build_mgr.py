# coding=utf-8

from frame import *


class BuildMgr:
    def __init__(self, workdir: str, branch: str):
        self.__workdir = workdir
        self.__branch = branch
        self.__client_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/client")
        self.__ms_build = '"C:/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/MSBuild/Current/Bin/MSBuild.exe"'

    @advance.timeout(seconds=30, exception_msg="编译白盒超时")
    def build_press_test_main(self):
        """
        编译press_test_main
        """
        ret = cmd.run_shell(
            cmds=[f"{self.__ms_build} -t:Build PressTestMain.sln -fl -flp:logfile=PressTestMain.log"],
            workdir=self.__client_path,
        )
        if ret[0] != 0:
            press_test_main_errors = self.__parse_build_errors(build_log=os.path.join(self.__client_path, "PressTestMain.log"))
            raise PyframeException(
                f"编译PressTestMain.sln失败, {self.__ms_build} -t:Build PressTestMain.sln error!\n build errors: {press_test_main_errors}"
            )

    def __parse_build_errors(self, build_log: str) -> list:
        """
        解析编译错误
        """
        if not path_mgr.exists(build_log):
            log.debug(f"build log {build_log} not exists")
            return []
        f = open(build_log, "r")
        line = f.readline()
        errors = []
        while line:
            if "): error" in line and ".cs" in line:
                format_line = line.replace("/", "/").strip()
                if format_line not in errors:
                    errors.append(format_line)
            line = f.readline()
        f.close()
        return errors
