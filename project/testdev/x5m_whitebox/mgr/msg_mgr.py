# coding=utf-8
import json
from frame import *
from project.testdev.x5m_whitebox import config


class MsgMgr:
    @staticmethod
    def get_jenkins_msg() -> str:
        try:
            msg = f"**测试用例ID**: {env.get('TESTCASE_ID')}\n"
            msg += f"**需求文件名**: [{env.get('FILE_NAME')}](https://www.kdocs.cn/p/{env.get('IS_DOWNLOAD_REQ')}?fl=scIUQYRgT&from=docs&source=docsWeb)\n"
            msg += f"**页签名**: {env.get('TAB_NAME')}\n"
            msg += f"**版本分支**: {env.get('BRANCH_NAME')}\n"
            msg += f"**周更分支**: {env.get('VERSION')}\n"
            msg += f"**多语言配置**: {env.get('LANGUAGE_INDEX', '0')}\n"
            report_url = env.get("REPORTURL")
            if report_url:
                msg += "**报表链接**: [{}]({})\n".format(report_url.split("/")[-1], report_url)
            log_url = env.get("LOGURL")
            if log_url:
                msg += "**日志链接**: [{}]({})".format(log_url.split("/")[-1], log_url)
        except Exception as e:
            msg = f"**拼接消息失败**: {str(e)}"
        return msg

    @staticmethod
    def get_requests_msg(url, response) -> str:
        msg = f"**GoProxyUrl**: {url}\n"
        msg += f"**状态码Status_code**: {response.status_code}\n"
        if response.status_code == 200:
            response_json = response_json = json.loads(response.text)
            msg += f"**错误码Error_code**: {response_json['code']}\n"
            msg += f"**错误信息Error_msg**: {response_json['error_msg']}\n"
        else:
            msg += f"**错误信息**: {response.text}\n"
        return msg

    @staticmethod
    def send_request_error(msg) -> None:
        wechat.send_unicast_post_failure(
            user_list=config.TESTDEV_X5M_DEVELOPER + config.EFFICIENCY_DEVELOPER, content=msg, to_admin=False, rescue=False
        )
