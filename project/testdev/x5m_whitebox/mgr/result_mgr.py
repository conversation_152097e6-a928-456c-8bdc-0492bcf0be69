# coding=utf-8

import json
import time
from frame import *
from project.testdev.x5m_whitebox import config


class ResultMgr:
    def __init__(self, workdir: str, branch: str):
        self.__workdir = workdir
        self.__branch = branch
        self.__output_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe/robot_test/output")
        self.__tested_data_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe/robot_test/testedFilesData")
        self.__result_file = os.path.join(self.__output_path, "ConfigCheckResult.txt")
        # self.__commit_files_set = {"totalFilesList.txt", "testedFilesList.json", "testedBranchs.txt"}
        # 白盒测试流水线不再记录全量文件列表
        self.__commit_files_set = {"testedFilesList.json", "testedBranchs.txt", "maxPipelineId.txt"}

    def get_white_box_check_result(self) -> str:
        if "0" not in env.get("TEST_LEVEL"):
            return
        result = ""
        with open(self.__result_file, "rb") as file:
            result = file.read().decode("utf-8-sig")
        env.set({"WhiteBoxTestResult": result})

    def delete_whitebox_result(self):
        """
        删除白盒反测用例生成的文件
        """
        activityId = env.get("ACTIVITY_ID")
        if activityId == "98":
            path_mgr.rm(self.__result_file)
        if activityId == "99":
            for file in os.listdir(self.__output_path):
                test_result_path = os.path.join(self.__output_path, file)
                if path_mgr.is_dir(test_result_path):
                    path_mgr.rm(test_result_path)
                    path_mgr.mkdir(test_result_path)

    def check_whitebox_result(self):
        if not os.path.exists(self.__result_file):
            raise PyframeException(f"No WhiteBoxCheckResults file found under `{self.__result_file}`")
        for file in os.listdir(self.__output_path):
            if file in {"demandFilesBak", "logs", "configBak", "buildLogsAndTestResultTemp"}:
                continue
            test_result_path = os.path.join(self.__output_path, file)
            if path_mgr.is_dir(test_result_path):
                for testlevel in env.get("TEST_LEVEL").split("|"):
                    test_result = config.TESTLEVELDICT[testlevel]
                    for filename in os.listdir(test_result_path):
                        if filename.startswith(test_result):
                            log.info(f"Find {test_result} file under {test_result_path}")
                            break
                    else:
                        raise PyframeException(f"No {test_result} file found under `{test_result_path}`")

    def update_tested_file_data(self, retry_count=5):
        # if env.get("WhiteBoxTestResult") != "pass":
        #     return
        retry_index = 0
        while retry_index < retry_count:
            try:
                if not path_mgr.exists(self.__tested_data_path):
                    self.__make_tested_file()
                else:
                    self.__update_x5mbolie()
                self.__update_tested_branch()
                self.__update_tested_files_list()
                # self.__update_total_files_list()
                self.__update_max_pipeline_id()
                self.__commit_tested_file_data()
                return
            except:
                retry_index += 1
                log.warning("Update testedFilesList error! Try again")

        """
        如果测试用例不检查配置文件且配置没有更新
        那么 testedFilesList.json 和 maxPipelineId.txt 没有改变
        上传一定失败，因此取消异常抛出改为日志打印
        """
        # raise PyframeException("Update testedFilesList error!")
        log.error("Update testedFilesList error!")

    def __update_x5mbolie(self):
        self.x5mobile_git_mgr = GitMgr(workdir=self.__workdir, project_name=os.path.join("x5mobile", self.__branch))
        for deleted_file in self.__commit_files_set:
            self.x5mobile_git_mgr.exec(f"git checkout -- {os.path.join(self.__tested_data_path, deleted_file)}")
        self.x5mobile_git_mgr.advance_pull(branch=self.__branch)

    def __make_tested_file(self):
        path_mgr.mkdir(self.__tested_data_path)
        if not path_mgr.exists(os.path.join(self.__tested_data_path, "testedBranchs.txt")):
            with open(os.path.join(self.__tested_data_path, "testedBranchs.txt"), "w", encoding="utf-8") as f:
                f.write(f"{env.get('BRANCH_NAME')}")
        # if not path_mgr.exists(os.path.join(self.__tested_data_path, "totalFilesList.txt")):
        #     with open(os.path.join(self.__tested_data_path, "totalFilesList.txt"), "w", encoding="utf-8") as f:
        #         pass
        if not path_mgr.exists(os.path.join(self.__tested_data_path, "testedFilesList.json")):
            with open(os.path.join(self.__tested_data_path, "testedFilesList.json"), "w", encoding="utf-8") as f:
                pass
        if not path_mgr.exists(os.path.join(self.__tested_data_path, "maxPipelineId.txt")):
            with open(os.path.join(self.__tested_data_path, "maxPipelineId.txt"), "w", encoding="utf-8") as f:
                pass

    def __update_tested_branch(self):
        # 判断当前分支是否测过
        tested_brach = False
        with open(os.path.join(self.__tested_data_path, "testedBranchs.txt"), "r", encoding="utf-8") as f:
            for line in f.readlines():
                line = line.strip()
                if line == env.get("BRANCH_NAME"):
                    tested_brach = True
                    break
        if not tested_brach:
            with open(os.path.join(self.__tested_data_path, "testedBranchs.txt"), "a", encoding="utf-8") as f:
                f.write(f"\n{env.get('BRANCH_NAME')}")
            # 如果是新分支则需要把之前的已测列表清空
            with open(os.path.join(self.__tested_data_path, "testedFilesList.json"), "w", encoding="utf-8") as f:
                pass

    def __update_tested_files_list(self):
        tested_file_list_old = {}
        tested_file_list_now = set()
        for file in os.listdir(os.path.join(self.__output_path, "configBak")):
            if file.endswith("zip"):
                continue
            tested_file_list_now.add(file)
        with open(os.path.join(self.__tested_data_path, "testedFilesList.json"), "r", encoding="utf-8") as f:
            strTemp = ""
            for line in f.readlines():
                strTemp += line
            if strTemp != "":
                tested_file_list_old = json.loads(strTemp)
        now = time.strftime(config.TIME_FORMAT)
        testid = env.get("TESTCASE_ID", "").split(",")[0]
        for file in tested_file_list_now:
            if file not in tested_file_list_old:
                tested_file_list_old[file] = {testid: [now]}
            elif testid not in tested_file_list_old[file]:
                tested_file_list_old[file][testid] = [now]
            else:
                tested_file_list_old[file][testid].append(now)
        with open(os.path.join(self.__tested_data_path, "testedFilesList.json"), "w", encoding="utf-8") as f:
            json.dump(tested_file_list_old, f, indent=4)
        self.tested_file_set = tested_file_list_now

    def __update_max_pipeline_id(self):
        max_pipeline_id = env.get("MAXPIPELINEID")
        with open(os.path.join(self.__tested_data_path, "maxPipelineId.txt"), "r", encoding="utf-8") as f:
            for line in f.readlines():
                line = int(line.strip())
                max_pipeline_id = max(max_pipeline_id, line)
        with open(os.path.join(self.__tested_data_path, "maxPipelineId.txt"), "w", encoding="utf-8") as f:
            f.write(str(max_pipeline_id))

    def __update_total_files_list(self):
        all_files = dict()
        with open(os.path.join(self.__tested_data_path, "totalFilesList.txt"), "r", encoding="utf-8") as f:
            for line in f.readlines():
                filename, root = line.strip().split(":")
                all_files[filename] = root
        hotfix_files = self.__get_all_file_with_root(os.path.join(self.__workdir, "x5mconfig/hotfix/onlineupdate", env.get("CONFIG_BRANCH_NAME")))
        week_files = self.__get_all_file_with_root(os.path.join(self.__workdir, "x5mweek", env.get("VERSION")))
        all_files.update(hotfix_files)
        all_files.update(week_files)
        for file in self.tested_file_set:
            if file not in all_files:
                all_files[file] = "null"
        all_files = sorted(all_files.items(), key=lambda a: a[0])
        with open(os.path.join(self.__tested_data_path, "totalFilesList.txt"), "w", encoding="utf-8") as f:
            f.writelines("\n".join(map(lambda a: ":".join(a), all_files)))

    def __get_all_file_with_root(self, root_dir):
        """
        筛选目标路径中server和shared子目录下的xml和csv
        """
        filedict = dict()
        for root, _, files in os.walk(root_dir):
            if "server" in root:
                root = "server" + root.split("server")[-1]
            elif "shared" in root:
                root = "shared" + root.split("shared")[-1]
            else:
                continue
            for file in files:
                if file.endswith("csv") or file.endswith("xml"):
                    filedict[file] = root
        return filedict

    def __commit_tested_file_data(self):
        for commit_file in self.__commit_files_set:
            self.x5mobile_git_mgr.add(os.path.join(self.__tested_data_path, commit_file))
        log.info(f'The followed files `{"; ".join(self.__commit_files_set)}` will be commit')
        self.x5mobile_git_mgr.commit(
            f"Update testedFilesList !\n testcasesID: `{env.get('TESTCASE_ID', '').split(',')[0]}`, weeklyBranch: `{env.get('VERSION')}`"
        )

        self.x5mobile_git_mgr.push(self.__branch)
