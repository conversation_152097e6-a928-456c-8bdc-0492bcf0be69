# coding=utf-8
import paramiko
from frame import *


class Ssh:
    def __init__(self, workdir: str, hostname: str, branch: str, username: str = "root", port=22):
        self.__workdir = workdir
        self.__branch = branch
        self.__hostname = hostname
        self.__port = port
        self.__username = username
        private_key_dir = f"{self.__workdir}/x5mobile/{self.__branch}/mobile_dancer/trunk/exe/bin/id_rsa_160"
        if not os.path.exists(private_key_dir):
            raise PyframeException(f"private_key_dir  {private_key_dir} not exist")
        self.__private_key = paramiko.RSAKey.from_private_key_file(private_key_dir)

    def run_cmd(self, cmd):
        _, stdout, stderr = self.sshClient.exec_command(cmd)
        out = stdout.read().decode()
        if out:
            log.info(out)
        # 获取远程返回的信息,既可以捕获异常信息,又可以保证该条命令执行完在执行下一条
        err = stderr.read().decode()
        if err:
            log.warning(err)
        return out, err

    def upload_file(self, local_path, remote_path):
        # local_md5 = common.compute_md5(local_path)
        # log.info(f"local file md5 is {local_md5}")
        cnt = 3
        while cnt > 0:
            if not os.path.exists(local_path):
                log.error("没有拉到对应版本的热更包,或者本地打包失败,请在 {} 路径下查看".format(local_path))
                return
            # self.sftpClient.put(local_path, remote_path)
            # remote_md5, _ = self.run_cmd(f"md5sum {remote_path}")
            # remote_md5 = remote_md5.split(" ")[0]
            # log.info(f"remote file md5 is {remote_md5}")
            # if remote_md5 == local_md5:
            #     log.info(f"upload local file `{local_path}` to remote `{remote_path}` success")
            #     break
            # log.error(f"upload local file `{local_path}` to remote `{remote_path}` error")
            # log.error(f"remote file md5 `{remote_md5}` not equal to local file md5 `{local_md5}`")
            # cnt -= 1

            # sftpClient.put 中包含文件大小的校验 取消md5的验证
            try:
                self.sftpClient.put(local_path, remote_path)
                log.info(f"upload local file `{local_path}` to remote `{remote_path}` success")
                break
            except Exception as e:
                log.error(f"upload local file `{local_path}` to remote `{remote_path}` error, errormsg: {str(e)}")
                cnt -= 1

    def __enter__(self):
        self.sshClient = paramiko.SSHClient()
        # 如果之前没有连接过的ip,会出现选择yes或者no的操作,自动选择yes
        self.sshClient.set_missing_host_key_policy(paramiko.AutoAddPolicy)
        try:
            self.sshClient.connect(hostname=self.__hostname, port=self.__port, username=self.__username, pkey=self.__private_key)
            # sftp上传或下载
            tran = self.sshClient.get_transport()
            self.sftpClient = paramiko.SFTPClient.from_transport(tran)
        except:
            raise PyframeException(f"连接{self.__hostname}失败")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.sshClient.close()
        self.sftpClient.close()
