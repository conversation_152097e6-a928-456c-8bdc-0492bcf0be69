# coding=utf-8

# import zipfile
import time
from frame import *


class ZipMgr:
    def __init__(self, workdir: str, branch: str):
        self.__workdir = workdir
        self.__branch = branch

    def copy_converseFile(self):
        if path_mgr.exists(os.path.join(self.__workdir, "configConversion/HttpConfig.zip")):
            path_mgr.copy(
                src=os.path.join(self.__workdir, "configConversion/HttpConfig.zip"),
                dst=os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe/robot_test/output/configBak"),
            )

    def copy_requestFile_and_buildlogFile(self):
        """
        复制白盒编译日志
        复制需求文档、同步表、美术需求表、部位兑换表、玩家自制歌曲ID列表（如果存在）
        """
        root_dir = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe/robot_test/checkData")
        dst = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe/robot_test/output/demandFilesBak")
        path_mgr.mkdir(dst)
        # src_list = ["Synchronization_table", "Player_homemade_table", "Arts_entrance_table"]
        src_list = ["Synchronization_table", "Arts_entrance_table"]
        for file in os.listdir(root_dir):
            src = os.path.join(root_dir, file)
            if path_mgr.is_file(src) or file in src_list:
                path_mgr.copy(src=src, dst=dst)
        path_mgr.copy(
            os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/client/PressTestMain.log"),
            os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe/robot_test/output/buildLogsAndTestResultTemp"),
        )

    def make_report_zip(self):
        self.__make_report_by_dir("output", "logs")
        outputpath = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe/robot_test/output")
        for file in os.listdir(outputpath):
            if file in {"demandFilesBak", "logs", "configBak", "buildLogsAndTestResultTemp"}:
                continue
            if not path_mgr.is_dir(os.path.join(outputpath, file)):
                continue
            self.__make_report_by_dir("output/" + file, "reports")

    def __make_report_by_dir(self, dir: str, zipfilename: str):
        now = time.strftime("%Y-%m-%d")
        zipfilename += f"_{env.get('BUILD_TAG')}_{now}.zip"
        src = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe/robot_test", dir)
        dst = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe", zipfilename)
        # try:
        #     zip = zipfile.ZipFile(dst, "w", zipfile.ZIP_DEFLATED)
        #     for path, _, filenames in os.walk(src):
        #         fpath = path.replace(src, "")
        #         for filename in filenames:
        #             zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
        #     zip.close()
        # except:
        #     raise PyframeException(f"压缩 {src} 文件失败")
        tar.compress(src=src, dst=dst)
        zip_url = advance.upload_pipeline_log(dst)
        if "log" in zipfilename:
            # env.set({"LOGURL": f"{env.get('BUILD_URL')}artifact/{zipfilename}"})
            env.set({"LOGURL": zip_url})
        else:
            # env.set({"REPORTURL": f"{env.get('BUILD_URL')}artifact/{zipfilename}"})
            env.set({"REPORTURL": zip_url})
