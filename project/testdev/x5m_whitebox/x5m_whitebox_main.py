# coding=utf-8
# import shutil
import re
from frame import *

from project.testdev.x5m_whitebox import config
from project.testdev.x5m_whitebox.mgr.x5m_git_mgr import X5mGitMgr
from project.testdev.x5m_whitebox.mgr.p4_mgr import P4COM1666
from project.testdev.x5m_whitebox.mgr.makelink_mgr import MakeLinkMgr
from project.testdev.x5m_whitebox.mgr.config_mgr import ConfigMgr
from project.testdev.x5m_whitebox.mgr.build_mgr import BuildMgr
from project.testdev.x5m_whitebox.mgr.test_mgr import TestMgr
from project.testdev.x5m_whitebox.mgr.report_mgr import ReportMgr
from project.testdev.x5m_whitebox.mgr.zip_mgr import ZipMgr
from project.testdev.x5m_whitebox.mgr.result_mgr import ResultMgr
from project.testdev.x5m_whitebox.mgr.msg_mgr import MsgMgr


@advance.stage(stage="开始测试")
def start_test():
    """
    开始测试,主要用来发通知
    """
    report_mgr = ReportMgr()
    report_mgr.init_request_by_env()
    report_mgr.send_request()
    __param_check()


@advance.stage(stage="准备")
def prepare():
    branch = env.get("BRANCH_NAME")
    log.info(branch)
    client = common.get_host_ip()
    server = config.CLIENT_SERVER_MAP[client]

    env.set(
        {
            "CONFIG_BRANCH_NAME": branch.split("_")[0],
            "CLIENT": client,
            "SERVER": server,
            "WORKSPACE": config.X5M_JENKINS_WORKSPACE,
            "JenkinsState": "start",
            "WhiteBoxTestResult": "TBD",
        }
    )
    # 手动执行则通过ftp拿热更包,远程调用(ruunid不为0)通过本地打包上传配置
    if env.get("RUN_ID", "0") != "0":
        env.set(
            {
                "GETCONFIGFROMLOCAL": "True",  # 本地热更包上传
                "GETCONFIGFROMLOCALFULL": "True",  # 本地全量包上传
            }
        )


def __param_check():
    test_level = env.get("TEST_LEVEL")
    right_str = {"0", "1", "2", "3", "|"}
    for item in test_level:
        if item not in right_str:
            raise PyframeException(f"test_level has `{item}` not in `{'、'.join(right_str)}`")

    weekly_branch = env.get("VERSION")
    if weekly_branch not in {"resources", "release", "online"}:
        raise PyframeException("weekly branch should be one of 'resources'、'release'、'online'")


@advance.stage(stage="更新gitlab")
def update_gitlab():
    branch = env.get("BRANCH_NAME")
    x5m_git_mgr = X5mGitMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch)
    x5m_git_mgr.update_x5mobile()
    x5m_git_mgr.update_x5mconfig()
    x5m_git_mgr.update_x5mweek()
    x5m_git_mgr.set_max_pipeline_id()


@advance.stage(stage="更新p4")
def update_p4():
    config_branch = env.get("CONFIG_BRANCH_NAME")
    p4_com_1666 = P4COM1666(branch=config_branch, workdir=config.X5M_JENKINS_WORKSPACE)
    p4_com_1666.sync_all()


@advance.stage(stage="拷贝配置")
def copy_and_makelink_config():
    branch = env.get("BRANCH_NAME")
    config_branch = env.get("CONFIG_BRANCH_NAME")
    make_link_mgr = MakeLinkMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch, config_branch=config_branch)
    make_link_mgr.make_link()
    weekly_branch = env.get("VERSION")
    config_mgr = ConfigMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch, config_branch=config_branch, weekly_branch=weekly_branch)
    config_mgr.copy_config()


@advance.stage(stage="编译")
def build_whitebox():
    branch = env.get("BRANCH_NAME")
    build_mgr = BuildMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch)
    build_mgr.build_press_test_main()


@advance.stage(stage="测试用例")
def run_testcase():
    branch = env.get("BRANCH_NAME")
    config_branch = env.get("CONFIG_BRANCH_NAME")
    test_mgr = TestMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch, config_branch=config_branch)
    test_mgr.run_whitebox()


@advance.stage(stage="生成报告")
def make_report_zip():
    branch = env.get("BRANCH_NAME")
    zip_mgr = ZipMgr(workdir=config.X5M_JENKINS_WORKSPACE, branch=branch)
    zip_mgr.copy_converseFile()
    zip_mgr.copy_requestFile_and_buildlogFile()
    zip_mgr.make_report_zip()


def on_success():
    workdir = config.X5M_JENKINS_WORKSPACE
    branch = env.get("BRANCH_NAME")
    result_mgr = ResultMgr(workdir=workdir, branch=branch)
    if env.get("TESTCASE_ID", "").split(",")[0] == "0|100|42":
        result_mgr.delete_whitebox_result()
    # 暂时不检查白盒结果文件是否存在
    # result_mgr.check_whitebox_result()
    result_mgr.get_white_box_check_result()
    result_mgr.update_tested_file_data()
    env.set({"JenkinsState": "success"})
    wechat.send_unicast_post_success(user_list=config.TESTDEV_X5M_DEVELOPER, content=MsgMgr.get_jenkins_msg())
    advance.insert_pipeline_history_on_success()


def on_unstable():
    env.set({"JenkinsState": "unstable"})
    wechat.send_unicast_post_unstable(user_list=config.TESTDEV_X5M_DEVELOPER, content=MsgMgr.get_jenkins_msg())
    advance.insert_pipeline_history_on_unstable()


def on_failure():
    env.set({"JenkinsState": "failure"})
    wechat.send_unicast_post_failure(user_list=config.TESTDEV_X5M_DEVELOPER, content=MsgMgr.get_jenkins_msg(), to_admin=True, rescue=False)
    advance.insert_pipeline_history_on_failure()


def on_canceled():
    env.set({"JenkinsState": "canceled"})
    wechat.send_unicast_post_canceled(user_list=config.TESTDEV_X5M_DEVELOPER, content=MsgMgr.get_jenkins_msg())
    advance.insert_pipeline_history_on_canceled()


def end_test():
    """
    测试结束,包括结果的解析和日志、报告链接的发送
    """
    report_mgr = ReportMgr()
    report_mgr.init_request_by_env(env.get("JenkinsState"))
    report_mgr.send_request()
    __clear_workspace()


def __clear_workspace():
    """
    清理工作目录
    """
    for filename in os.listdir(config.X5M_JENKINS_WORKSPACE):
        if filename.endswith("@tmp") and "pyframe-pipeline" not in filename:
            path_mgr.rm(os.path.join(config.X5M_JENKINS_WORKSPACE, filename[:-4]))
            path_mgr.rm(os.path.join(config.X5M_JENKINS_WORKSPACE, filename))
    if path_mgr.exists(os.path.join(config.X5M_JENKINS_WORKSPACE, "configTemp")):
        path_mgr.rm(os.path.join(config.X5M_JENKINS_WORKSPACE, "configTemp"))
    if path_mgr.exists(os.path.join(config.X5M_JENKINS_WORKSPACE, "configConversion")):
        path_mgr.rm(os.path.join(config.X5M_JENKINS_WORKSPACE, "configConversion"))
    # 只保留最近两个分支
    x5_mobile_mr_b_path = os.path.join(config.X5M_JENKINS_WORKSPACE, "x5_mobile/mr/b")
    x5mconfig_path = os.path.join(config.X5M_JENKINS_WORKSPACE, "x5mconfig")
    x5mobile_path = os.path.join(config.X5M_JENKINS_WORKSPACE, "x5mobile")
    __clear_unused_folder(x5_mobile_mr_b_path)
    __clear_unused_folder(x5mconfig_path)
    __clear_unused_folder(x5mobile_path)


def __clear_unused_folder(root: str, save_count: int = 2) -> None:
    """
    清理旧的分支，包括代码(x5mobile)、配置(x5mconfig)和资源(x5_mobile)
    默认保留两个分支

    Args:
        root (str): 根目录
        save_count (int): 要保留的文件夹数量
    """
    file_list = []
    for file in os.listdir(root):
        if re.search("^[0-9]\.[0-9][0-9]\.[0-9]$", file) or re.search("^[0-9]\.[0-9][0-9]\.[0-9](_wb)$", file):
            file_list.append(file)
    file_list.sort()
    if len(file_list) > save_count:
        delete_file_list = file_list[: len(file_list) - save_count]
        for filename in delete_file_list:
            path = os.path.join(root, filename)
            cmd.run_shell(['rd /S /Q "{}"'.format(path)])
