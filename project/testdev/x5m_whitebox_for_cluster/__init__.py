# coding=utf-8
import os
from frame import env


class Config:
    TESTDEV_X5M_DEVELOPER = [
        # "<EMAIL>",
        # "<EMAIL>",
        "<EMAIL>"
    ]
    # 效能组开发人员，用于Jenkins回调失败后发送错误
    EFFICIENCY_DEVELOPER = [
        "<EMAIL>",
    ]
    GIT_URL = {
        "x5mobile": "http://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git",
        "x5mconfig": "http://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig.git",
        "x5mweek": "http://x5mobile-gitlab.h3d.com.cn/dgm/x5mweek.git",
    }
    GITLAB_MAINTAINER = {
        "url": "http://x5mobile-gitlab.h3d.com.cn/",
        "username": "<EMAIL>",
        "password": "maintainer123",
        "token": "o-yNgwVADiQ8CicYdt_4",
    }
    # x5m jenkins流水线p4账号
    P4_1666_CONFIG = {
        "host": "x5_mobile.p4.com:1666",
        "username": "dgm_jenkins",
        "password": "x5m12345",
    }
    CLIENT_SERVER_MAP = {
        "************": "**************",
        "*************": "**************",
    }
    CLIENT_IP_SET = []
    SERVER_IP_SET = ["**************", "**************"]
    # CONNECT_SERVER = ""
    GO_PROXY_URI = {
        "ip": "************",
        # "ip": "*************",
        "port": "8080",
        "handler": "jenkinsStateForward",
    }
    Go_CONNECT_CONFIG = {
        "connect_timeout": 3,
        "read_timeout": 10,
        "download_timeout": 30,
    }
    FTP_CONFIG = {
        "ip": "*************",
        "port": 21,
        "user": "administrator",
        "password": "dgm123!#*",
        "dir": "/version_test/update_config/{}",
    }
    X5M_JENKINS_WORKSPACE = os.path.dirname(env.pipeline.workspace())

    # 软链接映射关系
    LINKMAPPING = {
        # 服务端配置映射
        "server_config": [
            # {"config/server": "mobile_dancer/trunk/exe/resources/config/server"},
            # {"config/shared": "mobile_dancer/trunk/exe/resources/config/shared"},
            {"config": "mobile_dancer/trunk/exe/resources/config"},
            {"level": "mobile_dancer/trunk/exe/resources/level"},
            {"experience": "mobile_dancer/trunk/exe/resources/experience"},
        ],
        # 服务端配置映射,但是他的根目录比较特殊,所以单独列出来
        "tencent_xg": [
            {"mobile_dancer/trunk/client_build_config/config/shared/tencent_xg": "mobile_dancer/trunk/exe/resources/config/shared/tencent_xg"},
        ],
        # 客户端资源映射
        "client_config": [
            {"ResourcePublish/CDN/SourceFiles/android/assetbundles": "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles"},
            {"ResourcePublish/CDN/SourceFiles/crossplatform/config": "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/config"},
            {"ResourcePublish/CDN/SourceFiles/crossplatform/level": "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/level"},
            {"ResourcePublish/CDN/SourceFiles/crossplatform/luascript": "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/luascript"},
            {"ResourcePublish/CDN/SourceFiles/crossplatform/luascript": "mobile_dancer/trunk/client/GameLuaProject/luascript"},
        ],
    }
    # jenkins工具:gokit,用来修改文件中的内容
    GOKIT_PATH = "D:/jenkins_workspace/tools/com.cloudbees.jenkins.plugins.customtools.CustomTool/gokit/gokit.exe"
    # ssh 密码
    SSH_PASSWORD = "Centos78"
    # ssh密钥
    SSHKEYFILE = """*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
    # 测试等级和测试结果文件的对应关系
    TESTLEVELDICT = {
        "0": "ConfigCheck",
        "2": "ProbabilityCheck",
        "3": "KeChuan",
    }

    TIME_FORMAT = "%Y-%m-%d %H:%M"


config = Config()
