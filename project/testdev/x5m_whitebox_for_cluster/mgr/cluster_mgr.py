# coding=utf-8

from functools import partial
import multiprocessing
import shutil
import zipfile
import xml.dom.minidom
from frame import *
from project.testdev.x5m_whitebox_for_cluster import config
from project.testdev.x5m_whitebox.mgr.download_request_file_mgr import Download
from project.testdev.x5m_whitebox_for_cluster.mgr.ssh_mgr import Ssh
import os


class ClusterMgr:
    def __init__(self, workdir: str, branch: str, config_branch: str):
        self.__workdir = workdir
        self.__branch = branch
        self.__config_branch = config_branch
        self.__whitebox_exe_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/exe")
        self.__gokit_path = config.GOKIT_PATH
        self.__nunit3_dir = os.path.join(
            self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/client/packages/NUnit.ConsoleRunner.3.11.1/tools/nunit3-console.exe"
        )
        self.__whitebox_dir = os.path.join(self.__whitebox_exe_path, "bin/PressTestMain.exe")
        self.__test_run_plan_dir = os.path.join(self.__whitebox_exe_path, "bin/test_run_plan")
        # self.__init_server_set()

    def start_cluster_server(self):
        self.__run_case("StartClusterServer")

    def run_whitebox(self):
        self.__get_encoding()
        self.__modify_whitebox_config()
        self.__upload_to_server()
        self.__clean_log_file()
        self.__download_request_file()
        self.__getandupload_config()
        self.__run_white_box()

    def __modify_whitebox_config(self):
        """
        修改白盒配置 robot_test_config.xml config.ini
        """
        config_path = os.path.join(self.__whitebox_exe_path, "robot_test")
        cmd.run_shell(
            cmds=[
                "attrib -r /S /D",
            ],
            workdir=config_path,
        )
        cmd.run_shell(
            cmds=[
                f"{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/test_env --attr-name host --attr-value \"{env.get('SERVER')}\" -i",
                f'{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/test_env --attr-name pwd --attr-value "{config.SSH_PASSWORD}" -i',
                f"{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/test_env --attr-name docker_version --attr-value \"{env.get('CONFIG_BRANCH_NAME')}-{env.get('VERSION')}-ssh\" -i",
                f"{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/version_list/version --attr-name version_ip --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/server_list/cluster_ssh --attr-name docker_version --attr-value \"{env.get('CONFIG_BRANCH_NAME')}-{env.get('VERSION')}-ssh\" -i",
                f"{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/server_list/cluster_ssh --attr-name host --attr-value \"{env.get('SERVER')}\" -i",
                f'{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/server_list/cluster_ssh --attr-name password --attr-value "{config.SSH_PASSWORD}" -i',
                f"{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/server_list/server --attr-name ip --attr-value \"{env.get('SERVER')}\" -i",
                f"attrib -r config.ini",
                f"{self.__gokit_path} ini set config.ini total_process \"{env.get('TOTAL_PROCESS')}\" -i",
                f"{self.__gokit_path} ini set config.ini local_process \"{env.get('LOCAL_PROCESS')}\" -i",
                f"{self.__gokit_path} ini set config.ini restart_cluster_server \"{env.get('RESTART_CLUSTER_SERVER')}\" -i",
                f"{self.__gokit_path} ini set config.ini cluster_server true -i",
                f"{self.__gokit_path} ini set config.ini local_server false -i",
                f"{self.__gokit_path} ini set config.ini use_plan_prefab \"{env.get('TESTCASE_ID').split(',')[0]}\" -i",
                f"{self.__gokit_path} ini set config.ini prefab_server_time \"{env.get('TESTCASE_TIME')}\" -i",
                f"{self.__gokit_path} ini set config.ini prefab_campaign_id \"{env.get('ACTIVITY_ID')}\" -i",
                f"{self.__gokit_path} ini set config.ini use_k8s \"{env.get('ISK8SSERVER')}\" -i",
                f"{self.__gokit_path} ini set config.ini demand_file_source_type \"{env.get('DEMAND_FILE_SOURCE_TYPE')}\" -i",
                f"{self.__gokit_path} ini set config.ini week_config \"{env.get('VERSION')}\" -i",
                f"{self.__gokit_path} ini set config.ini dev_env \"{env.get('IS_DEV_ENV')}\" -i",
                f"{self.__gokit_path} ini set config.ini test_level \"{env.get('TEST_LEVEL')}\" -i",
                f"{self.__gokit_path} ini set config.ini run_id \"{env.get('RUN_ID')}\" -i",
                f"{self.__gokit_path} ini set config.ini is_test_jenkins \"{env.get('IS_TEST_JENKINS')}\" -i",
            ],
            workdir=config_path,
        )
        # 改启服务配置
        cmd.run_shell(
            cmds=[
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/GatewayOutter/OUTTER_IP/ip --attr-name value --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/Gateway/Server --attr-name INNER_IP --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/Transmit/Server --attr-name INNER_IP --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/CrossZoneTransmit/Server --attr-name INNER_IP --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/VersionServer/Server --attr-name INNER_IP --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/mysql_config/Server --attr-name ip --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/MusicWebServer/Server --attr-name INNER_IP --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/MusicWebServer/Server --attr-name INNER_IP --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/RedisServer/Server[@ID='1'] --attr-name ip --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/RedisServer/Server[@ID='1'] --attr-name ip_d --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/RedisServer/Server[@ID='2'] --attr-name ip --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/RedisServer/Server[@ID='2'] --attr-name ip_d --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/RedisServer/Server[@ID='3'] --attr-name ip --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set zone_globals.xml //Globals/RedisServer/Server[@ID='3'] --attr-name ip_d --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set server_globals.xml //Globals/GatewayOutter/OUTTER_IP/ip --attr-name value --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set server_globals.xml //Globals/Gateway/Server --attr-name INNER_IP --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set server_globals.xml //Globals/Transmit/Server --attr-name INNER_IP --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set server_globals.xml //Globals/Version/Server --attr-name INNER_IP --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set server_globals.xml //Globals/Vedio/Server/OUTTER_IP/ip --attr-name INNER_IP --attr-value \"{env.get('SERVER')}\" -i",
                f"{self.__gokit_path} xml set server_globals.xml //Globals/mysql_config/Server --attr-name IP --attr-value \"{env.get('SERVER')}\" -i",
            ],
            workdir=os.path.join(self.__whitebox_exe_path, "robot_test/ClusterConfig"),
        )
        if common.str2bool(env.get("ISK8SSERVER")):
            cmd.run_shell(
                cmds=[
                    f"{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/test_env --attr-name host --attr-value ************** -i",
                    f"{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/version_list/version --attr-name version_ip --attr-value ************** -i",
                    f"{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/test_env --attr-name docker_version --attr-value {env.get('CONFIG_BRANCH_NAME')}-{env.get('VERSION')} -i",
                    f"{self.__gokit_path} xml set robot_test_config.xml //RobotTestConfig/service_manager --attr-name docker_version --attr-value {env.get('CONFIG_BRANCH_NAME')}-{env.get('VERSION')} -i",
                ],
                workdir=config_path,
            )
        cmd.run_shell(
            cmds=[
                f"{self.__gokit_path} ini set config.ini ori_excel_config_name \"{env.get('FILE_NAME')}\" -i",
                f"{self.__gokit_path} ini set config.ini ori_sheet_name \"{env.get('TAB_NAME')}\" -i",
            ],
            workdir=config_path,
            encoding="GBK",
        )

    def __upload_to_server(self):
        """
        连接服务器 上传配置等
        """
        # 这部分为了兼容老的机器用密钥登录，后续都应该改成用用户名密码登录，到时可以废弃这写ssh文件这部分
        bin_path = os.path.join(self.__whitebox_exe_path, "bin")
        # 写一个ssh秘钥文件,访问服务器使用
        id_rsa_160_filepath = os.path.join(bin_path, "id_rsa_160")
        if not os.path.exists(id_rsa_160_filepath):
            with open(id_rsa_160_filepath, "w") as file:
                file.write(config.SSHKEYFILE)
        log.info("__upload_to_server 连接服务器 上传配置等！")
        # 修改reportportal的配置,将debug模式设为false
        cmd.run_shell(
            cmds=[f'{self.__gokit_path} json set ReportPortal.config.json "launch.debugMode" false -i'],
            workdir=bin_path,
            encoding="utf-8",
            errors="ignore",
        )
        if not common.str2bool(env.get("ISK8SSERVER")):
            # for server_ip in config.SERVER_IP_SET:  # 先注释掉不要了，目前继续使用 1对1 方式， wz todo: 增加调度程序后，需要把配置传到当前要用的所有机器
            with Ssh(hostname=env.get("SERVER")) as ssh:
                ssh.upload_path(f"{self.__workdir}/x5mobile/{self.__branch}/mobile_dancer/trunk/whitebox", "/data/white-box-new")
                ssh.upload_path(
                    f"{self.__workdir}/x5mobile/{self.__branch}/mobile_dancer/trunk/server/mysql",
                    "/data/white-box-new/run/run-data/mysql-run-data",
                )
                ssh.run_cmd("chmod +x /data/white-box-new/*.sh")
                ssh.run_cmd("dos2unix /data/white-box-new/*.sh")

    def __clean_log_file(self):
        """
        清理之前运行产生的日志等文件
        """
        self.__safe_clean(os.path.join(self.__whitebox_exe_path, "logs"), True)
        self.__safe_clean(os.path.join(self.__whitebox_exe_path, "robot_test/output"), True)
        self.__safe_clean(os.path.join(self.__whitebox_exe_path, "robot_test/robot_test_config.local.xml"))
        self.__safe_clean(os.path.join(self.__whitebox_exe_path, "robot_test/checkData/*.*"))
        self.__safe_clean(os.path.join(self.__whitebox_exe_path, "*.zip"))
        self.__safe_clean(os.path.join(self.__workdir, "configTemp"), True)
        self.__safe_clean(os.path.join(self.__workdir, "ConfigConversion"), True)

    def __safe_clean(self, file_path: str, is_dir: bool = False):
        if is_dir:
            if not os.path.exists(file_path):
                os.makedirs(file_path)
            shutil.rmtree(file_path)
            os.makedirs(file_path)
            return
        cmd.run_shell(cmds=[f"del /q {os.path.basename(file_path)}"], workdir=os.path.dirname(file_path))

    def __download_request_file(self):
        """
        下载需求文件
        """
        if env.get("IS_DOWNLOAD_REQ") == "":
            # 下载配置从前端迁移到流水线中的做,但是为了兼容旧的流水线,这部分暂时保留,后续可以删掉
            cmd.run_shell(
                cmds=["WhiteboxPretreatment.exe -t WinSCPJenkinsDownLoadDemandFile"],
                workdir=os.path.join(self.__whitebox_exe_path, "bin"),
                encoding="utf-8",
            )
        else:
            Download(self.__workdir, self.__branch, "scIUQYRgT", env.get("IS_DOWNLOAD_REQ"))

    def __getandupload_config(self):
        """
        获取服务端配置包并上传
        """
        if not common.str2bool(env.get("IS_TEST_JENKINS")):
            return
        # 无论怎么样都会获取一份ftp的包,用来对比远程打出来的包和本地压缩的配置包
        has_ftp_config = self.__get_ftp_config()
        if common.str2bool(env.get("GETCONFIGFROMLOCAL")):
            # 压缩cdn、周更、热更配置(放在根目录 configTemp/local 中),并上传到对应服务器
            self.__zip_file()
            self.__upload_config(env.get("SERVER"), False)
        elif common.str2bool(env.get("GETCONFIGFROMLOCALFULL")):
            # 压缩exe/resources/config下的配置(放在根目录 configTemp/local 中),并上传到对应服务器
            self.__zip_full_file()
            self.__upload_config(env.get("SERVER"), False)
        elif has_ftp_config:
            # 从ftp获取热更资源(放在根目录 configTemp/ftp 中),并上传到对应服务器
            # for server_ip in config.SERVER_IP_SET:  # 先注释掉不要了，目前继续使用 1对1 方式， wz todo: 增加调度程序后，需要把配置传到当前要用的所有机器
            self.__upload_config(env.get("SERVER"), False)

    def __zip_file(self):
        """
        压缩热更周更cdn
        """
        dir = os.path.join(self.__workdir, "configTemp/local/test/resources")
        self.__safe_clean(dir, True)
        log.info("拷贝cdn配置")
        cdn_cfg_path = os.path.join(self.__workdir, "x5mconfig/cdn")
        self.__safe_xcopy(
            src=os.path.join(cdn_cfg_path, "config/server"),
            dst=os.path.join(dir, "config/server"),
        )
        self.__safe_xcopy(
            src=os.path.join(cdn_cfg_path, "config/shared"),
            dst=os.path.join(dir, "config/shared"),
        )
        log.info("拷贝热更配置")
        path = os.path.join(self.__workdir, "x5mconfig/hotfix/onlineupdate", self.__config_branch)
        if os.path.exists(path):
            for i in os.listdir(path):
                content = os.path.join(path, i)
                client = os.path.join(content, "client/crossplatform/config")
                server = os.path.join(content, "server/resources/config")
                if os.path.exists(server) and os.path.exists(dir):
                    self.__safe_xcopy(
                        src=server,
                        dst=os.path.join(dir, "config"),
                    )
                if os.path.exists(client):
                    for subdir in os.listdir(client):
                        if subdir == "shared":
                            self.__safe_xcopy(
                                src=os.path.join(client, subdir),
                                dst=os.path.join(dir, "config", subdir),
                            )
                            break
        log.info("拷贝周更配置")
        week_cfg_path = os.path.join(self.__workdir, "x5mweek", env.get("VERSION"))
        self.__safe_xcopy(
            src=os.path.join(week_cfg_path, "config/server"),
            dst=os.path.join(dir, "config/server"),
        )
        self.__safe_xcopy(
            src=os.path.join(week_cfg_path, "config/shared"),
            dst=os.path.join(dir, "config/shared"),
        )
        version_control = os.path.join(self.__workdir, "configTemp/local/test/resources/config/server/h3d")
        if not os.path.exists(version_control):
            # 如果热更中没有版本配置文件，就去版本配置中拷一份
            self.__safe_xcopy(
                src=os.path.join(self.__workdir, f"x5mconfig/{self.__config_branch}/config/server/h3d"),
                dst=version_control,
            )
        log.info("压缩配置")
        try:
            zip = zipfile.ZipFile(os.path.join(self.__workdir, "configTemp/local/temp_server_pack.zip"), "w", zipfile.ZIP_DEFLATED)
            for path, _, filenames in os.walk(dir):
                fpath = path.replace(dir, "test/resources")
                for filename in filenames:
                    zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
            zip.close()
        except:
            raise PyframeException("压缩配置文件失败")

    def __safe_xcopy(self, src, dst):
        if os.path.exists(src):
            path_mgr.xcopy(src=src, dst=dst, dst_is_file=False)
        else:
            log.warning(f"源路径 {src} 不存在")

    def __zip_full_file(self):
        """
        压缩exe/resources/config下的配置
        """
        log.info("压缩(exe/resources/config)配置文件")
        try:
            dir = f"{self.__workdir}/x5mobile/{self.__branch}/mobile_dancer/trunk/exe/resources/config"
            if not os.path.exists(dir):
                raise PyframeException(f"{dir} 目标目录不存在")
            if not os.path.exists(f"{self.__workdir}/configTemp/local"):
                os.makedirs(f"{self.__workdir}/configTemp/local")
            zip = zipfile.ZipFile(f"{self.__workdir}/configTemp/local/temp_server_pack.zip", "w", zipfile.ZIP_DEFLATED)
            for dirname in ["server", "shared"]:
                src = os.path.join(dir, dirname)
                for path, _, filenames in os.walk(src):
                    fpath = path.replace(src, f"test/resources/config/{dirname}")
                    for filename in filenames:
                        if filename.endswith(".bytes"):
                            continue
                        if filename == "server_globals.xml" or filename == "zone_globals.xml":
                            continue
                        zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
            zip.close()
        except:
            raise PyframeException("压缩配置文件失败")

    def __get_ftp_config(self):
        """
        获取ftp配置包
        """
        ftp_mgr = FtpMgr(
            ip=config.FTP_CONFIG.get("ip"),
            port=config.FTP_CONFIG.get("port"),
            username=config.FTP_CONFIG.get("user"),
            password=config.FTP_CONFIG.get("password"),
        )
        dir_name = env.get("VERSION")
        if dir_name == "resources":
            dir_name = "source"
        """
        # # 获取对应wb分支下最新的包
        # dir_name += "_wb"
        # target_dir = config.FTP_CONFIG.get("dir").format(dir_name)
        # config_list = ftp_mgr.dirs(target_dir)
        # if len(config_list) == 0:
        #     raise PyframeException(f"ftp error, remote directory {target_dir} has no files")
        # elif len(config_list) > 1:
        #     raise PyframeException(f"ftp error, remote directory {target_dir} has too many files")
        # config_filename = config_list[0]
        # # 校验远程目录版本号
        # if config_filename.split("_")[1][:3] != "".join(self.__branch.split(".")[:2]):
        #     log.error(f"ftp error, 版本号告警,远程目录版本号{config_filename.split('_')[1][:3]},本地分支{''.join(self.__branch.split('.')[:2])}")
        #     return
        """

        target_dir = config.FTP_CONFIG.get("dir").format(dir_name)
        config_list = ftp_mgr.dirs(target_dir)
        version_str = "".join(self.__branch.split(".")[:2])
        log.info(f"branch version is {version_str}")
        file_list = [file for file in config_list if file.split("_")[1][:3] == version_str]
        if len(file_list) == 0:
            log.warn(f"ftp error, remote directory {target_dir} version str {version_str} has no files")
            # raise PyframeException(f"ftp error, remote directory {target_dir} version str {version_str} has no files")
            return False
        file_list.sort(reverse=True)
        config_filename = file_list[0]
        log.info(f"find remote zip file [{config_filename}] by local branch version {version_str}")
        ftp_mgr.download_file(
            src=os.path.join(target_dir, config_filename), dst=os.path.join(self.__workdir, "configTemp/ftp/temp_server_pack.zip"), process_bar=True
        )
        return True

    def __upload_config(self, host, local: bool = True):
        """
        上传服务器配置包
        local:
            True  上传本地配置包
            False 上传ftp远程配置包
        """
        with Ssh(hostname=host) as ssh:
            log.info("上传配置压缩包")
            log.info("删除以前遗留的配置包")
            ssh.run_cmd("rm /data/white-box-new/run/upload-dir/temp_server_pack.zip")
            log.info("上传配置压缩包")
            ssh.upload_file(
                f"{self.__workdir}/configTemp/{'local' if local else 'ftp'}/temp_server_pack.zip",
                "/data/white-box-new/run/upload-dir/temp_server_pack.zip",
            )
            log.info("删除以前遗留的配置目录")
            ssh.run_cmd("rm -r -f /data/white-box-new/run/upload-dir/temp_server_pack")
            log.info("创建配置解压结果目录,防止如果没有配置,docker启动拷贝错误")
            ssh.run_cmd("mkdir -p /data/white-box-new/run/upload-dir/temp_server_pack/test/resources")
            log.info("解压下载的配置包（如果没有则会出错,但是没关系）")
            ssh.run_cmd("unzip -o /data/white-box-new/run/upload-dir/temp_server_pack.zip -d" " /data/white-box-new/run/upload-dir/temp_server_pack")
            log.info("清理upload-dir/resources目录")
            ssh.run_cmd("rm -r -f /data/white-box-new/run/upload-dir/resources")
            log.info("清理docker")
            ssh.run_cmd("docker system prune -a -f")

    def __run_white_box(self):
        """
        运行白盒
        """
        self.__run_case("ConvertToBinaryFiles")
        self.__run_case("GenerateTestFixtureTmpFile")
        cmd.run_shell(
            cmds=[f"{self.__nunit3_dir} {self.__whitebox_dir} --explore={self.__test_run_plan_dir}/robot_cases.xml;format=nunit3"],
            encoding=self.__encoding,
        )
        self.__run_case("GenerateTestPlanWithTmpFile")
        self.__run_testcase()
        self.__run_case("MergeJsonLotteryResultTest")

    def __run_case(self, testname):
        """
        执行白盒测试用例
        """
        log_name = "TestResult" + testname + ".xml"
        # 把测试结果也保存到log中，到时候可以一并打包，方便反查定位
        log_dir = os.path.join(self.__whitebox_exe_path, "robot_test/output/buildLogsAndTestResultTemp", log_name)

        cmd.run_shell(
            cmds=[f'{self.__nunit3_dir} /result:{log_dir} {self.__whitebox_dir} --where "name == {testname}"'],
            encoding=self.__encoding,
        )

        try:
            domtree = xml.dom.minidom.parse(log_dir)
            rootnode = domtree.documentElement
            res = rootnode.getElementsByTagName("test-case")[0].getAttribute("result")
            if res == "Passed":
                log.info("{}, Passed".format(testname))
            else:
                raise PyframeException("{} 运行失败, 日志请查看 {}".format(testname, log_dir))
        except:
            raise PyframeException("{} 运行失败, 日志请查看 {}".format(testname, log_dir))

    def __run_testcase(self):
        """
        生成并执行测试套件
        """
        filenames = ["robot_cluster_cases.txt", "robot_normal_cases.txt", "robot_polluted_fixture.txt", "robot_polluted_cases.txt"]
        has_case = False
        local_process = int(env.get("LOCAL_PROCESS"))
        # 集群中的用例
        filename = filenames[0]
        path = os.path.join(self.__test_run_plan_dir, filename)
        with open(path, "r", encoding="utf-8") as file:
            if file.readlines():
                has_case = True
                log.info("执行 {} 中的测试用例".format(filename))
                processes = []
                for i in range(local_process):
                    process = multiprocessing.Process(target=self.parrallel_run, args=(path, local_process, i))
                    process.start()
                    processes.append(process)

                for process in processes:
                    process.join()
        # 集群化的用例跑完了，改一下配置
        # cluster_server 为 false
        cmd.run_shell(
            cmds=[
                f"{self.__gokit_path} ini set config.ini cluster_server false -i",
            ],
            verbose=Cmd.Verbose.LOG,
            workdir=os.path.join(self.__whitebox_exe_path, "robot_test"),
            encoding="utf-8",
            errors="ignore",
        )
        for i in range(1, 4):
            case_file = filenames[i]
            case_path = os.path.join(self.__test_run_plan_dir, case_file)
            with open(case_path, "r", encoding="utf-8") as file:
                if file.readlines():
                    has_case = True
                    log.info("执行 {} 中的测试用例".format(case_file))
                    cmd.run_shell(
                        cmds=[f"{self.__nunit3_dir} {self.__whitebox_dir} --testlist={case_path}"],
                        verbose=Cmd.Verbose.LOG,
                        workdir=os.path.join(self.__whitebox_exe_path, "logs"),
                        encoding="utf-8",
                        errors="ignore",
                    )
        if not has_case:
            raise PyframeException("测试用例生成失败, {0}、 {1} 、{2}、{3}为空".format(*filenames))

    def parrallel_run(self, path, local_process, run_num):
        restart_server = "false"
        if run_num == 0:
            restart_server = "true"
        cmd.run_shell(
            cmds=[
                f"{self.__nunit3_dir} {self.__whitebox_dir} --testlist={path} --testparam:restart_server={restart_server} --agents={local_process} --result={run_num}_TestResult.xml"
            ],
            verbose=Cmd.Verbose.LOG,
            workdir=os.path.join(self.__whitebox_exe_path, "logs"),
            errors="ignore",
        )

    def __get_encoding(self):
        self.__encoding = "gb18030"
