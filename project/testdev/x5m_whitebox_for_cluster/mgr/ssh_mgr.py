# coding=utf-8
import paramiko
from project.testdev.x5m_whitebox_for_cluster import config
from frame import *


class Ssh:
    def __init__(self, hostname: str, username: str = "root", port=22):
        self.__hostname = hostname
        self.__port = port
        self.__username = username
        self.__password = config.SSH_PASSWORD

    def run_cmd(self, cmd):
        _, stdout, stderr = self.sshClient.exec_command(cmd)
        out = stdout.read().decode()
        if out:
            log.info(out)
        # 获取远程返回的信息,既可以捕获异常信息,又可以保证该条命令执行完在执行下一条
        err = stderr.read().decode()
        if err:
            log.warning(err)
        return out, err

    def upload_file(self, local_path, remote_path):
        local_md5 = common.compute_md5(local_path)
        log.info(f"local file md5 is {local_md5}")
        cnt = 3
        while cnt > 0:
            if not os.path.exists(local_path):
                log.error("没有拉到对应版本的热更包,或者本地打包失败,请在 {} 路径下查看".format(local_path))
                return
            self.sftpClient.put(local_path, remote_path)
            remote_md5, _ = self.run_cmd(f"md5sum {remote_path}")
            remote_md5 = remote_md5.split(" ")[0]
            log.info(f"remote file md5 is {remote_md5}")
            if remote_md5 == local_md5:
                log.info(f"upload local file `{local_path}` to remote `{remote_path}` success")
                break
            log.error(f"upload local file `{local_path}` to remote `{remote_path}` error")
            log.error(f"remote file md5 `{remote_md5}` not equal to local file md5 `{local_md5}`")
            cnt -= 1

    def upload_path(self, local_path, remote_path):
        sftp = self.sshClient.open_sftp()
        # 复制本地目录到远程机器上
        try:
            # 递归上传整个本地目录
            for root, dirs, files in os.walk(local_path):
                remote_root = os.path.normpath(os.path.join(remote_path, os.path.relpath(root, local_path))).replace("\\", "/")
                # log.info(f"remote_root is : {remote_root}")
                for dir_name in dirs:
                    remote_dir = os.path.normpath(os.path.join(remote_root, dir_name)).replace("\\", "/")
                    try:
                        # log.info(f"mkdir: {remote_dir}")
                        sftp.mkdir(remote_dir)  # 尝试创建目录
                    except IOError:
                        pass  # 如果目录已存在，忽略异常
                for file_name in files:
                    local_file_path = os.path.join(root, file_name)
                    remote_file_path = os.path.normpath(os.path.join(remote_root, file_name)).replace("\\", "/")
                    # 如果目标文件已存在，先删除它
                    try:
                        sftp.remove(remote_file_path)
                    except IOError:
                        # log.info(f"remove file: {self.__hostname}:{remote_file_path}, file not exits")
                        pass  # 如果文件不存在，忽略异常
                    # 将文件上传
                    # log.info(f"上传{local_file_path} to  {self.__hostname}:{remote_file_path}")
                    sftp.put(local_file_path, remote_file_path)
            log.info("目录上传完成！")
        except IOError as e:
            log.error(f"目录上传失败了{local_path} {remote_path}")
            log.error(e)

    def __enter__(self):
        self.sshClient = paramiko.SSHClient()
        # 如果之前没有连接过的ip,会出现选择yes或者no的操作,自动选择yes
        self.sshClient.set_missing_host_key_policy(paramiko.AutoAddPolicy)
        try:
            self.sshClient.connect(hostname=self.__hostname, port=self.__port, username=self.__username, password=self.__password)
            # sftp上传或下载
            tran = self.sshClient.get_transport()
            self.sftpClient = paramiko.SFTPClient.from_transport(tran)
        except:
            raise PyframeException(f"连接{self.__hostname}失败")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.sshClient.close()
