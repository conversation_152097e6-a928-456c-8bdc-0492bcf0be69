# coding=utf-8
from frame import *
from project.x51 import config


class P4COM2003:
    def __init__(self, branch: str, root: str):
        self.__branch = branch
        ip = common.get_host_ip()
        self.__client = "{}".format(ip.replace(".", "_"))
        self.p4 = P4Client(
            host=config.P4_2003_ART_READ_CONFIG.get("host"),
            username=config.P4_2003_ART_READ_CONFIG.get("username"),
            password=config.P4_2003_ART_READ_CONFIG.get("password"),
            client=self.__client,
        )
        client = self.__client
        views = [
            f"//H3D_X51_res/QQX5_Mainland/... //{client}/H3D_X51_res/QQX5_Mainland/...",
        ]
        self.p4.set_view(views=views)
        self.__root = root
        self.p4.set_root(self.__root)

    def get_exe_latest_changelist(self):
        """
        获取资源分支的最新changelist
        """
        ret = self.p4.get_latest_changes(f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/...")
        try:
            changelist = ret.get("change")
        except Exception as e:
            log.error(e)
            changelist = "head"
        return changelist

    def get_bin_changelist(self, mannual_changelist: str):
        """
        获取bin的changelist
        """
        ret = self.p4.get_changes(path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/bin/...", max=100)
        for change in ret:
            desc = change.get("desc")
            if mannual_changelist in desc:
                log.info(change.get("change"))
                return change.get("change")
        raise PyframeException("未找到bin的changelist, 请流水线触发人检查changelist是否正确")

    def sync_all(self, exe_changelist: str, bin_changelist: str, mannual_changelist: str, force: bool):
        """
        拉取p4
        """
        self.p4.sync(path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/bin/...", changelist=bin_changelist, force=force)

        self.p4.sync(path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/data/...", changelist=mannual_changelist, force=force)
        self.p4.sync(path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/TCLS/...", changelist=mannual_changelist, force=force)
        self.p4.sync(path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/server/...", changelist=exe_changelist, force=force)
        self.p4.sync(path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/editor/...", changelist=exe_changelist, force=force)
        self.p4.sync(path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/level/...", changelist=mannual_changelist, force=force)
        self.p4.sync(path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/ResTable/...", changelist=exe_changelist, force=force)
        self.p4.sync(path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/strings/...", changelist=exe_changelist, force=force)
        self.p4.sync_with_files_and_dirs(
            path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/media/...", changelist=exe_changelist, force=force
        )

        self.p4.sync(
            path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/level/inventory/...", changelist=mannual_changelist, force=False
        )
        self.p4.sync(
            path=f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/resources/media/ui/music_t/...", changelist=mannual_changelist, force=False
        )

    def force_sync_config(self):
        admin_client_config = f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/server/config/admin_client.xml"
        server_common_config = f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/server/config/server_common_config.xml"
        user_config = f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/bin/config/user.ini"
        client_config = f"//H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/bin/config/client_config.ini"
        self.p4.sync(path=admin_client_config, force=True)
        self.p4.sync(path=server_common_config, force=True)
        self.p4.sync(path=user_config, force=True)
        self.p4.sync(path=client_config, force=True)
