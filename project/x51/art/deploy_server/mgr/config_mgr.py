from frame import cmd, log, PyframeException
from project.x51.art.deploy_server.mgr.env_mgr import BkEnvMgr


class ConfigMgr:
    def __init__(self, branch: str) -> None:
        self._branch = branch
        self._skip_switch = BkEnvMgr.get_skip_switch()

    def modify_config(self):
        """
        修改配置
        """
        cmd.run_shell(
            cmds=[
                "chmod +x *.so app_box admin_proxy service_box* ktv_* console_admin",
                'echo "+0" > .faketime',
            ],
            workdir=f"/data/workspace/x51/{self._branch}/exe/bin",
        )
        cmd.run_shell(
            cmds=[
                "chmod +x *.so",
            ],
            workdir=f"/data/workspace/x51/{self._branch}/exe/bin/lib64",
        )
        cmd.run_shell(
            cmds=["cp settings.inc.template settings.inc", "chmod +x *.sh", "sh auto_config.sh"],
            workdir=f"/data/workspace/x51/{self._branch}/src/star",
        )
        ret = cmd.run_shell(
            cmds=[
                """
    sed -i "s/IP_MYSQL_\(.*\)value=\\\".*\\"/IP_MYSQL_\\1value=\\\"************\\\"/g" macros.xml
    && sed -i "s/IP_GLOBAL_DB_MYSQL_\(.*\)value=\\\".*\\\"/IP_GLOBAL_DB_MYSQL_\\1value=\\\"************\\\"/g" macros.xml
    && sed -i "s/.*use_simulate.*/<use_simulate>1<\/use_simulate>/g" mall_server.xml
    && sed -i "s/\(.*svc_idip.*idip[23456].*\)/<\!-- \\1 -->/g" admin_proxy.xml
    && rm -fr ../logs
                """
            ],
            workdir=f"/data/workspace/x51/{self._branch}/exe/bin/config",
        )
        if ret[0] != 0:
            log.error("修改配置失败")
            raise PyframeException("修改服务器配置失败")

        ret = cmd.run_shell(
            cmds=[
                """
    sed -i "s/\(.*\)args=\\\"\(.*\)/\\1args=\\\"-env LD_PRELOAD=\/usr\/local\/lib\/faketime\/libfaketime.so.1 FAKETIME_TIMESTAMP_FILE=.faketime FAKETIME_NO_CACHE=1 NO_FAKE_STAT=1 DONT_FAKE_MONOTONIC=1 \\2/g" admin_proxy.xml
    && sed -i "s/\(.*\)service\(.*\)name\(.*\)display=\\\"\([a-z_]\{0,\}\)\\\"[ ]\{0,\}\/>/\\1service\\2name\\3display=\\\"\\4\\\" args=\\\"-env LD_PRELOAD=\/usr\/local\/lib\/faketime\/libfaketime.so.1 FAKETIME_TIMESTAMP_FILE=.faketime FAKETIME_NO_CACHE=1 NO_FAKE_STAT=1 DONT_FAKE_MONOTONIC=1\\\" \/>/g" admin_proxy.xml
                """
            ],
            workdir=f"/data/workspace/x51/{self._branch}/exe/bin/config",
        )
        if ret[0] != 0:
            log.error("修改配置失败")
            raise PyframeException("修改服务器配置失败")

        # server_common_config.xml
        if self._skip_switch == "1":
            server_common_config = f"/data/workspace/x51/{self._branch}/exe/bin/config/server_common_config.xml"
            # with open(server_common_config, "r+", encoding="gb2312") as f:
            #     config = f.read()
            #     if config.find('ShopUploadPicSkipSwitch switch="1"') == 0:
            #         log.info("已经修改成功，可以忽略")
            #     elif config.find('ShopUploadPicSkipSwitch switch="0"') == 0:
            #         new_config = config.replace('ShopUploadPicSkipSwitch switch="0"', 'ShopUploadPicSkipSwitch switch="1"')
            #         f.seek(0)
            #         f.write(new_config)
            #     # else:
            #     #     raise PyframeException(f"配置文件{server_common_config}中未找到ShopUploadPicSkipSwitch相关配置，请流水线组检查匹配规则是否正确")
            # log.info(f"modified file: {server_common_config}")
            content = []
            with open(server_common_config, "r", encoding='gbk') as f:
                content = f.readlines()
            new_content = []
            for line in content:
                if "ShopUploadPicSkipSwitch" in line and "switch=\"0\"" in line:
                    line = line.replace("switch=\"0\"","switch=\"1\"")
                new_content.append(line)
            with open(server_common_config, "w", encoding="gbk") as f:
                f.write("\n".join(new_content))
