# coding=GBK
from frame import *
from project.x51.art.replace_server_shop.mgr.p4_com_2003 import P4COM2003

WEBHOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0fb917b4-7527-4fca-9e29-57fd46446001"


@advance.stage(stage="锟截凤拷")
def prepare(**kwargs):
    # 锟斤拷锟斤拷锟教空硷拷
    advance.check_disk_size(threshold=5)
    ps = ["admin_proxy", "app_box", "service_box", "service_box64"]
    proc_mgr.kill_procs(names=ps)


@advance.stage(stage="锟斤拷锟斤拷P4")
def sync_p4(**kwargs):
    p4_path = env.get("P4_PATH")
    update_path = env.get("update_path")
    if update_path:
        update_path = update_path.split(" ")
        log.info(f"update_path: {update_path}")

    p4_2003 = P4COM2003(p4_path=p4_path)
    changelist = p4_2003.get_latest_changes()
    env.set({"update_path": update_path, "changelist": changelist})
    log.info(f"changelist = {changelist}")
    for path in update_path:
        p4_2003.sync_file(path=path, changelist=changelist)
    shop_path = "//H3D_X51_res/X51_SourceBase/锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷/server/config/weekly/Shop.xml"
    p4_2003.sync_file(path=shop_path, changelist="head")
    # 锟芥换锟杰革拷锟斤拷锟斤拷
    p4_2003.sync_weekly_shop_config()


@advance.stage(stage="锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷")
def start_server(**kwargs):
    p4_path = env.get("P4_PATH")
    cmd.run_shell(cmds=["export DEVOPS_DONT_KILL_PROCESS_TREE=true", "sh start_tlinux.sh"], workdir="/data/workspace/x51/{}/src/star".format(p4_path))


def __get_msg():
    server_ip = env.get("SERVER_IP")
    branch = env.get("P4_PATH")
    changelist = env.get("changelist")
    update_path = env.get("update_path")
    msg = f"**锟斤拷锟斤拷锟斤拷:** {server_ip}\n"
    msg += f"**锟斤拷支:** {branch}\n"
    msg += f"**changelist:** {changelist}\n"
    msg += f"**锟芥换锟斤拷锟斤拷锟斤拷锟侥硷拷:**\n"
    for path in update_path:
        msg += f"{path}\n"
    return msg


def on_success(**kwargs):
    wechat.send_unicast_post_success(
        # user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
        content=__get_msg()
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(
        # user_list=["<EMAIL>"],
        content=__get_msg()
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(
        # user_list=["<EMAIL>"],
        content=__get_msg()
    )
    advance.insert_pipeline_history_on_canceled()
