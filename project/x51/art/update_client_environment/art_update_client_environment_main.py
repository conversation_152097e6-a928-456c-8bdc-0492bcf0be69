# coding=utf-8
from frame import *
from project.x51.art.update_client_environment.mgr.config_mgr import ConfigMgr
from project.x51.art.update_client_environment.mgr.p4_com_2003 import P4COM2003


@advance.stage(stage="准备")
def prepare(**kwargs):
    proc_mgr.kill_procs(names=["launch_dx.exe", "admin_client.exe", "p4.exe", "WindowsFormsApp1.exe", "DumpClient.exe"])
    mannual_changelist = env.get("P4_VERSION")
    if mannual_changelist.lower().strip() == "head":
        raise PyframeException("p4版本不能为head, 需要填写exe/bin下的提交备注。如svn:xxxx, p4:yyyy，则填写yyyy")


@advance.stage(stage="拉取p4资源")
def sync_p4(**kwargs):
    branch = env.get("P4_PATH")
    local_path = env.get("LOCAL_PATH")
    p4_force_update = common.str2bool(env.get("P4_FORCE_UPDATE"))
    mannual_changelist = env.get("P4_VERSION")
    p4_2003 = P4COM2003(branch=branch, root=local_path)
    exe_changelist = p4_2003.get_exe_latest_changelist()
    bin_changelist = p4_2003.get_bin_changelist(mannual_changelist=mannual_changelist)
    env.set({"bin_changelist": bin_changelist, "exe_changelist": exe_changelist, "mannual_changelist": mannual_changelist})
    p4_2003.sync_all(exe_changelist=exe_changelist, bin_changelist=bin_changelist, mannual_changelist=mannual_changelist, force=p4_force_update)


@advance.stage(stage="修改配置文件")
def modify_config(**kwargs):
    config_mgr = ConfigMgr(root=env.get("LOCAL_PATH"), branch=env.get("P4_PATH"))
    config_mgr.modify_config(server_ip=env.get("SERVER_IP"))


def __get_msg():
    music_t = env.get("P4_VERSION")
    branch = env.get("P4_PATH")
    p4_force_update = env.get("P4_FORCE_UPDATE")
    local_path = env.get("LOCAL_PATH")
    server_ip = env.get("SERVER_IP")
    client_ip = env.get("CLIENT_IP")
    msg = f"""**分支**: {branch}
**服务器**: {server_ip}
**客户端**: {client_ip}
**p4根目录**: {local_path}
**是否强更**: {p4_force_update}
**music_t版本**: {music_t}
    """
    return msg


def on_success(**kwargs):
    branch = env.get("P4_PATH")
    msg = __get_msg()
    msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/bin/...@{env.get('bin_changelist')}\n"
    msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/data/...@{env.get('exe_changelist')}\n"
    msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/TCLS/...@{env.get('exe_changelist')}\n"
    msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/server/...@{env.get('exe_changelist')}\n"
    # msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/resources/editor/...@{env.get('exe_changelist')}\n"
    msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/resources/level/...@{env.get('exe_changelist')}\n"
    # msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/resources/ResTable/...@{env.get('exe_changelist')}\n"
    msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/resources/strings/...@{env.get('exe_changelist')}\n"
    # msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/resources/media/...@{env.get('exe_changelist')}\n"
    msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/resources/level/inventory/...@{env.get('mannual_changelist')}\n"
    msg += f"//H3D_X51_res/QQX5_Mainland/{branch}/exe/resources/media/ui/music_t/...@{env.get('mannual_changelist')}\n"
    wechat.send_unicast_post_success(
        # user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
        content=msg
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(
        # user_list=["<EMAIL>", "<EMAIL>"],
        content=__get_msg()
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(user_list=[], content=__get_msg())
    advance.insert_pipeline_history_on_canceled()
