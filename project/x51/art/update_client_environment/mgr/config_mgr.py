# coding=utf-8
import configparser

from frame import *


class ConfigMgr:
    def __init__(self, root: str, branch: str):
        self.__root = root
        self.__branch = branch

    def modify_config(self, server_ip: str):
        """
        修改配置文件
        """
        admin_client_config_path = os.path.join(self.__root, f"H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/server/config/admin_client.xml")
        user_config_path = os.path.join(self.__root, f"H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/bin/config/user.ini")
        client_config_path = os.path.join(self.__root, f"H3D_X51_res/QQX5_Mainland/{self.__branch}/exe/bin/config/client_config.ini")

        ret = cmd.run_shell(
            cmds=[
                f"attrib -r {admin_client_config_path}",
                f"attrib -r {user_config_path}",
                f"attrib -r {client_config_path}",
            ]
        )
        if ret[0] != 0:
            log.error("attrib failed")
            exit(-1)

        # admin_client.xml
        with open(admin_client_config_path, "r+", encoding="gb2312") as f:
            config = f.read()
            new_config = config.replace("$IP_ADMIN_PROXY", server_ip)
            f.seek(0)
            f.write(new_config)
        log.info(f"modified file: {admin_client_config_path}")

        # user.ini
        cf = configparser.ConfigParser()
        # 区分大小写
        cf.optionxform = lambda s: s

        cf.read(user_config_path, encoding="gb2312")
        cf.set("Network", "hallserverip", server_ip)
        cf.set("Debug", "showCurrentTime", "true")
        with open(user_config_path, "w+", encoding="gb2312") as f:
            cf.write(f)
        log.info(f"modified file: {user_config_path}")

        # client_config.ini
        cf.read(client_config_path, encoding="gb2312")
        cf.set("Network", "hallserverip", server_ip)
        with open(client_config_path, "w+", encoding="gb2312") as f:
            cf.write(f)
        log.info(f"modified file: {client_config_path}")
