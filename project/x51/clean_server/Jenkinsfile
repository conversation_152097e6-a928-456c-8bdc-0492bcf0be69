node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node{
            label params.SERVER_IP
            customWorkspace "/data/workspace/clean_server"
        }
    }

    parameters {
      string description: '要清理的服务器IP', name: 'SERVER_IP', trim: true, defaultValue: '*************'
    }

    options {
        timestamps()
        // disableConcurrentBuilds()
        timeout(time: 60, unit: 'MINUTES')
        skipDefaultCheckout true
    }
    environment {
        PYFRAME_PYTHON = ''
    }
    // triggers {
    //     cron('30 18 * * *')
    // }

    stages {
        stage('查找python路径') {
            steps {
                script {
                    // 检查 conda 是否可用
                    def isCondaAvailable = sh(script: 'conda --version', returnStatus: true) == 0

                    if (isCondaAvailable) {
                        echo "Using conda Python environment"
                        PYFRAME_PYTHON = 'conda run -n pyf368 python'
                    } else {
                        echo "Using system Python3"
                        PYFRAME_PYTHON = 'python3'
                    }
                    sh encoding: 'UTF-8', script: '''
                    echo "python: ${PYFRAME_PYTHON}"
                    '''
                }
            }
        }
        stage("创建虚拟环境") {
            steps {
                script {
                    // 检查 conda 是否可用
                    def isCondaAvailable = sh(script: 'conda --version', returnStatus: true) == 0

                    if (isCondaAvailable) {
                        script: """
                        conda env list | findstr pyf368 || conda create -y -n pyf368 python=3.6.8
                        """
                    }
                }
            }
        }
        stage('拉取脚本') {
            steps {
                sh encoding: 'UTF-8', script: '''
                branch="sujiajun"

                if [ ! -d "pyframe-pipeline/.git" ]; then
                    git clone -b $branch https://gitlab.h3d.com.cn/dev-productivity/pyframe-pipeline.git
                    cd pyframe-pipeline
                    git config --local user.name "<EMAIL>"
                    git config --local user.email "<EMAIL>"
                else
                    cd pyframe-pipeline
                    git config --local user.name "<EMAIL>"
                    git config --local user.email "<EMAIL>"
                    git config pull.rebase false
                    git reset --hard
                    git clean -xdf -e logs
                    # local_branch="$(git branch --show-current)"
                    local_branch="$(git branch  | grep '*' | awk '{print$2}')"
                    if [ "$branch" = "$local_branch" ];then
                        git pull
                    else
                        git checkout $branch
                        git pull
                    fi
                fi

                # ${PYFRAME_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                # ${PYFRAME_PYTHON} -m pip install -r requirements.txt -i  http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
              '''
            }
        }
        stage("安装python依赖") {
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                            ${PYFRAME_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            ${PYFRAME_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("清理分支") {
            steps {
                dir("pyframe-pipeline") {
                    sh "${PYFRAME_PYTHON} x51.py clean_server --job=clean_branch"
                }
            }
        }
        stage("自适应释放文件句柄") {
            steps {
                dir("pyframe-pipeline") {
                    sh "${PYFRAME_PYTHON} x51.py clean_server --job=adaptive_restart_server"
                }
            }
        }
        stage("自适应清理") {
            steps {
                dir("pyframe-pipeline") {
                    sh "${PYFRAME_PYTHON} x51.py clean_server --job=adaptive_clean"
                }
            }
        }
    }

    post {
        success{
            dir("pyframe-pipeline") {
                sh "${PYFRAME_PYTHON} x51.py clean_server --job=post_success"
            }
        }
        failure{
            dir("pyframe-pipeline") {
                sh "${PYFRAME_PYTHON} x51.py clean_server --job=post_failure"
            }
        }
    }
}
