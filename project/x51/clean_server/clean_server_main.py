# coding=utf-8

import re
from datetime import datetime

from frame import advance, common, log, wechat
from project.x51.clean_server.mgr.clean_mgr import clean_mgr
from project.x51.clean_server.mgr.env_mgr import env_mgr, jenkins_env_mgr


@advance.stage(stage="清理分支")
def clean_branch(**kwargs: dict):
    """
    清理分支，默认最多保留2个分支
    """
    remain = jenkins_env_mgr.get_remained_branches()
    clean_mgr.clean_branch(remain=remain)


@advance.stage(stage="清理core")
def clean_core(**kwargs: dict):
    """
    清理core，默认最多保留7天
    """
    days = jenkins_env_mgr.get_remained_days()
    clean_mgr.clean_core(remain_days=days)
    log.info("清理core结束")


@advance.stage(stage="清理日志")
def clean_log(**kwargs: dict):
    """
    清理日志，默认保留7天，最少保留3个
    """
    days = jenkins_env_mgr.get_remained_days()
    clean_mgr.clean_log(remain_days=days)
    log.info("清理log结束")
    # clean_mgr.adaptive_clean(remain_size=30)
    # log.info("自适应清理结束")


@advance.stage(stage="自适应清理")
def adaptive_clean(**kwargs: dict):
    env_mgr.set_before_clean(common.get_disk_free_size())
    clean_mgr.adaptive_clean(remain_size=40)
    env_mgr.set_after_clean(common.get_disk_free_size())
    log.info("自适应清理结束")


@advance.stage(stage="自适应重启服务器进程")
def adaptive_restart_server(**kwargs: dict):
    free_size = clean_mgr.get_disk_free_by_df()
    log.info(f"/data 余量 {free_size}G")
    if free_size > 20:
        log.info("余量大于 20G")
    elapsed_time = clean_mgr.get_server_elapsed_time()
    log.info(f"服务器进程已运行时间 {elapsed_time}")
    if "天" not in elapsed_time:
        return
    result = re.findall("(\d+)天.*", elapsed_time)
    if not result:
        return
    days = result[0]
    if int(days) > 2:
        log.info("服务器进程 已运行超过 2 天")
    # 0点 - 9点 之间会重启
    now = datetime.now()
    if 0 <= int(now.hour) < 9:
        log.info("开始重启服务器进程")
        clean_mgr.restart_running_server()
    else:
        log.info("当前时间不重启")


def post_success(**kwargs: dict):
    content = ""
    if env_mgr.get_before_clean() and env_mgr.get_after_clean():
        content += f"**清理之前容量:** {env_mgr.get_before_clean()}GB\n"
        content += f"**清理之后容量:** {env_mgr.get_after_clean()}GB\n"
    wechat.send_unicast_post_success(user_list=[], content=content)
    advance.insert_pipeline_history_on_success()


def post_failure(**kwargs: dict):
    wechat.send_unicast_post_failure(user_list=[])
    advance.insert_pipeline_history_on_failure()


def post_canceled(**kwargs: dict):
    wechat.send_unicast_post_canceled(user_list=[])
    advance.insert_pipeline_history_on_canceled()


@advance.stage(stage="发送硬盘告警")
def check_disc_size_msg(**kwargs: dict):
    log.info("硬盘容量检测开始")
    warning_threshold = 30

    totalsize, freesize, free_percent, used_percent = common.get_disk_usage_info()
    log.info(f"硬盘剩余空间: {freesize}G")
    log.info(f"硬盘容量: {totalsize}G")
    log.info(f"硬盘已经使用: {used_percent}%")

    userlist = ["<EMAIL>"]
    user_id = __get_pc_user()
    if user_id is not None:
        print("get userid: ", user_id)
        userlist.append(user_id)
    else:
        wechat.send_unicast_msg(user_list=userlist, content=__get_template(f"未指定用户", True))
    
    if free_percent <= warning_threshold:
        content = f"硬盘剩余空间不足{warning_threshold}% \n 当前剩余空间{freesize}G/{totalsize}G, 已使用{used_percent}%"
        log.info(content)
        zero_error = False
        if free_percent <= 5:
            zero_error = True
        wechat.send_unicast_msg(user_list=userlist, content=__get_template(content, zero_error))
    log.info("硬盘容量检测结束")


def __get_template(msg, error: bool) -> str:
    """
    根据状态获取不同的消息模板

    Args:
        status: 流水线的状态，成功，失败，不稳定，取消
    """
    prefix_msg = f"## <font color='warning'>硬盘容量告警 - {common.get_host_ip()}</font>\n"
    if error == True:
        prefix_msg = f"## <font color='#dc143c'>硬盘即将耗尽 - {common.get_host_ip()}</font>\n"
        if msg == "未指定用户":
            prefix_msg = f"## <font color='#dc143c'>测试机未指定用户 {common.get_host_ip()} </font>\n"
    prefix_msg += f"**主机IP:** {common.get_host_ip()}\n"
    prefix_msg += f"**时间:** {common.get_ftime()}\n"
    user_id = __get_pc_user()
    if user_id is not None:
        prefix_msg += f"**用户:** {user_id}\n"
    prefix_msg += f"**硬盘情况:** {msg} \n"

    return prefix_msg


def __get_pc_user():
    pc_user_mapping = {
        "************": "<EMAIL>",
        "************": "<EMAIL>",
        "*************": "<EMAIL>",
        "*************": "<EMAIL>",
        "**************": "<EMAIL>",
        "*************": "<EMAIL>",
        "**************": "<EMAIL>",
        "*************": "<EMAIL>",
        "**************": "<EMAIL>",
        "*************": "<EMAIL>",
        "**************": "<EMAIL>",
        "**************": "<EMAIL>",
        "*************": "<EMAIL>",
        "*************": "<EMAIL>",
        "*************": "<EMAIL>",
        "*************": "<EMAIL>",
        "*************": "<EMAIL>",
        "*************": "<EMAIL>",
        "*************": "<EMAIL>",
         "*************": "<EMAIL>",
          "************": "<EMAIL>",
           "************** ": "<EMAIL>",
           "**************": "<EMAIL>",
    }

    return pc_user_mapping.get(common.get_host_ip())
