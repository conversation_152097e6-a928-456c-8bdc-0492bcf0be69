from pathlib import Path

from frame import PyframeException, advance, cmd, common, env, log, path_mgr
from frame.exception.exception import PyframeException
from frame.p4.p4client import P4Client
from project.x51 import config
from project.x51.compile.compile_client.mgr.env_mgr import x51_client_env
from project.x51.compile.compile_client.mgr.svn_mgr import X51CompileSvnMgr


class X51CompileClientMgr:
    def __init__(self):
        self.workdir: Path = x51_client_env.workdir
        self.year: str = x51_client_env.year
        self.branch: str = x51_client_env.branch.strip("/")
        self.build_type: str = x51_client_env.build_type
        self.game_svn_revision: str = x51_client_env.game_svn
        self.video_svn_revision: str = x51_client_env.video_svn
        self.build_path: Path = x51_client_env.build_path
        self.p4: P4Client = self.__init_p4()
        self.game_src_dir = self.build_path / f"{self.branch}/src/star"
        self.video_src_dir = self.build_path / f"{self.branch}/src/video"
        self.exe_dir = self.build_path / f"{self.branch}/exe"
        self.exe_dirs = [self.exe_dir / i for i in ["bin", "server", "VideoAdminClient"]]
        self.svn = X51CompileSvnMgr(config.SVN_CONFIG["username"], config.SVN_CONFIG["password"])
        self.record = {}
        self.warning = ""
        self.svn_revision_key = "svn_revision"
        self.p4_revision_key = "p4_revision"
        # build 相关参数
        # self.server_build_option = "service_release"
        self.client_build_option = "final_release"
        # self.game_server_solution = "build_server.sln"
        self.game_client_solution = "build_client.sln"
        # self.video_server_solution = "build_video_server.sln"
        # self.video_client_solution = "build_video_client.sln"
        # self.video_admin_client_solution = "VideoAdminClient.sln"
        self.inter_path = self.build_path / f"{self.branch}/inter"
        self.server_inter_path = self.build_path / f"{self.branch}/server_inter"

    def __init_p4(self) -> P4Client:
        p4client = P4Client(
            host=config.P4_2003_PROD_RW_CONFIG["host"],
            username=config.P4_2003_PROD_RW_CONFIG["username"],
            password=config.P4_2003_PROD_RW_CONFIG["password"],
            client="pipeline_x51_compile_" + common.get_host_ip(),
        )
        p4client.set_options(clobber=True, allwrite=True)
        return p4client

    @property
    def p4_exe_path(self) -> str:
        """
        获取P4目录中对应指定branch的exe目录
        :return:
        """
        if self.branch == "trunc":
            p4_server_path = f"//H3D_X51_res/QQX5_Mainland/trunc/exe"
        else:
            branch_year = f"//H3D_X51_res/QQX5_Mainland/branch_{self.year}"

            dirs = self.p4.dirs(f"{branch_year}/*")
            branch_dirs = [i for i in dirs if f"{branch_year}/QQX5_Mainland_{self.branch}" in i]
            if len(branch_dirs) == 0:
                raise PyframeException(f"在P4路径{branch_year}下没有找到对应的分支路径，分支路径格式应该是QQX5_Mainland_{self.branch}_xx")
            p4_server_path = f"{branch_dirs[0]}/exe"
        return p4_server_path

    def create_dirs(self) -> None:
        """
        创建相关目录，目录结构为：
            代码：build_path/分支名称/src/{star,video}
            P4：build_path/分支名称/exe/{bin,server,VideoAdminClient}
        """
        if not self.build_path.exists():
            raise PyframeException(f"build_path: {self.build_path} NOT FOUNDED!")

        for dirname in self.exe_dirs + [self.game_src_dir, self.video_src_dir]:
            path_mgr.mkdir(path=dirname)

    def checkout_svn(self):
        """
        拉取游戏侧和视频侧代码，并记录当前版本号以及对应P4中 resources 目录的版本号
        """
        svn_url = config.SVN_CONFIG["host"]
        if self.branch == "trunc":
            game_svn_url = f"{svn_url}/trunc"
            video_svn_url = f"{svn_url}/video_platform"
        else:
            game_svn_url = f"{svn_url}/branches{self.year}/{self.branch}"
            video_svn_url = f"{svn_url}/video_branches{self.year}/{self.branch}"

        self.svn.checkout(game_svn_url, self.game_src_dir, self.game_svn_revision)
        self.svn.checkout(video_svn_url, self.video_src_dir, self.video_svn_revision)
        # 记录版本
        game_svn_rev = self.svn.get_revision(str(self.game_src_dir))
        video_svn_rev = self.svn.get_revision(str(self.video_src_dir))
        svn_revision = max(game_svn_rev, video_svn_rev)
        x51_client_env.set_svn_revision(svn_revision)

    def sync_p4_dirs(self):
        """
        添加P4映射，并强更本地exe目录下的"bin"、"server"、"VideoAdminClient"
        强更方式是先判断目录是否为空，如果不为空则先删除目录，再重新创建空目录，再同步P4资源
        """
        self.p4.set_root(str(self.exe_dir))
        views = [f"{self.p4_exe_path}/... //{self.p4.client}/..."]
        self.p4.set_view(views)

        p4_changelist = x51_client_env.p4_changelist
        if not p4_changelist or p4_changelist.upper() == "HEAD":
            p4_change = self.p4.get_latest_changes(path=f"{self.p4_exe_path.rstrip('/exe')}/...")
            p4_changelist = p4_change.get("change")
            x51_client_env.set_p4_changelist(p4_changelist)

        self.p4.sync_list(
            path_list=[str(i) + "/" for i in self.exe_dirs],
            changelist=p4_changelist,
            force=x51_client_env.p4_force_update,
        )

    def clean(self):
        # self._clean_up_build_workspace()
        for p in self.exe_dirs:
            path_mgr.chmod(path=p, write=True)
        path_mgr.chmod(path=self.exe_dir / "debug_bin", write=True)

        if self.build_type.upper() == "REBUILD":
            cmds = [f'BuildConsole.exe {self.game_client_solution} /CFG="{self.client_build_option}|Win32" /CLEAN /StopOnErrors /MAXCPUS=60']
            ret = cmd.run_shell(
                cmds=cmds,
                workdir=self.game_src_dir,
            )
            if ret[0] != 0:
                raise PyframeException(f"BuildConsole.exe clean失败，请检查失败原因")
        else:
            log.info("不需要清理，直接编译")

    def build(self):
        """
        编译游戏侧客户端
        """
        cmds = [f'BuildConsole.exe {self.game_client_solution} /CFG="{self.client_build_option}|Win32" /BUILD /StopOnErrors /MAXCPUS=60']

        ret = cmd.run_shell(
            cmds=cmds,
            workdir=self.game_src_dir,
            log_to_file=f"build_game_client_{env.pipeline.build_num()}.log",
        )
        if ret[0] != 0:
            advance.incredibuild.raise_ib_log_exception(log_path=f"build_game_client_{env.pipeline.build_num()}.log")
            raise PyframeException(f"BuildConsole.exe编译失败，请检查失败原因")

    def submit(self):
        # 正式环境上传
        if not x51_client_env.test_env:
            r = self.p4.reconcile(
                path=f"//H3D_X51_res/QQX5_Mainland/{x51_client_env.p4_path}/exe/bin/...",
                add=True,
                edit=True,
            )
            if not r:
                return
            submit_resp = self.p4.submit(
                desc=f"SVN:{x51_client_env.svn_revision}  P4:{x51_client_env.p4_changelist} 不适用于蓝盾部署",
                # revert_if_failed=True,
            )
            log.debug(submit_resp)
            change = submit_resp[0]["change"]
            x51_client_env.set_p4_client_changelist(change)
            x51_client_env.set_run_smoke_test(True)
            return
        self.submit_p4_2005()

    def submit_p4_2005(self):
        p4_2005_root = self.exe_dir / "2005_bin"
        if p4_2005_root.exists():
            path_mgr.rm(str(p4_2005_root))
        p4_2005_root.mkdir(parents=True)
        p4_2005_client = P4Client(
            host=config.P4_2005_CONFIG["host"],
            username=config.P4_2005_CONFIG["username"],
            password=config.P4_2005_CONFIG["password"],
            client="pipeline_x51_compile_" + common.get_host_ip(),
        )
        p4_2005_client.set_options(allwrite=True)
        p4_2005_client.set_root(str(p4_2005_root))
        p4_2005_client.set_view([(f"//H3D_MUG_QE/x51_dev/QQX5_Mainland/{x51_client_env.p4_path}/exe/bin/... //{p4_2005_client.client}/...")])
        p4_2005_client.sync_all()

        path_mgr.copy(src=str(self.exe_dir / "bin"), dst=str(p4_2005_root), verbose=True)
        rs = []
        r = p4_2005_client.reconcile(
            path=f"//H3D_MUG_QE/x51_dev/QQX5_Mainland/{x51_client_env.p4_path}/exe/bin/...",
            add=True,
            edit=True,
        )
        if r:
            rs.extend(r)

        if not rs:
            return
        submit_resp = p4_2005_client.submit(
            desc=f"SVN:{x51_client_env.svn_revision}  P4:{x51_client_env.p4_changelist}",
            # revert_if_failed=True,
        )
        log.debug(submit_resp)
        change = submit_resp[0]["change"]
        x51_client_env.set_p4_client_changelist(change)
        x51_client_env.set_run_smoke_test(True)

    def upload_version(self):
        log.info("暂时不上报")
        log.info(f"svn_rev: {x51_client_env.svn_revision}")
        log.info(f"p4_changelist: {x51_client_env.p4_changelist}")
        log.info(f"p4_client_changelist: {x51_client_env.p4_client_changelist}")


x51_compile_client_mgr = X51CompileClientMgr()
