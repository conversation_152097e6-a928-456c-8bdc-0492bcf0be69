from frame import advance, wechat
from project.x51.compile.compile_server.mgr.compile_mgr import (
    x51_compile_server_mgr,
)
from project.x51.compile.compile_server.mgr.env_mgr import x51_server_env


@advance.stage("准备")
def prepare():
    x51_compile_server_mgr.prepare()


@advance.stage("更新svn")
def update_svn():
    x51_compile_server_mgr.update_svn()


@advance.stage("更新p4")
def update_p4():
    x51_compile_server_mgr.update_p4()


# @advance.stage("编译")
# def compile_server():
#     x51_compile_server_mgr.compile_server()


@advance.stage("编译star")
def build_star():
    x51_compile_server_mgr.build_star()


@advance.stage("编译video")
def build_video():
    x51_compile_server_mgr.build_video()


@advance.stage("上传")
def upload():
    x51_compile_server_mgr.upload()


def _get_msg() -> str:
    build_star_log_url = x51_server_env.build_star_log_url
    if build_star_log_url:
        msg += f"star编译日志: {build_star_log_url}\n" if build_star_log_url else ""
    return msg


def on_success():
    wechat.send_unicast_post_success(content=_get_msg())


def on_failure():
    wechat.send_unicast_post_failure(content=_get_msg())


def on_canceled():
    wechat.send_unicast_post_canceled(content=_get_msg())
