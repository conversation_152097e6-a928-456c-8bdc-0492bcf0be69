node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

pipeline {
    agent {
        node {
            label "tlinux2.2_172.17.104.174"
            customWorkspace "/data/workspace/"
        }
    }

    options {
        disableConcurrentBuilds()
    }
    // triggers {
    //     cron('0 */2 * * *')
    // }

    environment {
       PYFRAME_PYTHON = '/root/.conda/envs/pyf368/bin/python'
    }

    parameters {
        string(name: 'BRANCH', defaultValue: '6.5.6_07', description: '游戏分支')
        string(name: 'GAME_SVN', defaultValue: 'HEAD', description: '游戏代码版本号')
        string(name: 'VIDEO_SVN', defaultValue: 'HEAD', description: '视频代码版本号')
        string(name: 'P4_REV', defaultValue: '', description: '资源版本号')
        choice( name: 'SERVER_BUILD_TYPE',choices: ["--rebuild", "--no-rebuild"],description: '构建类型')
        booleanParam(name: 'BUILD_VIDEO', defaultValue: true, description: '同时编译视频侧')
        string(name: 'P4_PATH', defaultValue: 'branch_2023/QQX5_Mainland_6.5.6_07', description: 'P4资源路径')
        string(name: 'STAR_BRANCH', defaultValue: 'branches2023/6.5.6', description: '游戏侧SVN路径')
        string(name: 'VIDEO_BRANCH', defaultValue: 'video_branches2023/6.5.6', description: '视频侧SVN路径')
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        sh(
                            script: """
                                $env.PYFRAME_PYTHON -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                $env.PYFRAME_PYTHON -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("准备") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        sh(script: "$env.PYFRAME_PYTHON x51.py compile_server --job=prepare")
                    }
                }
            }
        }
        stage("更新SVN") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        sh(script: "$env.PYFRAME_PYTHON x51.py compile_server --job=update_svn")
                    }
                }
            }
        }
        stage("更新P4") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        sh(script: "$env.PYFRAME_PYTHON x51.py compile_server --job=update_p4")
                    }
                }
            }
        }
        stage("编译star") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        sh(script: "$env.PYFRAME_PYTHON x51.py compile_server --job=build_star")
                    }
                }
            }
        }
        stage("编译video") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        sh(script: "$env.PYFRAME_PYTHON x51.py compile_server --job=build_video")
                    }
                }
            }
        }
        stage("上传") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        sh(script: "$env.PYFRAME_PYTHON x51.py compile_server --job=upload")
                    }
                }
            }
        }
    }
    post {
        success {
            dir("pyframe-pipeline") {
                script {
                    sh(script: "$env.PYFRAME_PYTHON x51.py compile_server --job=on_success")
                }
            }
        }
        failure {
            dir("pyframe-pipeline") {
                script {
                    sh(script: "$env.PYFRAME_PYTHON x51.py compile_server --job=on_failure")
                }
            }
        }
        aborted {
            dir("pyframe-pipeline") {
                script {
                    sh(script: "$env.PYFRAME_PYTHON x51.py compile_server --job=on_aborted")
                }
            }
        }
    }
}
