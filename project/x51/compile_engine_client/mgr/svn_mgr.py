from pathlib import Path

from frame import PyframeException, cmd


class X51CompileSvnMgr:
    __slots__ = "svn_cmd"

    def __init__(self, username: str, password: str):
        self.svn_cmd = f"svn --username={username} --password={password} --no-auth-cache"

    def checkout(self, remote_url: str, local_dir: Path, revision: str):
        checkout_cmd_format = " ".join([self.svn_cmd, "checkout {url} {local}", "-r {revision} -q"])
        is_working_copy = (local_dir / ".svn").exists()
        if is_working_copy:
            revert_cmd = f"{self.svn_cmd} revert -R {local_dir}"
            cmd.run_shell(cmds=[revert_cmd])

        checkout_cmd = checkout_cmd_format.format(url=remote_url, local=local_dir, revision=revision)
        code, output = cmd.run_shell(cmds=[checkout_cmd])
        if code != 0 or "is already a working copy for a different URL" in output:
            if is_working_copy:
                cmd.run_shell(cmds=[f"{self.svn_cmd} switch -r {revision} -q {remote_url} {local_dir} --ignore-ancestry"])

    # def checkout(self, remote_url: str, local_path: Path, revision: str):
    #     svn_dir = local_path / ".svn"
    #     if svn_dir.exists() and svn_dir.is_dir():
    #         self._update(local_path, revision, remote_url)
    #     else:
    #         path_mgr.rm(local_path)
    #         self._checkout(remote_url, local_path, revision)

    # def _checkout(self, remote_url: str, local_path: str, revision: str):
    #     cmds = [f"{self.svn_cmd} checkout {remote_url} {local_path} -r {revision} -q"]
    #     code, _ = cmd.run_shell(cmds=cmds, verbose=cmd.Verbose.LOG)
    #     if code != 0:
    #         raise PyframeException(
    #             f"svn checkout {remote_url} {local_path} -r {revision} -q failed"
    #         )

    # def _update(self, local_path: str, revision: str, remote_url: str):
    #     self.clean_up(local_path)
    #     cmds = [f"{self.svn_cmd} revert -R {local_path} -q"]
    #     ret = cmd.run_shell(cmds=cmds, verbose=cmd.Verbose.LOG)
    #     if ret[0] != 0:
    #         raise PyframeException(f"{cmds} failed")

    #     cmds = [f"{self.svn_cmd} update {local_path} -r {revision} -q"]
    #     ret = cmd.run_shell(cmds=cmds, verbose=cmd.Verbose.LOG)
    #     if ret[0] != 0:
    #         # raise PyframeException(f"{cmds} failed")
    #         switch_cmd = (
    #             f"{self.svn_cmd} switch -q {remote_url} {local_path} --ignore-ancestry"
    #         )
    #         cmd.run_shell(cmds=switch_cmd)

    def get_revision(self, local_path: str) -> str:
        cmds = [f"{self.svn_cmd} info {local_path} --show-item=last-changed-revision"]
        ret = cmd.run_shell(cmds=cmds, return_output=True)
        if ret[0] != 0:
            raise PyframeException(f"{cmds} failed")
        revision = ret[1][0].strip()
        return revision

    def clean_up(self, local_path: str):
        cmds = [f"{self.svn_cmd} cleanup {local_path} -q"]
        ret = cmd.run_shell(
            cmds=cmds,
        )
        if ret[0] != 0:
            raise PyframeException(f"{cmds} failed")
