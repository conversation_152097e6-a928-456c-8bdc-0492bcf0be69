node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

pipeline {
    agent {
        node {
            label "win10_192.168.14.105"
        }
    }

    options {
        disableConcurrentBuilds()
    }

    parameters {
        string(name: 'BRANCH', defaultValue: 'video_from_trunc', description: '分支名，格式如trunc、6.5.0，不填写默认最新分支')
        string(name: 'P4_REV', defaultValue: '', description: '资源版本号，不填默认HEAD')
        booleanParam(name: 'FORCE_UPDATE', defaultValue: false, description: '强制更新p4')
        string(name: 'GAME_SVN', defaultValue: 'HEAD', description: '游戏代码版本号')
        string(name: 'BUILD_PATH', defaultValue: 'D:\\x51_client_compile', description: '构建路径')
    }
    environment {
       PYFRAME_PYTHON = 'conda run -n pyf368 --live-stream python'
    }

    stages {
        stage("创建虚拟环境") {
            steps {
                script {
                    bat(
                        script: """
                        conda env list | findstr pyf368 || conda create -y -n pyf368 python=3.6.8
                        """
                    )
                }
            }
        }
        stage("安装python依赖") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("准备") {
            steps {
                script {
                    bat(script: "$env.PYFRAME_PYTHON x51.py compile_record_tool --job=prepare")
                }
            }
        }
        stage("更新SVN") {
            steps {
                script {
                    bat(script: "$env.PYFRAME_PYTHON x51.py compile_record_tool --job=update_svn")
                }
            }
        }
        stage("更新P4") {
            steps {
                script {
                    bat(script: "$env.PYFRAME_PYTHON x51.py compile_record_tool --job=update_p4")
                }
            }
        }
        // stage("清理") {
        //     steps {
        //         script {
        //             bat(script: "$env.PYFRAME_PYTHON x51.py compile_record_tool --job=clean")
        //         }
        //     }
        // }
        stage("编译录制工具") {
            steps {
                script {
                    bat(script: "$env.PYFRAME_PYTHON x51.py compile_record_tool --job=compile")
                }
            }
        }
        stage("版本设置") {
            steps {
                script {
                    bat(script: "$env.PYFRAME_PYTHON x51.py compile_record_tool --job=set_version")
                }
            }
        }
        stage("reconcile") {
            steps {
                script {
                    bat(script: "$env.PYFRAME_PYTHON x51.py compile_record_tool --job=submit")
                }
            }
        }
    }
    post {
        success {
            script {
                bat(script: "$env.PYFRAME_PYTHON x51.py compile_record_tool --job=on_success")
            }
        }
        failure {
            script {
                bat(script: "$env.PYFRAME_PYTHON x51.py compile_record_tool --job=on_failure")
            }
        }
        aborted {
            script {
                bat(script: "$env.PYFRAME_PYTHON x51.py compile_record_tool --job=on_canceled")
            }
        }
    }
}
