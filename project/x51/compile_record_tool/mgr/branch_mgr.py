import re
from typing import List

from frame import P4Client, PyframeException, common, env, log
from project.x51 import config


class BranchParser:
    def __init__(self) -> None:
        self.p4client = P4Client(
            host=config.P4_2003_PROD_RO_CONFIG["host"],
            username=config.P4_2003_PROD_RO_CONFIG["username"],
            password=config.P4_2003_PROD_RO_CONFIG["password"],
            client="pipeline_compiler_" + common.get_host_ip(),
        )

    @property
    def p4_branches(self) -> List[str]:
        """p4分支"""
        branches = self.p4client.dirs("//H3D_X51_res/QQX5_Mainland/branch_*/QQX5_Mainland_*")
        log.info(f"获取到分支: {branches}")
        return ["trunc"] + branches

    @property
    def normal_branches(self):
        """p4正式分支"""
        normal_branches = []
        for branch in self.p4_branches:
            if re.match(".*branch_\d{4}/QQX5_Mainland_\d.\d.\d_\d\d$", branch):
                normal_branches.append(branch)
        log.info(f"获取到正式分支: {normal_branches}")
        return normal_branches

    @property
    def latest_branch(self) -> str:
        """
        取最新正式分支

        return:
            6.5.9
        """

        def get_key(branch):
            group = re.findall(".*branch_(\d+)/QQX5_Mainland_(\d\.\d\.\d)_(\d+)", branch)
            return group[0]

        # 取最新分支
        p4_branch = sorted(self.normal_branches, key=get_key, reverse=True)[0]
        latest_branch = re.findall("branch_.*/QQX5_Mainland_(\d\.\d\.\d)_.*", p4_branch)[0]
        log.info(f"获取到最新分支: {latest_branch}")
        return latest_branch

    def parse_branch(self):
        branch = env.get("BRANCH")
        # 设置为最新分支
        if not branch:
            branch = self.latest_branch
            env.set({"BRANCH": branch})
        # 主支
        if branch == "trunc":
            env.set({"STAR_BRANCH": "trunc"})
            env.set({"VIDEO_BRANCH": "video_platform"})
            env.set({"P4_PATH": "trunc"})
        # 正式分支
        elif re.match("\d+\.\d\.\d", branch):
            for normal_branch in self.normal_branches:
                if branch in normal_branch:
                    year, month = re.findall(f"branch_(.+)/QQX5_Mainland_{branch}_(.+)", normal_branch)[0]
                    env.set({"YEAR": year})
                    env.set({"STAR_BRANCH": f"branches{year}/{branch}"})
                    env.set({"VIDEO_BRANCH": f"video_branches{year}/{branch}"})
                    env.set({"P4_PATH": f"branch_{year}/QQX5_Mainland_{branch}_{month}"})
                    break

            if not env.get("STAR_BRANCH"):
                raise PyframeException(f"未找到正式分支: {branch}")
        # 特殊分支
        else:
            for b in self.p4_branches:
                group = re.findall(f"branch_(.+)/QQX5_Mainland_{branch}", b)
                if not group:
                    continue
                env.set({"YEAR": group[0]})
                env.set({"STAR_BRANCH": f"branches{group[0]}/{branch}"})
                env.set({"VIDEO_BRANCH": f"video_branches{group[0]}/{branch}"})
                env.set({"P4_PATH": f"branch_{group[0]}/QQX5_Mainland_{branch}"})
