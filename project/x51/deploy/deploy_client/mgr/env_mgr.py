from frame import common, env


class EnvMgr:
    def __init__(self) -> None:
        pass

    def set_bin_changelist(self, changelist: str):
        env.set({"BIN_CHANGELIST": changelist})

    def get_bin_changelist(self) -> str:
        return env.get("BIN_CHANGELIST")

    def set_exe_changelist(self, changelist: str):
        env.set({"EXE_CHANGELIST": changelist})

    def get_exe_changelist(self) -> str:
        return env.get("EXE_CHANGELIST")

    def set_mannual_changelist(self, changelist: str):
        env.set({"MANNUAL_CHANGELIST": changelist})

    def get_mannual_changelist(self) -> str:
        return env.get("MANNUAL_CHANGELIST")


class JenkinsEnvMgr:
    def __init__(self) -> None:
        pass

    def get_changelist(self) -> str:
        return env.get("CHANGELIST")

    def get_branch(self) -> str:
        return env.get("BRANCH")

    def get_server_ip(self) -> str:
        return env.get("SERVER_IP")

    def get_client_ip(self) -> str:
        return env.get("CLIENT_IP")

    def get_local_path(self) -> str:
        return env.get("LOCAL_PATH")

    def get_force_update(self) -> bool:
        return common.str2bool(env.get("FORCE_UPDATE"))

    @property
    def test_env(self) -> bool:
        return common.str2bool(env.get("TEST_ENV", default="True"))

    @property
    def login_qq(self) -> str:
        return env.get("LOGIN_QQ")


env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
