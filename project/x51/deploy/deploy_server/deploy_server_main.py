# coding=utf-8
import xml.etree.cElementTree as ET
from pathlib import Path

from tenacity import retry, stop_after_attempt, wait_fixed

from frame import (
    PyframeException,
    advance,
    cmd,
    log,
    path_mgr,
    proc_mgr,
    wechat,
)
from project.x51 import config
from project.x51.deploy.deploy_server.mgr.config_mgr import ConfigMgr
from project.x51.deploy.deploy_server.mgr.env_mgr import (
    env_mgr,
    jenkins_env_mgr,
)
from project.x51.deploy.deploy_server.mgr.linux_bin_mgr import LinuxBinMgr
from project.x51.deploy.deploy_server.mgr.p4_com_2003 import P4COM2003
from project.x51.deploy.deploy_server.mgr.p4_com_2005 import P4COM2005

WEBHOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0fb917b4-7527-4fca-9e29-57fd46446001"


@advance.stage(stage="获取svn")
def get_svn(**kwargs):
    if jenkins_env_mgr.svn_version:
        return

    if not jenkins_env_mgr.p4_2005_bin:
        return

    branch = jenkins_env_mgr.branch
    changelist = jenkins_env_mgr.changelist

    p4_mgr = P4COM2005(branch=branch, root=str(Path.cwd()))
    svn = p4_mgr.get_svn(changelist)
    jenkins_env_mgr.set_svn_version(svn)


@advance.stage(stage="准备")
def prepare(**kwargs):
    advance.check_disk_size(threshold=5)

    log.info("开始关服")
    proc_mgr.kill_procs(names=["admin_proxy", "app_box", "service_box", "service_box64"])

    path_mgr.mkdir("/data/mockp4/x51")
    path_mgr.mkdir("/data/workspace/x51")
    path_mgr.mkdir("/data/workspace/logs")


@advance.stage(stage="更新P4")
def sync_p4(**kwargs):
    branch = jenkins_env_mgr.branch
    force_update = jenkins_env_mgr.force_update
    changelist = jenkins_env_mgr.changelist

    p4_2003 = P4COM2003(branch=branch)
    if changelist == "None" or not changelist:
        changelist = "head"
    if changelist.lower() == "head":
        changelist = p4_2003.get_latest_changes()

    env_mgr.set_changelist(changelist=changelist)

    if not Path(f"/data/workspace/x51/{branch}").exists():
        force_update = True
    p4_2003.sync_all(changelist=changelist, force=force_update)

    p4_2003.sync_weekly_shop_config()


@advance.stage(stage="修改配置")
def modify_config(**kwargs):
    config_mgr = ConfigMgr(branch=jenkins_env_mgr.branch)
    config_mgr.modify_config()


@retry(stop=stop_after_attempt(3), wait=wait_fixed(60))
@advance.stage(stage="下载并解压linux_bin")
def download_decompress_linux_bin(**kwargs):
    branch = jenkins_env_mgr.branch
    changelist = env_mgr.get_changelist()
    svn_version = jenkins_env_mgr.svn_version

    linux_bin_mgr = LinuxBinMgr(branch=branch)

    if not svn_version or svn_version == "None":
        svn_version, changelist = linux_bin_mgr.find_ftp_linux_bin_by_changelist(changelist)
        if svn_version:
            jenkins_env_mgr.set_svn_version(svn_version)
        if changelist:
            jenkins_env_mgr.set_chagnelist(changelist)
    # linux_bin_mgr.download_linux_bin()
    last_download = Path(f"/data/workspace/x51/{branch}/last_download")
    if last_download.exists():
        with open(last_download, "r") as f:
            last_version = f.read()
            if last_version == f"{svn_version}_{changelist}":
                log.info(f"已存在 {last_version} linux_bin，不需要重复下载")
                return
    linux_bin_mgr.download_linux_bin_from_ftp(
        svn_ver=svn_version,
        p4_changelist=changelist,
    )
    linux_bin_mgr.decompress_linux_bin()
    with open(last_download, "w") as f:
        f.write(f"{svn_version}_{changelist}")

    linux_bin_version = f"svn_{svn_version}_p4_{changelist}"
    env_mgr.set_linux_bin_version(linux_bin_version=linux_bin_version)


@advance.stage(stage="下载svn脚本")
def download_svn_script(**kwargs):
    branch = jenkins_env_mgr.branch
    path_mgr.mkdir(f"/data/workspace/x51/{branch}/src/star")
    # TODO 个人账号密码
    try:
        username = config.SVN_CONFIG["username"]
        password = config.SVN_CONFIG["password"]
    except Exception as e:
        raise PyframeException(f"请配置正确的svn账号密码: {e}")

    command = f"svn checkout --force --depth=files http://*************/svn/starx52/trunc/ ./src/star --username {username} --password {password}"
    cmd.run_shell(
        cmds=[command],
        workdir=f"/data/workspace/x51/{branch}",
    )


@advance.stage(stage="启动服务器")
def start_server(**kwargs):
    branch = jenkins_env_mgr.branch
    ret = cmd.run_shell(
        cmds=[
            "sh start_tlinux.sh",
        ],
        workdir=f"/data/workspace/x51/{branch}/src/star",
        dot_kill_me=True,
    )
    if ret[0] != 0:
        raise PyframeException("启动服务器失败")


@advance.stage(stage="检测进程")
def monitor(**kwargs):
    branch = jenkins_env_mgr.branch
    is_video = False
    full_path = f"/data/workspace/x51/{branch}/exe/bin/config/admin_proxy.xml"
    with open(full_path, "rb") as f:
        byte_data = f.read()
    str_data = byte_data.decode("gb2312")
    root = ET.fromstring(str_data)
    for server in root.iter("server"):
        port = server.attrib.get("port")
        pnames = []
        if not is_video:
            if port != "$PORT_APP_BOX_VIDEO_1":
                for child in server:
                    at = child.attrib.get("display")
                    pnames.append(at)
        else:
            if port == "$PORT_APP_BOX_VIDEO_1":
                for child in server:
                    att = child.attrib.get("display")
                    pnames.append(att)
        for name in pnames:
            if proc_mgr.exists_proc(name=name):
                log.info(f"{name} exists")
            else:
                log.error(f"{name} doesn't exist")
                exit(-1)


def __get_msg():
    branch = jenkins_env_mgr.branch
    changelist = env_mgr.get_changelist()
    force = jenkins_env_mgr.force_update
    linux_bin_version = env_mgr.get_linux_bin_version()

    msg = f"**分支**: {branch}\n" if branch else ""
    msg += f"**changelist**: {changelist}\n" if changelist else ""
    msg += f"**强更p4**: {force}\n" if force else ""
    msg += f"**服务器版本**: {linux_bin_version}\n" if linux_bin_version else ""
    return msg


def on_success(**kwargs):
    wechat.send_unicast_post_success(
        user_list=jenkins_env_mgr.notice_to(),
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    # wechat.send_unicast_post_failure(user_list=[], content=__get_msg())
    # 流水线失败群
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=21472abe-94a1-45e5-a8e3-4b64b8f9dc02"
    wechat.send_multicast_post_failure(
        webhook=webhook,
        content=__get_msg(),
        mentioned_list=["<EMAIL>"],
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()
    user_list = jenkins_env_mgr.notice_to()
    if user_list:
        wechat.send_unicast_post_failure(user_list=user_list, content=__get_msg())


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(
        user_list=jenkins_env_mgr.notice_to(),
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_canceled()
