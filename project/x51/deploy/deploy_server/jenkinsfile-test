node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    parameters {
        string(name: 'SERVER_IP', defaultValue: '', description: '服务器IP')
        string(name: 'BRAN<PERSON>', defaultValue: '', description: '游戏分支')
        string(name: 'SVN_VERSION', defaultValue: '', description: '游戏代码版本号')
        string(name: 'CHANGELIST', defaultValue: '', description: 'p4版本号')
        booleanParam(name: 'FORCE_UPDATE', defaultValue: false, description: '强制更新p4')
        booleanParam(name: 'P4_2005_BIN', defaultValue: true, description: '下载2005的服务器bin')
        choice( name: 'START_TYPE',choices: ["start_tlinux", "start_video", "manual"],description: '启动方式')
        choice( name: 'DEPLOY_TYPE',choices: ["全服", "灰度"])
        string(name: 'NOTICE_TO', defaultValue: '', description: '企微通知人员')
    }
    environment {
        PYFRAME_PYTHON = 'conda run -n pyf368 python'
    }
    agent {
        node {
            label "tlinux2.2_${params.SERVER_IP}"
            customWorkspace "/data/workspace/pyframe-pipeline"
        }
    }

    stages {
        stage('查找python路径') {
            steps {
                script {
                    // 检查 conda 是否可用
                    def isCondaAvailable = sh(script: 'conda --version', returnStatus: true) == 0

                    if (isCondaAvailable) {
                        echo "Using conda Python environment"
                        PYFRAME_PYTHON = 'conda run -n pyf368 python'
                    } else {
                        echo "Using system Python3"
                        PYFRAME_PYTHON = 'python3'
                    }
                }
            }
        }
        stage("安装python依赖") {
            steps {
                script {
                    sh(
                        script: """
                            ${PYFRAME_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            ${PYFRAME_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("获取svn") {
            steps {
                script {
                    sh(script: "${PYFRAME_PYTHON} x51.py deploy_server --job=get_svn")
                }
            }
        }
        stage("准备") {
            steps {
                script {
                    sh(script: "${PYFRAME_PYTHON} x51.py deploy_server --job=prepare")
                } }
        }
        stage("更新P4") {
            steps {
                script {
                    sh(script: "${PYFRAME_PYTHON} x51.py deploy_server --job=sync_p4")
                }
            }
        }
        stage("下载并解压linux_bin") {
            steps {
                script {
                    sh(script: "${PYFRAME_PYTHON} x51.py deploy_server --job=download_decompress_linux_bin")
                }
            }
        }
        stage("获取svn脚本") {
            steps {
                script {
                    sh(script: "${PYFRAME_PYTHON} x51.py deploy_server --job=download_svn_script")
                }
            }
        }
        stage("修改配置") {
            steps {
                script {
                    sh(script: "${PYFRAME_PYTHON} x51.py deploy_server --job=modify_config")
                }
            }
        }
        stage("启动服务") {
            steps {
                script {
                    sh(script: "${PYFRAME_PYTHON} x51.py deploy_server --job=start_server")
                }
            }
        }
    }
    post {
        success {
            script {
                sh(script: "${PYFRAME_PYTHON} x51.py deploy_server --job=on_success")
            }
        }
        failure {
            script {
                sh(script: "${PYFRAME_PYTHON} x51.py deploy_server --job=on_failure")
            }
        }
        aborted {
            script {
                sh(script: "${PYFRAME_PYTHON} x51.py deploy_server --job=on_canceled")
            }
        }
    }
}
