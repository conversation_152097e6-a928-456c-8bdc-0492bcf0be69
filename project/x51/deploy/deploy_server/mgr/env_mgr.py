from typing import List

from frame import common, env


class EnvMgr:
    def __init__(self) -> None:
        pass

    def set_changelist(self, changelist: str):
        env.set({"LATEST_CHANGELIST": changelist})

    def get_changelist(self) -> str:
        return env.get("LATEST_CHANGELIST")

    def set_linux_bin_version(self, linux_bin_version: str):
        env.set({"LINUX_BIN_VERSION": linux_bin_version})

    def get_linux_bin_version(self) -> str:
        return env.get("LINUX_BIN_VERSION")


class JenkinsEnvMgr:
    def __init__(self) -> None:
        pass

    @property
    def branch(self) -> str:
        return env.get("BRANCH")

    @property
    def p4_2005_bin(self) -> bool:
        return common.str2bool(env.get("P4_2005_BIN", default="False"))

    @property
    def svn_version(self) -> str:
        return env.get("SVN_VERSION")

    def set_svn_version(self, svn):
        env.set({"SVN_VERSION": svn})

    @property
    def changelist(self) -> str:
        return env.get("CHANGELIST")

    def set_chagnelist(self, changelist):
        env.set({"CHANGELIST": changelist})

    @property
    def force_update(self) -> bool:
        return common.str2bool(env.get("FORCE_UPDATE", default="False"))

    def get_server_ip(self):
        return env.get("SERVER_IP")

    def get_branch(self):
        return env.get("BRANCH")

    def get_changelist(self):
        return env.get("CHANGELIST")

    def get_force_update(self):
        return common.str2bool(env.get("FORCE_UPDATE"))

    def get_deploy_type(self):
        return env.get("DEPLOY_TYPE")

    def notice_to(self) -> List[str]:
        notice_to = env.get("NOTICE_TO")
        if notice_to == "None" or not notice_to:
            return []

        result = []
        for persion in notice_to.split(","):
            if not persion.endswith("@h3d.com.cn"):
                persion += "@h3d.com.cn"
            result.append(persion)
        return result


env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
