# coding=utf-8
import re
from pathlib import Path
from typing import Union

from frame import FtpMgr, PyframeException, cmd, os
from project.x51.deploy.deploy_server.mgr.env_mgr import jenkins_env_mgr


class LinuxBinMgr:
    def __init__(self, branch: str):
        self._branch = branch
        self._remote_linux_bin_path = f"/data/mockp4/x51/{self._branch}/exe/linux_bin"
        self._local_bin_path = f"/data/workspace/x51/{self._branch}/exe/bin/"
        self._bin_version_path = f"/data/workspace/x51/{self._branch}/exe/bin/bin_version.txt"

    def match_bin_version(self, changelist: str) -> Union[str, None]:
        """
        匹配linux_bin的版本
        Args:
            changelist:

        Returns:
            str: linux_bin_version
        """
        versions = os.listdir(self._remote_linux_bin_path)
        versions.sort(reverse=True)
        for version in versions:
            arr = version.split("_")
            if len(arr) != 4:
                continue
            # 根据p4号寻找
            if int(arr[3]) <= int(changelist):
                return version
        return None

    def read_local_bin_version(self) -> Union[str, None]:
        """
        读取本地bin版本
        Returns:
            str: 本地bin版本
        """
        p = Path(self._bin_version_path)
        if p.exists():
            return p.read_text(encoding="utf-8")
        return None

    def write_local_bin_version(self, version: str):
        """
        写入本地bin版本
        Args:
            version: 版本号
        """
        p = Path(self._bin_version_path)
        p.write_text(data=version, encoding="utf-8")

    def download_linux_bin(self):
        """
        下载linux_bin
        Args:
            version:
        """
        ftp_mgr = FtpMgr(ip="**************", port=21, username="administrator", password="Admin123")
        remote_folder = f"/x51/server/{jenkins_env_mgr.branch}/svn_{jenkins_env_mgr.svn_version}_p4_{jenkins_env_mgr.changelist}"
        if not ftp_mgr.exists_folder(remote_folder):
            raise PyframeException(f"svn {jenkins_env_mgr.svn_version} p4 {jenkins_env_mgr.changelist} 不存在")
        ftp_mgr.download_folder(
            src=remote_folder,
            dst=self._local_bin_path,
        )

    def decompress_linux_bin(self):
        """
        解压linux_bin
        """
        ret = cmd.run_shell(
            cmds=["pzstd -r -d --force --rm *.zst -p 28"],
            workdir=self._local_bin_path,
        )
        if ret[0] != 0:
            raise PyframeException(
                message="解压linux_bin失败，请联系流水线管理员",
                mentioned_list=["<EMAIL>"],
            )

    def download_linux_bin_from_ftp(self, svn_ver: str, p4_changelist: str):
        """
        下载linux_bin
        Args:
            version:
        """
        if not jenkins_env_mgr.p4_2005_bin:
            remote_prefix = "/x51/tester/server"
        else:
            remote_prefix = "/x51/server"
        ftp_mgr = FtpMgr(ip="**************", port=21, username="administrator", password="Admin123")
        remote_folder = f"{remote_prefix}/{self._branch}/svn_{svn_ver}_p4_{p4_changelist}"
        if not ftp_mgr.exists_folder(remote_folder):
            raise PyframeException(f"svn {svn_ver} p4 {p4_changelist} 不存在")
        ftp_mgr.download_folder(
            src=remote_folder,
            dst=f"/data/workspace/x51/{self._branch}/exe/bin",
        )

    def find_ftp_linux_bin_by_svn(self, svn: str):
        ftp_mgr = FtpMgr(ip="**************", port=21, username="administrator", password="Admin123")

        if jenkins_env_mgr.p4_2005_bin:
            remote_prefix = "/x51/server"
        else:
            remote_prefix = "/x51/tester/server"
        if svn.isdigit():
            for version in ftp_mgr.dirs(f"{remote_prefix}/{self._branch}"):
                pattern = re.compile("^svn_(\d+)_p4_(\d+)$")  # 将遍历后的目录通过正则筛选一次
                match = re.findall(pattern, version)
                if svn not in version:
                    continue
                if match and len(match[0]) == 2:
                    return match[0]
            return None, None
        else:
            #  取最大svn && 最大changelist
            versions = {}
            for version in ftp_mgr.dirs(f"{remote_prefix}/{self._branch}"):
                pattern = re.compile("^svn_(\d+)_p4_(\d+)$")  # 将遍历后的目录通过正则筛选一次
                match = re.findall(pattern, version)
                if match and len(match[0]) == 2:
                    versions[match[0][0]] = match[0][1]

            svn = max(versions)
            return svn, versions[svn]

    def find_ftp_linux_bin_by_changelist(self, changelist: str):
        ftp_mgr = FtpMgr(ip="**************", port=21, username="administrator", password="Admin123")
        if jenkins_env_mgr.p4_2005_bin:
            remote_prefix = "/x51/server"
        else:
            remote_prefix = "/x51/tester/server"
        all_versions = []
        for version in ftp_mgr.dirs(f"{remote_prefix}/{self._branch}"):
            pattern = re.compile("^svn_(\d+)_p4_(\d+)$")  # 将遍历后的目录通过正则筛选一次
            match = re.findall(pattern, version)
            # if changelist not in version:
            #     continue
            # if match and len(match[0]) == 2:
            #     return match[0]
            if not match:
                continue
            all_versions.append(match[0])

        # changelist 排序
        all_versions.sort(reverse=True, key=lambda x: int(x[1]))
        for version in all_versions:
            if version[1] <= changelist:
                return version

        return None, None
