class Cos:
    secret_id = "AKIDZy96qMEjcswmPopveCbqreDYeV6WPdKl"
    secret_key = "WnpcATYQ5XlmY6OTLzQdfT3PXR6WgdMs"
    region = "ap-beijing"
    bucket = "123-dancer-beijing-1259494283"
    upload_group = 10


class Redis:
    host = "*************"
    port = 6379
    db = 0
    queue = "dump_queue_dev"


class ES:
    domain = "http://*************:9200"
    index_name = "x51_dump_stack"
    history_index_name = "x51_dump_stack_history"


class P4:
    # TODO 个人账号密码
    port = "p4.com:2003"
    user = "songmin"
    password = "Lang5208"


class FTP:
    # host = "*************"
    host = "dgmdd.h3d.com.cn"
    port = 21
    username = "administrator"
    password = "dgm123!#*"
    upload_path_prefix = "/dgm/version_test/x51_dump_test"


class Nexus:
    user = "pipeline-storage"
    password = "BKuinNNk"
    download_url = "http://nexus.h3d.com.cn/repository/pipeline-storage/x51/dump_tool/v1.0/dump_tool.zip"


class Analyze:
    process_num = 10
    stack_file_name = "dump_stack.txt"


class Jenkins:
    # TODO 个人账号密码
    url = "http://jenkins-x51.h3d.com.cn/"
    username = "<EMAIL>"
    password = "4BBfdk0UyXYPpN9gLHAXFj3YPKSAnSBS"
    jobs = {
        "center/test/dump_analyze_worker1",
        # "center/test/dump_analyze_worker2",
        # "center/test/dump_analyze_worker3",
    }
    agent = [
        "**************",
        # "**************",
        # "**************",
    ]
    dump_collection_job = "center/test/dump_collection"
    analysis_monitor_job = "center/test/analysis_monitor"


class Notification:
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1d143382-fafd-40fd-8599-262860acbb37"
    users = ["<EMAIL>", "<EMAIL>"]
