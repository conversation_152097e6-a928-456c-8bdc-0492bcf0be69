from frame import advance, log

from project.x51.dump_analysis.mgr.analysis_mgr import AnalysisMgr
from project.x51.dump_analysis.mgr.processing_data_mgr import ProcessDataMgr
from project.x51.dump_analysis.mgr.env_mgr import JenkinsEnvMgr
from project.x51.dump_analysis.mgr.exceptions import X51DumpAnalyzeException


@advance.stage_with_exception(stage="dump分析", exception=X51DumpAnalyzeException)
def analyze_dump():
    p4_bin_branch = JenkinsEnvMgr.get_bin_branch()
    p4_shell_branch = JenkinsEnvMgr.get_shell_branch()
    time_str = JenkinsEnvMgr.get_time_str()
    p4_force_sync = JenkinsEnvMgr.get_p4_force_sync()

    # 分析dump
    analysis_mgr = AnalysisMgr(p4_bin_branch=p4_bin_branch, p4_shell_branch=p4_shell_branch, time_str=time_str)
    log.info("开始下载dump分析工具")
    analysis_mgr.get_dump_tool()
    log.info("开始更新p4")
    analysis_mgr.sync_p4(force=p4_force_sync)
    log.info("开始下载dump文件")
    analysis_mgr.download_dump_file()
    log.info("开始解压dump文件")
    analysis_mgr.unzip_dump_file()
    log.info("开始解压所有zip文件")
    analysis_mgr.unzip_all_zip()
    log.info("开始分析dump文件")
    analysis_mgr.start()

    # dump数据入库
    processing_data_mgr = ProcessDataMgr(p4_bin_branch, p4_shell_branch)
    stack_files = analysis_mgr.find_all_stack_files()
    for stack_file in stack_files:
        processing_data_mgr.process_and_store_dump(stack_file)


def on_success():
    # wechat.send_unicast_post_success()
    advance.insert_pipeline_history_on_success()


def on_failure():
    # wechat.send_unicast_post_failure()
    advance.insert_pipeline_history_on_failure()


def on_unstable():
    # wechat.send_multicast_post_unstable()
    advance.insert_pipeline_history_on_unstable()
