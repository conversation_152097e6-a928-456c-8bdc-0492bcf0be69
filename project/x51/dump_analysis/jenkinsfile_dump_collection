node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "dump_collection"
            customWorkspace "D:/jenkins_dump_collection"
        }
    }
    parameters {
        string(name: 'dump_date', defaultValue: '', description: '输入dump日期', trim: true)
        string(name: 'game_zone', defaultValue: '', description: '游戏大区', trim: true)
    }
    options {
        disableConcurrentBuilds()
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("触发dump收集") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python x51.py dump_collection --job=collect_dump")
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python x51.py dump_collection --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python x51.py dump_collection --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}"){
                script {
                    bat(script: "python x51.py dump_collection --job=on_failure")
                }
            }
        }
    }
}