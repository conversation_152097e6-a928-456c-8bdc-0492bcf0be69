import os
import re
import time

from multiprocessing import Pool

from frame import env, path_mgr, cmd, log
from project.x51.dump_analysis.config import config
from project.x51.dump_analysis.mgr.nexus_mgr import NexusMgr
from project.x51.dump_analysis.mgr.zipfile_mgr import ZipFileMgr
from project.x51.dump_analysis.mgr.cos_mgr import CosMgr
from project.x51.dump_analysis.mgr.es_mgr import ElasticSearchMgr
from project.x51.dump_analysis.mgr.p4_mgr import P4Mgr


class AnalysisMgr:
    def __init__(self, p4_bin_branch: str, p4_shell_branch: str, time_str: str = None):
        if not time_str:
            time_str = time.strftime("%Y_%m%d_%H%M%S", time.localtime(int(time.time())))

        self.time_str = time_str
        self.download_path = os.path.join(env.pipeline.workspace(), f"dump_{self.time_str}")
        self.unzip_path_prefix = os.path.join(env.pipeline.workspace(), f"unzip_dump_{self.time_str}")
        self.dump_tool_path = os.path.join(env.pipeline.workspace(), "dump_tool")

        self.cos_client = CosMgr()
        self.es_client = ElasticSearchMgr()
        self.p4_client = P4Mgr(p4_bin_branch, p4_shell_branch)
        self.nexus_client = NexusMgr()

        self.analysis_config = config.Analyze

    def _get_qq_dirs(self) -> list:
        """
        获取QQ号目录列表
        Returns:
            list: QQ号目录列表
        """
        depth = 6
        exclude = ["result", "users", "dump", "games"]
        dump_dirs = []
        for root, dirs, _ in os.walk(self.unzip_path_prefix):
            current_depth = root.count(os.sep)
            if current_depth >= depth:
                del dirs[:]
                continue

            dirs[:] = [d for d in dirs if d not in exclude]
            for d in dirs:
                if re.match(re.compile(r"^([1-9][0-9]{4,}|passwd)$", re.S), d):
                    dump_dirs.append(os.path.join(root, d))
                    continue

        return dump_dirs

    def sync_p4(self, force: bool = False):
        self.p4_client.sync(force)

    def download_dump_file(self):
        """
        从COS下载dump文件
        """
        self.cos_client.check_and_download(download_path=self.download_path)

    def unzip_dump_file(self):
        """
        解压dump文件
        """
        for root, _, files in os.walk(self.download_path):
            for file in files:
                file_name, _ = os.path.splitext(file)
                ZipFileMgr.decompress_zip(src=os.path.join(root, file), dst=os.path.join(self.unzip_path_prefix, file_name))

    def unzip_all_zip(self):
        """
        解压所有zip文件
        """
        for root, _, files in os.walk(self.unzip_path_prefix):
            for i, file in enumerate(files):
                if " " in file:
                    new_file = file.replace(" ", "")
                    os.rename(os.path.join(root, file), os.path.join(root, new_file))
                    files[i] = new_file

                file_name, suffix = os.path.splitext(files[i])
                if suffix == ".zip":
                    src_file = os.path.join(root, files[i])
                    ZipFileMgr.decompress_zip(src_file, os.path.join(root, file_name))
                    path_mgr.rm(src_file)

    def find_all_stack_files(self):
        stack_files_list = []
        for root, _, files in os.walk(self.unzip_path_prefix):
            for file in files:
                if file == self.analysis_config.stack_file_name:
                    stack_files_list.append(os.path.join(root, file))
                    continue

        return stack_files_list

    def get_dump_tool(self):
        """
        从制品库下载dump分析工具
        """
        download_path = f"{self.dump_tool_path}.zip"
        if path_mgr.exists(download_path):
            log.warn("dump分析工具已经存在， 跳过下载")
            return

        self.nexus_client.download_dump_tool(download_path)
        ZipFileMgr.decompress_zip(download_path, self.dump_tool_path)

    @staticmethod
    def analyze(dump_tool_path: str, dump_path: str, pdb_path: str, stack_save_path: str):
        """
        分析dump文件
        Args:
            dump_tool_path: dump分析工具路径
            dump_path: dump文件路径
            pdb_path: pdb文件路径
            stack_save_path: 分析结果保存路径
        """
        path_mgr.rm(os.path.join(dump_path, "result"))
        for root, dirs, files in os.walk(dump_path):
            for i, d in enumerate(dirs):
                if " " in d or "$" in d or "." in d or "*" in d or "?" in d:
                    new_dir = d.replace(" ", "").replace("$", "").replace(".", "").replace("*", "").replace("?", "")
                    # os.rename(os.path.join(root, d), os.path.join(root, new_dir))
                    path_mgr.move(src=os.path.join(root, d), dst=os.path.join(root, new_dir))
                    dirs[i] = new_dir

            for file in files:
                if " " in file:
                    new_file = file.replace(" ", "")
                    # os.rename(os.path.join(root, file), os.path.join(root, new_file))
                    path_mgr.move(src=os.path.join(root, file), dst=os.path.join(root, new_file))

        ret = cmd.run_shell(
            cmds=[rf"{dump_tool_path}\DumpTool.exe {dump_path} {pdb_path} {stack_save_path}"],
            verbose=None,
        )
        log.info(ret)

    def start(self):
        dump_dirs = self._get_qq_dirs()
        dump_dirs.reverse()
        process_pool_size = self.analysis_config.process_num
        p = Pool(process_pool_size)
        for i in range(len(dump_dirs)):
            dump_path = dump_dirs[i]
            p.apply_async(
                self.analyze,
                args=(
                    self.dump_tool_path,
                    dump_path,
                    self.p4_client.pdb_path,
                    os.path.join(dump_path, self.analysis_config.stack_file_name),
                ),
            )

        p.close()
        p.join()


if __name__ == "__main__":
    analysis_mgr = AnalysisMgr("branch_2023/QQX5_Mainland_6.3.4_03", "2023/6.3.4/6.3.7")
    analysis_mgr.get_dump_tool()
    # analysis_mgr.sync_p4_data(True)
    # analysis_mgr.download_dump_file()
    # analysis_mgr.unzip_dump_file()
    # analysis_mgr.start()
