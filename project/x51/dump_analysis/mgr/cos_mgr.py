import os
import time

from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client

from frame import log

from project.x51.dump_analysis.config import config
from project.x51.dump_analysis.models.files import UploadFile
from project.x51.dump_analysis.config.game_zone import game_zone_map
from project.x51.dump_analysis.models.message import Message
from project.x51.dump_analysis.models.files import DumpFile
from project.x51.dump_analysis.mgr.redis_mgr import RedisMgr
from project.x51.dump_analysis.mgr.exceptions import X51DumpAnalyzeException


class UploadFileMgr:
    @staticmethod
    def get_upload_file(zone: str) -> UploadFile:
        zone_list = list(game_zone_map.keys())
        file_name_prefix = f'dump_{time.strftime("%Y%m%d%H%M", time.localtime())}'
        full_file_name = file_name_prefix + "{:02}".format(zone_list.index(zone))
        return UploadFile(zone, full_file_name)


class CosMgr:
    __secret_id = config.Cos.secret_id
    __secret_key = config.Cos.secret_key
    __region = config.Cos.region
    __bucket = config.Cos.bucket
    __upload_group = config.Cos.upload_group

    def __init__(self):
        cos_config = CosConfig(Region=self.__region, SecretId=self.__secret_id, SecretKey=self.__secret_key)
        self.client = CosS3Client(cos_config)
        self.redis_mgr = RedisMgr()

    @staticmethod
    def check_game_zone(zone_list):
        if zone_list == ["all"]:
            zone_list = list(game_zone_map.keys())

        for zone in zone_list:
            if zone not in list(game_zone_map.keys()):
                raise X51DumpAnalyzeException(f"没有找到大区信息, 请检查大区信息是否有误或者联系管理员添加, {zone}")

        return zone_list

    def upload(self, zone_list: list, dump_date: str):
        zone_list = self.check_game_zone(zone_list)
        dump_file_mgr = UploadFileMgr()
        # 将请求分为10个一组发送到cos
        for i in range(0, len(zone_list), self.__upload_group):
            for zone in zone_list[i : i + self.__upload_group]:
                upload_file = dump_file_mgr.get_upload_file(zone)
                file_content = f"{zone} {dump_date}"
                response = self.client.put_object(
                    Bucket=self.__bucket,
                    Body=file_content.encode(),
                    Key=f"server/dev_ftp_mirror/yunwei/{upload_file.file_name}.txt",
                    StorageClass="STANDARD",
                )
                if not response.get("ETag"):
                    raise X51DumpAnalyzeException("触发dump收集失败")

                log.info(f"触发收集dump成功, 准备将数据写入redis, {upload_file.message}")
                self.redis_mgr.push(upload_file.message)

            # 除了最后一组请求, 其他每组请求之间间隔5分钟
            if i + self.__upload_group < len(zone_list):
                time.sleep(300)

        log.info(f"大区全部触发成功, {Message.FINISH_STATUS}")
        self.redis_mgr.push(Message.FINISH_STATUS)

    def check_cos(self, redis_message: Message) -> DumpFile:
        dump_file = DumpFile(redis_message)
        try:
            response = self.client.list_objects(
                Bucket=self.__bucket,
                Prefix=f"other/datadump/{dump_file.file_name}.zip",
            )
        except Exception as e:
            self.redis_mgr.revert(redis_message.message)
            raise X51DumpAnalyzeException(f"检查dump文件失败, 准备revert, {redis_message.message}, {e.__str__()}")

        if response.get("Contents"):
            return dump_file

        return DumpFile()

    def download(self, dump_file: DumpFile, download_path="."):
        log.info(f"检查到cos中已上传dump, 准备下载, {dump_file.file_name}")
        try:
            response = self.client.get_object(
                Bucket=self.__bucket,
                Key=f"other/datadump/{dump_file.file_name}.zip",
            )
            if not os.path.exists(download_path):
                os.makedirs(download_path)

            response["Body"].get_stream_to_file(f"{download_path}/{dump_file.zone}_{dump_file.file_name}.zip")

        except Exception as e:
            self.redis_mgr.revert(dump_file.upload_message())
            raise X51DumpAnalyzeException(f"下载dump文件失败, 准备revert, {dump_file.file_name}, {e.__str__()}")

    def check_and_download(self, download_path):
        """
        检查redis中是否有dump文件, 如果有则下载
        Args:
            download_path: 下载路径
        """
        while True:
            redis_message = self.redis_mgr.pop()
            if not redis_message.is_valid():
                time.sleep(10)
                continue

            if redis_message.is_finished():
                log.info("队列消费完成, 结束运行")
                break

            while not self.check_cos(redis_message).exists():
                time.sleep(10)
                continue

            self.download(DumpFile(redis_message), download_path)
