from frame import env, common


class JenkinsEnvMgr:
    @classmethod
    def get_dump_date(cls):
        return env.get("DUMP_DATE")

    @classmethod
    def get_game_zone(cls):
        return env.get("GAME_ZONE")

    @classmethod
    def get_choice_game_zone(cls):
        return env.get("CHOICE_GAME_ZONE")

    @classmethod
    def get_bin_branch(cls):
        return env.get("BIN_BRANCH")

    @classmethod
    def get_shell_branch(cls):
        return env.get("SHELL_BRANCH")

    @classmethod
    def get_time_str(cls):
        return env.get("TIME_STR")

    @classmethod
    def get_p4_force_sync(cls):
        return common.str2bool(env.get("P4_FORCE_SYNC", "false"))


class EnvMgr:
    @classmethod
    def set_game_zone(cls, game_zone):
        env.set({"GAME_ZONE": game_zone})

    @classmethod
    def get_game_zone(cls):
        return env.get("GAME_ZONE")

    @classmethod
    def set_trigger_url(cls, url: list):
        env.set({"TIGGER_URL": url})

    @classmethod
    def get_trigger_url(cls):
        return env.get("TIGGER_URL", [])

    @classmethod
    def set_dump_analysis_result(cls, result):
        env.set({"DUMP_ANALYSIS_RESULT": result})

    @classmethod
    def get_dump_analysis_result(cls):
        return env.get("DUMP_ANALYSIS_RESULT")

    @classmethod
    def set_dump_analyze_error_message(cls, msg):
        env.set({"DUMP_ANALYZE_ERROR_MESSAGE": msg})

    @classmethod
    def get_dump_analyze_error_message(cls):
        return env.get("DUMP_ANALYZE_ERROR_MESSAGE", [])
