import os.path

from frame import FtpMgr
from project.x51.dump_analysis.config import config


class FTPMgr:
    def __init__(self):
        self.ftp_config = config.FTP
        host = self.ftp_config.host
        port = self.ftp_config.port
        username = self.ftp_config.username
        password = self.ftp_config.password
        self.ftp_client = FtpMgr(ip=host, port=port, username=username, password=password)

        # self.upload_log_prefix = f"{ftp_config.upload_path_prefix}"
        # self.upload_dump_prefix = f"{ftp_config.upload_path_prefix}"

    def upload_file(self, file, dst):
        self.ftp_client.upload_file(file, os.path.join(self.ftp_config.upload_path_prefix, dst))

    # def upload_log(self, log_path, dst):
    #     self.ftp_client.upload_file(log_path, os.path.join(self.ftp_config.upload_path_prefix, dst))
    #
    # def upload_dump(self, dump_path, dst):
    #     self.ftp_client.upload_file(dump_path, os.path.join(self.ftp_config.upload_path_prefix, dst))


if __name__ == "__main__":
    ftp = FTPMgr()
    # f"/version_test/server_pack/{artifacts_branch}/"
    ftp.upload_file("./p4_mgr.py", "logs")
