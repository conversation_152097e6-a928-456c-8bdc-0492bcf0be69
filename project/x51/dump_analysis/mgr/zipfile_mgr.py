import time
import zipfile

from frame import log, common


class ZipFileMgr:
    @staticmethod
    def support_gbk(zf: zipfile.ZipFile, encode, decode):
        name_to_info = zf.NameToInfo
        for name, info in name_to_info.copy().items():
            try:
                real_name = name.encode(encode).decode(decode)
            except UnicodeDecodeError as e:
                log.warn(f"decode {zf.filename} error, {e.__str__()}")
                real_name = name

            if real_name != name:
                info.filename = real_name
                del name_to_info[name]
                name_to_info[real_name] = info

        return zf

    @classmethod
    def decompress_zip(cls, src: str, dst: str):
        """
        解压zip文件
        Args:
            src: zip文件路径
            dst: 解压路径
        """
        log.info(f"src: {src}")
        log.info(f"dst: {dst}")
        start_time = time.time()
        r = zipfile.is_zipfile(src)
        if not r:
            raise Exception("{} is not zip".format(src))
        f = zipfile.ZipFile(src, "r")
        f = cls.support_gbk(f, encode="cp437", decode="gbk")
        for file in f.namelist():
            f.extract(member=file, path=dst)

        log.info(f"cost time: {common.format_duration(time.time() - start_time)}")
