from dataclasses import dataclass
from typing import AnyStr
from frame import env

from frame import common
from project.x51 import config as x51_config

build_env = env.get("BUILD_ENV")


@dataclass
class P4Config:
    host: AnyStr = x51_config.P4_2003_TEST_CONFIG["host"]  # type: ignore
    username: AnyStr = x51_config.P4_2003_TEST_CONFIG["username"]  # type: ignore
    password: AnyStr = x51_config.P4_2003_TEST_CONFIG["password"]  # type: ignore
    client: AnyStr = f"x51_engine_update_{common.get_host_ip()}_{build_env}"  # type: ignore


@dataclass
class SvnConfig:
    project: AnyStr = "http://mug-qetest.h3d.com.cn:8000/svn/starx52"
    username: AnyStr = "mug_qe_svn"
    password: AnyStr = "111111"


class DevConfig:
    p4 = P4Config(username="yangming", password="19931213Ym")
    # p4 = P4Config(username="cheyuxuan", password="973")
    svn = SvnConfig()
    output2017_p4_files = ("Engine.pdb", "Engine.dll", "Effect_dx.pdb", "Effect_dx.dll", "ShaderCompiler_r.exe")
    output2017_svn_files = ("EffectCore_dx.h", "RenderCore_DX.h")
    robot_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=25714461-e96d-4c75-8e64-9118ba44c445"
    success_robot_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=25714461-e96d-4c75-8e64-9118ba44c445"
    rescue = False


class ProdConfig:
    p4 = P4Config(username="cheyuxuan", password="973")
    svn = SvnConfig(project="http://*************/svn/starx52", username="cheyuxuan", password="abcd1234")
    output2017_p4_files = ("Engine.pdb", "Engine.dll", "Effect_dx.pdb", "Effect_dx.dll", "ShaderCompiler_r.exe")
    output2017_svn_files = ("EffectCore_dx.h", "RenderCore_DX.h")
    robot_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=82d89df0-dcae-441c-a283-02b28011100b"
    success_robot_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e5c0e030-96c3-490f-a6d2-6cbf8d62bee2"
    rescue = True


if build_env == "DEV" or build_env == "TEST":
    config = DevConfig()
elif build_env == "PROD":
    config = ProdConfig()  # type: ignore
else:
    raise Exception(f"BUILD_ENV:{build_env}不合法")
