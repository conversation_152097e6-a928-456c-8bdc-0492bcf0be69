import re
import shutil

from frame import env, wechat
from frame.advance.advance import advance
from frame.exception.exception import PyframeException
from frame.log.log import log
from project.x51.engine_update.config.config import config
from project.x51.engine_update.mgr.p4_mgr import P4Mgr
from project.x51.engine_update.mgr.svn_mgr import SvnMgr


class EngineUpdate:
    def __init__(self, zip_path, p4_path, svn_path, commit_msg):
        self.commit_p4_info = None
        self.commit_svn_revision = None
        self.commit_msg = commit_msg
        self.zip_path = zip_path
        self.p4_path = p4_path
        self.svn_path = svn_path
        self.p4 = P4Mgr(self.p4_path, self.zip_path)
        self.svn = SvnMgr(config.svn.project, config.svn.username, config.svn.password)

    @property
    def engine_version(self):
        try:
            return re.findall(r"[0-9]+\.[0-9]+\.[0-9]+", self.zip_path.split("/")[-1])[0]
        except Exception:
            return "未匹配到版本号"

    def _download_zip(self):
        """
        下载zip包
        """
        log.info("下载zip包, zip_path: %s" % self.zip_path)
        zip_path = self.p4.sync_zip()
        extract_path = zip_path.parent / "extract"
        shutil.unpack_archive(str(zip_path), str(extract_path))
        log.info("解压成功")
        return extract_path

    def run(self):
        # 下载源数据
        source_path = self._download_zip()

        # 拷贝p4部分
        # data
        self.p4.sync_update_file()
        for branch in self.p4.p4_branchs:
            data_source = source_path / "exe" / "data"
            for file in data_source.rglob("*"):
                if file.is_file():
                    target = self.p4.local_root / branch / P4Mgr.data_path / file.relative_to(data_source)
                    target.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy(file, target)
                    log.info(f"拷贝 {file} to {self.p4.local_root / branch / P4Mgr.data_path / file.relative_to(data_source)}")
            # 3.6.8不支持
            # shutil.copytree(data_source, self.p4.local_root / P4Manager.data_path, dirs_exist_ok=True)

            # output2017
            output_source = source_path / "exe" / "output2017"
            output_target_bin = self.p4.local_root / branch / P4Mgr.output2017_paths[0]
            output_target_debug_bin = self.p4.local_root / branch / P4Mgr.output2017_paths[1]
            if not output_target_bin.exists():
                output_target_bin.mkdir(parents=True)
            if not output_target_debug_bin.exists():
                output_target_debug_bin.mkdir(parents=True)

            for file in config.output2017_p4_files:
                # origin file
                _p = output_source.joinpath(file)
                if _p.exists():
                    shutil.copy(_p, output_target_bin)
                    shutil.copy(_p, output_target_debug_bin)
                    log.info(f"拷贝 {_p} to {output_target_bin}  {output_target_debug_bin}")

                    # debug file
                    if _p.name in (
                        "Engine.pdb",
                        "Engine.dll",
                        "Effect_dx.pdb",
                        "Effect_dx.dll",
                    ):
                        # _debug_p = _p.with_stem(_p.stem + "_d")
                        suffix = _p.suffix
                        _debug_p = _p.with_name(_p.name.replace(suffix, "") + "_d" + suffix)
                        # 生成 file_d 文件
                        shutil.copy(_p, _debug_p)
                        if output_target_debug_bin.joinpath(_debug_p.name).exists():
                            log.info(f"p4 目录中存在{_debug_p}, 拷贝")
                            shutil.copy(_debug_p, output_target_debug_bin)
                            log.info(f"拷贝 {_debug_p} to {output_target_debug_bin}")
                        else:
                            log.info(f"p4 中不存在{_debug_p.name}不存在， 忽略")
                else:
                    log.warning(f"{_p} 不存在， 忽略")

        # 拷贝svn部分
        self.svn.set_workspace(self.svn_path)
        source_svn = source_path / "exe" / "output2017"
        target_svn = self.svn.local_workspace

        # copy engine_interface.h to RenderCore_DX.h
        s = source_svn / "engine_interface.h"
        if s.exists():
            shutil.copy(s, source_svn / "RenderCore_DX.h")

        # copy EffectCore.h to EffectCore_dx.h
        s = source_svn / "EffectCore.h"
        if s.exists():
            shutil.copy(s, source_svn / "EffectCore_dx.h")

        # modify EffectCore_dx.h
        s = source_svn / "EffectCore_dx.h"
        rewrite_text = s.read_text(encoding="GBK").replace("engine_interface.h", "RenderCore_DX.h")
        s.open("w", encoding="GBK").write(rewrite_text)

        for file in config.output2017_svn_files:
            _p = source_svn / file
            if _p.exists():
                shutil.copy(_p, target_svn)
                log.info(f"拷贝 {_p} to {target_svn}")
            else:
                log.warning(f"{_p} 不存在， 忽略")

        # 上传p4
        self.p4.mark_files()
        ret = self.p4.submit(self.commit_msg)
        self.commit_p4_info = self._parse_p4_submit(ret)

        # 上传svn
        self.commit_svn_revision = self.svn.submit_workspace(self.commit_msg)

    def teardown(self):
        self.p4.teardown()
        self.svn.teardown()
        shutil.rmtree("download", ignore_errors=True)

    @classmethod
    def _parse_p4_submit(cls, ret):
        """
        [{'change': '1347964', 'locked': '12'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/bin/Effect_dx.dll', 'rev': '10', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/bin/Effect_dx.pdb', 'rev': '9', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/bin/Engine.dll', 'rev': '9', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/bin/Engine.pdb', 'rev': '9', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/debug_bin/Effect_dx.dll', 'rev': '5', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/debug_bin/Effect_dx.pdb', 'rev': '5', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/debug_bin/Effect_dx_d.dll', 'rev': '5', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/debug_bin/Effect_dx_d.pdb', 'rev': '5', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/debug_bin/Engine.dll', 'rev': '5', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/debug_bin/Engine.pdb', 'rev': '5', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/debug_bin/Engine_d.dll', 'rev': '5', 'action': 'edit'}, {'depotFile': '//H3D_X51_res/X51_SourceBase/Test/QQX5_Mainland/trunc/exe/debug_bin/Engine_d.pdb', 'rev': '5', 'action': 'edit'}, {'submittedChange': '1347964'}]
        """
        if_data_update = False
        revision = None
        if ret:
            for r in ret:
                if isinstance(r, dict):
                    tmp = r.get("submittedChange", None)
                    if tmp is not None:
                        revision = tmp
                    tmp = r.get("depotFile", None)
                    if tmp is not None:
                        if "exe/data" in tmp:
                            if_data_update = True
            # for i in ret[1:-1]:
            #     if "exe/data" in i.get("depotFile"):
            #         if_data_update = True
            #         break

        return {"revision": revision, "if_data_update": if_data_update}


@advance.stage(stage="更新引擎")
def update_engine(**kwargs):
    """
    更新引擎
    """
    try:
        # zip_path
        zip_path = env.get("ENGINE_RESOURCE")
        if not re.match("//H3D_X51_res.+\.zip", zip_path):
            raise PyframeException(f"引擎zip包路径错误! {zip_path}")

        # svn_path
        svn_path = env.get("CODE")
        if not svn_path.strip("/").endswith("share/h3d"):
            raise Exception("svn路径只能是share/h3d")
        if "com.cn" in svn_path and "8000" not in svn_path:
            svn_path = svn_path.replace("com.cn", "com.cn:8000")
        if "*************:8000" in svn_path:
            raise PyframeException("代码路径端口错误，参考值http://*************/svn/starx52/trunc/share/h3d")

        if not env.get("COMMIT_MSG"):
            raise Exception("请填写提交信息")
        engine_update = EngineUpdate(
            zip_path=zip_path,
            p4_path=env.get("P4_PATH").splitlines(),
            svn_path=svn_path,
            commit_msg=env.get("COMMIT_MSG"),
        )
        engine_update.run()
    except Exception as e:
        log.error(f"更新引擎失败, {e}")
        on_failure(error=str(e))
        raise e
    else:
        on_success(engine_update=engine_update)
    return


def on_success(**kwargs):
    engine_update: EngineUpdate = kwargs.get("engine_update")
    engine_update.teardown()

    branch_msg = ", ".join(engine_update.p4.p4_branchs)
    #     content = f"""
    # 引擎版本：{engine_update.engine_version}
    # 更新的分支：（{branch_msg}）
    # 代码更新： {'有' if engine_update.commit_svn_revision else '无'}
    # data更新：{'有' if engine_update.commit_p4_info.get("if_data_update") else '无'}
    # svn：{engine_update.commit_svn_revision}
    # p4：{engine_update.commit_p4_info.get("revision")}
    # """
    content = f"引擎版本: {engine_update.engine_version}\n" if engine_update.engine_version else ""
    content += f"分支: {branch_msg}\n" if branch_msg else ""
    content += f"代码更新: {'有' if engine_update.commit_svn_revision else '无'}\n"
    content += f"data更新: {'有' if engine_update.commit_p4_info.get('if_data_update') else '无'}\n"
    content += f"svn版本: {engine_update.commit_svn_revision}\n"
    content += f"p4版本: {engine_update.commit_p4_info.get('revision')}\n"

    wechat.send_multicast_msg(
        webhook=config.success_robot_url,
        content=content,
        mentioned_list=["@all"],
        markdown=False,
    )
    # wechat.send_unicast_post_success(content=content)
    # 发给流水线和触发人
    operator = env.pipeline.pipeline_operator_email()
    wechat.send_unicast_post_success(user_list=[operator] if operator else None, content=content)


def on_failure(**kwargs):
    error = kwargs.get("error", "")
    wechat.send_multicast_msg(content=error, webhook=config.robot_url)
    wechat.send_unicast_post_failure(content=error)
