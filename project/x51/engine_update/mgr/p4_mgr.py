import re
import shutil
from pathlib import Path, PurePath

from frame import P4Client, common, log
from project.x51.engine_update.config.config import config


class P4Mgr:
    output2017_paths = (PurePath("bin"), PurePath("debug_bin"))
    data_path = PurePath("data")
    local_root = Path("p4_root")
    download_path = Path("download")

    def __init__(self, exe_path, zip_path):
        self.client = f"x51_engine_update_{config.p4.username}_{common.get_host_ip()}"
        self.p4 = P4Client(
            host=config.p4.host,
            username=config.p4.username,
            password=config.p4.password,
            client=self.client,
        )
        self.exe_path = [PurePath(_) for _ in exe_path]
        self.zip_path = PurePath(zip_path)
        self.p4.set_root(self.local_root.absolute().as_posix())
        log.info(f"set root {self.local_root.absolute().as_posix()}")
        self.p4.set_options(allwrite=True)
        self.set_views()
        self.p4.set_line_end("win")
        self.p4.set_encoding(encoding="GBK")
        # self.p4.set_charset(charset=P4Client.Charset.GBK)

    @property
    def p4_branchs(self):
        versions = list()
        for p in self.exe_path:
            if "/trunc/" in p.as_posix():
                versions.append("trunc")
            elif re.match(r".*QQX5_Mainland_\d+\.\d\.\d_\d\d.*", p.as_posix()):
                branch = re.findall(r"QQX5_Mainland_(\d+\.\d\.\d)_\d\d", p.as_posix())[0]
                versions.append(branch)
            else:
                branch = re.findall("QQX5_Mainland_(.*)\/", p.as_posix())[0]
                versions.append(branch)
        return versions

    def set_views(self):
        views = []
        # 2017
        for exe_path, branch in zip(self.exe_path, self.p4_branchs):
            for path in self.output2017_paths:
                views.append(f"{exe_path.as_posix()}/{path.as_posix()}/... //{self.client}/{branch}/{path.as_posix()}/...")

            # data
            views.append(f"{exe_path.as_posix()}/{self.data_path.as_posix()}/... //{self.client}/{branch}/{self.data_path.as_posix()}/...")

        # zip
        views.append(f"{self.zip_path.parent.as_posix()}/... //{self.client}/{self.download_path.as_posix()}/...")

        self.p4.set_view(views)

    def sync_update_file(self):
        """
        同步所有文件
        """
        #  output2017 文件 Engine.pdb、Engine.dll、Effect_dx.pdb、Effect_dx.dll
        for branch in self.p4_branchs:
            for name in config.output2017_p4_files:
                suffix = name.split(".")[-1]
                stem = name.split(".")[0]
                debug_name = stem + "_d." + suffix
                for path in self.output2017_paths:
                    self.p4.sync(f"//{self.client}/{branch}/{path.as_posix()}/{name}", force=True)
                    self.p4.sync(
                        f"//{self.client}/{branch}/{path.as_posix()}/{debug_name}",
                        force=True,
                    )

            # data文件
            self.p4.sync(f"//{self.client}/{branch}/{self.data_path}/...", force=True)

    def sync_zip(self) -> Path:
        """
        同步zip包
        """
        zip_path = Path(self.zip_path)
        local_p = self.download_path / zip_path.name
        # origin_encoding = self.p4.get_encoding()
        # self.p4.set_encoding(encoding="GBK")
        self.p4.print(self.zip_path.as_posix(), str(local_p))
        # self.p4.set_encoding(origin_encoding)
        return local_p

    def mark_files(self):
        """
        使用reconcile标记文件, 并把文件add，和edit
        """
        # 2017
        for branch in self.p4_branchs:
            for path in self.output2017_paths:
                self.p4.reconcile(str(self.local_root.absolute() / branch / path) + "/...")

            # data
            self.p4.reconcile(str(self.local_root.absolute() / branch / self.data_path) + "/...")

    def submit(self, description):
        try:
            return self.p4.submit(description, revert_if_failed=True)
        except Exception as e:
            if "No files to submit from the default changelist" in str(e):
                log.info("没有文件需要提交")
            else:
                raise e

    def teardown(self):
        self.p4.delete_client(self.client)
        shutil.rmtree(self.local_root, ignore_errors=True)
        shutil.rmtree(self.download_path, ignore_errors=True)
