import os
import shutil
from stat import S_IWRITE

from frame import log, cmd

from pathlib import Path, PurePath


class SvnMgr:
    def __init__(self, project_path, username=None, password=None):
        self.project_path = project_path
        self.username = username
        self.password = password
        self._workspace = None

    def set_workspace(self, workspace):
        _workspace = PurePath(workspace)
        if workspace.startswith(self.project_path):
            _workspace = _workspace.relative_to(self.project_path)
        else:
            _workspace = _workspace
        self._run_svn_command("checkout", [self.project_path + "/" + _workspace.as_posix(), str(Path("svn_workspace") / _workspace)])

        self._workspace = _workspace

    @property
    def workspace(self) -> PurePath:
        return self._workspace

    @property
    def local_workspace(self):
        return Path("svn_workspace") / self.workspace

    def _run_svn_command(self, command, args):
        """
        Internal function to run an SVN command and return the output as a string.
        """
        full_command = ["svn"]
        if self.username:
            full_command.extend(["--username", self.username])
        if self.password:
            full_command.extend(["--password", self.password])
        full_command.extend([command] + args)

        log.info(full_command)
        if self.workspace is None:
            ret, result = cmd.run_shell([" ".join(full_command)], return_output=True, verbose=cmd.Verbose.LOG)
        else:
            ret, result = cmd.run_shell([" ".join(full_command)], workdir=str(self.local_workspace), return_output=True, verbose=cmd.Verbose.LOG)

        if ret != 0:
            raise Exception("svn command failed", full_command, result)
        return result

    def submit_workspace(self, msg):
        msg = f'"{msg}"'
        self._run_svn_command("add", "--force .".split())
        ret = self._run_svn_command("commit", ["-m", msg])
        if ret:
            revision_msg = ret[-1]
            revision = revision_msg.split()[-1].strip(".")
            return revision
        else:
            return None

    def teardown(self):
        log.info(f"清空workspace, {self.local_workspace}")
        shutil.rmtree("svn_workspace", onerror=self.on_rm_error)

    @classmethod
    def on_rm_error(cls, func, path, exc_info):
        os.chmod(path, S_IWRITE)
        os.unlink(path)
