import os

from frame import advance, cmd, env, PyframeException

from project.x51.excel2csv.mgr.p4_mgr import P4Mgr


@advance.stage("更新p4")
def sync_p4():
    p4_mgr = P4Mgr()
    p4_mgr.sync_all()


@advance.stage("执行转换")
def transfer_config():
    ret = cmd.run_shell(
        cmds=["call start.bat"],
        workdir=os.path.join(env.pipeline.workspace(), "exe", "csv_tool"),
    )
    if ret[0] != 0:
        raise PyframeException(f"执行转换失败: {ret[1]}")


def on_success():
    pass


def on_failure():
    pass


def on_unstable():
    pass
