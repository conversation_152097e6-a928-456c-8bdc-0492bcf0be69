import re
import os

from frame import env, log, common, P4Client
from project.x51.excel2csv import config
from project.x51.excel2csv.mgr.env_mgr import JenkinsEnvMgr


class P4Mgr:
    def __init__(self):
        self.p4_port = config.P4.port
        self.p4_user = config.P4.user
        self.p4_password = config.P4.password
        self.workspace = env.pipeline.workspace()
        self.client = re.sub(re.compile(r"\W+", re.S), "_", f"{env.pipeline.job_name()}_engine_{common.get_host_ip()}")

        self.p4_client = P4Client(self.p4_port, self.p4_user, self.p4_password, self.client)
        self.p4_force = JenkinsEnvMgr.get_p4_force()

        p4_branch = JenkinsEnvMgr.get_p4_branch()
        views = [
            f"//H3D_X51_res/QQX5_Mainland/{p4_branch}/exe/csv_tool/... //{self.client}/exe/csv_tool/...",
            f"//H3D_X51_res/QQX5_Mainland/{p4_branch}/exe/server/config/... //{self.client}/exe/server/config/...",
        ]
        self.p4_client.set_view(views)
        self.p4_client.set_options(clobber=True, rmdir=True)
        self.p4_client.set_root(os.path.join(self.workspace))

    def sync_all(self):
        self.p4_client.sync_all(force=self.p4_force)

    def submit(self):
        reconcile_result = self.p4_client.reconcile(f"//{self.client}/...")
        # self.p4_client.revert(f"//{self.client}/...")  # 测试用
        if not reconcile_result:
            log.warn("no files to reconcile")
            return
        submit_result = self.p4_client.submit(f"[ci submit]")
        log.info(f"submit result: {submit_result}")
