# coding=utf-8
import os
from frame import *
from project.x51.generate_icon.mgr.env_mgr import env_mgr, jenkins_env_mgr
from project.x51.generate_icon.mgr.p4_mgr import p4_2004_mgr, p4_2011_mgr
from project.x51.generate_icon.mgr.gen_mgr import gen_mgr
from project.x51.generate_icon.mgr.gitlab_mgr import git_mgr
import shutil


@advance.stage(stage="清理项目分支")
def clean_branch(**kwargs):
        base_path = os.path.join(env.pipeline.workspace(),"project" )
        branches = [str(c.absolute()) for c in Path(base_path).glob("*branch_202*")]
        branches = sorted(branches, reverse=False)
        log.debug(f"branches: {branches}")
        for i in range(0, len(branches) - 1):
            log.warn(f"delete branch: {branches[i]}")
            path_mgr.rm(branches[i])

@advance.stage(stage="拉取项目p4")
def sync_project_p4(**kwargs):
    p4_version = jenkins_env_mgr.get_project_p4_version()
    force_update = jenkins_env_mgr.get_project_force_update()
    changelist = p4_version.strip().lower()
    if changelist == "head":
        changelist = p4_2004_mgr.get_latest_changes()
    env_mgr.set_project_changelist(changelist=changelist)
    log.info("获取 changelist并保存 = {}".format(changelist))
    # 同步p4
    p4_2004_mgr.sync_all(changelist=changelist, force=force_update)

@advance.stage(stage="拉取图标p4")
def sync_icon_p4(**kwargs):
    p4_version = jenkins_env_mgr.get_icon_p4_version()
    force_update = jenkins_env_mgr.get_icon_force_update()
    changelist = p4_version.strip().lower()    
    
    if changelist == "head":
        changelist = p4_2011_mgr.get_latest_changes()
    env_mgr.set_icon_changelist(changelist=changelist)
    log.info("获取 icon changelist并保存 = {}".format(changelist))
    # 同步p4
    p4_2011_mgr.sync_all(changelist=changelist, force=force_update)


@advance.stage(stage="获取 git 项目")
def clone_git_project(**kwargs):
    git_mgr.clone_projet()


@advance.stage(stage="执行工具")
def exec_tool(**kwargs):
    gen_mgr.exec_tool()

@advance.stage(stage="准备物品清单")
def parse_item_list(**kwargs):
    gen_mgr.exec_script(command="python3.10 -m cmds.prepare_manifest_x51")


@advance.stage(stage="导入新图标")
def import_new_icon(**kwargs):
    gen_mgr.exec_script(command=rf'python3.10 -m cmds.pipeline.copy_icons_by_subtype -p x51 --from-dir "\\f.h3d.com.cn\技术支持中心\高清图标\x51" --to-dir {os.path.join( env_mgr.get_hires_dir(), "icons")}')


@advance.stage(stage="合成图标")
def compose_icons_x52(**kwargs):
    gen_mgr.exec_script(command="python3.10 -m cmds.compose_icons -p x51")


@advance.stage(stage="复制饰品原始图标")
def copy_orig_icons(**kwargs):
    gen_mgr.exec_script(command="python3.10 -m cmds.pipeline.copy_orig_icons_x51")


@advance.stage(stage="提交高清图标修改")
def submit_icons(**kwargs):
    p4_2011_mgr.submit_icons()

@advance.stage(stage="删除共享目录内容")
def delete_shared_dictory(**kwargs):
    gen_mgr.exec_script(command=r"del \\f.h3d.com.cn\技术支持中心\高清图标\x51\* /S /Q")
    gen_mgr.exec_script(command=r'for /D %d in ("\\f.h3d.com.cn\技术支持中心\高清图标\x51\*") do rmdir /S /Q "%d"')


@advance.stage(stage="计算图标特征")
def calc_icon_feature(**kwargs):    
    gen_mgr.exec_script(command="python3.10 -m cmds.generate_features --proj=x51")


@advance.stage(stage="提交图标特征")
def submit_feats(**kwargs):    
    p4_2011_mgr.submit_feats()


@advance.stage(stage="打包数据集")
def pack_dataset(**kwargs):
    gen_mgr.package_datapack()

@advance.stage(stage="上传制品库")
def upload_to_database(**kwargs):
    gen_mgr.upload_to_database() 

@advance.stage(stage="错误信息")
def show_error(**kwargs):
    pass


@advance.stage(stage="成功通知")
def send_success(**kwargs):
    on_success(**kwargs)

@advance.stage(stage="失败通知")
def send_failure(**kwargs):
    on_failure(**kwargs)

@advance.stage(stage="取消通知")
def send_canceled(**kwargs):
    on_canceled(**kwargs)

def __get_detail_msg() -> str:
    # server_ip = env.get("SERVER_IP")

    project_branch = jenkins_env_mgr.get_project_branch()
    project_changelist = env_mgr.get_project_changelist()
    project_force = jenkins_env_mgr.get_project_force_update()
    icon_branch = env_mgr.get_icon_version_branch()
    icon_changelist = env_mgr.get_icon_changelist()
    icon_force = jenkins_env_mgr.get_icon_force_update()
    msg = (
        f"**项目分支**: {project_branch}\n"
        f"**项目提交号**: {project_changelist}\n"
        f"**是否强更项目**: {project_force}\n"
        f"**图标分支**: {icon_branch}\n"
        f"**图标提交号**: {icon_changelist}\n"
        f"**是否强拉图标**: {icon_force}\n"
    )
    
    pkl_url = env_mgr.get_pkl_url()
    if pkl_url:
        msg += f"**PKL文件**: [pkl文件目录]({pkl_url})\n"
    
    bad_item_url = env_mgr.get_bad_items_url()
    if bad_item_url:
        bad_item_name = bad_item_url.split("/")[-1]
        msg += f"**bad items**: [{bad_item_name}]({bad_item_url}#target=out)\n"
    
    missing_item_url = env_mgr.get_missing_items_url()
    if missing_item_url:
        missing_item_name = missing_item_url.split("/")[-1]
        msg += f"**missing items**: [{missing_item_name}]({missing_item_url}#target=out)\n"
    return msg


default_user_list = ["<EMAIL>"]

def on_success(**kwargs):
    """
    成功时的操作
    """
    # user_list = __get_user_list()
    wechat.send_unicast_post_success(user_list=default_user_list, content=__get_detail_msg())
    try:
        advance.insert_pipeline_history_on_success()
    except Exception as e:
        log.info("insert_pipeline_history_on_success failed: " + repr(e))


def on_failure(**kwargs):
    """
    失败时的操作
    """
    wechat.send_unicast_post_failure(user_list=default_user_list,rescue=True, content=__get_detail_msg())
    try:
        advance.insert_pipeline_history_on_failure()
    except Exception as e:
        log.info("insert_pipeline_history_on_failure failed: " + repr(e))


def on_canceled(**kwargs):
    """
    取消时的操作
    """
    wechat.send_unicast_post_canceled(content=__get_detail_msg())
    try:    
        advance.insert_pipeline_history_on_canceled()
    except Exception as e:
        log.info("insert_pipeline_history_on_canceled failed: " + repr(e))
