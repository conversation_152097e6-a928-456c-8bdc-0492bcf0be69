// 固定写法
node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_ENV = "TEST"
    }
}

def clone_pyframe_pipeline() {
    def branch = "sujiajun"
    if (!fileExists("pyframe-pipeline/.git")) {
        bat """
        git clone -b $branch https://gitlab.h3d.com.cn/dev-productivity/pyframe-pipeline.git
        cd pyframe-pipeline
        git config --local user.name "<EMAIL>"
        git config --local user.email "<EMAIL>"
        """
    }else{
        dir('pyframe-pipeline') {
            bat """
            git config pull.rebase false
            git clean -xdf -e logs
            git reset --hard HEAD
            git checkout $branch
            git pull origin $branch --quiet
            """
        }
    }
    dir('pyframe-pipeline') {
        bat """
        $env.PYFRAME_PYTHON -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
        $env.PYFRAME_PYTHON -m pip install -r requirements.txt -i  http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
        """
    }
}

pipeline {
    agent {
        node {
            label "win10_192.168.7.141"
        }
    }
    parameters {
        string(name: 'server_ip', defaultValue: '*************', description: '服务器IP', trim: true)
        booleanParam(name: 'force_update', defaultValue: true, description: '是否强制更新p4')
        string(name: 'changelist', defaultValue: '1380095', description: 'p4 changelist', trim: true)
        string(name: 'branch', defaultValue: 'branch_2023/QQX5_Mainland_6.5.6_07', description: '分支', trim: true)
        string(name: 'local_path', defaultValue: 'D:\\p4_com_2003', description: '分支', trim: true)
    }
    environment {
       PYFRAME_PYTHON= 'C:\\ProgramData\\miniconda3\\envs\\pyf368\\python.exe'
    }
    options {
        disableConcurrentBuilds()
    }

    stages {
        stage("下载pyframe脚本") {
            steps {
                clone_pyframe_pipeline()
             }
        }
        stage("安装python依赖") {
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            $env.PYFRAME_PYTHON -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("准备") {
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        $env.PYFRAME_PYTHON x51.py music_deploy_client --job=prepare
                        """
                    )
                }
            }
        }
        stage("同步p4") {
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        $env.PYFRAME_PYTHON x51.py music_deploy_client --job=sync_p4
                        """
                    )
                }
            }
        }
        stage("修改配置") {
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        $env.PYFRAME_PYTHON x51.py music_deploy_client --job=modify_config
                        """
                    )
                }
            }
        }
    }
    post {
        aborted {
            dir("pyframe-pipeline"){
                script {
                    bat(script: "$env.PYFRAME_PYTHON x51.py music_deploy_client --job=on_cancel")
                }
            }
        }
        success {
            dir("pyframe-pipeline"){
                script {
                    bat(script: "$env.PYFRAME_PYTHON x51.py music_deploy_client --job=on_success")
                }
            }
        }
        failure {
            dir("pyframe-pipeline"){
                script {
                    bat(script: "$env.PYFRAME_PYTHON x51.py music_deploy_client --job=on_failure")
                }
            }
        }
    }
}
