from frame import common, env


class EnvMgr:
    def __init__(self) -> None:
        pass

    def set_bin_changelist(self, changelist: str):
        env.set({"bin_changelist": changelist})

    def get_bin_changelist(self) -> str:
        return env.get("bin_changelist")

    def set_exe_changelist(self, changelist: str):
        env.set({"exe_changelist": changelist})

    def get_exe_changelist(self) -> str:
        return env.get("exe_changelist")

    def set_mannual_changelist(self, changelist: str):
        env.set({"mannual_changelist": changelist})

    def get_mannual_changelist(self) -> str:
        return env.get("mannual_changelist")


class JenkinsEnvMgr:
    def __init__(self) -> None:
        pass

    def get_changelist(self) -> str:
        return env.get("changelist")

    def get_branch(self) -> str:
        return env.get("branch")

    def get_server_ip(self) -> str:
        return env.get("server_ip")

    def get_client_ip(self) -> str:
        return env.get("client_ip")

    def get_local_path(self) -> str:
        return env.get("local_path")

    def get_force_update(self) -> bool:
        return common.str2bool(env.get("force_update"))


env_mgr = EnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
