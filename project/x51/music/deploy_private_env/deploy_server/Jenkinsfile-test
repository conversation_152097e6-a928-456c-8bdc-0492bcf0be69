// 固定写法
node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_ENV = "TEST"
    }
}

def clone_pyframe_pipeline() {
    def branch = "sujiajun"
    if (!fileExists("pyframe-pipeline/.git")) {
        sh """
        git clone -b $branch https://gitlab.h3d.com.cn/dev-productivity/pyframe-pipeline.git
        cd pyframe-pipeline
        git config --local user.name "<EMAIL>"
        git config --local user.email "<EMAIL>"
        """
    }else{
        dir('pyframe-pipeline') {
            sh"""
            git config pull.rebase false
            git clean -xdf -e logs
            git reset --hard HEAD
            git checkout $branch
            git pull origin $branch --quiet
            """
        }
    }
    dir('pyframe-pipeline') {
        sh """
        $env.PYFRAME_PYTHON -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
        $env.PYFRAME_PYTHON -m pip install -r requirements.txt -i  http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
        """
    }
}

pipeline {
    agent {
        node {
            label "tlinux2.2_172.17.60.232"
        }
    }
    parameters {
        booleanParam(name: 'force_update', defaultValue: true, description: '是否强制更新p4')
        string(name: 'changelist', defaultValue: '1380093', description: 'p4 changelist', trim: true)
        string(name: 'branch', defaultValue: 'branch_2023/QQX5_Mainland_6.5.6_07', description: '分支', trim: true)
        choice( name: 'deploy_type', choices: ["全服", "灰度"])
    }
    environment {
       PYFRAME_PYTHON= '/root/.conda/envs/pyf368/bin/python'
    }
    options {
        disableConcurrentBuilds()
    }

    stages {
        stage("下载pyframe脚本") {
            steps {
                clone_pyframe_pipeline()
             }
        }
        stage("安装python依赖") {
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                            $env.PYFRAME_PYTHON -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            $env.PYFRAME_PYTHON -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("准备") {
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                        $env.PYFRAME_PYTHON x51.py music_deploy_server --job=prepare
                        """
                    )
                }
            }
        }
        stage("同步p4") {
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                        $env.PYFRAME_PYTHON x51.py music_deploy_server --job=sync_p4
                        """
                    )
                }
            }
        }
        stage("下载并解压linux_bin") {
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                        $env.PYFRAME_PYTHON x51.py music_deploy_server --job=download_decompress_linux_bin
                        """
                    )
                }
            }
        }
        stage("获取svn脚本") {
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                        $env.PYFRAME_PYTHON x51.py music_deploy_server --job=download_svn_script
                        """
                    )
                }
            }
        }
        stage("修改配置") {
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                        $env.PYFRAME_PYTHON x51.py music_deploy_server --job=modify_config
                        """
                    )
                }
            }
        }
        stage("启动服务") {
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                        $env.PYFRAME_PYTHON x51.py music_deploy_server --job=start_server
                        """
                    )
                }
            }
        }
    }
    post {
        aborted {
            dir("pyframe-pipeline"){
                script {
                    sh(script: "$env.PYFRAME_PYTHON x51.py music_deploy_server --job=on_cancel")
                }
            }
        }
        success {
            dir("pyframe-pipeline"){
                script {
                    sh(script: "$env.PYFRAME_PYTHON x51.py music_deploy_server --job=on_success")
                }
            }
        }
        failure {
            dir("pyframe-pipeline"){
                script {
                    sh(script: "$env.PYFRAME_PYTHON x51.py music_deploy_server --job=on_failure")
                }
            }
        }
    }
}
