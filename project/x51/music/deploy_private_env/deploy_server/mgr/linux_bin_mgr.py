# coding=utf-8
from pathlib import Path
from typing import Union

from frame import PyframeException, cmd, os


class LinuxBinMgr:
    def __init__(self, branch: str):
        self.__branch = branch
        self.__remote_linux_bin = "/data/mockp4/x51/{}/exe/linux_bin".format(self.__branch)
        self.__local_bin = "/data/workspace/x51/{}/exe/bin/".format(self.__branch)
        self.__bin_version_path = "/data/workspace/x51/{}/exe/bin/bin_version.txt".format(self.__branch)

    def match_bin_version(self, changelist: str) -> Union[str, None]:
        """
        匹配linux_bin的版本
        Args:
            changelist:

        Returns:
            str: linux_bin_version
        """
        versions = os.listdir(self.__remote_linux_bin)
        versions.sort(reverse=True)
        for version in versions:
            arr = version.split("_")
            if len(arr) != 4:
                continue
            # 根据p4号寻找
            if int(arr[3]) <= int(changelist):
                return version
        return None

    def read_local_bin_version(self) -> Union[str, None]:
        p = Path(self.__bin_version_path)
        if p.exists():
            return p.read_text(encoding="utf-8")
        return None

    def write_local_bin_version(self, version: str):
        p = Path(self.__bin_version_path)
        p.write_text(data=version, encoding="utf-8")

    def download_linux_bin(self, version: str):
        """
        下载linux_bin
        Args:
            version:
        """
        src_path = os.path.join(self.__remote_linux_bin, version)
        dst_path = "/data/workspace/x51/{}/exe/bin".format(self.__branch)
        print(src_path, dst_path)
        cmd.run_shell(cmds=["cp -rvf {}/* {}".format(src_path, dst_path)])

    def decompress_linux_bin(self):
        command = "pzstd -r -d --force --rm *.zst -p 28"
        ret = cmd.run_shell(cmds=[command], workdir=self.__local_bin)
        if ret[0] != 0:
            raise PyframeException("解压linux_bin失败，请联系管理员。")
