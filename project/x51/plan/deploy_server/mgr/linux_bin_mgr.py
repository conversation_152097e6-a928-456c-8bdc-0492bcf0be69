# coding=utf-8
import os
import re
import time
from pathlib import Path
from typing import Union

from frame import FtpMgr, PyframeException, cmd, log


class LinuxBinMgr:
    def __init__(self, branch: str):
        self._branch = branch
        self._remote_linux_bin = f"/data/mockp4/x51/{self._branch}/exe/linux_bin"
        self._local_bin = f"/data/workspace/x51/{self._branch}/exe/bin/"
        self._bin_version_path = f"/data/workspace/x51/{self._branch}/exe/bin/bin_version.txt"

    def match_bin_version(self, changelist: str) -> Union[str, None]:
        """
        匹配linux_bin的版本
        Args:
            changelist:

        Returns:
            str: linux_bin_version
        """
        if not os.path.exists(self._remote_linux_bin):
            raise PyframeException(f"共享目录没有{self._branch}分支的linux_bin，请联系测试编译服务器")
        versions = os.listdir(self._remote_linux_bin)
        versions.sort(reverse=True)
        for version in versions:
            arr = version.split("_")
            if len(arr) != 4:
                continue
            # 根据p4号寻找
            if int(arr[3]) <= int(changelist):
                return version
        return None

    def read_local_bin_version(self) -> Union[str, None]:
        """
        读取本地的bin_version
        Returns:
            str: bin_version
        """
        p = Path(self._bin_version_path)
        if p.exists():
            return p.read_text(encoding="utf-8")
        return None

    def write_local_bin_version(self, version: str):
        """
        写入本地的bin_version
        Args:
            version: 版本号
        """
        p = Path(self._bin_version_path)
        p.write_text(data=version, encoding="utf-8")

    def download_linux_bin(self, version: str, timeout: int = 1800):
        """
        下载linux_bin
        Args:
            version: 版本号
            timeout: 超时时间，默认30分钟
        """
        src_path = os.path.join(self._remote_linux_bin, version)
        dst_path = self._local_bin
        if not Path(src_path).exists():
            raise PyframeException(f"共享目录没有{self._branch}分支的linux_bin，请先编译服务器再进行部署")

        log.info("开始下载")
        start = 0
        while not Path(src_path).joinpath("end").exists() and start < timeout:
            log.warning(f"{src_path}目录中未发现end文件，继续等待")
            time.sleep(10)
            start += 10

        if start >= timeout:
            raise PyframeException("获取linux_bin超时，请联系流水线组相关同事")
        code, output = cmd.run_shell(cmds=[f"cp -rvf {src_path}/* {dst_path}"])
        if code != 0:
            for line in output:
                if "No space left on device" in line:
                    raise PyframeException("服务器磁盘空间不足, 请清理磁盘空间后重试")
            raise PyframeException("下载linux_bin失败，请联系流水线组相关同事")

    def decompress_linux_bin(self):
        """
        解压linux_bin
        """
        command = "pzstd -r -d --force --rm *.zst -p 16"
        ret = cmd.run_shell(cmds=[command], workdir=self._local_bin)
        if ret[0] != 0:
            for line in ret[1]:
                if "No space left on device" in line:
                    raise PyframeException("解压linux_bin失败，No space left on device，磁盘空间不足，请清理磁盘空间后重试")
            raise PyframeException("解压linux_bin失败，请联系流水线组相关同事")

    def download_linux_bin_from_ftp(self, svn_ver: str, p4_changelist: str):
        """
        下载linux_bin
        Args:
            version:
        """
        ftp_mgr = FtpMgr(ip="**************", port=21, username="administrator", password="Admin123")
        remote_folder = f"/x51/tester/server/{self._branch}/svn_{svn_ver}_p4_{p4_changelist}"
        if not ftp_mgr.exists_folder(remote_folder):
            raise PyframeException(f"svn {svn_ver} p4 {p4_changelist} 不存在")
        ftp_mgr.download_folder(
            src=remote_folder,
            dst=f"/data/workspace/x51/{self._branch}/exe/bin",
        )

    def find_ftp_linux_bin_by_svn(self, svn: str):
        ftp_mgr = FtpMgr(ip="**************", port=21, username="administrator", password="Admin123")

        if svn.isdigit():
            for version in ftp_mgr.dirs(f"/x51/tester/server/{self._branch}"):
                pattern = re.compile("^svn_(\d+)_p4_(\d+)$")  # 将遍历后的目录通过正则筛选一次
                match = re.findall(pattern, version)
                if svn not in version:
                    continue
                if match and len(match[0]) == 2:
                    return match[0]
            return None, None
        else:
            #  取最大svn && 最大changelist
            versions = {}
            for version in ftp_mgr.dirs(f"/x51/tester/server/{self._branch}"):
                pattern = re.compile("^svn_(\d+)_p4_(\d+)$")  # 将遍历后的目录通过正则筛选一次
                match = re.findall(pattern, version)
                if match and len(match[0]) == 2:
                    versions[match[0][0]] = match[0][1]

            svn = max(versions)
            return svn, versions[svn]

    def find_ftp_linux_bin_by_changelist(self, changelist: str):
        ftp_mgr = FtpMgr(ip="**************", port=21, username="administrator", password="Admin123")
        all_versions = []
        for version in ftp_mgr.dirs(f"/x51/tester/server/{self._branch}"):
            pattern = re.compile("^svn_(\d+)_p4_(\d+)$")  # 将遍历后的目录通过正则筛选一次
            match = re.findall(pattern, version)
            # if changelist not in version:
            #     continue
            # if match and len(match[0]) == 2:
            #     return match[0]
            if not match:
                continue
            all_versions.append(match[0])

        # changelist 排序
        all_versions.sort(reverse=True, key=lambda x: int(x[1]))
        for version in all_versions:
            if version[1] <= changelist:
                return version

        return None, None
