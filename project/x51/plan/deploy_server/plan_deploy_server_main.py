# coding=utf-8
from pathlib import Path

from frame import (
    PyframeException,
    advance,
    cmd,
    env,
    log,
    path_mgr,
    proc_mgr,
    wechat,
)
from project.x51.plan.deploy_server.mgr.env_mgr import bk_env_mgr, env_mgr
from project.x51.plan.deploy_server.mgr.linux_bin_mgr import LinuxBinMgr
from project.x51.plan.deploy_server.mgr.p4_com_2003 import P4COM2003
from project.x51 import config


@advance.stage("准备")
def prepare(**kwargs):
    advance.check_disk_size(threshold=5)
    proc_mgr.kill_procs(names=["admin_proxy", "app_box", "service_box", "service_box64"])
    path_mgr.mkdir("/data/mockp4/x51")
    path_mgr.mkdir("/data/workspace/x51")
    path_mgr.mkdir("/data/workspace/logs")
    command = "mount.cifs -o username=age,password=111,dir_mode=0777,file_mode=0777 //172.17.100.6/release/x51 /data/mockp4/x51"
    ret = cmd.run_shell(cmds=[command])
    if ret[0] != 0:
        cmd.run_shell(cmds=["yum install nfs-utils cifs-utils -y", command])


@advance.stage("更新P4")
def sync_p4(**kwargs):
    branch = bk_env_mgr.get_branch()
    force_update = bk_env_mgr.get_force_update()
    changelist = bk_env_mgr.get_changelist()

    p4_2003 = P4COM2003(branch=branch)
    if changelist.lower() == "head":
        changelist = p4_2003.get_latest_changes()
    env_mgr.set_changelist(changelist=changelist)
    if not Path(f"/data/workspace/x51/{branch}").exists():
        force_update = True
    p4_2003.sync_all(changelist=changelist, force=force_update)
    # 替换周更配置
    p4_2003.sync_weekly_shop_config()


@advance.stage("修改配置")
def modify_config(**kwargs):
    branch = bk_env_mgr.get_branch()
    cmd.run_shell(
        cmds=[
            "chmod +x *.so app_box admin_proxy service_box* ktv_* console_admin",
            'echo "+0" > .faketime',
        ],
        workdir=f"/data/workspace/x51/{branch}/exe/bin",
    )
    cmd.run_shell(
        cmds=[
            "chmod +x *.so",
        ],
        workdir=f"/data/workspace/x51/{branch}/exe/bin/lib64",
    )
    cmd.run_shell(
        cmds=[
            "cp settings.inc.template settings.inc",
            "chmod +x *.sh",
            "sh auto_config.sh",
        ],
        workdir=f"/data/workspace/x51/{branch}/src/star",
    )
    ret = cmd.run_shell(
        cmds=[
            """
sed -i "s/IP_MYSQL_\(.*\)value=\\\".*\\"/IP_MYSQL_\\1value=\\\"************\\\"/g" macros.xml
 && sed -i "s/IP_GLOBAL_DB_MYSQL_\(.*\)value=\\\".*\\\"/IP_GLOBAL_DB_MYSQL_\\1value=\\\"************\\\"/g" macros.xml
 && sed -i "s/.*use_simulate.*/<use_simulate>1<\/use_simulate>/g" mall_server.xml
 && sed -i "s/\(.*svc_idip.*idip[23456].*\)/<\!-- \\1 -->/g" admin_proxy.xml
 && rm -fr ../logs
            """
        ],
        workdir=f"/data/workspace/x51/{branch}/exe/bin/config",
    )
    if ret[0] != 0:
        raise PyframeException("修改配置失败")

    ret = cmd.run_shell(
        cmds=[
            """
sed -i "s/\(.*\)args=\\\"\(.*\)/\\1args=\\\"-env FAKETIME_DONT_RESET=1 LD_PRELOAD=\/usr\/local\/lib\/faketime\/libfaketime.so.1 FAKETIME_TIMESTAMP_FILE=.faketime FAKETIME_NO_CACHE=1 NO_FAKE_STAT=1 DONT_FAKE_MONOTONIC=1 \\2/g" admin_proxy.xml
 && sed -i "s/\(.*\)service\(.*\)name\(.*\)display=\\\"\([a-z_]\{0,\}\)\\\"[ ]\{0,\}\/>/\\1service\\2name\\3display=\\\"\\4\\\" args=\\\"-env LD_PRELOAD=\/usr\/local\/lib\/faketime\/libfaketime.so.1 FAKETIME_TIMESTAMP_FILE=.faketime FAKETIME_NO_CACHE=1 NO_FAKE_STAT=1 DONT_FAKE_MONOTONIC=1\\\" \/>/g" admin_proxy.xml
            """
        ],
        workdir=f"/data/workspace/x51/{branch}/exe/bin/config",
    )
    if ret[0] != 0:
        raise PyframeException("修改admin_proxy.xml配置失败")

    if env.get("DEPLOY_TYPE") == "灰度":
        # 改配置,bin/config下的macros.xml中，SERVER_GROUP_ID改为133或151
        # bin/config/invite_return下创建133.txt或者151.txt
        # 备用命令 sed -i '7s/value="9999"/value="131"/g' macros.xml
        ret = cmd.run_shell(
            cmds=[
                """
sed -i 's/^.*<macro name="SERVER_GROUP_ID".*$/    <macro name="SERVER_GROUP_ID"            value="131"\/>/' macros.xml
                """
            ],
            workdir=f"/data/workspace/x51/{branch}/exe/bin/config",
        )
        if ret[0] != 0:
            log.error("修改配置失败")
            raise PyframeException("修改macros.xml配置失败")

        ret = cmd.run_shell(
            cmds=["touch 131.txt"],
            workdir=f"/data/workspace/x51/{branch}/exe/bin/config/invite_return",
        )
        if ret[0] != 0:
            log.error("touch 131.txt error")
            raise PyframeException("创建131.txt文件失败")
    elif env.get("DEPLOY_TYPE") == "全服":
        # 改配置,bin/config下的macros.xml中，SERVER_GROUP_ID改为9999
        # 备用命令 sed -i '7s/value="131"/value="9999"/g' macros.xml
        ret = cmd.run_shell(
            cmds=[
                """
sed -i 's/^.*<macro name="SERVER_GROUP_ID".*$/    <macro name="SERVER_GROUP_ID"            value="9999"\/>/' macros.xml
                """
            ],
            workdir=f"/data/workspace/x51/{branch}/exe/bin/config",
        )
        if ret[0] != 0:
            raise PyframeException(f"修改macros.xml配置失败")


@advance.stage("下载linux_bin并解压")
def download_decompress_linux_bin(**kwargs):
    branch = bk_env_mgr.get_branch()
    changelist = bk_env_mgr.get_changelist()

    linux_bin_mgr = LinuxBinMgr(branch=branch)
    # linux_bin_version = linux_bin_mgr.match_bin_version(changelist=changelist)
    svn_ver, changelist = linux_bin_mgr.find_ftp_linux_bin_by_changelist(changelist)
    if svn_ver and changelist:
        linux_bin_version = f"svn_{svn_ver}_p4_{changelist}"
    else:
        # linux_bin_version = linux_bin_mgr.match_bin_version(changelist=changelist)
        # if linux_bin_version is None:
        raise PyframeException(f"p4 changelist: {changelist}可能太老，制品库上已无该制品。请填写较新版本的changelist")

    linux_bin_mgr.download_linux_bin_from_ftp(svn_ver, changelist)
    # linux_bin_mgr.download_linux_bin(version=linux_bin_version)
    linux_bin_mgr.decompress_linux_bin()
    # linux_bin_mgr.write_local_bin_version(version=linux_bin_version)
    env_mgr.set_linux_bin_version(linux_bin_version=linux_bin_version)


@advance.stage("下载svn脚本")
def download_svn_script(**kwargs):
    branch = bk_env_mgr.get_branch()
    # path_mgr.rm("/data/workspace/x51/{}/src/star".format(branch))
    path_mgr.mkdir(f"/data/workspace/x51/{branch}/src/star")
    # TODO 个人账号密码
    try:
        username = config.SVN_CONFIG["username"]
        password = config.SVN_CONFIG["password"]
    except Exception as e:
        raise PyframeException(f"请配置正确的svn账号密码: {e}")
    command = f"svn checkout --force --depth=files http://*************/svn/starx52/trunc/ ./src/star --username {username} --password {password}"
    ret = cmd.run_shell(cmds=[command], workdir=f"/data/workspace/x51/{branch}")
    if ret[0] != 0:
        raise PyframeException("下载svn脚本失败")


@advance.stage("启动服务器")
def start_server(**kwargs):
    branch = kwargs.get("branch")
    ret = cmd.run_shell(
        cmds=["sh start_tlinux.sh"],
        workdir=f"/data/workspace/x51/{branch}/src/star",
        dot_kill_me=True,
    )
    if ret[0] != 0:
        raise PyframeException("启动服务器失败")


def _get_msg():
    branch = bk_env_mgr.get_branch()
    changelist = bk_env_mgr.get_changelist()
    linux_bin_version = env_mgr.get_linux_bin_version()
    force = bk_env_mgr.get_force_update()
    msg = ""
    msg += f"**分支:** {branch}\n" if branch is not None else ""
    msg += f"**changelist:** {changelist}\n" if changelist is not None else ""
    msg += f"**强更p4**: {force}\n" if force else ""
    msg += f"**服务器版本:** {linux_bin_version}" if linux_bin_version is not None else ""
    return msg


def on_success(**kwargs):
    wechat.send_unicast_post_success(user_list=["<EMAIL>"], content=_get_msg())
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(user_list=["<EMAIL>"], content=_get_msg())
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(user_list=["<EMAIL>"], content=_get_msg())
    advance.insert_pipeline_history_on_canceled()
