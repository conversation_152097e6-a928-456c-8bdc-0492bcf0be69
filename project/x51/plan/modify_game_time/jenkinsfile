node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    parameters {
        string(name: 'SERVER_IP', defaultValue: '', description: '服务器IP')
        string(name: 'P4_PATH', defaultValue: 'trunc', description: 'p4路径')
        string(name: 'CUSTUM_TIME', defaultValue: '2023-07-06 20:38:00', description: '自定义时间')
        booleanParam(name: 'RESTART_SERVER', defaultValue: true, description: '重启服务器')
        string(name: 'NOTICE_TO', defaultValue: '', description: '企微通知人员')
    }
    environment {
        PYFRAME_PYTHON = '/root/.conda/envs/pyf368/bin/python'
    }
    agent {
        node {
            label "tlinux2.2_${params.SERVER_IP}"
            customWorkspace "/data/workspace/pyframe-pipeline"
        }
    }

    stages {
        stage('查找python路径') {
            steps {
                script {
                    // 检查 conda 是否可用
                    def isCondaAvailable = sh(script: 'conda --version', returnStatus: true) == 0

                    if (isCondaAvailable) {
                        echo "Using conda Python environment"
                        PYFRAME_PYTHON = 'conda run -n pyf368 python'
                    } else {
                        echo "Using system Python3"
                        PYFRAME_PYTHON = 'python3'
                    }
                }
            }
        }
        stage("安装python依赖") {
            steps {
                script {
                    sh(
                        script: """
                            ${PYFRAME_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            ${PYFRAME_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("修改时间") {
            steps {
                script {
                    sh(script: "${PYFRAME_PYTHON} x51.py plan_modify_game_time --job=custom_game_time")
                }
            }
        }
        stage("重启服务器") {
            steps {
                script {
                    sh(script: "${PYFRAME_PYTHON} x51.py plan_modify_game_time --job=restart_server")
                }
            }
        }
    }
    post {
        success {
            script {
                sh(script: "${PYFRAME_PYTHON} x51.py plan_modify_game_time --job=on_success")
            }
        }
        failure {
            script {
                sh(script: "${PYFRAME_PYTHON} x51.py plan_modify_game_time --job=on_failure")
            }
        }
        aborted {
            script {
                sh(script: "${PYFRAME_PYTHON} x51.py plan_modify_game_time --job=on_canceled")
            }
        }
    }
}
