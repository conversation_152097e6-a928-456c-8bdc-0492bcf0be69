from pathlib import Path
from frame import *


class ConfigMgr:
    def compile_install_libfaketime(self):
        """
        编译安装libfaketime
        """
        ret, _ = cmd.run_shell(
            cmds=["sh compile_install_libfaketime.sh"], workdir="/data/workspace/pyframe-pipeline/scripts/x51", verbose=cmd.Verbose.LOG
        )
        if ret != 0:
            raise PyframeException("编译安装libfaketime失败")

    def modify_game_time(self, branch: str, custom_time: str):
        """
        修改游戏时间
        Args:
            branch: 分支
            custom_time: 自定义时间
        """
        workdir = os.path.join("/data/workspace/x51", branch, "exe/bin")
        if not Path(workdir).exists():
            raise PyframeException(f"服务器上{branch}不完整，请先部署服务器")
        if len(custom_time.strip(" ")) == 0:
            Path(os.path.join(workdir, ".faketime")).write_text("+0")
        else:
            Path(os.path.join(workdir, ".faketime")).write_text(f"@{custom_time}")


config_mgr = ConfigMgr()
