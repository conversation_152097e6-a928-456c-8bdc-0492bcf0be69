# coding=utf-8
import time
from typing import List

from frame import PyframeException, advance, cmd, env, log, proc_mgr, wechat
from project.x51.plan.modify_game_time.mgr.config_mgr import config_mgr
from project.x51.plan.modify_game_time.mgr.env_mgr import bk_env_mgr


@advance.stage(stage="自定义游戏时间")
def custom_game_time(**kwargs):
    branch = bk_env_mgr.get_branch()
    custom_time = bk_env_mgr.get_custom_time()
    config_mgr.compile_install_libfaketime()
    config_mgr.modify_game_time(branch=branch, custom_time=custom_time)


@advance.stage(stage="重启服务器")
def restart_server(**kwargs):
    if not bk_env_mgr.get_restart_server():
        return
    branch = bk_env_mgr.get_branch()
    log.info("使用stop.sh关闭服务器")
    ret = cmd.run_shell(
        cmds=["sh stop.sh"],
        workdir=f"/data/workspace/x51/{branch}/src/star",
        dot_kill_me=True,
    )
    if ret[0] != 0:
        log.error("sh 使用stop.sh failed")
        raise PyframeException("关闭服务器失败")
    log.info("强制杀死服务器进程")
    try:
        proc_mgr.kill_procs(names=["admin_proxy", "app_box", "service_box", "service_box64"])
    except Exception as e:
        log.warning("强制杀死服务器进程失败: " + str(e))
    time.sleep(3)
    log.info("使用 start_tlinux.sh 启动服务器")
    ret = cmd.run_shell(
        cmds=["sh start_tlinux.sh"],
        workdir=f"/data/workspace/x51/{branch}/src/star",
        dot_kill_me=True,
    )
    if ret[0] != 0:
        log.error("sh start_tlinux.sh failed")
        raise PyframeException("启动服务器失败")


def __get_msg():
    server = bk_env_mgr.get_server_ip()
    branch = bk_env_mgr.get_branch()
    custom_time = bk_env_mgr.get_custom_time()
    msg = f"**服务器**: {server}\n"
    msg += f"**分支**: {branch}\n"
    msg += f"**自定义时间**: {custom_time}"
    return msg


def __get_notice_to() -> List[str]:
    user_list = env.get("NOTICE_TO")
    if not user_list or user_list == "None":
        return []
    result = []
    for user in user_list.split(","):
        if not user.endswith("@h3d.com.cn"):
            user += "@h3d.com.cn"
        result.append(user)
    return result


def on_success(**kwargs):
    user_list = __get_notice_to()
    wechat.send_unicast_post_success(
        user_list=["<EMAIL>"] + user_list if user_list else [],
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    user_list = __get_notice_to()
    wechat.send_unicast_post_failure(
        user_list=["<EMAIL>"] + user_list if user_list else [],
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    user_list = __get_notice_to()
    wechat.send_unicast_post_canceled(
        user_list=["<EMAIL>"] + user_list if user_list else [],
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_canceled()
