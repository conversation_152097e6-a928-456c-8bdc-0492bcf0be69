from frame import common
from frame.log.log import log
from project.x51.test.deploy_server.mgr.env_mgr import env_mgr


class UserMachineDict(dict):
    def __init__(self):
        super().__init__(
            {
                "172.17.50.18": "<EMAIL>",
                "172.17.50.19": "<EMAIL>",
                "172.17.60.135": "<EMAIL>",
                "172.17.60.136": "<EMAIL>",
                "172.17.60.137": "<EMAIL>",
                "172.17.60.138": "<EMAIL>",
                "172.17.60.139": "<EMAIL>",
                "172.17.60.140": "<EMAIL>",
                "172.17.60.141": "sheng<PERSON><PERSON><PERSON>@h3d.com.cn",
                "172.17.60.142": "re<PERSON><PERSON><PERSON>@h3d.com.cn",
                "172.17.60.143": "<EMAIL>",
                "172.17.60.144": "<EMAIL>",
                "172.17.60.145": "<EMAIL>",
                "172.17.60.146": "<EMAIL>",
                "172.17.60.147": "<EMAIL>",
                "172.17.60.148": "<EMAIL>",
                "*************": "<EMAIL>",
                "*************": "<EMAIL>",
                "*************": "<EMAIL>",
                "*************": "<EMAIL>",
                "*************": "<EMAIL>",
                "*************": "<EMAIL>",
                "*************": "<EMAIL>",
            }
        )


class DeployMgr:
    @property
    def get_msg(self) -> str:
        """
        组织通知消息内容
        Returns:

        """
        msg = ""
        host_ip = common.get_host_ip()
        msg += f"**服务器:** {host_ip}\n" if host_ip else ""
        msg += f"**分支:** {env_mgr.get_branch()}\n" if env_mgr.get_branch() else ""
        msg += f"**changelist:** {env_mgr.get_changelist()}\n" if env_mgr.get_changelist() else ""
        return msg

    @property
    def get_user_list(self) -> list:
        """
        获取测试机器归属人
        Returns: 归属人邮箱地址
        """
        user = None
        try:
            ip_addr = common.get_host_ip()
            user = UserMachineDict().get(ip_addr)

        except Exception as e:
            log.error(e)

        return [user] if user else []


deploy_mgr = DeployMgr()
