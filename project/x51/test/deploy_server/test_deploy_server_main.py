# coding=utf-8

import os
import re
from pathlib import Path

from frame import PyframeException, advance, cmd, path_mgr, proc_mgr, wechat
from project.x51 import config
from project.x51.test.deploy_server.mgr.deploy_mgr import *
from project.x51.test.deploy_server.mgr.env_mgr import *
from project.x51.test.deploy_server.mgr.linux_bin_mgr import LinuxBinMgr
from project.x51.test.deploy_server.mgr.p4_com_2003 import P4COM2003


@advance.stage("准备")
def prepare(**kwargs):
    log.info("开始关服")
    proc_mgr.kill_procs(names=["admin_proxy", "app_box", "service_box", "service_box64"])
    path_mgr.mkdir("/data/mockp4/x51")
    path_mgr.mkdir("/data/workspace/x51")
    path_mgr.mkdir("/data/workspace/logs")
    command = "mount.cifs -o username=age,password=111,dir_mode=0777,file_mode=0777 //172.17.100.6/release/x51 /data/mockp4/x51"
    ret = cmd.run_shell(cmds=[command])
    if ret[0] != 0:
        cmd.run_shell(
            cmds=["yum install nfs-utils cifs-utils -y", command],
            verbose=cmd.Verbose.LOG,
        )


@advance.stage("更新P4")
def sync_p4(**kwargs):
    branch = env_mgr.get_branch()
    force_update = env_mgr.get_force_update()
    changelist = env_mgr.get_changelist()

    p4_2003 = P4COM2003(branch=branch)
    # 如果changelist（P4提交号）未填写，则根据SVN号计算
    if not changelist:
        svn_version = env_mgr.get_svn_version()
        if not svn_version:
            log.warning("svn_version未设置，将其设置为HEAD")
            svn_version = "HEAD"
            # raise PyframeException("请检查参数SVN_VERSION填写是否正确")
        linux_bin_mgr = LinuxBinMgr(branch=branch)
        svn_version, changelist = linux_bin_mgr.find_linux_bin_by_svn(svn_version)
        log.info(f"changelist: {changelist} svn_version: {svn_version}")
        env_mgr.set_svn(svn_version)
        if not changelist:
            raise PyframeException("根据SVN_VERSION号未找到对应LINUX BIN，请检查参数SVN_VERSION填写是否正确")
    env_mgr.set_changelist(changelist)
    log.info(f"changelist = {changelist}")
    if not Path(f"/data/workspace/x51/{branch}").exists():
        force_update = True
    p4_2003.sync_all(changelist=changelist, force=force_update)
    # 替换周更配置
    p4_2003.sync_weekly_shop_config()


@advance.stage("修改配置")
def modify_config(**kwargs):
    branch = env_mgr.get_branch()
    ret = cmd.run_shell(
        cmds=[
            "chmod +x *.so app_box admin_proxy service_box* ktv_* console_admin",
            'echo "+0" > .faketime',
        ],
        workdir=f"/data/workspace/x51/{branch}/exe/bin",
    )
    if ret[0] != 0:
        raise PyframeException("修改bin目录的文件权限失败，请流水线组检查原因")

    ret = cmd.run_shell(
        cmds=[
            'echo "+0" > .faketime',
        ],
        workdir=f"/data/workspace/x51/{branch}/exe/bin",
    )
    if ret[0] != 0:
        raise PyframeException("修改.faketime失败，请流水线组检查原因")

    ret = cmd.run_shell(
        cmds=[
            "chmod +x *.so",
        ],
        workdir=f"/data/workspace/x51/{branch}/exe/bin/lib64",
    )
    if ret[0] != 0:
        raise PyframeException("修改lib64目录的文件权限失败，请流水线组检查原因")

    ret = cmd.run_shell(
        cmds=[
            "cp settings.inc.template settings.inc",
            "chmod +x *.sh",
            # 2024/03/13 这个脚本里会运行修改配置
            "sh auto_config.sh",
        ],
        workdir=f"/data/workspace/x51/{branch}/src/star",
    )
    if ret[0] != 0:
        raise PyframeException("修改src/star目录的文件权限失败，请流水线组检查原因")

#     ip = common.get_host_ip()
#     ret = cmd.run_shell(
#         cmds=[
#             """
# sed -i "s/IP_MYSQL_\(.*\)value=\\\".*\\"/IP_MYSQL_\\1value=\\\"%s\\\"/g" macros.xml
# && sed -i "s/IP_GLOBAL_DB_MYSQL_\(.*\)value=\\\".*\\\"/IP_GLOBAL_DB_MYSQL_\\1value=\\\"%s\\\"/g" macros.xml
#             """
#             % (ip, ip)
#         ],
#         workdir=f"/data/workspace/x51/{branch}/exe/bin/config",
#     )
#     if ret[0] != 0:
#         raise PyframeException("修改bin/config/macros.xml失败，请流水线组检查原因")

#     ret = cmd.run_shell(
#         cmds=[
#             'sed -i "s/.*use_simulate.*/<use_simulate>1<\/use_simulate>/g" mall_server.xml',
#         ],
#         workdir=f"/data/workspace/x51/{branch}/exe/bin/config",
#     )
#     if ret[0] != 0:
#         raise PyframeException("修改bin/config/macros.xml失败，请流水线组检查原因")

#     ret = cmd.run_shell(
#         cmds=[
#             """
# sed -i "s/\(.*svc_idip.*idip[23456].*\)/<\!-- \\1 -->/g" admin_proxy.xml
# && sed -i "s/\(.*\)args=\\\"\(.*\)/\\1args=\\\"-env LD_PRELOAD=\/usr\/local\/lib\/faketime\/libfaketime.so.1 FAKETIME_TIMESTAMP_FILE=.faketime FAKETIME_NO_CACHE=1 NO_FAKE_STAT=1 DONT_FAKE_MONOTONIC=1 \\2/g" admin_proxy.xml
# && sed -i "s/\(.*\)service\(.*\)name\(.*\)display=\\\"\([a-z_]\{0,\}\)\\\"[ ]\{0,\}\/>/\\1service\\2name\\3display=\\\"\\4\\\" args=\\\"-env LD_PRELOAD=\/usr\/local\/lib\/faketime\/libfaketime.so.1 FAKETIME_TIMESTAMP_FILE=.faketime FAKETIME_NO_CACHE=1 NO_FAKE_STAT=1 DONT_FAKE_MONOTONIC=1\\\" \/>/g" admin_proxy.xml
#             """
#         ],
#         workdir=f"/data/workspace/x51/{branch}/exe/bin/config",
#     )
#     if ret[0] != 0:
#         raise PyframeException("修改bin/config/admin_proxy.xml失败，请流水线组检查原因")


def modify_config_reduce_log():
    """
    修改配置
    """
    try:
        branch = env_mgr.get_branch()
        server_common_config = f"/data/workspace/x51/{branch}/exe/bin/config/globals.xml"
        # globals.xml
        content = []
        with open(server_common_config, "r", encoding='gbk') as f:
            content = f.readlines()
        new_content = []
        for line in content:
            if "<LogLevel" in line and "category=" in line:
                line = line.replace("priority=\"debug\"","priority=\"warning\"")
            new_content.append(line.strip())
        with open(server_common_config, "w", encoding="gbk") as f:
            f.write("\n".join(new_content))
    except Exception as e:
        log.info("修改配置globals.xml失败: " + str(e))
    
            

@advance.stage("下载并解压linux_bin")
def download_decompress_linux_bin(**kwargs):
    """以svn号为准，找最新的包"""
    branch = env_mgr.get_branch()
    # changelist = env_mgr.get_changelist()
    svn_version = env_mgr.get_svn_version()
    linux_bin_mgr = LinuxBinMgr(branch=branch)
    log.info(f"svn_version: {svn_version}")
    # if not changelist or not svn_version.isdigit():
    #     svn_version, changelist = linux_bin_mgr.find_ftp_linux_bin_by_svn(svn_version)
    svn_version, changelist = linux_bin_mgr.find_ftp_linux_bin_by_svn(svn_version)
    log.info(f"svn_version: {svn_version} changelist: {changelist}")
    linux_bin_version = f"svn_{svn_version}_p4_{changelist}" if svn_version and changelist else None
    if linux_bin_version is None:
        hint = f"<font color=red>svn_version: {svn_version}可能太老，制品库上已无该制品。请填写较新版本的svn_version。</font>"
        env_mgr.set_wechat_hint(hint)
        exit(-1)

    local_bin_version = linux_bin_mgr.read_local_bin_version()
    if local_bin_version == linux_bin_version:
        log.info(f"local_bin_version: {local_bin_version} doesn't need to re download")
        return
    else:
        # linux_bin_mgr.download_linux_bin(version=linux_bin_version)
        linux_bin_mgr.download_linux_bin_from_ftp(svn_ver=svn_version, p4_changelist=changelist)

    linux_bin_mgr.decompress_linux_bin()
    linux_bin_mgr.write_local_bin_version(version=linux_bin_version)


def get_svn_branch(p4_branch):
    if p4_branch == "trunc":
        return p4_branch
    # TODO: 兼容主支
    result = re.findall(rf"branch_(\d+)/QQX5_Mainland_(.+)", p4_branch)
    if result:
        year, branch_name = result[0]
        if re.match("^.+_\d\d$", branch_name):
            branch_name = "_".join(branch_name.split("_")[:-1])

        return f"branches{year}/{branch_name}"

    raise PyframeException(f"未能正确解析P4 branch {p4_branch} 为SVN分支")


@advance.stage("下载svn脚本")
def download_svn_script(**kwargs):
    branch = env_mgr.get_branch()

    path_mgr.mkdir(f"/data/workspace/x51/{branch}/src/star")
    path_mgr.mkdir(f"/data/workspace/x51/{branch}/src/video")
    try:
        username = config.SVN_CONFIG["username"]
        password = config.SVN_CONFIG["password"]
    except Exception as e:
        raise PyframeException(f"请配置正确的svn账号密码: {e}")
    svn_branch = get_svn_branch(branch)

    ret = cmd.run_shell(
        cmds=[
            f"rm -r -f /data/workspace/x51/{branch}/src/star",
            # TODO 个人账号密码
            f"svn checkout http://*************/svn/starx52/{svn_branch}/ -r head --depth files ./src/star --username {username} --password {password}",
        ],
        workdir=f"/data/workspace/x51/{branch}",
    )
    if ret[0] != 0:
        raise PyframeException("svn checkout star失败，请流水线组检查原因")

    if svn_branch == "trunc":
        video_svn_branch = "platform"
    else:
        video_svn_branch = svn_branch
    ret = cmd.run_shell(
        cmds=[
            f"rm -r -f /data/workspace/x51/{branch}/src/video",
            # TODO 个人账号密码
            f"svn checkout http://*************/svn/starx52/video_{video_svn_branch}/ -r head --depth files ./src/video --username {username} --password {password}",
        ],
        workdir=f"/data/workspace/x51/{branch}",
    )
    if ret[0] != 0:
        raise PyframeException("svn checkout video失败，请流水线组检查原因")


@advance.stage("拷贝测试配置")
def copy_test_config(**kwargs):
    branch = env_mgr.get_branch()
    project_path = f"/data/workspace/x51/{branch}"
    exe_test_config = f"{project_path}/exe/test_config"
    if os.path.exists(exe_test_config):
        path_mgr.rm(exe_test_config)


@advance.stage("启动服务器")
def start_server(**kwargs):
    start_type = env_mgr.get_start_type()
    branch = env_mgr.get_branch()
    if start_type == "start_tlinux":
        sh = "sh start_tlinux.sh"
        workdir = f"/data/workspace/x51/{branch}/src/star"
    elif start_type == "start_video":
        sh = "sh start_video.sh"
        workdir = f"/data/workspace/x51/{branch}/src/video"
    elif start_type == "manual":
        log.info("手动启动服务器, 请自行启动")
        return
    else:
        raise PyframeException(f"不支持的启动类型: {start_type}")

    ret = cmd.run_shell(cmds=[sh], workdir=workdir, dot_kill_me=True)
    if ret[0] != 0:
        raise PyframeException(f"{sh}失败，请检查原因")


def on_success(**kwargs):
    wechat.send_unicast_post_success(user_list=deploy_mgr.get_user_list, content=deploy_mgr.get_msg)
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(user_list=deploy_mgr.get_user_list, content=deploy_mgr.get_msg)
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(user_list=deploy_mgr.get_user_list, content=deploy_mgr.get_msg)
    advance.insert_pipeline_history_on_canceled()
