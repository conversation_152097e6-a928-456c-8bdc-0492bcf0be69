# coding=utf-8
from frame.advance.advance import advance
from frame.wechat.wechat import wechat
from project.x51.test.pack_pc.mgr.pack_mgr import pack_mgr


@advance.stage("获取待加壳")
def get_shell_files():
    pack_mgr.get_shell_files()


@advance.stage("上传cos")
def upload_cos():
    pack_mgr.upload_cos()


@advance.stage("获取加壳文件")
def get_shelled_files():
    pack_mgr.get_shelled_files()


@advance.stage("解压压缩包")
def unzip_shelled_files():
    pack_mgr.unzip_shelled_files()


@advance.stage("上传P4")
def upload_p4():
    pack_mgr.upload_p4()


def on_success():
    wechat.send_unicast_post_success(content=pack_mgr.pipeline_msg)
    advance.insert_pipeline_history_on_success()
    pack_mgr.callback_release_on_success()


def on_failure():
    # wechat.send_unicast_post_failure(rescue=c.rescue, content=pack_mgr.pipeline_msg)
    advance.insert_pipeline_history_on_failure()
    pack_mgr.callback_release_on_failure()
    # 流水线失败群
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=21472abe-94a1-45e5-a8e3-4b64b8f9dc02"
    wechat.send_multicast_post_failure(
        webhook=webhook,
        content=pack_mgr.pipeline_msg,
        mentioned_list=["<EMAIL>"],
        rescue=False,
    )


def on_canceled():
    # wechat.send_unicast_post_canceled(content=pack_mgr.pipeline_msg)
    advance.insert_pipeline_history_on_canceled()
    pack_mgr.callback_release_on_aborted()
    # 流水线失败群
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=21472abe-94a1-45e5-a8e3-4b64b8f9dc02"
    wechat.send_multicast_post_failure(
        webhook=webhook,
        content=pack_mgr.pipeline_msg,
        mentioned_list=["<EMAIL>"],
        rescue=False,
    )
