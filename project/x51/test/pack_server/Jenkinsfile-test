node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "pack_server"
            // label "tlinux2.2_172.17.104.134"
        }
    }
    parameters {
        string(name: 'year', defaultValue: '2023', description: '年份')
        string(name: 'month',defaultValue: '10', description:'月份')
        string(name: 'version_stage',defaultValue: 'final', description:'版本阶段')
        string(name: 'pack_type',defaultValue: 'shell', description: 'shell:加壳  rg_shell:热更带加壳  rg_no_shell热更不带加壳')
        string(name: 'linux_bin_ab_path', defaultValue: '/data/workspace/tester/branches2023/6.6.4/exe/bin', description: 'server bin 目录绝对路径,参考默认值格式')
        string(name: 'linux_bin_build_agent', defaultValue: '*************', description: 'server linux 构建机的IP,参考默认值格式')
        string(name: 'ver_main', defaultValue: '6.6.4', description: '主版本号（同当月P4分支版本号一致）')
        string(name: 'ver_sub', defaultValue: '6.6.5', description: '子版本号（当前版本发布的版本号,PTR,RC四位版本号,FINAL,FINALX三位版本号）')
        string(name: 'game_level_check_path', defaultValue: '//H3D_X51_res/QQX5_Mainland/branch_2023/QQX5_Mainland_6.6.1_09/exe/resources/level/game_level/', description: '上月分支game_level的P4路径')
        string(name: 'p4_ver_num', defaultValue: '1415421', description: '封板资源号')
        string(name: 'anchor_ver', defaultValue: '2.3.2', description: '主播端版本号（finalx不发主播端时用默认值即可）')
        booleanParam(name:'is_have_check',defaultValue: true, description:'是否存在check工具（FINAL以及FINALX如果有客户端发布则需要勾选）')
        string(name: 'client_and_anchor_rule', defaultValue: 'client,anchor', description:'是否剔除客户端或主播端更新,client,anchor  client  anchor')
        hidden defaultValue: '', description: '回调地址', name: 'callback_url'
        hidden defaultValue: '', description: '最后的执行人', name: 'last_operator'
        hidden defaultValue: 'dev', description: '运行环境，dev or prod', name: 'build_env'
    }
    options {
        disableConcurrentBuilds()
    }

    environment {
        // PYTHON = "/root/.pyenv/versions/pack-server/bin/python"
        PYTHON = 'conda run -n pyf368 python'
    }

    stages {
        stage("创建虚拟环境") {
            steps {
                script {
                    sh(
                        script: """
                        conda env list | grep pyf368 || conda create -y -n pyf368 python=3.6.8
                        """
                    )
                }
            }
        }
        stage("安装python依赖") {
            steps {
                script {
                    sh(
                        script: """
                            ${PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("准备") {
            steps {
                script {
                    sh "${PYTHON} x51.py pack_server --job=prepare"
                }
            }
        }
        stage("同步server") {
            failFast true
            parallel {
                stage('同步P4') {
                    steps {
                        script {
                            sh "${PYTHON} x51.py pack_server --job=sync_server"
                        }
                    }
                }
                stage('同步so') {
                    steps {
                        script {
                            sh "${PYTHON} x51.py pack_server --job=sync_so"
                        }
                    }
                }
            }
        }
        stage("打包") {
            failFast true
            parallel {
                stage("打包server") {
                    steps {
                        script {
                            sh "${PYTHON} x51.py pack_server --job=zip_server"
                        }
                    }
                }
                stage("打包其他") {
                    steps {
                        script {
                            sh "${PYTHON} x51.py pack_server --job=pack_other"
                        }
                    }
                }
            }
        }
        // stage("回调服务") {
        //     steps {
        //         script {
        //             sh "${PYTHON} x51.py pack_server --job=callback_server"
        //         }
        //     }
        // }
    }
    // post {
    //     success {
    //         script {
    //             sh "${PYTHON} x51.py pack_server --job=on_success"
    //             // cleanWs disableDeferredWipeout: true, deleteDirs: true
    //         }
    //     }
    //     failure {
    //         script {
    //             sh "${PYTHON} x51.py pack_server --job=on_failure"
    //             // cleanWs disableDeferredWipeout: true, deleteDirs: true
    //         }
    //     }
    //     aborted {
    //         script {
    //             sh "${PYTHON} x51.py pack_server --job=on_canceled"
    //             // cleanWs disableDeferredWipeout: true, deleteDirs: true
    //         }
    //     }
    // }
}
