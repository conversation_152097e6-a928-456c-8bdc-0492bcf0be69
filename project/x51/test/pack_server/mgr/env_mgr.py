from frame import env
from frame.common.common import common


class EnvMgr:
    @staticmethod
    def get_year():
        return env.get("year")

    @staticmethod
    def get_month():
        return env.get("month")

    @staticmethod
    def get_version_stage():
        return env.get("version_stage").lower()

    @staticmethod
    def get_pack_type():
        return env.get("pack_type")

    @staticmethod
    def get_linux_bin_ab_path():
        return env.get("linux_bin_ab_path").rstrip("/")

    @staticmethod
    def get_linux_bin_build_agent():
        return env.get("linux_bin_build_agent")

    @staticmethod
    def get_ver_main():
        return env.get("ver_main", "")

    @staticmethod
    def get_ver_sub():
        return env.get("ver_sub")

    @staticmethod
    def get_game_level_check_path():
        return env.get("game_level_check_path")

    @staticmethod
    def get_p4_ver_num():
        return env.get("p4_ver_num")

    @staticmethod
    def get_anchor_ver():
        return env.get("anchor_ver", "")

    @staticmethod
    def get_is_have_check() -> bool:
        return common.str2bool(env.get("is_have_check"))

    @staticmethod
    def get_client_and_anchor_rule():
        return [c.strip() for c in env.get("client_and_anchor_rule").split(",")]

    @staticmethod
    def get_workspace():
        return env.pipeline.workspace()

    @staticmethod
    def get_build_env():
        return env.get("build_env")

    @staticmethod
    def get_callback_url():
        return env.get("callback_url")

    @staticmethod
    def get_last_operator():
        return env.get("last_operator")

    def get_p4_branch(self):
        if self.get_version_stage().lower() == "ptr":
            return "//H3D_X51_res/QQX5_Mainland/trunc"
        else:
            return f"//H3D_X51_res/QQX5_Mainland/branch_{self.get_year()}/QQX5_Mainland_{self.get_ver_main()}_{self.get_month()}"

    def get_shell_name(self):
        return f"{self.get_ver_sub()}_{self.get_version_stage()}_shell"

    def get_server_dir(self):
        return f"QQDance_server_{self.get_shell_name()}"

    def get_ktv_so_path(self):
        if self.get_version_stage() == "ptr":
            return "//H3D_X51_res/QQX5_Mainland/trunc/exe/server"
        else:
            return f"//H3D_X51_res/QQX5_Mainland/branch_{self.get_year()}/QQX5_Mainland_{self.get_ver_main()}_{self.get_month()}/exe/server"

    def get_sql_so_path(self):
        if self.get_version_stage() == "ptr":
            return "//H3D_X51_res/QQX5_Mainland/trunc/exe/server/lib64/mysql_for_db64/libmysqlclient_x51.so.15"
        else:
            return f"//H3D_X51_res/QQX5_Mainland/branch_{self.get_year()}/QQX5_Mainland_{self.get_ver_main()}_{self.get_month()}/exe/server/lib64/mysql_for_db64/libmysqlclient_x51.so.15"

    def get_uic_so_path(self):
        if self.get_version_stage() == "ptr":
            return "//H3D_X51_res/QQX5_Mainland/trunc/exe/server/lib64/libtss_sdk.so"
        else:
            return f"//H3D_X51_res/QQX5_Mainland/branch_{self.get_year()}/QQX5_Mainland_{self.get_ver_main()}_{self.get_month()}/exe/server/lib64/libtss_sdk.so"


env_mgr = EnvMgr()
