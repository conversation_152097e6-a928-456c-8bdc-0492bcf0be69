# coding=utf-8

from pathlib import Path

from frame import P4Client, common, path_mgr, log
from project.x51 import config
from project.x51.test.pack_server.mgr.config import config as c
from project.x51.test.pack_server.mgr.env_mgr import env_mgr


class P4Mgr:
    year = env_mgr.get_year()
    # month = env_mgr.get_month().replace("0", "")
    month = env_mgr.get_month().lstrip("0")
    stage = env_mgr.get_version_stage()
    latest_version = env_mgr.get_ver_sub()
    root = c.p4_root
    p4_num = env_mgr.get_p4_ver_num()

    @classmethod
    def __init_p4(cls, name: str) -> P4Client:
        p4 = P4Client(
            host=config.P4_2003_TEST_CONFIG.get("host"),
            username=config.P4_2003_TEST_CONFIG.get("username"),
            password=config.P4_2003_TEST_CONFIG.get("password"),
            client=f"x51_test_pack_server_{common.get_host_ip()}_{name}",
        )
        if not Path(cls.root).exists:
            Path(cls.root).mkdir(parents=True)
        p4.set_root(cls.root)
        p4.set_options(clobber=True, allwrite=False, modtime=False, rmdir=False)
        p4.set_line_end("WIN")
        p4.set_encoding("GBK")
        return p4

    @classmethod
    def sync_server(cls):
        p4 = cls.__init_p4("server")

        views = [
            f"{env_mgr.get_p4_branch()}/exe/resources/level/... //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/resources/level/...",
            f"{env_mgr.get_p4_branch()}/exe/resources/level/game_level/... //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/resources/level/game_level/...",
            f"{env_mgr.get_p4_branch()}/exe/resources/strings/... //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/resources/strings/...",
        ]
        p4.set_view(views)
        p4.sync_all(force=True, changelist=cls.p4_num)
        cls.__compare_game_level(p4)

    @classmethod
    def __compare_game_level(cls, p4: P4Client):
        last_month_game_level = env_mgr.get_game_level_check_path()
        current_game_level = f"{env_mgr.get_p4_branch()}/exe/resources/level/game_level/...@{cls.p4_num}"
        last_month_game_level = f"{last_month_game_level}...#head"

        # 只保留有修改的文件和原有文件夹
        current_game_level_path = Path(p4.run("where", current_game_level.split("@")[0])[0].get("path").rstrip("..."))
        log.info(current_game_level_path)
        for file in current_game_level_path.rglob("*"):
            if not file.is_file():
                continue
            file.unlink()
        r = p4.diff2(current_game_level, last_month_game_level, q=True)
        files = [i.get("depotFile") for i in r if i.get("status") != "right only"]
        views = [
            f"{env_mgr.get_p4_branch()}/exe/resources/level/game_level/... //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/resources/level/game_level/..."
        ]
        p4.set_view(views)
        p4.sync_list(files, force=True, changelist=cls.p4_num)

    @classmethod
    def sync_so(cls):
        p4 = cls.__init_p4("so")
        views = [
            f"{env_mgr.get_uic_so_path()} //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/bin/lib64/libtss_sdk.so",
            f"{env_mgr.get_sql_so_path()} //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/bin/lib64/mysql_for_db64/libmysqlclient_x51.so.15",
            f"{env_mgr.get_ktv_so_path()}/libsvc_ktv_server.so //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/bin/libsvc_ktv_server.so",
            f"{env_mgr.get_ktv_so_path()}/libsvc_ktv_center.so //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/bin/libsvc_ktv_center.so",
            f"{env_mgr.get_ktv_so_path()}/libktv_service64.so //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/bin/libktv_service64.so",
            f"{env_mgr.get_ktv_so_path()}/libktv_service.so //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/bin/libktv_service.so",
            f"{env_mgr.get_ktv_so_path()}/ktv_server //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/bin/ktv_server",
            f"{env_mgr.get_ktv_so_path()}/ktv_center //{p4.client}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}/bin/ktv_center",
        ]
        p4.set_view(views)
        p4.sync_all(force=True, changelist=cls.p4_num)

    @classmethod
    def sync_db(cls):
        p4 = cls.__init_p4("db")

        views = [
            f"//H3D_X51_res/X51_SourceBase/版本/版本文档/{cls.year}/{cls.year}年{cls.month}月版本/db/... //{p4.client}/db/db/...",
        ]
        p4.set_view(views)
        p4.sync_all(force=True)

        need_delete = [f"{cls.root}/db/db/globals", f"{cls.root}/db/db/globals.xml"]
        for i in need_delete:
            if path_mgr.exists(i):
                path_mgr.rm(i)

    @classmethod
    def sync_doc(cls):
        p4 = cls.__init_p4("doc")

        views = [
            f"//H3D_X51_res/X51_SourceBase/版本/版本文档/{cls.year}/{cls.year}年{cls.month}月版本/doc/... //{p4.client}/doc/doc/...",
            f"//H3D_X51_res/X51_SourceBase/doc/测试部专用/版本功能文档/{cls.year}年/{cls.year}年{cls.month}月版本功能文档/01【执行案&QA文档】/... //{p4.client}/doc/doc/01【执行案&QA文档】/...",
            f"//H3D_X51_res/X51_SourceBase/doc/测试部专用/版本功能文档/{cls.year}年/{cls.year}年{cls.month}月版本功能文档/02【需求分析&测试用例&QA文档汇总表】/... //{p4.client}/doc/doc/02【需求分析&测试用例&QA文档汇总表】/...",
        ]
        if cls.stage != "ptr":
            views.extend(
                [
                    f"//H3D_X51_res/X51_SourceBase/doc/常规配置需求/游戏侧/版本配置/{cls.year}年{cls.month}月版本/... //{p4.client}/doc/doc/常规配置需求/游戏侧/版本配置/{cls.year}年{cls.month}月版本/...",
                    f"//H3D_X51_res/X51_SourceBase/doc/常规配置需求/游戏侧/称号数据更新/{cls.year}年/{cls.month}月/... //{p4.client}/doc/doc/常规配置需求/游戏侧/称号数据更新/{cls.year}年/{cls.month}月/...",
                    f"//H3D_X51_res/X51_SourceBase/doc/常规配置需求/梦工厂/版本配置/{cls.year}年{cls.month}月版本/... //{p4.client}/doc/doc/常规配置需求/梦工厂/版本配置/{cls.year}年{cls.month}月版本/...",
                    f"//H3D_X51_res/X51_SourceBase/版本/协议文档/{cls.year}年{cls.month}月版本/... //{p4.client}/doc/doc/协议文档/{cls.year}年{cls.month}月版本/...",
                ]
            )
        p4.set_view(views)
        p4.sync_all(force=True)

    @classmethod
    def sync_test_shop(cls):
        p4 = cls.__init_p4("test_shop")

        views = [
            f"//H3D_X51_res/X51_SourceBase/测试用配置/server/... //{p4.client}/test_shop/test_shop/bin/...",
        ]
        p4.set_view(views)
        need_files = [
            "//H3D_X51_res/X51_SourceBase/测试用配置/server/config/weekly/Shop.xml",
            "//H3D_X51_res/X51_SourceBase/测试用配置/server/config/weekly/FitmentShop.xml",
        ]
        p4.sync_list(need_files, force=True)

    @classmethod
    def sync_tcls_config(cls):
        p4 = cls.__init_p4("tcls_config")

        if cls.stage in ("ptr", "rc"):
            file = f"release_note_{cls.latest_version}.html"
        else:
            file = f"release_note_{cls.latest_version}.0.html"

        views = [
            f"//H3D_X51_res/X51_SourceBase/版本/版本文档/{cls.year}/tcls_config/{file} //{p4.client}/tcls_config/tcls_config/{file}",
        ]
        p4.set_view(views)
        p4.sync_all(force=True)
