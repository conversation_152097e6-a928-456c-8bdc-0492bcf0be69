import requests

from frame import (
    Path,
    PyframeException,
    SMBClient,
    cmd,
    common,
    env,
    file_mgr,
    path_mgr,
)
from frame.log.log import log
from project.x51.test.pack_server.mgr.config import config
from project.x51.test.pack_server.mgr.env_mgr import env_mgr
from project.x51.test.pack_server.mgr.p4_mgr import P4Mgr


class PackMgr:
    @property
    def __server_local_path(self):
        path = f"{config.p4_root}/{env_mgr.get_server_dir()}/{env_mgr.get_server_dir()}"
        if not Path(path).exists():
            Path(path).mkdir(parents=True, exist_ok=True)
        return path

    def sync_server(self):
        if env_mgr.get_pack_type() == "shell":
            P4Mgr.sync_server()
        else:
            config_path = Path(self.__server_local_path).joinpath("bin").joinpath("config")
            if not config_path.exists():
                config_path.mkdir(parents=True, exist_ok=True)
            if "client" in env_mgr.get_client_and_anchor_rule():
                with SMBClient(username=config.smb_user, password=config.smb_password) as c:
                    c.download(
                        config.smb_root,
                        f"{config.smb_remote_prefix}/{env_mgr.get_shell_name()}/md5_verify.xml",
                        config_path.joinpath("md5_verify.xml"),
                    )

    def sync_so(self):
        if env_mgr.get_pack_type() == "shell":
            command = f"scp -r root@{env_mgr.get_linux_bin_build_agent()}:{env_mgr.get_linux_bin_ab_path()} {self.__server_local_path}/"
            log.info(command)
            code, output = cmd.run_shell([command])
            if code != 0:
                raise PyframeException(f"获取so文件失败：{output}")

            bin_path = Path(self.__server_local_path).joinpath("bin")

            # with open(bin_path.joinpath("md5.txt"), "a+") as f:
            for p in Path(self.__server_local_path).joinpath("bin").iterdir():
                if p.is_dir():
                    path_mgr.rm(p)
                    # else:
                    #     f.write(self.gen_md5(p) + os.linesep)

            cmd.run_shell(["md5sum *> md5.txt"], workdir=bin_path)

            with SMBClient(username=config.smb_user, password=config.smb_password) as c:
                c.download_directory(
                    config.smb_root,
                    f"{config.smb_remote_prefix}/{env_mgr.get_shell_name()}/server/bin/config",
                    bin_path.joinpath("config"),
                )

                if "client" in env_mgr.get_client_and_anchor_rule():
                    c.download(
                        config.smb_root,
                        f"{config.smb_remote_prefix}/{env_mgr.get_shell_name()}/md5_verify.xml",
                        bin_path.joinpath("config").joinpath("md5_verify.xml"),
                    )

            P4Mgr.sync_so()

    def zip_server(self):
        self.__tar_zip("server")

    @staticmethod
    def gen_md5(file: Path) -> str:
        return common.compute_md5(str(file.absolute())) + "  " + file.name

    def pack_other(self):
        if env_mgr.get_pack_type() == "shell":
            self.__pack_db()
        self.__pack_doc()
        self.__pack_test_shop()
        self.__pack_tcls_config()
        self.__get_anchor_or_client()

        if env_mgr.get_pack_type() != "rg_no_shell":
            self.__get_shell()
        if env_mgr.get_is_have_check():
            self.__get_check()

    def __pack_db(self):
        log.info("start pack db")
        P4Mgr.sync_db()
        self.__tar_zip("db")

    def __pack_doc(self):
        log.info("start pack doc")
        P4Mgr.sync_doc()
        self.__tar_zip("doc")

    def __pack_test_shop(self):
        log.info("start pack test_shop")
        P4Mgr.sync_test_shop()
        self.__tar_zip("test_shop")

    def __pack_tcls_config(self):
        log.info("start pack tcls_config")
        P4Mgr.sync_tcls_config()
        self.__tar_zip("tcls_config")

    def __get_check(self):
        log.info("start get check")
        tool_name = f"bin_{env_mgr.get_ver_sub()}_{env_mgr.get_version_stage()}_shell.zip"
        xml_name = f"version_check_md5_{env_mgr.get_ver_sub()}.xml"
        local_check_path = Path(config.pkg_path).joinpath("check")
        local_check_path.mkdir(exist_ok=True)
        with SMBClient(username=config.smb_user, password=config.smb_password) as c:
            for file_name in (tool_name, xml_name):
                file = local_check_path.joinpath(file_name)
                md5_file = local_check_path.joinpath(f"{file.stem}.md5")
                c.download(
                    config.smb_root,
                    f"{config.smb_remote_prefix}/{env_mgr.get_shell_name()}/check/{file_name}",
                    file,
                )
                with open(md5_file, "w") as f:
                    f.write(self.gen_md5(file))
                file_mgr.unix2dos(md5_file)

    def __get_anchor_or_client(self):
        log.info("start get client and anchor")
        with SMBClient(username=config.smb_user, password=config.smb_password) as c:
            if "anchor" in env_mgr.get_client_and_anchor_rule():
                anchor_path = Path(config.pkg_path).joinpath("anchor")
                anchor_path.mkdir(exist_ok=True)
                anchor_exe = f"QQx5_anchor_{env_mgr.get_anchor_ver()}_shell_setup.exe"
                pkg_anchor_exe = anchor_path.joinpath(anchor_exe)
                c.download(
                    config.smb_root,
                    f"{config.smb_remote_prefix}/{env_mgr.get_shell_name()}/{anchor_exe}",
                    pkg_anchor_exe,
                )

                anchor_md5 = anchor_path.joinpath(f"{Path(anchor_exe).stem}.md5")
                with open(anchor_md5, "w") as f:
                    f.write(self.gen_md5(pkg_anchor_exe))

                file_mgr.unix2dos(str(anchor_md5))

            if "client" in env_mgr.get_client_and_anchor_rule():
                client_path = Path(config.pkg_path).joinpath("client")
                client_path.mkdir(exist_ok=True)
                if env_mgr.get_version_stage() in ("ptr", "rc", "final"):
                    client_exe = f"patch_{env_mgr.get_ver_sub()}_shell_setup.exe"
                else:
                    client_exe = f"patch_{env_mgr.get_ver_sub()}_{env_mgr.get_version_stage()}_shell_setup.exe"

                pkg_client_exe = client_path.joinpath(client_exe)
                c.download(
                    config.smb_root,
                    f"{config.smb_remote_prefix}/{env_mgr.get_shell_name()}/{client_exe}",
                    pkg_client_exe,
                )

                client_md5 = client_path.joinpath(f"{Path(client_exe).stem}.md5")
                with open(client_md5, "w") as f:
                    f.write(self.gen_md5(pkg_client_exe))

                file_mgr.unix2dos(str(client_md5))

    @staticmethod
    def __get_shell():
        log.info("start get shell")
        with SMBClient(username=config.smb_user, password=config.smb_password) as c:
            c.download_directory(
                config.smb_root,
                f"{config.smb_remote_prefix}/{env_mgr.get_shell_name()}/shell",
                f"{config.pkg_path}/shell",
            )

    def __tar_zip(self, name: str):
        if name == "server":
            temp_path = f"{config.p4_root}/{env_mgr.get_server_dir()}"
            name = env_mgr.get_server_dir()
            pkg_path = Path(config.pkg_path).joinpath("server")
        else:
            temp_path = Path(config.p4_root).joinpath(name)
            pkg_path = Path(config.pkg_path).joinpath(name)
        if not pkg_path.exists():
            pkg_path.mkdir(parents=True, exist_ok=True)

        name_zip = pkg_path.joinpath(f"{name}.zip")
        name_md5 = pkg_path.joinpath(f"{name}.md5")
        command = f"zip -r {name_zip} {name}"
        log.info(command)
        code, output = cmd.run_shell([command], encoding="gbk", workdir=temp_path)
        if code != 0:
            raise PyframeException(f"打包{name}失败：{output}")
        with open(name_md5, "w") as f:
            f.write(self.gen_md5(name_zip))

        file_mgr.unix2dos(str(name_md5))

    @staticmethod
    def __send_callback(status: str, detail: str):
        headers = {"Content-Type": "application/json"}
        data = {
            "status": status,
            "detail": detail,
            "last_operator": env_mgr.get_last_operator(),
        }
        requests.post(env_mgr.get_callback_url(), json=data, headers=headers)

    def send_success_callback(self):
        download_addr = rf"\\{common.get_host_ip()}\download\pkg_release"
        version = f"{env_mgr.get_year()}年{env_mgr.get_month()}月{env_mgr.get_version_stage()}"
        detail = f"{version}版本服务器打包成功 \n下载目录 {download_addr}"
        self.__send_callback("success", detail=detail)

    def send_failure_callback(self):
        fail_msg = env.get("PYFRAME_EXCEPTION_MESSAGE")
        detail = f"{env_mgr.get_year()}年{env_mgr.get_month()}月{env_mgr.get_version_stage()}版本服务器打包失败，失败原因：{fail_msg}\n请相关人员检查错误原因，解决后再次执行流水线"
        self.__send_callback("failure", detail=detail)

    def send_abort_callback(self):
        detail = f"{env_mgr.get_year()}年{env_mgr.get_month()}月{env_mgr.get_version_stage()}版本服务器打包被取消"
        self.__send_callback("aborted", detail=detail)

    @staticmethod
    def prepare():
        path_mgr.rm(config.pkg_path)
        path_mgr.rm(config.p4_root)

    @staticmethod
    def gen_detail() -> str:
        detail = ""
        detail += f"**发版阶段:** {env_mgr.get_year()}年{env_mgr.get_month()}月{env_mgr.get_version_stage()}\n"
        detail += f"**主版本号:** {env_mgr.get_ver_main()}\n"
        detail += f"**子版本号:** {env_mgr.get_ver_sub()}\n"
        detail += f"**打包类型:** {env_mgr.get_pack_type()}\n"
        detail += f"**封板资源号:** {env_mgr.get_p4_ver_num()}\n"
        detail += f"**打包内容:** {env_mgr.get_client_and_anchor_rule()}\n"
        if "anchor" in env_mgr.get_client_and_anchor_rule():
            detail += f"**主播端版本号:** {env_mgr.get_client_and_anchor_rule()}\n"

        return detail


pack_mgr = PackMgr()
