# coding=utf-8

from frame import *


class LinuxBinMgr:
    def __init__(self, branch: str, changelist: str):
        self.__branch = branch
        self.__changelist = changelist

    def download_linux_bin(self):
        ftp_mgr = FtpMgr("192.168.13.215", port=21, username="administrator", password="Admin123")
        remote_dir = f"/x52/server/{self.__branch}"

        files = ftp_mgr.dirs(remote_dir)
        files.sort(reverse=True)
        log.info("files: {}".format(files))
        dst_dir = "/data/home/<USER>/exe/bin11/"

        log.info("start search changelist {} in ftp {}".format(self.__changelist, remote_dir))
        for file in files:
            arr = file.split("_")
            log.info(arr)
            if len(arr) != 4:
                continue
            # 根据p4号寻找
            if int(arr[1]) <= int(self.__changelist):
                log.info("find file match: {}".format(file))
                src_path = os.path.join(remote_dir, file)
                dst_path = dst_dir + file
                # log.info("src: {}, dst: {}".format(src_path, dst_path))
                log.info(f"start download file [{dst_path}] from ftp [{src_path}]")
                ftp_mgr.download_file(src=src_path, dst=dst_path)
                log.info("download success")
                env.set({"server_tar": file})
                break

    def decompress_linux_bin(self):
        server_tar_path = os.path.join("/data/home/<USER>/exe/bin11/", env.get("server_tar"))
        tar.decompress(src=server_tar_path, dst="/data/home/<USER>/exe/bin11/")
        path_mgr.rm(path=server_tar_path)


if __name__ == "__main__":
    m = LinuxBinMgr(branch="2.6.4", changelist="1421445")
    m.download_linux_bin()
