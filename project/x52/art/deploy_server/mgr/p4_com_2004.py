# coding=utf-8
from frame import *
from project.x52 import config


class P4COM2004:
    def __init__(self, branch: str):
        self._branch = branch
        self._client = f"x52_art_deploy_server_{self._branch}_{common.get_host_ip()}"
        self.p4 = P4Client(
            host=config.P4_2004_CONFIG.get("host"),
            username=config.P4_2004_CONFIG.get("username"),
            password=config.P4_2004_CONFIG.get("password"),
            client=self._client,
        )
        client = self._client
        if branch == "Vacation":
            views = [
                f"//H3D_X52_res/QQX5-2_Exe/Vacation/resources/config/... //{client}/exe/resources/config/...",
                f"//H3D_X52_res/QQX5-2_Exe/Vacation/resources/level/... //{client}/exe/resources/level/...",
                f"//H3D_X52_res/QQX5-2_Exe/Vacation/bin/%%1.sh //{client}/exe/bin11/%%1.sh",
                f"//H3D_X52_res/X52_SourceBase/test_lucky_consumeqb_score/lucky_consumeqb_score.xml //{client}/exe/resources/config/server/bonus/lucky_consumeqb_score.xml",
                f"//H3D_X52_res/X52_SourceBase/test_lucky_consumeqb_score/custom_suit_dynamic_config.xml //{self._client}/exe/resources/config/server/h3d/item/custom_suit_dynamic_config.xml",
            ]
        else:
            views = [
                f"//H3D_X52_res/QQX5-2_Exe/Branchs/{branch}/resources/config/... //{client}/exe/resources/config/...",
                f"//H3D_X52_res/QQX5-2_Exe/Branchs/{branch}/resources/level/... //{client}/exe/resources/level/...",
                f"//H3D_X52_res/QQX5-2_Exe/Branchs/{branch}/bin/%%1.sh //{client}/exe/bin11/%%1.sh",
                f"//H3D_X52_res/X52_SourceBase/test_lucky_consumeqb_score/lucky_consumeqb_score.xml //{client}/exe/resources/config/server/bonus/lucky_consumeqb_score.xml",
                f"//H3D_X52_res/X52_SourceBase/test_lucky_consumeqb_score/custom_suit_dynamic_config.xml //{self._client}/exe/resources/config/server/h3d/item/custom_suit_dynamic_config.xml",
                "{src} {dest}".format(
                    src='//H3D_X52_res/X52_SourceBase/test_lucky_consumeqb_score/reward_display.xml',
                    dest=f'//{client}/exe/resources/config/server/bonus/lucky_score/reward_display.xml'
                )
            ]
        self.p4.set_view(views=views)
        root = "/data/home/<USER>/"
        self.p4.set_root(root)
        self.p4.remove_host()

    def get_latest_changes(self):
        """
        获取资源分支的最新changelist
        """
        if self._branch == "Vacation":
            ret = self.p4.get_latest_changes("//H3D_X52_res/QQX5-2_Exe/Vacation/...")
        else:
            ret = self.p4.get_latest_changes(f"//H3D_X52_res/QQX5-2_Exe/Branchs/{self._branch}/...")
        try:
            changelist = ret.get("change")
        except Exception as e:
            log.error(e)
            changelist = "head"
        return changelist

    def sync_all(self, changelist: str, force: bool):
        """
        拉取p4
        """
        self.p4.sync_all(changelist=changelist, force=force)
