from frame import P4Client, common
from project.x52 import config


class P42004:
    def __init__(self, branch: str):
        self.__branch = branch
        self.__client = "{}_{}".format(common.get_host_ip(), self.__branch)
        self.p4 = P4Client(
            host=config.P4_2004_CONFIG.get("host"),
            username=config.P4_2004_CONFIG.get("username"),
            password=config.P4_2004_CONFIG.get("password"),
            client=self.__client,
        )
        views = [
            f"//H3D_X52_res/X52_SourceBase/test_shop/shop.xml //{self.__client}/exe/resources/config/server/bonus/shop.xml",
        ]
        self.p4.set_view(views=views)
        # root = "/data/workspace/{}".format(self.__branch)
        root = "/data/home/<USER>/"
        self.p4.set_root(root)
        self.p4.set_options(clobber=True, rmdir=True)

    def sync_all(self, changelist: str = "head", force: bool = True):
        self.p4.sync_all(changelist=changelist, force=force)
