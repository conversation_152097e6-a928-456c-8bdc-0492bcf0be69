# coding=utf-8
import pathlib
import re
import xml.etree.ElementTree as ET

from frame import common, log, os


class ConfigMgr:
    def __init__(self):
        self.base_path = "/data/home/<USER>/exe"

    def modify_config(
        self,
        server_ip: str,
        cross_server_ip: str,
        redis_ip: str,
        zone_id: str,
        world_id: str,
        mysql_type: str,
    ):
        self._modify_server_globals(server_ip, cross_server_ip, redis_ip, zone_id, world_id)
        self._modify_global_server(cross_server_ip)
        self._modify_mysql_config(mysql_type)
        self._modify_mysql_config_global(mysql_type)
        self._modify_gateway()
        self._modify_common_config()

    def generate_server_test(self):
        server_test_path = os.path.join(self.base_path, "resources/config/server/op/server_test.xml")
        p = pathlib.Path(server_test_path)
        p.write_text(
            data="""
<?xml version="1.0" encoding="UTF-8"?>
<config>
	<use_itc>false</use_itc>
</config>"""
        )

    def get_zone_world_id(self):
        """
        server\op\area_id_map.xml
        """
        log.info("get_zone_world_id()")
        path = os.path.join(self.base_path, "resources/config/server/op/area_id_map.xml")
        tree = ET.parse(path)
        root = tree.getroot()
        map_infos = root.find("map_infos")
        items = map_infos.findall("item")
        zone_world_dict = {}
        for item in items:
            zone_world_dict[item.get("zone_id")] = item.get("area_id")
        return zone_world_dict

    def _modify_server_globals(
        self,
        server_ip: str,
        cross_server_ip: str,
        redis_ip: str,
        zone_id: str,
        world_id: str,
    ):
        """
        修改resources\config\server\server_globals.xml
        """
        log.info("__modify_server_globals()")
        path = os.path.join(self.base_path, "resources/config/server/server_globals.xml")
        tree = ET.parse(path)
        root = tree.getroot()
        # 修改Gateway
        gateway = root.findall("Gateway/Server")
        for server in gateway:
            if server.get("ID") == "1":
                server.set("INNER_IP", server_ip)

        # 修改CrossAreaTransmit
        cross_area_transmit = root.findall("CrossAreaTransmit/Server")
        for server in cross_area_transmit:
            if server.get("ID") == "1":
                server.set("INNER_IP", cross_server_ip)

        # 修改RedisServer
        redis_server = root.findall("RedisServer/Server")
        for server in redis_server:
            if server.get("ID") == "1":
                server.set("ip", redis_ip)
                server.set("ip_d", redis_ip)
                # if common.get_host_ip() in ["*************", "*************"]:
                if common.get_host_ip() in ["*************", "************"]:
                    server.set("port", "12041")
                    log.debug("RedisServer/Server port: 12041")

        # 修改zone_id
        zone_id_node = root.find("zone_id")
        zone_id_node.text = zone_id

        # 修改world_id
        world_id_node = root.find("world_id")
        world_id_node.text = world_id
        tree.write(path)

    def _modify_global_server(self, cross_server_ip: str):
        log.info("__modify_global_server()")
        path = os.path.join(self.base_path, "resources/config/server/op/global_server.xml")
        tree = ET.parse(path)
        root = tree.getroot()
        # 修改Gateway
        gateway = root.find("Gateway")
        servers = gateway.findall("Server")
        for server in servers:
            if server.get("ID") == "201":
                gateway.remove(server)
            else:
                server.set("INNER_IP", cross_server_ip)

        # 修改transmit
        transmit = root.findall("Transmit/Server")
        for server in transmit:
            if server.get("ID") == "1":
                server.set("INNER_IP", cross_server_ip)
        tree.write(path)

    def _modify_mysql_config(self, mysql_type: str):
        log.info("__modify_mysql_config()")
        path = os.path.join(self.base_path, "resources/config/server/op/mysql_config_test.xml")
        tree = ET.parse(path)
        root = tree.getroot()
        # 修改Gateway
        mysql = root.findall("mysql/db_list/db")
        for db in mysql:
            if db.get("dbname") == "x52":
                db.set("dbname", mysql_type)
        path = os.path.join(self.base_path, "resources/config/server/op/mysql_config.xml")
        tree.write(path)

    def _modify_mysql_config_global(self, mysql_type: str):
        log.info("__modify_mysql_config_global()")
        path = os.path.join(self.base_path, "resources/config/server/op/mysql_config_global.xml")
        tree = ET.parse(path)
        root = tree.getroot()
        # 修改Gateway
        mysql = root.findall("mysql/db_list/db")
        for db in mysql:
            if db.get("dbname") == "x52":
                db.set("dbname", mysql_type)
        tree.write(path)

    def _modify_gateway(self):
        log.info("__modify_gateway()")
        path = os.path.join(self.base_path, "resources/config/server/op/gateway.xml")
        tree = ET.parse(path)
        root = tree.getroot()
        # 修改auth_enable
        auth_enable = root.find("auth_enable")
        auth_enable.text = "false"
        # 修改checksum_enable
        checksum_enable = root.find("checksum_enable")
        checksum_enable.text = "false"
        # 修改crypt_enable
        crypt_enable = root.find("crypt_enable")
        crypt_enable.text = "false"
        # 修改start_cloud_enable
        start_cloud_enable = root.find("start_cloud_enable")
        start_cloud_enable.text = "false"
        tree.write(path)

    def _modify_common_config(self):
        log.info("__modify_common_config()")
        path = os.path.join(self.base_path, "resources/config/server/bonus/common_config.xml")
        f = open(path, "r+", encoding="utf-8")
        lines = f.readlines()  # .decode("gb2312")
        f.close()

        f = open(path, "w+", encoding="utf-8")
        for line in lines:
            res = re.sub('desc="物品配置文件校验开关" value="1"', 'desc="物品配置文件校验开关" value="0"', line)
            f.writelines(res)
        f.close()
