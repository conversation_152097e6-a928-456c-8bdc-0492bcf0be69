from frame import env


class EnvMgr:
    def set_version_branch(self, branch: str):
        env.set({"VERSION_BRANCH": branch})

    def get_version_branch(self):
        return env.get("VERSION_BRANCH")

    def set_p4_changelist(self, changelist: str):
        env.set({"P4_CHANGELIST": changelist})

    def get_p4_changelist(self):
        return env.get("P4_CHANGELIST")

    def set_cross_server_ip(self, ip: str):
        env.set({"CROSS_SERVER_IP": ip})

    def get_cross_server_ip(self):
        return env.get("CROSS_SERVER_IP")


class BkEnvMgr:
    def get_branch(self):
        return env.get("BRANCH")

    def get_p4_version(self):
        return env.get("P4_VERSION")

    def get_server_ip(self):
        return env.get("SERVER_IP")

    def get_cross_server_ip(self):
        return env.get("CROSS_SERVER_IP")

    def get_redis_ip(self):
        return env.get("REDIS_IP")

    def get_zone_id(self):
        return env.get("ZONE_ID")

    def get_world_id(self):
        return env.get("WORLD_ID")

    def get_mysql_type(self):
        return env.get("MYSQL_NAME")


env_mgr = EnvMgr()
bk_env_mgr = BkEnvMgr()
