node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}


pipeline {
    agent {
        node {
            label "**************"
        }
    }

    stages {
        stage("安装python依赖") {
            steps {
                script {
                    bat(
                        script: """
                            pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("获取p4最新分支") {
                steps {
                    bat(
                        script: """
                            python x52.py branch_vacation_compare --job=get_p4_branches
                        """
                    )
                }
            }
        stage("最新分支和主支对比") {
            steps {
                bat(
                    script: """
                        python x52.py branch_vacation_compare --job=compare
                    """
                )
            }
        }
    }
    post {
        success{
            bat(
                script: """
                    python x52.py branch_vacation_compare --job=on_success
                """
            )
        }
        failure{
            bat(
                script: """
                    python x52.py branch_vacation_compare --job=on_failure
                """
            )
        }
    }
}
