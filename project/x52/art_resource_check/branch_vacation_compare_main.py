#! coding:utf-8
import re
from frame import *
from project.x52.art_resource_check.mgr.p4_mgr import p4_mgr
from project.x52.art_resource_check.config import config
from project.x52.art_resource_check.mgr.env_mgr import env_mgr, jenkins_env_mgr
from project.x52.art_resource_check.mgr.compare_mgr import CompareMgr
from project.x52.art_resource_check.mgr.vika_mgr import vika_mgr


# 获取现网和当前最新分支, p4
# 获取各自的changelist, p4
# diff2, 返回字典列表, p4
# 获取维格的规则列表, vika
# 遍历diff2结果，匹配维格规则，拿到资源id, compare
# 根据资源id获取资源版本, vika
# 写入xlsx, compare
# 上传, compare
# 通知
@advance.stage(stage="获取p4最新分支")
def get_p4_branches():
    current_branch = p4_mgr.get_current_branch(path=config.p4_branch_root)
    env_mgr.set_current_branch(branch=current_branch)
    current_change = p4_mgr.get_last_change(path=current_branch)
    env_mgr.set_current_branch_change(change=current_change)
    vacation_change = p4_mgr.get_last_change(path=config.p4_vacation_path)
    env_mgr.set_vacation_change(change=vacation_change)


@advance.stage(stage="对比差异并上传制品库")
def compare():
    current_branch = env_mgr.get_current_branch()
    compare_mgr = CompareMgr()
    diffs = p4_mgr.diff(path1=current_branch, path2=config.p4_vacation_path)
    ret = []
    resource_list = vika_mgr.get_resource_list()
    for d in diffs:
        p4_path = d.get("depotFile")
        # 过滤白名单
        if config.white_list[0] in p4_path or config.white_list[1] in p4_path or config.white_list[2] in p4_path or config.white_list[3] in p4_path:
            continue
        is_only = d.get("status")
        resource_id = compare_mgr.get_resource_id(p4_path=p4_path)
        if not resource_id:
            resource_dict = {
                "p4_path": p4_path,
                "resource_id": resource_id,
                "submitter": "",
                "resource_version": "",
                "subject": "",
                "is_only": is_only,
                "output_system": "",
            }
            ret.append(resource_dict)
            log.info(resource_dict)
            continue
        for r in resource_list:
            if r.get("resource_id") and resource_id in r.get("resource_id"):
                if r.get("resource_version"):
                    resource_version = r.get("resource_version")[0]
                else:
                    resource_version = ""
                # 判断小包, 小包主题可能有多个
                is_small_package = False
                if jenkins_env_mgr.get_subject() and r.get("subject"):
                    jenkins_subjects = jenkins_env_mgr.get_subject().split(",")
                    for s in jenkins_subjects:
                        if s.strip() in r.get("subject"):
                            is_small_package = True
                            break

                if is_small_package:
                    resource_version = resource_version + "小包"

                resource_dict = {
                    "p4_path": p4_path,
                    "resource_id": resource_id,
                    "submitter": "",
                    "resource_version": resource_version,
                    "subject": r.get("subject"),
                    "is_only": is_only,
                    "output_system": r.get("output_system")[0] if r.get("output_system") else "",
                }
                ret.append(resource_dict)
                break
        else:
            # 没找到资源id
            resource_dict = {
                "p4_path": p4_path,
                "resource_id": resource_id,
                "submitter": "",
                "resource_version": "",
                "subject": "",
                "is_only": is_only,
                "output_system": "",
            }
            ret.append(resource_dict)
        log.info(resource_dict)

    # 生成excel
    branch_version_pattern = "(.*)(version_\d.\d.\d)(.*)"
    # 从分支完整路径匹配出version_x.x.x
    branch_version = re.match(branch_version_pattern, current_branch).groups()[1]
    filename = f"{branch_version}-vacation.xls"
    env_mgr.set_filename(filename)
    compare_mgr.generator_xlsx(diff_resources=ret, filename=filename)
    # upload nexus
    xlsx_url = advance.upload_pipeline_log(path=filename)
    env_mgr.set_xlsx_url(url=xlsx_url)


def on_success():
    current_branch = env_mgr.get_current_branch()
    current_change = env_mgr.get_current_branch_change()
    vacation_change = env_mgr.get_vacation_change()
    xlsx_url = env_mgr.get_xlsx_url()
    filename = env_mgr.get_filename()

    msg = ""
    msg += f"**分支**: {current_branch}\n"
    msg += f"**分支最新changelist**: {current_change}\n"
    msg += f"**主支**: {config.p4_vacation_path}\n"
    msg += f"**主支最新changelist**: {vacation_change}\n"

    msg += f"**对比结果**: [{filename}]({xlsx_url})"
    wechat.send_unicast_post_success(user_list=[], content=msg)
    wechat.send_multicast_post_success(webhook=config.webhook, content=msg)
    advance.insert_pipeline_history_on_success()


def on_failure():
    msg = ""
    msg += f"**对比结果**: 流水线异常,请点击流水线日志查看原因"
    wechat.send_unicast_post_failure(content=msg, user_list=[env.get("BUILD_USER_EMAIL")])
    wechat.send_multicast_post_failure(webhook=config.webhook, content=msg, rescue=False)
    advance.insert_pipeline_history_on_failure()
