import re
import xlwt
from frame import *
from project.x52.art_resource_check.mgr.vika_mgr import vika_mgr
from project.x52.art_resource_check.mgr.p4_mgr import p4_mgr


class CompareMgr:
    def __init__(self):
        self._rules = vika_mgr.get_resource_id_rules()

    def get_resource_id(self, p4_path: str) -> str:
        """
        根据资源规则表，匹配资源id
        Args:
            p4_path: p4完整路径
        Returns；
            str: 资源id
        """
        try:
            for rule in self._rules:
                pattern_result1 = re.match(f"(.*)\D({rule.get('first_rule')})\D(.*)", p4_path)
                pattern_result2 = re.match(f"(.*)\D({rule.get('second_rule')})\D(.*)", p4_path)
                if not pattern_result1 and not pattern_result2:
                    continue
                if pattern_result2:  # 直接取衍生色的占位规则的资源id
                    return pattern_result2.groups()[1]

                return pattern_result1.groups()[1]
        except Exception as e:
            raise PyframeException(f"获取资源id失败: {e}, 请检查p4路径{p4_path} 匹配规则")

    def get_vacation_info(self, path: str):
        """
        分支改成主支，获取主支提交人
        "//H3D_X52_res/QQX5-2_Exe/Branchs/version_2.6.3/resources/art/role/bodypart/female/shoe/618654601/618654601.mat"
        ---> "//H3D_X52_res/QQX5-2_Exe/Vacation/resources/art/role/bodypart/female/shoe/618654601/618654601.mat"
        Args:
            resources: 差异资源列表
        """
        vacation_path = re.sub("Branchs/version_\d+.\d+.\d+", "Vacation", path)
        # 判断主支是否存在
        exist = p4_mgr.p4.files(path=vacation_path)
        if not exist:
            return None
        submitter = p4_mgr.p4.get_latest_changes(path=vacation_path).get("user")

        return {"path": vacation_path, "submitter": submitter}

    def generator_xlsx(self, diff_resources, filename):
        """
        根据维格过滤后生成的资源列表，写入excel
        Args:
            diff_resources: 差异资源列表
        """
        work_book = xlwt.Workbook(encoding="utf-8")
        worksheet = work_book.add_sheet("比较结果")
        worksheet.write(0, 0, "p4路径")
        worksheet.write(0, 1, "资源id")
        worksheet.write(0, 2, "维格版本")
        worksheet.write(0, 3, "修改人")
        worksheet.write(0, 4, "是否独有")
        worksheet.write(0, 5, "主题名")
        count = 1
        for i in diff_resources:
            vacation_info = self.get_vacation_info(path=i.get("p4_path"))
            if vacation_info:
                i["p4_path"] = vacation_info.get("path")  # 改成主支
                i["submitter"] = vacation_info.get("submitter")
            only = "是"
            if i.get("is_only") == "content":
                only = "否"
            worksheet.write(count, 0, i.get("p4_path"))
            worksheet.write(count, 1, i.get("resource_id"))
            worksheet.write(count, 2, i.get("resource_version"))
            worksheet.write(count, 3, i.get("submitter"))
            worksheet.write(count, 4, only)
            worksheet.write(count, 5, i.get("subject") if i.get("subject") else i.get("output_system"))
            count += 1
        work_book.save(filename)
