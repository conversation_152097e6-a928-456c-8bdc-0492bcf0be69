import re
from frame import *
from frame.p4.p4client import P4Client
from project.x52.art_resource_check.config import config


class P4Mgr:
    def __init__(self, *args, **kwargs):
        self.p4 = P4Client(*args, **kwargs)

    def get_current_branch(self, path) -> str:
        pattern = "^version_\d.\d.\d$"
        pattern_rule = re.compile(pattern)
        try:
            paths = [i for i in self.p4.dirs(path=path) if pattern_rule.match(os.path.basename(i))]
        except Exception as e:
            raise PyframeException(f"ERROR 获取最新分支代码异常: {e}, 请检查p4路径是否正确{path}")
        # 无需在排序，分支已经按照从小达到版本排序
        return paths[-1] + "/resources/..."

    def get_xw_branch(self, path) -> str:
        pattern = "^version_\d.\d.\d$"
        pattern_rule = re.compile(pattern)
        try:
            paths = [i for i in self.p4.dirs(path=path) if pattern_rule.match(os.path.basename(i))]
        except Exception as e:
            raise PyframeException(f"获取现网分支代码异常: {e}， 请检查p4路径是否正确{path}")
        # 无需在排序，分支已经按照从小达到版本排序
        return paths[-2] + "/resources/..."

    def listdir(self, root_path):
        """
        获取path文件及文件夹
        Args:
            path: 路径
        """
        result1 = []
        result2 = []
        result3 = []
        result4 = []
        result = []
        for d in diff_path_list:
            dir = os.path.dirname(d.get("depotFile"))
            if root_path in dir:
                path = d.get("depotFile").split(root_path)[-1].split("/")[1]
                if path not in result1 and path not in result2:
                    if "." in path:
                        result2.append(path)
                        temp = {"path": path, "info": d}
                        result4.append(temp)
                    else:
                        result1.append(path)
                        temp = {"path": path, "info": d}
                        result3.append(temp)
        result.extend(result3)
        result.extend(result4)
        return result

    def generator_dir_tree(self, path: str, depth: int, site: list):
        """
        生成目录树
        Args:
            path: 根路径
            depth: 目录层级深度
        """
        void_num = 0
        filenames_list = self.listdir(path)

        for item in filenames_list:
            if item != filenames_list[-1]:
                pass
            else:
                void_num += 1
                # 添加当前已出现转折的层级数
                site.append(depth)
            new_item = path + "/" + item.get("path")
            if "." not in item.get("path"):  # 说明是目录
                self.generator_dir_tree(new_item, depth + 1, site)
            if item == filenames_list[-1]:
                void_num -= 1
                # 移除当前已出现转折的层级数
                site.pop()
            if "." in item.get("path"):
                node = {"path": new_item, "info": item}
                sorted_diffs.append(node)

    def sorted_diff_files(self, root_path):
        """
        排序差异文件
        Args:
            diff_files: 差异文件列表
            root_path: 资源根路径
        """
        self.generator_dir_tree(path=root_path, depth=0, site=[])

    def diff(self, path1: str, path2: str) -> list:
        try:
            global diff_path_list
            diff_path_list = []
            global sorted_diffs
            sorted_diffs = []
            diff_path_list = [diff for diff in self.p4.diff2(path1=path1, path2=path2, q=True) if diff.get("status") != "right only"]
            # 生成根路径
            root_path = diff_path_list[0].get("depotFile").split("resources", 1)[0] + "resources"
            # 排序差异文件
            self.sorted_diff_files(root_path=root_path)

            filter_diff_result = [
                {
                    "status": i.get("info").get("info").get("status"),
                    "depotFile": i.get("path"),
                }
                for i in sorted_diffs
            ]

            return filter_diff_result
        except Exception as e:
            raise PyframeException(f"p4 diff2 代码异常: {e}，请检查diff2方法")

    def get_last_change(self, path: str):
        try:
            change = self.p4.get_latest_changes(path=path).get("change")
            return change
        except Exception as e:
            raise PyframeException(f"获取最新changelist代码异常: {e}, 请检查{path}是否正确")

    def pattern_branch_path(self, path: str, branch_version: str):
        """
        根据用户传入branch_version: x.x.x 例子 2.6.3 匹配出分支路径//H3D_X52_res/QQX5-2_Exe/Branchs/version_2.6.3/resources/
        Args:
            path: 分支路径前缀"//H3D_X52_res/QQX5-2_Exe/Branchs/*"
            branch_version: 分支版本号
        """
        pattern = "^version_\d.\d.\d$"
        pattern_rule = re.compile(pattern)
        try:
            paths = [i for i in self.p4.dirs(path=path) if pattern_rule.match(os.path.basename(i)) and i.endswith(branch_version)]
            if len(paths) == 1:
                return paths[0] + "/resources/..."
            raise PyframeException(f"未匹配到用户传递的分支版本号，请检查p4上是否存在分支{branch_version}")
        except Exception as e:
            raise PyframeException(f"获取现网分支代码异常: {e}, 请检查分支匹配规则")

    def changelist_validate(self, path: str, change: str):
        """
        changelist校验
        Args:
            change: resources上的changelist号
        """
        try:
            change = [i.get("change") for i in self.p4.get_changes(path=path, max=100000) if change == str(i.get("change"))]
            if change:
                return True
            else:
                raise PyframeException(f"填写changelist不在对应分支上，请检查p4号")
        except Exception as e:
            raise PyframeException(f"获取最新changelist代码异常: {e}, 请检查{path}是否正确")


p4_mgr = P4Mgr(host=config.p4_host, username=config.p4_username, password=config.p4_password, client=config.p4_client, charset=P4Client.Charset.NONE)
