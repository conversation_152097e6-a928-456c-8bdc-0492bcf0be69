node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}


pipeline {
    agent {
        node {
            label "**************"
        }
    }
    parameters {
           string name: 'branch_version', defaultValue: "", description: '小包资源分支名'
           string name: 'subject', defaultValue: "", description: '小包资源主题名'
           string name: 'changelist', defaultValue: "", description: '小包资源对比第一个资源changelist'
    }


    stages {
        stage("安装依赖") {
            steps {
                bat(
                    script: """
                        python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        python -m pip install -r requirements.txt -i  http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                    """
                )
            }
        }
        stage("校验参数") {
            steps {
                bat(
                    script: """
                        python x52.py small_package_compare --job=args_check
                    """
                )
            }
        }
        stage("获取p4小包资源分支") {
                steps {
                    bat(
                        script: """
                            python x52.py small_package_compare --job=get_p4_branches
                        """
                    )
                }
            }
        stage("小包资源对比") {
            steps {
                bat(
                    script: """
                        python x52.py small_package_compare --job=compare
                    """
                )
            }
        }
    }
    post {
        success{
            bat(
                script: """
                    python x52.py small_package_compare --job=on_success
                """
            )
        }
        failure{
            bat(
                script: """
                    python x52.py small_package_compare --job=on_failure
                """
            )
        }
    }
}