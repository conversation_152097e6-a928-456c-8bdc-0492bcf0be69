node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}


pipeline {
    agent {
        node {
            label "**************"
        }
    }

    stages {
        stage("获取p4最新分支和现网分支") {
                steps {
                    bat(
                        script: """
                            python x52.py xw_current_compare --job=get_p4_branches
                        """
                    )
                }
            }
        stage("现网分支和最新分支对比") {
            steps {
                bat(
                    script: """
                        python x52.py xw_current_compare --job=compare
                    """
                )
            }
        }
    }
    post {
        success{
            bat(
                script: """
                    python x52.py xw_current_compare --job=on_success
                """
            )
        }
        failure{
            bat(
                script: """
                    python x52.py xw_current_compare --job=on_failure
                """
            )
        }
    }
}