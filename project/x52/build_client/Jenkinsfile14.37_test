node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}
pipeline{
    agent {
        node {
            label "win10_192.168.14.37"
            customWorkspace "D:/jks_test"
        }
    }
    environment {
       IS_DEBUG = 'true'
    }
    stages {
        stage('参数校验'){
            steps {
                dir('pyframe-pipeline') {
                    bat """
                        python x52.py build_client --job=check_args
                    """
                }
            }
        }
        stage('更新p4资源'){
            steps {
                dir('pyframe-pipeline') {
                    bat """
                        python x52.py build_client --job=update_p4
                    """
                }
            }
        }
        stage('编译'){
            steps {
                dir('pyframe-pipeline') {
                    bat """
                        python x52.py build_client --job=build
                    """
                }
            }
        }
        stage('上传客户端'){
            steps {
                dir('pyframe-pipeline') {
                    bat """
                        python x52.py build_client --job=upload
                    """
                }
            }
        }
    }
    post {
        success{
            dir('pyframe-pipeline'){
                bat(
                script: """
                    python x52.py build_client --job=on_success
                """
                )
            }

        }
        failure{
            dir('pyframe-pipeline') {
                bat(
                script: """
                    python x52.py build_client --job=on_failure
                """
                )
            }

        }
    }
}