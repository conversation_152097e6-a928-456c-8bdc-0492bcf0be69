# import socket
import os
from frame import common
from project.x52 import config


def is_debug():
    return os.getenv("IS_DEBUG", "False").lower() == "true"


# p4.com:2002配置
p4_build_host = config.P4_2002_AUTO_BUILD_CONFIG.get("host")
p4_build_username = config.P4_2002_AUTO_BUILD_CONFIG.get("username")
p4_build_password = config.P4_2002_AUTO_BUILD_CONFIG.get("password")
p4_build_client = f"x52_build_client_{common.get_host_ip()}_test" if is_debug() else f"x52_build_client_{common.get_host_ip()}"
p4_trunk = "//depot/products/Project_X52"

# p4.com:2004配置
p4_upload_host = config.P4_2004_AUTO_BUILD_CONFIG.get("host")
p4_upload_username = config.P4_2004_AUTO_BUILD_CONFIG.get("username")
p4_upload_password = config.P4_2004_AUTO_BUILD_CONFIG.get("password")
p4_upload_client = f"x52_build_client_{common.get_host_ip()}_test" if is_debug() else f"x52_build_client_{common.get_host_ip()}"

# upload p4 上生产再放开
p4_upload_trunk_path = "//H3D_X52_res/QQX5-2_Exe/Vacation_binary"
p4_upload_branch_path = "//H3D_X52_res/QQX5-2_Exe/branches_binary"

if is_debug():
    p4_upload_trunk_path = "//H3D_X52_res/X52_SourceBase/QE_Test/client_build/Vacation_binary"
    p4_upload_branch_path = "//H3D_X52_res/X52_SourceBase/QE_Test/client_build/branches_binary"

# 企微通知
webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f129a8e5-f097-47f0-a8af-2ef327ec31c5"
if is_debug():
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0579ea0e-4ec2-435b-9f63-9f561fdeaf9a"
