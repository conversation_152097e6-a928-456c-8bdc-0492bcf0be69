# coding=utf-8
from frame import *


class BkEnvMgr:
    def __init__(self):
        pass

    def get_branch(self):
        branch = env.get("BRANCH")
        return branch.strip()

    def get_p4_force_update(self):
        return common.str2bool(env.get("P4_FORCE_UPDATE"))

    def get_rebuild(self):
        return common.str2bool(env.get("REBUILD"))

    def get_code_changelist(self):
        code_changelist = env.get("CODE_CHANGELIST")
        if isinstance(code_changelist, str):
            code_changelist = code_changelist.strip()
            code_changelist = code_changelist.lower()
        return code_changelist


class EnvMgr:
    def __init__(self):
        pass

    def set_resource_changelist(self, resource_changelist):
        env.set({"resource_changelist": resource_changelist})

    def get_resource_changelist(self):
        return env.get("resource_changelist")

    def set_artifact_path(self, artifact_path):
        env.set({"artifact_path": artifact_path})

    def get_artifact_path(self):
        return env.get("artifact_path")

    def set_artifact_ftp_path(self, p):
        env.set({"artifact_ftp_path": p})

    def get_artifact_ftp_path(self):
        return env.get("artifact_ftp_path")

    def set_code_changelist(self, code_changelist):
        env.set({"accurate_code_changelist": code_changelist})

    def get_code_changelist(self):
        return env.get("accurate_code_changelist")

    def set_server_tar_size(self, server_tar_size: str):
        env.set({"server_tar_size": server_tar_size})

    def get_server_tar_size(self):
        return env.get("server_tar_size")

    def set_distcc_log_url(self, distcc_log_url: str):
        env.set({"distcc_log_url": distcc_log_url})

    def get_distcc_log_url(self):
        return env.get("distcc_log_url")


class GlobalEnvMgr:
    def set_global_last_code_changelist(self, branch: str, code_changelist: str):
        env.set_global({f"{branch}_global_last_code_changelist": code_changelist})

    def get_global_last_code_changelist(self, branch: str):
        return env.get_global(f"{branch}_global_last_code_changelist")


env_mgr = EnvMgr()
bk_env_mgr = BkEnvMgr()
global_env_mgr = GlobalEnvMgr()
