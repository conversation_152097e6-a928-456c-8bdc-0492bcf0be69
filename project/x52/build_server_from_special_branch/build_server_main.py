# coding=utf-8
import time

from frame import *
from project.x52.build_server_from_special_branch.mgr.p4_com_2002 import P42002
from project.x52.build_server_from_special_branch.mgr.p4_com_2004 import P42004


def __clean_inter():
    """
    清理编译的中间目录
    """
    branch = env.get("BRANCH")
    if branch == "trunc":
        workdir = "/data/workspace/trunc/src/"
    else:
        workdir = f"/data/workspace/{branch}/src/"
    inter_path = os.path.join(workdir, "inter")
    path_mgr.rm(inter_path)


def __clean_branch():
    """
    编译之前，清理分支
    """
    base_path = "/data/workspace"
    branches = [c.name for c in Path(base_path).glob("*.*.*")]
    for i in range(0, len(branches) - 1):
        path_mgr.rm(branches[i])
        log.warn(f"delete branch: {branches[i]}")


@advance.stage(stage="拉取p4")
def sync_code_from_p4(**kwargs):
    __clean_branch()
    # 检查磁盘空间是否满足执行流水线
    advance.check_disk_size(threshold=5)
    branch = kwargs.get("BRANCH")
    p4_force_update = kwargs.get("P4_FORCE_UPDATE")
    rebuild = kwargs.get("REBUILD")
    changelist = kwargs.get("CODE_CHANGELIST")

    p4_2002 = P42002(branch=branch)
    p4_2004 = P42004(branch=branch)
    code_changelist = p4_2002.get_code_latest_changes() if changelist == "head" else changelist
    resource_changelist = p4_2004.get_resource_latest_changes()

    env.set({"branch": branch, "rebuild": rebuild, "code_changelist": code_changelist, "resource_changelist": resource_changelist})

    if p4_force_update:
        p4_2002.sync_all(changelist=code_changelist, force=True)
    else:
        p4_2002.sync_all(changelist=code_changelist, force=False)


@advance.stage(stage="编译服务器")
def build_server(**kwargs):
    branch = kwargs.get("BRANCH")
    rebuild = kwargs.get("REBUILD")
    env.set({"branch": branch})
    if branch == "trunc":
        workdir = "/data/workspace/trunc/src/"
    else:
        workdir = f"/data/workspace/{branch}/src/"
    inter_path = os.path.join(workdir, "inter")

    env.set({"inter_path": inter_path})
    cmakelists_path = os.path.join(workdir, "CMakeLists.txt")
    bin_path = f"/data/workspace/{branch}/exe/bin"
    file_mgr.replace_file_string(file=cmakelists_path, old="/home/<USER>/exe/bin11", new=bin_path)

    if os.path.exists(inter_path):
        if rebuild:
            path_mgr.rm(inter_path)
            os.makedirs(inter_path)
    else:
        os.makedirs(inter_path)

    start_time = time.time()

    ret = cmd.run_shell(
        cmds=[
            "source /etc/profile",
            "source /opt/rh/devtoolset-9/enable",
            "export CC='/usr/local/bin/distcc'",
            "export CXX='/usr/local/bin/distcc g++'",
            "which gcc",
        ],
        workdir=inter_path,
    )
    if ret[0] != 0:
        raise PyframeException("编译环境初始化失败, 返回码: {}".format(ret[0]))

    ret = cmd.run_shell(
        cmds=[
            "source /etc/profile",
            "source /opt/rh/devtoolset-9/enable",
            "export CC='/usr/local/bin/distcc'",
            "export CXX='/usr/local/bin/distcc g++'",
            "which gcc",
            "export DISTCC_HOSTS=`curl -qs http://distcc.tac.com/hosts?env=centos66-with-gcc9`",
            "../cmake.sh ../ > ../buildlog.log",
            "cd ..",
            "make install -j40",
            # "make install -j40 2>&1 | tee make.log"
        ],
        workdir=inter_path,
    )
    if ret[0] != 0:
        log.error("build error, return {}".format(ret[0]))
        errors = __parse_build_errors(build_log=os.path.join(inter_path, "make.log"))
        log.debug("build errors: {}".format(errors))
        if len(errors) > 0:
            env.set({"build_errors": "\n".join(errors)})
        raise PyframeException("编译失败, 请检查编译错误, 返回码: {}".format(ret[0]))
    else:
        log.info("build server cost time: {}".format(common.format_duration(time.time() - start_time)))


@advance.stage(stage="压缩上传")
def zip_upload(**kwargs):
    branch = kwargs.get("BRANCH")
    code_changelist = env.get("code_changelist")
    resource_changelist = env.get("resource_changelist")
    bin_path = f"/data/workspace/{branch}/exe/bin"
    tar_path = f"/data/workspace/{branch}/exe/resource_{resource_changelist}_code_{code_changelist}.tar.gz"
    tar.compress(src=bin_path, dst=tar_path)
    remote_path = f"/data/mockp4/x52/{branch}/"
    cmd.run_shell(
        cmds=[
            "mount.cifs -o username=age,password=111,dir_mode=0777,file_mode=0777 //************/release/x52 /data/mockp4/x52/",
        ],
    )
    path_mgr.mkdir(remote_path)
    dst = os.path.join(remote_path, f"resource_{resource_changelist}_code_{code_changelist}.tar.gz")
    count = 0
    while count < 3:
        count += 1
        try:
            path_mgr.move(src=tar_path, dst=dst)
        except Exception as e:
            log.error(f"upload failed, try again. error: {e}")
            time.sleep(1)
        if os.path.exists(path=dst):
            log.info("upload success")
            break
    artifacts = f"//soft.h3d.com.cn/release/x52/{branch}/resource_{resource_changelist}_code_{code_changelist}.tar.gz"
    env.set({"artifacts": artifacts})


def __parse_build_errors(build_log: str) -> list:
    """
    解析编译错误
    """
    if not path_mgr.exists(build_log):
        return []
    f = open(build_log, "r")
    line = f.readline()
    errors = []
    while line:
        if " error: " in line:
            if ".cpp" in line or ".h" in line or ".c" in line or ".hpp" in line or ".cc" in line:
                errors.append(line)
        line = f.readline()
    f.close()
    return errors


def post_success(**kwargs):
    """
    成功后通知
    """
    __clean_inter()
    content = "**分支:** {}\n**rebuild:** {}\n**制品:** {}".format(env.get("branch"), env.get("rebuild"), env.get("artifacts"))
    wechat.send_unicast_post_success(user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>"], content=content)
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4577d356-e6ee-4504-a87e-063b5f10b8a3", content=content
    )
    cmd.run_shell(["umount /data/mockp4/x52/"])
    advance.insert_pipeline_history_on_success()


def post_failure(**kwargs):
    """
    失败后通知
    """
    __clean_inter()

    content = "**分支:** {}\n**rebuild:** {}".format(env.get("branch"), env.get("rebuild"))
    errors = env.get("build_errors")
    if errors is not None:
        content += f"\n**错误定位:**\n{errors}"
    wechat.send_unicast_post_failure(
        # user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        content=content
    )
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4577d356-e6ee-4504-a87e-063b5f10b8a3", content=content
    )
    cmd.run_shell(["umount /data/mockp4/x52/"])
    advance.insert_pipeline_history_on_failure()


def post_canceled(**kwargs):
    """
    取消后通知
    """
    __clean_inter()
    content = "**分支:** {}\n**rebuild:** {}".format(env.get("branch"), env.get("rebuild"))
    wechat.send_unicast_post_canceled(user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>"], content=content)
    wechat.send_multicast_post_canceled(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4577d356-e6ee-4504-a87e-063b5f10b8a3", content=content
    )
    cmd.run_shell(["umount /data/mockp4/x52/"])
    advance.insert_pipeline_history_on_canceled()
