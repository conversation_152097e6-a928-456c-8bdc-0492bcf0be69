# coding=utf-8
import re

from frame import *
from project.x52 import config


class P42004:
    def __init__(self, branch: str):
        self.__branch = branch
        self.__client = "x52_build_server_{}_{}".format(self.__branch, common.get_host_ip())
        self.p4 = P4Client(
            host=config.P4_2004_CONFIG.get("host"),
            username=config.P4_2004_CONFIG.get("username"),
            password=config.P4_2004_CONFIG.get("password"),
            client=self.__client,
        )
        views = [
            "//H3D_X52_res/QQX5-2_Exe/...        //{}/QQX5-2_Exe/...".format(self.__client),
        ]
        self.p4.set_view(views=views)
        root = "/data/workspace/{}/src".format(branch)
        self.p4.set_root(root)

    def __get_resource_branch_path(self):
        """
        获取资源分支的路径

        Returns:
            //H3D_X52_res/QQX5-2_Exe/Vacation_binary/...
            //H3D_X52_res/QQX5-2_Exe/branches_binary/version_2.4.9_binary/...
        """
        pattern = r"^(\d+.\d+.\d+$|trunc)"
        if not re.findall(pattern, self.__branch):
            return "//H3D_X52_res/QQX5-2_Exe/branches_binary/..."
        if self.__branch == "trunc":
            return "//H3D_X52_res/QQX5-2_Exe/Vacation_binary/..."
        else:
            s = self.__branch.split(".")
            return "//H3D_X52_res/QQX5-2_Exe/branches_binary/version_{}.{}.{}_binary/...".format(s[0], s[1], s[2])

    def get_resource_latest_changes(self):
        """
        获取资源分支的最新changelist
        """
        ret = self.p4.get_latest_changes(self.__get_resource_branch_path())
        try:
            changelist = ret.get("change")
        except Exception as e:
            log.error(e)
            changelist = "head"
        return changelist
