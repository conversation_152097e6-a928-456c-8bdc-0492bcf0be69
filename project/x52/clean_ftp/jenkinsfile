node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}
pipeline {
    agent {
        node {
            label "win_server_2019_192.168.13.215"
        }
    }
    options {
        disableConcurrentBuilds()
        parallelsAlwaysFailFast()
    }
    triggers {
        cron('0 11 * * *')
    }
    parameters {
        string(name: 'X52_FTP_ROOT', defaultValue: 'E:\\x52', description: 'x52 ftp 根目录')
        string(name: 'RETAIN', defaultValue: '10', description: '同一个分支的版本保留数量')
    }
    environment {
       PYFRAME_PYTHON = 'conda run -n pyf368 --live-stream python'
    }
    stages {
        stage("创建虚拟环境") {
            steps {
                script {
                    bat(
                        script: """
                        conda env list | findstr pyf368 || conda create -y -n pyf368 python=3.6.8
                        """
                    )
                }
            }
        }
        stage("安装python依赖") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("清理ftp") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py clean_x52_ftp --job=clean_ftp
                        """
                    )
                }
            }
        }
    }
    post {
        success {
            script {
                bat(script: "$env.PYFRAME_PYTHON x52.py clean_x52_ftp --job=on_success")
            }
        }
        failure {
            script {
                bat(script: "$env.PYFRAME_PYTHON x52.py clean_x52_ftp --job=on_failure")
            }
        }
        aborted {
            script {
                bat(script: "$env.PYFRAME_PYTHON x52.py clean_x52_ftp --job=on_aborted")
            }
        }
    }
}
