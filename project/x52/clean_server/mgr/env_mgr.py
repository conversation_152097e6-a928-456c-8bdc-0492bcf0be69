from frame import env


class EnvMgr:
    @staticmethod
    def get_before_clean() -> int:
        return env.get("before_clean")

    @staticmethod
    def set_before_clean(size: int):
        env.set({"before_clean": size})

    @staticmethod
    def get_after_clean() -> int:
        return env.get("after_clean")

    @staticmethod
    def set_after_clean(size: int):
        env.set({"after_clean": size})


env_mgr = EnvMgr()
