# 1. 炫舞2服务器清理

## 1.1. 目录结构

```bash
cd /data/workspace
tree -L 2
.
|-- 2.4.7
|   |-- X52_SourceBase
|   |-- _p4client
|   |-- exe
|   |-- p4logs
|   `-- x52-bkpipeline
|-- 2.4.8
|   |-- X52_SourceBase
|   |-- _p4client
|   |-- exe
|   |-- p4logs
|   `-- x52-bkpipeline
`-- trunc
    |-- X52_SourceBase
    |-- _p4client
    |-- exe
    |-- p4logs
    `-- x52-bkpipeline
```

## 1.2. 清理分支

主、分支的匹配规则：

```bash
主支是trunc，分支是x.x.x。
```

主、分支的路径：

```bash
/data/workspace/
```

清理任务：

```
不清理主支。
清理分支，最多保留2个最新分支。（根据创建时间来判断新旧）
```


## 1.3. 清理core
core的匹配规则：
```bash
core.*
```

主支的core路径：
```bash
/data/workspace/trunc/exe/bin11/
```

分支的core路径：
```bash
/data/workspace/x.x.x/exe/bin11/
```


## 1.4. 清理主支和分支日志

日志的匹配规则：
```bash
*.log
```

主支的日志路径：
```bash
/data/workspace/trunc/exe/logs/
```

分支的日志路径：
```bash
/data/workspace/x.x.x/exe/logs/
```

