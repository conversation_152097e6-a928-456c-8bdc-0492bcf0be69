import time

from api4jenkins import <PERSON>

from frame import PyframeException, log
from project.x52.create_new_branch.config import config


class CheckMgr:
    def __init__(self):
        self.config = config.<PERSON>
        if self.config.password:
            self.jen<PERSON> = <PERSON>(self.config.url, auth=(self.config.username, self.config.password))
        else:
            self.jen<PERSON> = <PERSON>(self.config.url, token=self.config.token)

    def check_jenkins_trunk_art_resources(self):
        job = self.jenkins.get_job(self.config.job_name)
        latest_build = job.get_last_build()
        while latest_build.building:
            log.info(f"资源转换流水线正在运行: {latest_build.url}, 资源转换结束后继续创建分支")
            time.sleep(2)

        if not latest_build.result == "SUCCESS":
            raise PyframeException(f"主支美术资源转换失败, 请保证主支美术资源正确后再切分支, {latest_build.url}")


if __name__ == "__main__":
    check_mgr = CheckMgr()
    check_mgr.check_jenkins_trunk_art_resources()
