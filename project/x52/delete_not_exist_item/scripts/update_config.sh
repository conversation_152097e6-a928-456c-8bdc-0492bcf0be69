#!/bin/bash 
CONFIG_PREFIX="resources/config/shared/item/"
CONFIG_PATH="${TOOLROOT}/${CONFIG_PREFIX}"
CONFIG_INI="./item_config.ini"
ALLTYPES="${OUTROOT}/all_types.txt"


cat /dev/null > $ALLTYPES
total_count=0
cp /data/workspace/delete_not_exist_item/resources/config/shared/item/servant_item.xml /data/workspace/delete_not_exist_item/resources/config/shared/item/servant_item_2.xml

while read FILE TAG TAG2;
do
  echo $FILE.xml $TAG $TAG2;
  FILE=$FILE.xml

  FILE_PATH=${CONFIG_PATH}${FILE}

  if ! test -f "${FILE_PATH}"; then
    echo "Error: File not exist ${FILE_PATH}"
    exit 1
  fi

  tag_count1=`grep -i "<$TAG id" "${FILE_PATH}" | wc -l`
  tag_count2=`grep -i "<$TAG2>" "${FILE_PATH}" | wc -l`
  ((tag_count=$tag_count1+$tag_count2))

  type_count1=`grep -i "<$TAG id" "${FILE_PATH}" | awk '{print $2;}' | cut -d'"' -f2 | wc -l`
  type_count2=`grep -i "<$TAG2>" "${FILE_PATH}" | sed -e 's/</ /g' -e 's/>/ /g' | awk '{print $2;}' | wc -l`
  ((type_count=$type_count1+$type_count2))

  echo $FILE tagcount $tag_count = $tag_count1 + $tag_count2 typecount $type_count = $type_count1 + $type_count2

  if [ $tag_count -eq $type_count ]; then
    grep -i "<$TAG id" "${FILE_PATH}" | awk '{print $2;}' | cut -d'"' -f2 >> $ALLTYPES

    if [ $type_count2 -gt 0 ]; then
      grep -i "<$TAG2>" "${FILE_PATH}" | sed -e 's/</ /g' -e 's/>/ /g' | awk '{print $2;}' >> $ALLTYPES
    fi
  else
    echo "Error: tag and type not equal. ${FILE_PATH}"
    exit 1
  fi

  ((total_count=$total_count+$type_count))

done < $CONFIG_INI

echo all type `wc -l $ALLTYPES`

if ! [ $total_count -eq `cat $ALLTYPES|wc -l` ]; then
  echo "Error: total count $total_count not match the result file"
  exit 1
fi

echo




