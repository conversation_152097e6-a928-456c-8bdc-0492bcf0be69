node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}
pipeline {
    agent {
        node {
            label "linux_server_2019_192.168.13.215"
        }
    }
    options {
        disableConcurrentBuilds()
        parallelsAlwaysFailFast()
    }
    triggers {
        cron('0 11 * * *')
    }
    parameters {
        string(name: 'PROJECT_BRANCH', defaultValue: 'trunk', description: '项目分支')
        string(name: 'PROJECT_CHANGELIST', defaultValue: 'head', description: '项目changelist')
        string(name: 'ICON_BRANCH', defaultValue: 'trunk', description: '图标分支')
        string(name: 'ICON_CHANGELIST', defaultValue: 'head', description: '图标changelist')      
        booleanParam(name: 'PROJECT_P4_FORCE', defaultValue: false, description: '是否强更项目p4')  
        booleanParam(name: 'ICON_P4_FORCE', defaultValue: false, description: '是否强更图标p4')
    }
    environment {
       PYFRAME_PYTHON = 'conda run -n pyf368 --live-stream python'
    }
    stages {
        stage("创建虚拟环境") {
            steps {
                script {
                    bat(
                        script: """
                        conda env list | findstr pyf368 || conda create -y -n pyf368 python=3.6.8
                        """
                    )
                }
            }
        }
        stage("安装python依赖") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("获取项目数据") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=sync_project_p4
                        """
                    )
                }
            }
        }
        stage("获取图标数据") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=sync_icon_p4
                        """
                    )
                }
            }
        }
        stage("准备物品清单") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=parse_item_list
                        """
                    )
                }
            }
        }
        stage("导入新图标") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=import_new_icon
                        """
                    )
                }
            }
        }
        stage("合成图标") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=synthesis_icon
                        """
                    )
                }
            }
        }
        stage("复制饰品原始图标") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=copy_original_icon
                        """
                    )
                }
            }
        }
        stage("计算图标特征") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=calc_icon_feature
                        """
                    )
                }
            }
        }
        stage("提交图标P4") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=submit_icon
                        """
                    )
                }
            }
        }
        stage("打包数据集") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=pack_dataset
                        """
                    )
                }
            }
        }
        stage("上传制品库") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=submit_dataset
                        """
                    )
                }
            }
        }
        stage("错误信息") {
            steps {
                script {
                    bat(
                        script: """
                            $env.PYFRAME_PYTHON x52.py generate_icon --job=show_error
                        """
                    )
                }
            }
        }
    }
    post {
        success {
            script {
                bat(script: "$env.PYFRAME_PYTHON x52.py generate_icon --job=on_success")
            }
        }
        failure {
            script {
                bat(script: "$env.PYFRAME_PYTHON x52.py generate_icon --job=on_failure")
            }
        }
        aborted {
            script {
                bat(script: "$env.PYFRAME_PYTHON x52.py generate_icon --job=on_aborted")
            }
        }
    }
}
