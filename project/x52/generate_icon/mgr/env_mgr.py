from frame import env, common


class JenkinsEnvMgr:
    @staticmethod
    def get_project_branch() -> str:
        return env.get("PROJECT_BRANCH")

    @staticmethod
    def get_project_p4_version() -> str:
        return env.get("PROJECT_CHANGELIST")

    @staticmethod
    def get_icon_branch() -> str:
        return env.get("ICON_BRANCH")

    @staticmethod
    def get_icon_p4_version() -> str:
        return env.get("ICON_CHANGELIST")

    @staticmethod
    def get_project_force_update() -> bool:
        return common.str2bool(env.get("PROJECT_P4_FORCE"))

    @staticmethod
    def get_icon_force_update() -> bool:
        return common.str2bool(env.get("ICON_P4_FORCE"))


class EnvMgr:
    @staticmethod
    def set_project_version_branch(version_branch: str):
        env.set({"PROJECT_VERSION_BRANCH": version_branch})

    @staticmethod
    def get_project_version_branch():
        return env.get("PROJECT_VERSION_BRANCH")

    @staticmethod
    def set_project_changelist(changelist: str):
        env.set({"PROJECT_CHANGELIST": changelist})

    @staticmethod
    def get_project_changelist():
        return env.get("PROJECT_CHANGELIST")

    @staticmethod
    def set_icon_version_branch(version_branch: str):
        env.set({"ICON_VERSION_BRANCH": version_branch})

    @staticmethod
    def get_icon_version_branch():
        return env.get("ICON_VERSION_BRANCH")

    @staticmethod
    def set_icon_changelist(changelist: str):
        env.set({"ICON_CHANGELIST": changelist})

    @staticmethod
    def get_icon_changelist():
        return env.get("ICON_CHANGELIST")

    @staticmethod
    def set_cmd_root(root_path: str):
        env.set({"CMD_ROOT_PATH": root_path})

    @staticmethod
    def get_cmd_root():
        return env.get("CMD_ROOT_PATH")

    @staticmethod
    def set_resources_dir(path: str):
        env.set({"RESOURCES_DIR": path})

    @staticmethod
    def get_resources_dir():
        return env.get("RESOURCES_DIR")

    @staticmethod
    def set_hires_dir(path: str):
        env.set({"HIRES_DIR": path})

    @staticmethod
    def get_hires_dir():
        return env.get("HIRES_DIR")

    @staticmethod
    def set_pkl_url(path: str):
        env.set({"PKL_URL": path})

    @staticmethod
    def get_pkl_url():
        return env.get("PKL_URL")

    @staticmethod
    def set_bad_items_url(path: str):
        env.set({"BAD_ITEMS_URL": path})

    @staticmethod
    def get_bad_items_url():
        return env.get("BAD_ITEMS_URL")

    @staticmethod
    def set_missing_items_url(path: str):
        env.set({"MISSING_ITEMS_URL": path})

    @staticmethod
    def get_missing_items_url():
        return env.get("MISSING_ITEMS_URL")

jenkins_env_mgr = JenkinsEnvMgr()
env_mgr = EnvMgr()
