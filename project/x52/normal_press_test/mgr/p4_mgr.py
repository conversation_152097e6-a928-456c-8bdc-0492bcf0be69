# coding=utf-8
from frame import os, path_mgr, P4Client, common, log, env
from project.x52 import config
from project.x52.normal_press_test.mgr.env_mgr import env_mgr, jenkins_env_mgr


class P4COM2004:
    def __init__(self):
        self.__branch = jenkins_env_mgr.get_branch()
        if  "." in self.__branch:
            version_branch = f"Branchs/version_{self.__branch}" 
        else:
            version_branch = self.__branch
        self.__client = "x52_normal_press_test_{}_{}".format(self.__branch, common.get_host_ip())
        self.p4 = P4Client(
            host=config.P4_2004_CONFIG.get("host"),
            username=config.P4_2004_CONFIG.get("username"),
            password=config.P4_2004_CONFIG.get("password"),
            client=self.__client,
        )
        self.p4.remove_host()
        self.p4.set_line_end(line_end=P4Client.LineEnd.WIN)
        views = [
            f"//H3D_X52_res/QQX5-2_Exe/{version_branch}/resources/config/... //{self.__client}/{self.__branch}/exe/resources/config/...",
            f"//H3D_X52_res/QQX5-2_Exe/{version_branch}/resources/level/... //{self.__client}/{self.__branch}/exe/resources/level/...",            
            f"//H3D_X52_res/QQX5-2_Exe/{version_branch}/bin/bot_config/... //{self.__client}/{self.__branch}/exe/bin/bot_config/...",
            f"//H3D_X52_res/QQX5-2_Exe/{version_branch}/bin/*.sh //{self.__client}/{self.__branch}/exe/bin/*.sh",
        ]
        
        self.p4.set_view(views=views)
        root = os.path.join(env.pipeline.workspace(), self.__branch)
        self.p4.set_root(root)
        self.p4.set_options(allwrite=True)
        self.p4.print_client()

    def get_latest_binary_changelist(self):
        """
        获取资源分支的最新changelist
        """
        if self.__branch == "Vacation":
            ret = self.p4.get_latest_changes("//H3D_X52_res/QQX5-2_Exe/Vacation_binary/...")
        else:
            ret = self.p4.get_latest_changes(f"//H3D_X52_res/QQX5-2_Exe/Branchs/version_{self.__branch}/...")
        try:
            changelist = ret.get("change")
        except Exception as e:
            log.error(e)
            changelist = "head"
        return changelist

    def sync_all(self, changelist: str, force: bool):
        """
        拉取p4
        """
        self.p4.sync_all(changelist=changelist, force=force)


class P4COM2002:
    def __init__(self):
        self.__branch = jenkins_env_mgr.get_branch()    
        if "." in self.__branch:
            self.__actual_path = f"//depot/products/Project_X52_branches/v{self.__branch.replace('.', '_')}"   # 输入为 2.8.5 时，目录为 //depot/products/Project_X52_branches/v2_8_5
        else:
            self.__actual_path = "//depot"
        self.__client = "x52_normal_press_test_{}_{}".format(self.__branch, common.get_host_ip())
        self.p4 = P4Client(
            host=config.P4_2002_CONFIG.get("host"),
            username=config.P4_2002_CONFIG.get("username"),
            password=config.P4_2002_CONFIG.get("password"),
            client=self.__client,
        )
        views = [
            f"{self.__actual_path}/... //{self.__client}/{self.__branch}/depot/...",
            f"{self.__actual_path}/common_lib/... //{self.__client}/{self.__branch}/depot/common_lib/...",
            f"{self.__actual_path}/demos/... //{self.__client}/{self.__branch}/depot/demos/...",
            f"-{self.__actual_path}/platform/... //{self.__client}/{self.__branch}/depot/platform/...",
            f"-{self.__actual_path}/products/... //{self.__client}/{self.__branch}/depot/products/...",
            f"{self.__actual_path}/platform/branches/Project_X52/... //{self.__client}/{self.__branch}/depot/platform/...",
            f"{self.__actual_path}/products/Project_X52/... //{self.__client}/{self.__branch}/depot/products/Project_X52/...",
            f"{self.__actual_path}/third_party/... //{self.__client}/{self.__branch}/depot/third_party/...",
            f"-{self.__actual_path}/minigame_inter/... //{self.__client}/{self.__branch}/depot/minigame_inter/...",
            f"-{self.__actual_path}/special_lib/... //{self.__client}/{self.__branch}/depot/special_lib/...",
            f"-{self.__actual_path}/branches/... //{self.__client}/{self.__branch}/depot/branches/...",
            f"-{self.__actual_path}/demos/... //{self.__client}/{self.__branch}/depot/demos/...",
            f"-{self.__actual_path}/editor/... //{self.__client}/{self.__branch}/depot/editor/...",
            f"-{self.__actual_path}/editor_branches/... //{self.__client}/{self.__branch}/depot/editor_branches/...",
            f"-{self.__actual_path}/products/Project_X52/homeland/... //{self.__client}/{self.__branch}/depot/products/Project_X52/homeland/...",
            f"-{self.__actual_path}/products/Project_X52/homeland_1.0/... //{self.__client}/{self.__branch}/depot/products/Project_X52/homeland_1.0/...",
            f"-{self.__actual_path}/third_party/branches/... //{self.__client}/{self.__branch}/depot/third_party/branches/...",
        ]
        self.p4.set_view(views=views)
        root = os.path.join(env.pipeline.workspace(), self.__branch)
        self.p4.set_root(root)

    def get_code_latest_changes(self):
        """
        获取代码的最新changelist
        """
        _paths = [
            "//depot/platform/branches/Project_X52/...",
        ]
        if "Vacation" == jenkins_env_mgr.get_branch():
            _paths.append("//depot/products/Project_X52/...")
        else:
            _paths.append(f"{self.__actual_path}/...")
        changelist = 0
        for _path in _paths:
            ret = self.p4.get_latest_changes(_path)
            try:
                log.info(ret)
                changelist = max(int(ret.get("change")), int(changelist))
            except Exception as e:
                log.error(e)
                changelist = "head"
        return str(changelist)

    def sync_all(self, changelist, force):
        self.p4.sync_all(changelist=changelist, force=force)


p4_2004 = P4COM2004()
p4_2002 = P4COM2002()