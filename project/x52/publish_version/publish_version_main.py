# coding=utf-8
from frame import *

# from project.x52.publish_version import config

WORKDIR = "/home/<USER>/publish"


# def move_files(**kwargs):
#     """
#     将需要的sh脚本和配置文件移动到指定目录 /home/<USER>
#     """
#     src = "/data/workspace/pyframe-pipeline/project/x52/publish_version/scripts/*"
#     dst = "/home/<USER>/publish"
#     path_mgr.mkdir(dst)
#     cmd.run_shell(
#         cmds=["mv -n {} {}".format(src, dst)],
#         workdir=WORKDIR
#     )


# def pull_nexus(**kwargs):
#     """
#     拉取制品库文件
#     """
#     nexus = Nexus(**config.NEXUS_CONFIG)
#     src = "http://nexus.h3d.com.cn/repository/dev-productivity/test/coscli-linux"
#     dst = "/home/<USER>/publish/coscli-linux"
#     nexus.download(src=src, dst=dst)


@advance.stage(stage="更新ini配置")
def update_ini(**kwargs):
    """
    更新 ini 文件
    """
    # 检查磁盘空间是否满足执行流水线
    advance.check_disk_size(threshold=5)
    # 获取环境变量
    publish_version = env.get("PUBLISH_VERSION")
    src_svr = env.get("SRC_SVR")
    tlog_file = env.get("TLOG_FILE")
    tlog_cppfile = env.get("TLOG_CPPFILE")
    log.info("........update ini......")
    # 执行 set_publish.sh 脚本
    command = f"sh set_publish.sh {publish_version} {src_svr} {tlog_file} {tlog_cppfile}"
    ret = cmd.run_shell(cmds=[command], workdir=WORKDIR)
    if ret[0] != 0:
        log.error(f"{command} failed")
        raise PyframeException(f"{command}执行失败, 请联系管理员")


@advance.stage(stage="判断发布")
def publish_sql(**kwargs):
    """
    判断是否发布 sql 语句
    """
    # 获取环境变量
    is_publish_sql = env.get("IS_PUBLISH_SQL")
    sql_dir = env.get("SQL_DIR")
    publish_version = env.get("PUBLISH_VERSION")
    # 根据变量值判断是否发布sql语句
    if is_publish_sql == "1":
        command = f"sh set_publish_sql.sh {sql_dir}"
        ret = cmd.run_shell(cmds=[command], workdir=WORKDIR)
        if ret[0] != 0:
            log.error(f"{command} failed")
            raise PyframeException(f"{command}执行失败, 请联系管理员")
        command = f"sh publish_sql.sh {publish_version}"
        ret = cmd.run_shell(cmds=[command], workdir=WORKDIR)
        if ret[0] != 0:
            log.error(f"{command} failed")
            raise PyframeException(f"{command}执行失败, 请联系管理员")


@advance.stage(stage="发布歌曲")
def publish_music(**kwargs):
    """
    判断是否发布 歌曲sql
    """
    is_publish_music = env.get("IS_PUBLISH_MUSIC")
    song_ids = env.get("SONG_IDS")
    publish_version = kwargs.get("publish_version")
    if is_publish_music == "1":
        command = "sh set_publish_song.sh {}".format(song_ids)
        log.info(command)
        ret = cmd.run_shell(cmds=[command], workdir=WORKDIR)
        if ret[0] != 0:
            log.error("{} failed".format(command))
            exit(-1)
        command = "sh publish_music.sh {}".format(publish_version)
        log.info(command)
        ret = cmd.run_shell(cmds=[command], workdir=WORKDIR)
        if ret[0] != 0:
            log.error("{} failed".format(command))
            exit(-1)


@advance.stage(stage="发布配置")
def publish_config(**kwargs):
    """
    判断是否发布 配置文件
    """
    is_publish_config = env.get("IS_PUBLISH_CONFIG")
    all_config_path = env.get("ALL_CONFIG_PATH")
    publish_version = env.get("PUBLISH_VERSION")
    if is_publish_config == "1":
        command = "sh set_publish_config.sh {}".format(all_config_path)
        log.info(command)
        ret = cmd.run_shell(cmds=[command], workdir=WORKDIR)
        if ret[0] != 0:
            log.error("{} failed".format(command))
            exit(-1)
        command = "sh publish_config.sh {}".format(publish_version)
        log.info(command)
        ret = cmd.run_shell(cmds=[command], workdir=WORKDIR)
        if ret[0] != 0:
            log.error("{} failed".format(command))
            exit(-1)


@advance.stage(stage="发布tlog")
def publish_tlog(**kwargs):
    """
    判断是否发布 tlog 文件
    """
    is_publish_tlog = env.get("IS_PUBLISH_TLOG")
    publish_version = env.get("PUBLISH_VERSION")
    if is_publish_tlog == "1":
        command = f"sh publish_tlog.sh {publish_version}"
        ret = cmd.run_shell(cmds=[command], workdir=WORKDIR)
        if ret[0] != 0:
            log.error(f"{command} failed")
            raise PyframeException(f"{command}执行失败")


def print_msg(**kwargs):
    """
    打印路径、*.error文件信息
    """
    publish_version = env.get("PUBLISH_VERSION")
    log.info("pwd > /dev/null;  find /home/<USER>/publish/{} -name *.error | xargs cat ;".format(publish_version))
    ret = cmd.run_shell(
        cmds=["pwd > /dev/null;  find /home/<USER>/publish/{} -name *.error | xargs cat ;".format(publish_version)], workdir=WORKDIR, return_output=True
    )
    if ret[1] is not None:
        log.info(ret)


def post_success(**kwargs: dict):
    wechat.send_unicast_post_success()
    advance.insert_pipeline_history_on_success()


def post_failure(**kwargs: dict):
    wechat.send_unicast_post_failure()
    advance.insert_pipeline_history_on_failure()


def post_canceled(**kwargs: dict):
    wechat.send_unicast_post_canceled()
    advance.insert_pipeline_history_on_canceled()
