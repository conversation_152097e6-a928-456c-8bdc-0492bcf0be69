#!/bin/bash


old_dir='/home/<USER>/temp/publish_old.ini'
root_dir='/home/<USER>/publish/'
old_ini="${root_dir}publish_old.ini"
conf_ini="${root_dir}publish.ini"
pulish_version="s/PUBLISH_VERSION=/PUBLISH_VERSION=${PUBLISH_VERSION}/g"
song_id="s/SONG_IDS=/SONG_IDS=${SONG_IDS}/g"
sql="s/SQL_DIR=/SQL_DIR=${SQL_DIR}/g"
config_path="s/ALL_CONFIG_PATH=/ALL_CONFIG_PATH=${ALL_CONFIG_PATH}/g"
tlog_file="s/TLOG_FILE=/TLOG_FILE=${TLOG_FILE}/g"
tlog_cppfile="s/TLOG_CPPFILE=/TLOG_CPPFILE=${TLOG_CPPFILE}/g"
src_server="s/SRC_SERVER=/SRC_SERVER=${SRC_SVR}/g"
config_ini="${root_dir}server_config.ini"






sed -i "s|ALL_CONFIG_PATH=.*|ALL_CONFIG_PATH=\"$1\"|" ${conf_ini};



