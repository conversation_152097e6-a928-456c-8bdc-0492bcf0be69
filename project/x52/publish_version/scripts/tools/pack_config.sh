#!/bin/bash

function f_chk_file()
{
  echo $1
   
  if [ "$1"x != ""x ] && [ ! -f $1 ]; then
    echo "Error! $1 not exist!"
    exit 1
  fi
}

if [ $# -lt 2 ]; then
  echo "./pack_config.sh <version> <pub_dir>"
  exit
fi

VER=$1
DES_DIR=$2

test -z $DES_DIR || mkdir -p $DES_DIR

DATE=`date +%Y%m%d`
FILE_NAME="x52_server_config_op_${VER}_$DATE.tgz" 
PACK_NAME="$DES_DIR$FILE_NAME" 

echo "packing $PACK_NAME"
echo

f_chk_file $3
f_chk_file $4
f_chk_file $5
f_chk_file $6
f_chk_file $7
f_chk_file $8

tar czvf $PACK_NAME $3 $4 $5 $6 $7 $8

echo
echo "md5ing"

cd $DES_DIR
md5sum $FILE_NAME > $PACK_NAME.md5

if [ $? -eq 0 ]; then
  echo "OK!"
else
  echo "Error!"
fi
cd -

cat $PACK_NAME.md5

find $DES_DIR -type f
