#!/bin/bash


path=$1
name="tlog_wrapper.cpp"
nameone="tlog_wrapper.h"
filename=${path}${name}
filenameone=${path}${nameone}
rm -rf Tlog_file1.txt
rm -rf Tlog_file.txt



startnum=0
endnum=0
num=0
COMM_COUNT=0

while read line
do
x=$(($x+1))

if [[ "$line" =~ "TLogLogInfo(\"" ]];then
  startnum=$x
  strtwo=${line%\"*}
  endstr=${strtwo#*\"}
  COMM_COUNT=0
fi

if [[ "$line" =~ "TlogWrite(\"" ]];then
  startnum=$x
  strtwo=${line%\"*}
  endstr=${strtwo#*\"}
  COMM_COUNT=18
fi

if [[ "$line" =~ "TglogWrite(\"" ]];then
  startnum=$x
  strtwo=${line%\"*}
  endstr=${strtwo#*\"}
  COMM_COUNT=22
fi

if [[ "$line" =~ ");" ]];then
  if [ "$startnum" -ne 0 ]
  then

    endnum=$x
    num=$(($endnum-$startnum+$COMM_COUNT))

    tmpstr=${endstr}" "${num}
    echo $tmpstr >> Tlog_file1.txt

    startnum=0
    endnum=0
  fi

fi

done < $filename






startnumone=0
endnumone=0

numone=0
while read lineone
do

y=$(($y+1))

if [[ "$lineone" =~ "<struct" ]];then

startnumone=$y
tmpstrtwo=${lineone#*name=\"}
tmpendstr=${tmpstrtwo%%\"*}

fi

 if [[ "$lineone" =~ "</struct" ]];then
endnumone=$y
numone=$(($endnumone-$startnumone))

tmpstrone=${tmpendstr}" "${numone}

echo $tmpstrone >> Tlog_file.txt

fi

done < $filenameone

rm -f $path/tlog.error

diff_msg=`grep -vwf Tlog_file.txt Tlog_file1.txt`

if ! [[ "$diff_msg" == "" ]]; then
  echo $diff_msg > $path/tlog.error
  echo $diff_msg
fi

echo "end................."
