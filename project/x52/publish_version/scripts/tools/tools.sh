#!/bin/bash

function f_yesno()
{
  while true; do
    read -p "$1(y/n)" yn
    if [ "$yn"x = "y"x ] || [ "$yn"x = "Y"x ]; then
        return 0
    elif [ "$yn"x = "n"x ] || [ "$yn"x = "n"x ]; then
        return 1
    fi
  done
}

function f_replace()
{
    if [ -f $1 ]; then
        if yesno "File $1 exist, replace?" 
        then
           return 0
        else
           return 1
        fi
    fi
    return 0
}

#eval $1 $2
