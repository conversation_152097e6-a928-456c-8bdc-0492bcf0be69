import org.jenkinsci.plugins.workflow.steps.FlowInterruptedException

node {
    def r = sh returnStdout: true, script: 'curl -s --user administrator:Admin123 \'ftp://**************/x52/server/\' | awk \'{print $NF}\''
    def branches = r.trim().readLines().reverse()

    def zone_ids = ['1', '5', '6', '7', '50', '100', '101']

    def MYSQL_NAME = ['X5II_40', 'X5II_41', 'X5II_new_6', 'X5II_new_7', 'X5II_new_50', 'X5II_new_100', 'X5II_new_101', 'x52_test_100', 'x52_new2_1']

    def CROSS_SERVER_IP = '127.0.0.1'

    def REDIS_IP = '**************'

    properties([
//         disableConcurrentBuilds(),
        parameters([
            [$class: 'ChoiceParameter',
                choiceType: 'PT_CHECKBOX',
                description: '选择需要部署的服务器',
                filterLength: 1,
                filterable: false,
                name: 'SERVER_IP',
                script: [
                    $class: 'GroovyScript',
                    script: [
                        sandbox: true,
                        script: '''def nodes = [
                            '************',
                            '************',
                            '*************',
                            '*************',
                            '*************',
                            '*************',
                            '*************',
                            '*************',
                            '*************',
                            '*************',
                            '*************',
                            '*************',
                            '***********',
                            '***********',
                            '*************',
                            '**************',
                            '**************'
                        ]
                        return nodes
                        '''
                    ]
                ]
            ],
            choice(choices: MYSQL_NAME, name: 'MYSQL_NAME'),
            choice(choices: zone_ids, name: 'ZONE_ID'),
            [$class: 'DynamicReferenceParameter',
                choiceType: 'ET_FORMATTED_HTML',
                name: 'WORLD_ID',
                omitValueField: true,
                referencedParameters: 'ZONE_ID',
                script: [
                    $class: 'GroovyScript',
                    script: [
                        sandbox: false,
                        script: '''def map = [\'1\': 1101, \'50\': 2201, \'100\': 3101, \'101\': 3102, \'5\': 3103, \'6\': 3104, \'7\': 3105]

return "<input name=\'value\' value=\'${map[ZONE_ID]}\' class=\'setting-input\' type=\'text\'>"'''
                    ]
                ]
            ],
            choice(choices: branches, name: 'BRANCH'),
            string(defaultValue: 'head', name: 'P4_VERSION', trim: true),
            booleanParam(defaultValue: true, name: 'FORCE_UPDATE'),
            hidden(defaultValue: CROSS_SERVER_IP, name: 'CROSS_SERVER_IP'),
            hidden(defaultValue: REDIS_IP, name: 'REDIS_IP')
        ])
    ])

    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }

    def export_lc = 'export LC_ALL=en_US.utf-8 && export LANG=en_US.utf-8'

    def labels = params.SERVER_IP.split(',')
    def builders = [:]
    for (x in labels) {
        def label = x.trim()

        builders[label] = {
          node(label) {
            try {
                stage('拉取脚本') {
                  sh encoding: 'UTF-8', script: '''
                    branch="master"

                    export GIT_SSL_NO_VERIFY=true
                    if [ ! -d "pyframe-pipeline/.git" ]; then
                        git clone -b $branch https://gitlab.h3d.com.cn/dev-productivity/pyframe-pipeline.git
                        cd pyframe-pipeline
                        git config --local user.name "<EMAIL>"
                        git config --local user.email "<EMAIL>"
                    else
                        cd pyframe-pipeline
                        git config --local user.name "<EMAIL>"
                        git config --local user.email "<EMAIL>"
                        git config pull.rebase false
                        git reset --hard
                        git clean -xdf -e logs
                        local_branch="$(git branch --show-current)"
                        if [ "$branch" = "$local_branch" ];then
                            git pull
                        else
                            git fetch
                            git checkout --track origin/$branch
                            git pull
                        fi
                    fi

                    python3 -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                    python3 -m pip install -r requirements.txt -i  http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                  '''
                }
                stage('准备') {
                  dir('pyframe-pipeline') {
                    sh encoding: 'UTF-8', script: "$export_lc && python3 x52.py deploy_private_server --job=prepare"
                  }
                }
                stage('拉取p4资源') {
                  dir('pyframe-pipeline') {
                    sh encoding: 'UTF-8', script: "$export_lc && python3 x52.py deploy_private_server --job=sync_p4"
                  }
                }
                stage('获取linux_bin') {
                  dir('pyframe-pipeline') {
                    sh encoding: 'UTF-8', script: "$export_lc && python3 x52.py deploy_private_server --job=download_linux_bin"
                  }
                }
                stage('解压linux_bin') {
                  dir('pyframe-pipeline') {
                    sh encoding: 'UTF-8', script: "$export_lc && python3 x52.py deploy_private_server --job=decompress_linux_bin"
                    sh encoding: 'UTF-8', script: "$export_lc && python3 x52.py deploy_private_server --job=add_exec_perm_linux_bin"
                  }
                }
                stage('修改配置') {
                  dir('pyframe-pipeline') {
                    sh encoding: 'UTF-8', script: "$export_lc && python3 x52.py deploy_private_server --job=modify_config"
                  }
                }
                stage('启动服务器') {
                  dir('pyframe-pipeline') {
                    sh encoding: 'UTF-8', script: "$export_lc && python3 x52.py deploy_private_server --job=start_server"
                  }
                }
                stage('发送通知') {
                  dir('pyframe-pipeline') {
                    sh encoding: 'UTF-8', script: "$export_lc && python3 x52.py deploy_private_server --job=on_success"
                  }
                }
            } catch(FlowInterruptedException e) {
                stage('发送通知') {
                  dir('pyframe-pipeline') {
                    sh encoding: 'UTF-8', script: "$export_lc && python3 x52.py deploy_private_server --job=on_canceled"
                  }
                }
            } catch (e) {
                stage('发送通知') {
                  dir('pyframe-pipeline') {
                    sh encoding: 'UTF-8', script: "$export_lc && python3 x52.py deploy_private_server --job=on_failure"
                  }
                }
            }
          }
        }
    }

    parallel builders
}
