# coding=utf-8
import os
from frame import PyframeException, advance, cmd, common, env, log, path_mgr, proc_mgr, wechat
from project.x52.test.deploy_private_server.mgr.config_mgr import ConfigMgr
from project.x52.test.deploy_private_server.mgr.const import SERVER_USER_MAP
from project.x52.test.deploy_private_server.mgr.env_mgr import (
    env_mgr,
    jenkins_env_mgr,
)
from project.x52.test.deploy_private_server.mgr.linux_bin_mgr import LinuxBinMgr
from project.x52.test.deploy_private_server.mgr.p4_com_2004 import P4COM2004

PATH_BIN11 = "/data/home/<USER>/exe/bin11/"


@advance.stage(stage="准备")
def prepare(**kwargs):
    # 停止服务
    proc_mgr.kill_procs(names=["svc_launch"])
    # 挂载目录
    path_mgr.mkdir("/data/mockp4/x52/")
    ret = cmd.run_shell(cmds=["mount.cifs -o username=age,password=111,dir_mode=0777,file_mode=0777 //172.17.100.6/release/x52 /data/mockp4/x52"])
    if ret[0] != 0:
        log.error("mount.cifs failed ", str(ret))
        raise PyframeException("挂载共享目录失败, 请联系管理员")

    path_mgr.mkdir(PATH_BIN11)
    path_mgr.mkdir("/data/home/<USER>/exe/resources/config/")
    path_mgr.mkdir("/data/home/<USER>/exe/resources/level/")
    # 处理分支
    branch = jenkins_env_mgr.get_branch()
    if branch.count(".") == 2:
        version_branch = f"version_{branch}"
        env_mgr.set_version_branch(version_branch=version_branch)
    elif branch == "trunc":
        version_branch = "Vacation"
        env_mgr.set_version_branch(version_branch=version_branch)
    else:
        log.error("invalid BRANCH")
        raise PyframeException(f"错误的分支: {branch}")


@advance.stage(stage="拉取p4")
def sync_p4(**kwargs):
    version_branch = env_mgr.get_version_branch()
    p4_version = jenkins_env_mgr.get_p4_version()
    force_update = jenkins_env_mgr.get_force_update()

    p4_2004 = P4COM2004(branch=version_branch)
    changelist = p4_version.strip().lower()
    if changelist == "head":
        changelist = p4_2004.get_latest_changes()
    env_mgr.set_changelist(changelist=changelist)
    log.info("获取 changelist并保存 = {}".format(changelist))
    # 同步p4
    p4_2004.sync_all(changelist=changelist, force=force_update)
    # 新增替换测试自定义脚本
    p4_2004.replace_cus_scripts()


@advance.stage(stage="下载linux_bin")
def download_linux_bin(**kwargs):
    branch = jenkins_env_mgr.get_branch()
    changelist = env_mgr.get_changelist()
    if changelist is None or changelist.isdigit() is False:
        log.error(f"changelist 未被正确加载 {changelist} 请重新运行流水线")
        raise PyframeException(f"changelist 未被正确加载: {changelist} 请重新运行流水线")
    linux_bin_mgr = LinuxBinMgr(branch=branch, changelist=changelist)
    linux_bin_mgr.download_linux_bin()


@advance.stage(stage="解压linux_bin")
def decompress_linux_bin(**kwargs):
    branch = jenkins_env_mgr.get_branch()
    changelist = env_mgr.get_changelist()
    linux_bin_mgr = LinuxBinMgr(branch=branch, changelist=changelist)
    linux_bin_mgr.decompress_linux_bin(PATH_BIN11)


@advance.stage(stage="添加执行权限linux_bin")
def add_exec_perm_linux_bin(**kwargs):
    branch = jenkins_env_mgr.get_branch()
    changelist = env_mgr.get_changelist()
    linux_bin_mgr = LinuxBinMgr(branch=branch, changelist=changelist)
    linux_bin_mgr.chmod(PATH_BIN11, mode='+x')



@advance.stage(stage="修改配置")
def modify_config(**kwargs):
    # server_ip = env.get("SERVER_IP")
    server_ip = common.get_host_ip()
    # jenkins_cross_server_ip = env.get("CROSS_SERVER_IP")
    # redis_ip = env.get("REDIS_IP")
    # zone_id = env.get("ZONE_ID")
    # world_id = env.get("WORLD_ID")
    # mysql_name = env.get("MYSQL_NAME")

    jenkins_cross_server_ip = jenkins_env_mgr.get_cross_server_ip()
    redis_ip = jenkins_env_mgr.get_redis_ip()
    zone_id = jenkins_env_mgr.get_zone_id()
    world_id = jenkins_env_mgr.get_world_id()
    mysql_name = jenkins_env_mgr.get_mysql_name()

    if jenkins_cross_server_ip == "127.0.0.1":
        jenkins_cross_server_ip = server_ip
        # env.set({f"CROSS_SERVER_IP_{server_ip.replace('.', '')}": jenkins_cross_server_ip})
        env_mgr.set_cross_server_ip(server_ip=server_ip, cross_server_ip=jenkins_cross_server_ip)

    # 200.77和100.41特殊处理
    if server_ip == "*************":
        mysql_name = "x52_test_100"
        redis_ip = "**************"
        zone_id = "100"
        world_id = "3101"
    # elif server_ip == "*************":
    elif server_ip == "************":
        mysql_name = "x52_test_50"
        redis_ip = "**************"
        zone_id = "50"
        world_id = "2201"
    else:
        pass

    if None in [
        server_ip,
        jenkins_cross_server_ip,
        redis_ip,
        zone_id,
        world_id,
        mysql_name,
    ]:
        log.error("exist None params")
        raise PyframeException(f"错误的参数")
    config_mgr = ConfigMgr()
    config_mgr.modify_config(
        server_ip=server_ip,
        cross_server_ip=jenkins_cross_server_ip,
        redis_ip=redis_ip,
        zone_id=zone_id,
        world_id=world_id,
        mysql_name=mysql_name,
    )
    config_mgr.generate_server_test()


# def replace_prize_config(**kwargs):
#     """
#     修改大奖池配置
#     """
#     pass


@advance.stage(stage="启动服务器")
def start_server(**kwargs):
    ret = cmd.run_shell(cmds=["dos2unix *.sh"], workdir=PATH_BIN11)
    if ret[0] != 0:
        log.error("dos2unix failed")
        raise PyframeException("dos2unix失败, 请联系管理员")

    ret = cmd.run_shell(
        cmds=["sh start_allserver.sh"],
        workdir=PATH_BIN11,
        dot_kill_me=True,
    )
    if ret[0] != 0:
        log.error("sh start_allserver.sh failed")
        raise PyframeException("启动all server失败")

    ret = cmd.run_shell(
        cmds=["sh start_global_server.sh"],
        workdir=PATH_BIN11,
        dot_kill_me=True,
    )
    if ret[0] != 0:
        log.error("sh start_global_server.sh failed")
        raise PyframeException("启动global server失败")


def __get_detail_msg() -> str:
    # server_ip = env.get("SERVER_IP")
    server_ip = common.get_host_ip()
    # cross_server_ip = env.get(f"CROSS_SERVER_IP_{server_ip.replace('.', '')}")
    # redis_ip = env.get("REDIS_IP")
    # mysql_name = env.get("MYSQL_NAME")
    # zone_id = env.get("ZONE_ID")
    # world_id = env.get("WORLD_ID")
    # branch = env.get("BRANCH")
    # changelist = env.get("changelist")
    # force_update = env.get("FORCE_UPDATE")

    cross_server_ip = env_mgr.get_cross_server_ip(server_ip=server_ip)
    redis_ip = jenkins_env_mgr.get_redis_ip()
    mysql_name = jenkins_env_mgr.get_mysql_name()
    zone_id = jenkins_env_mgr.get_zone_id()
    world_id = jenkins_env_mgr.get_world_id()
    branch = jenkins_env_mgr.get_branch()
    changelist = env_mgr.get_changelist()
    force_update = jenkins_env_mgr.get_force_update()

    msg = (
        f"**服务器**: {server_ip}\n"
        f"**跨服**: {cross_server_ip}\n"
        f"**redis_ip**: {redis_ip}\n"
        f"**mysql_name**: {mysql_name}\n"
        f"**zone_id**: {zone_id}\n"
        f"**world_id**: {world_id}\n"
        f"**分支**: {branch}\n"
        f"**changelist**: {changelist}\n"
        f"**强更p4**: {force_update}"
    )
    return msg


def __get_user_list() -> list:
    user_list = []
    server_ip = common.get_host_ip()
    user_map_file = PATH_BIN11 + "machine_users.txt"
    if os.path.exists(user_map_file) == False:
        log.warning(f"{user_map_file} does not exist. Using default user list")
    else:
        try:
            with open(user_map_file, "r", encoding="utf-8") as f:
                for line in f:
                    lines = line.strip().split(":")
                    if len(lines) != 2:
                        continue
                    # log.info(f"读取ip及用户： {lines}")
                    if lines[0] == server_ip:
                        if lines[1] not in user_list:
                            log.info(f"添加 {server_ip} 使用用户： {lines[1]}")
                            user_list.append(lines[1])
        except Exception as e:
            log.warning(f"read {user_map_file} error: {e}")
    # server_user = SERVER_USER_MAP.get(server_ip)
    # if server_user:
    #     if server_user not in user_list:
    #         user_list.append(server_user)
    user_email = env.get("BUILD_USER_EMAIL")
    if user_email:
        if user_email not in user_list:
            log.info(f"添加触发流水线用户： {user_email}")
            user_list.append(user_email)
    return user_list


def on_success(**kwargs):
    """
    成功时的操作
    """

    user_list = __get_user_list()
    wechat.send_unicast_post_success(content=__get_detail_msg(), user_list=user_list)
    cmd.run_shell(cmds=["umount /data/mockp4/x52"])
    try:
        advance.insert_pipeline_history_on_success()
    except Exception as e:
        log.info("insert_pipeline_history_on_success failed: " + repr(e))


def on_failure(**kwargs):
    """
    失败时的操作
    """
    user_list = __get_user_list()
    wechat.send_unicast_post_failure(content=__get_detail_msg(), user_list=user_list)
    cmd.run_shell(cmds=["umount /data/mockp4/x52"])
    try:
        advance.insert_pipeline_history_on_failure()
    except Exception as e:
        log.info("insert_pipeline_history_on_failure failed: " + repr(e))


def on_canceled(**kwargs):
    """
    取消时的操作
    """
    user_list = __get_user_list()
    wechat.send_unicast_post_canceled(content=__get_detail_msg(), user_list=user_list)
    cmd.run_shell(cmds=["umount /data/mockp4/x52"])
    try:    
        advance.insert_pipeline_history_on_canceled()
    except Exception as e:
        log.info("insert_pipeline_history_on_canceled failed: " + repr(e))
