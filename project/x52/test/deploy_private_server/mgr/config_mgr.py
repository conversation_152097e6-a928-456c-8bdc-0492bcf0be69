# coding=utf-8
import pathlib
import re
import xml.etree.ElementTree as ET

from frame import *


class ConfigMgr:
    def __init__(self):
        self.base_path = "/data/home/<USER>/exe"

    def modify_config(self, server_ip: str, cross_server_ip: str, redis_ip: str, zone_id: str, world_id: str, mysql_name: str):
        self.__modify_server_globals(server_ip, cross_server_ip, redis_ip, zone_id, world_id)
        self.__modify_global_server(cross_server_ip)
        self.__modify_mysql_config(mysql_name)
        self.__modify_mysql_config_global(mysql_name)
        self.__modify_gateway()
        self.__modify_micro_service_center(server_ip)
        self.__modify_ptlogin_verify_type(verify_type="0")
        # self.__modify_common_config() # 新QA取消修改物品配置文件校验开关

    def generate_server_test(self):
        log.info("generate_server_test()")
        server_test_path = os.path.join(self.base_path, "resources/config/server/op/server_test.xml")
        p = pathlib.Path(server_test_path)
        p.write_text(
            data="""
<?xml version="1.0" encoding="UTF-8"?>
<config>
	<use_itc>false</use_itc>
</config>"""
        )
        if not os.path.exists(server_test_path):
            log.error(f"generate_server_test() 创建test文件失败 fail: {server_test_path}")
            raise Exception("generate_server_test() 创建test文件失败 fail: {}".format(server_test_path))
        else:
            log.info(f"generate_server_test() 创建test文件成功 success: {server_test_path}")

    def get_zone_world_id(self):
        """
        server\op\area_id_map.xml
        """
        log.info("get_zone_world_id()")
        path = os.path.join(self.base_path, "resources/config/server/op/area_id_map.xml")
        if not os.path.exists(path):
            log.error(f"get_zone_world_id() 获取area_id_map.xml文件失败 fail: {path}")
            raise Exception("get_zone_world_id() 获取area_id_map.xml文件失败 fail: {}".format(path))
        else:
            log.info(f"get_zone_world_id() 获取area_id_map.xml文件成功 success: {path}")
        tree = ET.parse(path)
        root = tree.getroot()
        map_infos = root.find("map_infos")
        items = map_infos.findall("item")
        zone_world_dict = {}
        for item in items:
            zone_world_dict[item.get("zone_id")] = item.get("area_id")
        return zone_world_dict

    def __modify_server_globals(self, server_ip: str, cross_server_ip: str, redis_ip: str, zone_id: str, world_id: str):
        """
        修改resources\config\server\server_globals.xml
        """
        log.info("__modify_server_globals()")
        path = os.path.join(self.base_path, "resources/config/server/server_globals.xml")
        if not os.path.exists(path):
            log.error(f"__modify_server_globals() 获取server_globals.xml文件失败 fail: {path}")
            raise Exception("__modify_server_globals() 获取server_globals.xml文件失败 fail: {}".format(path))
        else:
            log.info(f"__modify_server_globals() 获取server_globals.xml文件成功 success: {path}")
        tree = ET.parse(path)
        root = tree.getroot()
        # 修改Gateway
        gateway = root.findall("Gateway/Server")
        for server in gateway:
            if server.get("ID") == "1":
                server.set("INNER_IP", server_ip)

        # 修改CrossAreaTransmit
        cross_area_transmit = root.findall("CrossAreaTransmit/Server")
        for server in cross_area_transmit:
            if server.get("ID") == "1":
                server.set("INNER_IP", cross_server_ip)

        # 修改RedisServer
        redis_server = root.findall("RedisServer/Server")
        for server in redis_server:
            if server.get("ID") == "1":
                server.set("ip", redis_ip)
                server.set("ip_d", redis_ip)

        # 修改zone_id
        zone_id_node = root.find("zone_id")
        zone_id_node.text = zone_id

        # 修改world_id
        world_id_node = root.find("world_id")
        world_id_node.text = world_id
        tree.write(path)

    def __modify_global_server(self, cross_server_ip: str):
        log.info("__modify_global_server()")
        path = os.path.join(self.base_path, "resources/config/server/op/global_server.xml")
        if not os.path.exists(path):
            log.error(f"__modify_global_server() 获取global_server.xml文件失败 fail: {path}")
            raise Exception("__modify_global_server() 获取global_server.xml文件失败 fail: {}".format(path))
        else:
            log.info(f"__modify_global_server() 获取global_server.xml文件成功 success: {path}")
        tree = ET.parse(path)
        root = tree.getroot()
        # 修改Gateway
        gateway = root.find("Gateway")
        servers = gateway.findall("Server")
        for server in servers:
            if server.get("ID") == "201":
                gateway.remove(server)
            else:
                server.set("INNER_IP", cross_server_ip)

        # 修改transmit
        transmit = root.findall("Transmit/Server")
        for server in transmit:
            if server.get("ID") == "1":
                server.set("INNER_IP", cross_server_ip)
        tree.write(path)

    def __modify_mysql_config(self, mysql_name: str):
        log.info("__modify_mysql_config()")
        path = os.path.join(self.base_path, "resources/config/server/op/mysql_config_test.xml")
        if not os.path.exists(path):
            log.error(f"__modify_mysql_config() 获取mysql_config_test.xml文件失败 fail: {path}")
            raise Exception("__modify_mysql_config() 获取mysql_config_test.xml文件失败 fail: {}".format(path))
        else:
            log.info(f"__modify_mysql_config() 获取mysql_config_test.xml文件成功 success: {path}")
        tree = ET.parse(path)
        root = tree.getroot()
        # 修改Gateway
        mysql = root.findall("mysql/db_list/db")
        for db in mysql:
            if db.get("dbname") == "x52":
                db.set("dbname", mysql_name)
        path = os.path.join(self.base_path, "resources/config/server/op/mysql_config.xml")
        tree.write(path)

    def __modify_mysql_config_global(self, mysql_name: str):
        log.info("__modify_mysql_config_global()")
        path = os.path.join(self.base_path, "resources/config/server/op/mysql_config_global.xml")
        if not os.path.exists(path):
            log.error(f"__modify_mysql_config_global() 获取mysql_config_global.xml文件失败 fail: {path}")
            raise Exception("__modify_mysql_config_global() 获取mysql_config_global.xml文件失败 fail: {}".format(path))
        else:
            log.info(f"__modify_mysql_config_global() 获取mysql_config_global.xml文件成功 success: {path}")
        tree = ET.parse(path)
        root = tree.getroot()
        # 修改Gateway
        mysql = root.findall("mysql/db_list/db")
        for db in mysql:
            if db.get("dbname") == "x52":
                db.set("dbname", mysql_name)
        tree.write(path)

    def __modify_gateway(self):
        log.info("__modify_gateway()")
        path = os.path.join(self.base_path, "resources/config/server/op/gateway.xml")
        if not os.path.exists(path):
            log.error(f"__modify_gateway() 获取gateway.xml文件失败 fail: {path}")
            raise Exception("__modify_gateway() 获取gateway.xml文件失败 fail: {}".format(path))
        else:
            log.info(f"__modify_gateway() 获取gateway.xml文件成功 success: {path}")
        tree = ET.parse(path)
        root = tree.getroot()
        # 修改auth_enable
        auth_enable = root.find("auth_enable")
        auth_enable.text = "false"
        # 修改checksum_enable
        checksum_enable = root.find("checksum_enable")
        checksum_enable.text = "false"
        # 修改crypt_enable
        crypt_enable = root.find("crypt_enable")
        crypt_enable.text = "false"
        # 修改start_cloud_enable
        start_cloud_enable = root.find("start_cloud_enable")
        start_cloud_enable.text = "false"
        tree.write(path)

    def __modify_common_config(self):
        log.info("__modify_common_config()")
        path = os.path.join(self.base_path, "resources/config/server/bonus/common_config.xml")
        f = open(path, "r+", encoding="utf-8")
        lines = f.readlines()  # .decode("gb2312")
        f.close()

        f = open(path, "w+", encoding="utf-8")
        for line in lines:
            res = re.sub('desc="物品配置文件校验开关" value="1"', 'desc="物品配置文件校验开关" value="0"', line)
            f.writelines(res)
        f.close()

    def __modify_micro_service_center(self, server_ip: str):
        log.info("__modify_micro_service_center()")
        path = os.path.join(self.base_path, "resources/config/server/op/micro_service_center.xml")
        if not os.path.exists(path):
            log.error(f"__modify_micro_service_center() 获取micro_service_center.xml文件失败 fail: {path}")
            raise Exception("__modify_micro_service_center() 获取micro_service_center.xml文件失败 fail: {}".format(path))
        else:
            log.info(f"__modify_micro_service_center() 获取micro_service_center.xml文件成功 success: {path}")
        tree = ET.parse(path)
        root = tree.getroot()
        outernodes = root.findall("center/processes/process/spawn/outer")
        update_count = 0
        for server in outernodes:
            if server.get("ip") == "*************":
                server.set("ip", server_ip)
                update_count = update_count + 1
                log.info(f"修改节点 center-processes-process-spawn-outer: ************* 为 {server_ip}")
        if update_count == 0:
            raise Exception("未找到需要修改的节点 - *************")
        tree.write(path)


    def __modify_ptlogin_verify_type(self, verify_type: str = "0"):
        log.info("__modify_ptlogin_verify_type()")
        path = os.path.join(self.base_path, "resources/config/server/tencent/ptlogin.xml")
        if not os.path.exists(path):
            log.error(f"__modify_ptlogin_verify_type() 获取ptlogin.xml文件失败 fail: {path}")
            raise Exception("__modify_ptlogin_verify_type() 获取ptlogin.xml文件失败 fail: {}".format(path))
        else:
            log.info(f"__modify_ptlogin_verify_type() 获取ptlogin.xml文件成功 success: {path}")
        tree = ET.parse(path)
        root = tree.getroot()
        outernodes = root.findall("verify_type")
        update_count = 0
        for verify_type_node in outernodes:
            if verify_type_node.text is not None:
                log.info(f"修改节点 verify_type: {verify_type_node.text} 为 {verify_type}")
                verify_type_node.text = verify_type
                update_count += 1 
        if update_count == 0:
            raise Exception("未找到需要修改的节点 - verify_type")
        tree.write(path)
