# coding=utf-8
from frame import *
from project.x52 import config


class P4COM2004:
    def __init__(self, branch: str):
        self.__branch = branch
        self.__client = "x52_deploy_server_{}_{}".format(self.__branch, common.get_host_ip())
        self.p4 = P4Client(
            host=config.P4_2004_CONFIG.get("host"),
            username=config.P4_2004_CONFIG.get("username"),
            password=config.P4_2004_CONFIG.get("password"),
            client=self.__client,
        )
        self.p4.remove_host()
        self.p4.set_line_end(line_end=P4Client.LineEnd.WIN)
        client = self.__client
        if branch == "Vacation":
            views = [
                f"//H3D_X52_res/QQX5-2_Exe/Vacation/resources/config/... //{client}/exe/resources/config/...",
                f"//H3D_X52_res/QQX5-2_Exe/Vacation/resources/level/... //{client}/exe/resources/level/...",
                f"//H3D_X52_res/QQX5-2_Exe/Vacation/bin/%%1.sh //{client}/exe/bin11/%%1.sh",
            ]
        else:
            views = [
                f"//H3D_X52_res/QQX5-2_Exe/Branchs/{branch}/resources/config/... //{client}/exe/resources/config/...",
                f"//H3D_X52_res/QQX5-2_Exe/Branchs/{branch}/resources/level/... //{client}/exe/resources/level/...",
                f"//H3D_X52_res/QQX5-2_Exe/Branchs/{branch}/bin/%%1.sh //{client}/exe/bin11/%%1.sh",
            ]
        self.p4.set_view(views=views)
        root = "/data/home/<USER>/"
        self.p4.set_root(root)
        self.p4.set_options(allwrite=False)
        self.p4.print_client()

    def get_latest_changes(self):
        """
        获取资源分支的最新changelist
        """
        if self.__branch == "Vacation":
            ret = self.p4.get_latest_changes("//H3D_X52_res/QQX5-2_Exe/Vacation_binary/...")
        else:
            ret = self.p4.get_latest_changes("//H3D_X52_res/QQX5-2_Exe/branches_binary/{}_binary/...".format(self.__branch))
        try:
            changelist = ret.get("change")
        except Exception as e:
            log.error(e)
            changelist = "head"
        return changelist

    def sync_all(self, changelist: str, force: bool):
        """
        拉取p4
        """
        self.p4.sync_all(changelist=changelist, force=force)
