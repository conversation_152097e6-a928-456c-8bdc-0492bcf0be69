pipeline {
  agent {
    node {
      label 'win10_192.168.12.240' 
      customWorkspace 'D:/AutoTest/x52_autotest_performance'
    }
  }
  parameters {
    string(name: 'branch', defaultValue: 'trunc', description: '''trunc: 更新主支代码\n vx_x_x:更新分支代码''')
    string(name: 'res_branch', defaultValue: 'trunc', description: '''trunc: 更新主支资源\n version_x.x.x_binary:更新分支资源''')
    string(name: 'version', defaultValue: '#', description: '''#: 默认更新代码至最新\n 填写版本号讲更新至此版本''')
    choice(name: 'choice_bin', choices: ['final_bin', 'bin'], description: '''选择编译成final_bin还是bin''')
    booleanParam(name: 'get_code', defaultValue: true, description: '''是否获取代码和配置''')
  }
  stages {
    stage('clean-previous-build') {
      steps {
        script{
        //删除所有文件
           def a = bat (returnStatus: true, script:  'del /s /q /f depot exe inter')
           def b = bat (returnStatus: true, script:  'del /q /f *.zip')
           def c = bat (returnStatus: true, script:  'rd /q /s depot exe inter')
        }
      }
    }
     stage('reorg-code-structure') {
      steps {
        script{
        //重整代码结构
           def d = bat (returnStatus: true, script:  'robocopy pyframe-pipeline/project/x52/test/x52_performance_robot_build %WORKSPACE% /e')
        }
      }
    }
    stage('get-code') {
       when {
        expression {
          return (params.get_code == true)
        }
       }
      steps {
        //解压bot.zip
        bat 'python unzip_bot.py'
        //获取代码
        bat 'python getcode.py %branch% %version% %choice_bin% %res_branch%'
        //去除只读
        bat """attrib -r /s %WORKSPACE%/depot/third_party/*.*
          attrib -r %WORKSPACE%/exe/${choice_bin}/*.*
          attrib -r %WORKSPACE%/exe/${choice_bin}/BenchMark/*.*"""
      }
    }
    stage('build-robot') {
      steps {
        //IncrediBuild编译机器人
          script {
            if ("${choice_bin}" == 'final_bin') {
                c_bin = 'final_release'
            } else {
                c_bin = 'Release'
            }
          }
                bat(returnStatus: true, script: """BuildConsole.exe  %WORKSPACE%/depot/products/Project_X52/_for_bot/bot_vs2010.sln /CFG="${c_bin}|Mixed Platforms" /BUILD /MAXCPUS=40""")
                //         //若IncrediBuild编译失败，使用VS编译器继续编译
                //         bat """cd %WORKSPACE%/depot/products/Project_X52/_for_bot
                // E:\\\\"Program Files (x86)"\\\\"Microsoft Visual Studio"\\\\2017\\\\Community\\\\MSBuild\\\\15.0\\\\Bin\\\\MSBuild.exe bot_vs2010.sln /t:Build /p:Configuration=${c_bin} /p:Platform="Mixed Platforms" /m"""
                //讲编译好的库拷贝到待打包区域
                bat """cd %WORKSPACE%
                  del bot*.zip
                  robocopy copy_robot %WORKSPACE%/exe/${choice_bin}/copy_robot /e
                  cd %WORKSPACE%/exe/${choice_bin}/copy_robot
                  .\\copy_binary_to_robot.bat
                  exit 0"""
      }
    }
    //压缩，打包机器人
    stage('pack-robot') {
      steps {
          script{
              def DATE_TAG = java.time.LocalDate.now()
              def name = "bot_${branch}_${DATE_TAG}.zip"
              // zip zipFile: name, archive:false, dir: "${WORKSPACE}\\exe\\${choice_bin}\\copy_robot\\bot"
              bat 'python unzip_bot.py %WORKSPACE%\\exe\\%choice_bin%\\copy_robot\\bot %branch%'
              archiveArtifacts artifacts: name, fingerprint: true
            }
      }
    }
  }
}