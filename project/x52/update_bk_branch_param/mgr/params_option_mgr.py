import re

from frame import P4Client
from project.x52 import config


class ParamsMgr:
    @staticmethod
    def get_options_from_p4():
        # 初始化P4客户端
        p4_client = P4Client(
            host=config.P4_2004_CONFIG.get("host"),
            username=config.P4_2004_CONFIG.get("username"),
            password=config.P4_2004_CONFIG.get("password"),
            client="192_168_6_150",
        )

        branches = p4_client.dirs(f"//H3D_X52_res/QQX5-2_Exe/Branchs/*")

        # 对分支目录进行排序
        options_branches = []
        pattern = r"^\/\/H3D_X52_res\/QQX5-2_Exe\/Branchs\/version_\d+.\d+.\d+$"
        for branch in branches:
            if re.findall(pattern, branch):
                options_branches.append(branch)

        options_branches = sorted(options_branches, reverse=True)
        options_branches.insert(0, "Vacation_binary")

        # 组织流水线锁需要的参数
        options = []
        for options_branch in options_branches[:20]:
            temp = [re.sub(r"^(\w+)_([\d.]+)$", r"branches_binary/\1_\2_binary", t) for t in options_branch.split("/")[-1:]]
            item = {"key": temp[0], "value": temp[0]}
            options.append(item)
        return options
