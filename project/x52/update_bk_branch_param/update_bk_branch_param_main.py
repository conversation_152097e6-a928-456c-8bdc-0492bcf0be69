from frame import wechat
from frame.advance.advance import advance
from frame.log.log import log
from project.x52.update_bk_branch_param.mgr.bk_class_mgr import BaseMetaClass


@advance.stage(stage="更新参数")
def update_params(**kwargs):
    for cls in BaseMetaClass.record_cls:
        try:
            cls().start_update()
        except Exception as e:
            log.error(e)


def on_success(**kwargs):
    wechat.send_unicast_post_success()
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure()
    advance.insert_pipeline_history_on_failure()


def on_cancel(**kwargs):
    wechat.send_unicast_post_canceled()
    advance.insert_pipeline_history_on_canceled()
