# coding=utf-8
from frame import *
from project.x52.update_client_version import config


class P42002:
    def __init__(self, branch: str):
        self.__client = "update_client_versions_test_{}".format(common.get_host_ip())
        self.__branch = branch
        self.p4 = P4Client(
            host=config.P4_2002_CONFIG.get("host"),
            username=config.P4_2002_CONFIG.get("username"),
            password=config.P4_2002_CONFIG.get("password"),
            client=self.__client,
        )
        v_branch = "v{}".format(branch.replace(".", "_"))
        self.__v_branch = v_branch
        if branch == "trunc":
            views = [
                f"//depot/products/Project_X52/... //{self.__client}/trunc/Project_X52/...",
                f"//depot/platform/branches/... //{self.__client}/trunc/branches/...",
            ]
        else:
            views = [f"//depot/products/Project_X52_branches/{v_branch}/... //{self.__client}/{v_branch}/..."]
        self.p4.set_view(views=views)
        root = "/data/workspace/update_client_version/p4/"
        self.p4.set_root(root)

        if self.__branch == "trunc":
            self.__filelist = [
                "//depot/products/Project_X52/svc/cli.rc",
                "//depot/products/Project_X52/ui_launcher/ui_launcher.rc",
                "//depot/products/Project_X52/gameround/projects/client_gameround_rc.rc",
                "//depot/products/Project_X52/demo_platform/Demo_platform.rc",
                "//depot/platform/branches/Project_X52/client_frame/client_front/client_front.rc",
                "//depot/platform/branches/Project_X52/client_frame/launch/launch.rc",
                "//depot/products/Project_X52/qqmusiclite/qqmusiclite.rc",
            ]
        else:
            self.__filelist = [
                f"//depot/products/Project_X52_branches/{self.__v_branch}/products/Project_X52/svc/cli.rc",
                f"//depot/products/Project_X52_branches/{self.__v_branch}/products/Project_X52/ui_launcher/ui_launcher.rc",
                f"//depot/products/Project_X52_branches/{self.__v_branch}/products/Project_X52/gameround/projects/client_gameround_rc.rc",
                f"//depot/products/Project_X52_branches/{self.__v_branch}/products/Project_X52/demo_platform/Demo_platform.rc",
                f"//depot/products/Project_X52_branches/{self.__v_branch}/platform/branches/Project_X52/client_frame/client_front/client_front.rc",
                f"//depot/products/Project_X52_branches/{self.__v_branch}/platform/branches/Project_X52/client_frame/launch/launch.rc",
                f"//depot/products/Project_X52_branches/{self.__v_branch}/products/Project_X52/qqmusiclite/qqmusiclite.rc",
            ]

    def sync_all(self):
        # self.p4.sync_all(changelist=changelist, force=force)
        changelist = "head"
        for file in self.__filelist:
            self.p4.sync(path=file, changelist=changelist, force=True)

    def submit(self):
        self.p4.fast_submit(files=self.__filelist, desc="xw000000##skipcodecheck## {}".format(env.pipeline.build_url()))
        self.p4.delete_client(client=self.__client)
