from enum import Enum


class LanguageEnum(Enum):
    # python3.6.8 没有 StrEnum
    chin_simp = "chin_simp"
    chin_trad = "chin_trad"


class Config:
    # 蓝盾 P4账号
    P4_CONFIG_BKCI = {
        "port": "x5_mobile.p4.com:1666",
        "user": "dgm_bkci",
        "password": "x5m12345",
    }

    P4_CONFIG_BKCI_P4CLIENT = {
        "host": "x5_mobile.p4.com:1666",
        "username": "dgm_bkci",
        "password": "x5m12345",
    }

    # jenkins p4账号
    P4_CONFIG_JENKINS = {
        "host": "x5_mobile.p4.com:1666",
        "username": "dgm_jenkins",
        "password": "x5m12345",
    }

    # ************* FTP配置
    FTP_CONFIG_150_30 = {
        "ip": "*************",
        "port": 21,
        "username": "administrator",
        "password": "dgm123!#*",
    }
    # ************* FTP配置
    FTP_CONFIG_150_46 = {
        "ip": "*************",
        "port": 21,
        "username": "administrator",
        "password": "Jiubugaosuni.46",
    }

    # Nexus 账号
    NEXUS_CONFIG = {"username": "mobile-robot", "password": "mobile-robot"}

    # Nexus mobile-date仓库 账号
    NEXUS_CONFIG_DATA = {
        "username": "mobile-data-robot",
        "password": "mobile-data-admin",
    }

    # nexus制品库技术中心账号
    NEXUS_CONFIG_DEVPRO = {
        "username": "productivity-robot",
        "password": "productivity-robot",
    }

    # gitlab
    GITLAB_MAINTAINER = {
        "url": "https://x5mobile-gitlab.h3d.com.cn/",
        "username": "<EMAIL>",
        "password": "maintainer123",
        "token": "o-yNgwVADiQ8CicYdt_4",
    }

    # 阳振岳jenkins账号
    # yangzhenyue_jenkins = {
    #     "url": "http://jenkins-devpro.h3d.com.cn:90/job/test_xml_to_byte/",
    #     "username": "<EMAIL>",
    #     "password": "yzy135678",
    # }

    # 腾讯FTP账号，用于手游发版使用
    # TENCENT_FTP = {
    #     "ip": "ftp.cloudstone.qq.com",
    #     "port": 9055,
    #     "username": "fworks_1019",
    #     "password": "Ieg#xH]xy[WRqEIVv4+6",
    #     "is_tls": True,
    # }
    TENCENT_FTP = {
        "ip": "ftp-sh1.cloudstone.qq.com",
        "port": 9054,
        "username": "fworks_1019",
        "password": "Ieg#xH]xy[WRqEIVv4+6",
        "is_tls": True,
    }

    # TENCENT_FTP = {
    #     "ip": "*************",
    #     "port": 21,
    #     "username": "Administrator",
    #     "password": "dgm123!#*",
    #     "is_tls": False,
    # }


config = Config()
