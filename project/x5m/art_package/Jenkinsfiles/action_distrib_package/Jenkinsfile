@Library('h3d_libs') _
import groovy.json.JsonSlurper
def changeList = ""
def isAndroidPackage = "true"
def isIosPackage = "true"
def timeRetryAndroid = 0
def timeRetryIos = 0
def messageSendUserList = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "wang<PERSON><PERSON>@h3d.com.cn", "<EMAIL>"]
/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
def buildResultCode = 0


def getP4View(platform,b_path, s_b_path, p4_view) {
    def tpl = '''
//x5_mobile/mr/art_resource/art_src/role/actions/action-expression/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/assets/resources/art/role/actions/action-expression/...
//x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/actions/... //${p4Client}/x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/actions/...
//x5_mobile/mr/b/${b_path}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/actions/... //${p4Client}/x5_mobile/mr/b/${b_path}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/actions/...
//x5_mobile/mr/b/7.04.0_dance/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/actions/... //${p4Client}/x5_mobile/mr/b/7.04.0_dance/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/actions/...
//x5_mobile/mr/onlineupdate/*/*/client/${platform}/assetbundles/art/role/actions/... //${p4Client}/x5_mobile/mr/onlineupdate/*/*/client/${platform}/assetbundles/art/role/actions/...
//x5mplan/resmg/act/... //${p4Client}/x5mplan/resmg/act/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/role/actions/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/role/actions/...
    '''
    if (!s_b_path.isEmpty()){
        tpl += '''//x5_mobile/mr/b/${s_b_path}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/actions/... //${p4Client}/x5_mobile/mr/b/${s_b_path}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/actions/...
'''
    }
    for (String v in p4_view) {
        tpl += v + '\n'
    }
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([platform: platform, b_path: b_path, s_b_path:s_b_path, p4Client: '${P4_CLIENT}']).toString()
}


def IsPackage(platform,isAndroidPackage,isIosPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    }else{
        return isIosPackage == "true"
    }
}


def getArtTrunkFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/release"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    url: '******************************:dgm/arttrunk.git']]]
}

def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}


pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        // timeout(time: 60, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
         booleanParam(name: 'force', defaultValue: true, description: '是否强更P4')
    }
    //     因为四点到五点p4需要备份数据，流水线会执行失败
    triggers {
        cron('H/15 0-3,5-23 * * *')
    }

    stages {
        stage('美术资源打包'){
            matrix {
                agent {
                    node{
                        label "action-cdn-distrib-${PLATFORM}-package"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'ios','android'
                    }
                }
                stages{
                    stage('配置初始化'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    if (!fileExists(".git/config")) {
                                        bat """
                                        del /s /f /q .
                                        """
                                    }
                                    env.ErrInfo='配置初始化失败'
                                    getBuildUserInfo()
                                    def configFile = "config.ini"
                                    def configExists = fileExists "$configFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}")
                                    if (!configExists) {
                                        bat """
                                        git clone -b action-distrib-${PLATFORM} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                                        """
                                    }
                                    bat """
                                    git clean -xdf
                                    git reset --hard HEAD
                                    git pull origin action-distrib-${PLATFORM}
                                    """
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='检查是否需要打包失败'
                                    def out = bat(returnStdout: true, script: "python setup.py get_updates --tp=action_distrib --first_changelist=${params.first_changelist}").split('\n')
                                    println(out[2])
                                    def jsonSlurper = new JsonSlurper()
                                    def result_map = jsonSlurper.parseText(out[2])
                                    p4_client_path_b = result_map.p4_client_path_b
                                    p4_client_path_s_b = result_map.p4_client_path_s_b
                                    println(result_map)
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDWORKSPACE = env.WORKSPACE
                                        isAndroidPackage = result_map.update
                                        println(isAndroidPackage)
                                    }else{
                                        env.IOSWORKSPACE = env.WORKSPACE
                                        isIosPackage = result_map.update
                                        println(isIosPackage)
                                    }
                                    println("result_map.update:" + result_map.update)
                                    if (result_map.update == 'true'){
                                        env.PACKAGELIST = result_map.package_list
                                        changeList = result_map.changelist
                                        env.SEND_MESSAGE_USER_INFO = result_map.send_user
                                        p4_view = result_map.p4_view.split(";")
                                        println(p4_view)
                                    }
                                    println("result_map.package_list:" + result_map.package_list)
                                    if(isAndroidPackage == 'false' && isIosPackage == 'false'){
                                        currentBuild.result = 'ABORTED'
                                    }
                                }
                            }
                        }
                    }
                    stage('清理资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps {
                            dir("x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/role/actions"){
                                deleteDir()
                            }
                        }
                    }
                    stage('获取p4资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            retry(2){
                                script{
                                    env.ErrInfo='获取p4资源失败'
                                    if ("${PLATFORM}" == "android"){
                                        timeRetryAndroid ++
                                        echo "安卓执行次数：${timeRetryAndroid}"
                                    }
                                    else{
                                        timeRetryIos ++
                                        echo "ios执行次数：${timeRetryIos}"
                                    }
                                    bat """
                                        git config --system core.longpaths true
                                        """
                                    getArtTrunkFromGitlab()
                                    def view = getP4View("${PLATFORM}","${p4_client_path_b}","${p4_client_path_s_b}", p4_view)
                                    // println(view)
                                    def p4_workspace = "jenkins-action-cdnart-distrib-${PLATFORM}-package"
                                    def python_view = view.replaceAll("\n", "#")
                                    writeFile encoding: 'GB2312', file: "p4_view.txt", text: "${python_view}"
                                    try {
                                        timeout(time: 15, unit: 'MINUTES') {
                                        bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=${params.force}")
                                        }
                                    } catch (err) {
                                        def sendUserString = messageSendUserList.join(",")
                                        if (sendUserString != "") {
                                            emailext to: "${sendUserString}", subject: "jenkins-动作资源分发打包 #${env.BUILD_NUMBER}", body: "获取p4资源卡住超时，重试中"
                                        }
                                        error("获取p4资源卡住超时，重试中")
                                    }
                                }
                            }
                            script {
                                def out = bat(returnStdout: true, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py distribute_precheck --p4_root=${env.WORKSPACE} --tp=action_distrib").split('\n')
                                def jsonSlurper = new JsonSlurper()
                                def result_map = jsonSlurper.parseText(out[2])
                                println(result_map)
                                if (result_map.state != "true"){
                                    env.ErrInfo = result_map.msg
                                    error(result_map.msg)
                                }
                            }
                        }
                    }
                    stage('packagelist写入文件'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                writeFile encoding: 'GB2312', file: "x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/resources.txt", text: "${env.PACKAGELIST}"
                                // env.PACKAGELIST = ""
                                println("处理之后的PACKAGELIST： ${env.PACKAGELIST}")
                            }
                        }
                    }

                    stage('调用打包命令'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='调用unity打包失败'
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    platform = "Android"
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    platform = "iOS"
                                    workspace = env.IOSWORKSPACE
                                }
                                def jenkins_log = "${workspace}\\jenkins_log\\${env.BUILD_NUMBER}"
                                def logExists = fileExists "$jenkins_log"
                                if(!logExists) {
                                    bat label:"日志文件夹",script:"mkdir jenkins_log\\${env.BUILD_NUMBER}"
                                }
                                def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat(script: "unity -quit -batchmode -logFile ${build_pack_log} -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" -executeMethod H3DBuildTools.BuildAction -buildTarget ${platform} out_path=${workspace}\\x5_mobile\\mr\\art_release\\cs\\${PLATFORM}\\assetbundles")
                            }
                        }
                    }
                    stage('计算打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='计算打包结果失败'
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }
                                    out = bat(returnStdout: true, script: "python setup.py print_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client ").split('\n')
                                    print(out[2])
                                    def count_result = readJSON text: out[2]
                                    env.UPLOADP4 = count_result.upload_p4
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDTOTALCOUNT = count_result.total_count
                                        print(env.ANDROIDTOTALCOUNT)
                                        env.ANDROIDSUCCCOUNT = count_result.succ_count
                                        print(env.ANDROIDSUCCCOUNT)
                                        env.ANDROIDFAILEDCOUNT = count_result.failed_count
                                        print(env.ANDROIDFAILEDCOUNT)
                                        env.ANDROIDERRORCOUNT = count_result.error_count

                                        if (env.ANDROIDERRORCOUNT != "0" || env.ANDROIDFAILEDCOUNT != "0" || env.ANDROIDSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                    else{
                                        env.IOSTOTALCOUNT = count_result.total_count
                                        print(env.IOSTOTALCOUNT)
                                        env.IOSSUCCCOUNT = count_result.succ_count
                                        print(env.IOSSUCCCOUNT)
                                        env.IOSFAILEDCOUNT = count_result.failed_count
                                        print(env.IOSFAILEDCOUNT)
                                        env.IOSERRORCOUNT = count_result.error_count
                                        if (env.IOSERRORCOUNT != "0" || env.IOSFAILEDCOUNT != "0" || env.IOSSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('提交p4'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.UPLOADP4 == 'true'
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='提交p4失败'
                                    def workspace
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }
                                    def submitP4Desc = "uploader:${env.SEND_MESSAGE_USER_INFO}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitP4Desc}"
                                    def out = bat(returnStdout: true,script: "python setup.py submit_files_to_p4  --root=${workspace} --platform=${PLATFORM} --file_paths=${env.PACKAGELIST} --tp=action_distrib --changelist=${changeList} --desc=\"${submitP4Desc}\" --workspace=jenkins-action-cdnart-distrib-${PLATFORM}-package")
                                    println(out)
                                    String[] outArras=out.split("\n")
                                    if (outArras.size() > 4){
                                        println("p4workspace下没有需要提交的内容！")
                                    }else{
                                        if ("${PLATFORM}" == "android"){
                                            env.ANDROID_CHANGELIST = outArras[2]
                                            env.ANDROID_RESOURCES = outArras[3]
                                        }else{
                                            env.IOS_CHANGELIST = outArras[2]
                                            env.IOS_RESOURCES = outArras[3]
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('持久化配置文件'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.UPLOADP4 == 'true'
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='持久化配置文件失败'
                                    bat """
                                    git config --global user.email "<EMAIL>"
                                    git config --global user.name "dgm_developer"
                                    git add config.ini
                                    git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                                    git push origin action-distrib-${PLATFORM}
                                    """
                                }
                            }
                        }
                    }
                    stage('上传打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.UPLOADP4 == 'true'
                            }
                        }
                        steps{
                                script{
                                    env.ErrInfo='上传打包结果失败'
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }
                                    def artifactreportName
                                    if ("${PLATFORM}" == "android"){
                                        artifactreportName = "log/${env.ANDROID_CHANGELIST.replaceAll("[\r\n]+","")}.html"
                                        env.ANDROIDREPORTULTURL = env.BUILD_URL + 'artifact/' + artifactreportName
                                        echo "$env.ANDROIDREPORTULTURL"
                                    }else{
                                        artifactreportName = "log/${env.IOS_CHANGELIST.replaceAll("[\r\n]+","")}.html"
                                        env.IOSREPORTULTURL = env.BUILD_URL + 'artifact/' + artifactreportName
                                        echo "$env.IOSREPORTULTURL"
                                    }
                                    echo "$artifactreportName"
                                    def buildLogAndroidExists = fileExists "$artifactreportName"
                                    if(buildLogAndroidExists){
                                        echo "1111"
                                        archiveArtifacts artifacts: "$artifactreportName", followSymlinks: false
                                    }
                                    def out
                                    dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                         out = bat(returnStdout: true, script: "python setup.py get_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}").split('\n')
                                    }
                                    def artifactsPath="x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/"
                                    dir("${artifactsPath}"){
                                        def artifactName = out[2]
                                        archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                        def artifactUrl = env.BUILD_URL + 'artifact/' + out[2]
                                        echo "upload_url: ${artifactUrl}"
                                         if ("${PLATFORM}" == "android"){
                                            env.ANDROIDPACKAGERESULTURL = artifactUrl.trim()
                                            }
                                        else{
                                            env.IOSPACKAGERESULTURL = artifactUrl.trim()
                                        }
                                    }
                                }
                        }
                    }
                    stage('判断是否单端失败'){
                          when {
                             expression {
                                 buildResultCode <= 3
                             }
                         }
                         steps{
                             script{
                                 if ("${PLATFORM}" == "android"){
                                     buildResultCode += 1
                                 }
                                 else{
                                     buildResultCode += 2
                                 }
                             }
                         }
                    }
                }
                post{
                    always{
                        //上传日志
                        script {
                            def build_log_android = "jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log"
                            def buildLogAndroidExists = fileExists "$build_log_android"
                            if(buildLogAndroidExists){
                                archiveArtifacts artifacts: "$build_log_android", followSymlinks: false
                            }
                            def build_log_ios = "jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log"
                            def buildLogIosExists = fileExists "$build_log_ios"
                            if(buildLogIosExists){
                                archiveArtifacts artifacts: "$build_log_ios", followSymlinks: false
                            }
                            //定义信息模板
                            env.ANDROIDBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.ANDROID_CHANGELIST}
[打包统计](${env.ANDROIDPACKAGERESULTURL})
[分发结果统计](${env.ANDROIDREPORTULTURL})
**打包数量**: ${env.ANDROIDTOTALCOUNT}
**成功数量**: ${env.ANDROIDSUCCCOUNT}"""
                            env.IOSBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.IOS_CHANGELIST}
[打包统计](${env.IOSPACKAGERESULTURL})
[分发结果统计](${env.IOSREPORTULTURL})
**打包数量**: ${env.IOSTOTALCOUNT}
**成功数量**: ${env.IOSSUCCCOUNT}"""
                            if (currentBuild.result != 'SUCCESS'){
                                env.ANDROIDBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.ANDROIDFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.ANDROIDERRORCOUNT}</font>"""
                                env.IOSBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.IOSFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.IOSERRORCOUNT}</font>"""
                            }
                            else{
                                env.ANDROIDBACKINFO += """
**失败数量**: ${env.ANDROIDFAILEDCOUNT}
**检查出错数量**: ${env.ANDROIDERRORCOUNT}"""
                                env.IOSBACKINFO += """
**失败数量**: ${env.IOSFAILEDCOUNT}
**检查出错数量**: ${env.IOSERRORCOUNT}"""
                            }
                            env.LOGBACKINFO = """
[安卓 unity日志](http://jenkins-x5mobile.h3d.com.cn/job/art_package/job/action_package/${env.BUILD_NUMBER}/artifact/jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log)
[ios unity日志](http://jenkins-x5mobile.h3d.com.cn/job/art_package/job/action_package/${env.BUILD_NUMBER}/artifact/jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log)"""
                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    echo "执行状态码： ${buildResultCode}"
                    def msg = """**结果**: 动作资源分发打包失败"""
                    if (buildResultCode == 1){
                        msg += """
<font color = "#dc143c">**单端成功**: 安卓(下次打包会自动把失败的一端打包)</font>
${env.ANDROIDBACKINFO}
"""
                    }
                    else if (buildResultCode == 2){
                        msg += """
<font color = "#dc143c">**单端成功**: ios(下次打包会自动把失败的一端打包)</font>
${env.IOSBACKINFO}
"""
                    }
                    msg += """
<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>
${env.LOGBACKINFO}
"""
                    echo "${msg}"
                    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033',content: msg
                    if (sendUserString != "") {
                        emailext to: "${sendUserString}", subject: 'jenkins-动作资源分发打包失败', body: msg
                    }
                }
            }
        }
        unstable{
            node('master') {
                script{
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg=""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}\n${env.LOGBACKINFO}"""
                        }
                        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033',content: msg
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: 'jenkins-动作资源分发打包部分失败', body: msg
                        }
                    }
                }
            }
        }
        success {
            node('master') {
                script{
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg=""
                        if (isAndroidPackage == "true")
                        {
                            def android_resources_list = env.ANDROID_RESOURCES.split(/#/, -1)
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                            msg += ("${android_resources_list[0]}" != "") ? "**CDN分发资源**：${android_resources_list[0]}\n" : ""
                            msg += ("${android_resources_list[1]}" != "") ? "**版内分发资源**：${android_resources_list[1]}\n" : ""
                            msg += ("${android_resources_list[2]}" != "") ? "**热更分发资源**：${android_resources_list[2]}\n".replaceAll(",", "\n") : ""
                            msg += ("${android_resources_list[3]}" != "") ? "**特殊分支分发资源**：${android_resources_list[3]}\n".replaceAll(",", "\n") : ""
                        }
                        if (isIosPackage == "true")
                        {
                            def ios_resources_list = env.IOS_RESOURCES.split(/#/, -1)
                            msg+="""**ios**：${env.IOSBACKINFO}\n"""
                            msg += ("${ios_resources_list[0]}" != "") ? "**CDN分发资源**：${ios_resources_list[0]}\n" : ""
                            msg += ("${ios_resources_list[1]}" != "") ? "**版内分发资源**：${ios_resources_list[1]}\n" : ""
                            msg += ("${ios_resources_list[2]}" != "") ? "**热更分发资源**：${ios_resources_list[2]}\n".replaceAll(",", "\n") : ""
                            msg += ("${ios_resources_list[3]}" != "") ? "**特殊分支分发资源**：${ios_resources_list[3]}\n".replaceAll(",", "\n") : ""
                        }
                        // workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033',content: msg
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: 'jenkins-动作资源分发打包成功', body: msg
                        }
                    }
                }
            }
        }
    }
}
