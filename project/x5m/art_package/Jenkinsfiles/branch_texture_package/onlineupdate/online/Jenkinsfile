@Library('h3d_libs') _
def changeList = ""
def texturePaths = ""
def isAndroidPackage = "true"
def isIosPackage = "true"
def timeRetryAndroid = 0
def timeRetryIos = 0
def messageSendUserList = ["<EMAIL>", "<EMAIL>", "ji<PERSON><PERSON><PERSON><PERSON>@h3d.com.cn", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "wang<PERSON><EMAIL>", "<EMAIL>"]
/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
def buildResultCode = 0
String[] onlineUpdateBranches
String[] view_list


def getArtTrunkFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/master"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    url: '******************************:dgm/arttrunk.git']]]
}

def getTextureView(view_list,branch,onlineUpdateBranches,platform){
    def tpl = ''''''
    for(String b in onlineUpdateBranches){
        tpl += "//x5_mobile/mr/onlineupdate/${branch}/${b}/client/${platform}/assetbundles/texture/... //\${p4Client}/x5_mobile/mr/onlineupdate/${branch}/${b}/client/${platform}/assetbundles/texture/...\n"
        tpl += "//x5_mobile/mr/onlineupdate/${branch}/${b}/client/${platform}/assetbundles/c/... //\${p4Client}/x5_mobile/mr/onlineupdate/${branch}/${b}/client/${platform}/assetbundles/c/...\n"
    }
    for(String v in view_list){
        tpl+=v+'\n'
    }
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([branch:branch,platform: platform,p4Client: '${P4_CLIENT}']).toString()
}
def IsPackage(platform,isAndroidPackage,isIosPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    }else{
        return isIosPackage == "true"
    }
}


def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}

pipeline {
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
     }
//     因为四点到五点p4需要备份数据，流水线会执行失败
    triggers {
       cron('H/15 0-3,5-23 * * *')
    }
    stages {
        stage('美术CDN资源打包'){
            matrix {
                agent {
                    node{
                        label "${NAMESPACE}-texture-${PLATFORM}-package"
                    }
                }
                axes {
                    axis {
                        name 'NAMESPACE'
                        values "online"
                    }
                    axis {
                        name 'PLATFORM'
                        values "ios" ,"android"
                    }
                }
                stages{
                    stage('配置初始化'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='配置初始化失败'
                                    getBuildUserInfo()
                                    if (!fileExists(".git/config")) {
                                        bat """
                                        del /s /f /q .
                                        """
                                    }
                                    // 判断配置 config.ini 是否存在，存在, 不处理, 否则判断 config.ini.example 是否存在, 存在，改名为config.ini
                                    def configFile = "config.ini"
                                    def configExampleFile = "config.ini.example"
                                    def configExists = fileExists "$configFile"
                                    def exampleExists = fileExists "$configExampleFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
                                    if (!configExists) {
                                        // 不存在
                                        bat """
                                        git clone -b texture-online-${PLATFORM} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                                        """
//                                         if (exampleExists) {
//                                             // 改名
//                                             fileOperations([fileRenameOperation(destination: "${configFile}", source: "${configExampleFile}")])
//                                         }
                                    }
                                    bat """
                                    git clean -xdf
                                    git reset --hard HEAD
                                    git pull origin texture-online-${PLATFORM}
                                    """
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='检查是否需要打包失败'
                                    def out = bat(returnStdout: true, script: "python setup.py check_onlineupdate_texture_update --namespace=${NAMESPACE} --first_changelist=${params.first_changelist}").split('\n')
                                    def result_map = readJSON text: out[2]
                                    println(result_map)
                                    branch = result_map.branch

                                    env.TEXTURE_PATHS = ""
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDWORKSPACE = env.WORKSPACE
                                        isAndroidPackage = result_map.update
                                        println(isAndroidPackage)
                                    }else{
                                        env.IOSWORKSPACE = env.WORKSPACE
                                        isIosPackage = result_map.update
                                        println(isIosPackage)
                                    }
                                    if (result_map.update == 'true'){
                                        view_list = result_map.views.split(';')
                                        env.MAJORVERSION = result_map.major_version
                                        env.VERSIONS = result_map.versions
                                        onlineUpdateBranches = result_map.versions.split(';')
                                        texturePaths = result_map.texture_paths
                                        changeList = result_map.changelist
                                        env.SEND_MESSAGE_USER_INFO = result_map.send_user
                                        env.TEXTURE_PATHS = result_map.texture_paths.replace(",", "\n")
                                        writeFile encoding: 'GB2312', file: "${workspace}/package_list/package_list_${env.BUILD_NUMBER}.log", text: "${env.TEXTURE_PATHS}"
                                    }
                                    if(isAndroidPackage == 'false' && isIosPackage == 'false'){
                                        currentBuild.result = 'ABORTED'
                                    }
                                }
                            }
                        }
                    }
                    stage('获取p4资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            retry(2){
                                script{
                                    env.ErrInfo='获取p4资源失败'
                                    if ("${PLATFORM}" == "android"){
                                        timeRetryAndroid ++
                                        echo "安卓执行次数：${timeRetryAndroid}"
                                    }
                                    else{
                                        timeRetryIos ++
                                        echo "ios执行次数：${timeRetryIos}"
                                    }
                                    bat """
                                        git config --system core.longpaths true
                                        """
                                    getArtTrunkFromGitlab()
                                    dir('x5_mobile/mobile_dancer/arttrunk/client/Assets/ab_resources/texture'){
                                        deleteDir()
                                    }
                                    dir('x5_mobile/mobile_dancer/arttrunk/client/Assets/ab_resources/c'){
                                        deleteDir()
                                    }
                                    dir('x5_mobile/mr/onlineupdate'){
                                        deleteDir()
                                    }
                                    def texture_view = getTextureView(view_list,env.MAJORVERSION,onlineUpdateBranches,"${PLATFORM}")
                                    // println(texture_view)
                                    def p4_workspace = "jenkins-${PLATFORM}-${NAMESPACE}-onlineupdate-texture-package"
                                    def python_view = texture_view.replaceAll("\n", "#")
                                    bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=${params.force}")

//                                     checkout perforce(credential: 'p4_dgm_jenkins_upwd',
//                                         populate: syncOnly(force: true, have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: env.P4_REVISION, quiet: true, revert: false),
//                                         workspace: manualSpec(
//                                             charset: 'none',
//                                             cleanup: false,
//                                             name: "jenkins-${PLATFORM}-${NAMESPACE}-onlineupdate-texture-package",
//                                             pinHost: false,
//                                             spec: clientSpec(
//                                                 allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
//                                                 locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: texture_view
//                                             )
//                                         )
//                                     )
                                }
                            }
                        }
                    }
                    stage('调用打包命令'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='调用unity打包失败'
                                for(String b in onlineUpdateBranches){
                                    dir('x5_mobile/mobile_dancer/arttrunk/client/Assets/ab_resources'){
                                        try {
                                            bat script: """
                                                rmdir "texture"
                                                rmdir "c"
                                                """
                                        }
                                        catch (exc) {
                                            echo '不需要删除!'
                                        }
                                    }
                                    dir("x5_mobile/mr/onlineupdate/${env.MAJORVERSION}"){
                                        if(fileExists("${b}/art_src/texture"))
                                        {
                                          bat """mklink /J ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\\Assets\\ab_resources\\texture ${b}\\art_src\\texture"""
                                        }
                                        if(fileExists("${b}/art_src/c"))
                                        {
                                          bat """mklink /J ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\\Assets\\ab_resources\\c ${b}\\art_src\\c"""
                                        }
                                    }
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android"){
                                        platform = "Android"
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        platform = "iOS"
                                        workspace = env.IOSWORKSPACE
                                    }
                                    def jenkins_log = "${workspace}\\jenkins_log\\${env.BUILD_NUMBER}"
                                    def logExists = fileExists "$jenkins_log"
                                    if(!logExists)
                                    {
                                        bat label:"日志文件夹",script:"mkdir jenkins_log\\${env.BUILD_NUMBER}"
                                    }
                                    def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                                    bat(script: "unity -quit -batchmode -logFile ${build_pack_log} -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" -executeMethod H3DBuildTools.BuildAB -buildTarget ${platform} outpath=${workspace}/x5_mobile/mr/onlineupdate/${env.MAJORVERSION}/${b}/client/${PLATFORM}/assetbundles")
                                }
                            }
                        }
                    }
                    stage('提交p4'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='提交p4失败'
                                    def workspace
                                    def submitCsv
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                        env.SUBMITCSV = true
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                        env.SUBMITCSV = false
                                    }
                                    def submitP4Desc = "uploader:${env.SEND_MESSAGE_USER_INFO}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitP4Desc}"
                                    def out = bat(returnStdout: true, script: "python setup.py submit_files_to_p4 --root=${workspace} --platform=${PLATFORM} --file_paths=${texturePaths}  --tp=online_online_update_texture --changelist=${changeList} --desc=\"${submitP4Desc}\" --workspace=jenkins-${PLATFORM}-${NAMESPACE}-onlineupdate-texture-package")

                                    println(out)
                                    String[] outArras=out.split("\n")
                                    if (outArras.size() > 4){
                                        println("p4workspace下没有需要提交的内容！")
                                    }else{
                                        if ("${PLATFORM}" == "android"){
                                            env.ANDROID_CHANGELIST = outArras[2]
                                        }else{
                                            env.IOS_CHANGELIST = outArras[2]
                                       }
                                    }
                                }
                            }
                        }
                    }
                    stage('持久化配置文件'){
//                         when {
//                             expression {
//                                 return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
//                             }
//                         }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='持久化配置文件失败'
                                    bat """
                                    git config --global user.email "<EMAIL>"
                                    git config --global user.name "dgm_developer"
                                    git add config.ini
                                    git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                                    git push origin texture-online-${PLATFORM}
                                    """
                                }
                            }
                        }
                    }
                }
                post{
                    always{
                        //上传资源列表
                        script {
                            def package_list = "package_list/package_list_${env.BUILD_NUMBER}.log"
                            def packageListExists = fileExists "$package_list"
                            if(packageListExists){
                                archiveArtifacts artifacts: "$package_list", followSymlinks: false
                            }
                        }
                    }
                }
            }
        }
    }
    post {
         failure {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    def msg = """**结果**: 现网周更online环境texture资源打包失败\n"""
                    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb',content: msg
                    if (sendUserString != "") {
                        emailext to: "${sendUserString}", subject: "jenkins-现网周更online环境texture资源打包失败 #${env.BUILD_NUMBER}", body: msg
                    }
                }
            }
        }
       success {
            node('master') {
                script{
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg = """**结果**: 现网周更online环境texture资源打包成功\n"""
                        msg += """**[资源列表](${env.BUILD_URL}artifact/package_list/package_list_${env.BUILD_NUMBER}.log)**\n"""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：
**原图 changelist**: ${changeList}
**ab包 changelist**: ${env.ANDROID_CHANGELIST}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：
**原图 changelist**: ${changeList}
**ab包 changelist**: ${env.IOS_CHANGELIST}"""
                        }
                        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb', content: msg
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: "jenkins-现网周更online环境texture资源打包成功 #${env.BUILD_NUMBER}", body: msg
                        }
                    }
                }
            }
        }

    }
}