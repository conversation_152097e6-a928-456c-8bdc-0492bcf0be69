@Library('h3d_libs') _

def config_depository_name="x5mconfig"
def config_url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig.git"

def weekly_depository_name="x5mweek"
def weekly_url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mweek.git"


def workspace_dir1="D:/jenkins/workspace"
def workspace_dir2="D:\\jenkins\\workspace"

def zip_dir1="D:/file_services/version_test"
def zip_dir2="D:\\file_services\\version_test"

def timeRetryResources = 0
def timeRetryRelease = 0
def timeRetryOnline = 0

def getCodeRelease()
{
    env.ErrInfo='release:获取代码失败'
    clear_config(env.X5MOBILE_RELEASE_BRANCH,env.RELEASE_WEEKLY_RESOURCE_DIR)
    getToolAndConfigFromGitlab(env.X5MOBILE_RELEASE_BRANCH)
    def v = getServerHotFixAndCDNP4View(env.X5MOBILE_RELEASE_BRANCH,false,0)
    checkout perforce(credential: 'p4_dgm_jenkins_upwd',
        populate: syncOnly(force: "${params.force}", have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: env.P4_REVISION, quiet: true, revert: false),
        workspace: manualSpec(
            charset: 'none',
            cleanup: false,
            name: 'release-jenkins-${NODE_NAME}-${JOB_NAME}',
            pinHost: false,
            spec: clientSpec(
                allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
                locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: v
            )
        )
    )
}

def getCodeResource(){
    env.ErrInfo='resources:获取代码失败'
    clear_config(env.X5MOBILE_RESOURCES_BRANCH,env.RESOURCES_WEEKLY_RESOURCE_DIR)
    dir("pyframe-pipeline/project/x5m/art_package/scripts")
    {
        bat(script: "python setup.py clear_config --folder_path ${env.WORKSPACE}\\x5_mobile\\mr\\onlineupdate\\${env.X5MOBILE_RESOURCES_BRANCH}")
    }
    getToolAndConfigFromGitlab(env.X5MOBILE_RESOURCES_BRANCH)

    def v = getServerHotFixAndCDNP4View(env.X5MOBILE_RESOURCES_BRANCH,true,env.CDN_BASE_VERSION)
    checkout perforce(credential: 'p4_dgm_jenkins_upwd',
        populate: syncOnly(force: "${params.force}", have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: '', quiet: true, revert: false),
        workspace: manualSpec(
            charset: 'none',
            cleanup: false,
            name: 'jenkins-resources-auto',
            pinHost: false,
            spec: clientSpec(
                allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
                locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: v
            )
        )
    )
}

def getCodeOnline(){
    env.ErrInfo='online:获取代码失败'
    clear_config(env.X5MOBILE_ONLINE_BRANCH,env.ONLINE_WEEKLY_RESOURCE_DIR)
    getToolAndConfigFromGitlab(env.X5MOBILE_ONLINE_BRANCH)
    def v = getServerHotFixAndCDNP4View(env.X5MOBILE_ONLINE_BRANCH,false,0)
    checkout perforce(credential: 'p4_dgm_jenkins_upwd',
        populate: syncOnly(force: "${params.force}", have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: env.P4_REVISION, quiet: true, revert: false),
        workspace: manualSpec(
            charset: 'none',
            cleanup: false,
            name: 'release-jenkins-${NODE_NAME}-${JOB_NAME}',
            pinHost: false,
            spec: clientSpec(
                allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
                locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: v
            )
        )
    )
}


def getServerHotFixAndCDNP4View(branchName,isResource,cdnBaseVersion) {
    def tpl = '''
    //x5_mobile/mr/art_release/pack/hotfix/... //${p4Client}/x5_mobile/mr/art_release/pack/hotfix/...
    //x5_mobile/mr/art_release/art_src/fetter_cg/... //${p4Client}/x5_mobile/mr/Resources/美术资源/UI/羁绊背景图/...
    //x5_mobile/mr/onlineupdate/... //${p4Client}/x5_mobile/mr/onlineupdate/...
    //x5_mobile/mr/onlineupdate/${branchName}/version.xml //${p4Client}/x5_mobile/mr/onlineupdate/${branchName}/version.xml
    '''
    if(isResource)
    {
        tpl+='''
        //x5m/res/cdn/pack/${cdnBaseVersion}/... //${p4Client}/x5_mobile/mr/art_release/pack/dress/${cdnBaseVersion}/...
        //x5m/res/cdn/cooked/android/... //${p4Client}/x5_mobile/mr/art_release/cs/android/...
        //x5m/res/cdn/cooked/ios/... //${p4Client}/x5_mobile/mr/art_release/cs/ios/...
        '''
    }

    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([branchName: branchName, p4Client: '${P4_CLIENT}',cdnBaseVersion:cdnBaseVersion]).toString()
}

def getToolAndConfigFromGitlab(branch){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/${branch}"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile/gitlab'],
    [$class: 'SparseCheckoutPaths', sparseCheckoutPaths: [[path: 'mobile_dancer/trunk/exe/ResListTool']]]],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm-package-gitlab-sshpk',
    url: '******************************:dgm/x5mobile.git']]]
}

def checkupdate(){
    // bat """
    // python -m pip install --upgrade pip
    //  pip install -r requirements.txt
    // """
    def out = bat(returnStdout: true, script: "python setup.py calculate_hotfix_versions --namespace release").split('\n')
    def result_map = readJSON text: out[2]
    println(result_map)
    env.X5MOBILE_RELEASE_BRANCH=result_map['major_version']
    env.X5MOBILE_RELEASE_BIZNAMESPACE=result_map['namespace']
    if (result_map['hotfix_package'] == 'True' || params.releaseForcePackage){
        env.X5MOBILE_RELEASE_IS_HOT_FIX = 'true'
    }else{
        env.X5MOBILE_RELEASE_IS_HOT_FIX = 'false'
    }
    env.RELEASE_WEEKLY_RESOURCE_DIR = result_map['weekly_resource']
    env.RELEASE_WEEKLY_RESOURCE_DIR_NAME ="${env.WORKSPACE}\\x5_mobile\\mr\\onlineupdate\\${env.X5MOBILE_RELEASE_BRANCH}\\${result_map['weekly_resource']}\\client\\"
    env.RELEASE_WEEKLY_RESOURCE_VERSION = result_map['weekly_version']
    env.RELEASE_HOTFIX_BASE_VERSION = result_map['hotfix_base_version']
    env.RELEASE_HOTFIX_CHANGE_LIST = result_map['hotfix_changelist']
    env.RELEASE_CONFIG_PIPELINE_ID = result_map['config_pipeline_id']
}
def clear_config(branch,version){
    dir("x5_mobile/mr/art_release/cs/config"){
        deleteDir()
    }
    dir("x5_mobile/mr/onlineupdate/${branch}/${version}/client/crossplatform/config"){
        deleteDir()
    }
    dir("x5_mobile/mr/onlineupdate/${branch}/${version}/server/resources"){
        deleteDir()
    }
    dir("x5_mobile/gitlab/mobile_dancer/trunk/exe/resources"){
        deleteDir()
    }
}

def copy_config(branch){
    bat label:"拷贝config",script: """
        git fetch --all
        git reset --hard
        git checkout cdn
        git pull
        echo d y| xcopy config  ${env.WORKSPACE}\\x5_mobile\\mr\\art_release\\cs\\config /e /y /r
        git fetch --all
        git reset --hard
        git checkout hotfix
        git pull
        echo d y| xcopy onlineupdate ${env.WORKSPACE}\\x5_mobile\\mr\\onlineupdate /e /y /r
        git fetch --all
        git reset --hard
        git checkout ${branch}
        git pull
        echo d y| xcopy config\\server\\h3d ${env.WORKSPACE}\\x5_mobile\\gitlab\\mobile_dancer\\trunk\\exe\\resources\\config\\server\\h3d /e /y /r"""

}

def init_config(){
     env.ErrInfo="配置初始化失败"
     def tmp_path =env.WORKSPACE.replace("\\",'/')
     def configFile = "config.ini"
     def configExampleFile = "config.ini.example"
     def copyConfigFile = "${tmp_path}/config.ini"
     def configExists = fileExists "$configFile"
     def copyConfigExists = fileExists "$copyConfigFile"
     def exampleExists = fileExists "$configExampleFile"
     println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
     if (!configExists) {
         // 不存在
         if(copyConfigExists) {
             // 拷贝替换
            bat label: '重新拷贝config配置', script: """
            copy ${env.WORKSPACE}\\config.ini ${env.WORKSPACE}\\gitlab\\Script\\config\\config.ini
            """
         }
         else{
             if(exampleExists){
                // 改名
                fileOperations([fileRenameOperation(destination: "${configFile}", source: "${configExampleFile}")])
             }
         }
     }
}

def config_ini(namespace){
    env.ErrInfo="${namespace}: 备份config.ini失败"
    def tmp_path =env.WORKSPACE.replace("\\",'/')
    def copyConfigFile = "${tmp_path}/config.ini"
    def copyConfigExists = fileExists "$copyConfigFile"
    if(copyConfigExists) {
        bat label: '${namespace}: 删除上一个config 配置', script: """
        del /f /q ${env.WORKSPACE}\\config.ini
        """
    }
    bat label: '${namespace}: 重新拷贝config配置', script: """
        copy ${env.WORKSPACE}\\gitlab\\Script\\config\\config.ini ${env.WORKSPACE}\\config.ini
        """
}

def copy_x5mweek_config(){
    bat label:"拷贝x5mweek config", script: """
        git checkout env.X5MOBILE_RESOURCES_BIZNAMESPACE
        git pull
        echo d y| xcopy config  ${env.WORKSPACE}\\x5_mobile\\mr\\xmlweek_${env.X5MOBILE_RESOURCES_BIZNAMESPACE}\\config /e /y /r
     """
}

def get_week_config(){
      def  getRepo =false
      if (!fileExists('../x5mweek/.git')) {
          echo "x5mweek/.git 文件夹不存在"
          getRepo = true
      } else {
          dir('../x5mweek') {
              //检查是否正常仓库
             ret= bat(label:"检查Git仓库",returnStatus :true,encoding:'GBK',script:'git branch')
             //如果不是正常的git仓库重新下载代码
             if(ret !=0) {
                getRepo = true;
                echo  "不是正常的git仓库需要强制更新"
             }
             else{
                 bat label:"切换分支",script:"""
              git checkout ${env.X5MOBILE_RESOURCES_BIZNAMESPACE}
              git pull"""
             }
          }
      }
      if(getRepo.toBoolean()){
          dir('..') {
              //首次正常下载代码
              env.ErrInfo='创建本地Git仓库失败'
              bat label:"克隆新仓库",script:"""
              git clone -b ${env.X5MOBILE_RESOURCES_BIZNAMESPACE} ${weekly_url}
              """
          }
      }
}

def get_cdn_config(){
      def  getRepo =false
      if (!fileExists('../x5mconfig/.git')) {
          echo "x5mconfig/.git 文件夹不存在"
          getRepo = true
      } else {
          dir('../x5mconfig') {
              //检查是否正常仓库
             ret= bat(label:"检查Git仓库",returnStatus :true,encoding:'GBK',script:'git branch')
             //如果不是正常的git仓库重新下载代码
             if(ret !=0) {
                getRepo = true;
                echo  "不是正常的git仓库需要强制更新"
             }
          }
      }
      if(getRepo.toBoolean()){
          dir('..') {
              //首次正常下载代码
              env.ErrInfo='创建本地Git仓库失败'
              bat label:"克隆新仓库",script:"""
              git clone -b cdn ${config_url}
              """
          }
      }
}

def check_online_update(){
    def out = bat(returnStdout: true, script: "python setup.py calculate_hotfix_versions --namespace online").split('\n')
    def result_map = readJSON text: out[2]
    println(result_map)

    env.X5MOBILE_ONLINE_BRANCH=result_map['major_version']
    env.X5MOBILE_ONLINE_BIZNAMESPACE=result_map['namespace']
    if (result_map['hotfix_package'] == 'True' || params.onlineForecePackage){
        env.X5MOBILE_ONLINE_IS_HOT_FIX = 'true'
    }else{
        env.X5MOBILE_ONLINE_IS_HOT_FIX = 'false'
    }
    env.ONLINE_WEEKLY_RESOURCE_DIR = result_map['weekly_resource']
    env.ONLINE_WEEKLY_RESOURCE_DIR_NAME ="${env.WORKSPACE}\\x5_mobile\\mr\\onlineupdate\\${env.X5MOBILE_ONLINE_BRANCH}\\${result_map['weekly_resource']}\\client\\"
    env.ONLINE_WEEKLY_RESOURCE_VERSION = result_map['weekly_version']
    env.ONLINE_HOTFIX_BASE_VERSION = result_map['hotfix_base_version']
    env.ONLINE_HOTFIX_CHANGE_LIST = result_map['hotfix_changelist']
    env.ONLINE_CONFIG_PIPELINE_ID = result_map['config_pipeline_id']
}

def post_log(namespace){
    def out
    def tmp_path =env.WORKSPACE.replace("\\",'/')
    def path = "${tmp_path}/x5_mobile/gitlab/mobile_dancer/trunk/exe/ResListTool/Logs/"
    dir('pyframe-pipeline/project/x5m/art_package/scripts'){
        out = bat(returnStdout: true, script: "python setup.py get_cdn_hotfix_log --namespace ${namespace} --path ${path}").split('\n')
    }
    def filename = out[2].replaceAll("[\r\n]+","")
    println("filename:${filename}")
    def logFile = "x5_mobile/gitlab/mobile_dancer/trunk/exe/ResListTool/Logs/${filename}"
    def LogExists = fileExists "$logFile"
    if(LogExists){
        archiveArtifacts artifacts: "$logFile", followSymlinks: false
    }

}

pipeline {
    agent {
        node {
            label 'cdn_hotfix_resources'
        }
    }
     parameters {
         booleanParam(name: 'force', defaultValue: false, description: '是否强更')
         booleanParam(name: 'resourceForcePackage', defaultValue: false, description: 'resource强制打包')
         booleanParam(name: 'releaseForcePackage', defaultValue: false, description: 'release强制打包')
         booleanParam(name: 'onlineForecePackage', defaultValue: false, description: 'online强制打包')
     }
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 20, unit: 'MINUTES')
        // buildDiscarder logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '30', numToKeepStr: '500')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
//     因为五点到六点半p4需要备份数据，流水线会执行失败
    triggers {
      cron('H/30 0-3,5-20 * * *\nH/10 21-23 * * *')
    }
    stages {
        stage('resources'){
            stages{
                stage('配置初始化'){
                    steps {
                        dir('pyframe-pipeline/project/x5m/art_package/config'){
                            script{
                                init_config()
                            }
                        }
                    }
                }
                stage('检查resources更新'){
                    steps {
                        dir("pyframe-pipeline/project/x5m/art_package/scripts")
                        {
                            echo env.WORKSPACE
                            script {
                                String[] out = bat(returnStdout: true, script: "python setup.py calculate_hotfix_versions --namespace resources").split('\n')
                                def result_map = readJSON text: out[2]
                                println(result_map)

                                env.X5MOBILE_RESOURCES_BRANCH=result_map['major_version']
                                env.X5MOBILE_RESOURCES_BIZNAMESPACE=result_map['namespace']
                                env.X5MOBILE_RESOURCES_PACK = "true"
                                if (result_map['hotfix_package'] == "False" && result_map['cdn_package'] == "False"){
                                    env.X5MOBILE_RESOURCES_PACK = "false"
                                }else if(result_map['cdn_package'] == "False" && result_map['hotfix_package'] == "True"){
                                    env.X5MOBILE_RES_TOOL_PACK_TYPE = "Hotfix"
                                }else if(result_map['cdn_package'] == "True" && result_map['hotfix_package'] == "False"){
                                    env.X5MOBILE_RES_TOOL_PACK_TYPE = "Cdn"
                                }else{
                                    env.X5MOBILE_RES_TOOL_PACK_TYPE = "All"
                                }
                                if (params.resourceForcePackage){
                                    if(result_map['cdn_package'] == "False"){
                                        env.X5MOBILE_RES_TOOL_PACK_TYPE = "Hotfix"
                                    }
                                    else{
                                        env.X5MOBILE_RES_TOOL_PACK_TYPE = "All"
                                    }
                                    env.X5MOBILE_RESOURCES_PACK = "true"
                                }
                                env.CDN_RAW_RESOURCE_CHANGELIST = result_map['cdn_raw_resource_changelist']
                                env.RESOURCES_WEEKLY_RESOURCE_DIR = result_map['weekly_resource']
                                env.RESOURCES_WEEKLY_RESOURCE_DIR_NAME ="${env.WORKSPACE}\\x5_mobile\\mr\\onlineupdate\\${env.X5MOBILE_RESOURCES_BRANCH}\\${result_map['weekly_resource']}\\client\\"
                                env.RESOURCES_WEEKLY_RESOURCE_VERSION = result_map['weekly_version']
                                env.RESOURCES_HOTFIX_BASE_VERSION = result_map['hotfix_base_version']
                                env.CDN_SOURCE_CHANGE_LIST =  result_map['cdn_source_change_list']
                                env.RESOURCES_HOTFIX_CHANGE_LIST = result_map['hotfix_changelist']
                                env.RESOURCES_CONFIG_PIPELINE_ID = result_map['config_pipeline_id']
                                String[] CDN_Versions = bat(returnStdout: true, script: "python setup.py cdn-package --package ${result_map['cdn_package']}").split("\n")
                                def cdn_result_map = readJSON text: CDN_Versions[2]
                                println(cdn_result_map)
                                env.CDN_VERSION = cdn_result_map['cdn_version']
                                env.CDN_BASE_VERSION = cdn_result_map['cdn_base_version']
                                env.RES_LIST_VERSION = cdn_result_map['res_list_version']
                            }
                        }
                    }
                }

                //1. 检查新Git仓库
                stage('获取config仓库'){
                    when {
                        expression {
                            return env.X5MOBILE_RESOURCES_PACK == "true"
                        }
                    }
                   steps{
                       script{
                            get_cdn_config()
                       }
                   }
                }


                stage('resources:获取代码') {
                    when {
                        expression {
                            return env.X5MOBILE_RESOURCES_PACK == "true"
                        }
                    }
                    steps {
                        retry(2){
                            script {
                                timeRetryResources ++
                                getCodeResource()
                            }
                        }
                    }
                }


                //1. 检查新Git仓库
                //env.X5MOBILE_RESOURCES_BIZNAMESPACE
                stage('获取xmlweek仓库'){
                    when {
                        expression {
                            return env.X5MOBILE_RESOURCES_PACK == "true"
                        }
                    }
                   steps{
                       script{
                            get_week_config()
                       }
                   }
                }

                stage('复制文件到指定目录下'){
                    when {
                        expression {
                            return env.X5MOBILE_RESOURCES_PACK == "true"
                        }
                    }
                    steps{
                        println("env.WORKSPACE:${env.WORKSPACE}")
                        dir("../x5mconfig")
                        {
                            script {
                                env.ErrInfo='resources:复制文件到指定目录下失败'
                                // if (env.X5MOBILE_RESOURCES_BRANCH == '4.14.0'){
                                //     copy_config('4.12.0')
                                // }else{
                                //     copy_config(env.X5MOBILE_RESOURCES_BRANCH)
                                // }
                                copy_config(env.X5MOBILE_RESOURCES_BRANCH)
                            }
                        }
                        dir("x5_mobile\\mr\\xmlweek_${env.X5MOBILE_RESOURCES_BIZNAMESPACE}\\config"){
                            deleteDir()
                        }
                        dir("../x5mweek")
                        {
                            script {
                               copy_x5mweek_config()
                            }
                        }
                    }
                }


                stage('resources:获取cdn res_list.zip'){
                    when {
                        expression {
                            return env.X5MOBILE_RES_TOOL_PACK_TYPE == "Hotfix"
                        }
                    }
                    steps{
                        dir("D:/jenkins/workspace")
                        {
                            script {
                                def out = bat(returnStdout: true, script: "mkdir.exe --ra=D:/jenkins/workspace/ResourcePublish/3.12.0/SourceFiles/android/${env.RESOURCES_WEEKLY_RESOURCE_VERSION} --ri=D:/jenkins/workspace/ResourcePublish/3.12.0/SourceFiles/iOS/${env.RESOURCES_WEEKLY_RESOURCE_VERSION} --rlv=${env.RES_LIST_VERSION}")
                                println(out)
                            }
                        }
                    }
                }
                stage('resources: unzip'){
                    when {
                        expression {
                            return env.X5MOBILE_RES_TOOL_PACK_TYPE == "Hotfix"
                        }
                    }
                    steps{
                        dir("${workspace_dir1}/archive/android"){
                            unzip dir: '.', glob: '', zipFile: "res_list_${env.RES_LIST_VERSION}.zip"
                            //unzip res_list_${env.RES_LIST_VERSION}.zip
                            bat label: '拷贝安卓zip包', script: """
                            move /Y "res_list_${env.RES_LIST_VERSION}.csv" "${workspace_dir2}\\ResourcePublish\\3.12.0\\SourceFiles\\android\\${env.RESOURCES_WEEKLY_RESOURCE_VERSION}\\res_list_${env.RES_LIST_VERSION}.csv"
                            """
                        }
                        dir("${workspace_dir1}/archive/ios"){
                            unzip dir: '.', glob: '', zipFile: "res_list_${env.RES_LIST_VERSION}.zip"
                            //unzip res_list_${env.RES_LIST_VERSION}.zip
                            bat label: '拷贝ios zip包', script: """
                            move /Y "res_list_${env.RES_LIST_VERSION}.csv" "${workspace_dir2}\\ResourcePublish\\3.12.0\\SourceFiles\\iOS\\${env.RESOURCES_WEEKLY_RESOURCE_VERSION}\\res_list_${env.RES_LIST_VERSION}.csv"
                            """
                        }
                    }
                }
                stage('resources:执行打包工具'){
                    when {
                        expression {
                            return env.X5MOBILE_RESOURCES_PACK == "true"
                        }
                    }
                    steps{
                        script{
                            dir("x5_mobile/gitlab/mobile_dancer/trunk/exe/ResListTool")
                            {
                                writeFile encoding: 'GB2312', file: './start.bat', text: "start /wait ResListTool CDNVersion:${env.CDN_VERSION} ServerConfigPath:${env.WORKSPACE}\\x5_mobile\\gitlab\\mobile_dancer\\trunk\\exe\\resources\\config\\server\\ CDNLastVersionZipPath:${env.WORKSPACE}\\x5_mobile\\mr\\art_release\\pack\\dress\\${env.CDN_BASE_VERSION}\\${env.CDN_BASE_VERSION}.zip CDNPath:${env.WORKSPACE}\\x5_mobile\\mr\\art_release\\ SourcesPath:${env.RESOURCES_WEEKLY_RESOURCE_DIR_NAME} CDNSourcesPath:${env.WORKSPACE}\\x5_mobile\\mr\\art_release\\cs\\ BizPath:${env.X5MOBILE_RESOURCES_BIZNAMESPACE} HotFixVersion:${env.RESOURCES_WEEKLY_RESOURCE_VERSION } HotFixLastVersion:${env.RESOURCES_HOTFIX_BASE_VERSION} ResToolPackType:${env.X5MOBILE_RES_TOOL_PACK_TYPE} LanguageType:chin_simp"
                                ret= bat(label:"执行bat文件",returnStatus :true,encoding:'GBK',script:'call start.bat')
                                if(ret != 0){
                                    env.ErrInfo="resources:执行打包工具失败,ret返回值：${ret}"
                                    if(ret == 2) {
                                       env.ErrInfo='resources:xml转二进制失败,请查看失败文件'
                                    }
                                    currentBuild.result = 'FAILURE'
                                }
                            }
                        }

                    }
                }
                stage('resources:拷贝cdn包，热更包和服务器包'){
                    when {
                        expression {
                            return env.X5MOBILE_RESOURCES_PACK == "true"
                        }
                    }
                    steps{
                        dir("x5_mobile/mr/art_release/pack/dress/${env.CDN_VERSION}")
                        {
                            script{
                                if(env.X5MOBILE_RES_TOOL_PACK_TYPE == "Cdn" || env.X5MOBILE_RES_TOOL_PACK_TYPE == "All"){
                                    bat label: '拷贝cdn服务器配置', script: """
                                    unzip ${env.CDN_VERSION}.zip -d ${zip_dir1}/dynamic
                                    copy "cdn_${env.CDN_VERSION}.zip" "${zip_dir1}/update_config/fix_cdn_config/cdn_config.zip"
                                    move /Y "cdn_${env.CDN_VERSION}.zip" "${zip_dir1}/server_resources/cdn_${env.CDN_VERSION}.zip"
                                    curl -d "bk_app_code=x5m-web-devops&bk_app_secret=48f9c7d9-3dba-4d40-9fa1-ba797749ea8b&bk_username=<EMAIL>&bk_biz_id=4&bk_job_id=1000055" "https://bk.h3d.com.cn/api/c/compapi/v2/job/execute_job/"
                                    """
                                }
                                if(env.X5MOBILE_RES_TOOL_PACK_TYPE == "Hotfix" || env.X5MOBILE_RES_TOOL_PACK_TYPE == "All"){
                                    bat label: '拷贝热更包', script: """
                                    rmdir /S /Q "${zip_dir1}/resources/android/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}"
                                    rmdir /S /Q "${zip_dir1}/resources/ios/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}"
                                    move /Y "${workspace_dir1}/ResourcePublish/3.12.0/ReleaseFiles/android/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}" "${zip_dir1}/resources/android/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}"
                                    move /Y "${workspace_dir1}/ResourcePublish/3.12.0/ReleaseFiles/ios/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}" "${zip_dir1}/resources/ios/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}"
                                    """
                                    bat label: '拷贝服务器包', script: """
                                    rmdir /S /Q "${zip_dir1}/update_config/source"
                                    mkdir "${zip_dir1}/update_config/source"
                                    copy "${workspace_dir2}\\ResourcePublish\\3.12.0\\ReleaseFiles\\server_resources\\resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip" "${zip_dir1}/update_config/source/resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip"
                                    move /Y "${workspace_dir2}\\ResourcePublish\\3.12.0\\ReleaseFiles\\server_resources\\resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip" "${zip_dir2}\\server_resources\\resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip"
                                    """
                                }
                            }
                        }
                    }
                }
                stage('resources:拷贝config.ini'){
                    steps{
                        dir('pyframe-pipeline/project/x5m/art_package/config'){
                            script{
                                config_ini('resources')
                            }
                        }
                   }
                }
            }
            post{
                always{
                    //上传日志
                    script {
                        if(env.X5MOBILE_RESOURCES_PACK == "true")
                        {
                            post_log('resources')
                        }
                    }
                }
            }
        }

        stage('并行执行打包流程'){
            parallel {
                stage('release'){
                    agent {
                        node {
                            label 'hotfix_release'
                        }
                    }
                    stages{
                        stage('配置初始化'){
                            steps {
                                dir('pyframe-pipeline/project/x5m/art_package/config'){
                                    script{
                                        init_config()
                                    }
                                }
                            }
                        }

                        stage('检查release更新'){
                            steps {
                                dir("pyframe-pipeline/project/x5m/art_package/scripts")
                                {
                                    script {
                                        checkupdate()
                                    }
                                }
                            }
                        }

                        stage('release:获取代码') {
                            when {
                                expression {
                                    return env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"
                                }
                            }
                            steps {
                                retry(2){
                                    script {
                                        timeRetryRelease ++
                                        getCodeRelease()
                                    }
                                }
                            }
                        }

                        //1. 检查新Git仓库
                        stage('获取config仓库'){
                            when {
                                expression {
                                    return env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"
                                }
                            }
                           steps{
                               script{
                                    get_cdn_config()
                               }
                           }
                        }

                        //1. 检查新Git仓库
                        //env.X5MOBILE_RESOURCES_BIZNAMESPACE
                        stage('获取xmlweek仓库'){
                            when {
                                expression {
                                    return env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"
                                }
                            }
                           steps{
                               script{
                                    get_week_config()
                               }
                           }
                        }

                        stage('复制文件到指定目录下'){
                            when {
                                expression {
                                    return env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"
                                }
                            }
                            steps{

                                dir("../x5mconfig")
                                {
                                    script {
                                        env.ErrInfo='release:复制文件到指定目录下失败'
                                        // if (env.X5MOBILE_RESOURCES_BRANCH == '4.14.0'){
                                        //     copy_config('4.12.0')
                                        // }else{
                                        //     copy_config(env.X5MOBILE_RESOURCES_BRANCH)
                                        // }
                                        copy_config(env.X5MOBILE_RELEASE_BRANCH)
                                    }
                                }
                                dir("x5_mobile\\mr\\xmlweek_${env.X5MOBILE_RELEASE_BIZNAMESPACE}\\config"){
                                    deleteDir()
                                }
                                dir("../x5mweek")
                                {
                                    script {
                                         bat label:"拷贝x5mweek config", script: """
                                            git checkout ${env.X5MOBILE_RELEASE_BIZNAMESPACE}
                                            git pull
                                            echo d y| xcopy config  ${env.WORKSPACE}\\x5_mobile\\mr\\xmlweek_${env.X5MOBILE_RELEASE_BIZNAMESPACE}\\config /e /y /r
                                         """
                                    }
                                }
                            }
                        }


                        stage('release:获取cdn res_list.zip'){
                            when {
                                expression {
                                    return env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"
                                }
                            }
                            steps{
                                dir("D:/jenkins/workspace")
                                {
                                    script {
                                        def out = bat(returnStdout: true, script: """
                                        mkdir.exe  --ra=D:/jenkins/workspace/ResourcePublish/3.12.0/SourceFiles/android/${env.RELEASE_WEEKLY_RESOURCE_VERSION}  --ri=D:/jenkins/workspace/ResourcePublish/3.12.0/SourceFiles/iOS/${env.RELEASE_WEEKLY_RESOURCE_VERSION}  --rlv=${env.RES_LIST_VERSION}""")
                                        println(out)
                                    }
                                }
                            }
                        }

                        stage('release: unzip'){
                            when {
                                expression {
                                    return env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"
                                }
                            }
                            steps{
                                dir("${workspace_dir1}/archive/android"){
                                    unzip dir: '.', glob: '', zipFile: "res_list_${env.RES_LIST_VERSION}.zip"
                                    //unzip res_list_${env.RES_LIST_VERSION}.zip
                                    bat label: '拷贝安卓zip包', script: """
                                    move /Y "res_list_${env.RES_LIST_VERSION}.csv" "${workspace_dir2}\\ResourcePublish\\3.12.0\\SourceFiles\\android\\${env.RELEASE_WEEKLY_RESOURCE_VERSION}\\res_list_${env.RES_LIST_VERSION}.csv"
                                    """
                                }
                                dir("${workspace_dir1}/archive/ios"){
                                    unzip dir: '.', glob: '', zipFile: "res_list_${env.RES_LIST_VERSION}.zip"
                                    //unzip res_list_${env.RES_LIST_VERSION}.zip
                                    bat label: '拷贝ios zip包', script: """
                                    move /Y "res_list_${env.RES_LIST_VERSION}.csv" "${workspace_dir2}\\ResourcePublish\\3.12.0\\SourceFiles\\iOS\\${env.RELEASE_WEEKLY_RESOURCE_VERSION}\\res_list_${env.RES_LIST_VERSION}.csv"
                                    """
                                }
                            }
                        }
                        stage('release:执行打包工具'){
                            when {
                                expression {
                                    return env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"
                                }
                            }
                            steps{
                                script{
                                    dir("${env.WORKSPACE}/x5_mobile/gitlab/mobile_dancer/trunk/exe/ResListTool")
                                    {
                                        writeFile encoding: 'GB2312', file: './start.bat', text: "start /wait ResListTool CDNVersion:${env.CDN_VERSION} ServerConfigPath:${env.WORKSPACE}\\x5_mobile\\gitlab\\mobile_dancer\\trunk\\exe\\resources\\config\\server\\ SourcesPath:${env.RELEASE_WEEKLY_RESOURCE_DIR_NAME} BizPath:${env.X5MOBILE_RELEASE_BIZNAMESPACE} HotFixVersion:${env.RELEASE_WEEKLY_RESOURCE_VERSION } HotFixLastVersion:${env.RELEASE_HOTFIX_BASE_VERSION} ResToolPackType:Hotfix"
                                        ret= bat(label:"执行bat文件",returnStatus :true,encoding:'GBK',script:'call start.bat')
                                        if(ret != 0){
                                            env.ErrInfo="release:执行打包工具失败,ret返回值：${ret}"
                                            if(ret == 2) {
                                                env.ErrInfo='release:xml转二进制失败,请查看失败文件'
                                            }
                                            currentBuild.result = 'FAILURE'
                                        }
                                    }
                                }
                            }
                        }

                        stage('release:拷贝热更包和服务器包到ftp服务器'){
                            when {
                                expression {
                                    return env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"
                                }
                            }
                            steps{
                                script{
                                    env.ErrInfo='release:拷贝热更包和服务器包到ftp服务器失败'
                                }
                                dir("${workspace_dir1}/ResourcePublish/3.12.0/ReleaseFiles")
                                {
                                    ftpPublisher    alwaysPublishFromMaster: false,
                                                    continueOnError: false,
                                                    failOnError: false,
                                                    masterNodeName: '',
                                                    paramPublish: null,
                                                    publishers: [[
                                                        configName: '*************',
                                                        transfers: [[
                                                            asciiMode: false,
                                                            cleanRemote: false,
                                                            excludes: '',
                                                            flatten: false,
                                                            makeEmptyDirs: false,
                                                            noDefaultExcludes: false,
                                                            patternSeparator: '[, ]+',
                                                            remoteDirectory: "version_test/resources/android/${env.RELEASE_WEEKLY_RESOURCE_VERSION }",
                                                            remoteDirectorySDF: false,
                                                            removePrefix: "android/${env.RELEASE_WEEKLY_RESOURCE_VERSION }",
                                                            sourceFiles: "android/${env.RELEASE_WEEKLY_RESOURCE_VERSION }/**"
                                                        ],
                                                        [
                                                            asciiMode: false,
                                                            cleanRemote: false,
                                                            excludes: '',
                                                            flatten: false,
                                                            makeEmptyDirs: false,
                                                            noDefaultExcludes: false,
                                                            patternSeparator: '[, ]+',
                                                            remoteDirectory: "version_test/resources/ios/${env.RELEASE_WEEKLY_RESOURCE_VERSION }",
                                                            remoteDirectorySDF: false,
                                                            removePrefix: "ios/${env.RELEASE_WEEKLY_RESOURCE_VERSION }",
                                                            sourceFiles: "ios/${env.RELEASE_WEEKLY_RESOURCE_VERSION }/**"
                                                        ],
                                                        [
                                                            asciiMode: false,
                                                            cleanRemote: false,
                                                            excludes: '',
                                                            flatten: false,
                                                            makeEmptyDirs: false,
                                                            noDefaultExcludes: false,
                                                            patternSeparator: '[, ]+',
                                                            remoteDirectory: 'version_test/server_resources',
                                                            remoteDirectorySDF: false,
                                                            removePrefix: 'server_resources',
                                                            sourceFiles: "server_resources/resources_${env.RELEASE_WEEKLY_RESOURCE_VERSION }.zip"
                                                        ],
                                                        [
                                                            asciiMode: false,
                                                            cleanRemote: true,
                                                            excludes: '',
                                                            flatten: false,
                                                            makeEmptyDirs: false,
                                                            noDefaultExcludes: false,
                                                            patternSeparator: '[, ]+',
                                                            remoteDirectory: 'version_test/update_config/release',
                                                            remoteDirectorySDF: false,
                                                            removePrefix: 'server_resources',
                                                            sourceFiles: "server_resources/resources_${env.RELEASE_WEEKLY_RESOURCE_VERSION }.zip"
                                                        ]],
                                                        usePromotionTimestamp: false,
                                                        useWorkspaceInPromotion: false,
                                                        verbose: true
                                                    ]]
                                }
                            }
                        }
                        stage('release:拷贝config.ini'){
                            steps{
                                dir('pyframe-pipeline/project/x5m/art_package/config'){
                                    script{
                                        config_ini('release')
                                    }
                                }
                           }
                        }
                    }
                    post{
                        always{
                            //上传日志
                            script {
                                if(env.X5MOBILE_RELEASE_IS_HOT_FIX == "true")
                                {
                                    post_log('release')
                                }

                            }
                        }
                    }
                }
                stage('online'){
                    agent {
                        node {
                            label 'hotfix_online'
                        }
                    }
                    stages{
                        stage('配置初始化'){
                            steps {
                                dir('pyframe-pipeline/project/x5m/art_package/config'){
                                    script{
                                        init_config()
                                    }
                                }
                            }
                        }

                        stage('检查online更新'){
                            steps {
                                dir("pyframe-pipeline/project/x5m/art_package/scripts")
                                {
                                    script {
                                        check_online_update()
                                    }
                                }
                            }
                        }


                        //1. 检查新Git仓库
                        stage('获取config仓库'){
                            when {
                                expression {
                                    return env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"
                                }
                            }
                           steps{
                               script{
                                    get_cdn_config()
                               }
                           }
                        }



                        stage('online:获取代码') {
                            when {
                                expression {
                                    return env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"
                                }
                            }
                            steps {
                                retry(2){
                                    script {
                                        timeRetryOnline ++
                                        getCodeOnline()
                                    }
                                }
                            }
                        }

                        //1. 检查新Git仓库
                        //env.X5MOBILE_RESOURCES_BIZNAMESPACE
                        stage('获取xmlweek仓库'){
                            when {
                                expression {
                                    return env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"
                                }
                            }
                           steps{
                               script{
                                    get_week_config()
                               }
                           }
                        }

                        stage('复制文件到指定目录下'){
                              when {
                                expression {
                                    return env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"
                                }
                            }
                            steps{
                                println("env.WORKSPACE:${env.WORKSPACE}")
                                dir("../x5mconfig")
                                {
                                    script {
                                        env.ErrInfo='online:复制文件到指定目录下失败'
                                        // if (env.X5MOBILE_RESOURCES_BRANCH == '4.14.0'){
                                        //     copy_config('4.12.0')
                                        // }else{
                                        //     copy_config(env.X5MOBILE_RESOURCES_BRANCH)
                                        // }
                                        copy_config(env.X5MOBILE_ONLINE_BRANCH)
                                    }
                                }
                                dir("x5_mobile\\mr\\xmlweek_${env.X5MOBILE_ONLINE_BIZNAMESPACE}\\config"){
                                    deleteDir()
                                }
                                dir("../x5mweek")
                                {
                                    script {
                                         bat label:"拷贝x5mweek config", script: """
                                            git checkout ${env.X5MOBILE_ONLINE_BIZNAMESPACE}
                                            git pull
                                            echo d y| xcopy config  ${env.WORKSPACE}\\x5_mobile\\mr\\xmlweek_${env.X5MOBILE_ONLINE_BIZNAMESPACE}\\config /e /y /r
                                         """
                                    }
                                }
                            }
                        }
                        stage('online:获取cdn res_list.zip'){
                            when {
                                expression {
                                    return env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"
                                }
                            }
                            steps{
                                dir("D:/jenkins/workspace")
                                {
                                    script {
                                        def out = bat(returnStdout: true, script: "mkdir.exe --ra=D:/jenkins/workspace/ResourcePublish/3.12.0/SourceFiles/android/${env.ONLINE_WEEKLY_RESOURCE_VERSION} --ri=D:/jenkins/workspace/ResourcePublish/3.12.0/SourceFiles/iOS/${env.ONLINE_WEEKLY_RESOURCE_VERSION} --rlv=${env.RES_LIST_VERSION}")
                                        println(out)
                                    }
                                }
                            }
                        }

                        stage('online: unzip'){
                            when {
                                expression {
                                    return env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"
                                }
                            }
                            steps{
                                dir("${workspace_dir1}/archive/android"){
                                    unzip dir: '.', glob: '', zipFile: "res_list_${env.RES_LIST_VERSION}.zip"
                                    //unzip res_list_${env.RES_LIST_VERSION}.zip
                                    bat label: '拷贝安卓zip包', script: """
                                    move /Y "res_list_${env.RES_LIST_VERSION}.csv" "${workspace_dir2}\\ResourcePublish\\3.12.0\\SourceFiles\\android\\${env.ONLINE_WEEKLY_RESOURCE_VERSION}\\res_list_${env.RES_LIST_VERSION}.csv"
                                    """
                                }
                                dir("${workspace_dir1}/archive/ios"){
                                    unzip dir: '.', glob: '', zipFile: "res_list_${env.RES_LIST_VERSION}.zip"
                                    //unzip res_list_${env.RES_LIST_VERSION}.zip
                                    bat label: '拷贝ios zip包', script: """
                                    move /Y "res_list_${env.RES_LIST_VERSION}.csv" "${workspace_dir2}\\ResourcePublish\\3.12.0\\SourceFiles\\iOS\\${env.ONLINE_WEEKLY_RESOURCE_VERSION}\\res_list_${env.RES_LIST_VERSION}.csv"
                                    """
                                }
                            }
                        }
                        stage('online:执行打包工具'){
                            when {
                                expression {
                                    return env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"
                                }
                            }
                            steps{
                                script{
                                    dir("${env.WORKSPACE}/x5_mobile/gitlab/mobile_dancer/trunk/exe/ResListTool")
                                    {
                                        writeFile encoding: 'GB2312', file: './start.bat', text: "start /wait ResListTool CDNVersion:${env.CDN_VERSION} ServerConfigPath:${env.WORKSPACE}\\x5_mobile\\gitlab\\mobile_dancer\\trunk\\exe\\resources\\config\\server\\ SourcesPath:${env.ONLINE_WEEKLY_RESOURCE_DIR_NAME} BizPath:${env.X5MOBILE_ONLINE_BIZNAMESPACE} HotFixVersion:${env.ONLINE_WEEKLY_RESOURCE_VERSION } HotFixLastVersion:${env.ONLINE_HOTFIX_BASE_VERSION} ResToolPackType:Hotfix"
                                        ret= bat(label:"执行bat文件",returnStatus :true,encoding:'GBK',script:'call start.bat')
                                        if(ret != 0){
                                            currentBuild.result = 'FAILURE'
                                            env.ErrInfo="online:执行打包工具失败,ret返回值：${ret}"
                                            if(ret == 2) {
                                                env.ErrInfo='online:xml转二进制失败,请查看失败文件'
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        stage('online:拷贝热更包和服务器包到ftp服务器'){
                            when {
                                expression {
                                    return env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"
                                }
                            }
                            steps{
                                script{
                                    env.ErrInfo='online:拷贝热更包和服务器包到ftp服务器失败'
                                }
                                dir("${workspace_dir1}/ResourcePublish/3.12.0/ReleaseFiles")
                                {
                                    ftpPublisher    alwaysPublishFromMaster: false,
                                                    continueOnError: false,
                                                    failOnError: true,
                                                    masterNodeName: '',
                                                    paramPublish: null,
                                                    publishers: [[
                                                        configName: '*************',
                                                        transfers: [[
                                                            asciiMode: false,
                                                            cleanRemote: false,
                                                            excludes: '',
                                                            flatten: false,
                                                            makeEmptyDirs: false,
                                                            noDefaultExcludes: false,
                                                            patternSeparator: '[, ]+',
                                                            remoteDirectory: "version_test/resources/android/${env.ONLINE_WEEKLY_RESOURCE_VERSION }",
                                                            remoteDirectorySDF: false,
                                                            removePrefix: "android/${env.ONLINE_WEEKLY_RESOURCE_VERSION }",
                                                            sourceFiles: "android/${env.ONLINE_WEEKLY_RESOURCE_VERSION }/**"
                                                        ],
                                                        [
                                                            asciiMode: false,
                                                            cleanRemote: false,
                                                            excludes: '',
                                                            flatten: false,
                                                            makeEmptyDirs: false,
                                                            noDefaultExcludes: false,
                                                            patternSeparator: '[, ]+',
                                                            remoteDirectory: "version_test/resources/ios/${env.ONLINE_WEEKLY_RESOURCE_VERSION }",
                                                            remoteDirectorySDF: false,
                                                            removePrefix: "ios/${env.ONLINE_WEEKLY_RESOURCE_VERSION }",
                                                            sourceFiles: "ios/${env.ONLINE_WEEKLY_RESOURCE_VERSION }/**"
                                                        ],
                                                        [
                                                            asciiMode: false,
                                                            cleanRemote: false,
                                                            excludes: '',
                                                            flatten: false,
                                                            makeEmptyDirs: false,
                                                            noDefaultExcludes: false,
                                                            patternSeparator: '[, ]+',
                                                            remoteDirectory: 'version_test/server_resources',
                                                            remoteDirectorySDF: false,
                                                            removePrefix: 'server_resources',
                                                            sourceFiles: "server_resources/resources_${env.ONLINE_WEEKLY_RESOURCE_VERSION }.zip"
                                                        ],
                                                        [
                                                            asciiMode: false,
                                                            cleanRemote: true,
                                                            excludes: '',
                                                            flatten: false,
                                                            makeEmptyDirs: false,
                                                            noDefaultExcludes: false,
                                                            patternSeparator: '[, ]+',
                                                            remoteDirectory: 'version_test/update_config/online',
                                                            remoteDirectorySDF: false,
                                                            removePrefix: 'server_resources',
                                                            sourceFiles: "server_resources/resources_${env.ONLINE_WEEKLY_RESOURCE_VERSION }.zip"
                                                        ]],
                                                        usePromotionTimestamp: false,
                                                        useWorkspaceInPromotion: false,
                                                        verbose: true
                                                    ]]
                                }

                            }
                        }
                        stage('online:拷贝config.ini'){
                            steps{
                                dir('pyframe-pipeline/project/x5m/art_package/config'){
                                    script{
                                        config_ini('online')
                                    }
                                }
                           }
                        }
                    }
                    post{
                        always{
                            //上传日志
                            script {
                                if(env.X5MOBILE_ONLINE_IS_HOT_FIX == "true")
                                {
                                    post_log('online')
                                }
                                else if(env.X5MOBILE_RELEASE_IS_HOT_FIX == "false" && env.X5MOBILE_RESOURCES_PACK == "false")
                                {
                                    currentBuild.result = 'ABORTED'
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            script {
                def msg = """**结果**: 打包失败
**日志链接**: http://jenkins-x5mobile.h3d.com.cn/job/CDN_And_HotFix/${env.BUILD_NUMBER}/console"""
                msg += """
<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>
"""
                workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=14457620-a0c0-45ce-9220-584c19b1dfa7',content: msg
            }
        }
        success {
            script{
                def msg = """**结果**: 打包成功\n"""
                if (env.X5MOBILE_RESOURCES_PACK == "true"){
                    msg += """**Source环境下载地址**：\n"""
                    if (env.X5MOBILE_RES_TOOL_PACK_TYPE == "All" || env.X5MOBILE_RES_TOOL_PACK_TYPE == "Cdn"){
                        msg += """[CDN](https://dgmdd.h3d.com.cn/dgm/version_test/dynamic)
[CDN Config](https://dgmdd.h3d.com.cn/dgm/version_test/server_resources/cdn_${env.CDN_VERSION}.zip)
**CDN 版本号**: ${env.CDN_VERSION}
**原始资源版本号**: ${env.CDN_RAW_RESOURCE_CHANGELIST}
**CDN changelist**: ${env.CDN_SOURCE_CHANGE_LIST}\n"""
                    }
                    if (env.X5MOBILE_RES_TOOL_PACK_TYPE == "All"){ msg += """**''''''''''''''''''''''''''''''''''''''''''''''**\n"""}
                    if (env.X5MOBILE_RES_TOOL_PACK_TYPE == "All" || env.X5MOBILE_RES_TOOL_PACK_TYPE == "Hotfix"){
                        msg += """[服务器包](https://dgmdd.h3d.com.cn/dgm/version_test/server_resources/resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip)
[热更包_Android](https://dgmdd.h3d.com.cn/dgm/version_test/resources/android/${env.RESOURCES_WEEKLY_RESOURCE_VERSION }/)
[热更包_ios](https://dgmdd.h3d.com.cn/dgm/version_test/resources/ios/${env.RESOURCES_WEEKLY_RESOURCE_VERSION }/)
**热更版本号**: ${env.RESOURCES_WEEKLY_RESOURCE_VERSION}
**热更 changelist**: ${env.RESOURCES_HOTFIX_CHANGE_LIST}
**配置 pipelineid**: ${env.RESOURCES_CONFIG_PIPELINE_ID}\n"""
                    }
                }
                if (env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"){
                    if (env.X5MOBILE_RESOURCES_PACK == "true"){
                        msg += """**''''''''''''''''''''''''''''''''''''''''''''''**\n"""
                }
                    msg += """**Release环境下载地址**：
[服务器包](https://dgmdd.h3d.com.cn/dgm/version_test/server_resources/resources_${env.RELEASE_WEEKLY_RESOURCE_VERSION }.zip)
[热更包_Android](https://dgmdd.h3d.com.cn/dgm/version_test/resources/android/${env.RELEASE_WEEKLY_RESOURCE_VERSION }/)
[热更包_ios](https://dgmdd.h3d.com.cn/dgm/version_test/resources/ios/${env.RELEASE_WEEKLY_RESOURCE_VERSION }/)
**热更版本号**: ${env.RELEASE_WEEKLY_RESOURCE_VERSION}
**热更 changelist**: ${env.RELEASE_HOTFIX_CHANGE_LIST}
**配置 pipelineid**: ${env.RELEASE_CONFIG_PIPELINE_ID}\n"""
                }
                if (env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"){
                    if (env.X5MOBILE_RESOURCES_PACK == "true" || env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"){msg += """**''''''''''''''''''''''''''''''''''''''''''''''**\n"""}
                    msg+= """**Online环境下载地址**:
[服务器包](https://dgmdd.h3d.com.cn/dgm/version_test/server_resources/resources_${env.ONLINE_WEEKLY_RESOURCE_VERSION }.zip)
[热更包_Android](https://dgmdd.h3d.com.cn/dgm/version_test/resources/android/${env.ONLINE_WEEKLY_RESOURCE_VERSION }/)
[热更包_ios](https://dgmdd.h3d.com.cn/dgm/version_test/resources/ios/${env.ONLINE_WEEKLY_RESOURCE_VERSION }/)
**热更版本号**: ${env.ONLINE_WEEKLY_RESOURCE_VERSION}
**热更 changelist**: ${env.ONLINE_HOTFIX_CHANGE_LIST}
**配置 pipelineid**: ${env.ONLINE_CONFIG_PIPELINE_ID}\n"""
                }
                if (env.X5MOBILE_RESOURCES_PACK == "true" || env.X5MOBILE_RELEASE_IS_HOT_FIX == "true" || env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"){
                    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=99072e8c-c267-411c-b063-e8b1b6495847',content: msg
                }
            }
        }
    }
}
