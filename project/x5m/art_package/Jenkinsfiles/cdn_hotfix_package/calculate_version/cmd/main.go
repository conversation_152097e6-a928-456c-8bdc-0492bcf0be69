package main

import (
	"calculate_version/pkg/cmd"
)
//var cdnPackage = flag.String("package", "true", "是否打cdn包")
func main() {
	cmd.Execute()
	//flag.Parse()
	//ftpConn, err := ftp.Connect("172.17.150.30:21")
	//if err != nil {
	//	log.Fatal("建立ftp连接失败！err=", err)
	//}
	//defer ftpConn.Quit()
	//err = ftpConn.Login("administrator", "dgm123!#*")
	//if err != nil {
	//	log.Fatal("登录ftp服务器失败！err=", err)
	//}
	//// 计算CDN
	//CDNFileList, err := utils.FindFtpFileList("version_test/dynamic/android/", ftpConn)
	//if err != nil {
	//	log.Fatal("找CDN版本号出错!err=", err)
	//}
	//if len(CDNFileList) == 0 {
	//	log.Fatal("不存在CDN包!err=", err)
	//}
	//firstMax, secondMax, err := utils.FindMaxCdnVersion(CDNFileList)
	//if err != nil {
	//	log.Fatal(err)
	//}
	//var cdnVersion, resListVersion string
	//if *cdnPackage == "true" {
	//	cdnVersion = fmt.Sprintf("v%dv%d", firstMax, secondMax+1)
	//	resListVersion = fmt.Sprintf("v%d_v%d", firstMax, secondMax+1)
	//} else {
	//	cdnVersion = fmt.Sprintf("v%dv%d", firstMax, secondMax)
	//	resListVersion = fmt.Sprintf("v%d_v%d", firstMax, secondMax)
	//}
	//
	//cdnBaseVersions, err := utils.ExecuteP4DirsCommand("//x5_mobile/mr/art_release/pack/dress/*")
	//
	//firstMax, secondMax, err = utils.FindMaxCdnVersion(cdnBaseVersions)
	//if err != nil {
	//	log.Fatal(err)
	//}
	//
	//cdnBaseVersion := fmt.Sprintf("v%dv%d", firstMax, secondMax)
	//
	//fmt.Printf("%s\n%s\n%s\n", cdnVersion, cdnBaseVersion, resListVersion)

}

