package cmd

import (
	Config "calculate_version/pkg/config"
	"calculate_version/pkg/utils"
	"fmt"
	"github.com/jlaffaye/ftp"
	"github.com/spf13/cobra"
	"log"
	"sort"
	"strconv"
	"strings"
	"time"
)

var namespace *string
var isSwitch *bool
var hotfixCmd = &cobra.Command{
	Use:  "hotfix",
	Long: `计算hotfix版本号`,
	Run:  CalculateHotfixVersion,
}

func init() {
	rootCmd.AddCommand(hotfixCmd)
	namespace = hotfixCmd.PersistentFlags().String("namespace", "resources", "打包的环境resources，release，online三种")
	isSwitch = hotfixCmd.PersistentFlags().Bool("switch", true, "是否切换")
}

func CalculateHotfixVersion(cmd *cobra.Command, args []string) {
	configPath := "config/config.json"
	config := new(Config.Config)
	// 读取配置文件
	err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatal(err)
	}

	defer func() {
		err = config.ArchiveConfig(configPath)
		if err != nil {
			log.Fatal(err)
		}
	}()
	//先看今天周几 周一或者周四 要切换版本
	//以resources分支的版本为标的物
	var isNeedSwitch = true
	today := time.Now().Weekday()
	if today == time.Monday || today == time.Thursday {
		if !config.IsModify {
			config.IsModify = true
			if config.SwitchedVersionTime>= 2 {
				if *namespace == "resources"{
					isNeedSwitch,err = utils.CalculateResourceNextVersion(config)
					if err != nil {
						log.Fatal(err)
					}
				}else{
					config.IsSwitch = *isSwitch
					err := utils.CalculateReleaseAndOnlineNextVersion(config)
					if err != nil {
						log.Fatal(err)
					}
				}
				config.SwitchedVersionTime = 1
			} else {
				isNeedSwitch = false
				config.SwitchedVersionTime++
			}

		} else{
			isNeedSwitch = false
		}
	} else {
		// 如果不是周一或者周四 那么不需要切换版本
		isNeedSwitch = false
		config.IsModify = false
	}

	// 记录cdn是否需要打包  热更是否需要打包
	cdnIsPack := true
	hotfixIsPack := true

	majorVersion := config.MajorVersion
	weeklyTimes := config.WeeklyTime

	ftpConn, err := ftp.Connect("172.17.150.30:21")
	if err != nil {
		log.Fatal("建立ftp连接失败！err=", err)
	}
	defer ftpConn.Quit()
	err = ftpConn.Login("administrator", "dgm123!#*")
	if err != nil {
		log.Fatal("登录ftp服务器失败！err=", err)
	}
	var weeklyResourceDirName string
	var weeklyDirVersions []string

	// 1. 查看p4上有没有文件夹
	hotFixVersions, err := utils.ExecuteP4FilesCommand(fmt.Sprintf("//x5_mobile/mr/art_release/pack/hotfix/%s*", strings.Replace(majorVersion, ".", "", -1)+fmt.Sprintf(".%s", weeklyTimes)))
	if err != nil {
		weeklyResourceDirName = fmt.Sprintf("%s.%s01000", strings.Replace(majorVersion, ".", "", -1), weeklyTimes)
	} else {
		for _, version := range hotFixVersions {
			if strings.Contains(version, fmt.Sprintf("%s.%s", strings.Replace(majorVersion, ".", "", -1), weeklyTimes)) {
				weeklyDirVersions = append(weeklyDirVersions, version)
			}
		}
		if len(weeklyDirVersions) == 0 {
			weeklyResourceDirName = fmt.Sprintf("%s.%s01000", strings.Replace(majorVersion, ".", "", -1), weeklyTimes)
		} else {
			var hotFixMaxVersion float64
			for _, version := range weeklyDirVersions {
				//lastIndex := strings.LastIndex(version, "/")
				versionFloat, err := strconv.ParseFloat(version, 64)
				if err != nil {
					continue
				}
				if versionFloat > hotFixMaxVersion {
					hotFixMaxVersion = versionFloat
				}
			}
			// 查找p4周更目录  看看是否有比hotFixMaxVersion高的
			fileNames, err := utils.ExecuteP4DirsCommand(fmt.Sprintf("//x5_mobile/mr/onlineupdate/%s/%s*", majorVersion, strings.Replace(majorVersion, ".", "", -1)+fmt.Sprintf(".%s", weeklyTimes)))
			if err != nil {
				hotfixIsPack = false
				weeklyResourceDirName = fmt.Sprintf("%s.%s01000", strings.Replace(majorVersion, ".", "", -1), weeklyTimes)
			}
			var fl []float64
			for _, fileName := range fileNames {
				if len(fileName) != 11 {
					continue
				}

				versionFloat, err := strconv.ParseFloat(fileName, 64)
				if err != nil {
					continue
				}
				if versionFloat > hotFixMaxVersion {
					fl = append(fl, versionFloat)
				}
			}
			if len(fl) == 0 {
				//hotfixIsPack = false
				weeklyResourceDirName = fmt.Sprintf("%s.%s01000", strings.Replace(majorVersion, ".", "", -1), weeklyTimes)
			} else {
				sort.Float64s(fl)
				weeklyResourceDirName = fmt.Sprintf("%f", fl[0])
				if len(weeklyResourceDirName) != 11 && len(weeklyResourceDirName) == 8 {
					weeklyResourceDirName = weeklyResourceDirName + "000"
				}
			}

		}
	}

	// 计算周更版本号参数
	floderNameList, err := utils.FindFtpFolderList("version_test/resources/android/", ftpConn)
	if err != nil {
		log.Fatal(err)
	}

	maxWeeklyVersionFloat := 0.0
	for _, floderName := range floderNameList {
		if len(floderName) != 11 || !strings.Contains(floderName, weeklyResourceDirName[:8]) {
			continue
		}
		weeklyVersionFloat, err := strconv.ParseFloat(floderName, 64)
		if err != nil {
			continue
		}

		if weeklyVersionFloat > maxWeeklyVersionFloat {
			maxWeeklyVersionFloat = weeklyVersionFloat
		}
	}
	var weeklyVersion string

	if maxWeeklyVersionFloat == 0.0 {
		//fmt.Println(weeklyResourceDirName[:8])
		weeklyVersion = weeklyResourceDirName[:8] + "000"
	} else {
		weeklyVersion = fmt.Sprintf("%f", maxWeeklyVersionFloat+0.000001)
	}
	var hotFixBaseVersion string
	hotFixVersions, err = utils.ExecuteP4FilesCommand(fmt.Sprintf("//x5_mobile/mr/art_release/pack/hotfix/%s*", strings.Replace(majorVersion, ".", "", -1)))
	if err != nil {
		hotFixBaseVersion = fmt.Sprintf("%s.000000", strings.Replace(majorVersion, ".", "", -1))
	} else {
		// 计算热更base
		weeklyVersionFloat, err := strconv.ParseFloat(weeklyVersion, 64)
		if err != nil {
			log.Fatal(err)
		}
		var closestVersions []float64
		for _, version := range hotFixVersions {
			versionFloat, err := strconv.ParseFloat(version, 64)
			if err != nil {
				continue
			}

			if versionFloat < weeklyVersionFloat {
				closestVersions = append(closestVersions, versionFloat)
			}
		}

		sort.Float64s(closestVersions)
		if len(closestVersions) == 0 {
			hotFixBaseVersion = fmt.Sprintf("%s.000000", strings.Replace(majorVersion, ".", "", -1))
		} else {
			hotFixBaseVersion = fmt.Sprintf("%f", closestVersions[len(closestVersions)-1])
		}
	}

	// 查看p4 热更目录的changelist
	hotFixChangeList, err := utils.ExecuteP4ChangeListCommand(fmt.Sprintf("//x5_mobile/mr/onlineupdate/%s/%s/...", majorVersion, weeklyResourceDirName))
	if err != nil {
		log.Fatal(err)
	}
	fetterChangeList, err := utils.ExecuteP4ChangeListCommand("//x5_mobile/mr/Resources/美术资源/UI/羁绊背景图/...")
	if err != nil {
		log.Fatal(err)
	}
	cdnSourceAndroidChangeList, err := utils.ExecuteP4ChangeListCommand("//x5_mobile/mr/art_release/cs/android/...")
	if err != nil {
		log.Fatal(err)
	}
	cdnSourceIosChangeList, err := utils.ExecuteP4ChangeListCommand("//x5_mobile/mr/art_release/cs/ios/...")
	if err != nil {
		log.Fatal(err)
	}
	csConfigChangeList, err := utils.ExecuteP4ChangeListCommand("//x5_mobile/mr/art_release/cs/config/...")
	if err != nil {
		log.Fatal(err)
	}

	serverConfigChangeList := 0

	// 服务器配置changelist
	cl, err := utils.ExecuteP4ChangeListCommand(fmt.Sprintf("//x5_mobile/mr/xmlweek_%s/...", *namespace))
	if err != nil {
		log.Fatal(err)
	}
	if cdnSourceAndroidChangeList <= config.CdnSourceChangeList && cdnSourceIosChangeList <= config.CdnSourceChangeList {
		cdnIsPack = false
	}
	if cl <= config.ChangeList && hotFixChangeList <= config.HotFixChangeList && fetterChangeList <= config.FetterChangeList &&
		csConfigChangeList <= config.CsConfigChangeList {
		hotfixIsPack = false
	}
	if *namespace == "release" && hotfixIsPack && config.SwitchedVersionTime == 2 {
		hotfixIsPack = false
	}
	if config.ChangeList < cl {
		config.ChangeList = cl
	}
	serverConfigChangeList = cl

	if cdnSourceAndroidChangeList > config.CdnSourceChangeList || cdnSourceIosChangeList > config.CdnSourceChangeList {
		if cdnSourceAndroidChangeList > cdnSourceIosChangeList {
			config.CdnSourceChangeList = cdnSourceAndroidChangeList
		} else {
			config.CdnSourceChangeList = cdnSourceIosChangeList
		}
	}
	if fetterChangeList > config.FetterChangeList {
		config.FetterChangeList = fetterChangeList
	}
	if hotFixChangeList > config.HotFixChangeList {
		config.HotFixChangeList = hotFixChangeList
	}
	if csConfigChangeList > config.CsConfigChangeList {
		config.CsConfigChangeList = csConfigChangeList
	}

	if *namespace == "resources" {
		fmt.Printf("%s\n%s\n%t\n%t\n%s\n%s\n%s\n", majorVersion, *namespace, cdnIsPack, hotfixIsPack, weeklyResourceDirName, weeklyVersion, hotFixBaseVersion)
		fmt.Printf("%d\n%d\n%d\n%t\n", config.CdnSourceChangeList, hotFixChangeList, serverConfigChangeList,isNeedSwitch)
	} else {
		fmt.Printf("%s\n%s\n%t\n%s\n%s\n%s\n", majorVersion, *namespace, hotfixIsPack, weeklyResourceDirName, weeklyVersion, hotFixBaseVersion)
		fmt.Printf("%d\n%d\n", hotFixChangeList, serverConfigChangeList)
	}
}
