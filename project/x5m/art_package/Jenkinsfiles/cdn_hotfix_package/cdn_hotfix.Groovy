def check_resources_update(){
    String[] out = bat(returnStdout: true, script: "python setup.py calculate-hotfix-versions --namespace resources").split('\n')
             def result_map = readJSON text: out[2]
    println(result_map)

    env.X5MOBILE_RESOURCES_BRANCH=result_map['major_version']
    env.X5MOBILE_RESOURCES_BIZNAMESPACE=result_map['namespace']
    env.X5MOBILE_RESOURCES_PACK = "true"
    if (result_map['hotfix_package'] == "False" && result_map['cdn_package'] == "False"){
        env.X5MOBILE_RESOURCES_PACK = "false"
    }else if(result_map['cdn_package'] == "False" && result_map['hotfix_package'] == "True"){
        env.X5MOBILE_RES_TOOL_PACK_TYPE = "Hotfix"
    }else if(result_map['cdn_package'] == "True" && result_map['hotfix_package'] == "False"){
        env.X5MOBILE_RES_TOOL_PACK_TYPE = "Cdn"
    }else{
        env.X5MOBILE_RES_TOOL_PACK_TYPE = "All"
    }
    if (params.resourceForcePackage){
        if(result_map['cdn_package'] == "False"){
            env.X5MOBILE_RES_TOOL_PACK_TYPE = "Hotfix"
        }
        else{
            env.X5MOBILE_RES_TOOL_PACK_TYPE = "All"
        }
        env.X5MOBILE_RESOURCES_PACK = "true"
    }
    env.CDN_RAW_RESOURCE_CHANGELIST = result_map['cdn_raw_resource_changelist']
    env.RESOURCES_WEEKLY_RESOURCE_DIR = result_map['weekly_resource']
    env.RESOURCES_WEEKLY_RESOURCE_DIR_NAME ="${env.WORKSPACE}\\x5_mobile\\mr\\onlineupdate\\${env.X5MOBILE_RESOURCES_BRANCH}\\${result_map['weekly_resource']}\\client\\"
    env.RESOURCES_WEEKLY_RESOURCE_VERSION = result_map['weekly_version']
    env.RESOURCES_HOTFIX_BASE_VERSION = result_map['hotfix_base_version']
    env.CDN_SOURCE_CHANGE_LIST =  result_map['cdn_source_change_list']
    env.RESOURCES_HOTFIX_CHANGE_LIST = result_map['hotfix_changelist']
    env.RESOURCES_CONFIG_PIPELINE_ID = result_map['config_pipeline_id']
    String[] CDN_Versions = bat(returnStdout: true, script: "python setup.py cdn-package --package ${result_map['cdn_package']}").split("\n")
    def cdn_result_map = readJSON text: CDN_Versions[2]
    println(cdn_result_map)
    env.CDN_VERSION = cdn_result_map['cdn_version']
    env.CDN_BASE_VERSION = cdn_result_map['cdn_base_version']
    env.RES_LIST_VERSION = cdn_result_map['res_list_version']
}


def getCode(x5mobile_env_branch,weekly_resource_dir,p4_revision,jenkins_node,judge,cdn_base_version)
{
    env.ErrInfo='��ȡ����ʧ��'
    if (judge == true){
        dir("pyframe-pipeline/project/x5m/art_package/scripts")
        {
            bat(script: "python setup.py clear-config --folder_path ${env.WORKSPACE}\\x5_mobile\\mr\\onlineupdate\\${env.X5MOBILE_RESOURCES_BRANCH}")
        }
    }
    clear_config(x5mobile_env_branch,weekly_resource_dir)
    getToolAndConfigFromGitlab(x5mobile_env_branch)
    def v = getServerHotFixAndCDNP4View(x5mobile_env_branch,judge,cdn_base_version)
    checkout perforce(credential: 'p4_dgm_jenkins_upwd',
        populate: syncOnly(force: "${params.force}", have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: p4_revision, quiet: false, revert: false),
        workspace: manualSpec(
            charset: 'none',
            cleanup: false,
            name: jenkins_node,
            pinHost: false,
            spec: clientSpec(
                allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
                locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: v
            )
        )
    )
}


def getToolAndConfigFromGitlab(branch){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/${branch}"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile/gitlab'],
    [$class: 'SparseCheckoutPaths', sparseCheckoutPaths: [[path: 'mobile_dancer/trunk/exe/ResListTool']]]],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm-package-gitlab-sshpk',
    url: '******************************:dgm/x5mobile.git']]]
}


def getServerHotFixAndCDNP4View(branchName,isResource,cdnBaseVersion) {
    def tpl = '''
    //x5_mobile/mr/art_release/pack/hotfix/... //${p4Client}/x5_mobile/mr/art_release/pack/hotfix/...
    //x5_mobile/mr/art_release/art_src/fetter_cg/... //${p4Client}/x5_mobile/mr/Resources/������Դ/UI/���ͼ/...
    //x5_mobile/mr/onlineupdate/... //${p4Client}/x5_mobile/mr/onlineupdate/...
    //x5_mobile/mr/onlineupdate/${branchName}/version.xml //${p4Client}/x5_mobile/mr/onlineupdate/${branchName}/version.xml
    '''
    if(isResource)
    {
        tpl+='''
        //x5m/res/cdn/pack/${cdnBaseVersion}/... //${p4Client}/x5_mobile/mr/art_release/pack/dress/${cdnBaseVersion}/...
        //x5m/res/cdn/cooked/android/... //${p4Client}/x5_mobile/mr/art_release/cs/android/...
        //x5m/res/cdn/cooked/ios/... //${p4Client}/x5_mobile/mr/art_release/cs/ios/...
        '''
    }

    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([branchName: branchName, p4Client: '${P4_CLIENT}',cdnBaseVersion:cdnBaseVersion]).toString()
}

def checkupdate(){
    def out = bat(returnStdout: true, script: "python setup.py calculate-hotfix-versions --namespace release").split('\n')
    def result_map = readJSON text: out[2]
    println(result_map)
    env.X5MOBILE_RELEASE_BRANCH=result_map['major_version']
    env.X5MOBILE_RELEASE_BIZNAMESPACE=result_map['namespace']
    if (result_map['hotfix_package'] == 'True' || params.releaseForcePackage){
        env.X5MOBILE_RELEASE_IS_HOT_FIX = 'true'
    }else{
        env.X5MOBILE_RELEASE_IS_HOT_FIX = 'false'
    }
    env.RELEASE_WEEKLY_RESOURCE_DIR = result_map['weekly_resource']
    env.RELEASE_WEEKLY_RESOURCE_DIR_NAME ="${env.WORKSPACE}\\x5_mobile\\mr\\onlineupdate\\${env.X5MOBILE_RELEASE_BRANCH}\\${result_map['weekly_resource']}\\client\\"
    env.RELEASE_WEEKLY_RESOURCE_VERSION = result_map['weekly_version']
    env.RELEASE_HOTFIX_BASE_VERSION = result_map['hotfix_base_version']
    env.RELEASE_HOTFIX_CHANGE_LIST = result_map['hotfix_changelist']
    env.RELEASE_CONFIG_PIPELINE_ID = result_map['config_pipeline_id']
}


def clear_config(branch,version){
    dir("x5_mobile/mr/art_release/cs/config"){
        deleteDir()
    }
    dir("x5_mobile/mr/onlineupdate/${branch}/${version}/client/crossplatform/config"){
        deleteDir()
    }
    dir("x5_mobile/mr/onlineupdate/${branch}/${version}/server/resources"){
        deleteDir()
    }
    dir("x5_mobile/gitlab/mobile_dancer/trunk/exe/resources"){
        deleteDir()
    }
}


def copy_config(branch){
    git_ret = bat(label:"git status", returnStatus:true, encoding:'UTF-8', script: """
        git status
        """)

    println("git_ret: ${git_ret}")
    if(git_ret != 0) {
        error 'git occurred error'
    }

    bat label:"����config",script: """
        git fetch --all
        git reset --hard
        git checkout cdn
        git pull
        echo d y| xcopy config  ${env.WORKSPACE}\\x5_mobile\\mr\\art_release\\cs\\config /e /y /r /q
        git fetch --all
        git reset --hard
        git checkout hotfix
        git pull
        echo d y| xcopy onlineupdate ${env.WORKSPACE}\\x5_mobile\\mr\\onlineupdate /e /y /r /q
        git fetch --all
        git reset --hard
        git checkout ${branch}
        git pull
        echo d y| xcopy config\\server\\h3d ${env.WORKSPACE}\\x5_mobile\\gitlab\\mobile_dancer\\trunk\\exe\\resources\\config\\server\\h3d /e /y /r /q"""
}


def init_config(type){
     env.ErrInfo="���ó�ʼ��ʧ��"
     def tmp_path =env.WORKSPACE.replace("\\",'/')
     def configFile = "config.ini"
     def configExampleFile = "config.ini.example"
     def copyConfigFile = "${tmp_path}/config.ini"
     def configExists = fileExists "$configFile"
     def copyConfigExists = fileExists "$copyConfigFile"
     def exampleExists = fileExists "$configExampleFile"
     println("���ó�ʼ�����ļ��Ƿ����, configExists:${configExists}, exampleExists:${exampleExists}")
     if (!configExists) {
        bat label: "��¡���òֿ�",script: """
        cd ${env.WORKSPACE}/pyframe-pipeline/project/x5m/art_package/config
        git clone -b cdn-${type} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
        """
         // ������
//          if(copyConfigExists) {
//              // �����滻
//             bat label: '���¿���config����', script: """
//             copy ${env.WORKSPACE}\\config.ini ${env.WORKSPACE}\\gitlab\\Script\\config\\config.ini
//             """
//          }
//          else{
//              if(exampleExists){
//                 // ����
//                 fileOperations([fileRenameOperation(destination: "${configFile}", source: "${configExampleFile}")])
//              }
//          }
     }
     bat """
     cd ${env.WORKSPACE}/pyframe-pipeline/project/x5m/art_package/config
     git clean -xdf
     git reset --hard HEAD
     git pull origin cdn-${type}
     """
}


def config_ini(namespace){
    env.ErrInfo="${namespace}: ����config.iniʧ��"
    def tmp_path =env.WORKSPACE.replace("\\",'/')
    def copyConfigFile = "${tmp_path}/config.ini"
    def copyConfigExists = fileExists "$copyConfigFile"
    if(copyConfigExists) {
        bat label: '${namespace}: ɾ����һ��config ����', script: """
        del /f /q ${env.WORKSPACE}\\config.ini
        """
    }
    bat label: '${namespace}: ���¿���config����', script: """
        copy ${env.WORKSPACE}\\gitlab\\Script\\config\\config.ini ${env.WORKSPACE}\\config.ini
        """
}


def copy_x5mweek_config(namespace){
    bat label:"����x5mweek config", script: """
        git checkout ${namespace}
        git pull
        echo d y| xcopy config  ${env.WORKSPACE}\\x5_mobile\\mr\\xmlweek_${namespace}\\config /e /y /r /q
     """
}

def unzip(platform,Platform,resource_version){
    def workspace_dir1="D:/cdn_hotfix_zip/workspace"
    def workspace_dir2="D:\\cdn_hotfix_zip\\workspace"
    dir("${workspace_dir1}/archive/${platform}"){
        unzip dir: '.', glob: '', zipFile: "res_list_${env.RES_LIST_VERSION}.zip"
        bat label: '����${platform}��zip��', script: """
        move /Y "res_list_${env.RES_LIST_VERSION}.csv" "${workspace_dir2}\\ResourcePublish\\3.12.0\\SourceFiles\\${Platform}\\${resource_version}\\res_list_${env.RES_LIST_VERSION}.csv"
        """
    }
}


def execute_packaging_tool(text_info){
    dir("${env.WORKSPACE}/x5_mobile/gitlab/mobile_dancer/trunk/exe/ResListTool")
    {
        writeFile encoding: 'GB2312', file: './start.bat', text: text_info
        ret= bat(label:"ִ��bat�ļ�",returnStatus :true,encoding:'GBK',script:'call start.bat')
        if(ret != 0){
            if(ret == 2) {
                env.ErrInfo='xmlת������ʧ��,��鿴ʧ���ļ�'
            } else if (ret == 1002) {
                env.ErrInfo='������Ŀ¼������'
            } else {
                env.ErrInfo="ִ�д������ʧ��,ret����ֵ��${ret}, �ͼݰ�æ�鿴�˴���<@<EMAIL>>"
            }
            currentBuild.result = 'FAILURE'
//            currentBuild.result = 'ABORTED'
            error("${env.ErrInfo}")
        }
    }
}


def copy_cdn_hotfix_server(){
    def zip_dir1="D:/file_services/version_test"
    def zip_dir2="D:\\file_services\\version_test"
    def workspace_dir1="D:/cdn_hotfix_zip/workspace"
    def workspace_dir2="D:\\cdn_hotfix_zip\\workspace"
    if(env.X5MOBILE_RES_TOOL_PACK_TYPE == "Cdn" || env.X5MOBILE_RES_TOOL_PACK_TYPE == "All"){
        bat label: '����cdn����������', script: """
        unzip ${env.CDN_VERSION}.zip -d ${zip_dir1}/dynamic
        copy "cdn_${env.CDN_VERSION}.zip" "${zip_dir1}/update_config/fix_cdn_config/cdn_config.zip"
        move /Y "cdn_${env.CDN_VERSION}.zip" "${zip_dir1}/server_resources/cdn_${env.CDN_VERSION}.zip"
        curl -d "bk_app_code=x5m-web-devops&bk_app_secret=48f9c7d9-3dba-4d40-9fa1-ba797749ea8b&bk_username=<EMAIL>&bk_biz_id=4&bk_job_id=1000055" "https://bk.h3d.com.cn/api/c/compapi/v2/job/execute_job/"
        """
    }
    if(env.X5MOBILE_RES_TOOL_PACK_TYPE == "Hotfix" || env.X5MOBILE_RES_TOOL_PACK_TYPE == "All"){
        bat label: '�����ȸ���', script: """
        rmdir /S /Q "${zip_dir1}/resources/android/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}"
        rmdir /S /Q "${zip_dir1}/resources/ios/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}"
        move /Y "${workspace_dir1}/ResourcePublish/3.12.0/ReleaseFiles/android/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}" "${zip_dir1}/resources/android/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}"
        move /Y "${workspace_dir1}/ResourcePublish/3.12.0/ReleaseFiles/ios/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}" "${zip_dir1}/resources/ios/${env.RESOURCES_WEEKLY_RESOURCE_VERSION}"
        """
        bat label: '������������', script: """
        copy "${workspace_dir2}\\ResourcePublish\\3.12.0\\ReleaseFiles\\server_resources\\resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip" "${zip_dir1}/update_config/source/resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip"
        move /Y "${workspace_dir2}\\ResourcePublish\\3.12.0\\ReleaseFiles\\server_resources\\resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip" "${zip_dir2}\\server_resources\\resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip"
        rmdir /S /Q "${zip_dir1}/update_config/source_wb"
        mkdir "${zip_dir1}/update_config/source_wb"
        copy "${zip_dir1}\\update_config\\source\\resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip" "${zip_dir1}\\update_config\\source_wb\\resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip"
        """
    }
}



def get_week_config(){
      def  getRepo =false
      if (!fileExists('../x5mweek/.git')) {
          echo "x5mweek/.git �ļ��в�����"
          getRepo = true
      } else {
          dir('../x5mweek') {
              //����Ƿ������ֿ�
             ret= bat(label:"���Git�ֿ�",returnStatus :true,encoding:'UTF-8',script:'git branch')
             //�������������git�ֿ��������ش���
             if(ret !=0) {
                getRepo = true;
                echo  "����������git�ֿ���Ҫǿ�Ƹ���"
             }
             else{
                 bat label:"�л���֧",script:"""
              git checkout ${env.X5MOBILE_RESOURCES_BIZNAMESPACE}
              git pull"""
             }
          }
      }
      if(getRepo.toBoolean()){
          dir('..') {
              //�״��������ش���
              def weekly_url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mweek.git"
              env.ErrInfo='��������Git�ֿ�ʧ��'
              bat label:"��¡�²ֿ�",script:"""
              git clone -b ${env.X5MOBILE_RESOURCES_BIZNAMESPACE} ${weekly_url}
              """
          }
      }
}


def get_cdn_config(){
      def  getRepo =false
      if (!fileExists('../x5mconfig/.git')) {
          echo "x5mconfig/.git �ļ��в�����"
          getRepo = true
      } else {
          dir('../x5mconfig') {
              //����Ƿ������ֿ�
             ret= bat(label:"���Git�ֿ�",returnStatus :true,encoding:'UTF-8',script:'git branch')
             //�������������git�ֿ��������ش���
             if(ret !=0) {
                getRepo = true;
                echo  "����������git�ֿ���Ҫǿ�Ƹ���"
             }
          }
      }
      if(getRepo.toBoolean()){
          dir('..') {
              //�״��������ش���
              def config_url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig.git"
              env.ErrInfo='��������Git�ֿ�ʧ��'
              bat label:"��¡�²ֿ�",script:"""
              git clone -b cdn ${config_url}
              """
          }
      }
}


def check_online_update(){
    def out = bat(returnStdout: true, script: "python setup.py calculate-hotfix-versions --namespace online").split('\n')
    def result_map = readJSON text: out[2]
    println(result_map)
    white_list = ['5122.0402000']
    env.X5MOBILE_ONLINE_BRANCH=result_map['major_version']
    env.X5MOBILE_ONLINE_BIZNAMESPACE=result_map['namespace']
    def weekly_resource = result_map['weekly_resource']
    if ((result_map['hotfix_package'] == 'True' || params.onlineForecePackage) && !white_list.contains(weekly_resource)){
        out = bat(returnStdout: true, script: "python setup.py allow-online-packages").split('\n')
        result_map1 = readJSON text: out[2]
        if (result_map1['allow_package'] == 'true' ){
            env.X5MOBILE_ONLINE_IS_HOT_FIX = 'true'
        } else {
            env.X5MOBILE_ONLINE_IS_HOT_FIX = 'false'
            env.ErrInfo = "x5mweek�ֿ���online��֧û��release to  online��merge��¼������ϵpm����merge"
//            currentBuild.result = 'ABORTED'
            println("ִ�е�������1")
            currentBuild.result = 'FAILURE'
            error("${env.ErrInfo}")
        }
    }else{
        env.X5MOBILE_ONLINE_IS_HOT_FIX = 'false'
    }
    println("ִ�е�������2")
    env.ONLINE_WEEKLY_RESOURCE_DIR = result_map['weekly_resource']
    env.ONLINE_WEEKLY_RESOURCE_DIR_NAME ="${env.WORKSPACE}\\x5_mobile\\mr\\onlineupdate\\${env.X5MOBILE_ONLINE_BRANCH}\\${result_map['weekly_resource']}\\client\\"
    env.ONLINE_WEEKLY_RESOURCE_VERSION = result_map['weekly_version']
    env.ONLINE_HOTFIX_BASE_VERSION = result_map['hotfix_base_version']
    env.ONLINE_HOTFIX_CHANGE_LIST = result_map['hotfix_changelist']
    env.ONLINE_CONFIG_PIPELINE_ID = result_map['config_pipeline_id']
    println("ִ�е�������3")
    println(env.ONLINE_WEEKLY_RESOURCE_VERSION)
    println(result_map['weekly_version'])
}


// def upload_to_qe(){
//     def cdn_zip = "${env.WORKSPACE}\\x5_mobile\\mr\\art_release\\pack\\dress\\${env.CDN_VERSION}\\${env.CDN_VERSION}.zip"
//
//     if (!fileExists(cdn_zip)) {
//         echo "${cdn_zip}�ļ�������"
//         env.ErrInfo="${cdn_zip}�ļ�������"
//         currentBuild.result = 'FAILURE'
//     } else {
//         def out = bat(returnStdout: true, script: "python setup.py upload-to-qe --file_path ${cdn_zip} --artifact_type cdn --env prod --area cn").split('\n')
//         def result_map = readJSON text: out[2]
//         println(result_map)
//         if (result_map['error_code'] == 0){
//             echo "${cdn_zip}�ļ��ϴ��ɹ�"
//         }else{
//             echo "${cdn_zip}�ļ��ϴ�ʧ�ܣ�${out}"
//             env.ErrInfo="${cdn_zip}�ļ��ϴ�ʧ�ܣ�${out}"
//             currentBuild.result = 'FAILURE'
//         }
//     }
// }


def post_log(namespace){
    def out
    def tmp_path =env.WORKSPACE.replace("\\",'/')
    def path = "${tmp_path}/x5_mobile/gitlab/mobile_dancer/trunk/exe/ResListTool/Logs/"
    dir('pyframe-pipeline/project/x5m/art_package/scripts'){
        out = bat(returnStdout: true, script: "python setup.py get-cdn-hotfix-log --namespace ${namespace} --path ${path}").split('\n')
    }
    def filename = out[2].replaceAll("[\r\n]+","")
    println("filename:${filename}")
    def logFile = "x5_mobile/gitlab/mobile_dancer/trunk/exe/ResListTool/Logs/${filename}"
    def LogExists = fileExists "$logFile"
    if(LogExists){
        archiveArtifacts artifacts: "$logFile", followSymlinks: false
    }

}


def failure_info(){
    def msg = """**���**: ���ʧ��
**��־����**: http://jenkins-x5mobile.h3d.com.cn/job/CDN_And_HotFix/${env.BUILD_NUMBER}/console"""
    msg += """
<font color = "#dc143c">**������Ϣ**: ${env.ErrInfo}</font>
"""
    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=14457620-a0c0-45ce-9220-584c19b1dfa7',content: msg
}


def success_info(){
    def msg = """**���**: ����ɹ�\n"""
    if (env.X5MOBILE_RESOURCES_PACK == "true"){
        msg += """**Source�������ص�ַ**��\n"""
        if (env.X5MOBILE_RES_TOOL_PACK_TYPE == "All" || env.X5MOBILE_RES_TOOL_PACK_TYPE == "Cdn"){
            msg += """[CDN](https://dgmdd.h3d.com.cn/dgm/version_test/dynamic)
[CDN Config](https://dgmdd.h3d.com.cn/dgm/version_test/server_resources/cdn_${env.CDN_VERSION}.zip)
**CDN �汾��**: ${env.CDN_VERSION}
**ԭʼ��Դ�汾��**: ${env.CDN_RAW_RESOURCE_CHANGELIST}
**CDN changelist**: ${env.CDN_SOURCE_CHANGE_LIST}\n"""
        }
        if (env.X5MOBILE_RES_TOOL_PACK_TYPE == "All"){ msg += """**''''''''''''''''''''''''''''''''''''''''''''''**\n"""}
        if (env.X5MOBILE_RES_TOOL_PACK_TYPE == "All" || env.X5MOBILE_RES_TOOL_PACK_TYPE == "Hotfix"){
            msg += """[��������](https://dgmdd.h3d.com.cn/dgm/version_test/server_resources/resources_${env.RESOURCES_WEEKLY_RESOURCE_VERSION }.zip)
[�ȸ���_Android](https://dgmdd.h3d.com.cn/dgm/version_test/resources/android/${env.RESOURCES_WEEKLY_RESOURCE_VERSION }/)
[�ȸ���_ios](https://dgmdd.h3d.com.cn/dgm/version_test/resources/ios/${env.RESOURCES_WEEKLY_RESOURCE_VERSION }/)
**�ȸ��汾��**: ${env.RESOURCES_WEEKLY_RESOURCE_VERSION}
**�ȸ� changelist**: ${env.RESOURCES_HOTFIX_CHANGE_LIST}
**���� pipelineid**: ${env.RESOURCES_CONFIG_PIPELINE_ID}\n"""
        }
    }
    if (env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"){
        if (env.X5MOBILE_RESOURCES_PACK == "true"){
            msg += """**''''''''''''''''''''''''''''''''''''''''''''''**\n"""
    }
        msg += """**Release�������ص�ַ**��
[��������](https://dgmdd.h3d.com.cn/dgm/version_test/server_resources/resources_${env.RELEASE_WEEKLY_RESOURCE_VERSION }.zip)
[�ȸ���_Android](https://dgmdd.h3d.com.cn/dgm/version_test/resources/android/${env.RELEASE_WEEKLY_RESOURCE_VERSION }/)
[�ȸ���_ios](https://dgmdd.h3d.com.cn/dgm/version_test/resources/ios/${env.RELEASE_WEEKLY_RESOURCE_VERSION }/)
**�ȸ��汾��**: ${env.RELEASE_WEEKLY_RESOURCE_VERSION}
**�ȸ� changelist**: ${env.RELEASE_HOTFIX_CHANGE_LIST}
**���� pipelineid**: ${env.RELEASE_CONFIG_PIPELINE_ID}\n"""
    }
    if (env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"){
        if (env.X5MOBILE_RESOURCES_PACK == "true" || env.X5MOBILE_RELEASE_IS_HOT_FIX == "true"){msg += """**''''''''''''''''''''''''''''''''''''''''''''''**\n"""}
        msg+= """**Online�������ص�ַ**:
[��������](https://dgmdd.h3d.com.cn/dgm/version_test/server_resources/resources_${env.ONLINE_WEEKLY_RESOURCE_VERSION }.zip)
[�ȸ���_Android](https://dgmdd.h3d.com.cn/dgm/version_test/resources/android/${env.ONLINE_WEEKLY_RESOURCE_VERSION }/)
[�ȸ���_ios](https://dgmdd.h3d.com.cn/dgm/version_test/resources/ios/${env.ONLINE_WEEKLY_RESOURCE_VERSION }/)
**�ȸ��汾��**: ${env.ONLINE_WEEKLY_RESOURCE_VERSION}
**�ȸ� changelist**: ${env.ONLINE_HOTFIX_CHANGE_LIST}
**���� pipelineid**: ${env.ONLINE_CONFIG_PIPELINE_ID}\n"""
    }
    if (env.X5MOBILE_RESOURCES_PACK == "true" || env.X5MOBILE_RELEASE_IS_HOT_FIX == "true" || env.X5MOBILE_ONLINE_IS_HOT_FIX == "true"){
        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=99072e8c-c267-411c-b063-e8b1b6495847',content: msg
    }
}


return [
    init_config:this.&init_config,
    check_resources_update:this.&check_resources_update,
    get_cdn_config: this.&get_cdn_config,
    getCode:this.&getCode,
    get_week_config:this.&get_week_config,
    copy_config:this.&copy_config,
    copy_x5mweek_config:this.&copy_x5mweek_config,
    unzip:this.&unzip,
    execute_packaging_tool:this.&execute_packaging_tool,
    copy_cdn_hotfix_server:this.&copy_cdn_hotfix_server,
    config_ini:this.&config_ini,
    post_log:this.&post_log,
    checkupdate:this.&checkupdate,
    check_online_update:this.&check_online_update,
//     upload_to_qe:this.&upload_to_qe,
    failure_info:this.&failure_info,
    success_info:this.&success_info
]