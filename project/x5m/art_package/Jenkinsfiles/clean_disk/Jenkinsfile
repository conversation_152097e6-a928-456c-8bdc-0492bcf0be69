node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

CACHED_DB = [:]

pipeline {
    agent none
    options {
        timestamps()
        timeout(time: 180, unit: 'MINUTES')
    }
    parameters {
        string defaultValue: '', description: 'agents', name: 'server_agent', trim: true
        string defaultValue: '3', description: '清理几天前的日志', name: 'Days', trim: true
    }
    environment {
        PIPELINE_PYTHON = 'python3'
    }
    stages {
        stage("并行打包流程"){
            steps {
                script {
                    def agents = params.server_agent.split(',')
                    def maxVertical = 10
                    def parallelStages = [:]

                    // Calculate grid dimensions
                    def totalAgents = agents.size()
                    def columns = Math.ceil(totalAgents / maxVertical)

                    // Create stages for each agent
                    agents.eachWithIndex { agent, index ->
                        // Calculate position in grid
                        def row = index % maxVertical
                        def column = Math.floor(index / maxVertical)

                        // Create stage name with position information for visual arrangement
                        def stageName = "${column}_${row}_${agent}"

                        parallelStages[stageName] = {
                            node(agent) {
                                stage("清理磁盘 - ${agent}") {
                                    sh """
                                        ${PIPELINE_PYTHON} /data/workspace/pyframe-pipeline/x5m.py clean_server --job=clean_log --days=${params.Days}
                                    """
                                }
                            }
                        }
                    }

                    parallel parallelStages
                }
            }
        }
    }
}