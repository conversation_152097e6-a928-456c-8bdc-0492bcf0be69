node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

CACHED_DB = [:]

pipeline {
    agent {
        node {
            label "clean_46_disk"
            customWorkspace "D:\\clean_disk"
        }
    }
    options {
        timestamps()
        timeout(time: 180, unit: 'MINUTES')
    }
    triggers {
        cron('H 6 * * *')
    }
    environment {
        PIPELINE_PYTHON = 'python'
    }
    stages {
        stage("更新流水线依赖") {
            steps {
                dir("pyframe-pipeline") {
                    script{
                        bat(script: "${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q")
                    }
                }
            }
        }
        stage("清理dynamic"){
            steps {
                dir("pyframe-pipeline") {
                    script{
                        bat(script: "${PIPELINE_PYTHON} x5m.py clean_ftp_150_46 --job=clean_dynamic")
                    }
                }
            }
        }
        stage("清理dress"){
            steps {
                dir("pyframe-pipeline") {
                    script{
                        bat(script: "${PIPELINE_PYTHON} x5m.py clean_ftp_150_46 --job=clean_dress")
                    }
                }
            }
        }
        stage("清理update_config"){
            steps {
                dir("pyframe-pipeline") {
                    script{
                        bat(script: "${PIPELINE_PYTHON} x5m.py clean_ftp_150_46 --job=clean_ftp_update_config")
                    }
                }
            }
        }
        stage("清理server_pack"){
            steps {
                dir("pyframe-pipeline") {
                    script{
                        bat(script: "${PIPELINE_PYTHON} x5m.py clean_ftp_150_46 --job=clean_ftp_server_pack")
                    }
                }
            }
        }
        stage("清理cdn&热更流水线"){
            steps {
                dir("pyframe-pipeline") {
                    script{
                        bat(script: "${PIPELINE_PYTHON} x5m.py clean_ftp_150_46 --job=clean_cdn_hotfix_zip")
                    }
                }
            }
        }
        stage("按时间清理"){
            steps {
                dir("pyframe-pipeline") {
                    script{
                        bat(script: "${PIPELINE_PYTHON} x5m.py clean_ftp_150_46 --job=delete")
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("pyframe-pipeline") {
                script{
                    bat(script: "${PIPELINE_PYTHON} x5m.py clean_ftp_150_46 --job=post_canceled")
                }
            }
        }
        success {
            dir("pyframe-pipeline") {
                script {
                    bat(script: "${PIPELINE_PYTHON} x5m.py clean_ftp_150_46 --job=post_success")
                }
            }
        }
        failure {
            dir("pyframe-pipeline") {
                script {
                    bat(script: "${PIPELINE_PYTHON} x5m.py clean_ftp_150_46 --job=post_failure")
                }
            }
        }
    }
}