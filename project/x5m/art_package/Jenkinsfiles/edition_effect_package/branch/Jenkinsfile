@Library('h3d_libs') _
import groovy.json.JsonSlurper
def changeList = ""
def isAndroidPackage = "true"
def isIosPackage = "true"
def branch = ""
String[] view_list
def messageSendUserList = ["<EMAIL>", "<EMAIL>", "ji<PERSON><PERSON><PERSON><PERSON>@h3d.com.cn", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
def buildResultCode = 0


def getP4View(platform, branch) {
    // 生产环境映射
    def tpl = '''
//x5_mobile/mr/b/${branch}/art_src/effect/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/effect/...
//x5_mobile/mr/b/${branch}/art_src/c/*/art/effect/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/c/*/art/effect/...
//x5_mobile/mr/b/${branch}/ResourcePublish/CDN/SourceFiles/${PLATFORM}/assetbundles/... //${p4Client}/x5_mobile/mr/b/${branch}/art_release/cs/${PLATFORM}/assetbundles/...
//x5_mobile/mr/b/${branch}/ResourcePublish/CDN/SourceFiles/${PLATFORM}/assetbundles/c/*/art/effect/... //${p4Client}/x5_mobile/mr/b/${branch}/art_release/cs/${PLATFORM}/assetbundles/art/c/*/art/effect/...
    '''
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([p4Client: '${P4_CLIENT}', PLATFORM: platform, branch: branch]).toString()
}

def IsPackage(platform,isAndroidPackage,isIosPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    }else{
        return isIosPackage == "true"
    }
}


def sendMessage(users, subject, body) {
    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
        if (!users.contains("${env.BUILD_USER_EMAIL}".toString())) {
            users.add("${env.BUILD_USER_EMAIL}".toString())
        }
    }
    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
            users.add(env.SEND_MESSAGE_USER_INFO)
    }
    def sendUserString = users.join(",")
    if (sendUserString != "") {
        emailext to: "${sendUserString}", subject: subject, body: body
    }
}


def getArtTrunkFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/master"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    url: '******************************:dgm/arttrunk.git']]]
}


def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}

pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
         booleanParam(name: 'force', defaultValue: false, description: '是否强更P4')
    }
//     因为四点到五点p4需要备份数据，流水线会执行失败
    triggers {
        cron('H/15 0-3,5-23 * * *')
    }

    stages {
        stage('美术资源之版本内分支打包'){
            matrix {
                agent {
                    node{
                        label "edition_effect-branch-${PLATFORM}-package"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'android','iOS'
                    }
                }
                stages{
                    stage('配置初始化'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    getBuildUserInfo()
                                    if (!fileExists(".git/config")) {
                                        bat """
                                        del /s /f /q .
                                        """
                                    }
                                    def platform_lower = PLATFORM.toLowerCase()
                                    // 判断配置 config.ini 是否存在，存在, 不处理, 否则判断 config.ini.example 是否存在, 存在，改名为config.ini
                                    def configFile = "config.ini"
                                    def configExampleFile = "config.ini.example"
                                    def configExists = fileExists "$configFile"
                                    def exampleExists = fileExists "$configExampleFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
                                    if (!configExists) {
                                        // 不存在
                                        bat """
                                        git clone -b edition-effect-branch-${platform_lower} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                                        """
//                                         if (exampleExists) {
//                                             // 改名
//                                             fileOperations([fileRenameOperation(destination: "${configFile}", source: "${configExampleFile}")])
//                                             bat """
//                                             python -m pip install --upgrade pip
//                                             pip install -r ${env.WORKSPACE}\\gitlab\\Script\\scripts\\requirements.txt
//                                             """
//                                         }
                                    }
                                    bat """
                                    git clean -xdf
                                    git reset --hard HEAD
                                    git pull origin
                                    """
                                }
                            }
                        }
                    }
                    stage('获取p4上的最新分支'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    def branch_out = bat(returnStdout: true, script: "python setup.py get_mr_latest_branch").split('\n')
                                    println(branch_out[2])
                                    branch = branch_out[2].trim()
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    echo "${branch}"
                                    def out = bat(returnStdout: true, script: "python setup.py get_branch_edition_updates --tp edition_effect_branch --branch ${branch} --first_changelist=${params.first_changelist}").split('\n')
                                    println("检查是否需要打包返回的out值:${out}")
                                    def jsonSlurper = new JsonSlurper()
                                    def result_map = jsonSlurper.parseText(out[2])
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDWORKSPACE = env.WORKSPACE
                                        isAndroidPackage = result_map.update
                                        println(isAndroidPackage)
                                    }else{
                                        env.IOSWORKSPACE = env.WORKSPACE
                                        isIosPackage = result_map.update
                                        println(isIosPackage)
                                    }
                                    if (result_map.update == 'true'){
                                        env.PACKAGELIST = result_map.package_list
                                        changeList = result_map.changelist
                                        env.SEND_MESSAGE_USER_INFO = result_map.send_user
                                    }
                                    if(isAndroidPackage == 'false' && isIosPackage == 'false'){
                                        currentBuild.result = 'ABORTED'
                                    }
                                }
                            }
                        }
                    }
                    stage('获取p4资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                bat """git config --global core.longpaths true"""
                                getArtTrunkFromGitlab()
                                def view = getP4View("${PLATFORM}", "${branch}")
                                echo "view:${view}"
                                def p4_workspace = "jenkins-edition_effect-branch-${PLATFORM}-package"
                                def python_view = view.replaceAll("\n", "#")
                                bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=${params.force}")


//                                 checkout perforce(credential: 'p4_dgm_jenkins_upwd',
//                                     populate: syncOnly(force: "${params.force}", have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: env.P4_REVISION, quiet: true, revert: false),
//                                     workspace: manualSpec(
//                                         charset: 'none',
//                                         cleanup: false,
//                                         name: "jenkins-edition_effect-branch-${PLATFORM}-package",
//                                         pinHost: false,
//                                         spec: clientSpec(
//                                             allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
//                                             locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: view
//                                         )
//                                     )
//                                 )
                                // echo "P4_CLIENT:${P4_CLIENT}"
                            }
                        }
                    }
                    stage('调用打包命令'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    platform = "Android"
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    platform = "iOS"
                                    workspace = env.IOSWORKSPACE
                                }
                                writeFile encoding: 'GB2312', file: 'x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/resources.txt', text: "${env.PACKAGELIST}"
                                bat(script:  "unity -quit -batchmode -logFile package_${PLATFORM}_${branch}.log -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" -executeMethod H3DBuildTools.BuildArt -buildTarget ${platform} out_path=${workspace}\\x5_mobile\\mr\\b\\${branch}\\art_release\\cs\\${PLATFORM}\\assetbundles")
                            }
                        }
                    }
                    stage('计算打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }
                                    out = bat(returnStdout: true, script: "python setup.py print_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client ").split('\n')
                                    print(out[2])
                                    def count_result = readJSON text: out[2]
                                    env.UPLOADP4 = count_result.upload_p4
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDTOTALCOUNT = count_result.total_count
                                        print(env.ANDROIDTOTALCOUNT)
                                        env.ANDROIDSUCCCOUNT = count_result.succ_count
                                        print(env.ANDROIDSUCCCOUNT)
                                        env.ANDROIDFAILEDCOUNT = count_result.failed_count
                                        print(env.ANDROIDFAILEDCOUNT)
                                        env.ANDROIDERRORCOUNT = count_result.error_count

                                        if (env.ANDROIDERRORCOUNT != "0" || env.ANDROIDFAILEDCOUNT != "0" || env.ANDROIDSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                    else{
                                        env.IOSTOTALCOUNT = count_result.total_count
                                        print(env.IOSTOTALCOUNT)
                                        env.IOSSUCCCOUNT = count_result.succ_count
                                        print(env.IOSSUCCCOUNT)
                                        env.IOSFAILEDCOUNT = count_result.failed_count
                                        print(env.IOSFAILEDCOUNT)
                                        env.IOSERRORCOUNT = count_result.error_count
                                        if (env.IOSERRORCOUNT != "0" || env.IOSFAILEDCOUNT != "0" || env.IOSSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('提交p4'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.UPLOADP4 == 'true'
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    def workspace
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }
                                    def submitP4Desc = "uploader:${env.SEND_MESSAGE_USER_INFO}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitP4Desc}"
                                    def out = bat(returnStdout: true,script: "python setup.py edition_branch_submit_to_p4  --root=${workspace} --platform=${PLATFORM} --tp=edition_effect_branch --branch=${branch} --changelist=${changeList} --desc=\"${submitP4Desc}\" --workspace=jenkins-edition_effect-branch-${PLATFORM}-package")
                                    echo "${out}"
                                    String[] outArras=out.split("\n")
                                    if (outArras.size() > 4){
                                        println("p4workspace下没有需要提交的内容！")
                                    }else{
                                        if ("${PLATFORM}" == "android"){
                                            env.ANDROID_CHANGELIST = outArras[2]
                                        }else{
                                            env.IOS_CHANGELIST = outArras[2]
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('持久化配置文件'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.UPLOADP4 == 'true'
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='持久化配置文件失败'
                                    bat """
                                    git config --global user.email "<EMAIL>"
                                    git config --global user.name "dgm_developer"
                                    git add config.ini
                                    git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                                    git push origin
                                    """
                                }
                            }
                        }
                    }
                    stage('上传打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    workspace = env.IOSWORKSPACE
                                }
                                def out
                                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                     out = bat(returnStdout: true, script: "python setup.py get_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}").split('\n')
                                }
                                def artifactsPath="x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/"
                                dir("${artifactsPath}"){
                                    def artifactName = out[2]
                                    archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                    def artifactUrl = env.BUILD_URL + 'artifact/' + out[2]
                                    echo "upload_url: ${artifactUrl}"
                                     if ("${PLATFORM}" == "android"){
                                        env.ANDROIDPACKAGERESULTURL = artifactUrl.trim()
                                        }
                                    else{
                                        env.IOSPACKAGERESULTURL = artifactUrl.trim()
                                    }
                                }
                                def artifactName_1 = "package_${PLATFORM}_${branch}.log"
                                archiveArtifacts artifacts: "$artifactName_1", followSymlinks: false
                                def artifactUrl_1 = env.BUILD_URL + 'artifact/' + artifactName_1
                                echo "upload_url: ${artifactUrl_1}"
                                if ("${PLATFORM}" == "android"){
                                    env.ANDROIDPACKAGERESULTURL_UNITY = artifactUrl_1
                                }
                                else{
                                    env.IOSPACKAGERESULTURL_UNITY = artifactUrl_1
                                }
                            }
                        }
                    }
                    stage('判断是否单端失败'){
                        when {
                            expression {
                                buildResultCode <= 3
                            }
                        }
                        steps{
                            script{
                                if ("${PLATFORM}" == "android"){
                                    buildResultCode += 1
                                }
                                else{
                                    buildResultCode += 2
                                }
                            }
                        }
                    }
                }
                post{
                    always{
                        //上传日志
                        script {
                            def build_log_android = "package_android_${branch}.log"
                            def buildLogAndroidExists = fileExists "$build_log_android"
                            if(buildLogAndroidExists){
                                archiveArtifacts artifacts: "$build_log_android", followSymlinks: false
                            }
                            def build_log_ios = "package_iOS_${branch}.log"
                            def buildLogIosExists = fileExists "$build_log_ios"
                            if(buildLogIosExists){
                                archiveArtifacts artifacts: "$build_log_ios", followSymlinks: false
                            }
                            //定义信息模板
                            env.ANDROIDBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.ANDROID_CHANGELIST}
[打包统计](${env.ANDROIDPACKAGERESULTURL})
**打包数量**: ${env.ANDROIDTOTALCOUNT}
**成功数量**: ${env.ANDROIDSUCCCOUNT}"""
                            env.IOSBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.IOS_CHANGELIST}
[打包统计](${env.IOSPACKAGERESULTURL})
**打包数量**: ${env.IOSTOTALCOUNT}
**成功数量**: ${env.IOSSUCCCOUNT}"""
                            if (currentBuild.result != 'SUCCESS'){
                                env.ANDROIDBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.ANDROIDFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.ANDROIDERRORCOUNT}</font>"""
                                env.IOSBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.IOSFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.IOSERRORCOUNT}</font>"""
                            }
                            else{
                                env.ANDROIDBACKINFO += """
**失败数量**: ${env.ANDROIDFAILEDCOUNT}
**检查出错数量**: ${env.ANDROIDERRORCOUNT}"""
                                env.IOSBACKINFO += """
**失败数量**: ${env.IOSFAILEDCOUNT}
**检查出错数量**: ${env.IOSERRORCOUNT}"""
                            }
                            env.LOGBACKINFO = """
[安卓 unity日志](http://jenkins-x5mobile.h3d.com.cn/job/feature_hotfix_pack/job/effect/job/effect_branch/${env.BUILD_NUMBER}/artifact/${build_log_android})
[ios unity日志](http://jenkins-x5mobile.h3d.com.cn/job/feature_hotfix_pack/job/effect/job/effect_branch/${env.BUILD_NUMBER}/artifact/${build_log_ios})"""
                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            node('master') {
                script {
                    echo "执行状态码： ${buildResultCode}"
                    def msg = """**结果**: effect版本内分支资源打包失败"""

                    if (buildResultCode == 1){
                        msg += """
<font color = "#dc143c">**单端成功**: 安卓(下次打包会自动把失败的一端打包)</font>
${env.ANDROIDBACKINFO}
"""
                    }
                    else if (buildResultCode == 2){
                        msg += """
<font color = "#dc143c">**单端成功**: ios(下次打包会自动把失败的一端打包)</font>
${env.IOSBACKINFO}
"""
                   }
                   msg += """
<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>
${env.LOGBACKINFO}
"""
                echo "${msg}"
                sendMessage(messageSendUserList, "jenkins-版本内特效分支打包失败 #${env.BUILD_NUMBER}", msg)
                workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9210a69f-33cf-4d37-ade4-767b36e8ac11',content: msg
                }
            }
        }
        unstable{
            node('master') {
                script{
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg=""""""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}\n${env.LOGBACKINFO}"""
                        }
                        sendMessage(messageSendUserList, "jenkins-版本内特效分支打包部分失败 #${env.BUILD_NUMBER}", msg)
                        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9210a69f-33cf-4d37-ade4-767b36e8ac11',content: msg
                    }
                }
            }
        }
        success {
            node('master') {
                script{
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg=""""""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}\n"""
                        }
                        sendMessage(messageSendUserList, "jenkins-版本内特效分支打包成功 #${env.BUILD_NUMBER}", msg)
                        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9210a69f-33cf-4d37-ade4-767b36e8ac11',content: msg
                    }
                }
            }
        }
    }
}