@Library('h3d_libs_qe') _
import groovy.json.JsonSlurper

def file_info = ""
def send_wx(context,title=null){
    context=workwx.get_standard_format context:context,title:title
    def robotid="14457620-a0c0-45ce-9220-584c19b1dfa7";
    def to_people="<EMAIL>|<EMAIL>|<EMAIL>";
    def art="python -c \"from send_wecom import SendWeCom; import sys;print(SendWeCom().send_standard_message('${robotid}','${to_people}','${context}'))\""
    def filename=env.JOB_NAME.split('/')[-1]+".bat" //env.BUILD_NUMBER
    writeFile file: filename, encoding: 'GBK', text: """${art}"""
    bat script: filename
}

def getX5mConifgFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/master"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'gitlab_dgm_developer_sshpk',
    url: '******************************:dgm/x5mobile.git']]]
}

def automereg(deviation){
    def iid_info = bat(returnStdout: true, script: "python auto_merge_witebox.py --deviation ${deviation}")
    println(iid_info)
    def printinfo=iid_info.split('\n')
    for(i in printinfo)
    {
        println(i)
    }
    def branchname_args=printinfo[2]+" to "+printinfo[2]+"_wb"
    def resultinfo=printinfo[-1]
    result_map = readJSON text: resultinfo
    def success_option="00000003"
    def fail_option="00000001"
    def sucess_content = """*结果*: 白盒工程自动merge执行成功
重要参数:${branchname_args}
**merge成功iid**:${result_map.sucess}
**merge成功分支**:${result_map.sucess_branch}"""
    def fail_content = """*结果*: 白盒工程自动merge执行失败
重要参数:${branchname_args}
**merge失败iid**:${result_map.fail}
**merge失败分支**:${result_map.fail_branch}
**merge失败信息**:${result_map.fail_info}"""
    if (result_map.close_sucess != "")
    {
        fail_content+="""
**close成功iid**: ${result_map.close_sucess}
**close成功分支**:${result_map.close_sucess_branch}"""
    }
    if (result_map.close_fail != "")
    {
        fail_content+="""
**close失败iid**:${result_map.close_fail}
**close失败分支**:${result_map.close_fail_info}
**close失败信息**:${result_map.close_fail_branch}"""
    }

    def context=""
    def option=success_option
    if (result_map.fail == "")
    {
        //成功不发消息
        //workwx.send url:"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=14457620-a0c0-45ce-9220-584c19b1dfa7",content:sucess_content
    }
    else
    {
        context=fail_content
        option=fail_option
    }
    send_wx(context,"白盒分支自动合并"+":"+option)
    if (result_map.fail != "")
    {
        error '-1'
    }
}

pipeline {
    agent {
            node{
                label "whitebox-outo-mereg"
            }
        }
    stages {
        stage('git自动merge上一个版本'){
            steps {
                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                    script{
                        automereg(1)
                    }
                }
            }
        }
        stage('git自动merge当前版本'){
            steps {
                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                    script{
                        automereg(0)
                    }
                }
            }
        }
    }
}