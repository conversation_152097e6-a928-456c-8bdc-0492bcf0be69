@Library('h3d_libs') _
import groovy.json.JsonSlurper
def changeList = ""
def isAndroidPackage = "true"
def isIosPackage = "true"
def timeRetryAndroid = 0
def timeRetryIos = 0
def viewsAndroid = ""
def viewsIos = ""
def messageSendUserList = ["<EMAIL>", "<EMAIL>", "ji<PERSON><PERSON><PERSON><PERSON>@h3d.com.cn", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
def buildResultCode = 0


def getP4View(view_list,b_path, s_b_path){
    def tpl = '''
//x5m/res/cdn/cooked/${platform}/assetbundles/art/role/link/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/role/link/...
//x5_mobile/mr/art_release/art_src/role/cloth_show/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/assets/resources/art/role/cloth_show/...
//x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/link/... //${p4Client}/x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/link/...
//x5_mobile/mr/b/${b_path}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/link/... //${p4Client}/x5_mobile/mr/b/${b_path}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/link/...
//x5_mobile/mr/onlineupdate/*/*/client/${platform}/assetbundles/art/role/link/... //${p4Client}/x5_mobile/mr/onlineupdate/*/*/client/${platform}/assetbundles/art/role/link/...
//x5mplan/resmg/link/... //${p4Client}/x5mplan/resmg/link/...
    '''
    if (!s_b_path.isEmpty()) {
        tpl += '''//x5_mobile/mr/b/${s_b_path}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/link/... //${p4Client}/x5_mobile/mr/b/${s_b_path}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/link/...
'''
    }
    for(String v in view_list){
        tpl+=v+'\n'
    }
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([platform: platform,b_path: b_path, s_b_path:s_b_path, p4Client: '${P4_CLIENT}']).toString()
}

def getArtTrunkFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/release"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    url: '******************************:dgm/arttrunk.git']]]
}
def IsPackage(platform,isAndroidPackage,isIosPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    }else{
        return isIosPackage == "true"
    }
}


def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}


pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 120, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
         booleanParam(name: 'force', defaultValue: true, description: '是否强更P4')
    }
    //     因为五点到六点半p4需要备份数据，流水线会执行失败
    triggers {
        cron('H/15 0-4,8-23 * * *')
    }

    stages {
        stage('美术资源打包'){
            matrix {
                agent {
                    node{
                        label "link-distrib-${PLATFORM}-package-newp4"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'ios','android'
                    }
                }
                stages{
                    stage('配置初始化'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='配置初始化失败'
                                    getBuildUserInfo()
                                    if (!fileExists(".git/config")) {
                                        bat """
                                        del /s /f /q .
                                        """
                                    }
                                    // 判断配置 config.ini 是否存在，存在, 不处理, 否则判断 config.ini.example 是否存在, 存在，改名为config.ini
                                    def configFile = "config.ini"
                                    def configExampleFile = "config.ini.example"
                                    def configExists = fileExists "$configFile"
                                    def exampleExists = fileExists "$configExampleFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
                                    if (!configExists) {
                                        // 不存在
                                        bat """
                                        git clone -b link-distrib-${PLATFORM} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                                        """
//                                         if (exampleExists) {
//                                             // 改名
//                                             fileOperations([fileRenameOperation(destination: "${configFile}", source: "${configExampleFile}")])
//                                             bat """
//                                             python -m pip install --upgrade pip
//                                             pip install -r ${env.WORKSPACE}\\gitlab\\Script\\scripts\\requirements.txt
//                                             """
//                                         }
                                    }
                                    bat """
                                    git clean -xdf
                                    git reset --hard HEAD
                                    git pull origin link-distrib-${PLATFORM}
                                    """
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='检查是否需要打包失败'
                                    def out = bat(returnStdout: true, script: "python setup.py get_updates --tp link_distrib --first_changelist=${params.first_changelist}").split('\n')
                                    println(out[2])
                                    def jsonSlurper = new JsonSlurper()
                                    def result_map = jsonSlurper.parseText(out[2])
                                    max_version_b = result_map.max_version_b
                                    s_b_path = result_map.s_b_path

                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDWORKSPACE = env.WORKSPACE
                                        isAndroidPackage = result_map.update
                                        println(isAndroidPackage)
                                        env.ID_PATHS_ANDROID = result_map.idpaths
                                        env.SEND_MESSAGE_USER_INFO_ANDROID = result_map.send_user
                                        viewsAndroid = result_map.views
                                    }else{
                                        env.IOSWORKSPACE = env.WORKSPACE
                                        isIosPackage = result_map.update
                                        println(isIosPackage)
                                        env.SEND_MESSAGE_USER_INFO_IOS = result_map.send_user
                                        env.ID_PATHS_IOS = result_map.idpaths
                                        viewsIos = result_map.views
                                    }
                                    if (result_map.update == 'true'){
                                        changeList = result_map.changelist
//                                        env.IDLIST = result_map.idlist
//                                        env.IDPATHS = result_map.idpaths
//                                        view_list = result_map.views.split(';')
//                                        env.SEND_MESSAGE_USER_INFO = result_map.send_user
                                        env.SEND_MESSAGE_USER_INFO = env.SEND_MESSAGE_USER_INFO_ANDROID + "," + env.SEND_MESSAGE_USER_INFO_IOS
                                    }
                                    if(isAndroidPackage == 'false' && isIosPackage == 'false'){
                                        currentBuild.result = 'ABORTED'
                                    }
                                }
                            }

                        }
                    }
                    stage('清理资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps {
                            dir("x5_mobile\\mobile_dancer\\arttrunk\\client\\Assets\\resources\\Art\\role\\link"){
                                deleteDir()
                            }
                        }
                    }
                    stage('获取p4资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            retry(2){
                                script{
                                    env.ErrInfo='获取p4资源失败'
                                    def viewList
                                    if ("${PLATFORM}" == "android"){
                                        timeRetryAndroid ++
                                        echo "安卓执行次数：${timeRetryAndroid}"
                                        viewList = viewsAndroid.split(";")
                                    }
                                    else{
                                        timeRetryIos ++
                                        echo "ios执行次数：${timeRetryIos}"
                                        viewList = viewsIos.split(";")
                                    }
                                    bat """git config --global core.longpaths true"""
                                    getArtTrunkFromGitlab()
                                    def view = getP4View(viewList,"${max_version_b}", "${s_b_path}")
                                    // println(view)
                                    def python_view = view.replaceAll("\n", "#")
                                    def p4_workspace = "jenkins-link-distrib-${PLATFORM}-package-newp4"
                                    try {
                                        timeout(time: 600, unit: 'MINUTES') {
                                            bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=${params.force}")
                                        }
                                    } catch (err) {
                                        def sendUserString = messageSendUserList.join(",")
                                        if (sendUserString != "") {
                                            emailext to: "${sendUserString}", subject: "jenkins-饰品资源分发打包 #${env.BUILD_NUMBER}", body: "获取p4资源卡住超时，重试中"
                                        }
                                        error("获取p4资源卡住超时，重试中")
                                    }
                                }
                            }
                            script {
                                def out = bat(returnStdout: true, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py distribute_precheck --p4_root=${env.WORKSPACE} --tp=link_distrib").split('\n')
                                def jsonSlurper = new JsonSlurper()
                                def result_map = jsonSlurper.parseText(out[2])
                                println(result_map)
                                if (result_map.state != "true"){
                                    env.ErrInfo = result_map.msg
                                    error(result_map.msg)
                                }
                            }
                        }
                    }
                    stage('调用打包命令'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='调用unity打包失败'
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    platform = "Android"
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    platform = "iOS"
                                    workspace = env.IOSWORKSPACE
                                }
                                def jenkins_log = "${workspace}\\jenkins_log\\${env.BUILD_NUMBER}"
                                def logExists = fileExists "$jenkins_log"
                                if(!logExists)
                                {
                                    bat label:"日志文件夹",script:"mkdir jenkins_log\\${env.BUILD_NUMBER}"
                                }
                                def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat(script: "unity -quit -batchmode -logFile ${build_pack_log} -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" -executeMethod H3DBuildTools.BuildBodyPartOrLink -buildTarget ${platform} out_path=${workspace}\\x5_mobile\\mr\\art_release\\cs\\${PLATFORM}\\assetbundles")
                            }
                        }
                    }
                    stage('计算打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='计算打包结果失败'
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }
                                    out = bat(returnStdout: true, script: "python setup.py print_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client ").split('\n')
                                    print(out[2])
                                    def count_result = readJSON text: out[2]
                                    // env.UPLOADP4 = count_result.upload_p4
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDTOTALCOUNT = count_result.total_count
                                        print(env.ANDROIDTOTALCOUNT)
                                        env.ANDROIDSUCCCOUNT = count_result.succ_count
                                        print(env.ANDROIDSUCCCOUNT)
                                        env.ANDROIDFAILEDCOUNT = count_result.failed_count
                                        print(env.ANDROIDFAILEDCOUNT)
                                        env.ANDROIDERRORCOUNT = count_result.error_count
                                        print(env.ANDROIDERRORCOUNT)
                                        env.ANDROID_SUCCESS_IDS = count_result.success_ids
                                        print(env.ANDROID_SUCCESS_IDS)
                                        env.ANDROID_FAIL_IDS = count_result.fail_ids
                                        print(env.ANDROID_FAIL_IDS)
                                        env.ANDROID_UPLOADP4 = count_result.upload_p4

                                        if (env.ANDROIDERRORCOUNT != "0" || env.ANDROIDFAILEDCOUNT != "0" || env.ANDROIDSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                    else{
                                        env.IOSTOTALCOUNT = count_result.total_count
                                        print(env.IOSTOTALCOUNT)
                                        env.IOSSUCCCOUNT = count_result.succ_count
                                        print(env.IOSSUCCCOUNT)
                                        env.IOSFAILEDCOUNT = count_result.failed_count
                                        print(env.IOSFAILEDCOUNT)
                                        env.IOSERRORCOUNT = count_result.error_count
                                        print(env.IOSERRORCOUNT)
                                        env.IOS_SUCCESS_IDS = count_result.success_ids
                                        print(env.IOS_SUCCESS_IDS)
                                        env.IOS_FAIL_IDS = count_result.fail_ids
                                        print(env.IOS_FAIL_IDS)
                                        env.IOS_UPLOADP4 = count_result.upload_p4

                                        if (env.IOSERRORCOUNT != "0" || env.IOSFAILEDCOUNT != "0" || env.IOSSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('提交相关custom文件'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && (env.ANDROID_UPLOADP4 == 'true' || env.IOS_UPLOADP4 == 'true')
                            }
                        }
                        steps{
                            script{
                                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                    def workspace
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                        IdPath = env.ID_PATHS_ANDROID
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                        IdPath = env.ID_PATHS_IOS
                                    }
                                    def uploader_user = env.BUILD_USER_EMAIL
                                    if(uploader_user == "") {
                                        uploader_user = env.SEND_MESSAGE_USER_INFO
                                    }
                                    def submitDesc = "--Auto--|uploader:${uploader_user}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitDesc}"
                                    bat "python setup.py submit_related_file --root=${workspace} --platform=${PLATFORM} --file_paths=${IdPath} --tp=link_distrib --desc=\"${submitDesc}\""
                                }
                            }
                        }
                    }
                    stage('提交p4'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && (env.ANDROID_UPLOADP4 == 'true' || env.IOS_UPLOADP4 == 'true')
                            }
                        }
                        steps{
                            script{
                                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                    env.ErrInfo='提交p4失败'
                                    def workspace
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                        IdPath = env.ID_PATHS_ANDROID
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                        IdPath = env.ID_PATHS_IOS
                                    }
                                    def uploader_user = env.BUILD_USER_EMAIL
                                    if(uploader_user == "") {
                                        uploader_user = env.SEND_MESSAGE_USER_INFO
                                    }
                                    def submitP4Desc = "--Auto--|uploader:${uploader_user}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitP4Desc}"
                                    def out = bat(returnStdout: true,script: "python setup.py submit_files_to_p4  --root=${workspace} --platform=${PLATFORM} --file_paths=${IdPath} --tp=link_distrib --changelist=${changeList} --desc=\"${submitP4Desc}\" --workspace=jenkins-link-distrib-${PLATFORM}-package-newp4")
                                    println(out)
                                    String[] outArras=out.split("\n")
                                    if (outArras.size() > 4){
                                        println("p4workspace下没有需要提交的内容！")
                                    }else{
                                        if ("${PLATFORM}" == "android"){
                                            env.ANDROID_CHANGELIST = outArras[2]
                                            env.ANDROID_RESOURCES = outArras[3]
                                        }else{
                                            env.IOS_CHANGELIST = outArras[2]
                                            env.IOS_RESOURCES = outArras[3]
                                        }
                                    }
                                }
                                def artifactreportName
                                if ("${PLATFORM}" == "android"){
                                    artifactreportName = "log/${env.ANDROID_CHANGELIST.replaceAll("[\r\n]+","")}.html"
                                    env.ANDROIDREPORTULTURL = env.BUILD_URL + 'artifact/' + artifactreportName
                                    echo "$env.ANDROIDREPORTULTURL"
                                }else{
                                    artifactreportName = "log/${env.IOS_CHANGELIST.replaceAll("[\r\n]+","")}.html"
                                    env.IOSREPORTULTURL = env.BUILD_URL + 'artifact/' + artifactreportName
                                    echo "$env.IOSREPORTULTURL"
                                }
                                echo "$artifactreportName"
                                def buildLogAndroidExists = fileExists "$artifactreportName"
                                if(buildLogAndroidExists){
                                    echo "1111"
                                    archiveArtifacts artifacts: "$artifactreportName", followSymlinks: false
                                }
                            }
                        }
                    }
                    stage('持久化配置文件'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && (env.ANDROID_UPLOADP4 == 'true' || env.IOS_UPLOADP4 == 'true')
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='持久化配置文件失败'
                                    bat """
                                    git config --global user.email "<EMAIL>"
                                    git config --global user.name "dgm_developer"
                                    git add config.ini
                                    git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                                    git push origin link-distrib-${PLATFORM}
                                    """
                                }
                            }
                        }
                    }
                    stage('上传打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='上传打包结果失败'
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    workspace = env.IOSWORKSPACE
                                }
                                def out
                                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                     out = bat(returnStdout: true, script: "python setup.py get_and_modify_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}").split('\n')
                                }
                                println(out)
                                println(out[0])
                                println(out[1])
                                println(out[2])
                                println(out[3])
                                def artifactsPath="x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/"
                                dir("${artifactsPath}"){
                                    def artifactName = out[2]
                                    archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                    def artifactUrl = env.BUILD_URL + 'artifact/' + out[2]
                                    echo "upload_url: ${artifactUrl}"
                                     if ("${PLATFORM}" == "android"){
                                        env.ANDROIDPACKAGERESULTURL = artifactUrl.trim()
                                        }
                                    else{
                                        env.IOSPACKAGERESULTURL = artifactUrl.trim()
                                    }
                                }

                                if (out[3] == "False"){
                                    currentBuild.result = 'UNSTABLE'
                                }
                            }
                        }
                    }
                    stage('判断是否单端失败'){
                          when {
                             expression {
                                 buildResultCode <= 3
                             }
                         }
                         steps{
                             script{
                                 if ("${PLATFORM}" == "android"){
                                     buildResultCode += 1
                                 }
                                 else{
                                     buildResultCode += 2
                                 }
                             }
                         }
                    }
                }
                post{
                    always{
                        //上传日志
                        script {
                            def build_log_android = "jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log"
                            def buildLogAndroidExists = fileExists "$build_log_android"
                            if(buildLogAndroidExists){
                                archiveArtifacts artifacts: "$build_log_android", followSymlinks: false
                            }
                            def build_log_ios = "jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log"
                            def buildLogIosExists = fileExists "$build_log_ios"
                            if(buildLogIosExists){
                                archiveArtifacts artifacts: "$build_log_ios", followSymlinks: false
                            }
                            //定义信息模板
                            env.ANDROIDBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.ANDROID_CHANGELIST}
[打包统计](${env.ANDROIDPACKAGERESULTURL})
[分发结果统计](${env.ANDROIDREPORTULTURL})
**打包数量**: ${env.ANDROIDTOTALCOUNT}
**成功数量**: ${env.ANDROIDSUCCCOUNT}
**成功ID**: ${env.ANDROID_SUCCESS_IDS}
**失败ID**: ${env.ANDROID_FAIL_IDS}"""
                            env.IOSBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.IOS_CHANGELIST}
[打包统计](${env.IOSPACKAGERESULTURL})
[分发结果统计](${env.IOSREPORTULTURL})
**打包数量**: ${env.IOSTOTALCOUNT}
**成功数量**: ${env.IOSSUCCCOUNT}
**成功ID**: ${env.IOS_SUCCESS_IDS}
**失败ID**: ${env.IOS_FAIL_IDS}"""
                            if (currentBuild.result != 'SUCCESS'){
                                env.ANDROIDBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.ANDROIDFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.ANDROIDERRORCOUNT}</font>"""
                                env.IOSBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.IOSFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.IOSERRORCOUNT}</font>"""
                            }
                            else{
                                env.ANDROIDBACKINFO += """
**失败数量**: ${env.ANDROIDFAILEDCOUNT}
**检查出错数量**: ${env.ANDROIDERRORCOUNT}"""
                                env.IOSBACKINFO += """
**失败数量**: ${env.IOSFAILEDCOUNT}
**检查出错数量**: ${env.IOSERRORCOUNT}"""
                            }
                            env.LOGBACKINFO = """
[安卓 unity日志](${env.BUILD_URL}artifact/jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log)
[ios unity日志](${env.BUILD_URL}artifact/jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log)"""

                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    echo "执行状态码： ${buildResultCode}"
                    def msg = """**结果**: link资源分发打包失败"""

                    if (buildResultCode == 1){
                        msg += """
<font color = "#dc143c">**单端成功**: 安卓(下次打包会自动把失败的一端打包)</font>
${env.ANDROIDBACKINFO}
"""
                    }
                    else if (buildResultCode == 2){
                        msg += """
<font color = "#dc143c">**单端成功**: ios(下次打包会自动把失败的一端打包)</font>
${env.IOSBACKINFO}
"""
                    }
                    msg += """
<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>
${env.LOGBACKINFO}
"""
                    echo "${msg}"
                    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033',content: msg
                    if (sendUserString != "") {
                        emailext to: "${sendUserString}", subject: "jenkins-饰品资源分发打包失败 #${env.BUILD_NUMBER}", body: msg
                    }
                }
            }
        }
        unstable{
            node('master') {
                script{
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg=""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}\n"""
                        }
                        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033',content: msg
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: "jenkins-饰品资源分发打包部分失败 #${env.BUILD_NUMBER}", body: msg
                        }
                    }
                }
            }
        }
        success {
            node('master') {
                script{
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg=""
                        if (isAndroidPackage == "true")
                        {
                            def android_resources_list = env.ANDROID_RESOURCES.split(/#/, -1)
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                            msg += ("${android_resources_list[0]}" != "") ? "**CDN分发资源**：${android_resources_list[0]}\n" : ""
                            msg += ("${android_resources_list[1]}" != "") ? "**版内分发资源**：${android_resources_list[1]}\n" : ""
                            msg += ("${android_resources_list[2]}" != "") ? "**热更分发资源**：${android_resources_list[2]}\n".replaceAll(",", "\n") : ""
                            msg += ("${android_resources_list[3]}" != "") ? "**特殊分支分发资源**：${android_resources_list[3]}\n".replaceAll(",", "\n") : ""
                        }
                        if (isIosPackage == "true")
                        {
                            def ios_resources_list = env.IOS_RESOURCES.split(/#/, -1)
                            msg+="""**ios**：${env.IOSBACKINFO}\n"""
                            msg += ("${ios_resources_list[0]}" != "") ? "**CDN分发资源**：${ios_resources_list[0]}\n" : ""
                            msg += ("${ios_resources_list[1]}" != "") ? "**版内分发资源**：${ios_resources_list[1]}\n" : ""
                            msg += ("${ios_resources_list[2]}" != "") ? "**热更分发资源**：${ios_resources_list[2]}\n".replaceAll(",", "\n") : ""
                            msg += ("${ios_resources_list[3]}" != "") ? "**特殊分支分发资源**：${ios_resources_list[3]}\n".replaceAll(",", "\n") : ""
                        }
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: "jenkins-饰品资源分发打包成功 #${env.BUILD_NUMBER}", body: msg
                        }
                    }
                }
            }
        }
    }
}

