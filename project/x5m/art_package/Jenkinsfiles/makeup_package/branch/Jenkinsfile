@Library('h3d_libs@v0.1.3') _
import groovy.json.JsonSlurper
def changeList = ""
def isAndroidPackage = "true"
def isIosPackage = "true"
def branch = ""
def messageSendUserList = ["<EMAIL>"]
/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
def buildResultCode = 0

def getFromNewP4(platform, branch){
    def tpl = '''
//x5_mobile/mr/b/${branch}/art_src/role/makeup/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/role/makeup/...
//x5_mobile/mr/b/${branch}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/makeup/... //${p4Client}/x5_mobile/mr/b/${branch}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/role/makeup/...
    '''
	
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([platform: platform, p4Client: '${P4_CLIENT}', branch: branch]).toString()  
}

def IsPackage(platform,isAndroidPackage,isIosPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    }else{
        return isIosPackage == "true"
    }
}

def getArtTrunkFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/zhaolz_test"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    url: '******************************:dgm/arttrunk.git']]]
}


def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}


pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 60, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
         booleanParam(name: 'force', defaultValue: false, description: '是否强更P4')
    }
    triggers {
        cron('H/15 0-3,3-11,13-23 * * *')
    }

    stages {
        stage('美术资源打包'){
            matrix {
                agent {
                    node{
                        label "makeup-branch-${PLATFORM}-package"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'android','ios'
                    }
                }
                stages{
					stage('配置初始化'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='配置初始化失败'
                                    getBuildUserInfo()
                                    // 判断配置 config.ini 是否存在，存在, 不处理, 否则判断 config.ini.example 是否存在, 存在，改名为config.ini
                                    def configFile = "config.ini"
                                    def configExampleFile = "config.ini.example"
                                    def configExists = fileExists "$configFile"
                                    def exampleExists = fileExists "$configExampleFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
                                    if (!configExists) {
                                        // 不存在
                                        if (exampleExists) {
                                            // 改名
                                            fileOperations([fileRenameOperation(destination: "${configFile}", source: "${configExampleFile}")])
                                            bat """
                                            python -m pip install --upgrade pip
                                            pip install -r ${env.WORKSPACE}\\gitlab\\Script\\scripts\\requirements.txt
                                            """
                                        }
                                    }
                                }
                            }
                        }
                    }
					stage('获取p4上的最新分支'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='获取p4上的最新分支失败'
                                    def branch_out = bat(returnStdout: true, script: "python setup.py get-mr-latest-branch").split('\n')
                                    println(branch_out[2])
                                    branch = branch_out[2].trim()
                                    echo "当前分支: ${branch}"
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='检查是否需要打包失败'
                                    def out = bat(returnStdout: true, script: "python setup.py get-branch-general-updates --tp makeup --branch ${branch} --first_changelist=${params.first_changelist}").split('\n')
                                    println(out[2])
                                    def jsonSlurper = new JsonSlurper()
                                    def result_map = jsonSlurper.parseText(out[2])

                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDWORKSPACE = env.WORKSPACE
                                        isAndroidPackage = result_map.update
                                        println(isAndroidPackage)
                                    }else{
                                        env.IOSWORKSPACE = env.WORKSPACE
                                        isIosPackage = result_map.update
                                        println(isIosPackage)
                                    }
                                    if (result_map.update == 'true'){
                                        env.PACKAGELIST = result_map.package_list
                                        env.UNITY_PACKAGE_LIST = result_map.unity_package_list
                                        changeList = result_map.changelist
                                        env.SEND_MESSAGE_USER_INFO = result_map.send_user
                                    }
                                }
                            }
                        }
                    }
                    stage('获取p4资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='获取p4资源失败'
                                bat """git config --global core.longpaths true"""
                                getArtTrunkFromGitlab()
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    platform = "android"
                                }else{
                                    platform = "iOS"
                                }
								
                                checkout perforce(credential: 'p4_dgm_jenkins_upwd',
                                    populate: syncOnly(force: "${params.force}", have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: env.P4_REVISION, quiet: true, revert: false),
                                    workspace: manualSpec(
                                        charset: 'none',
                                        cleanup: false,
                                        name: "jenkins-edition-makeup-branch-${platform}-package",,
                                        pinHost: false,
                                        spec: clientSpec(
                                            allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
                                            locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: getFromNewP4("${platform}", "${branch}")
                                        )
                                    )
                                )
                            }
                        }
                    }
                    stage('调用打包命令'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='调用打包命令失败'
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROIDWORKSPACE
                                    platform = "Android"
                                }else{
                                    workspace = env.IOSWORKSPACE
                                    platform = "iOS"
                                }
                                def jenkins_log = "${workspace}\\${branch}\\jenkins_log"
                                def logExists = fileExists "$jenkins_log"
                                if(!logExists)
                                {
                                    bat label:"日志文件夹",script:"mkdir ${branch}\\jenkins_log"
                                }
                                def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat(script: "unity -quit -batchmode -logFile ${build_pack_log} -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" -executeMethod H3DBuildTools.BuildMakeup -buildTarget ${platform} path=${env.UNITY_PACKAGE_LIST} out_path=${workspace}\\x5_mobile\\mr\\b\\${branch}\\ResourcePublish\\CDN\\SourceFiles\\${PLATFORM}\\assetbundles")
                            }
                        }
                    }
                    stage('计算打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='计算打包结果失败'
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }
                                    out = bat(returnStdout: true, script: "python setup.py print-package-result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client").split('\n')
                                    print(out[2])
                                    def count_result = readJSON text: out[2]
									env.UPLOADP4 = count_result.upload_p4
									if ("${PLATFORM}" == "android"){
                                        env.ANDROIDTOTALCOUNT = count_result.total_count
                                        env.ANDROIDSUCCCOUNT = count_result.succ_count
                                        env.ANDROIDFAILEDCOUNT = count_result.failed_count
                                        env.ANDROIDERRORCOUNT = count_result.error_count

                                        if (env.ANDROIDERRORCOUNT != "0" || env.ANDROIDFAILEDCOUNT != "0" || env.ANDROIDSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                    else{
                                        env.IOSTOTALCOUNT = count_result.total_count
                                        env.IOSSUCCCOUNT = count_result.succ_count
                                        env.IOSFAILEDCOUNT = count_result.failed_count
                                        env.IOSERRORCOUNT = count_result.error_count
                                        if (env.IOSERRORCOUNT != "0" || env.IOSFAILEDCOUNT != "0" || env.IOSSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }	
                                }
                            }
                        }
                    }
                    stage('提交p4'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.UPLOADP4 == 'true'
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='提交p4失败'
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                        platform = "android"
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                        platform = "iOS"
                                    }

                                    def submitP4Desc = "--Auto--|uploader:${env.SEND_MESSAGE_USER_INFO}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitP4Desc}"
									def desc = "${branch}" + "-" + "${changeList}"
                                    def out = bat(returnStdout: true,script: "python setup.py submit-files-to-p4  --root=${workspace} --platform=${platform} --tp=makeup --file_paths=${env.PACKAGELIST} --changelist=${desc} --desc=\"${submitP4Desc}\" --workspace=jenkins-edition-makeup-branch-${platform}-package")

                                    println(out)
                                    String[] outArras=out.split("\n")
                                    if (outArras.size() > 4){
                                        println("p4workspace下没有需要提交的内容！")
                                    }else{
                                        if ("${PLATFORM}" == "android"){
                                            env.ANDROID_CHANGELIST = outArras[2]
                                        }else{
                                            env.IOS_CHANGELIST = outArras[2]
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('上传打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='上传打包结果失败'
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    workspace = env.IOSWORKSPACE
                                }
                                def out
                                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                     out = bat(returnStdout: true, script: "python setup.py get-package-result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}").split('\n')
                                }
                                def artifactsPath="x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/"
                                dir("${artifactsPath}"){
                                    def artifactName = out[2]
                                    archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                    def artifactUrl = env.BUILD_URL + 'artifact/' + out[2]
                                    echo "upload_url: ${artifactUrl}"
                                     if ("${PLATFORM}" == "android"){
                                        env.ANDROIDPACKAGERESULTURL = artifactUrl.trim()
                                        }
                                    else{
                                        env.IOSPACKAGERESULTURL = artifactUrl.trim()
                                    }
                                }
                            }
                        }
                    }
                    stage('判断是否单端失败'){
                          when {
                             expression {
                                 buildResultCode <= 3
                             }
                         }
                         steps{
                             script{
                                 if ("${PLATFORM}" == "android"){
                                     buildResultCode += 1
                                 }
                                 else{
                                     buildResultCode += 2
                                 }
                             }
                         }
                    }
                }
                post{
                    always{
                        //上传日志
                        script {
                            def build_log_android = "${branch}/jenkins_log/android_${env.BUILD_NUMBER}.log"
                            def buildLogAndroidExists = fileExists "$build_log_android"
                            if(buildLogAndroidExists){
                                archiveArtifacts artifacts: "$build_log_android", followSymlinks: false
                            }
                            def build_log_ios = "${branch}/jenkins_log/ios_${env.BUILD_NUMBER}.log"
                            def buildLogIosExists = fileExists "$build_log_ios"
                            if(buildLogIosExists){
                                archiveArtifacts artifacts: "$build_log_ios", followSymlinks: false
                            }
                            //定义信息模板
                            env.ANDROIDBACKINFO = """
***原始资源 changelist***: ${changeList}
***ab包 changelist***: ${env.ANDROID_CHANGELIST}
***打包统计***: ${env.ANDROIDPACKAGERESULTURL}
***打包数量***: ${env.ANDROIDTOTALCOUNT}
***成功数量***: ${env.ANDROIDSUCCCOUNT}"""
                            env.IOSBACKINFO = """
***原始资源 changelist***: ${changeList}
***ab包 changelist***: ${env.IOS_CHANGELIST}
***打包统计***: ${env.IOSPACKAGERESULTURL}
***打包数量***: ${env.IOSTOTALCOUNT}
***成功数量***: ${env.IOSSUCCCOUNT}"""
                            if (currentBuild.result != 'SUCCESS'){
                                env.ANDROIDBACKINFO += """
<font color = "#dc143c">***失败数量***: ${env.ANDROIDFAILEDCOUNT}</font>
<font color = "#dc143c">***检查出错数量***: ${env.ANDROIDERRORCOUNT}</font>"""
                                env.IOSBACKINFO += """
<font color = "#dc143c">***失败数量***: ${env.IOSFAILEDCOUNT}</font>
<font color = "#dc143c">***检查出错数量***: ${env.IOSERRORCOUNT}</font>"""
                            }
                            else{
                                env.ANDROIDBACKINFO += """
***失败数量***: ${env.ANDROIDFAILEDCOUNT}
***检查出错数量***: ${env.ANDROIDERRORCOUNT}"""
                                env.IOSBACKINFO += """
***失败数量***: ${env.IOSFAILEDCOUNT}
***检查出错数量***: ${env.IOSERRORCOUNT}"""
                            }
                            env.LOGBACKINFO = """
[安卓 unity日志](${env.BUILD_URL}${branch}/artifact/jenkins_log/android_${env.BUILD_NUMBER}.log)
[ios unity日志](${env.BUILD_URL}${branch}/artifact/jenkins_log/ios_${env.BUILD_NUMBER}.log)"""

                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    echo "执行状态码： ${buildResultCode}"
                    def msg = """**结果**: 妆容版本内分支资源打包失败"""

                    if (buildResultCode == 1){
                        msg += """
<font color = "#dc143c">**单端成功**: 安卓(下次打包会自动把失败的一端打包)</font>
${env.ANDROIDBACKINFO}
"""
                    }
                    else if (buildResultCode == 2){
                        msg += """
<font color = "#dc143c">**单端成功**: ios(下次打包会自动把失败的一端打包)</font>
${env.IOSBACKINFO}
"""
                    }
                    msg += """
<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>
${env.LOGBACKINFO}
"""
                    echo "${msg}"
                    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9210a69f-33cf-4d37-ade4-767b36e8ac11',content: msg
                    if (sendUserString != "") {
                        emailext to: "${sendUserString}", subject: 'jenkins-版本内妆容分支打包失败', body: msg
                    }
                }
            }
        }
        unstable{
            node('master') {
                script{
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg=""""""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}\n${env.LOGBACKINFO}"""
                        }
                        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9210a69f-33cf-4d37-ade4-767b36e8ac11',content: msg
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: 'jenkins-版本内妆容分支打包部分失败', body: msg
                        }
                    }
                }
            }
        }
        success {
            node('master') {
                script{
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg=""""""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}\n"""
                        }
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: 'jenkins-妆容版本内分支打包成功', body: msg
                        }
                    }
                }
            }
        }
    }
}
