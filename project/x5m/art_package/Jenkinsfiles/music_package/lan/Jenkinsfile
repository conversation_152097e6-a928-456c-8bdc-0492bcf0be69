@Library('h3d_libs') _
import groovy.json.JsonSlurper
def distributeBeforePackage = ""    // 分发前的最新时间目录
def distributeAfterPackage = ""     // 分发后的最新时间目录
def uploadCDNPackage = ""           // 要上传CDN的时间目录
def distributeResultMsg = ""
def gitCommitStatus = ""
def currentRunSteps = 0
def messageSendUserList = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]


// 从x5mconfig的 audio分支获取 bgm资源
def getX5mConfigAudioFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/audio"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/crossplatform/audio'],
    [$class: 'SparseCheckoutPaths', sparseCheckoutPaths: [[path: 'bgm']]]],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'gitlab_dgm_developer_sshpk',
    url: '******************************:dgm/x5mconfig.git']]]
}

//x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/crossplatform/audio/bgm/
//x5_mobile/mr/b/X.X.X/ResourcePublish/CDN/SourceFiles/xml_sources/config/shared/music/music_size_info.xml

// 从x5mconfig的 cdn分支获取 music_size_info.xml配置
def getX5mConfigCDNFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/cdn"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/xml_sources']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'gitlab_dgm_developer_sshpk',
    url: '******************************:dgm/x5mconfig.git']]]
}


// 从p4上获取pack/levels/whole  分发工具代码
def getP4View() {
    // 正式环境映射
    def tpl = '''
//x5_mobile/mr/art_release/pack/levels/... //${p4Client}/x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/pack/levels/...
//x5mplan/dev_bak/MusicLevelDistributor/... //${p4Client}/x5_mobile/mobile_dancer/tools/MusicLevelDistributor/...
    '''
    // 测试环境映射
    //def tpl = '''

    //'''
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([p4Client: '${P4_CLIENT}']).toString()
}


def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}


// 根据执行到的步骤返回提示信息
def getErrorTipsMsg(steps) {
    def errorTipsMsg = ""
    if (steps == 0) {
        errorTipsMsg = "节点机可能掉线或者执行超时了, 请您重试下, 如果还是错误, 请联系我吧"
    } else if (steps == 1) {
        errorTipsMsg = "获取p4和gitlab资源可能超时了, 请重试下, 如果还是错误, 请联系我吧"
    } else if (steps == 2) {
        errorTipsMsg = "安装python依赖环境失败或者执行脚本失败了, 请重试下"
    } else if (steps == 3) {
        errorTipsMsg = "分发工具执行错误, 请联系程序组看下"
    } else {
        errorTipsMsg = "请联系我吧"
    }
    return errorTipsMsg
}


pipeline{
    agent {
        node{
            label "music-lan-cdn-package"
        }
    }
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 60, unit: 'MINUTES')
        buildDiscarder logRotator(numToKeepStr: env.numToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }

    parameters {
         string(name: 'build_user_email', defaultValue: '', description: '构建人邮箱(可不传)')
    }

    stages {
        stage('获取p4和gitlab资源'){
            steps{
                script{
                    currentRunSteps = 1
                    def userEmail = "${params.build_user_email}"
                    if (userEmail !="") {
                        if (!messageSendUserList.contains(userEmail)) {
                            messageSendUserList.add(userEmail)
                        }
                    }
                    getBuildUserInfo()
                    bat """
                    git config --global core.fscache false
                    git config --global core.quotepath false
                    git config --global core.longpaths true
                    """
                    // 清除 pack\levels目录下文件
                    dir('x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/pack/levels'){
                        deleteDir()
                    }
                    getX5mConfigAudioFromGitlab()
                    getX5mConfigCDNFromGitlab()
                    // 拉取资源p4
                    def view = getP4View()
                    echo "view:${view}"

                    def p4_root = pwd()
                    def p4_workspace = "jenkins-music-lan-cdn-package"
                    def python_view = view.replaceAll("\n", "#")
                    bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${p4_root} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=true")

//                     checkout perforce(credential: 'p4_dgm_jenkins_upwd',
//                         populate: syncOnly(force: true, have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: env.P4_REVISION, quiet: true, revert: false),
//                         workspace: manualSpec(
//                             charset: 'none',
//                             cleanup: false,
//                             name: "jenkins-music-lan-cdn-package",
//                             pinHost: false,
//                             spec: clientSpec(
//                                 allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
//                                 locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: view
//                             )
//                         )
//                     )
                    // echo "P4_CLIENT:${P4_CLIENT}"
                }
            }
        }
        stage('分发前最新时间目录'){
            steps{
                dir("pyframe-pipeline/project/x5m/art_package/scripts"){
                    script{
                        currentRunSteps = 2
//                         bat """
//                         python -m pip install --upgrade pip
//                         pip install -r requirements.txt
//                         """
                        def package_out = bat(returnStdout: true, script: "python setup.py get_latest_timestamp_package --path=${workspace}\\x5_mobile\\mr\\Resources\\ResourcePublish\\CDN\\SourceFiles\\pack\\levels")
                        println("分发前的:${package_out}")
                        String[] packageArray=package_out.split("\n")
                        println("package array: ${packageArray}")
                        distributeBeforePackage = packageArray[2]
                    }
                }
            }
        }
        stage('调用分发命令'){
            steps{
                dir('x5_mobile/mobile_dancer/tools/MusicLevelDistributor/exe'){
                    script{
                        currentRunSteps = 3
                        def cdnSourcePath = "${env.WORKSPACE}\\x5_mobile\\mr\\Resources\\ResourcePublish\\CDN\\SourceFiles"
                        def musicSizeInfoPath = "${cdnSourcePath}\\xml_sources\\config\\shared\\music\\music_size_info.xml"
                        bat(script: "MusicLevelDistributor.exe ${cdnSourcePath} ${musicSizeInfoPath}")
                    }
                }
            }
        }
        stage('统计分发结果'){
            steps{
                dir("pyframe-pipeline/project/x5m/art_package/scripts"){
                    script{
                        currentRunSteps = 4
                        def out = bat(returnStdout: true, script: "python setup.py get_distribute_result --path=${workspace}\\x5_mobile\\mobile_dancer\\tools\\MusicLevelDistributor\\exe\\log").split('\n')
                        println(out[2])
                        def count_result = readJSON text: out[2]
                        println("统计结果count result: ${count_result}")
                        env.DISTRIBUTE_RESULT = count_result.distribute_result
                        env.DISTRIBUTE_LOG_NAME = count_result.distribute_log_name
                        if (env.DISTRIBUTE_RESULT == "true") {
                            distributeResultMsg = "成功"
                        } else {
                            distributeResultMsg = "失败"
                        }
                        if (count_result.size() > 2){
                            env.COPY_TOTAL = count_result.copy_total
                        }
                    }
                }
            }
        }
        stage('分发后最新时间目录'){
            steps{
                dir("pyframe-pipeline/project/x5m/art_package/scripts"){
                    script{
                        currentRunSteps = 5
                        def out = bat(returnStdout: true, script: "python setup.py get_latest_timestamp_package --path=${workspace}\\x5_mobile\\mr\\Resources\\ResourcePublish\\CDN\\SourceFiles\\pack\\levels")
                        println(out)
                        String[] afterArray=out.split("\n")
                        println("after array: ${afterArray}")
                        distributeAfterPackage = afterArray[2]
                        if (distributeAfterPackage > distributeBeforePackage) {
                            uploadCDNPackage = distributeAfterPackage.trim()
                            // 同时写入translog日志
                            bat(returnStdout: true, script: "python setup.py package_store_trans_log --path=${workspace}\\x5_mobile\\mr\\Resources\\ResourcePublish\\CDN\\SourceFiles\\pack\\levels --package_name=${uploadCDNPackage}")
                        }
                    }
                }
            }
        }
        stage('并行上传'){
            //when {
            //    expression {
            //        return env.DISTRIBUTE_RESULT == "true" && env.COPY_TOTAL != "0"
            //    }
            //}
            parallel {
                stage('上传p4'){
                    steps {
                        dir("pyframe-pipeline/project/x5m/art_package/scripts")
                        {
                            script {
                                echo "准备上传p4"
                                def submitP4Desc = "uploader:${params.build_user_email}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                echo "${submitP4Desc}"
                                def out = bat(returnStdout: true,script: "python setup.py package_submit_to_p4  --submit_path=${env.WORKSPACE}/x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/pack/levels --desc=\"${submitP4Desc}\" --workspace=jenkins-music-lan-cdn-package")
                                String[] outArras=out.split("\n")
                                echo "${outArras}"
                                if (outArras.size() > 4){
                                    println("p4workspace下没有需要提交的内容！")
                                } else {
                                    env.P4_SUBMIT_CHANGELIST = outArras[2]
                                }
                                echo "提交p4结束"
                            }
                        }
                    }
                }
                stage('上传gitlab'){
                    steps {
                        // 上传变更了的 x5mconfig工程的 cdn分支的 config/shared/music/music_size_info.xml文件
                        dir("x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/xml_sources")
                        {
                            script {
                                echo "准备上传gitlab"
                                bat"""
                                git config --global user.email <EMAIL>
                                git config --global user.name <EMAIL>
                                git remote set-<NAME_EMAIL>:dgm/x5mconfig.git
                                git checkout cdn
                                git add config/shared/music/music_size_info.xml
                                git commit -m "jenkins music to cdn lines. build number: ${env.BUILD_NUMBER}"
                                git pull origin cdn
                                git push origin cdn
                                """
                                echo "上传gitlab结束"
                                env.COMMIT_GITLAB = "true"
                                if (env.COMMIT_GITLAB == "true") {
                                    gitCommitStatus = "成功"
                                } else {
                                    gitCommitStatus = "失败"
                                }
                            }
                        }
                    }
                }
                stage('上传内网CDN'){
                    steps {
                        dir("pyframe-pipeline/project/x5m/art_package/scripts")
                        {
                            script {
                                println("准备上传cdn")
                                println("待上传的CDN时间目录:${uploadCDNPackage}")
                                def cdn_out = bat(returnStdout: true,script: "python setup.py upload_files_to_cdn --cdn_type=lan --package_names=${uploadCDNPackage} --path=${env.WORKSPACE}/x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/pack/levels").split("\n")
                                echo "cdn 上传返回的值:${cdn_out}"
                                println(cdn_out[2])
                                def cdn_count_result = readJSON text: cdn_out[2]
                                println("统计结果cdn_count_result: ${cdn_count_result}")
                                env.CDN_UPLOAD_TOTAL = cdn_count_result.upload_total
                                env.CDN_UPLOAD_SUCCESS_TOTAL = cdn_count_result.upload_success_total
                                env.CDN_UPLOAD_FAILED_TOTAL = cdn_count_result.upload_failed_total
                                env.CDN_UPLOAD_FAILED_NAMES = cdn_count_result.failed_names
                                echo "上传cdn结束"
                                echo "流水线当前状态0: ${currentBuild.result}"
                                echo "上传失败统计: ${env.CDN_UPLOAD_FAILED_TOTAL}"
                                if (env.CDN_UPLOAD_FAILED_TOTAL > "0") {
                                    currentBuild.result = 'FAILURE'
                                }
                                echo "流水线当前状态1: ${currentBuild.result}"
                            }
                        }
                        script {
                            if (env.CDN_UPLOAD_FAILED_NAMES) {
                                writeFile encoding: 'UTF8', file: 'failed_upload_names.txt', text: "${env.CDN_UPLOAD_FAILED_NAMES}"
                            }
                        }
                    }
                }
                /*stage('上传Ftp'){
                    steps {
                        dir("pyframe-pipeline/project/x5m/art_package/scripts")
                        {
                            script {
                                if (uploadCDNPackage != "") {
                                    echo "准备上传Ftp"
                                    echo "上传文件文件夹名称${uploadCDNPackage}"
                                    def ftp_out = bat(returnStdout: true,script: "python setup.py upload_files_to_ftp --path=${env.WORKSPACE}/x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/pack/levels/${uploadCDNPackage}").split("\n")
                                    echo "ftp 上传返回的值:${ftp_out}"
                                    println(ftp_out[2])
                                    def ftp_result = readJSON text: ftp_out[2]
                                    println("统计结果ftp_result: ${ftp_result}")
                                    env.FTP_UPLOAD_RESULT = ftp_result.upload_ftp
                                    echo "上传Ftp结束"
                                }
                            }
                        }
                    }
                }*/
            }
        }
    }
    post {
        // 使用always 方法上传制品 archiveArtifacts制品管理
        always{
            dir("x5_mobile/mobile_dancer/tools/MusicLevelDistributor/exe/log") {
                script {
                    if (env.DISTRIBUTE_LOG_NAME){
                        def htmlFileName = "${env.DISTRIBUTE_LOG_NAME}.html"
                        def htmlExists = fileExists "${htmlFileName}"
                        echo "htmlExists: ${htmlExists}"

                        if(htmlExists){
                            archiveArtifacts artifacts: "${htmlFileName}", followSymlinks: false
                            env.ARTIFACT_DISTRIBUTE_LOG = env.BUILD_URL + 'artifact/' + "${htmlFileName}"
                            echo env.ARTIFACT_DISTRIBUTE_LOG
                        }
                    }
                }
            }
            script {
                def failedTxt="failed_upload_names.txt"
                def invalidFileLog="invalid_file_names.log"
                def logExists = fileExists invalidFileLog
                def failedExists = fileExists failedTxt
                echo "log状态: ${logExists}, fail exists: ${failedExists}"
                if(failedExists){
                    archiveArtifacts artifacts: failedTxt, followSymlinks: false
                    env.ARTIFACT_DISTRIBUTE_LOG = env.BUILD_URL + 'artifact/' + failedTxt
                    echo env.ARTIFACT_DISTRIBUTE_LOG
                }
                if(logExists){
                    archiveArtifacts artifacts: invalidFileLog, followSymlinks: false
                    env.ARTIFACT_INVALID_LOG = env.BUILD_URL + 'artifact/' + invalidFileLog
                    echo env.ARTIFACT_INVALID_LOG
                }
            }
        }
        // 通知规则：1. 当本次执行有执行人时，无论执行状态，都给执行人发送信息  2. 本次执行失败，向 X5M-策划自动化助手 机器人发送信息
        failure {
            script {
                def msg = """*结果*: BGM工具分发上传内网CDN失败
**日志链接**: http://jenkins-x5mobile.h3d.com.cn/job/bgm_package/job/music_package/${env.BUILD_NUMBER}/console
\n"""
                if (env.ARTIFACT_INVALID_LOG) {
                    msg += "[上传文件超过1MB记录](${env.ARTIFACT_INVALID_LOG})\n"
                }
                def errorMsg = getErrorTipsMsg(currentRunSteps)
                if (errorMsg) {
                    msg += "友情提示信息: ${errorMsg}"
                }
                workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=95e61f80-88b1-43de-9be5-96a612b8c46c',content: msg
                if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                    if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                        messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    }
                }
                def sendUserString = messageSendUserList.join(",")
                if ("${sendUserString}" !="") {
                    emailext to: "${sendUserString}", subject: 'jenkins-bgm音乐文件分发上传失败', body: msg
                }
            }
        }
        success {
            script{
                def msg = """**BGM工具**:
**分发状态**: ${distributeResultMsg}
**COPY数量**: ${env.COPY_TOTAL}
[BGM分发日志](${env.ARTIFACT_DISTRIBUTE_LOG})"""
                if (env.ARTIFACT_INVALID_LOG) {
                    msg += "[上传文件超过1MB记录](${env.ARTIFACT_INVALID_LOG})"
                }
                if (uploadCDNPackage != "") {
                    msg += """
**生成的时间戳目录**: ${uploadCDNPackage}\n"""
                }
                if (env.COPY_TOTAL != "0"){
                    msg += """**CDN上传统计**：
**上传数量**: ${env.CDN_UPLOAD_TOTAL}
**成功数量**: ${env.CDN_UPLOAD_SUCCESS_TOTAL}
<font color = "#dc143c">**失败数量**: ${env.CDN_UPLOAD_FAILED_TOTAL}</font>
\n"""
                if (env.CDN_UPLOAD_FAILED_NAMES) {
                    msg += """[失败文件](${env.ARTIFACT_DISTRIBUTE_LOG})\n"""
                }
                msg += """**P4上传统计**:
**上传P4 changelist**: ${env.P4_SUBMIT_CHANGELIST}\n
**gitlab上传状态**: ${gitCommitStatus}\n"""
                }
                /*msg += """**FTP上传结果**:
**FTP上传状态**: ${env.FTP_UPLOAD_RESULT}\n"""*/
                if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                    if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                        messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    }
                }
                def sendUserString = messageSendUserList.join(",")
                if ("${sendUserString}" != "") {
                    emailext to: "${sendUserString}", subject: 'jenkins-bgm音乐文件分发上传成功', body: msg
                }
            }
        }
    }
}