@Library('h3d_libs') _

def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo '获取不到构建的用户邮箱'
            env.BUILD_USER_EMAIL = ''
        }
    }
}

def getArtTrunkFromGitlab() {
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: '*/release']],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    url: '******************************:dgm/arttrunk.git']]]
}

def get_platform_info(changelist, platform_info) {
    def message = """
**原始资源 changelist**: ${changelist}
**ab包 changelist**: ${platform_info.changelist}
[打包统计](${platform_info.package_result_url})
[分发结果统计](${platform_info.report_url})
**打包数量**: ${platform_info.total_count}
**成功数量**: ${platform_info.succ_count}
<font color = "#dc143c">**失败数量**: ${platform_info.failed_count}</font>
<font color = "#dc143c">**检查出错数量**: ${platform_info.error_count}</font>
        """
    return message
}

def get_resource_info(platform, back_info, resource_list) {
    def message = """**${platform}**：${back_info}\n""" +
        ("${resource_list[0]}" != '') ? "**CDN分发资源**：${resource_list[0]}\n" : '' +
        ("${resource_list[1]}" != '') ? "**版内分发资源**：${resource_list[1]}\n" : '' +
        ("${resource_list[2]}" != '') ? "**热更分发资源**：${resource_list[2]}\n".replaceAll(',', '\n') : '' +
        ("${resource_list[3]}" != '') ? "**特殊分支分发资源**：${resource_list[3]}\n".replaceAll(',', '\n') : ''
    return message
}

messageSendUserList = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',  '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']

// 用于存储platforminfo
PLATFORM_INFO = [:]

/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
buildResultCode = 0
changelist = 0
pipeline {
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
        text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
        text(name: 'PIPELINE_TP', defaultValue: 'ingame_prefabs', description: '使用哪个模板打包资源')
        booleanParam(name: 'force', defaultValue: true, description: '是否强更P4')
    }
    //     因为五点到六点半p4需要备份数据，流水线会执行失败
    triggers {
        cron('H/15 0-4,8-23 * * *')
    }
    stages {
        stage('美术资源打包') {
            matrix {
                agent {
                    node {
                        label 'mv_ingame'
                        customWorkspace "workspace/${PLATFORM}"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'ios', 'android'
                    }
                }
                environment {
                    GIT_PERSISTENT_BRANCH = "mv-ingame-${PLATFORM}"
                    GIT_PRESISTENT_PATH = 'pyframe-pipeline/project/x5m/art_package/config'
                    P4_WORKSPACE = "mv-ingame-2024-${PLATFORM}"
                }
                stages {
                    stage('配置初始化') {
                        steps {
                            dir(GIT_PRESISTENT_PATH) {
                                script {
                                    PLATFORM_INFO[PLATFORM] = [:]
                                    if (!fileExists('.git/config')) {
                                        bat '''
                                        del /s /f /q .
                                        '''
                                    }
                                    env.ErrInfo = '配置初始化失败'
                                    getBuildUserInfo()
                                    def configFile = 'config.ini'
                                    def configExists = fileExists "$configFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}")
                                    if (!configExists) {
                                        bat """
                                        git clone -b ${GIT_PERSISTENT_BRANCH} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                                        """
                                    }
                                    bat """
                                    git clean -xdf
                                    git reset --hard HEAD
                                    git pull origin ${GIT_PERSISTENT_BRANCH}
                                    """
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包') {
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts') {
                                script {
                                    env.ErrInfo = '检查是否需要打包失败'
                                    def out = bat(returnStdout: true, script: "python setup.py get_updates --tp=${params.PIPELINE_TP} --first_changelist=${params.first_changelist}").split('\n')
                                    def result_map = readJSON text: out[2]
                                    PLATFORM_INFO[PLATFORM].update = result_map.update == 'true'
                                    PLATFORM_INFO[PLATFORM].update_origin = result_map.update
                                    if (result_map.update == 'true') {
                                        env.PACKAGELIST = result_map.package_list
                                        changelist = result_map.changelist
                                        env.SEND_MESSAGE_USER_INFO = result_map.send_user
                                        p4_view = result_map.p4_view.split(';')
                                        println(p4_view)
                                    }else {
                                        currentBuild.result = 'ABORTED'
                                    }
                                    println('result_map.package_list:' + result_map.package_list)
                                }
                            }
                        }
                    }
                    stage('获取p4上的最新分支'){
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].update
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='获取p4上的最新分支失败'
                                    def branch_out = bat(returnStdout: true, script: "python setup.py get_mr_latest_branch").split('\n')
                                    println(branch_out[2])
                                    branch = branch_out[2].trim()
                                }
                            }
                        }
                    }
                    stage('清理资源') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].update
                            }
                        }
                        steps {
                            dir('x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/ingame_prefabs') {
                                deleteDir()
                            }
                        }
                    }
                    stage('获取p4资源') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].update
                            }
                        }
                        steps {
                            retry(2) {
                                script {
                                    env.ErrInfo = '获取p4资源失败'
                                    bat '''
                                        git config --system core.longpaths true
                                        '''
                                    getArtTrunkFromGitlab()
                                    try {
                                        timeout(time: 15, unit: 'MINUTES') {
                                            bat(returnStdout: false, script: 'python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync ' +
                                                "--p4_root=${env.WORKSPACE} --workspace=${P4_WORKSPACE} --force_sync=${params.force} --resource=${params.PIPELINE_TP} " +
                                                "--branch=${branch}"
                                                )
                                        }
                                    } catch (err) {
                                        def sendUserString = messageSendUserList.join(',')
                                        if (sendUserString != '') {
                                            emailext to: "${sendUserString}", subject: "jenkins-mv-ingame #${env.BUILD_NUMBER}", body: '获取p4资源卡住超时，重试中'
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('调用打包命令') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].update
                            }
                        }
                        steps {
                            script {
                                env.ErrInfo = '调用unity打包失败'
                                def jenkins_log = "jenkins_log\\${env.BUILD_NUMBER}"
                                def logExists = fileExists "$jenkins_log"
                                if (!logExists) {
                                    bat label:'日志文件夹', script:"mkdir jenkins_log\\${env.BUILD_NUMBER}"
                                }
                                def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat(script: 'unity -quit -batchmode ' +
                                    "-logFile ${build_pack_log} " +
                                    '-projectPath "x5_mobile\\mobile_dancer\\arttrunk\\client\" ' +
                                    '-executeMethod H3DBuildTools.BuildArt ' +
                                    "-buildTarget ${PLATFORM} " +
                                    "path=${env.PACKAGELIST} " +
                                    "out_path=x5_mobile\\mr\\art_release\\cs\\${PLATFORM}\\assetbundles")
                            }
                        }
                    }
                    stage('计算打包结果') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].update
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts') {
                                script {
                                    env.ErrInfo = '计算打包结果失败'

                                    out = bat(returnStdout: true, script: "python setup.py print_package_result --path ${env.WORKSPACE}\\x5_mobile\\mobile_dancer\\arttrunk\\client ").split('\n')
                                    print(out[2])
                                    def count_result = readJSON text: out[2]
                                    PLATFORM_INFO[PLATFORM].upload_p4 = count_result.upload_p4
                                    PLATFORM_INFO[PLATFORM].total_count = count_result.total_count
                                    PLATFORM_INFO[PLATFORM].succ_count = count_result.succ_count
                                    PLATFORM_INFO[PLATFORM].failed_count = count_result.failed_count
                                    PLATFORM_INFO[PLATFORM].error_count = count_result.error_count
                                    if (count_result.error_count != 0 || count_result.failed_count != 0 || count_result.succ_count == 0) {
                                        PLATFORM_INFO[PLATFORM].result = 'UNSTABLE'
                                        currentBuild.result = 'UNSTABLE'
                                    }
                                    echo "PLATFORM_INFO: ${PLATFORM_INFO}"
                                }
                            }
                        }
                    }
                    stage('上传Unity打包结果') {
                        steps {
                            script {
                                def out
                                dir('pyframe-pipeline/project/x5m/art_package/scripts') {
                                    out = bat(
                                            returnStdout: true,
                                            script: 'python setup.py get_package_result '+
                                                    "--path ${WORKSPACE}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}"
                                    ).split('\n')
                                }
                                def artifactsPath = 'x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/'
                                dir(artifactsPath) {
                                    def artifactName = out[2]
                                    archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                    def artifactUrl = env.BUILD_URL + 'artifact/' + out[2]
                                    echo "upload_url: ${artifactUrl}"
                                    PLATFORM_INFO[PLATFORM].package_result_url = artifactUrl.trim()
                                }
                            }
                        }
                    }
                    stage('提交p4') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].upload_p4 == 'true'
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts') {
                                script {
                                    env.ErrInfo = '提交p4失败'
                                    def submitP4Desc = "uploader:${env.SEND_MESSAGE_USER_INFO}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitP4Desc}"
                                    def out = bat(returnStdout: true, script: 'python setup.py submit_files_to_p4 ' +
                                    "--root=${WORKSPACE} --platform=${PLATFORM} --file_paths=${env.PACKAGELIST} " +
                                    "--tp=${params.PIPELINE_TP} --changelist=${changelist} --desc=\"${submitP4Desc}\" " +
                                    "--workspace=${P4_WORKSPACE}")
                                    println(out)
                                    String[] outArras = out.split('\n')
                                    if (outArras.size() > 4) {
                                        println('p4workspace下没有需要提交的内容！')
                                }else {
                                        PLATFORM_INFO[PLATFORM].changelist = outArras[2]
                                        PLATFORM_INFO[PLATFORM].resources = outArras[3]
                                    }
                                }
                            }
                        }
                    }
                    stage('持久化配置文件') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].upload_p4 == 'true'
                            }
                        }
                        steps {
                            dir(GIT_PRESISTENT_PATH) {
                                script {
                                    env.ErrInfo = '持久化配置文件失败'
                                    bat """
                            git config --global user.email "<EMAIL>"
                            git config --global user.name "dgm_developer"
                            git add config.ini
                            git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                            git push origin ${GIT_PERSISTENT_BRANCH}
                            """
                                }
                            }
                        }
                    }
                    stage('上传打包结果') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].upload_p4 == 'true'
                            }
                        }
                        steps {
                            script {
                                env.ErrInfo = '上传打包结果失败'
                                echo "当前目录: ${pwd()}"
                                artifactreportName = "log/${PLATFORM_INFO[PLATFORM].changelist.replaceAll('[\r\n]+', '')}.html"
                                PLATFORM_INFO[PLATFORM].report_url = env.BUILD_URL + 'artifact/' + artifactreportName
                                echo "$artifactreportName"
                                def buildLogExists = fileExists "$artifactreportName"
                                if (buildLogExists) {
                                    echo '1111'
                                    archiveArtifacts artifacts: "$artifactreportName", followSymlinks: false
                                }
                            }
                        }
                    }
                    stage('判断是否单端失败') {
                        when {
                            expression {
                                buildResultCode <= 3
                            }
                        }
                        steps {
                            script {
                                if ("${PLATFORM}" == 'android') {
                                    buildResultCode += 1
                                }
                                 else {
                                    buildResultCode += 2
                                 }
                            }
                        }
                    }
                }
                post {
                    always {
                        //上传日志
                        script {
                            def build_log = "jenkins_log/${env.BUILD_NUMBER}/${PLATFORM}_${env.BUILD_NUMBER}.log"
                            def buildLogExists = fileExists "$build_log"
                            if (buildLogExists) {
                                archiveArtifacts artifacts: "$build_log", followSymlinks: false
                            }
                            env.LOGBACKINFO = """\n""" +
                                    """[安卓\tunity日志](http://jenkins-x5mobile.h3d.com.cn/job/art_package/job/action_package/${env.BUILD_NUMBER}/artifact/jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log)\n""" +
                                    """[安卓\t打包结果](${PLATFORM_INFO['android'].package_result_url})\n""" +
                                    """[ios\tunity日志](http://jenkins-x5mobile.h3d.com.cn/job/art_package/job/action_package/${env.BUILD_NUMBER}/artifact/jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log)\n""" +
                                    """[ios\t打包结果](${PLATFORM_INFO['ios'].package_result_url})"""
                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != '') {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != '') {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(',')
                    echo "执行状态码： ${buildResultCode}"
                    def msg = """**结果**: 动作资源分发打包失败"""
                    if (buildResultCode == 1) {
                        def back_info_android = get_platform_info(changelist, PLATFORM_INFO.android)
                        msg += """<font color = "#dc143c">**单端成功**: 安卓(下次打包会自动把失败的一端打包)</font>\n${back_info_android}\n"""
                    }
                    else if (buildResultCode == 2) {
                        def back_info_ios = get_platform_info(changelist, PLATFORM_INFO.ios)
                        msg += """\n<font color = "#dc143c">**单端成功**: ios(下次打包会自动把失败的一端打包)</font>\n${back_info_ios}\n"""
                    }
                    msg += """\n<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>\n${env.LOGBACKINFO}\n"""
                    echo "${msg}"
                    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033', content: msg
                    if (sendUserString != '') {
                        emailext to: "${sendUserString}", subject: 'jenkins-mv对局道具打包成功', body: msg
                    }
                }
            }
        }
        unstable {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != '') {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != '') {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(',')
                    if (PLATFORM_INFO['android'].update || PLATFORM_INFO['ios'].update) {
                        def msg = ''
                        if (PLATFORM_INFO['android'].update) {
                            def back_info_android =  get_platform_info(changelist, PLATFORM_INFO.android)
                            msg += """**安卓**：${back_info_android}\n"""
                        }
                        if (PLATFORM_INFO['ios'].update) {
                            def back_info_ios =  get_platform_info(changelist, PLATFORM_INFO.ios)
                            msg += """**ios**：${back_info_ios}\n${env.LOGBACKINFO}"""
                        }
                        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033', content: msg
                        if (sendUserString != '') {
                            emailext to: "${sendUserString}", subject: 'jenkins-mv对局道具打包成功', body: msg
                        }
                    }
                }
            }
        }
        success {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != '') {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != '') {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(',')

                    if (PLATFORM_INFO['android'].update || PLATFORM_INFO['ios'].update) {
                        def msg = ''
                        if (PLATFORM_INFO['android'].update) {
                            def back_info_android =  get_platform_info(changelist, PLATFORM_INFO.android)
                            msg += """**安卓**：${back_info_android}\n"""
                        }
                        if (PLATFORM_INFO['android'].update) {
                            def back_info_ios =  get_platform_info(changelist, PLATFORM_INFO.ios)
                            msg += """**ios**：${back_info_ios}\n${env.LOGBACKINFO}"""
                        }
                        // workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033',content: msg
                        if (sendUserString != '') {
                            emailext to: "${sendUserString}", subject: 'jenkins-mv对局道具打包成功', body: msg
                        }
                    }
                }
            }
        }
    }
}
