@Library('h3d_libs') _
import groovy.json.JsonSlurper
import groovy.json.JsonOutput


def toArgvJson(val) {
    return JsonOutput.toJson(JsonOutput.toJson(val))
}

def check_or_break(raw_out) {
    println raw_out
    def out =  raw_out.trim().split('\n')
    out = out.last()
    def result_info =new JsonSlurper().parseText(out)
    if (result_info[0] != 0) {
        currentBuild.result = 'FAILURE'
        error(result_info[1])
    }
    echo "check_or_break return:${result_info[1]}"
    return result_info[1]
}


node{
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo '获取不到构建的用户邮箱'
            env.BUILD_USER_EMAIL = ''
        }
    }
}

def getArtTrunkFromGitlab() {
    checkout changelog: false, poll: false,
            scm: [$class: 'GitSCM',
                  branches: [[name: '*/release']],
                  doGenerateSubmoduleConfigurations: false,
                  extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
                  submoduleCfg: [],
                  userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
                                       url: '******************************:dgm/arttrunk.git']]]
}

def get_platform_info(changelist, platform_info) {
    def message = """
**原始资源 changelist**: ${changelist}
**ab包 changelist**: ${platform_info.changelist}
[打包统计](${platform_info.package_result_url})
[分发结果统计](${platform_info.report_url})
**打包数量**: ${platform_info.total_count}
**成功数量**: ${platform_info.succ_count}
<font color = "#dc143c">**失败数量**: ${platform_info.failed_count}</font>
<font color = "#dc143c">**检查出错数量**: ${platform_info.error_count}</font>
        """
    return message
}

def get_resource_info(platform, back_info, resource_list) {
    def message = """**${platform}**：${back_info}\n""" +
            ("${resource_list[0]}" != '') ? "**CDN分发资源**：${resource_list[0]}\n" : '' +
            ("${resource_list[1]}" != '') ? "**版内分发资源**：${resource_list[1]}\n" : '' +
            ("${resource_list[2]}" != '') ? "**热更分发资源**：${resource_list[2]}\n".replaceAll(',', '\n') : '' +
            ("${resource_list[3]}" != '') ? "**特殊分支分发资源**：${resource_list[3]}\n".replaceAll(',', '\n') : ''
    return message
}

messageSendUserList = [
        '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',  '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']

// 用于存储platforminfo
PLATFORM_INFO = [:]
PYTHON = 'C:\\Python\\PipelinePython368\\pipe-python.exe'
/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
buildResultCode = 0
changelist = 0

pipeline {
    agent {label 'plaza-role-ios'}
    options {
        timestamps()
        disableConcurrentBuilds()
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
        text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
        booleanParam(name: 'force', defaultValue: true, description: '是否强更P4')
    }

    //     因为五点到六点半p4需要备份数据，流水线会执行失败
    triggers {
        cron('H/15 0-4,8-23 * * *')
    }
    stages {
        stage('炫舞广场打包'){
            matrix {
                agent {
                    node {
                        label "plaza-role-${PLATFORM}"
                        customWorkspace "plaza_role/${PLATFORM}"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'ios','android'
                    }
                }
                environment {
                    GIT_PERSISTENT_BRANCH = "plaza-role-${PLATFORM}"
                    GIT_PRESISTENT_PATH = "${env.WORKSPACE}/pyframe-pipeline/project/x5m/art_package/config"
                    P4_WORKSPACE = "plaza-role-${PLATFORM}"
                    WS = "${env.WORKSPACE.replace("\\", "/").toLowerCase()}"

                }
                stages {
                    stage('配置初始化') {
                        steps {
                            dir(GIT_PRESISTENT_PATH) {
                                script {
                                    PLATFORM_INFO[PLATFORM] = [:]
                                    if (!fileExists('.git/config')) {
                                        bat '''
                                del /s /f /q .
                            '''
                                    }
                                    env.ErrInfo = '配置初始化失败'
                                    def configFile = 'config.ini'
                                    def configExists = fileExists "$configFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}")
                                    if (!configExists) {
                                        bat """
                                git clone -b ${GIT_PERSISTENT_BRANCH} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                            """
                                    }
                                    bat """
                            git fetch origin 
                            git checkout -f ${GIT_PERSISTENT_BRANCH}
                            git reset --hard origin/${GIT_PERSISTENT_BRANCH}
                        """
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包') {
                        steps {
                            dir('pyframe-pipeline/project/x5m') {
                                script {
                                    env.ErrInfo = '检查是否需要打包失败'
                                    def extra = toArgvJson([
                                            first_changelist: params.first_changelist,
                                    ])
                                    def init = toArgvJson([
                                            config: "${GIT_PRESISTENT_PATH}/config.ini"
                                    ])
                                    def out = bat(returnStdout: true, script: "$PYTHON pipeline.py --kind=plaza_role --method=check_update --extra=${extra} --init=${init}")
                                    def result_map = check_or_break(out)
                                    if (result_map.update in [false, 'false'] ) {
                                        currentBuild.result = "ABORTED"

                                    }
                                    PLATFORM_INFO[PLATFORM].PACKAGELIST = result_map.ids
                                    PLATFORM_INFO[PLATFORM].update = result_map.update
                                    PLATFORM_INFO[PLATFORM].changelist = result_map.changelist
                                    PLATFORM_INFO[PLATFORM].send_users = result_map.send_user
                                    messageSendUserList.addAll(result_map.send_user)
                                    changelist = result_map.changelist
                                    PLATFORM_INFO[PLATFORM].id_dirs = result_map.id_dirs
                                }
                            }
                        }
                    }
                    stage("获取arttrunk"){
                        steps{
                            script{
                                getArtTrunkFromGitlab()
                            }
                        }
                    }
                    stage('获取p4资源') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].update
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m') {
                                script {
                                    env.ErrInfo = '获取p4资源失败'
                                    bat '''git config --system core.longpaths true'''
                                    def extra = toArgvJson([
                                            p4_root: env.WS,
                                            workspace: P4_WORKSPACE,
                                            force_sync: params.force,
                                            platform: PLATFORM,
                                            files: PLATFORM_INFO[PLATFORM].PACKAGELIST,
                                    ])
                                    def out = bat(
                                            returnStdout: true,
                                            script: "$PYTHON pipeline.py --kind=plaza_role --method=pull_p4_update --extra=${extra}"
                                    )
                                    def result = check_or_break(out)
                                }
                            }
                        }
                    }
                    stage('调用打包命令') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].update
                            }
                        }
                        steps {
                            script {
                                env.ErrInfo = '调用unity打包失败'
                                def jenkins_log = "jenkins_log\\${env.BUILD_NUMBER}"
                                def logExists = fileExists "$jenkins_log"
                                if (!logExists) {
                                    bat label:'日志文件夹', script:"mkdir jenkins_log\\${env.BUILD_NUMBER}"
                                }
                                def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat(
                                        returnStdout: true,
                                        script: 'unity -quit -batchmode ' +
                                                "-logFile ${build_pack_log} " +
                                                "-projectPath \"${env.WORKSPACE}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" " +
                                                "-executeMethod H3DBuildTools.BuildPlazaRoleCloth -buildTarget ${PLATFORM} " +
                                                "path=${PLATFORM_INFO[PLATFORM].PACKAGELIST} " +
                                                "out_path=${env.WORKSPACE}\\x5_mobile\\mr\\art_release\\cs\\${PLATFORM}\\assetbundles"
                                )
                            }
                        }
                    }
                    stage('计算打包结果') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].update
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts') {
                                script {
                                    env.ErrInfo = '计算打包结果失败'
                                    out = bat(returnStdout: true, script: "$PYTHON setup.py print_package_result --path ${env.WORKSPACE}\\x5_mobile\\mobile_dancer\\arttrunk\\client ").split('\n')
                                    print(out[2])
                                    def count_result = readJSON text: out[2]
                                    PLATFORM_INFO[PLATFORM].upload_p4 = count_result.upload_p4
                                    PLATFORM_INFO[PLATFORM].total_count = count_result.total_count
                                    PLATFORM_INFO[PLATFORM].succ_count = count_result.succ_count
                                    PLATFORM_INFO[PLATFORM].failed_count = count_result.failed_count
                                    PLATFORM_INFO[PLATFORM].error_count = count_result.error_count
                                    if (count_result.error_count != 0 || count_result.failed_count != 0 || count_result.succ_count == 0) {
                                        PLATFORM_INFO[PLATFORM].result = 'UNSTABLE'
                                        currentBuild.result = 'UNSTABLE'
                                    }
                                    echo "PLATFORM_INFO: ${PLATFORM_INFO}"
                                }
                            }
                        }
                    }
                    stage('上传Unity打包结果') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].upload_p4
                            }
                        }
                        steps {
                            script {
                                def out
                                dir('pyframe-pipeline/project/x5m/art_package/scripts') {
                                    out = bat(
                                            returnStdout: true,
                                            script: "$PYTHON setup.py get_package_result "+
                                                    "--path ${WORKSPACE}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}"
                                    ).split('\n')
                                }
                                def artifactsPath = 'x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/'
                                dir(artifactsPath) {
                                    def artifactName = out[2]
                                    archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                    def artifactUrl = env.BUILD_URL + 'artifact/' + out[2]
                                    echo "upload_url: ${artifactUrl}"
                                    PLATFORM_INFO[PLATFORM].package_result_url = artifactUrl.trim()
                                }
                            }
                        }
                    }
                    stage('提交p4') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].upload_p4
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m') {
                                script {
                                    env.ErrInfo = '提交p4失败'
                                    def extra = toArgvJson([
                                            root: env.WORKSPACE,
                                            platform: PLATFORM,
                                            files: PLATFORM_INFO[PLATFORM].id_dirs,
                                            workspace: P4_WORKSPACE,
                                            changelist: PLATFORM_INFO[PLATFORM].changelist,
                                            desc: "uploader:${env.SEND_MESSAGE_USER_INFO}++Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}++link:${env.BUILD_URL}console"
                                    ])
                                    def init = toArgvJson([
                                            config: "${GIT_PRESISTENT_PATH}/config.ini"
                                    ])
                                    def out = bat(
                                            returnStdout: true,
                                            script: """$PYTHON pipeline.py --kind=plaza_role --method=submit_files_to_p4 --extra=${extra} --init=${init}"""
                                    )
                                    def result = check_or_break(out)
                                    if (result.size() < 2) {
                                        println('p4workspace下没有需要提交的内容！')
                                    } else {
                                        PLATFORM_INFO[PLATFORM].changelist = result[0]
                                        PLATFORM_INFO[PLATFORM].resources = result[1]
                                    }
                                }
                            }
                        }
                    }
                    stage('持久化配置文件') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].upload_p4
                            }
                        }
                        steps {
                            dir(GIT_PRESISTENT_PATH) {
                                script {
                                    env.ErrInfo = '持久化配置文件失败'
                                    bat """
                            git config --global user.email "<EMAIL>"
                            git config --global user.name "dgm_developer"
                            git add config.ini
                            git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                            git push origin ${GIT_PERSISTENT_BRANCH}
                            """
                                }
                            }
                        }
                    }
                    stage('上传打包结果') {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].upload_p4
                            }
                        }
                        steps {
                            script {
                                env.ErrInfo = '上传打包结果失败'
                                echo "当前目录: ${pwd()}"
                                artifactreportName = "log/${PLATFORM_INFO[PLATFORM].changelist.replaceAll('[\r\n]+', '')}.html"
                                PLATFORM_INFO[PLATFORM].report_url = env.BUILD_URL + 'artifact/' + artifactreportName
                                echo "$artifactreportName"
                                def buildLogExists = fileExists "$artifactreportName"
                                if (buildLogExists) {
                                    echo '1111'
                                    archiveArtifacts artifacts: "$artifactreportName", followSymlinks: false
                                }
                            }
                        }
                    }
                    stage('判断是否单端失败') {
                        when {
                            expression {
                                buildResultCode <= 3
                            }
                        }
                        steps {
                            script {
                                if (PLATFORM == 'android') {
                                    buildResultCode += 1
                                }
                                else {
                                    buildResultCode += 2
                                }
                            }
                        }
                    }

                }
                post {
                    always {
                        //上传日志
                        script {
                            def build_log = "jenkins_log/${env.BUILD_NUMBER}/${PLATFORM}_${env.BUILD_NUMBER}.log"
                            def buildLogExists = fileExists "$build_log"
                            if (buildLogExists) {
                                archiveArtifacts artifacts: "$build_log", followSymlinks: false
                            }
                            env.LOGBACKINFO = """\n""" +
                                    """[安卓\tunity日志](http://jenkins-x5mobile.h3d.com.cn/job/art_package_distrib/job/plaza_role/${env.BUILD_NUMBER}/artifact/jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log)\n""" +
                                    """[安卓\t打包结果](${PLATFORM_INFO['android'].package_result_url})\n""" +
                                    """[ios\tunity日志](http://jenkins-x5mobile.h3d.com.cn/job/art_package_distrib/job/plaza_role/${env.BUILD_NUMBER}/artifact/jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log)\n""" +
                                    """[ios\t打包结果](${PLATFORM_INFO['ios'].package_result_url})"""
                        }
                    }
                }


            }
        }
    }
    post {
        failure {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != '') {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != '') {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(',')
                    echo "执行状态码： ${buildResultCode}"
                    def msg = """**结果**: 新形象模型打包失败"""
                    msg += """\n<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>\n${env.LOGBACKINFO}\n"""
                    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033', content: msg
                    if (sendUserString != '') {
                        emailext to: sendUserString, subject: 'jenkins-炫舞广场-新形象模型失败', body: msg
                    }
                }
            }
        }
        unstable {
            script {
                if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != '') {
                    if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                        messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    }
                }
                if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != '') {
                    messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                }
                def sendUserString = messageSendUserList.join(',')
                if (PLATFORM_INFO['android'].update || PLATFORM_INFO['ios'].update) {
                    def msg = ''
                    if (PLATFORM_INFO['android'].update) {
                        def back_info_android =  get_platform_info(changelist, PLATFORM_INFO.android)
                        msg += """**安卓**：${back_info_android}\n"""
                    }
                    if (PLATFORM_INFO['ios'].update) {
                        def back_info_ios =  get_platform_info(changelist, PLATFORM_INFO.ios)
                        msg += """**ios**：${back_info_ios}\n${env.LOGBACKINFO}"""
                    }
                    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033', content: msg
                    if (sendUserString != '') {
                        emailext to: "${sendUserString}", subject: 'jenkins-炫舞广场-新形象模型部分成功', body: msg
                    }
                }
            }
        }
        success {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != '') {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != '') {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(',')

                    if (PLATFORM_INFO['android'].update || PLATFORM_INFO['ios'].update) {
                        def msg = ''
                        if (PLATFORM_INFO['android'].update) {
                            def back_info_android =  get_platform_info(changelist, PLATFORM_INFO.android)
                            msg += """**安卓**：${back_info_android}\n"""
                        }
                        if (PLATFORM_INFO['ios'].update) {
                            def back_info_ios =  get_platform_info(changelist, PLATFORM_INFO.ios)
                            msg += """**ios**：${back_info_ios}\n${env.LOGBACKINFO}"""
                        }
                        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033', content: msg
                        if (sendUserString != '') {
                            emailext to: "${sendUserString}", subject: 'jenkins-炫舞广场-新形象模型成功', body: msg
                        }
                    }
                }
            }
        }
    }
}
