node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

CACHED_DB = [:]

pipeline {
    agent {
        node {
            label "$params.private_server_ip"
            customWorkspace "/data/workspace"
        }
    }
    options {
        timestamps()
//         disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
    }
    parameters {
        string defaultValue: '', description: '打包节点', name: 'private_server_ip', trim: true
        string defaultValue: '', description: '私人数据库名称', name: 'db_name', trim: true
        string defaultValue: '', description: '从数据库名称', name: 'sub_db_name', trim: true
        string defaultValue: '', description: 'resource or release', name: 'week_config', trim: true
        string defaultValue: '', description: '', name: 'config_path', trim: true
        string defaultValue: '不联服', description: '是否联服', name: 'server_slave', trim: true
        string defaultValue: '', description: '制品库目录', name: 'artifacts_branch', trim: true
        string defaultValue: '', description: 'dgm_server包', name: 'server_package', trim: true
        booleanParam defaultValue: false, description: '是否特殊分支', name: 'is_special_branch'
        string defaultValue: 'simp', description: '语言', name: 'language', trim: true
        string defaultValue: '', description: '操作人', name: 'operator', trim: true
    }
    environment {
        PIPELINE_PYTHON = 'python3'
        LC_ALL = 'en_US.UTF-8'
        LANG = 'en_US.UTF-8'
    }
    stages {
        stage("更新流水线依赖") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                    """
                }
            }
        }
        stage("关服") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=stop_all_server
                    """
                }
            }
        }
        stage("解析参数") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=analytic_parameter
                    """
                }
            }
        }
        stage("下载服务器包、脚本包") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=download_server_package_from_nexus
                    """
                }
            }
        }
        stage("修改数据库") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=change_db
                    """
                }
            }
        }
        stage("构造环境") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=construct_environment
                    """
                }
            }
        }
        stage("部署安装") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=deploy_install
                    """
                }
            }
        }
        stage("下载解压CDN CONFIG") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=download_ftp_cdn_config
                    """
                }
            }
        }
        stage("修改gcloud配置") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=update_gcloud_config
                    """
                }
            }
        }
        stage("关闭支付开关") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=close_pay
                    """
                }
            }
        }
        stage("同步数据库") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=sync_db
                    """
                }
            }
        }
        stage("开服") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=start_server_all
                    """
                }
            }
        }
        stage("查看进程运行") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=check_process
                    """
                }
            }
        }

    }
    post {
        unstable {
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                    ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=post_canceled
                """
            }
        }
        success {
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                    ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=post_success
                """
            }
        }
        failure {
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                    ${PIPELINE_PYTHON} x5m.py deploy_private_server --job=post_failure
                """
            }
        }
    }
}