@Library('h3d_libs') _
import groovy.json.JsonSlurper
def changeList = ""
def isAndroidPackage = "true"
def isIosPackage = "true"
def branch = ""
String[] view_list
def hotfixDataDict = ""
def androidHotfixP4View = ""
def iosHotfixP4View = ""
def androidHotfixLocalPaths = ""
def iosHotfixLocalPaths = ""
def specialDataDict = ""
def androidSpecialBranchP4View = ""
def iosSpecialBranchP4View = ""
def androidSpecialBranchLocalPaths = ""
def iosSpecialBranchLocalPaths = ""
def timeRetryAndroid = 0
def timeRetryIos = 0
def currentRunSteps = 0
def messageSendUserList = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
def buildResultCode = 0
def rootDir
def example

def IsPackage(platform,isAndroidPackage,isIosPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    }else{
        return isIosPackage == "true"
    }
}


pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 1800, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
         booleanParam(name: 'force', defaultValue: false, description: '是否强更P4')
    }
    //    因为四点到五点p4需要备份数据，流水线会执行失败
    triggers {
        cron('H/15 0-3,5-23 * * *')
    }

    stages {
        stage('美术资源打包'){
            matrix {
                agent {
                    node{
                        label "scene_${PLATFORM}_package"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'android','ios'
                    }
                }
                stages{
                    stage('配置初始化'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='配置初始化失败'
                                    currentRunSteps = 1
                                    rootDir = env.workspace+"\\pyframe-pipeline\\project\\x5m\\art_package\\Jenkinsfiles\\scene_package\\scene"
                                    example = load("${rootDir}\\scene.Groovy")
                                    example.getBuildUserInfo()
                                    if (!fileExists(".git/config")) {
                                        bat """
                                        del /s /f /q .
                                        """
                                    }
                                    // 判断配置 config.ini 是否存在，存在, 不处理, 否则判断 config.ini.example 是否存在, 存在，改名为config.ini
                                    def configFile = "config.ini"
                                    def configExampleFile = "config.ini.example"
                                    def configExists = fileExists "$configFile"
                                    def exampleExists = fileExists "$configExampleFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
                                    if (!configExists) {
                                        // 不存在
                                        bat """
                                        git clone -b scene-${PLATFORM} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                                        """
                                    }
                                    bat """
                                    git clean -xdf
                                    git reset --hard HEAD
                                    git pull origin scene-${PLATFORM}
                                    """
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='检查是否需要打包失败'
                                    currentRunSteps = 2
                                    def out = bat(returnStdout: true, script: "python setup.py get_updates --tp scene --first_changelist=${params.first_changelist}").split('\n')
                                    println("检查是否需要打包返回的out值:${out}")
                                    def jsonSlurper = new JsonSlurper()
                                    def result_map = jsonSlurper.parseText(out[2])
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDWORKSPACE = env.WORKSPACE
                                        isAndroidPackage = result_map.update
                                    }else{
                                        env.IOSWORKSPACE = env.WORKSPACE
                                        isIosPackage = result_map.update
                                    }
                                    if (result_map.update == 'true'){
                                        env.PACKAGELIST = result_map.package_list
                                        changeList = result_map.changelist
                                        env.SEND_MESSAGE_USER_INFO = result_map.send_user
                                    }
                                    if(isAndroidPackage == 'false' && isIosPackage == 'false'){
                                        currentBuild.result = 'ABORTED'
                                    }
                                }
                            }
                        }
                    }
                    stage('获取p4上的最新分支'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='获取p4上的最新分支失败'
                                    def branch_out = bat(returnStdout: true, script: "python setup.py get_mr_latest_branch").split('\n')
                                    println(branch_out[2])
                                    branch = branch_out[2].trim()
                                }
                            }
                        }
                    }
                    stage('获取p4资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            retry(2){
                                script{
                                    env.ErrInfo='获取p4资源失败'
                                    def platform
                                    if ("${PLATFORM}" == "android"){
                                        timeRetryAndroid ++
                                        platform = "android"
                                    }
                                    else{
                                        timeRetryIos ++
                                        platform = "iOS"
                                    }
                                    currentRunSteps = 4
                                    bat """git config --global core.longpaths true"""
                                    example.getArtTrunkFromGitlab()
                                    example.get_x5mconfig("${PLATFORM}")
                                    def view = example.getP4View("${PLATFORM}", platform, branch)
                                    def p4_workspace = "jenkins_scene_${PLATFORM}_package"
                                    def python_view = view.replaceAll("\n", "#")
                                    bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync_and_clean --p4_root=${env.WORKSPACE} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=${params.force}")
                                }
                            }
                            script{
                                def out = bat(returnStdout: true, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py distribute_precheck --p4_root=${env.WORKSPACE} --tp=scene").split('\n')
                                def jsonSlurper = new JsonSlurper()
                                def result_map = jsonSlurper.parseText(out[2])
                                println(result_map)
                                if (result_map.state != "true"){
                                    env.ErrInfo = result_map.msg
                                    error(result_map.msg)
                                }
                            }
                        }
                    }
                    stage('读取特殊分支分发配置'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='读取特殊分支分发配置失败'
                                    currentRunSteps = 10
                                    def workspace
                                    def platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                        platform = "android"
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                        platform = "iOS"
                                    }
                                    def out = bat(returnStdout: true, script: "python setup.py check_distribute_special_branch --special_branch_config_path=${workspace}/x5mplan/resmg/scene/special-branch-scene.xlsx --packages=${env.PACKAGELIST} --tp=scene --p4workspace=jenkins_scene_special_branch_${PLATFORM}_package --workspace=${workspace} --platform=${platform}").split('\n')
                                    print(out[2])
                                    def special_result = readJSON text: out[2]
                                    env.SPECIAL_CHECK_RESULT = special_result.check_result
                                    env.SPECIAL_CHECK_MSG = special_result.msg
                                    if (special_result.check_result == "true") {
                                        specialDataDict = special_result.data_dict
                                        if ("${PLATFORM}" == "android"){
                                            androidSpecialBranchP4View = special_result.special_branch_p4_views
                                            androidSpecialBranchLocalPaths = special_result.special_branch_local_paths
                                        }else{
                                            iosSpecialBranchP4View = special_result.special_branch_p4_views
                                            iosSpecialBranchLocalPaths = special_result.special_branch_local_paths
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('获取特殊分支上的P4资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.SPECIAL_CHECK_RESULT == "true"
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='获取特殊分支上的P4资源失败'
                                def platform
                                def specialBranchP4view
                                if ("${PLATFORM}" == "android"){
                                    platform = "android"
                                    specialBranchP4view = androidSpecialBranchP4View
                                }
                                else{
                                    platform = "iOS"
                                    specialBranchP4view = iosSpecialBranchP4View
                                }
                                currentRunSteps = 4
                                echo "特殊分支的P4 view: ${specialBranchP4view}"
                                def p4_workspace = "jenkins_scene_special_branch_${PLATFORM}_package"
                                def python_view = specialBranchP4view.replaceAll("\n", "#")
                                bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=${params.force}")
                            }
                        }
                    }
                    stage('读取热更配置'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='读取热更配置失败'
                                    currentRunSteps = 8
                                    def workspace
                                    def platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                        platform = "android"
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                        platform = "iOS"
                                    }
                                    def out = bat(returnStdout: true, script: "python setup.py check_distribute_hotfix --hotfix_config_path=${workspace}/x5mplan/resmg/scene/hotfix-scene.xlsx --packages=${env.PACKAGELIST} --tp=scene --p4workspace=jenkins_scene_hotfix_${PLATFORM}_package --workspace=${workspace} --platform=${platform}").split('\n')
                                    print(out[2])
                                    def hotfix_result = readJSON text: out[2]
                                    echo "热更结果：${hotfix_result}"
                                    env.HOTFIX_CHECK_RESULT = hotfix_result.check_result
                                    env.HOTFIX_CHECK_MSG = hotfix_result.msg
                                    if (hotfix_result.check_result == "true") {
                                        hotfixDataDict = hotfix_result.data_dict
                                        if ("${PLATFORM}" == "android"){
                                            androidHotfixP4View = hotfix_result.hotfix_p4_views
                                            androidHotfixLocalPaths = hotfix_result.hotfix_local_paths
                                        }else{
                                            iosHotfixP4View = hotfix_result.hotfix_p4_views
                                            iosHotfixLocalPaths = hotfix_result.hotfix_local_paths
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('获取p4热更资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.HOTFIX_CHECK_RESULT == "true"
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='获取p4热更资源失败'
                                def platform
                                def hotP4view
                                if ("${PLATFORM}" == "android"){
                                    platform = "android"
                                    hotP4view = androidHotfixP4View
                                }
                                else{
                                    platform = "iOS"
                                    hotP4view = iosHotfixP4View
                                }
                                currentRunSteps = 4
                                echo "热更的p4 view: ${hotP4view}"
                                def p4_workspace = "jenkins_scene_hotfix_${PLATFORM}_package"
                                def python_view = hotP4view.replaceAll("\n", "#")
                                bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=${params.force}")
                            }
                        }
                    }
                    stage('调用打包命令'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='调用unity打包失败'
                                currentRunSteps = 6
                                def workspace
                                String platform
                                String outPlatform
                                if ("${PLATFORM}" == "android"){
                                    platform = "Android"
                                    outPlatform = "android"
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    platform = "iOS"
                                    outPlatform = "iOS"
                                    workspace = env.IOSWORKSPACE
                                }
                                // 清空 雪景社区 和 普通社区资源
                                dir('x5_mobile/mobile_dancer/arttrunk/client/Assets/StaticResources/Art/3d/stage/xuejingshequ'){
                                    deleteDir()
                                }
                                dir('x5_mobile/mobile_dancer/arttrunk/client/Assets/StaticResources/Art/3d/stage/shequ_01'){
                                    deleteDir()
                                }
                                dir('x5_mobile/mobile_dancer/arttrunk/client/Assets/StaticResources/Art/3d/stage/streaming'){
                                    deleteDir()
                                }
                                def jenkins_log = "${workspace}\\jenkins_log"
                                def logExists = fileExists "$jenkins_log"
                                if(!logExists)
                                {
                                    bat label:"日志文件夹",script:"mkdir jenkins_log"
                                }
                                def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat(script: "unity -quit -batchmode -logFile ${build_pack_log} -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" -executeMethod H3DBuildTools.BuildArt -buildTarget ${platform} path=${env.PACKAGELIST} out_path=${workspace}\\x5_mobile\\mr\\common\\cs\\${outPlatform}\\assetbundles")
                            }
                        }
                    }
                    stage("生成并上传导航网格配置") {
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && "${PLATFORM}" == "android" && "${env.SPECIAL_CHECK_RESULT}" == "false"
                            }
                        }
                        steps {
                            dir("pyframe-pipeline/project/x5m/art_package/scripts") {
                                script {
                                    env.ErrInfo='生成并上传导航网格配置失败'
                                    def out = bat(returnStdout: true, script: "python setup.py export_and_commit_scene_config --jenkins_workspace ${env.ANDROIDWORKSPACE} --project_path ${env.ANDROIDWORKSPACE}\\x5_mobile\\mobile_dancer\\arttrunk\\client --assets ${env.PACKAGELIST}").split('\n')
                                    println(out[-1])
                                    env.GITLAB_COMMIT_URL = out[-1]
                                }
                            }
                        }
                    }
                    stage('计算打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='计算打包结果失败'
                                    currentRunSteps = 7
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }
                                    def out = bat(returnStdout: true, script: "python setup.py print_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client ").split('\n')
                                    print(out[2])
                                    def count_result = readJSON text: out[2]
                                    env.UPLOADP4 = count_result.upload_p4
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDTOTALCOUNT = count_result.total_count
                                        env.ANDROIDSUCCCOUNT = count_result.succ_count
                                        env.ANDROIDFAILEDCOUNT = count_result.failed_count
                                        env.ANDROIDERRORCOUNT = count_result.error_count

                                        if (env.ANDROIDERRORCOUNT != "0" || env.ANDROIDFAILEDCOUNT != "0" || env.ANDROIDSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                    else{
                                        env.IOSTOTALCOUNT = count_result.total_count
                                        env.IOSSUCCCOUNT = count_result.succ_count
                                        env.IOSFAILEDCOUNT = count_result.failed_count
                                        env.IOSERRORCOUNT = count_result.error_count
                                        if (env.IOSERRORCOUNT != "0" || env.IOSFAILEDCOUNT != "0" || env.IOSSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('分发ab'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.UPLOADP4 == 'true'
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='分发ab失败'
                                    currentRunSteps = 8
                                    def workspace
                                    def platform
                                    def distributeHotView
                                    def distributeSpecialView
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                        platform = "android"
                                        distributeHotView = androidHotfixLocalPaths
                                        distributeSpecialView = androidSpecialBranchLocalPaths
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                        platform = "iOS"
                                        distributeHotView = iosHotfixLocalPaths
                                        distributeSpecialView = iosSpecialBranchLocalPaths
                                    }
                                    def distribute_paths = "${workspace}/x5_mobile/mr/Resources/cs/${platform}/assetbundles,${workspace}/x5_mobile/mr/art_release/cs/${PLATFORM}/assetbundles,${workspace}/x5_mobile/mr/b/${branch}/cs/${platform}/assetbundles"
                                    echo "python setup.py distribute_scene_source --tp=scene --source_path=${workspace}/x5_mobile/mr/common/cs/${platform}/assetbundles/scene --distribute_paths=${distribute_paths} --hotfix_paths=${distributeHotView} --hotfix_data=${hotfixDataDict} --packages=${env.PACKAGELIST} --special_paths=${distributeSpecialView} --special_data=${specialDataDict}"
                                    def out = bat(returnStdout: true, script: "python setup.py distribute_scene_source --tp=scene --source_path=${workspace}/x5_mobile/mr/common/cs/${platform}/assetbundles/scene --distribute_paths=${distribute_paths} --hotfix_paths=${distributeHotView} --hotfix_data=${hotfixDataDict} --special_paths=${distributeSpecialView} --special_data=${specialDataDict} --packages=${env.PACKAGELIST}").split('\n')
                                    print(out[2])
                                    def distribute_result = readJSON text: out[2]
                                    echo "分发结果：${distribute_result}"
                                    if (distribute_result.result != "true") {
                                        env.DISTRIBUTE_AB_ERROR_MSG = distribute_result.msg
                                        currentBuild.result = 'FAILURE'
                                    }
                                    env.DISTRIBUTE_RESULT = distribute_result.result
                                }
                            }
                        }
                    }
                    stage('提交p4'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.UPLOADP4 == 'true' && env.DISTRIBUTE_RESULT == "true"
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='提交p4失败'
                                    currentRunSteps = 8
                                    def workspace
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }

                                    def submitP4Desc = "--Auto--|uploader:${env.SEND_MESSAGE_USER_INFO}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitP4Desc}"
                                    def out = bat(returnStdout: true,script: "python setup.py scene-submit_files_to_p4  --root=${workspace} --platform=${PLATFORM} --branch=${branch} --tp=scene --file_paths=${env.PACKAGELIST} --changelist=${changeList} --desc=\"${submitP4Desc}\" --hotfix_data=${hotfixDataDict} --workspace=jenkins_scene_${PLATFORM}_package --hotfix_workspace=jenkins_scene_hotfix_${PLATFORM}_package --special_data=${specialDataDict} --special_workspace=jenkins_scene_special_branch_${PLATFORM}_package")
                                    echo "${out}"
                                    String[] outArras=out.split("\n")
                                    if (outArras.size() > 5){
                                        println("p4workspace下没有需要提交的内容！")
                                    }else{
                                        if ("${PLATFORM}" == "android"){
                                            env.ANDROID_CHANGELIST = outArras[2]
                                            env.ANDROID_HOTFIX_CHANGELIST = outArras[3]
                                            env.ANDROID_SPECIAL_CHANGELIST = outArras[4]
                                        }else{
                                            env.IOS_CHANGELIST = outArras[2]
                                            env.IOS_HOTFIX_CHANGELIST = outArras[3]
                                            env.IOS_SPECIAL_CHANGELIST = outArras[4]
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('持久化配置文件'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.UPLOADP4 == 'true' && env.DISTRIBUTE_RESULT == "true"
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='持久化配置文件失败'
                                    bat """
                                    git config --global user.email "<EMAIL>"
                                    git config --global user.name "dgm_developer"
                                    git add config.ini
                                    git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                                    git push origin scene-${PLATFORM}
                                    """
                                }
                            }
                        }
                    }
                    stage('上传打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='上传打包结果失败'
                                currentRunSteps = 9
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    workspace = env.IOSWORKSPACE
                                }
                                def out
                                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                     out = bat(returnStdout: true, script: "python setup.py get_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}").split('\n')
                                }
                                def artifactsPath="x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/"
                                dir("${artifactsPath}"){
                                    def artifactName = out[2]
                                    archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                }
                                // 上传resources.txt
                                dir("x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool") {
                                    archiveArtifacts artifacts: "resources.txt", followSymlinks: false
                                    println("${env.BUILD_URL}" + "artifact/resources.txt")
                                }
                                // 环境变量赋值
                                def artifactUrl = env.BUILD_URL + 'artifact/' + out[2]
                                def resources_url = env.BUILD_URL + "artifact/resources.txt"
                                echo "upload_url: ${artifactUrl}"
                                if ("${PLATFORM}" == "android"){
                                    env.ANDROIDPACKAGERESULTURL = artifactUrl.trim()
                                    env.ANDROID_RESOURCES = resources_url
                                    }
                                else{
                                    env.IOSPACKAGERESULTURL = artifactUrl.trim()
                                    env.IOS_RESOURCES = resources_url
                                }
                            }
                        }
                    }
                    stage('判断是否单端失败'){
                          when {
                             expression {
                                 buildResultCode <= 3
                             }
                         }
                         steps{
                             script{
                                 if ("${PLATFORM}" == "android"){
                                     buildResultCode += 1
                                 }
                                 else{
                                     buildResultCode += 2
                                 }
                             }
                         }
                    }
                }
                post{
                    always{
                        //上传日志
                        script {

                            def build_log = "jenkins_log/${PLATFORM}_${env.BUILD_NUMBER}.log"
                            def buildLogExists = fileExists "$build_log"
                            if(buildLogExists){
                                archiveArtifacts artifacts: "$build_log", followSymlinks: false
                            }
                            def hotfixStatus = "不分发"
                            def specialStatus = "不分发"
                            // 存在热更需要分发
                            if (env.HOTFIX_CHECK_RESULT == "true") {
                                hotfixStatus = "成功"
                            }
                            if (env.SPECIAL_CHECK_RESULT == "true") {
                                specialStatus = "成功"
                            }
                            //定义信息模板
                            env.ANDROIDBACKINFO = """
[导航网格配置上传地址](${env.GITLAB_COMMIT_URL})
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.ANDROID_CHANGELIST}
**热更ab changelist**: ${env.ANDROID_HOTFIX_CHANGELIST}
**特殊分支ab changelist**: ${env.ANDROID_SPECIAL_CHANGELIST}
[打包统计](${env.ANDROIDPACKAGERESULTURL})
[打包资源](${env.ANDROID_RESOURCES})
**打包数量**: ${env.ANDROIDTOTALCOUNT}
**成功数量**: ${env.ANDROIDSUCCCOUNT}"""
                            env.IOSBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.IOS_CHANGELIST}
**热更ab changelist**: ${env.IOS_HOTFIX_CHANGELIST}
**特殊分支ab changelist**: ${env.IOS_SPECIAL_CHANGELIST}
[打包统计](${env.IOSPACKAGERESULTURL})
[打包资源](${env.IOS_RESOURCES})
**打包数量**: ${env.IOSTOTALCOUNT}
**成功数量**: ${env.IOSSUCCCOUNT}"""
                            if (currentBuild.result != 'SUCCESS'){
                                env.ANDROIDBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.ANDROIDFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.ANDROIDERRORCOUNT}</font>"""
                                env.IOSBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.IOSFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.IOSERRORCOUNT}</font>"""
                            }
                            else{
                                env.ANDROIDBACKINFO += """
**失败数量**: ${env.ANDROIDFAILEDCOUNT}
**检查出错数量**: ${env.ANDROIDERRORCOUNT}"""
                                env.IOSBACKINFO += """
**失败数量**: ${env.IOSFAILEDCOUNT}
**检查出错数量**: ${env.IOSERRORCOUNT}"""
                            }
                            env.ANDROIDBACKINFO += """
**热更版本号**: ${hotfixDataDict}
**热更分发状态**: ${hotfixStatus}
**特殊分支名**: ${specialDataDict}
**特殊分支状态**: ${specialStatus}
                            """
                                env.IOSBACKINFO += """
**热更版本号**: ${hotfixDataDict}
**热更分发状态**: ${hotfixStatus}
**特殊分支名**: ${specialDataDict}
**特殊分支状态**: ${specialStatus}
                            """
                            if (env.HOTFIX_CHECK_RESULT == "true") {
                                env.ANDROIDBACKINFO += """
**热更ab changelist**: ${env.ANDROID_HOTFIX_CHANGELIST}
                            """
                                env.IOSBACKINFO += """
**热更ab changelist**: ${env.IOS_HOTFIX_CHANGELIST}
                            """
                            }
                            env.LOGBACKINFO = """
[安卓 unity日志](${env.BUILD_URL}artifact/jenkins_log/android_${env.BUILD_NUMBER}.log)
[ios unity日志](${env.BUILD_URL}artifact/jenkins_log/ios_${env.BUILD_NUMBER}.log)"""

                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            node('master') {
                script {
                    example.failure_info(messageSendUserList, buildResultCode)
                }
            }
        }
        unstable{
            node('master') {
                script{
                    example.unstable_info(messageSendUserList, isAndroidPackage, isIosPackage)
                }
            }
        }
        success {
            node('master') {
                script{
                    example.success_info(messageSendUserList, isAndroidPackage, isIosPackage)
                }
            }
        }
    }
}
