@Library('h3d_libs') _
import groovy.json.JsonSlurper
def changeList = ""
def isAndroidPackage = "true"
def isIosPackage = "true"
def branch = ""
String[] view_list
def timeRetryAndroid = 0
def timeRetryIos = 0
def currentRunSteps = 0
def messageSendUserList = ["<EMAIL>", "<EMAIL>", "ji<PERSON><PERSON><PERSON><PERSON>@h3d.com.cn", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
def buildResultCode = 0


def getP4ResourceView(platform, relPlatform, branch, packageType, streamName, cdnBakName) {
    def tpl = '''
//x5_mobile/mr/art_resource/art_src/scene/${packageType}/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/Assets/StaticResources/Art/3d/stage/shequ_01/...
//x5_mobile/mr/art_resource/art_src/scene/streaming/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/Assets/ab_resources/${streamName}/...
//x5m/res/cdn/cooked/${platform}/assetbundles/streaming_stage/${cdnBakName}/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/streaming_stage/${packageType}/...
//x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/${relPlatform}/assetbundles/streaming/... //${p4Client}/x5_mobile/mr/Resources/cs/${relPlatform}/assetbundles/${streamName}/...
//x5_mobile/mr/b/${branch}/ResourcePublish/CDN/SourceFiles/${relPlatform}/assetbundles/streaming/... //${p4Client}/x5_mobile/mr/b/${branch}/cs/${relPlatform}/assetbundles/${streamName}/...
    '''
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([platform: platform, relPlatform: relPlatform, branch: branch, packageType: packageType, streamName: streamName, cdnBakName: cdnBakName, p4Client: '${P4_CLIENT}']).toString()
}


def getP4View() {
    def tpl = '''
//x5_mobile/mr/art_resource/art_src/scene/3d/fitment/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/3d/fitment/...
//x5_mobile/mr/art_resource/art_src/scene/sta/3d/fitment/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/Assets/StaticResources/Art/3d/fitment/...
//x5_mobile/mr/art_resource/art_src/role/actions/actions_anim/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/role/actions/actions_anim/...
    '''
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([p4Client: '${P4_CLIENT}']).toString()
}


def IsPackage(platform,isAndroidPackage,isIosPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    }else{
        return isIosPackage == "true"
    }
}


def getArtTrunkFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/master"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    url: '******************************:dgm/arttrunk.git']]]
}


def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}


pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
         booleanParam(name: 'force', defaultValue: false, description: '是否强更P4')
    }
    //     因为四点到五点p4需要备份数据，流水线会执行失败
    triggers {
        cron('H/15 0-3,5-23 * * *')
    }

    stages {
        stage('美术资源打包'){
            matrix {
                agent {
                    node{
                        label "stream_scene_${PLATFORM}_package"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'android','ios'
                    }
                }
                stages{
                    stage('配置初始化'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='配置初始化失败'
                                    currentRunSteps = 1
                                    getBuildUserInfo()
                                    if (!fileExists(".git/config")) {
                                        bat """
                                        del /s /f /q .
                                        """
                                    }
                                    // 判断配置 config.ini 是否存在，存在, 不处理, 否则判断 config.ini.example 是否存在, 存在，改名为config.ini
                                    def configFile = "config.ini"
                                    def configExampleFile = "config.ini.example"
                                    def configExists = fileExists "$configFile"
                                    def exampleExists = fileExists "$configExampleFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
                                    if (!configExists) {
                                        // 不存在
                                        bat """
                                        git clone -b scene-stream-${PLATFORM} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                                        """
//                                         if (exampleExists) {
//                                             // 改名
//                                             fileOperations([fileRenameOperation(destination: "${configFile}", source: "${configExampleFile}")])
//                                             bat """
//                                             python -m pip install --upgrade pip
//                                             pip install -r ${env.WORKSPACE}\\gitlab\\Script\\scripts\\requirements.txt
//                                             """
//                                         }
                                    }
                                    bat """
                                    git clean -xdf
                                    git reset --hard HEAD
                                    git pull origin scene-stream-${PLATFORM}
                                    """
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='检查是否需要打包失败'
                                    currentRunSteps = 2
                                    def out = bat(returnStdout: true, script: "python setup.py get_updates --tp stream_scene --first_changelist=${params.first_changelist}").split('\n')
                                    println("检查是否需要打包返回的out值:${out}")
                                    def jsonSlurper = new JsonSlurper()
                                    def result_map = jsonSlurper.parseText(out[2])
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDWORKSPACE = env.WORKSPACE
                                        isAndroidPackage = result_map.update
                                        println(isAndroidPackage)
                                    }else{
                                        env.IOSWORKSPACE = env.WORKSPACE
                                        isIosPackage = result_map.update
                                        println(isIosPackage)
                                    }
                                    if (result_map.update == 'true'){
                                        changeList = result_map.changelist
                                        env.SEND_MESSAGE_USER_INFO = result_map.send_user
                                        if ("${PLATFORM}" == "android"){
                                            env.ANDROID_PACKAGE_TYPE = result_map.package_type
                                            env.ANDROID_STREAMING_NAME = "streaming"
                                        }else{
                                            env.IOS_PACKAGE_TYPE = result_map.package_type
                                            env.IOS_STREAMING_NAME = "streaming"
                                        }
                                    }
                                    if(isAndroidPackage == 'false' && isIosPackage == 'false'){
                                        currentBuild.result = 'ABORTED'
                                    }
                                }
                            }
                        }
                    }
                    stage('获取p4上的最新分支'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='获取p4上的最新分支失败'
                                    def branch_out = bat(returnStdout: true, script: "python setup.py get_mr_latest_branch").split('\n')
                                    println(branch_out[2])
                                    branch = branch_out[2].trim()
                                }
                            }
                        }
                    }
                    stage('获取p4资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            retry(2){
                                script{
                                    env.ErrInfo='获取p4资源失败'
                                    def platform
                                    def streamingDirName
                                    def packageType
                                    if ("${PLATFORM}" == "android"){
                                        timeRetryAndroid ++
                                        platform = "android"
                                        echo "安卓执行次数：${timeRetryAndroid}"
                                        packageType = env.ANDROID_PACKAGE_TYPE
                                        streamingDirName = env.ANDROID_STREAMING_NAME
                                    }
                                    else{
                                        timeRetryIos ++
                                        platform = "iOS"
                                        echo "ios执行次数：${timeRetryIos}"
                                        packageType = env.IOS_PACKAGE_TYPE
                                        streamingDirName = env.IOS_STREAMING_NAME
                                    }
                                    currentRunSteps = 4
                                    bat """git config --global core.longpaths true"""
                                    getArtTrunkFromGitlab()
                                    echo "stream dir name: ${streamingDirName}"
                                    // 清除映射到工程中的原资源目录
                                    dir("x5_mobile/mobile_dancer/arttrunk/client/Assets/ab_resources/${streamingDirName}"){
                                        deleteDir()
                                    }
                                    dir("x5_mobile/mobile_dancer/arttrunk/client/Assets/StaticResources/Art/3d/stage/shequ_01"){
                                        deleteDir()
                                    }
                                    echo "platform: ${PLATFORM}, branch: ${branch}, package_type: ${packageType}"
                                    def cdnBakName
                                    if (packageType == "shequ_01") {
                                        cdnBakName = "shequ"
                                    } else {
                                        cdnBakName = "xuejingshequ"
                                    }
                                    // 拉取原资源目录，原资源目录需要强更，因为每次都会把上次的资源给清空，然后拉取
                                    def sourceView = getP4ResourceView("${PLATFORM}", platform, branch, packageType, streamingDirName, cdnBakName)
                                    echo "${sourceView}"
                                    // 拉取资源目录
                                    def p4_source_workspace = "jenkins_streaming_scene_resource_${PLATFORM}_package"
                                    def python_source_view = sourceView.replaceAll("\n", "#")
                                    bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync_and_clean --p4_root=${env.WORKSPACE} --workspace=${p4_source_workspace} --p4_view=\"${python_source_view}\" --force_sync=${params.force}")

//                                     checkout perforce(credential: 'p4_dgm_jenkins_upwd',
//                                         populate: syncOnly(force: true, have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: env.P4_REVISION, quiet: true, revert: false),
//                                         workspace: manualSpec(
//                                             charset: 'none',
//                                             cleanup: false,
//                                             name: "jenkins-streaming-scene-resource-${PLATFORM}-package",
//                                             pinHost: false,
//                                             spec: clientSpec(
//                                                 allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
//                                                 locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: sourceView
//                                             )
//                                         )
//                                     )

                                    // 拉取其他资源，不需要强更的
                                    def view = getP4View()
                                    echo "${view}"
                                    def p4_workspace = "jenkins_streaming_scene_${PLATFORM}_package"
                                    def python_view = view.replaceAll("\n", "#")
                                    bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=${params.force}")

                                    // 拉取资源目录
//                                     checkout perforce(credential: 'p4_dgm_jenkins_upwd',
//                                         populate: syncOnly(force: "${params.force}", have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: env.P4_REVISION, quiet: true, revert: false),
//                                         workspace: manualSpec(
//                                             charset: 'none',
//                                             cleanup: false,
//                                             name: "jenkins-streaming-scene-${PLATFORM}-package",
//                                             pinHost: false,
//                                             spec: clientSpec(
//                                                 allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
//                                                 locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: view
//                                             )
//                                         )
//                                     )
                                }
                            }
                        }
                    }
                    stage('清空AB目录'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='清空AB目录失败'
                                def platform
                                def streamingDirName
                                def packageType
                                if ("${PLATFORM}" == "android"){
                                    platform = "android"
                                    packageType = env.ANDROID_PACKAGE_TYPE
                                    streamingDirName = env.ANDROID_STREAMING_NAME
                                }
                                else{
                                    platform = "iOS"
                                    packageType = env.IOS_PACKAGE_TYPE
                                    streamingDirName = env.IOS_STREAMING_NAME
                                }
                                dir("x5_mobile/mr/art_release/cs/${PLATFORM}/assetbundles/streaming_stage/${packageType}"){
                                    deleteDir()
                                }
                                dir("x5_mobile/mr/Resources/cs/${platform}/assetbundles/${streamingDirName}"){
                                    deleteDir()
                                }
                                dir("x5_mobile/mr/b/${branch}/cs/${platform}/assetbundles/${streamingDirName}"){
                                    deleteDir()
                                }
                            }
                        }
                    }
                    stage('调用打包命令'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='调用unity打包失败'
                                currentRunSteps = 6
                                def workspace
                                String platform
                                String outPlatform
                                if ("${PLATFORM}" == "android"){
                                    platform = "Android"
                                    outPlatform = "android"
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    platform = "iOS"
                                    outPlatform = "iOS"
                                    workspace = env.IOSWORKSPACE
                                }
                                def jenkins_log = "${workspace}\\jenkins_log"
                                def logExists = fileExists "$jenkins_log"
                                if(!logExists)
                                {
                                    bat label:"日志文件夹",script:"mkdir jenkins_log"
                                }
                                def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat(script: "unity -quit -batchmode -logFile ${build_pack_log} -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" -executeMethod H3DBuildTools.BuildStreamingScene -buildTarget ${platform} out_path=${workspace}\\x5_mobile\\mr\\Resources\\cs\\${outPlatform}\\assetbundles")
                            }
                        }
                    }
                    /*stage('计算打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='计算打包结果失败'
                                    currentRunSteps = 7
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }
                                    def out = bat(returnStdout: true, script: "python setup.py print_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client ").split('\n')
                                    print(out[2])
                                    def count_result = readJSON text: out[2]
                                    env.UPLOADP4 = count_result.upload_p4
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDTOTALCOUNT = count_result.total_count
                                        print(env.ANDROIDTOTALCOUNT)
                                        env.ANDROIDSUCCCOUNT = count_result.succ_count
                                        print(env.ANDROIDSUCCCOUNT)
                                        env.ANDROIDFAILEDCOUNT = count_result.failed_count
                                        print(env.ANDROIDFAILEDCOUNT)
                                        env.ANDROIDERRORCOUNT = count_result.error_count

                                        if (env.ANDROIDERRORCOUNT != "0" || env.ANDROIDFAILEDCOUNT != "0" || env.ANDROIDSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                    else{
                                        env.IOSTOTALCOUNT = count_result.total_count
                                        print(env.IOSTOTALCOUNT)
                                        env.IOSSUCCCOUNT = count_result.succ_count
                                        print(env.IOSSUCCCOUNT)
                                        env.IOSFAILEDCOUNT = count_result.failed_count
                                        print(env.IOSFAILEDCOUNT)
                                        env.IOSERRORCOUNT = count_result.error_count
                                        if (env.IOSERRORCOUNT != "0" || env.IOSFAILEDCOUNT != "0" || env.IOSSUCCCOUNT == "0"){
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                }
                            }
                        }
                    }*/
                    stage('分发ab'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='分发ab失败'
                                    currentRunSteps = 8
                                    def workspace
                                    def platform
                                    def streamingDirName
                                    def packageType
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                        platform = "android"
                                        packageType = env.ANDROID_PACKAGE_TYPE
                                        streamingDirName = env.ANDROID_STREAMING_NAME
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                        platform = "iOS"
                                        packageType = env.IOS_PACKAGE_TYPE
                                        streamingDirName = env.IOS_STREAMING_NAME
                                    }
                                    def distribute_paths = "${workspace}/x5_mobile/mr/art_release/cs/${PLATFORM}/assetbundles/streaming_stage/${packageType},${workspace}/x5_mobile/mr/b/${branch}/cs/${platform}/assetbundles/${streamingDirName}"
                                    def out = bat(returnStdout: true, script: "python setup.py distribute_scene_source --tp=stream_scene --source_path=${workspace}/x5_mobile/mr/Resources/cs/${platform}/assetbundles/${streamingDirName} --distribute_paths=${distribute_paths}  --packages=").split('\n')
                                    print(out[2])
                                    def distribute_result = readJSON text: out[2]
                                    echo "分发结果：${distribute_result}"
                                    if (distribute_result.result != "true") {
                                        env.DISTRIBUTE_AB_ERROR_MSG = distribute_result.msg
                                        currentBuild.result = 'FAILURE'
                                    }
                                    env.DISTRIBUTE_RESULT = distribute_result.result
                                }
                            }
                        }
                    }
                    //stage('提交p4'){
                    //    when {
                    //        expression {
                    //            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.DISTRIBUTE_RESULT == "true"
                    //        }
                    //    }
                    //    steps{
                    //        dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                    //            script{
                    //                env.ErrInfo='提交p4失败'
                    //                currentRunSteps = 8
                    //                def workspace
                    //                def streamingDirName
                    //                def packageType
                    //                if ("${PLATFORM}" == "android"){
                    //                    workspace = env.ANDROIDWORKSPACE
                    //                    packageType = env.ANDROID_PACKAGE_TYPE
                    //                    streamingDirName = env.ANDROID_STREAMING_NAME
                    //                }else{
                    //                    workspace = env.IOSWORKSPACE
                    //                    packageType = env.IOS_PACKAGE_TYPE
                    //                    streamingDirName = env.IOS_STREAMING_NAME
                    //                }
                    //                def submitP4Desc = "uploader:${env.SEND_MESSAGE_USER_INFO}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                    //                echo "${submitP4Desc}"
                    //                echo "python setup.py scene-submit_files_to_p4  --root=${workspace} --platform=${PLATFORM} --branch=${branch} --tp=stream_scene --desc=${changeList} --workspace=jenkins_streaming_scene_${PLATFORM}_package --hotfix_data=${packageType} --hotfix_workspace=${streamingDirName}"
                    //                def out = bat(returnStdout: true,script: "python setup.py scene-submit_files_to_p4  --root=${workspace} --platform=${PLATFORM} --branch=${branch} --tp=stream_scene --changelist=${changeList} --desc=\"${submitP4Desc}\" --workspace=jenkins_streaming_scene_resource_${PLATFORM}_package --hotfix_data=${packageType} --hotfix_workspace=${streamingDirName}")
                    //                echo "${out}"
                    //                String[] outArras=out.split("\n")
                    //                if (outArras.size() > 4){
                    //                    println("p4workspace下没有需要提交的内容！")
                    //                }else{
                    //                    if ("${PLATFORM}" == "android"){
                    //                        env.ANDROID_CHANGELIST = outArras[2]
                    //                    }else{
                    //                        env.IOS_CHANGELIST = outArras[2]
                    //                    }
                    //                }
                    //            }
                    //        }
                    //    }
                    //}
                    //stage('持久化配置文件'){
                    //    when {
                    //        expression {
                    //            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) && env.UPLOADP4 == 'true'
                    //        }
                    //    }
                    //    steps{
                    //        dir('pyframe-pipeline/project/x5m/art_package/config'){
                    //            script{
                    //                env.ErrInfo='持久化配置文件失败'
                    //                bat """
                    //                git config --global user.email "<EMAIL>"
                    //                git config --global user.name "dgm_developer"
                    //                git add config.ini
                    //                git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                    //                git push origin scene-stream-${PLATFORM}
                    //                """
                    //            }
                    //        }
                    //    }
                    //}
                    /*stage('上传打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='上传打包结果失败'
                                currentRunSteps = 9
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    workspace = env.IOSWORKSPACE
                                }
                                def out
                                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                     out = bat(returnStdout: true, script: "python setup.py get_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}").split('\n')
                                }
                                def artifactsPath="x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/"
                                dir("${artifactsPath}"){
                                    def artifactName = out[2]
                                    archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                    def artifactUrl = env.BUILD_URL + 'artifact/' + out[2]
                                    echo "upload_url: ${artifactUrl}"
                                     if ("${PLATFORM}" == "android"){
                                        env.ANDROIDPACKAGERESULTURL = artifactUrl.trim()
                                        }
                                    else{
                                        env.IOSPACKAGERESULTURL = artifactUrl.trim()
                                    }
                                }
                            }
                        }
                    }*/
                    stage('判断是否单端失败'){
                          when {
                             expression {
                                 buildResultCode <= 3
                             }
                         }
                         steps{
                             script{
                                 if ("${PLATFORM}" == "android"){
                                     buildResultCode += 1
                                 }
                                 else{
                                     buildResultCode += 2
                                 }
                             }
                         }
                    }
                }
                post{
                    always{
                        //上传日志
                        script {

                            def build_log = "jenkins_log/${PLATFORM}_${env.BUILD_NUMBER}.log"
                            def buildLogExists = fileExists "$build_log"
                            if(buildLogExists){
                                archiveArtifacts artifacts: "$build_log", followSymlinks: false
                            }
                            if ("${PLATFORM}" == "android"){
                                if (env.ANDROID_PACKAGE_TYPE == "shequ_01") {
                                    env.STREAMING_TYPE = "社区"
                                } else if (env.ANDROID_PACKAGE_TYPE == "xuejingshequ") {
                                    env.STREAMING_TYPE = "雪景社区"
                                }
                            }else{
                                if (env.IOS_PACKAGE_TYPE == "shequ_01") {
                                    env.STREAMING_TYPE = "社区"
                                } else if (env.IOS_PACKAGE_TYPE == "xuejingshequ") {
                                    env.STREAMING_TYPE = "雪景社区"
                                }
                            }

                            //定义信息模板
                            env.ANDROIDBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.ANDROID_CHANGELIST}
"""
                            env.IOSBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.IOS_CHANGELIST}
"""
                            env.LOGBACKINFO = """
[安卓 unity日志](${env.BUILD_URL}artifact/jenkins_log/android_${env.BUILD_NUMBER}.log)
[ios unity日志](${env.BUILD_URL}artifact/jenkins_log/ios_${env.BUILD_NUMBER}.log)"""
                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    echo "执行状态码： ${buildResultCode}"
                    def msg = """**结果**: 流式场景打包失败
**场景类型**: ${env.STREAMING_TYPE}
                    """

                    if (buildResultCode == 1){
                        msg += """
<font color = "#dc143c">**单端成功**: 安卓(下次打包会自动把失败的一端打包)</font>
${env.ANDROIDBACKINFO}
"""
                    }
                    else if (buildResultCode == 2){
                        msg += """
<font color = "#dc143c">**单端成功**: ios(下次打包会自动把失败的一端打包)</font>
${env.IOSBACKINFO}
"""
                    }
                    if (env.DISTRIBUTE_AB_ERROR_MSG) {
                        msg += """
<font color = "#dc143c">**分发ab错误信息**: ${env.DISTRIBUTE_AB_ERROR_MSG}</font>
"""
                    }
                    msg += """
<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>
${env.LOGBACKINFO}
"""
                    echo "${msg}"
                    //workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033',content: msg
                    if (sendUserString != "") {
                        emailext to: "${sendUserString}", subject: 'jenkins-流式场景打包失败', body: msg
                    }
                }
            }
        }
        unstable {
            node('master') {
                script{
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg="""
***场景类型***: ${env.STREAMING_TYPE}
***分发分支***: 版本内主支、${branch}
                        """
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}\n"""
                        }

                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: 'jenkins-流式场景打包部分失败', body: msg
                        }
                    }
                }
            }
        }
        success {
            node('master') {
                script{
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg="""
**场景类型**: ${env.STREAMING_TYPE}
**分发分支**: 版本内主支、${branch}
                        """
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}\n"""
                        }

                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: 'jenkins-流式场景打包成功', body: msg
                        }
                    }
                }
            }
        }
    }
}