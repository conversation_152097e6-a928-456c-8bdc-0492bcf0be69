node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

CACHED_DB = [:]

pipeline {
    agent {
        node {
            label "$params.Agent"
            customWorkspace "/data/workspace"
        }
    }
    options {
        timestamps()
//         disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
    }
    parameters {
        string defaultValue: '', description: '打包节点', name: 'Agent', trim: true
        string defaultValue: '', description: '分支名', name: '<PERSON><PERSON><PERSON>', trim: true
        string defaultValue: '', description: '版本号', name: 'ver_path', trim: true
        string defaultValue: '', description: '主服务器', name: 'main_server', trim: true
        string defaultValue: '', description: '从服务器', name: 'sub_server', trim: true
        string defaultValue: '', description: '', name: 'build_version', trim: true
        string defaultValue: '', description: '刷热更周更版本,不填默认刷最新包', name: 'weekly_version', trim: true
        booleanParam defaultValue: true, description: '是否全量编译', name: 'is_rebuild'
        string defaultValue: '', description: 'resource/release/online/trunk/branch', name: 'artifacts_branch', trim: true
        string defaultValue: '', description: '周更目录名称', name: 'week_config', trim: true
        string defaultValue: '', description: '现网版本刷热更所需参数', name: 'config_path', trim: true
        string defaultValue: '', description: '同步程序和PY脚本所在的目录，如果为空则使用exe_path', name: 'script_path', trim: true
        string defaultValue: '/data/workspace/x5_mobile/mobile_dancer/trunk/exe/', description: 'exe_path', name: 'exe_path', trim: true
        string defaultValue: '1', description: '打包步骤所需参数', name: 'pack_zip_type', trim: true
        string defaultValue: 'simp', description: '语言', name: 'language', trim: true
        booleanParam defaultValue: false, description: '是否部署服务器', name: 'is_deploy'
    }
    environment {
        PIPELINE_PYTHON = 'python3'
        LC_ALL = 'en_US.UTF-8'
        LANG = 'en_US.UTF-8'
    }
    stages {
        stage("更新流水线依赖") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                    """
                }
            }
        }
        stage("判断是否为特殊分支") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=prepare
                    """
                }
            }
        }
        stage('获取x5mobile代码') {
            steps {
                dir('pyframe-pipeline') {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=clone_x5mobile_on_compiler
                    """
                }
            }
        }
        stage('编译代码') {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=build
                    """
                }
            }
        }
        stage('替换配置') {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=update_config
                    """
                }
            }
        }
        stage("打包") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=package
                    """
                }
            }
        }
        stage("上传ftp") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=upload_ftp_server_package
                    """
                }
            }
        }
        stage("上传服务器包") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=upload_nexus_server_package
                    """
                }
            }
        }
        stage("调起白盒打镜像") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=white_box_build
                    """
                }
            }
        }
        stage("删除build目录") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        rm -r -f  /data/workspace/x5_mobile/mobile_dancer/trunk/server/build
                    """
                }
            }
        }
        stage("并行打包流程"){
            when {
                expression { params.is_deploy }
            }
            parallel {
                stage("主服务器") {
                    agent {
                        node {
                            label "$params.main_server"
                            customWorkspace "/data/workspace"
                        }
                    }
                    stages {
                        stage('更新流水线依赖'){
                            steps {
                                dir("pyframe-pipeline") {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                    """
                                }
                            }
                        }
                        stage("获取x5mobile代码") {
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=clone_x5mobile_on_server
                                    """
                                }
                            }
                        }
                        stage("踢人") {
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=kick_player
                                    """
                                }
                            }
                        }
                        stage("暂停180秒") {
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        sleep 180
                                    """
                                }
                            }
                        }
                        stage("关服"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=shutdown_server
                                    """
                                }
                            }
                        }
                        stage("分发安装包"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=download_ftp_server_package
                                    """
                                }
                            }
                        }
                        stage("部署安装"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=deploy_install
                                    """
                                }
                            }
                        }
                        stage("分发CDN CONFIG"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=download_ftp_cdn_config
                                    """
                                }
                            }
                        }
                        stage("修改gcloud配置"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=update_gcloud_config
                                    """
                                }
                            }
                        }
                        stage("同步数据库"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=sync_db
                                    """
                                }
                            }
                        }
                        stage("开服"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=start_all_server
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=start_global_server
                                    """
                                }
                            }
                        }

                    }

                }
                stage("从服务器") {
                    agent {
                        node {
                            label "$params.sub_server"
                            customWorkspace "/data/workspace"
                        }
                    }
                    stages {
                        stage('更新流水线依赖'){
                            steps {
                                dir("pyframe-pipeline") {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                    """
                                }
                            }
                        }
                        stage("获取x5mobile代码") {
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=clone_x5mobile_on_server
                                    """
                                }
                            }
                        }

                        stage("踢人") {
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=kick_player
                                    """
                                }
                            }
                        }
                        stage("暂停180秒") {
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        sleep 180
                                    """
                                }
                            }
                        }
                        stage("关服"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=shutdown_server
                                    """
                                }
                            }
                        }
                        stage("分发安装包"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=download_ftp_server_package
                                    """
                                }
                            }
                        }
                        stage("部署安装"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=deploy_install
                                    """
                                }
                            }
                        }
                        stage("分发CDN CONFIG"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=download_ftp_cdn_config
                                    """
                                }
                            }
                        }
                        stage("修改gcloud配置"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=update_gcloud_config
                                    """
                                }
                            }
                        }
                        stage("同步数据库"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=sync_db
                                    """
                                }
                            }
                        }
                        stage("开服"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=start_all_server
                                        ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=start_global_server
                                    """
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                    ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=post_canceled
                """
            }
        }
        success {
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                    ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=post_success
                """
            }
        }
        failure {
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                    ${PIPELINE_PYTHON} x5m.py deploy_public_server --job=post_failure
                """
            }
        }
    }
}
