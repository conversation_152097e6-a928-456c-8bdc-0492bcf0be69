node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

CACHED_DB = [:]

pipeline {
    agent none
    options {
        timestamps()
        timeout(time: 180, unit: 'MINUTES')
    }
    parameters {
        string defaultValue: '', description: '主服务器', name: 'main_server', trim: true
        string defaultValue: '', description: '从服务器', name: 'sub_server', trim: true
    }
    environment {
        PIPELINE_PYTHON = 'python3'
        LC_ALL = 'en_US.UTF-8'
        LANG = 'en_US.UTF-8'
    }
    stages {
        stage("并行打包流程"){
            parallel {
                stage("主服务器") {
                    agent {
                        node {
                            label "$params.main_server"
                            customWorkspace "/data/workspace"
                        }
                    }
                    stages {
                        stage('更新流水线依赖'){
                            steps {
                                dir("pyframe-pipeline") {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                    """
                                }
                            }
                        }
                        stage("关服"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py restart_public_server --job=shutdown_server
                                    """
                                }
                            }
                        }
                        stage("开服"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py restart_public_server --job=start_server_all
                                        ${PIPELINE_PYTHON} x5m.py restart_public_server --job=start_global_server
                                    """
                                }
                            }
                        }
                    }
                }
                stage("从服务器") {
                    agent {
                        node {
                            label "$params.sub_server"
                            customWorkspace "/data/workspace"
                        }
                    }
                    stages {
                        stage('更新流水线依赖'){
                            steps {
                                dir("pyframe-pipeline") {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                    """
                                }
                            }
                        }
                        stage("关服"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py restart_public_server --job=shutdown_server
                                    """
                                }
                            }
                        }
                        stage("开服"){
                            steps {
                                dir('pyframe-pipeline') {
                                    sh label: '',
                                    script: """
                                        ${PIPELINE_PYTHON} x5m.py restart_public_server --job=start_server_all
                                        ${PIPELINE_PYTHON} x5m.py restart_public_server --job=start_global_server
                                    """
                                }
                            }
                        }
                    }
               }
           }
        }
    }
    post {
        unstable {
            node(params.main_server) {
                dir("/data/workspace/pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py restart_public_server --job=post_canceled
                    """
                }
            }
        }
        success {
            node(params.main_server) {
                dir("/data/workspace/pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py restart_public_server --job=post_success
                    """
                }
            }
        }
        failure {
            node(params.main_server) {
                dir("/data/workspace/pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py restart_public_server --job=post_failure
                    """
                }
            }
        }
    }
}