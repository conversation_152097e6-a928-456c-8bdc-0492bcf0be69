node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

CACHED_DB = [:]

pipeline {
    agent {
        node {
            label "$params.private_server_ip"
            customWorkspace "/data/workspace"
        }
    }
    options {
        timestamps()
        timeout(time: 180, unit: 'MINUTES')
    }
    parameters {
        string defaultValue: '', description: '打包节点', name: 'private_server_ip', trim: true
    }
    environment {
        PIPELINE_PYTHON = 'python3'
        LC_ALL = 'en_US.UTF-8'
        LANG = 'en_US.UTF-8'
    }
    stages {
        stage("更新流水线依赖") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                    """
                }
            }
        }
        stage("关服") {
            steps {
                dir("pyframe-pipeline") {
                    sh label: '',
                    script: """
                        ${PIPELINE_PYTHON} x5m.py close_private_server --job=stop_all_server
                    """
                }
            }
        }
    }
    post {
        unstable {
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                    ${PIPELINE_PYTHON} x5m.py close_private_server --job=post_canceled
                """
            }
        }
        success {
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                    ${PIPELINE_PYTHON} x5m.py close_private_server --job=post_success
                """
            }
        }
        failure {
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                    ${PIPELINE_PYTHON} x5m.py close_private_server --job=post_failure
                """
            }
        }
    }
}