@Library('h3d_libs') _
import groovy.json.JsonSlurper
def changeList = ""
def isAndroidPackage = "false"
def isIosPackage = "false"
def isWindowsPackage = "false"
def versionName = ""
def shaderZipFileName = ""
def shaderP4SubmitUser = ""
String[] view_list
def currentRunSteps = 0
def messageSendUserList = ["<EMAIL>", "<EMAIL>", "ji<PERSON><PERSON><PERSON><PERSON>@h3d.com.cn", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
"<EMAIL>", "<EMAIL>", "<EMAIL>",  "bian<PERSON><PERSON><PERSON>@h3d.com.cn",  "<EMAIL>", "<EMAIL>", "<EMAIL>"]
//def messageSendUserList = ["<EMAIL>"]
def errorMessageSendUserList = ["<EMAIL>"]    // 特殊的错误终止，包括：传入数据的校验，错误提示信息发送人
def errorTipsMessage = ""
def distributeShaderJenkinsUrl = "http://jenkins-x5mobile.h3d.com.cn/job/art_package/job/shader_distribute/buildWithParameters?token=11d11de7ad4022a12baeb47d281d40e573&version_name="
def timeRetryAndroid = 0
def timeRetryIos = 0
/* 执行状态码：
0：android、ios、windows都执行失败；
1：android成功；
2：ios成功；
3: android、ios都执行成功
4： windows成功
5：android、windows都执行成功
6：windows、ios都执行成功
7：android、windows、ios都执行成功
*/
def buildResultCode = 0


def getShaderP4View(platform, versionName) {
    // 测试环境映射
    def tpl = '''
//x5_mobile/mr/art_release/shader/${versionName}/shader.zip //${p4Client}/mr/art_release/shader/${versionName}/shader.zip
//x5_mobile/mr/art_release/shader/${versionName}/${PLATFORM}/assetbundles/global_dependeencies/... //${p4Client}/mr/art_release/shader/${versionName}/${PLATFORM}/assetbundles/global_dependeencies/...
    '''
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([p4Client: '${P4_CLIENT}', PLATFORM: platform, versionName: versionName]).toString()
}


def IsPackage(platform,isAndroidPackage,isIosPackage,isWindowsPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    }else if (platform == "ios"){
        return isIosPackage == "true"
    }else{
        return isWindowsPackage == "true"
    }
}


def getArtTrunkFromGitlab(){
    //checkout changelog: false, poll: false,
    //scm: [$class: 'GitSCM',
    //branches: [[name: "*/master"]],
    //doGenerateSubmoduleConfigurations: false,
    //extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    //submoduleCfg: [],
    //userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    //url: '******************************:dgm/arttrunk.git']]]
    def getRepo=false
    if(!fileExists("x5_mobile/.git")){
        getRepo=true
        echo "x5_mobile/.git 文件夹不存在,需要强更"
    }
    else{
        dir("x5_mobile") {
            //检查是否正常仓库
           ret= bat(label:"检查Git仓库",returnStatus :true,encoding:'GBK',script:'git branch')
           //如果不是正常的git仓库重新下载代码
           if(ret !=0) {
              getRepo=true
              echo  "不是正常的git仓库需要强制更新"
           }
        }
    }
    if(getRepo.toBoolean()){
        //首次正常下载代码
        env.ErrInfo='创建本地Git仓库失败'
        bat label:"克隆新仓库",script:"<NAME_EMAIL>:dgm/arttrunk.git x5_mobile"
    }
    else{
        dir("x5_mobile"){
            bat label:"切换分支",script:"""
            git fetch --all
            git reset --hard
            git clean -d -fx
            git pull
            """
        }
    }
}


def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}

// 清理软链接
def deleteMakeLink(platform, linkPath) {
    try {
        bat(script: "rd /s /q ${linkPath}")
        println("平台${platform} 已删除软链接:${linkPath}")
    } catch (exc) {
        echo "平台${platform} 不存在该软链接, 不删除"
    }
}


pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 60, unit: 'MINUTES')
        buildDiscarder logRotator(numToKeepStr: env.numToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
        retry(1)
    }

    parameters {
         string(name: 'version_name', defaultValue: '', description: 'shader版本号:比如:0.11866, shader版本号与p4 changlist号二选一输入', trim: true)
         string(name: 'changelist', defaultValue: '', description: 'p4changelist号', trim: true)
    }

    //triggers {
    //    cron('H/30 0-5,8-20 * * *\nH/10 21-23 * * *')
    //}

    stages {
        stage('shader打包'){
            matrix {
                agent {
                    node{
                        label "shader-alone-${PLATFORM}-package"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'android','ios','windows'
                    }
                }
                stages{
                    stage('配置初始化'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDErrInfo='android端配置初始化失败'
                                    }else if ("${PLATFORM}" == "ios"){
                                        env.IOSErrInfo='ios端配置初始化失败'
                                    }else{
                                        env.WINDOWSErrInfo='windows端配置初始化失败'
                                    }
                                    currentRunSteps = 1
                                    getBuildUserInfo()
                                    // 判断配置 config.ini 是否存在，存在, 不处理, 否则判断 config.ini.example 是否存在, 存在，改名为config.ini
                                    def configFile = "config.ini"
                                    def configExampleFile = "config.ini.example"
                                    def configExists = fileExists "$configFile"
                                    def exampleExists = fileExists "$configExampleFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
                                    if (!configExists) {
                                        // 不存在
                                        if (exampleExists) {
                                            // 改名
                                            fileOperations([fileRenameOperation(destination: "${configFile}", source: "${configExampleFile}")])
                                            // 初始化环境时, 执行python环境
                                            bat """
                                            python -m pip install --upgrade pip
                                            pip install -r ${env.WORKSPACE}\\gitlab\\Script\\scripts\\requirements.txt
                                            """
                                        }
                                    }
                                }
                            }
                        }
                    }
                    stage('changelist校验'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDErrInfo='android端changelist校验失败'
                                    }else if ("${PLATFORM}" == "ios"){
                                        env.IOSErrInfo='ios端changelist校验失败'
                                    }else{
                                        env.WINDOWSErrInfo='windows端changelist校验失败'
                                    }
                                    currentRunSteps = 2
                                    // 如果同时传入了changelist和version name 则提示
                                    echo "传入的值: ${params.version_name}, ${params.changelist}"
                                    if ("${params.version_name}" && "${params.changelist}") {
                                        errorTipsMessage = "传入参数：changelist与shader版本二选一"
                                        // 获取失败, 直接终止
                                        currentBuild.result = 'ABORTED'
                                    } else if ("${params.changelist}") {
                                        def check_out = bat(returnStdout: true, script: "python setup.py get_shader_updates --changelist=${params.changelist}").split('\n')
                                        println("通过changelist获取shader版本返回的check_out值:${check_out}")
                                        def jsonSlurper = new JsonSlurper()
                                        def result_check = jsonSlurper.parseText(check_out[2])
                                        if (result_check.update == "false") {
                                            def errorInfoP4 = result_check.msg
                                            errorTipsMessage = "校验传入的changelist错误信息: ${errorInfoP4}"
                                            // 获取失败, 直接终止
                                            currentBuild.result = 'ABORTED'
                                        } else {
                                            versionName = result_check.version_name
                                        }
                                    } else {
                                        versionName = "${params.version_name}"
                                    }
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包'){
                        when {
                            expression {
                                return versionName != ""
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDErrInfo='android端检查是否需要打包失败'
                                    }else if ("${PLATFORM}" == "ios"){
                                        env.IOSErrInfo='ios端检查是否需要打包失败'
                                    }else{
                                        env.WINDOWSErrInfo='windows端检查是否需要打包失败'
                                    }
                                    currentRunSteps = 2
                                    echo "versionName: ${versionName}"
                                    def out = bat(returnStdout: true, script: "python setup.py get_shader_updates --version_name=${versionName}").split('\n')
                                    println("检查shader版本返回的out值:${out}")
                                    def jsonSlurper = new JsonSlurper()
                                    def result_map = jsonSlurper.parseText(out[2])
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROID_WORKSPACE = env.WORKSPACE
                                        isAndroidPackage = result_map.update
                                        env.ANDROID_CHECK_MSG = result_map.msg
                                        env.ANDROID_PIPELINE_ID = result_map.pipeline_id
                                        println(isAndroidPackage)
                                    }else if ("${PLATFORM}" == "ios"){
                                        env.IOS_WORKSPACE = env.WORKSPACE
                                        isIosPackage = result_map.update
                                        env.IOS_CHECK_MSG = result_map.msg
                                        env.IOS_PIPELINE_ID = result_map.pipeline_id
                                        println(isIosPackage)
                                    } else {
                                        env.WINDOWS_WORKSPACE = env.WORKSPACE
                                        isWindowsPackage = result_map.update
                                        env.WINDOWS_CHECK_MSG = result_map.msg
                                        env.WINDOWS_PIPELINE_ID = result_map.pipeline_id
                                        println(isWindowsPackage)
                                    }
                                    if (result_map.update == "false" && result_map.commit_status == "") {
                                        def errorInfo = result_map.msg
                                        errorTipsMessage = "校验传入的版本错误信息: ${errorInfo}"
                                        // 获取失败, 直接终止
                                        currentBuild.result = 'ABORTED'
                                    }
                                    shaderZipFileName = result_map.zip_file_name
                                    shaderP4SubmitUser = result_map.submit_user
                                }
                            }
                        }
                    }
                    stage('获取p4和工程资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isWindowsPackage)
                            }
                        }
                        steps{
                            script{
                                if ("${PLATFORM}" == "android"){
                                    env.ANDROIDErrInfo='android端获取p4和工程资源失败'
                                }else if ("${PLATFORM}" == "ios"){
                                    env.IOSErrInfo='ios端获取p4和工程资源失败'
                                }else{
                                    env.WINDOWSErrInfo='windows端获取p4和工程资源失败'
                                }
                                currentRunSteps = 3
                                bat """git config --global core.longpaths true"""
                                getArtTrunkFromGitlab()
                                def platform
                                if ("${PLATFORM}" == "android"){
                                    platform = "android"
                                } else if ("${PLATFORM}" == "ios") {
                                    platform = "iOS"
                                } else {
                                    platform = "windows"
                                }
                                def view = getShaderP4View(platform, "${versionName}")
                                echo "view:${view}"

                                def p4_root = pwd()
                                def p4_workspace = "jenkins-shader-alone-${PLATFORM}-package"
                                def python_view = view.replaceAll("\n", "#")
                                bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${p4_root} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=true")


//                                 checkout perforce(credential: 'p4_dgm_jenkins_upwd',
//                                     populate: syncOnly(force: true, have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: env.P4_REVISION, quiet: true, revert: false),
//                                     workspace: manualSpec(
//                                         charset: 'none',
//                                         cleanup: false,
//                                         name: "jenkins-shader-alone-${PLATFORM}-package",
//                                         pinHost: false,
//                                         spec: clientSpec(
//                                             allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
//                                             locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: view
//                                         )
//                                     )
//                                 )
                            }
                        }
                    }
                    stage('清理工作'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isWindowsPackage)
                            }
                        }
                        steps {
                            script{
                                if ("${PLATFORM}" == "android"){
                                    env.ANDROIDErrInfo='android端清理工作失败'
                                }else if ("${PLATFORM}" == "ios"){
                                    env.IOSErrInfo='ios端清理工作失败'
                                }else{
                                    env.WINDOWSErrInfo='windows端清理工作失败'
                                }
                                currentRunSteps = 4
                                // 清理原shader目录
                                dir("x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/shaders") {
                                    deleteDir()
                                }
                                dir("x5_mobile/mobile_dancer/arttrunk/client/Assets/ExternPlugins/NGUI/Resources/Shaders") {
                                    deleteDir()
                                }
                                // 清理之前的压缩文件
                                dir("mr/art_release/shader/${versionName}/shader") {
                                    deleteDir()
                                }
                                def workspace
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROID_WORKSPACE
                                } else if ("${PLATFORM}" == "ios") {
                                    workspace = env.IOS_WORKSPACE
                                } else {
                                    workspace = env.WINDOWS_WORKSPACE
                                }
                                // 清理软链接
                                if ("${PLATFORM}" == "windows"){
                                    deleteMakeLink("${PLATFORM}", "${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\tools\\BundleBuild\\Builder\\Assets\\resources\\shaders")
                                    deleteMakeLink("${PLATFORM}", "${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\tools\\BundleBuild\\Builder\\Assets\\ExternPlugins\\NGUI\\Resources\\Shaders")
                                } else {
                                    deleteMakeLink("${PLATFORM}", "${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\\Assets\\resources\\shaders")
                                    deleteMakeLink("${PLATFORM}", "${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\\Assets\\ExternPlugins\\NGUI\\Resources\\Shaders")
                                }
                                // 根据workspace目录下的 flag.txt文件中的值,判断是否需要清除 Library/metadata目录
                                def flagFilePath = "${workspace}\\flag.txt"
                                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                    def out = bat(returnStdout: true, script: "python setup.py read_file --filepath=${flagFilePath}").split('\n')
                                    println("读取flag返回的out值:${out}")
                                    def result_map = out[2]
                                    if (result_map == "false\r") {
                                        dir("${workspace}/x5_mobile/mobile_dancer/arttrunk/client/Library/metadata") {
                                            echo "清理 metadata"
                                            deleteDir()
                                        }
                                        dir("${workspace}/x5_mobile/mobile_dancer/arttrunk/tools/BundleBuild/Builder/Library/metadata") {
                                            deleteDir()
                                        }
                                    }
                                    bat(script: "echo false>${flagFilePath}")
                                }
                            }
                        }
                    }
                    stage('解压zip文件'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isWindowsPackage)
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDErrInfo='android端解压zip文件失败'
                                    }else if ("${PLATFORM}" == "ios"){
                                        env.IOSErrInfo='ios端解压zip文件失败'
                                    }else{
                                        env.WINDOWSErrInfo='windows端解压zip文件失败'
                                    }
                                    currentRunSteps = 5
                                    echo "shader压缩文件名: ${shaderZipFileName}"
                                    def workspace
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROID_WORKSPACE
                                    } else if ("${PLATFORM}" == "ios") {
                                        workspace = env.IOS_WORKSPACE
                                    } else {
                                        workspace = env.WINDOWS_WORKSPACE
                                    }
                                    def out = bat(returnStdout: true, script: "python setup.py unzip_shader_file --file_path=${workspace}/mr/art_release/shader --version_name=${versionName} --filename=${shaderZipFileName}").split('\n')
                                    println("解压zip返回的out值:${out}")
                                }
                            }
                        }
                    }
                    stage('校验解压后文件'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isWindowsPackage)
                            }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDErrInfo='android端校验解压后文件失败'
                                    }else if ("${PLATFORM}" == "ios"){
                                        env.IOSErrInfo='ios端校验解压后文件失败'
                                    }else{
                                        env.WINDOWSErrInfo='windows端校验解压后文件失败'
                                    }
                                    currentRunSteps = 6
                                    echo "shader压缩文件名: ${shaderZipFileName}"
                                    def workspace
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROID_WORKSPACE
                                    } else if ("${PLATFORM}" == "ios") {
                                        workspace = env.IOS_WORKSPACE
                                    } else {
                                        workspace = env.WINDOWS_WORKSPACE
                                    }
                                    def out = bat(returnStdout: true, script: "python setup.py check_unzip_shader_file --file_path=${workspace}/mr/art_release/shader/${versionName}/shader").split('\n')
                                    println("校验解压文件返回的out值:${out}")
                                    def jsonSlurper = new JsonSlurper()
                                    def result_map = jsonSlurper.parseText(out[2])
                                    env.SHADER_MD5_CHECK_RESULT = result_map.check_result
                                    if (result_map.check_result == "true") {
                                        env.SHADER_MD5_CHECK_RESULT_INFO = "成功"
                                    }else {
                                        env.SHADER_MD5_CHECK_RESULT_INFO = "失败"

                                    }

                                    env.SHADER_MD5_CHECK_MSG = result_map.check_msg
                                }
                            }
                        }
                    }
                    stage('make_link'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isWindowsPackage) && env.SHADER_MD5_CHECK_RESULT == "true"
                            }
                        }
                        steps {
                            script{
                                if ("${PLATFORM}" == "android"){
                                    env.ANDROIDErrInfo='android端make_link失败'
                                }else if ("${PLATFORM}" == "ios"){
                                    env.IOSErrInfo='ios端make_link失败'
                                }else{
                                    env.WINDOWSErrInfo='windows端make_link失败'
                                }
                                currentRunSteps = 7
                                def workspace
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROID_WORKSPACE
                                } else if ("${PLATFORM}" == "ios") {
                                    workspace = env.IOS_WORKSPACE
                                } else {
                                    workspace = env.WINDOWS_WORKSPACE
                                }
                                String shaderResourcePath = "${workspace}\\mr\\art_release\\shader\\${versionName}\\shader"
                                if ("${PLATFORM}" == "windows"){
                                    bat(script: "mklink /J ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\tools\\BundleBuild\\Builder\\Assets\\resources\\shaders ${shaderResourcePath}\\Assets\\resources\\shaders")
									bat(script: "mklink /J ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\tools\\BundleBuild\\Builder\\Assets\\ExternPlugins\\NGUI\\Resources\\Shaders ${shaderResourcePath}\\Assets\\ExternPlugins\\NGUI\\Resources\\Shaders")
                                }else{
                                    bat(script: "mklink /J ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\\Assets\\resources\\shaders ${shaderResourcePath}\\Assets\\resources\\shaders")
									bat(script: "mklink /J ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\\Assets\\ExternPlugins\\NGUI\\Resources\\Shaders ${shaderResourcePath}\\Assets\\ExternPlugins\\NGUI\\Resources\\Shaders")
                                }
                            }
                        }
                    }
                    stage('调用shader打包'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isWindowsPackage) && env.SHADER_MD5_CHECK_RESULT == "true"
                            }
                        }
						steps {
							script{
							    if ("${PLATFORM}" == "android"){
                                    env.ANDROIDErrInfo='android端调用shader打包失败'
                                }else if ("${PLATFORM}" == "ios"){
                                    env.IOSErrInfo='ios端调用shader打包失败'
                                }else{
                                    env.WINDOWSErrInfo='windows端调用shader打包失败'
                                }
							    currentRunSteps = 8
							    def workspace
							    String platform
							    String abPlatform
                                if ("${PLATFORM}" == "android"){
                                    platform = "android"
                                    abPlatform = "android"
                                    workspace = env.ANDROID_WORKSPACE
                                } else if ("${PLATFORM}" == "ios") {
                                    platform = "iOS"
                                    abPlatform = "iOS"
                                    workspace = env.IOS_WORKSPACE
                                } else {
                                    platform = "Win64"
                                    abPlatform = "windows"
                                    workspace = env.WINDOWS_WORKSPACE
                                }
								String outPath = "${workspace}\\mr\\art_release\\shader\\${versionName}\\${abPlatform}\\assetbundles"
                                bat"""
                                cd ${outPath}
                                set abOutPath=${outPath}\\global_dependeencies
                                if exist %abOutPath% (echo 目录%abOutPath%已存在，无需创建) else (md %abOutPath%)
                                """
								if("${PLATFORM}" == "windows"){
									bat(script: "unity -quit -batchmode -logFile package_${PLATFORM}.log -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\tools\\BundleBuild\\Builder\" -executeMethod ShaderBuilder.GenerateWinShaderAb -buildTarget ${platform} outputPath=${outPath}")
								}else{
									bat(script: "unity -quit -batchmode -logFile package_${PLATFORM}.log -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" -executeMethod H3DABTool.ShaderABBuilder.GenerateShaderAb -buildTarget ${platform} platform=${platform} outputPath=${outPath}")
								}
							}
						}
					}
					stage('提交p4'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isWindowsPackage) && env.SHADER_MD5_CHECK_RESULT == "true"
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDErrInfo='android端提交p4失败'
                                    }else if ("${PLATFORM}" == "ios"){
                                        env.IOSErrInfo='ios端提交p4失败'
                                    }else{
                                        env.WINDOWSErrInfo='windows端提交p4失败'
                                    }
                                    currentRunSteps = 9
                                    def workspace
                                    String abPlatform
                                    String pipelineId
                                    if ("${PLATFORM}" == "android"){
                                        abPlatform = "android"
                                        workspace = env.ANDROID_WORKSPACE
                                        pipelineId = env.ANDROID_PIPELINE_ID
                                    } else if ("${PLATFORM}" == "ios") {
                                        abPlatform = "iOS"
                                        workspace = env.IOS_WORKSPACE
                                        pipelineId = IOS_PIPELINE_ID
                                    } else {
                                        abPlatform = "windows"
                                        workspace = env.WINDOWS_WORKSPACE
                                        pipelineId = env.WINDOWS_PIPELINE_ID
                                    }
                                    def submitP4Desc = "--Auto--|uploader:${shaderP4SubmitUser}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitP4Desc}"
                                    def out = bat(returnStdout: true,script: "python setup.py shader_submit_to_p4  --version_name=${versionName} --pipeline_id=${pipelineId} --submit_path=${workspace}/mr/art_release/shader/${versionName}/${abPlatform}/assetbundles/global_dependeencies --desc=\"${submitP4Desc}\" --workspace=jenkins-shader-alone-${PLATFORM}-package")
                                    echo "${out}"
                                    String[] outArras=out.split("\n")
                                    if (outArras.size() > 4){
                                        println("p4workspace下没有需要提交的内容！")
                                    }else{
                                        if ("${PLATFORM}" == "android"){
                                            env.ANDROID_CHANGELIST = outArras[2]
                                        }else if ("${PLATFORM}" == "ios"){
                                            env.IOS_CHANGELIST = outArras[2]
                                        }else{
                                            env.WINDOWS_CHANGELIST = outArras[2]
                                        }
                                    }
                                    def flagFilePath = "${workspace}\\flag.txt"
									if(currentBuild.currentResult == "SUCCESS"){
									    echo "write success info"
									    bat(script: "echo true>${flagFilePath}")
									}
                                }
                            }
                        }
                    }
                    stage('判断是否单端失败'){
                          when {
                             expression {
                                 buildResultCode <= 7
                             }
                         }
                         steps{
                             script{
                                 if ("${PLATFORM}" == "android"){
                                     buildResultCode += 1
                                 }
                                 else if ("${PLATFORM}" == "ios") {
                                     buildResultCode += 2
                                 }
                                 else {
                                     buildResultCode += 4
                                 }
                             }
                         }
                    }
                }
                post {
                    // 使用always 方法上传制品 archiveArtifacts制品管理
                    // 在各自的打包节点上发送post通知
                    always{
                        script {
                            if (currentRunSteps >= 8){
                                def unityLog="package_${PLATFORM}.log"
                                def logExists = fileExists "$unityLog"
                                if(logExists){
                                    archiveArtifacts artifacts: "$unityLog", followSymlinks: false
                                }
                            }
                            //定义信息模板
                            env.ANDROID_TEMPLATE = """
**Android ab包 changelist**: ${env.ANDROID_CHANGELIST}
                            """
                            env.IOS_TEMPLATE = """
**IOS ab包 changelist**: ${env.IOS_CHANGELIST}
                            """
                            env.WINDOWS_TEMPLATE = """
**Windows ab包 changelist**: ${env.WINDOWS_CHANGELIST}
                            """
                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    def sendUserString = messageSendUserList.join(",")
                    def msg="""*结果*: shader独立目录打包失败
**shader版本**: ${versionName}
**git分支**：master
**shader解压后校验结果**: ${env.SHADER_MD5_CHECK_RESULT_INFO}
                        """
                    if (env.SHADER_MD5_CHECK_RESULT == "false") {
                        msg += """
<font color = "#dc143c">**shader解压校验失败信息**: ${env.SHADER_MD5_CHECK_MSG}</font>
                        """
                    }
                    if (buildResultCode == 1){
                        msg += """
<font color = "#dc143c">**安卓成功**:</font>
${env.ANDROID_TEMPLATE}
<font color = "#dc143c">**IOS错误信息**: ${env.IOSErrInfo}</font>
<font color = "#dc143c">**windows错误信息**: ${env.WINDOWSErrInfo}</font>
"""
                    } else if (buildResultCode == 2){
                        msg += """
<font color = "#dc143c">**ios成功**:</font>
${env.IOS_TEMPLATE}
<font color = "#dc143c">**android错误信息**: ${env.ANDROIDErrInfo}</font>
<font color = "#dc143c">**windows错误信息**: ${env.WINDOWSErrInfo}</font>
"""
                    } else if (buildResultCode == 3){
                        msg += """
<font color = "#dc143c">**安卓、ios成功**:</font>
${env.ANDROID_TEMPLATE}
${env.IOS_TEMPLATE}
<font color = "#dc143c">**windows错误信息**: ${env.WINDOWSErrInfo}</font>
"""
                    } else if (buildResultCode == 4){
                        msg += """
<font color = "#dc143c">**windows成功**:</font>
${env.WINDOWS_TEMPLATE}
<font color = "#dc143c">**IOS错误信息**: ${env.IOSErrInfo}</font>
<font color = "#dc143c">**Android错误信息**: ${env.ANDROIDErrInfo}</font>
"""
                    } else if (buildResultCode == 5){
                        msg += """
<font color = "#dc143c">**安卓、windows成功**:</font>
${env.ANDROID_TEMPLATE}
${env.WINDOWS_TEMPLATE}
<font color = "#dc143c">**IOS错误信息**: ${env.IOSErrInfo}</font>
"""
                    } else if (buildResultCode == 6){
                        msg += """
<font color = "#dc143c">**windows、ios成功**:</font>
${env.WINDOWS_TEMPLATE}
${env.IOS_TEMPLATE}
<font color = "#dc143c">**android错误信息**: ${env.ANDROIDErrInfo}</font>
"""
                    }

                    msg += """
**日志链接**: ${env.BUILD_URL}console
                    """
                    if (env.ErrInfo) {
                        msg += """
<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>
                    """
                    }
                    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5c96cc13-e7a2-47b2-aaf8-e1ae03d0e0ab',content: msg
                    if (sendUserString != "") {
                        emailext to: "${sendUserString}", subject: 'shader独立目录打包失败', body: msg
                    }
                }
            }
        }
        aborted {
            node('master') {
                script {
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!errorMessageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            errorMessageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    def sendUserString = errorMessageSendUserList.join(",")
                    def msg="""*结果*: shader独立目录打包失败
**shader版本**: ${versionName}
**日志链接**: ${env.BUILD_URL}console
**失败信息**: ${errorTipsMessage}
\n"""
                    if (sendUserString != "") {
                        emailext to: "${sendUserString}", subject: 'shader独立目录打包失败', body: msg
                    }
                }
            }
        }
        success {
            node('master') {
                script{
                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                        }
                    }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true" || isWindowsPackage == "true"){
                        def msg="""
**shader版本**: ${versionName}
**shader解压后校验结果**: ${env.SHADER_MD5_CHECK_RESULT_INFO}
                        """
                        if (env.SHADER_MD5_CHECK_RESULT == "false") {
                            msg += """<font color = "#dc143c">**shader解压校验失败信息**: ${env.SHADER_MD5_CHECK_MSG}</font>\n"""
                        }
                        if (isAndroidPackage == "true")
                        {
                            msg += env.ANDROID_TEMPLATE

                        }
                        if (isIosPackage == "true")
                        {
                            msg+= env.IOS_TEMPLATE
                        }
                        if (isWindowsPackage == "true")
                        {
                            msg+= env.WINDOWS_TEMPLATE
                        }

                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: 'jenkins-shader独立目录打包成功', body: msg
                        }
                        // 打包成功, 调起shader分发流水线
                        def response = httpRequest contentType: 'APPLICATION_JSON',
                            httpMode: "GET",
                            customHeaders: [
                                [name: "TOKEN", value: "11d11de7ad4022a12baeb47d281d40e573"]
                            ],
                            url: "${distributeShaderJenkinsUrl}${versionName}"
                        println response.status
                        println response.content
                    }
                }
            }
        }
    }
}
