@Library('h3d_libs') _
def changeList = ""
def texturePaths = ""
def isAndroidPackage = "true"
def isIosPackage = "true"
def isHarmonyPackage = "true"
def timeRetryAndroid = 0
def timeRetryIos = 0
def timeRetryHarmony = 0
String[] view_list

def messageSendUserList = ["<EMAIL>"]
// def messageSendUserList = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>"]
/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
def buildResultios = false
def buildResultAndroid = false
def buildResultHarmony = false

def getP4View(platform) {
    def tpl = ""
//     def tpl = '''
// //x5m/res/cdn/cooked/${platform}/assetbundles/texture/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/texture/...
// //x5m/res/cdn/cooked/${platform}/assetbundles/c/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/c/...
//     '''
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([platform: platform, p4Client: '${P4_CLIENT}']).toString()
}

def getTextureView(view_list){
    def tpl = ''
    for(String v in view_list){
        tpl+=v+'\n'
    }
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([p4Client: '${P4_CLIENT}']).toString()
}


def getArtTrunkFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/release"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    url: '******************************:dgm/arttrunk.git']]]
}


def IsPackage(platform,isAndroidPackage,isIosPackage,isHarmonyPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    }else if (platform == "ios"){
        return isIosPackage == "true"
    }else if (platform == "harmony"){
        return isHarmonyPackage == "true"
    }
    return false
}


def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}

def getMaxChangelist() {
    def max_changelist = [env.ANDROID_CHANGELIST.toInteger(), env.IOS_CHANGELIST.toInteger()].max()
    return max_changelist
}


pipeline {
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 120, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
         booleanParam(name: 'force', defaultValue: true, description: '是否强更P4')
    }
    //     因为四点到五点p4需要备份数据，流水线会执行失败
    // triggers {
    //     cron('H/30 0-3,5-23 * * *')
    // }
    stages {
        stage('美术CDN资源打包'){
            matrix {
                agent {
                    node{
                        label "${PLATFORM}-art-cdn-package-newp4-test"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values "ios", "android", "harmony"
                        // values "harmony"
                    }
                }
                stages{
                    stage('配置初始化'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                script{
                                    env.ErrInfo='配置初始化失败'
                                    getBuildUserInfo()
                                    if (!fileExists(".git/config")) {
                                        bat """
                                        del /s /f /q .
                                        """
                                    }
                                    // 判断配置 config.ini 是否存在，存在, 不处理, 否则判断 config.ini.example 是否存在, 存在，改名为config.ini
                                    def configFile = "config.ini"
                                    def configExampleFile = "config.ini.example"
                                    def configExists = fileExists "$configFile"
                                    def exampleExists = fileExists "$configExampleFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
                                    if (!configExists) {
                                        // 不存在
                                        bat """
                                        git clone -b texture-${PLATFORM} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                                        """
                                    }
                                    bat """
                                    git clean -xdf
                                    git reset --hard HEAD
                                    git pull origin texture-${PLATFORM}
                                    """
                                }
                            }
                        }
                    }

                    stage('检查是否需要打包'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='检查是否需要打包失败'
                                    def out = bat(returnStdout: true, script: "python setup.py get_updates --tp texture --first_changelist=${params.first_changelist}").split('\n')
                                    def result_map = readJSON text: out[2]
                                    println(result_map)

                                    env.TEXTURE_PATHS = ""
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDWORKSPACE = env.WORKSPACE
                                        isAndroidPackage = result_map.update
                                        println(isAndroidPackage)
                                    }else if ("${PLATFORM}" == "ios") {
                                        env.IOSWORKSPACE = env.WORKSPACE
                                        isIosPackage = result_map.update
                                        println(isIosPackage)
                                    } else if ("${PLATFORM}" == "harmony") {
                                        env.HARMONYWORKSPACE = env.WORKSPACE
                                        isHarmonyPackage = result_map.update
                                        println(isHarmonyPackage)
                                    }
                                    if (result_map.update == 'true'){
                                        view_list = result_map.views.split(';')
                                        println("texturePaths:${view_list}")
                                        texturePaths = result_map.texturepaths
                                        println("texturePaths:${texturePaths}")
                                        changeList = result_map.changelist
                                        env.SEND_MESSAGE_USER_INFO = result_map.send_user
                                        env.TEXTURE_PATHS = result_map.texturepaths.replace(",", "\n")
                                        writeFile encoding: 'GB2312', file: "${workspace}/package_list/package_list_${env.BUILD_NUMBER}.log", text: "${env.TEXTURE_PATHS}"
                                    }
                                    if(isAndroidPackage == 'false' && isIosPackage == 'false' && isHarmonyPackage == "false"){
                                        currentBuild.result = 'ABORTED'
                                    }
                                }
                            }
                        }
                    }
                    stage('获取p4资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                            }
                        }
                        steps{
                            retry(2){
                                script{
                                    env.ErrInfo='获取p4资源失败'
                                    if ("${PLATFORM}" == "android"){
                                        timeRetryAndroid ++
                                        echo "安卓执行次数：${timeRetryAndroid}"
                                    }
                                    else if ("${PLATFORM}" == "android"){
                                        timeRetryIos ++
                                        echo "ios执行次数:${timeRetryIos}"
                                    }
                                    else if ("${PLATFORM}" == "harmony"){
                                        timeRetryHarmony ++
                                        echo "harmony执行次数:${timeRetryHarmony}"
                                    }
                                    bat """
                                        git config --system core.longpaths true
                                        """
                                    getArtTrunkFromGitlab()
                                    def view = getP4View("${PLATFORM}")
                                    // println(view)
                                    println("当前路径是: " + pwd())
                                    def p4_workspace = "jenkins-art-cdn-package-${PLATFORM}"
                                    def python_view = view.replaceAll("\n", "#")
                                    try {
                                        timeout(time: 5, unit: 'MINUTES') {
                                            bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=${params.force}")
                                        }
                                    } catch (err) {
                                        def sendUserString = messageSendUserList.join(",")
                                        if (sendUserString != "") {
                                            emailext to: "${sendUserString}", subject: "jenkins-CDN图片打包 #${env.BUILD_NUMBER}", body: "获取p4资源卡住超时，重试中"
                                        }
                                        error("获取p4资源卡住超时，重试中")
                                    }

                                    dir('x5_mobile/mr/art_release/art_src/texture'){
                                        deleteDir()
                                    }
                                    dir('x5_mobile/mobile_dancer/arttrunk/client/Assets/ab_resources'){
                                        try {
                                            bat script: """
                                                rmdir "texture"
                                                """
                                        }
                                        catch (exc) {
                                            echo '不需要删除!'
                                        }
                                    }

                                    def texture_view = getTextureView(view_list)
                                    println(texture_view)
                                    def texture_p4_workspace = "jenkins-${PLATFORM}-texture-package"
                                    def texture_python_view = texture_view.replaceAll("\n", "#")
                                    writeFile encoding: 'GB2312', file: "p4_view.txt", text: "${texture_python_view}"

                                    bat(returnStdout: false, script: "python pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${texture_p4_workspace} --force_sync=${params.force}")

                                }
                            }
                        }
                    }
                    //1. 检查新Git仓库
                    stage('获取config仓库'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                            }
                        }

                        steps{
                            script{
                                env.ErrInfo='获取config仓库失败'
                                def  getRepo =false
                                if (!fileExists('../x5mconfig/.git')) {
                                    echo "x5mconfig/.git 文件夹不存在"
                                    getRepo = true
                                } else {
                                    dir('../x5mconfig') {
                                        //检查是否正常仓库
                                       ret= bat(label:"检查Git仓库",returnStatus :true,encoding:'GBK',script:'git branch')
                                       //如果不是正常的git仓库重新下载代码
                                       if(ret !=0) {
                                          getRepo = true;
                                          echo  "不是正常的git仓库需要强制更新"
                                       }
                                       else{
                                       bat label:"克隆新仓库",script:"""
                                       git pull
                                       del /f /s /q "${workspace}\\x5_mobile\\mr\\art_release\\cs\\config\\card"
                                       echo d y| xcopy "config\\card\\card_decorate_wh.csv" "${workspace}\\x5_mobile\\mr\\art_release\\cs\\config\\card" /e /y /r
                                       """
                                       }
                                    }
                                }
                                if(getRepo.toBoolean()){
                                    dir('..') {

                                        bat label: '克隆新仓库', script: """
                                            git clone -b cdn http://dgm_developer%%40h3d.com.cn:<EMAIL>/dgm/x5mconfig.git
                                            cd x5mconfig

                                            git config --global core.fscache false
                                            git config --global core.quotepath false
                                            git config --global core.longpaths true
                                            """

                                    }
                                }
                            }
                        }
                    }


                    stage('链接本地资源'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                            }
                        }
                        steps{
                            script {
                                env.ErrInfo='链接本地资源失败'
                                def workspace
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROIDWORKSPACE
                                } else if ("${PLATFORM}" == "ios") {
                                    workspace = env.IOSWORKSPACE
                                } else if ("${PLATFORM}" == "harmony"){
                                    workspace = env.HARMONYWORKSPACE
                                }

                                bat script: """
                                mklink /J ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\\Assets\\ab_resources\\texture ${workspace}\\x5_mobile\\mr\\art_release\\art_src\\texture
                                """
                                // 将文件存入本地
                                writeFile encoding: 'GB2312', file: 'x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/resources.txt', text: "${texturePaths}"
                            }
                        }
                    }
                    stage('调用打包命令'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='调用unity打包失败'
                                def workspace
                                String platform
                                def packageTool = "unity"
                                if ("${PLATFORM}" == "android"){
                                    platform = "Android"
                                    workspace = env.ANDROIDWORKSPACE
                                }else if ("${PLATFORM}" == "ios"){
                                    platform = "iOS"
                                    workspace = env.IOSWORKSPACE
                                }else if ("${PLATFORM}" == "harmony"){
                                    platform = "OpenHarmony"
                                    packageTool = "tuanjie"
                                    workspace = env.HARMONYWORKSPACE
                                }
                                def jenkins_log = "${workspace}\\jenkins_log\\${env.BUILD_NUMBER}"
                                def logExists = fileExists "$jenkins_log"
                                if(!logExists)
                                {
                                    bat label:"日志文件夹",script:"mkdir jenkins_log\\${env.BUILD_NUMBER}"
                                }
                                def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat(script: "${packageTool} -quit -batchmode -logFile ${build_pack_log} -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" -executeMethod H3DBuildTools.BuildAB -buildTarget ${platform}  outpath=${workspace}\\x5_mobile\\mr\\art_release\\cs\\${PLATFORM}\\assetbundles")
                            }
                        }
                    }

                    //提交version_control
                    //ios 和 android 生成的csv一样，只用上传一个就可以
                    stage('提交csv到新git'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                            }
                        }
                        steps {
                            script{
                                echo "测试线不提交"
                                // env.ErrInfo='提交csv到新git失败'
                                // if("${PLATFORM}"=="android"){
                                //     dir("../x5mconfig"){
                                //         bat label: '拷贝文件', script:"""
                                //         fc "${workspace}\\x5_mobile\\mr\\art_release\\cs\\config\\card\\card_decorate_wh.csv" "config\\card\\card_decorate_wh.csv"
                                //         if errorlevel==1 (
                                //             echo diff
                                //             del /f /s /q "config\\card"
                                //             echo d y| xcopy "${workspace}\\x5_mobile\\mr\\art_release\\cs\\config\\card\\card_decorate_wh.csv" "config\\card" /e /y /r
                                //             git add -A
                                //             git commit -m "card_decorate_wh.csv"
                                //             git pull
                                //             git push
                                //         )
                                //         """
                                //      }

                                //  }

                            }
                        }
                    }


                    stage('提交p4'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='提交p4失败'
                                    def workspace
                                    def submitCsv
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                        env.SUBMITCSV = true
                                    }else if ("${PLATFORM}" == "ios"){
                                        workspace = env.IOSWORKSPACE
                                        env.SUBMITCSV = false
                                    }else if ("${PLATFORM}" == "harmony"){
                                        workspace = env.HARMONYWORKSPACE
                                        env.SUBMITCSV = false
                                    }
                                    def submitP4Desc = "uploader:${env.SEND_MESSAGE_USER_INFO}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitP4Desc}"
                                    // def out = bat(returnStdout: true, script: "python setup.py submit_files_to_p4 --root=${workspace} --platform=${PLATFORM} --tp=texture --changelist=${changeList} --desc=\"${submitP4Desc}\" --workspace=jenkins-art-cdn-package-${PLATFORM}")

                                    // println(out)
                                    // String[] outArras=out.split("\n")
                                    // if (outArras.size() > 4){
                                    //     println("p4workspace下没有需要提交的内容！")
                                    // }else{
                                    //     if ("${PLATFORM}" == "android"){
                                    //         env.ANDROID_CHANGELIST = outArras[2]
                                    //     }else if ("${PLATFORM}" == "ios"){
                                    //         env.IOS_CHANGELIST = outArras[2]
                                    //     }else if ("${PLATFORM}" == "harmony"){
                                    //         env.HARMONY_CHANGELIST = outArras[2]
                                    //     }
                                    // }
                                }
                            }
                        }
                    }
                    stage('持久化配置文件'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/config'){
                                echo "持久化配置文件"
                                // script{
                                //     env.ErrInfo='持久化配置文件失败'
                                //     bat """
                                //     git config --global user.email "<EMAIL>"
                                //     git config --global user.name "dgm_developer"
                                //     git add config.ini
                                //     git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                                //     git push origin texture-${PLATFORM}
                                //     """
                                // }
                            }
                        }
                    }
                    stage('上传打包结果'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='上传打包结果失败'
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROIDWORKSPACE
                                } else if ("${PLATFORM}" == "ios"){
                                    workspace = env.IOSWORKSPACE
                                } else if ("${PLATFORM}" == "harmony") {
                                    workspace = env.HARMONYWORKSPACE
                                }
                                def out
                                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                    out = bat(returnStdout: true, script: "python setup.py get_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}").split('\n')
                                }
                                def artifactsPath="x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/"
                                dir("${artifactsPath}"){
                                    def artifactName = out[2]
                                    archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                }
                                // 上传resources.txt
                                dir("x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool") {
                                    archiveArtifacts artifacts: "resources.txt", followSymlinks: false
                                    println("${env.BUILD_URL}" + "artifact/resources.txt")
                                }
                                // 环境变量赋值
                                def artifactUrl = env.BUILD_URL + 'artifact/' + out[2]
                                def resources_url = env.BUILD_URL + "artifact/resources.txt"
                                echo "upload_url: ${artifactUrl}"
                                if ("${PLATFORM}" == "android"){
                                    env.ANDROIDPACKAGERESULTURL = artifactUrl.trim()
                                    env.ANDROID_RESOURCES = resources_url
                                    }
                                else if ("${PLATFORM}" == "ios"){
                                    env.IOSPACKAGERESULTURL = artifactUrl.trim()
                                    env.IOS_RESOURCES = resources_url
                                }
                                else if ("${PLATFORM}" == "harmony") {
                                    env.HARMONYPACKAGERESULTURL = artifactUrl.trim()
                                    env.HARMONY_RESOURCES = resources_url
                                }
                            }
                        }
                    }
                    stage('判断是否单端失败'){
                        steps{
                            script{
                                if ("${PLATFORM}" == "android"){
                                    buildResultAndroid = true
                                }
                                else if ("${PLATFORM}" == "ios"){
                                    buildResultios = true
                                }
                                else if ("${PLATFORM}" == "harmony"){
                                    buildResultHarmony = true
                                }
                            }
                        }
                    }
                }
                post{
                    always{
                        //上传日志
                        script {
                            def build_log_android = "jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log"
                            def buildLogAndroidExists = fileExists "$build_log_android"
                            env.LOGBACKINFO = ""
                            if(buildLogAndroidExists){
                                archiveArtifacts artifacts: "$build_log_android", followSymlinks: false
                                env.LOGBACKINFO += "[安卓 unity日志](${env.BUILD_URL}artifact/jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log)\n"
                            }

                            def build_log_ios = "jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log"
                            def buildLogIosExists = fileExists "$build_log_ios"
                            if(buildLogIosExists){
                                archiveArtifacts artifacts: "$build_log_ios", followSymlinks: false
                                env.LOGBACKINFO += "[ios unity日志](${env.BUILD_URL}artifact/jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log)\n"
                            }

                            def build_log_harmony = "jenkins_log/${env.BUILD_NUMBER}/harmony_${env.BUILD_NUMBER}.log"
                            def buildLogHarmonyExists = fileExists "$build_log_harmony"
                            if(buildLogHarmonyExists){
                                archiveArtifacts artifacts: "$build_log_harmony", followSymlinks: false
                                env.LOGBACKINFO += "[harmony unity日志](${env.BUILD_URL}artifact/jenkins_log/${env.BUILD_NUMBER}/harmony_${env.BUILD_NUMBER}.log)\n"
                            }

                            def package_list = "package_list/package_list_${env.BUILD_NUMBER}.log"
                            def packageListExists = fileExists "$package_list"
                            if(packageListExists){
                                archiveArtifacts artifacts: "$package_list", followSymlinks: false
                            }
                            //定义信息模板
                            env.ANDROIDBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.ANDROID_CHANGELIST}
[打包资源](${env.ANDROID_RESOURCES})
"""
                            env.IOSBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.IOS_CHANGELIST}
[打包资源](${env.IOS_RESOURCES})
"""
                            env.HARMONYBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.HARMONY_CHANGELIST}
[打包资源](${env.HARMONY_RESOURCES})
"""
                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            node('master') {
                script {
                    // if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                    //     if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                    //         messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    //     }
                    // }
                    // if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                    //     messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    // }
                    def sendUserString = messageSendUserList.join(",")
                    // echo "执行状态码： ${buildResultCode}"
                    def msg = """**结果**: texture资源打包失败"""
                    if (buildResultAndroid || buildResultios || buildResultHarmony) {
                        msg += """
<font color = "#dc143c">**单端成功**:"""
                        if (buildResultAndroid) {
                            msg += " android"
                        }
                        if (buildResultios) {
                            msg += " ios"
                        }
                        if (buildResultHarmony) {
                            msg += " harmony"
                        }
                        msg += "</font>"
                        if (buildResultAndroid) {
                            msg += "**安卓**：${env.ANDROIDBACKINFO}"
                        }
                        if (buildResultios) {
                            msg += "**ios**：${env.IOSBACKINFO}"
                        }
                        if (buildResultHarmony) {
                            msg += "**harmony**：${env.HARMONYBACKINFO}"
                        }
                    }
                    msg += """
<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>
${env.LOGBACKINFO}
"""
                    echo "${msg}"
                    // workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb',content: msg
                    if (sendUserString != "") {
                        emailext to: "${sendUserString}", subject: "jenkins-CDN-图片鸿蒙测试打包失败 #${env.BUILD_NUMBER}", body: msg
                    }
                }
            }
        }
        unstable{
            node('master') {
                script{
                    // if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                    //     if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                    //         messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    //     }
                    // }
                    // if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                    //     messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    // }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true" || isHarmonyPackage == "true"){
                        def msg=""""""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}"""
                        }
                        if (isHarmonyPackage == "true")
                        {
                            msg+="""**harmony**：${env.HARMONYBACKINFO}"""
                        }
                        msg += """${env.LOGBACKINFO}"""
                        // workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb',content: msg
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: "jenkins-CDN-图片打包部分失败 #${env.BUILD_NUMBER}", body: msg
                        }
                    }
                }
            }
        }
        success {
            node('master') {
                script{
                    // if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                    //     if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                    //         messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    //     }
                    // }
                    // if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                    //     messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    // }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true" || isHarmonyPackage == "true"){
                        def msg="""**[资源列表](${env.BUILD_URL}artifact/package_list/package_list_${env.BUILD_NUMBER}.log)**\n"""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}"""
                        }
                        if (isHarmonyPackage == "true")
                        {
                            msg+="""**harmony**：${env.HARMONYBACKINFO}"""
                        }
                        msg += "**原始资源 changelist**：${changeList}\n"
                        // def max_changelist = getMaxChangelist()
                        // msg += "**提测号**： ${max_changelist}\n"
                        // workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb', content: msg
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: "jenkins-CDN-图片鸿蒙测试打包成功 #${env.BUILD_NUMBER}", body: msg
                        }
                    }
                }
            }
        }
    }
}

