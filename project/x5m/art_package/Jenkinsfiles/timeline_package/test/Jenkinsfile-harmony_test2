@Library('h3d_libs') _
import groovy.json.JsonSlurper
import groovy.json.JsonOutput

def changeList = ""
def isAndroidPackage = "true"
def isIosPackage = "true"
def isHarmonyPackage = "true"
String[] view_list
def timeRetryAndroid = 0
def timeRetryIos = 0
def timeRetryHarmony = 0
def currentRunSteps = 0
def messageSendUserList = ["<EMAIL>"]
def finalP4ViewAndroid = ""
def finalP4ViewIos = ""
def finalP4ViewHarmony = ""
/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
def iosBuildResult = false
def androidBuildResult = false
def harmonyBuildResult = false


def getP4View(platform, p4_view) {
    // 生产环境映射
    def tpl = '''
//x5_mobile/mr/art_release/art_src/timeline/common/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/assets/resources/art/timeline/common/...
//x5_mobile/mr/art_release/art_src/role/cloth_show/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/assets/resources/art/role/cloth_show/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/mp4/timeline/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/mp4/timeline/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/timeline/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/timeline/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/role/actions/actions_anim/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/timeline/actions_anim/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/role/actions/actions_anim_expression/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/timeline/actions_anim_expression/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/role/link/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/timeline/link/...
    '''
    for (String v in p4_view) {
        tpl += v + '\n'
    }
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([platform: platform, p4Client: '${P4_CLIENT}']).toString()
}


def IsPackage(platform,isAndroidPackage,isIosPackage,isHarmonyPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    } else if (platform == "ios"){
        return isIosPackage == "true"
    } else if (platform == "harmony"){
        return isHarmonyPackage == "true"
    }
    return false
}


def getArtTrunkFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/release"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    url: '******************************:dgm/arttrunk.git']]]
}


def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}

// def parse_pipeline_data(status, extends_data) {
//     def timestamp = (new Date().time / 1000).intValue().toString()
//     def previous_successful_build_duration = (currentBuild.previousSuccessfulBuild.duration / 1000).intValue().toString()
//     def pipeline_status_data = env.PipelineStatus.replaceAll("#status#", status).replaceAll("-1654790400", timestamp).replaceAll("-1655204400", previous_successful_build_duration)
//     def json_object = new JsonSlurper()
//     def pipeline_json_data = json_object.parseText(pipeline_status_data)
//     pipeline_json_data["extends"] = extends_data
//     return JsonOutput.toJson(pipeline_json_data)
// }
//
// def send_http_request(status, extends_data) {
//     def msg = parse_pipeline_data(status, extends_data)
//     println(msg)
//     try {
//         httpRequest acceptType: 'TEXT_PLAIN', consoleLogResponseBody: true, httpMode: 'POST', contentType: 'APPLICATION_JSON', ignoreSslErrors: true, requestBody: msg, timeout: 5, url: 'http://art.h3d.com.cn:80/resources/pipeline', validResponseCodes: '100:599'
//     } catch(Exception ex) {
//         println(ex)
//     }
// }


def mergeView(view1, view2) {
    def v1 =  view1.split(";").toList()
    def v2 =  view2.split(";").toList()
    def view = v1 + v2
    def unique_view = view.unique()
    println("合并后的完整view为：${unique_view}")
    return unique_view
}

// 判断字符串是否为空
def isEmpty(str) {
    return str == null || str.trim() == ""
}

// 更新resources文件
def getResourcesContent(views) {
    def regex = /(?s).*(\/assets\/resources\/art\/timeline\/[\w\/]+)\/?\d*\/?.*/
    def filteredList = views.collect { tuple ->
        def path = tuple =~ regex
        if (path) {
            path[0][1].substring(1).replaceAll(/\/$/, "") // 取第一个捕获组中的路径部分
        }
    }.findAll { path ->
        path != null && !path.contains("npc")
    }
    def output = filteredList.join(",")
    println("ResourcesContent：${output}")
    return output
}

pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 1800, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr)
    }
    parameters {
        text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
        booleanParam(name: 'force', defaultValue: true, description: '是否强更P4')
        text(name: 'timeline_ids', defaultValue: '', description: '需要强打的TimelineID，多个TimelineID用逗号分割, 例如: qinglvtao/1048626, qinglvtao/1048627')
        choice name: 'PLATFORM', choices: [
            "android",
            "ios",
            "harmony"
        ]
    }
    environment {
        // PYFRAME_PYTHON = "conda run -n pyf368 python"
        PYFRAME_PYTHON = "python"
    }

    // triggers {
    //     cron('H/30 0-3,5-23 * * *')
    // }

    stages {
        stage('美术资源打包'){
            agent {
                node{
                    label "${PLATFORM}-art-cdn-package-newp4-test"
                }
            }
            stages{
                // TODO 临时逻辑, 检查参数, 用于强打资源
                stage('检查参数'){
                    steps{
                        script{
                            env.ErrInfo='检查参数失败'
                            if (params.timeline_ids != ''){
                                def timeline_ids = params.timeline_ids.split(/,|\n/)
                                def timeline_ids_str = timeline_ids.join(",")
                                env.timeline_ids = timeline_ids_str
                            }
                        }
                    }
                }
                stage('配置初始化'){
                    steps {
                        dir('pyframe-pipeline/project/x5m/art_package/config'){
                            script{
                                env.ErrInfo='配置初始化失败'
                                currentRunSteps = 1
                                getBuildUserInfo()
                                if (!fileExists(".git/config")) {
                                    bat """
                                    del /s /f /q .
                                    """
                                }
                                // 判断配置 config.ini 是否存在，存在, 不处理, 否则判断 config.ini.example 是否存在, 存在，改名为config.ini
                                def configFile = "config.ini"
                                def configExampleFile = "config.ini.example"
                                def configExists = fileExists "$configFile"
                                def exampleExists = fileExists "$configExampleFile"
                                println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
                                if (!configExists) {
                                    // 不存在
                                    bat """
                                    git clone -b timeline-${PLATFORM}-unity2022 http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                                    """
                                }
                                bat """
                                git clean -xdf
                                git reset --hard HEAD
                                git pull origin timeline-${PLATFORM}-unity2022
                                """
                            }
                        }
                    }
                }
                stage("获取timeline映射配置文件") {
                    steps {
                        dir("pyframe-pipeline/project/x5m/art_package/scripts") {
                            script {
                                env.ErrInfo = "获取timeline映射配置文件失败"
                                bat(returnStdout: false, script: "${PYFRAME_PYTHON} setup.py sync_timeline_config --workspace=timeline_config_${PLATFORM}_**************")
                            }
                        }
                    }
                }
                stage('检查NPC资源-1'){
                    steps {
                        dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                            script{
                                env.ErrInfo='检查NPC资源失败'
                                def npc_config = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py get_npc_changelist --platform=${PLATFORM}").split('\n')
                                def npc_changelist = npc_config[2]
                                def out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py get_updates --tp=npc --first_changelist=${npc_changelist}").split('\n')
                                println(out[2])
                                def jsonSlurper = new JsonSlurper()
                                def result_map = jsonSlurper.parseText(out[2])

                                if ("${PLATFORM}" == "android"){
                                    env.ANDROIDWORKSPACE = env.WORKSPACE
                                    env.isNPCAndroidPackage = "false"
                                } else if ("${PLATFORM}" == "ios"){
                                    env.IOSWORKSPACE = env.WORKSPACE
                                    env.isNPCIosPackage = "false"
                                } else if ("${PLATFORM}" == "harmony"){
                                    env.HARMONYWORKSPACE = env.WORKSPACE
                                    env.isNPCHarmonyPackage = "false"
                                }

                                if (result_map.update == 'true'){
                                    env.NPC_P4_VIEW = result_map.p4_view
                                }
                            }
                        }
                    }
                }
                stage('检查NPC资源-2'){
                    steps {
                        dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                            script{
                                def npc_out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py fill_npc_p4_view --workspace=timeline_config_${PLATFORM}_************** --view_env=NPC_P4_VIEW").split("\n")[2]
                                println("npc_out:${npc_out}")
                                def empty = isEmpty(npc_out)
                                println("empty:${empty}")
                                if (!empty) {
                                    if ("${PLATFORM}" == "android"){
                                        env.isNPCAndroidPackage = "true"
                                    } else if ("${PLATFORM}" == "ios") {
                                        env.isNPCIosPackage = "true"
                                    } else if ("${PLATFORM}" == "harmony") {
                                        env.isNPCHarmonyPackage = "true"
                                    }
                                }
                            }
                        }
                    }
                }
                stage('检查timeline资源'){
                    steps {
                        dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                            script{
                                env.ErrInfo='检查timeline资源失败'
                                currentRunSteps = 2
                                def out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py get_updates --tp timeline --first_changelist=${params.first_changelist}").split('\n')
                                println("检查是否需要打包返回的out值:${out}")
                                def jsonSlurper = new JsonSlurper()
                                def result_map = jsonSlurper.parseText(out[2])
                                env.PipelineStatus = out[3]
                                println(env.PipelineStatus)
                                if ("${PLATFORM}" == "android"){
                                    env.ANDROIDWORKSPACE = env.WORKSPACE
                                    env.isTMAndroidPackage = result_map.update
                                    println("isTMAndroidPackage:${isTMAndroidPackage}")
                                    env.TM_P4_VIEW_ANDROID = result_map.p4_view
                                    env.SEND_MESSAGE_USER_INFO_ANDROID = result_map.send_user
                                }else if ("${PLATFORM}" == "ios") {
                                    env.IOSWORKSPACE = env.WORKSPACE
                                    env.isTMIosPackage = result_map.update
                                    println("isTMIosPackage:${isTMIosPackage}")
                                    env.TM_P4_VIEW_IOS = result_map.p4_view
                                }else if ("${PLATFORM}" == "harmony") {
                                    env.HARMONYWORKSPACE = env.WORKSPACE
                                    env.isTMHarmonyPackage = result_map.update
                                    println("isTMHarmonyPackage:${isTMHarmonyPackage}")
                                    env.TM_P4_VIEW_HARMONY = result_map.p4_view
                                }
                                if (result_map.update == 'true'){
                                    env.PACKAGELIST = result_map.package_list.replace(",", "\n")
                                    changeList = result_map.changelist
                                    env.SEND_MESSAGE_USER_INFO = env.SEND_MESSAGE_USER_INFO_ANDROID + "," + env.SEND_MESSAGE_USER_INFO_IOS
                                }
                            }
                        }
                    }
                }
                stage('检查是否需要打包'){
                    steps {
                        dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                            script{
                                env.ErrInfo='检查是否需要打包失败'
                                currentRunSteps = 2
                                println(env.PipelineStatus)
                                if ("${PLATFORM}" == "android"){
                                    if (isNPCAndroidPackage == "true" || isTMAndroidPackage == "true") {
                                        isAndroidPackage = "true"
                                    }else{
                                        isAndroidPackage = "false"
                                    }
                                }else if ("${PLATFORM}" == "ios"){
                                    if (isNPCIosPackage == "true" || isTMIosPackage == "true") {
                                        isIosPackage = "true"
                                    }else{
                                        isIosPackage = "false"
                                    }
                                }else if ("${PLATFORM}" == "harmony"){
                                    if (isNPCHarmonyPackage == "true" || isTMHarmonyPackage == "true") {
                                        isHarmonyPackage = "true"
                                    }else{
                                        isHarmonyPackage = "false"
                                    }
                                }
                                if(isAndroidPackage == 'false' && isIosPackage == 'false' && isHarmonyPackage == 'false'){
                                    println("不需要打包")
                                    currentBuild.result = 'ABORTED'
                                }else{
                                    println("需要打包")
                                }
                            }
                        }
                    }
                }
                stage("检查p4映射是否存在依赖") {
                    when {
                        expression {
                            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                        }
                    }
                    steps {
                        dir("pyframe-pipeline/project/x5m/art_package/scripts") {
                            script {
                                env.ErrInfo = "检查p4映射是否存在依赖失败"
                                def resourcesContent = ""
                                if ("${PLATFORM}" == "android") {
                                    def out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py fill_p4_view --workspace=timeline_config_${PLATFORM}_2022 --view_env=TM_P4_VIEW_ANDROID").split("\n")[2]
                                    println("out:${out}")
                                    def npc_out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py fill_npc_p4_view --workspace=timeline_config_${PLATFORM}_2022 --view_env=TM_P4_VIEW_ANDROID").split("\n")[2]
                                    print("npc_out:${npc_out}")
                                    finalP4ViewAndroid = mergeView(out, npc_out)
                                    resourcesContent = getResourcesContent(finalP4ViewAndroid)
                                } else if ("${PLATFORM}" == "ios"){
                                    def out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py fill_p4_view --workspace=timeline_config_${PLATFORM}_2022 --view_env=TM_P4_VIEW_IOS").split("\n")[2]
                                    println("out:${out}")
                                    def npc_out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py fill_npc_p4_view --workspace=timeline_config_${PLATFORM}_2022 --view_env=TM_P4_VIEW_IOS").split("\n")[2]
                                    print("npc_out:${npc_out}")
                                    finalP4ViewIos = mergeView(out, npc_out)
                                    resourcesContent = getResourcesContent(finalP4ViewIos)
                                } else if ("${PLATFORM}" == "harmony") {
                                    def out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py fill_p4_view --workspace=timeline_config_${PLATFORM}_2022 --view_env=TM_P4_VIEW_HARMONY").split("\n")[2]
                                    println("out:${out}")
                                    def npc_out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py fill_npc_p4_view --workspace=timeline_config_${PLATFORM}_2022 --view_env=TM_P4_VIEW_HARMONY").split("\n")[2]
                                    print("npc_out:${npc_out}")
                                    finalP4ViewHarmony = mergeView(out, npc_out)
                                    resourcesContent = getResourcesContent(finalP4ViewHarmony)
                                }
                                if (!isEmpty(resourcesContent)) {
                                    writeFile file: "${WORKSPACE}/x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/resources.txt", text: resourcesContent
                                }
                            }
                        }
                    }
                }
                stage('清理资源'){
                    when {
                        expression {
                            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                        }
                    }
                    steps {
                        dir("x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/timeline"){
                            deleteDir()
                        }
                    }
                }
                stage('获取p4资源'){
                    when {
                        expression {
                            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                        }
                    }
                    steps{
                        retry(2){
                            script{
                                env.ErrInfo='获取p4资源失败'
                                def view = ""
                                if ("${PLATFORM}" == "android"){
                                    timeRetryAndroid ++
                                    echo "安卓执行次数：${timeRetryAndroid}"
                                    view = getP4View("${PLATFORM}", finalP4ViewAndroid)
                                } else if ("${PLATFORM}" == "ios"){
                                    timeRetryIos ++
                                    echo "ios执行次数：${timeRetryIos}"
                                    view = getP4View("${PLATFORM}", finalP4ViewIos)
                                } else if ("${PLATFORM}" == "harmony"){
                                    timeRetryHarmony ++
                                    echo "harmony执行次数：${timeRetryHarmony}"
                                    view = getP4View("${PLATFORM}", finalP4ViewHarmony)
                                }
                                currentRunSteps = 4
                                bat """git config --global core.longpaths true"""
                                getArtTrunkFromGitlab()

                                // 拉取资源目录
                                println("当前路径是: " + pwd())
                                def python_view = view.replaceAll("\n", "#")
                                def p4_workspace = "jenkins-timeline-cdnart-${PLATFORM}-package-**************"
                                writeFile encoding: 'GB2312', file: "p4_view.txt", text: "${python_view}"
                                try {
                                    timeout(time: 15, unit: 'MINUTES') {
                                        bat(returnStdout: false, script: "${PYFRAME_PYTHON} pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${p4_workspace}  --force_sync=${params.force}")
                                    }
                                } catch (err) {
                                    // def sendUserString = messageSendUserList.join(",")
                                    // if (sendUserString != "") {
                                    //     emailext to: "${sendUserString}", subject: "jenkins-TimeLine-CDN-2022打包 #${env.BUILD_NUMBER}", body: "获取p4资源卡住超时，重试中"
                                    // }
                                    error("获取p4资源卡住超时，重试中")
                                }
                            }
                        }
                    }
                }
                stage("复制timeline映射配置文件") {
                    when {
                        expression {
                            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                        }
                    }
                    steps {
                        dir("pyframe-pipeline/project/x5m/art_package/scripts") {
                            script {
                                env.ErrInfo = "复制timeline映射配置文件失败"
                                bat(returnStdout: false, script: "${PYFRAME_PYTHON} setup.py copy_timeline_config --workspace=timeline_config_${PLATFORM}_**************")
                            }
                        }
                    }
                }
                stage('调用打包命令'){
                    when {
                        expression {
                            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                        }
                    }
                    steps{
                        script{
                            env.ErrInfo='调用unity打包失败'
                            currentRunSteps = 6
                            def workspace
                            String platform
                            def packageTool = "unity"
                            if ("${PLATFORM}" == "android"){
                                platform = "Android"
                                workspace = env.ANDROIDWORKSPACE
                            } else if ("${PLATFORM}" == "ios"){
                                platform = "iOS"
                                workspace = env.IOSWORKSPACE
                            } else if ("${PLATFORM}" == "harmony") {
                                platform = "OpenHarmony"
                                packageTool = "tuanjie"
                                workspace = env.HARMONYWORKSPACE
                            }
                            def jenkins_log = "${workspace}\\jenkins_log\\${env.BUILD_NUMBER}"
                            def logExists = fileExists "$jenkins_log"
                            if(!logExists)
                            {
                                bat label:"日志文件夹",script:"mkdir jenkins_log\\${env.BUILD_NUMBER}"
                            }
                            def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                            bat(script: "${packageTool} -quit -batchmode -logFile ${build_pack_log} -projectPath \"${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" -executeMethod H3DBuildTools.BuildTimeline -buildTarget ${platform} out_path=${workspace}\\x5_mobile\\mr\\art_release\\cs\\${PLATFORM}\\assetbundles")
                        }
                    }
                }
                stage('计算打包结果'){
                    when {
                        expression {
                            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                        }
                    }
                    steps{
                        dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                            script{
                                env.ErrInfo='计算打包结果失败'
                                currentRunSteps = 7
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROIDWORKSPACE
                                } else if ("${PLATFORM}" == "ios"){
                                    workspace = env.IOSWORKSPACE
                                } else if ("${PLATFORM}" == "harmony") {
                                    workspace = env.HARMONYWORKSPACE
                                }
                                out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py print_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client ").split('\n')
                                print(out[2])
                                def count_result = readJSON text: out[2]

                                if ("${PLATFORM}" == "android"){
                                    env.ANDROIDTOTALCOUNT = count_result.total_count
                                    print(env.ANDROIDTOTALCOUNT)
                                    env.ANDROIDSUCCCOUNT = count_result.succ_count
                                    print(env.ANDROIDSUCCCOUNT)
                                    env.ANDROIDFAILEDCOUNT = count_result.failed_count
                                    print(env.ANDROIDFAILEDCOUNT)
                                    env.ANDROIDERRORCOUNT = count_result.error_count
                                    print(env.ANDROIDERRORCOUNT)
                                    env.ANDROID_SUCCESS_IDS = count_result.success_ids
                                    print(env.ANDROID_SUCCESS_IDS)
                                    env.ANDROID_FAIL_IDS = count_result.fail_ids
                                    print(env.ANDROID_FAIL_IDS)
                                    env.ANDROID_UPLOADP4 = count_result.upload_p4

                                    if (env.ANDROIDERRORCOUNT != "0" || env.ANDROIDFAILEDCOUNT != "0" || env.ANDROIDSUCCCOUNT == "0"){
                                        currentBuild.result = 'UNSTABLE'
                                    }
                                } else if ("${PLATFORM}" == "ios"){
                                    env.IOSTOTALCOUNT = count_result.total_count
                                    print(env.IOSTOTALCOUNT)
                                    env.IOSSUCCCOUNT = count_result.succ_count
                                    print(env.IOSSUCCCOUNT)
                                    env.IOSFAILEDCOUNT = count_result.failed_count
                                    print(env.IOSFAILEDCOUNT)
                                    env.IOSERRORCOUNT = count_result.error_count
                                    print(env.IOSERRORCOUNT)
                                    env.IOS_SUCCESS_IDS = count_result.success_ids
                                    print(env.IOS_SUCCESS_IDS)
                                    env.IOS_FAIL_IDS = count_result.fail_ids
                                    print(env.IOS_FAIL_IDS)
                                    env.IOS_UPLOADP4 = count_result.upload_p4

                                    if (env.IOSERRORCOUNT != "0" || env.IOSFAILEDCOUNT != "0" || env.IOSSUCCCOUNT == "0"){
                                        currentBuild.result = 'UNSTABLE'
                                    }
                                } else if ("${PLATFORM}" == "harmony") {
                                    env.HARMONYTOTALCOUNT = count_result.total_count
                                    print(env.HARMONYTOTALCOUNT)
                                    env.HARMONYSUCCCOUNT = count_result.succ_count
                                    print(env.HARMONYSUCCCOUNT)
                                    env.HARMONYFAILEDCOUNT = count_result.failed_count
                                    print(env.HARMONYFAILEDCOUNT)
                                    env.HARMONYERRORCOUNT = count_result.error_count
                                    print(env.HARMONYERRORCOUNT)
                                    env.HARMONY_SUCCESS_IDS = count_result.success_ids
                                    print(env.HARMONY_SUCCESS_IDS)
                                    env.HARMONY_FAIL_IDS = count_result.fail_ids
                                    print(env.HARMONY_FAIL_IDS)
                                    env.HARMONY_UPLOADP4 = count_result.upload_p4

                                    if (env.HARMONYERRORCOUNT != "0" || env.HARMONYFAILEDCOUNT != "0" || env.HARMONYSUCCCOUNT == "0"){
                                        currentBuild.result = 'UNSTABLE'
                                    }
                                }
                            }
                        }
                    }
                }
                stage('提交p4'){
                    when {
                        expression {
                            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage) && (env.ANDROID_UPLOADP4 == 'true' || env.IOS_UPLOADP4 == 'true' || env.HARMONY_UPLOADP4 == 'true')
                        }
                    }
                    steps{
                        dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                            script{
                                echo "测试线不提交p4"
                                // env.ErrInfo='提交p4失败'
                                // currentRunSteps = 8
                                // def workspace
                                // if ("${PLATFORM}" == "android"){
                                //     workspace = env.ANDROIDWORKSPACE
                                // }else{
                                //     workspace = env.IOSWORKSPACE
                                // }

                                // def submitP4Desc = "--Auto--|uploader:${env.SEND_MESSAGE_USER_INFO}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                // echo "${submitP4Desc}"
                                // // 提交视频相关
                                // if ("${PLATFORM}" == "android") {
                                //     bat "${PYFRAME_PYTHON} setup.py submit_video_files  --root=${workspace} --view_env=TM_P4_VIEW_ANDROID --desc=\"${submitP4Desc}\" --platform=${PLATFORM} --workspace=jenkins-timeline-cdnart-${PLATFORM}-package-**************"
                                // }
                                // else {
                                //     bat "${PYFRAME_PYTHON} setup.py submit_video_files  --root=${workspace} --view_env=TM_P4_VIEW_IOS --desc=\"${submitP4Desc}\" --platform=${PLATFORM} --workspace=jenkins-timeline-cdnart-${PLATFORM}-package-**************"
                                // }

                                // def out = bat(returnStdout: true,script: "${PYFRAME_PYTHON} setup.py submit_files_to_p4  --root=${workspace} --platform=${PLATFORM} --tp=timeline --changelist=${changeList} --desc=\"${submitP4Desc}\" --workspace=jenkins-timeline-cdnart-${PLATFORM}-package-**************")
                                // echo "${out}"
                                // String[] outArras=out.split("\n")
                                // if (outArras.size() > 4){
                                //     println("p4workspace下没有需要提交的内容！")
                                // }else{
                                //     if ("${PLATFORM}" == "android"){
                                //         env.ANDROID_CHANGELIST = outArras[2]
                                //     }else{
                                //         env.IOS_CHANGELIST = outArras[2]
                                //     }
                                // }

                            }
                        }
                    }
                }
                stage('持久化配置文件'){
                    when {
                        expression {
                            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                        }
                    }
                    steps{
                        dir('pyframe-pipeline/project/x5m/art_package/config'){
                            script{
                                echo "测试线不提交持久化文件"
                                // echo "${out}"
                                // env.ErrInfo='持久化配置文件失败'
                                // bat "${PYFRAME_PYTHON} ../scripts/setup.py update_timeline_persistent_config --changelist=${changeList}"
                                // bat """
                                // git config --global user.email "<EMAIL>"
                                // git config --global user.name "dgm_developer"
                                // git add config.ini
                                // git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                                // git push origin timeline-${PLATFORM}-unity2022
                                // """
                            }
                        }
                    }
                }
                stage('上传打包结果'){
                    when {
                        expression {
                            return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage,isHarmonyPackage)
                        }
                    }
                    steps{
                        script{
                            env.ErrInfo='上传打包结果失败'
                            currentRunSteps = 9
                            def workspace
                            String platform
                            if ("${PLATFORM}" == "android"){
                                workspace = env.ANDROIDWORKSPACE
                            } else if ("${PLATFORM}" == "ios"){
                                workspace = env.IOSWORKSPACE
                            } else if ("${PLATFORM}" == "harmony") {
                                workspace = env.HARMONYWORKSPACE
                            }
                            def out
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py get_package_result --path ${workspace}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}").split('\n')
                            }
                            def artifactsPath="x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/"
                            dir("x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool") {
                                archiveArtifacts artifacts: "resources.txt", followSymlinks: false
                                println("${env.BUILD_URL}" + "artifact/resources.txt")
                            }
                            dir("${artifactsPath}"){
                                def artifactName = out[2].trim()
                                archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                def artifactUrl = env.BUILD_URL + 'artifact/' + out[2].trim()
                                def resources_url = env.BUILD_URL + "artifact/resources.txt"
                                echo "upload_url: ${artifactUrl}"
                                if ("${PLATFORM}" == "android"){
                                    env.ANDROIDPACKAGERESULTURL = artifactUrl.trim()
                                    env.ANDROID_RESOURCES = resources_url
                                } else if ("${PLATFORM}" == "ios"){
                                    env.IOSPACKAGERESULTURL = artifactUrl.trim()
                                    env.IOS_RESOURCES = resources_url
                                } else if ("${PLATFORM}" == "harmony") {
                                    env.HARMONYPACKAGERESULTURL = artifactUrl.trim()
                                    env.HARMONY_RESOURCES = resources_url
                                }
                            }
                        }
                    }
                }
                stage('判断是否单端失败'){
                    steps{
                        script{
                            if ("${PLATFORM}" == "android"){
                                androidBuildResult = true
                            } else if ("${PLATFORM}" == "ios"){
                                iosBuildResult = true
                            } else if ("${PLATFORM}" == "harmony") {
                                harmonyBuildResult = true
                            }
                        }
                    }
                }
            }
            post{
                always{
                    //上传日志
                    script {
                        def build_log_android = "jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log"
                        def buildLogAndroidExists = fileExists "$build_log_android"
                        if(buildLogAndroidExists){
                            archiveArtifacts artifacts: "$build_log_android", followSymlinks: false
                        }
                        def build_log_ios = "jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log"
                        def buildLogIosExists = fileExists "$build_log_ios"
                        if(buildLogIosExists){
                            archiveArtifacts artifacts: "$build_log_ios", followSymlinks: false
                        }
                        def build_log_harmony = "jenkins_log/${env.BUILD_NUMBER}/harmony_${env.BUILD_NUMBER}.log"
                        def buildLogHarmonyExists = fileExists "$build_log_harmony"
                        if(buildLogHarmonyExists){
                            archiveArtifacts artifacts: "$build_log_harmony", followSymlinks: false
                        }
                        //定义信息模板
                        env.ANDROIDBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.ANDROID_CHANGELIST}
[打包统计](${env.ANDROIDPACKAGERESULTURL})
[打包资源](${env.ANDROID_RESOURCES})
**打包数量**: ${env.ANDROIDTOTALCOUNT}
**成功数量**: ${env.ANDROIDSUCCCOUNT}
**成功ID**: ${env.ANDROID_SUCCESS_IDS}
**失败ID**: ${env.ANDROID_FAIL_IDS}"""
                        env.IOSBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.IOS_CHANGELIST}
[打包统计](${env.IOSPACKAGERESULTURL})
[打包资源](${env.IOS_RESOURCES})
**打包数量**: ${env.IOSTOTALCOUNT}
**成功数量**: ${env.IOSSUCCCOUNT}
**成功ID**: ${env.IOS_SUCCESS_IDS}
**失败ID**: ${env.IOS_FAIL_IDS}"""
                        env.HARMONYBACKINFO = """
**原始资源 changelist**: ${changeList}
**ab包 changelist**: ${env.HARMONY_CHANGELIST}
[打包统计](${env.HARMONYPACKAGERESULTURL})
[打包资源](${env.HARMONY_RESOURCES})
**打包数量**: ${env.HARMONYTOTALCOUNT}
**成功数量**: ${env.HARMONYSUCCCOUNT}
**成功ID**: ${env.HARMONY_SUCCESS_IDS}
**失败ID**: ${env.HARMONY_FAIL_IDS}"""
                        if (currentBuild.result != 'SUCCESS'){
                            env.ANDROIDBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.ANDROIDFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.ANDROIDERRORCOUNT}</font>"""
                            env.IOSBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.IOSFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.IOSERRORCOUNT}</font>"""
                            env.HARMONYBACKINFO += """
<font color = "#dc143c">**失败数量**: ${env.HARMONYFAILEDCOUNT}</font>
<font color = "#dc143c">**检查出错数量**: ${env.HARMONYERRORCOUNT}</font>"""
                        }
                        else{
                            env.ANDROIDBACKINFO += """
**失败数量**: ${env.ANDROIDFAILEDCOUNT}
**检查出错数量**: ${env.ANDROIDERRORCOUNT}"""
                            env.IOSBACKINFO += """
**失败数量**: ${env.IOSFAILEDCOUNT}
**检查出错数量**: ${env.IOSERRORCOUNT}"""
                            env.HARMONYBACKINFO += """
**失败数量**: ${env.HARMONYFAILEDCOUNT}
**检查出错数量**: ${env.HARMONYERRORCOUNT}"""
                        }
                        env.LOGBACKINFO = """
[安卓 unity日志](${env.BUILD_URL}artifact/jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log)
[ios unity日志](${env.BUILD_URL}artifact/jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log)
[harmony tuanjie日志](${env.BUILD_URL}artifact/jenkins_log/${env.BUILD_NUMBER}/harmony_${env.BUILD_NUMBER}.log)"""
                    }
                }
            }
        }
    }
    post {
//        failure {
//            node('master') {
//                script {
//                    if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
//                        if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
//                            messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
//                        }
//                    }
//                    if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
//                        messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
//                    }
//                    def sendUserString = messageSendUserList.join(",")
//                    echo "执行状态码： ${buildResultCode}"
//                    def msg = """**结果**: TimeLine资源cdn打包失败"""

//                    if (buildResultCode == 1){
//                        msg += """
// <font color = "#dc143c">**单端成功**: 安卓(下次打包会自动把失败的一端打包)</font>
// ${env.ANDROIDBACKINFO}
// """
//                    }
//                    else if (buildResultCode == 2){
//                        msg += """
// <font color = "#dc143c">**单端成功**: ios(下次打包会自动把失败的一端打包)</font>
// ${env.IOSBACKINFO}
// """
//                    }
//                    msg += """
// <font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>
// ${env.LOGBACKINFO}
// """
//                    echo "${msg}"
//                    workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=95ef2da3-767e-45ab-831b-d233f4f44983',content: msg
//                    if (sendUserString != "") {
//                        emailext to: "${sendUserString}", subject: "jenkins-TimeLine-CDN-2022打包失败 #${env.BUILD_NUMBER}", body: msg
//                    }
//                }
//            }
//        }
        unstable{
            node('master') {
                script{
                    // if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                    //     if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                    //         messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    //     }
                    // }
                    // if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                    //     messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    // }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true" || isHarmonyPackage == "true"){
                        def msg=""""""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}\n"""
                        }
                        if (isHarmonyPackage == "true")
                        {
                            msg+="""**鸿蒙**：${env.HARMONYBACKINFO}\n"""
                        }
                        msg += """${env.LOGBACKINFO}"""
                        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033', content: msg
                        // workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=95ef2da3-767e-45ab-831b-d233f4f44983',content: msg
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: "jenkins-TimeLine鸿蒙打包测试线部分失败 #${env.BUILD_NUMBER}", body: msg
                        }
                    }
                }
            }
        }
        success {
            node('master') {
                script{
                    // if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                    //     if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                    //         messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    //     }
                    // }
                    // if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                    //     messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                    // }
                    def sendUserString = messageSendUserList.join(",")
                    if (isAndroidPackage == "true" || isIosPackage == "true" || isHarmonyPackage == "true"){
                        def msg=""""""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """**安卓**：${env.ANDROIDBACKINFO}\n"""
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：${env.IOSBACKINFO}\n"""
                        }
                        if (isHarmonyPackage == "true")
                        {
                            msg+="""**鸿蒙**：${env.HARMONYBACKINFO}\n"""
                        }

                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: "jenkins-TimeLine鸿蒙打包测试线成功 #${env.BUILD_NUMBER}", body: msg
                        }
                    }
                }
            }
        }
    }
}
