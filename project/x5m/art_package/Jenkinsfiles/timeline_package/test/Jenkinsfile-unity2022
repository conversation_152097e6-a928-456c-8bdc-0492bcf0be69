@Library('h3d_libs') _
def timeRetry = 0
def currentRunSteps = 0
def messageSendUserList = ["<EMAIL>", ]
/* 执行状态码：
0：安卓、ios都执行失败；
1：安卓成功；
2：ios成功；
3：安卓、ios都执行成功 */
def buildResultCode = 0


def getP4View(platform, p4_view) {
    // 生产环境映射
    def tpl = '''
//x5_mobile/mr/art_release/art_src/timeline/common/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/assets/resources/art/timeline/common/...
//x5_mobile/mr/art_release/art_src/role/cloth_show/... //${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/assets/resources/art/role/cloth_show/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/mp4/timeline/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/mp4/timeline/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/timeline/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/timeline/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/role/actions/actions_anim/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/timeline/actions_anim/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/role/actions/actions_anim_expression/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/timeline/actions_anim_expression/...
//x5m/res/cdn/cooked/${platform}/assetbundles/art/role/link/... //${p4Client}/x5_mobile/mr/art_release/cs/${platform}/assetbundles/art/timeline/link/...
    '''
    for (String v in p4_view) {
        tpl += v + '\n'
    }
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([platform: platform, p4Client: '${P4_CLIENT}']).toString()
}

def getArtTrunkFromGitlab(){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    // branches: [[name: "*/Unity_2022"]],
    branches: [[name: "*/release"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5_mobile']],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm_reporter_package_sshpk',
    url: '******************************:dgm/arttrunk.git']]]
}


def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}
def mergeView(view1, view2) {
    def v1 =  view1.split(";").toList()
    def v2 =  view2.split(";").toList()
    def view = v1 + v2
    def unique_view = view.unique()
    println("合并后的完整view为：${unique_view}")
    return unique_view
}

// 判断字符串是否为空
def isEmpty(str) {
    return str == null || str.trim() == ""
}

// 更新resources文件
def getResourcesContent(views) {
    def regex = /(?s).*(\/assets\/resources\/art\/timeline\/[\w\/]+)\/?\d*\/?.*/
    def filteredList = views.collect { tuple ->
        def path = tuple =~ regex
        if (path) {
            path[0][1].substring(1).replaceAll(/\/$/, "") // 取第一个捕获组中的路径部分
        }
    }.findAll { path ->
        path != null && !path.contains("npc")
    }
    def output = filteredList.join(",")
    println("ResourcesContent：${output}")
    return output
}

def get_summary_info(platform_info){
    return """
**原始资源 changelist**: ${platform_info.changeList}
**ab包 changelist**: ${platform_info.CHANGELIST}
[打包统计](${platform_info.package_result_url})
[打包资源](${platform_info.resources})
**打包数量**: ${platform_info.total_count}
**成功数量**: ${platform_info.succ_count}
**成功ID**: ${platform_info.success_ids}
**失败ID**: ${platform_info.fail_ids}
<font color = "#dc143c">**失败数量**: ${platform_info.failed_count}</font>
<font color = "#dc143c">**检查出错数量**: ${platform_info.error_count}</font>
"""
}

// 用于存储platforminfo
PLATFORM_INFO = [:]


pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr)
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
         booleanParam(name: 'force', defaultValue: true, description: '是否强更P4')
         text(name: 'timeline_ids', defaultValue: '', description: '需要强打的TimelineID，多个TimelineID用逗号分割, 例如: qinglvtao/1048626, qinglvtao/1048627')
    }
    environment {
        PYFRAME_PYTHON = "conda run -n pyf368 python"
        // 打包大群机器人
        WX_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033'
    }

    triggers {
        cron('H/30 0-4,8-23 * * *')
    }

    stages {
        stage('美术资源打包'){
            matrix {
                agent {
                    node{
                        label "timeline_192.168.14.116"
//                        label "timeline_192.168.14.120"
                        customWorkspace "cws/${PLATFORM}"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'android','ios'
                    }
                }
                environment {
                    P4_WORKSPACE = "timeline_${PLATFORM}_2022_refactor"
                    P4_CONF_WORKSPACE = "timeline_conf_${PLATFORM}_2022_refactor"
                    GIT_PERSISTENT_BRANCH = "timeline-${PLATFORM}-unity2022"
                    GIT_PRESISTENT_PATH = 'pyframe-pipeline/project/x5m/art_package/config'
                }
                stages{
                    // TODO 临时逻辑, 检查参数, 用于强打资源
                    stage('检查参数'){
                        steps{
                            script{
                                env.ErrInfo='检查参数失败'
                                if (params.timeline_ids != ''){
                                    def timeline_ids = params.timeline_ids.split(/,|\n/)
                                    def timeline_ids_str = timeline_ids.join(",")
                                    env.timeline_ids = timeline_ids_str
                                }
                            }
                        }
                    }
                    stage('配置初始化'){
                        steps {
                            dir(GIT_PRESISTENT_PATH){
                                script{
                                    PLATFORM_INFO[PLATFORM] = [:]
                                    env.ErrInfo='配置初始化失败'
                                    currentRunSteps = 1
                                    getBuildUserInfo()
                                    if (!fileExists(".git/config")) {
                                        bat """
                                        del /s /f /q .
                                        """
                                    }
                                    // 判断配置 config.ini 是否存在，存在, 不处理, 否则判断 config.ini.example 是否存在, 存在，改名为config.ini
                                    def configFile = "config.ini"
                                    def configExampleFile = "config.ini.example"
                                    def configExists = fileExists "$configFile"
                                    def exampleExists = fileExists "$configExampleFile"
                                    println("配置初始化中文件是否存在, configExists:${configExists}, exampleExists:${exampleExists}")
                                    if (!configExists) {
                                        // 不存在
                                        bat """
                                        git clone -b ${GIT_PERSISTENT_BRANCH} http://oauth2:<EMAIL>/auto/x5m_config_persistent.git .
                                        """
                                    }
                                    bat """
                                    git clean -xdf
                                    git reset --hard HEAD
                                    git pull origin ${GIT_PERSISTENT_BRANCH}
                                    """
                                }
                            }
                        }
                    }
                    stage("获取timeline映射配置文件") {
                        steps {
                            dir("pyframe-pipeline/project/x5m/art_package/scripts") {
                                script {
                                    env.ErrInfo = "获取timeline映射配置文件失败"
                                    bat(returnStdout: false, script: "${PYFRAME_PYTHON} setup.py sync_timeline_config --workspace=${P4_CONF_WORKSPACE}")
                                }
                            }
                        }
                    }
                    stage('检查NPC资源-1'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='检查NPC资源失败'
                                    def npc_config = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py get_npc_changelist --platform=${PLATFORM}").split('\n')
                                    def npc_changelist = npc_config[2]
                                    def out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py get_updates --tp=npc --first_changelist=${npc_changelist}").split('\n')
                                    println(out[2])
                                    def result_map = readJSON text: out[2]
                                    PLATFORM_INFO[PLATFORM].isNPCPackage = false
                                    PLATFORM_INFO[PLATFORM].NPC_P4_VIEW = ""
                                    if (result_map.update == 'true'){
                                        PLATFORM_INFO[PLATFORM].NPC_P4_VIEW = result_map.p4_view
                                    }
                                }
                            }
                        }
                    }
                    stage('检查NPC资源-2'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    def npc_out = bat(returnStdout: true, script: """${PYFRAME_PYTHON} setup.py fill_npc_p4_view --workspace=${P4_CONF_WORKSPACE} --view="${PLATFORM_INFO[PLATFORM].NPC_P4_VIEW}" """).split("\n")[2]
                                    println("npc_out:${npc_out}")
                                    def empty = isEmpty(npc_out)
                                    println("empty:${empty}")
                                    if (!empty) {
                                        PLATFORM_INFO[PLATFORM].isNPCPackage = true
                                    }
                                }
                            }
                        }
                    }
                    stage('检查timeline资源'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='检查timeline资源失败'
                                    currentRunSteps = 2
                                    def out = bat(returnStdout: true, script: "${PYFRAME_PYTHON} setup.py get_updates --tp timeline --first_changelist=${params.first_changelist}").split('\n')
                                    println("检查是否需要打包返回的out值:${out}")
                                    def result_map = readJSON text: out[2]
                                    println("result_map: ${result_map}")
                                    env.PipelineStatus = out[3]
                                    println(env.PipelineStatus)
                                    PLATFORM_INFO[PLATFORM].isTMPackage = result_map.update == 'true'
                                    PLATFORM_INFO[PLATFORM].TM_P4_VIEW = result_map.p4_view
                                    PLATFORM_INFO[PLATFORM].send_user = result_map.send_user
                                    if (result_map.update == 'true'){
                                        PLATFORM_INFO[PLATFORM].PACKAGELIST = result_map.package_list.replace(",", "\n")
                                        PLATFORM_INFO[PLATFORM].changeList = result_map.changelist
                                    }
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='检查是否需要打包失败'
                                    currentRunSteps = 2
                                    println(env.PipelineStatus)
                                    PLATFORM_INFO[PLATFORM].needPackage = (PLATFORM_INFO[PLATFORM].isTMPackage || PLATFORM_INFO[PLATFORM].isNPCPackage)
                                    if(!PLATFORM_INFO['ios'].needPackage && !PLATFORM_INFO['android'].needPackage){
                                        println("不需要打包")
                                        currentBuild.result = 'ABORTED'
                                    }else{
                                        println("需要打包")
                                    }
                                }
                            }
                        }
                    }
                    stage("检查p4映射是否存在依赖") {
                        when {
                            expression {
                                return PLATFORM_INFO[PLATFORM].needPackage
                            }
                        }
                        steps {
                            dir("pyframe-pipeline/project/x5m/art_package/scripts") {
                                script {
                                    env.ErrInfo = "检查p4映射是否存在依赖失败"
                                    def out = bat(returnStdout: true, script: """${PYFRAME_PYTHON} setup.py fill_p4_view --workspace=${P4_CONF_WORKSPACE} --view="${PLATFORM_INFO[PLATFORM].TM_P4_VIEW}" """).split("\n")[2]
                                    println("out:${out}")
                                    def npc_out = bat(returnStdout: true, script: """${PYFRAME_PYTHON} setup.py fill_npc_p4_view --workspace=${P4_CONF_WORKSPACE} --view="${PLATFORM_INFO[PLATFORM].TM_P4_VIEW}" """).split("\n")[2]
                                    print("npc_out:${npc_out}")
                                    def finalP4View = mergeView(out, npc_out)
                                    def resourcesContent = getResourcesContent(finalP4View)
                                    PLATFORM_INFO[PLATFORM].finalP4View = finalP4View
                                    PLATFORM_INFO[PLATFORM].resources_path = resourcesContent ? resourcesContent : ""
                                }
                            }
                        }
                    }
                    stage('清理资源'){
                        when {
                            expression {
                                return  PLATFORM_INFO[PLATFORM].needPackage
                            }
                        }
                        steps {
                            dir("x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/timeline"){
                                deleteDir()
                            }
                        }
                    }
                    stage('获取p4资源'){
                        when {
                            expression {
                                return  PLATFORM_INFO[PLATFORM].needPackage
                            }
                        }
                        steps{
                            retry(2){
                                script{
                                    env.ErrInfo='获取p4资源失败'
                                    timeRetry ++
                                    echo "执行次数：${timeRetry}"
                                    def view = getP4View(PLATFORM, PLATFORM_INFO[PLATFORM].finalP4View)
                                    currentRunSteps = 4
                                    bat """git config --global core.longpaths true"""
                                    getArtTrunkFromGitlab()

                                    // 拉取资源目录
                                    println("当前路径是: " + pwd())
                                    def python_view = view.replaceAll("\n", "#")
                                    writeFile encoding: 'GB2312', file: "p4_view.txt", text: "${python_view}"
                                    try {
                                        timeout(time: 15, unit: 'MINUTES') {
                                            bat(returnStdout: false, script: "${PYFRAME_PYTHON} pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${env.WORKSPACE} --workspace=${P4_WORKSPACE}  --force_sync=${params.force}")
                                        }
                                    } catch (err) {
                                        def sendUserString = messageSendUserList.join(",")
                                        if (sendUserString != "") {
                                            emailext to: "${sendUserString}", subject: "jenkins-TimeLine-CDN-2022打包 #${env.BUILD_NUMBER}", body: "获取p4资源卡住超时，重试中"
                                        }
                                        error("获取p4资源卡住超时，重试中")
                                    }
                                }
                            }
                        }
                    }
                    stage("复制timeline映射配置文件") {
                        when {
                            expression {
                                return  PLATFORM_INFO[PLATFORM].needPackage
                            }
                        }
                        steps {
                            dir("pyframe-pipeline/project/x5m/art_package/scripts") {
                                script {
                                    env.ErrInfo = "复制timeline映射配置文件失败"
                                    bat(returnStdout: false, script: "${PYFRAME_PYTHON} setup.py copy_timeline_config --workspace=${P4_CONF_WORKSPACE}")
                                }
                            }
                        }
                    }
                    stage('调用打包命令'){
                        when {
                            expression {
                                return  PLATFORM_INFO[PLATFORM].needPackage
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='调用unity打包失败'
                                currentRunSteps = 6
                                def jenkins_log = "${WORKSPACE}\\jenkins_log\\${env.BUILD_NUMBER}"
                                def logExists = fileExists "$jenkins_log"
                                if(!logExists) {
                                    bat label:"日志文件夹",script:"mkdir jenkins_log\\${env.BUILD_NUMBER}"
                                }
                                def build_pack_log = "${jenkins_log}\\${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat script: "Unity.exe -quit -batchmode -logFile ${build_pack_log} " +
                                        "-projectPath \"${WORKSPACE}\\x5_mobile\\mobile_dancer\\arttrunk\\client\" " +
                                        "-executeMethod H3DBuildTools.BuildTimeline -buildTarget ${PLATFORM} " +
                                        "out_path=${WORKSPACE}\\x5_mobile\\mr\\art_release\\cs\\${PLATFORM}\\assetbundles " +
                                        "path=\"${PLATFORM_INFO[PLATFORM].resources_path}\" "
                            }
                        }
                    }
                    stage('计算打包结果'){
                        when {
                            expression {
                                return  PLATFORM_INFO[PLATFORM].needPackage
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='计算打包结果失败'
                                    currentRunSteps = 7
                                    def out = bat returnStdout: true, script: "${PYFRAME_PYTHON} setup.py print_package_result "+
                                    "--path ${WORKSPACE}\\x5_mobile\\mobile_dancer\\arttrunk\\client "
                                    out = out.split('\n')
                                    print(out[2])
                                    def count_result = readJSON text: out[2]
                                    PLATFORM_INFO[PLATFORM].total_count = count_result.total_count
                                    PLATFORM_INFO[PLATFORM].succ_count = count_result.succ_count
                                    PLATFORM_INFO[PLATFORM].failed_count = count_result.failed_count
                                    PLATFORM_INFO[PLATFORM].error_count = count_result.error_count
                                    PLATFORM_INFO[PLATFORM].success_ids = count_result.success_ids
                                    PLATFORM_INFO[PLATFORM].fail_ids = count_result.fail_ids
                                    PLATFORM_INFO[PLATFORM].upload_p4 = count_result.upload_p4
                                    if(PLATFORM_INFO[PLATFORM].error_count != 0 || PLATFORM_INFO[PLATFORM].failed_count != 0 || PLATFORM_INFO[PLATFORM].succ_count == 0){
                                        currentBuild.result = 'UNSTABLE'
                                    }
                                }
                            }
                        }
                    }
                    stage('提交p4'){
                        when {
                            expression {
                                return  PLATFORM_INFO[PLATFORM].needPackage && PLATFORM_INFO[PLATFORM].upload_p4 == 'true'
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    env.ErrInfo='提交p4失败'
                                    currentRunSteps = 8
                                    def submitP4Desc = "--Auto--|uploader:${PLATFORM_INFO[PLATFORM].send_user}|Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                                    echo "${submitP4Desc}"
                                    // 提交视频相关
                                    bat """${PYFRAME_PYTHON} setup.py submit_video_files --view=\"${PLATFORM_INFO[PLATFORM].TM_P4_VIEW}\" --root=${WORKSPACE} --desc=\"${submitP4Desc}\" --platform=${PLATFORM} --workspace=${P4_WORKSPACE}"""
                                    def out = bat returnStdout: true, script: "${PYFRAME_PYTHON} setup.py submit_files_to_p4  "+
                                        "--root=${WORKSPACE} --platform=${PLATFORM} --tp=timeline --changelist=${PLATFORM_INFO[PLATFORM].changeList} "+
                                        "--desc=\"${submitP4Desc}\" --workspace=${P4_WORKSPACE}"
                                    echo "${out}"
                                    String[] outArras=out.split("\n")
                                    if (outArras.size() > 4){
                                        println("p4workspace下没有需要提交的内容！")
                                    }else{
                                        PLATFORM_INFO[PLATFORM].CHANGELIST = outArras[2]
                                    }
                                }
                            }
                        }
                    }
                    stage('持久化配置文件'){
                        when {
                            expression {
                                // TODO: 细化失败情况, 而不是失败了,都算成是由于文件有问题导致的编译失败, 避免下面情况的产生: 流程有问题, 导致文件未编译
                                return  PLATFORM_INFO[PLATFORM].needPackage
                            }
                        }
                        steps{
                            dir(GIT_PRESISTENT_PATH){
                                script{
                                    env.ErrInfo='持久化配置文件失败'
                                    bat "${PYFRAME_PYTHON} ../scripts/setup.py update_timeline_persistent_config --changelist=${PLATFORM_INFO[PLATFORM].changeList}"
                                    bat """
                                    git config --global user.email "<EMAIL>"
                                    git config --global user.name "dgm_developer"
                                    git add config.ini
                                    git commit -m "jenkins auto commit ${env.BUILD_NUMBER}"
                                    git push origin ${GIT_PERSISTENT_BRANCH}
                                    """
                                }
                            }
                        }
                    }
                    stage('上传打包结果'){
                        when {
                            expression {
                                return  PLATFORM_INFO[PLATFORM].needPackage
                            }
                        }
                        steps{
                            script{
                                env.ErrInfo='上传打包结果失败'
                                currentRunSteps = 9
                                def out
                                dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                    out = bat returnStdout: true, script: "${PYFRAME_PYTHON} setup.py get_package_result --path ${WORKSPACE}\\x5_mobile\\mobile_dancer\\arttrunk\\client --platform ${PLATFORM}"
                                    out = out.split('\n')
                                }
                                def artifactsPath="x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/AssetBundleToolLogs/"
                                dir("x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool") {
                                    archiveArtifacts artifacts: "resources.txt", followSymlinks: false
                                    println("${env.BUILD_URL}" + "artifact/resources.txt")
                                }
                                dir(artifactsPath){
                                    def artifactName = out[2].trim()
                                    archiveArtifacts artifacts: "$artifactName", followSymlinks: false
                                    def artifactUrl = env.BUILD_URL + 'artifact/' + out[2].trim()
                                    def resources_url = env.BUILD_URL + "artifact/resources.txt"
                                    echo "upload_url: ${artifactUrl}"
                                    PLATFORM_INFO[PLATFORM].package_result_url = artifactUrl.trim()
                                    PLATFORM_INFO[PLATFORM].resources = resources_url
                                }
                            }
                        }
                    }
                    stage('判断是否单端失败'){
                          when {
                             expression {
                                 buildResultCode <= 3
                             }
                         }
                         steps{
                             script{
                                 if ("${PLATFORM}" == "android"){
                                     buildResultCode += 1
                                 }
                                 else{
                                     buildResultCode += 2
                                 }
                             }
                         }
                    }
                }
                post{
                    always{
                        //上传日志
                        script {
                            def build_log_android = "jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log"
                            def buildLogAndroidExists = fileExists "$build_log_android"
                            if(buildLogAndroidExists){
                                archiveArtifacts artifacts: "$build_log_android", followSymlinks: false
                            }
                            def build_log_ios = "jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log"
                            def buildLogIosExists = fileExists "$build_log_ios"
                            if(buildLogIosExists){
                                archiveArtifacts artifacts: "$build_log_ios", followSymlinks: false
                            }
                            PLATFORM_INFO[PLATFORM].BACK_INFO = get_summary_info(PLATFORM_INFO[PLATFORM])
                            env.LOGBACKINFO = """
[安卓 unity日志](${env.BUILD_URL}artifact/jenkins_log/${env.BUILD_NUMBER}/android_${env.BUILD_NUMBER}.log)
[ios unity日志](${env.BUILD_URL}artifact/jenkins_log/${env.BUILD_NUMBER}/ios_${env.BUILD_NUMBER}.log)"""

                            if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                                if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                                    messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                                }
                            }
                            if (PLATFORM_INFO[PLATFORM].send_user) {
                                messageSendUserList.add(PLATFORM_INFO[PLATFORM].send_user)
                            }
                        }
                    }
                }
            }
        }
    }
    post {
       failure {
           node('master') {
               script {
                   def sendUserString = messageSendUserList.join(",")
                   echo "执行状态码： ${buildResultCode}"
                   def msg = """**结果**: TimeLine资源cdn打包失败"""

                   if (buildResultCode == 1){
                       msg += """
<font color = "#dc143c">**单端成功**: 安卓(下次打包会自动把失败的一端打包)</font>
${PLATFORM_INFO['android'].BACK_INFO}
"""
                   }
                   else if (buildResultCode == 2){
                       msg += """
<font color = "#dc143c">**单端成功**: ios(下次打包会自动把失败的一端打包)</font>
${PLATFORM_INFO['ios'].BACK_INFO}
"""
                   }
                   msg += """
<font color = "#dc143c">**错误信息**: ${env.ErrInfo}</font>
${env.LOGBACKINFO}
"""
                   echo "${msg}"
                   // 发送信息到打包大群
                   workwx.send url: env.WX_URL,content: msg
                   if (sendUserString != "") {
                       emailext to: "${sendUserString}", subject: "jenkins-TimeLine-CDN-2022打包失败 #${env.BUILD_NUMBER}", body: msg
                   }
               }
           }
       }
        unstable{
            node('master') {
                script{
                    def sendUserString = messageSendUserList.join(",")
                    if (PLATFORM_INFO['ios'].needPackage || PLATFORM_INFO['android'].needPackage){
                        def msg=""""""
                        if (PLATFORM_INFO['android'].needPackage) {
                            msg+= """**安卓**：${PLATFORM_INFO['android'].BACK_INFO}\n"""
                        }
                        if (PLATFORM_INFO['ios'].needPackage) {
                            msg+="""**ios**：${PLATFORM_INFO['ios'].BACK_INFO}\n"""
                        }
                        msg += "${env.LOGBACKINFO}"
                        workwx.send url:env.WX_URL, content: msg
                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: "jenkins-TimeLine-CDN-2022打包部分失败 #${env.BUILD_NUMBER}", body: msg
                        }
                    }
                }
            }
        }
        success {
            node('master') {
                script{
                    def sendUserString = messageSendUserList.join(",")
                    if (PLATFORM_INFO['ios'].needPackage || PLATFORM_INFO['android'].needPackage){
                        def msg=""""""
                        if (PLATFORM_INFO['android'].needPackage) {
                            msg+= """**安卓**：${PLATFORM_INFO['android'].BACK_INFO}\n"""
                        }
                        if (PLATFORM_INFO['ios'].needPackage) {
                            msg+="""**ios**：${PLATFORM_INFO['ios'].BACK_INFO}\n"""
                        }

                        if (sendUserString != "") {
                            emailext to: "${sendUserString}", subject: "jenkins-TimeLine-CDN-2022打包成功 #${env.BUILD_NUMBER}", body: msg
                        }
                    }
                }
            }
        }
    }
}
