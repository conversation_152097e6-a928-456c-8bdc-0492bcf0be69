@Library('h3d_libs') _
import groovy.json.JsonSlurper
def changePrefab = ""
def changeAtlas = ""
def need_again_package_path = ""
def error_info = ""
def delete_p4_prefab_files = ""
def delete_p4_ab_files = ""
def isAndroidPackage = ""
def isIosPackage = ""
def git_depot = "http://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git"
def git_branch = ""
def ResBranch = ""
def messageSendUserList = ["<EMAIL>", "<EMAIL>", "jiny<PERSON><EMAIL>", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "wang<PERSON><PERSON>@h3d.com.cn", "dang<PERSON><PERSON>@h3d.com.cn", "<EMAIL>", "<EMAIL>", "<EMAIL>"]


def IsPackage(platform,isAndroidPackage,isIosPackage){
    if (platform == "android"){
        return isAndroidPackage == "true"
    }else{
        return isIosPackage == "true"
    }
}

def getAbP4View(platform, res_branch) {
    def tpl = '''
//x5_mobile/mr/${res_branch}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/uiprefabs_ab/... //${p4Client}/mr/${res_branch}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/art/uiprefabs_ab/...
//x5_mobile/mr/${res_branch}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/c/... //${p4Client}/mr/${res_branch}/ResourcePublish/CDN/SourceFiles/${platform}/assetbundles/c/...
    '''
    def engine = new groovy.text.SimpleTemplateEngine()
    return engine.createTemplate(tpl).make([platform: platform, res_branch: res_branch, p4Client: '${P4_CLIENT}']).toString()
}

def getCampaigninPackFromGitlab(branch){
    checkout changelog: false, poll: false,
    scm: [$class: 'GitSCM',
    branches: [[name: "*/${branch}"]],
    doGenerateSubmoduleConfigurations: false,
    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'x5mconfig'],
    [$class: 'SparseCheckoutPaths', sparseCheckoutPaths: [[path: 'config/campaigninpack.xml']]]],
    submoduleCfg: [],
    userRemoteConfigs: [[credentialsId: 'dgm-package-gitlab-sshpk',
    url: '******************************:dgm/x5mconfig.git']]]
}

pipeline {
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 60, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
        string name: 'input_pipeline_id', defaultValue: '', description: '需要手动触发的pipeline_id:'
        string name: 'input_commit_id', defaultValue: '', description: '需要手动触发的commit_id:'
        booleanParam name: 'upload_git', defaultValue: true, description: '是否上传索引表'
    }
    stages {
        stage('UI资源打包'){
            matrix {
                agent {
                    node{
                        label "dgm_pub_featab_ui_branch_${PLATFORM}_192.168.7.77"
                        customWorkspace "D:\\jenkins_branch_${PLATFORM}\\workspace"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values "ios","android"
                    }
                }
                stages {
                    stage('设置全局变量'){
                        steps {
                            script{
                                if ("${PLATFORM}" == "android"){
                                    env.SENDPLATFORM = "安卓"
                                    println("运行平台是：" + env.SENDPLATFORM)
                                }else{
                                    env.SENDPLATFORM = "ios"
                                    println("运行平台是：" + env.SENDPLATFORM)
                                }
                            }
                        }
                    }
                    stage('获取p4上的最新分支'){
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    def branch_out = bat(returnStdout: true, script: "python setup.py get_mr_latest_branch").split('\n')
                                    println(branch_out[2])
                                    branch = branch_out[2].trim()
                                    git_branch = "${branch}"
                                    ResBranch = "b/${branch}"

                                }
                            }
                        }
                    }
                    stage('获取git资源'){
                        steps {
                            script{
                                def clone_git = false
                                def delete_and_clone = false
                                if (!fileExists('x5mobile/.git')) {
                                    clone_git = true
                                    echo "x5mobile/.git 文件夹不存在,需要强更"
                                }
                                else{
                                    dir('x5mobile') {
                                        //检查是否正常仓库
                                        ret= bat(label:"检查Git仓库",returnStatus :true,encoding:'GBK',script:'git branch')
                                        //如果不是正常的git仓库重新下载代码
                                        if(ret != 0) {
                                            echo  "不是正常的git仓库需要删除强制更新"
                                            delete_and_clone = true
                                        }
                                        else{
                                            bat"""
                                            git reset --hard HEAD
                                            git fetch --all
                                            git checkout ${git_branch}
                                            git pull origin ${git_branch}
                                            """
                                        }
                                    }
                                }
                                if (clone_git == true){
                                    bat"""
                                    git clone -b ${git_branch} ${git_depot}
                                    """
                                }
                                if(delete_and_clone == true){
                                    dir('x5mobile'){
                                        deleteDir()
                                    }
                                    bat"""
                                    git clone -b ${git_branch} ${git_depot}
                                    """
                                }
                            }
                        }
                    }

                    stage('获取ab包资源'){
                        steps {
                            script{
                                //更新campaigninpack.xml
                                getCampaigninPackFromGitlab(git_branch)

                                dir('x5mobile'){
                                    println("工作区：" + env.WORKSPACE + " p4client：" + "jenkins-ui-${PLATFORM}-${git_branch}-package")
                                    def view = getAbP4View(PLATFORM, ResBranch)

                                    def p4_root = pwd()
                                    def p4_workspace = "jenkins-ui-${PLATFORM}-${git_branch}-package"
                                    def python_view = view.replaceAll("\n", "#")
                                    bat(returnStdout: false, script: "python ../pyframe-pipeline/project/x5m/art_package/scripts/setup.py p4_sync --p4_root=${p4_root} --workspace=${p4_workspace} --p4_view=\"${python_view}\" --force_sync=true")

//                                     checkout perforce(credential: 'p4_dgm_jenkins_upwd',
//                                         populate: syncOnly(force: true, have: true, modtime: false, parallel: [enable: false, minbytes: '1024', minfiles: '1', threads: '4'], pin: '', quiet: true, revert: false),
//                                         workspace: manualSpec(
//                                             charset: 'none',
//                                             cleanup: false,
//                                             name: "jenkins-ui-${PLATFORM}-${git_branch}-package",
//                                             pinHost: false,
//                                             spec: clientSpec(
//                                                 allwrite: true, backup: true, changeView: '', clobber: true, compress: false, line: 'LOCAL',
//                                                 locked: false, modtime: false, rmdir: true, serverID: '', streamName: '', type: 'WRITABLE', view: view
//                                             )
//                                         )
//                                     )
                                }
                            }
                        }
                    }

                    stage('检查是否需要打包') {
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts'){
                                script{
                                    bat"""
                                    python -m pip install --upgrade pip
                                    pip install -r requirements.txt
                                    """
                                    dir('ui_scripts')
                                    {
                                       if (input_pipeline_id == "" && input_commit_id == ""){
                                            def after_commit_id = env.gitlabAfter
                                            def before_commit_id = env.gitlabBefore
                                            println "本次提交commit_id是: " + after_commit_id
                                            println "上次提交commit_id是: " + before_commit_id
                                            def info_out = bat(returnStdout: true, script: "python setup.py get-pipeline-id --commit_id ${after_commit_id}").split('\n')
                                            def split_info_out = info_out[2].split(',')
                                            println("提交信息: " + split_info_out)
                                            // info_out数据格式：1617,<EMAIL>,hujianbin
                                            env.PIPELINEID = split_info_out[0]
                                            env.EMAIL = split_info_out[1]
                                            env.NAME = split_info_out[2]
                                            env.COMMITID = after_commit_id
                                            env.BEFORECOMMITID = before_commit_id
                                        }
                                       else if(input_commit_id != ""){
                                            def pipeline_info_out = bat(returnStdout: true, script: "python setup.py get-pipeline-id2 --commit_id ${input_commit_id}").split('\n')
                                            def split_pipe_info_out = pipeline_info_out[2].split(',')
                                            println("提交信息: " + split_pipe_info_out)
                                            // info_out数据格式：1617,<EMAIL>,hujianbin
                                            env.PIPELINEID = split_pipe_info_out[0]
                                            env.EMAIL = split_pipe_info_out[1]
                                            env.NAME = split_pipe_info_out[2]
                                            env.COMMITID = input_commit_id
                                            env.BEFORECOMMITID = 0
                                        }
                                       else if(input_pipeline_id != ""){
                                            def commit_id_out = bat(returnStdout: true, script: "python setup.py get-commit-id --pipeline_id ${input_pipeline_id}").split('\n')
                                            println("**: " + commit_id_out)
                                            def split_commit_id_out = commit_id_out[2].split(',')
                                            println("pipeline_id对应的信息是: " + split_commit_id_out)
                                            env.PIPELINEID = input_pipeline_id
                                            env.COMMITID = split_commit_id_out[0]
                                            env.EMAIL = split_commit_id_out[1]
                                            env.NAME = split_commit_id_out[2]
                                            env.BEFORECOMMITID = 0
                                       }

                                        def out = bat(returnStdout: true, script: "python setup.py check-submit-files --commit_id ${env.COMMITID} --last_commit_id ${env.BEFORECOMMITID} --workspace ${env.WORKSPACE} --git_depot ${git_depot} --git_branch ${git_branch}").split('\n')
                                        println(out[2])
                                        def jsonSlurper = new JsonSlurper()
                                        def result_map = jsonSlurper.parseText(out[2])
                                        if (result_map.action == 'true'){
                                            changePrefab = result_map.prefab
                                            changeAtlas = result_map.atlas
                                        }
                                        else if(result_map.action == 'delete_p4'){
                                            env.DELETEAB = 'true'
                                            delete_p4_prefab_files = result_map.delete_p4_files
                                        }
                                        else{
                                            currentBuild.result = 'ABORTED'
                                        }
                                        def workspace
                                        String platform
                                        if ("${PLATFORM}" == "android"){
                                            env.ANDROIDWORKSPACE = env.WORKSPACE
                                            isAndroidPackage = result_map.action
                                            println("isAndroidPackage是: " + isAndroidPackage)
                                        }else{
                                            env.IOSWORKSPACE = env.WORKSPACE
                                            isIosPackage = result_map.action
                                            println("isIosPackage是：" + isIosPackage)
                                        }
                                        if(isAndroidPackage == 'false' && isIosPackage == 'false'){
                                            currentBuild.result = 'ABORTED'
                                        }
                                    }
                                }
                            }
                        }
                    }

                    stage("copy xml"){
                        steps{
                            script{
                                bat"""
                                echo f y| xcopy x5mconfig\\config\\campaigninpack.xml x5mobile\\mobile_dancer\\trunk\\client\\assetbundles\\cdn\\assetbundles\\config\\campaigninpack.xml /e /y /r
                                """
                            }
                        }
                    }

                    stage("campaign_link"){
                        steps{
                            script{
                                env.ErrInfo='campaign_link.bat出错'
                                def campaign_link_cmd="${env.WORKSPACE}/x5mobile/mobile_dancer/trunk/campaign_link.bat "
                                bat label:"执行链接脚本",script:"${campaign_link_cmd}"
                            }
                        }
                    }
                    stage("删除ab目录") {
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) || env.DELETEAB == 'true'
                            }
                        }
                        steps {
                            script {
                                dir('x5mobile/mr/Resources'){
                                    deleteDir()
                                }
                            }
                        }
                    }

                    stage('打包检查'){
                          when {
                              expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) || env.DELETEAB == 'true'
                              }
                          }
                         steps {
                            script{
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    platform = "Android"
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    platform = "iOS"
                                    workspace = env.IOSWORKSPACE
                                }
                                def jenkins_log = "${workspace}\\jenkins_log\\${env.BUILD_NUMBER}"
                                def logExists = fileExists "$jenkins_log"
                                if(!logExists)
                                {
                                    bat label:"日志文件夹",script:"mkdir jenkins_log\\${env.BUILD_NUMBER}"
                                }
                                def check_pack_log = "${jenkins_log}\\checkPack_${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat(script: "unity -quit -batchmode -logFile ${check_pack_log} -projectPath \"${workspace}\\x5mobile\\mobile_dancer\\trunk\\client\" -executeMethod DealIndexTable.CheckIndexChange -buildTarget ${platform} prefab_files=${changePrefab} atlas_files=${changeAtlas} delete_p4_files=${delete_p4_prefab_files}")
                            }
                        }
                     }

                    stage('检查打包图集是否存在/变更'){
                          when {
                              expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) || env.DELETEAB == 'true'
                              }
                          }
                         steps {
                              dir('pyframe-pipeline/project/x5m/art_package/scripts/ui_scripts'){
                                   script{
                                        def workspace
                                        String platform
                                        if ("${PLATFORM}" == "android"){
                                            workspace = env.ANDROIDWORKSPACE
                                        }else{
                                            workspace = env.IOSWORKSPACE
                                        }
                                        def out = bat(returnStdout: true, script: "python setup.py check-package --workspace ${workspace}").split('\n')
                                        println(out[2])
                                        def jsonSlurper = new JsonSlurper()
                                        def result_map = jsonSlurper.parseText(out[2])
                                        if(result_map.need_package_prefab == 'false'){
                                            need_again_package_path = result_map.need_package_prefab_files
                                            println("需要重新打包的prefab：" + need_again_package_path)
                                        }
                                        if(result_map.not_exist_atlas == 'false'){
                                            error_info += "prefab引用的图集不存在, "
                                        }
                                        if(result_map.not_exist_index_prefabs == 'false'){
                                            error_info += "prefab不在prefab-atlas索引表里, "
                                        }
                                        if(result_map.not_exist_index_atlas == 'false'){
                                            error_info += "atlas不在atlas-prefab索引表里, "
                                        }
                                        if(result_map.not_quote_atlas == 'false'){
                                            error_info += "没有任何prefab引用的图集"
                                        }
                                        if(result_map.out_delete_p4_paths == 'false'){
                                            delete_p4_ab_files = result_map.out_delete_p4_paths_files
                                        }
                                        println("需要重新打包：" + need_again_package_path)
                                        println("错误信息：" + error_info)
                                        println("需要删除p4：" + delete_p4_ab_files)
                                   }
                              }
                        }
                    }

                    stage('上传错误统计'){
                          when {
                              expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage) || env.DELETEAB == 'true'
                              }
                          }
                         steps {
                              dir('pyframe-pipeline/project/x5m/art_package/scripts/ui_scripts'){
                                    script{
                                        def workspace
                                        String platform
                                        if ("${PLATFORM}" == "android"){
                                            workspace = env.ANDROIDWORKSPACE
                                        }else{
                                            workspace = env.IOSWORKSPACE
                                        }
                                        if(error_info != ""){
                                            env.ERRORINFO = 'true'
                                            out = bat(returnStdout: true, script: "python setup.py upload-error-result --path ${workspace}").split('\n')
                                            print(out[2])
                                            if ("${PLATFORM}" == "android"){
                                                env.ANDROIDERRORRESULTURL = 'http://x5mobile-package-service.tac.com/api/v1/package_result/'+ out[2]
                                            }
                                            else{
                                                env.IOSERRORRSULTURL = 'http://x5mobile-package-service.tac.com/api/v1/package_result/'+ out[2]
                                            }
                                        }
                                        else{
                                            env.ERRORINFO = 'false'
                                            println("没有错误信息")
                                        }
                                    }
                              }
                        }
                    }

                    stage('调用打包命令'){
                         when {
                              expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                              }
                        }
                        steps {
                            script{
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    platform = "Android"
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    platform = "iOS"
                                    workspace = env.IOSWORKSPACE
                                }
                                def jenkins_log = "${workspace}\\jenkins_log\\${env.BUILD_NUMBER}"
                                def logExists = fileExists "$jenkins_log"
                                if(!logExists)
                                {
                                    bat label:"日志文件夹",script:"mkdir jenkins_log\\${env.BUILD_NUMBER}"
                                }
                                def build_pack_log = "${jenkins_log}\\buildPack_${PLATFORM}_${env.BUILD_NUMBER}.log"
                                bat(script: "unity -quit -batchmode -logFile ${build_pack_log} -projectPath \"${workspace}\\x5mobile\\mobile_dancer\\trunk\\client\" -executeMethod UIBuild.JenkinsBuildUI -buildTarget ${platform} path1=${changePrefab} path2=${need_again_package_path}")
                            }
                        }
                    }

                    stage('计算打包结果'){
                        when {
                              expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                              }
                        }
                        steps {
                         dir('pyframe-pipeline/project/x5m/art_package/scripts/ui_scripts'){
                            script{
                                def workspace
                                String platform
                                if ("${PLATFORM}" == "android"){
                                    workspace = env.ANDROIDWORKSPACE
                                }else{
                                    workspace = env.IOSWORKSPACE
                                }
                                def build_log_path = "${workspace}\\jenkins_log\\${env.BUILD_NUMBER}\\buildPack_${PLATFORM}_${env.BUILD_NUMBER}.log"
                                out = bat(returnStdout: true, script: "python setup.py print_package_result --log_path ${build_log_path}").split('\n')
                                println(out[2])
                                def count_result = readJSON text: out[2]
                                env.UPLOADP4 = count_result.upload_p4
                                env.SUCC = count_result.succ_count
                                env.PACKAGEED = count_result.total_count - count_result.atlas_count
                                if(count_result.total_count == count_result.succ_count + count_result.atlas_count){
                                    env.PACKAGERESULT = 'true'
                                    println("全部打包成功")
                                }
                                else{
                                    env.PACKAGERESULT = 'false'
                                    println("打包数量失败：" + count_result.error_count)
                                    }
                                }
                            }
                        }
                    }

                    stage('上传索引表'){
                        when {
                              expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                              }
                        }
                        steps {
                            dir('x5mobile'){
                                script{
                                    def workspace
                                    String platform
                                    println("是否上传git：" + upload_git)
                                    if(params.upload_git)
                                    {
                                        if ("${PLATFORM}" == "android")
                                        {
                                            workspace = env.ANDROIDWORKSPACE
                                            bat"""
                                            git config --global core.fscache false
                                            git config --global core.quotepath false
                                            git config --global core.longpaths true
                                            git config --global user.email  <EMAIL>
                                            git config --global user.name  <EMAIL>
                                            git remote set-url origin http://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git

                                            git checkout ${git_branch}
                                            git add mobile_dancer/trunk/client/Assets/engine/UIBuild/Editor/UIAtlasRefCache.txt
                                            git commit -m "update indexTable"
                                            git pull origin ${git_branch}
                                            git push origin ${git_branch}
                                            """
                                            env.INDEXCOMMITID = "true"
                                        }
									}
								}
							}
						}
					}

					stage('将ab包移动到分支目录'){
                        when {
                              expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                              }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts/ui_scripts'){
                                script{
									def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android")
                                    {
                                        platform = "Android"
                                        workspace = env.ANDROIDWORKSPACE
                                    }
                                    else
                                    {
                                        platform = "iOS"
                                        workspace = env.IOSWORKSPACE
                                    }
                                    if (git_branch != "master"){
                                        def out = bat(returnStdout: true, script: "python setup.py move-package --system ${platform} --workspace ${workspace} --res_branch ${ResBranch}")
                                        println(out)
                                    }
								}
							}
						}
					}

                    stage('上传打包统计'){
                        when {
                            expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                            }
                        }
                        steps{
                            dir('pyframe-pipeline/project/x5m/art_package/scripts/ui_scripts'){
                                script{
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android"){
                                        workspace = env.ANDROIDWORKSPACE
                                    }else{
                                        workspace = env.IOSWORKSPACE
                                    }
                                    def build_log_path = "${workspace}\\jenkins_log\\${env.BUILD_NUMBER}\\buildPack_${PLATFORM}_${env.BUILD_NUMBER}.log"
                                    out = bat(returnStdout: true, script: "python setup.py upload_package_result --log_path ${build_log_path}").split('\n')
                                    print(out[2])
                                    if ("${PLATFORM}" == "android"){
                                        env.ANDROIDPACKAGERESULTURL = 'http://x5mobile-package-service.tac.com/api/v1/package_result/'+ out[2]
                                    }
                                    else{
                                        env.IOSPACKAGERESULTURL = 'http://x5mobile-package-service.tac.com/api/v1/package_result/'+ out[2]
                                    }
                                }
                            }
                        }
                    }

                    stage('提交P4'){
                        when {
                              expression {
                                return IsPackage("${PLATFORM}",isAndroidPackage,isIosPackage)
                              }
                        }
                        steps {
                            dir('pyframe-pipeline/project/x5m/art_package/scripts/ui_scripts'){
                                script{
                                    def workspace
                                    String platform
                                    if ("${PLATFORM}" == "android")
                                    {
                                        platform = "Android"
                                        workspace = env.ANDROIDWORKSPACE
                                    }
                                    else
                                    {
                                        platform = "iOS"
                                        workspace = env.IOSWORKSPACE
                                    }
                                    def out = bat(returnStdout: true, script: "python setup.py submit-p4-files --system ${platform} --branch ${git_branch} --workspace ${workspace} --pipeline_id ${env.PIPELINEID} --res_branch ${ResBranch} --p4_client jenkins-ui-${PLATFORM}-${git_branch}-package")
                                    String[] outArras=out.split("\n")
                                    println(outArras)
                                    if (outArras.size() > 4)
                                    {
                                        println("p4workspace下没有需要提交的内容！")
                                    }
                                    else{
                                        if ("${PLATFORM}" == "android"){
                                            env.ANDROID_CHANGELIST = outArras[2]
                                        }else{
                                            env.IOS_CHANGELIST = outArras[2]
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                post{
                    always{
                        // 发送个人消息、上传日志
                        script {
                            messageSendUserList.add(env.EMAIL)
                            def sendUserString = messageSendUserList.join(",")
                            def msg = "项目: dgm/x5mobile<br>分支: ${git_branch}<br>平台: ${env.PLATFORM}<br>触发人: ${env.EMAIL}<br>CommitID: ${env.COMMITID}<br>PipelineID: ${env.PIPELINEID}<br>"
                            if(currentBuild.result == 'FAILURE')
                            {
                                emailext to: sendUserString, subject: "【UI打包流水线】执行失败通知 #${env.BUILD_NUMBER}", body: msg
                            }
                            else if(currentBuild.result == 'ABORTED')
                            {
                                println("skip...")
                            }
                            else
                            {
                                if (env.PACKAGERESULT == 'true'){
                                    msg += "打包结果：全部打包成功<br>打包数量：${env.SUCC}<br>"
                                }
                                else{
                                    msg += "打包结果：部分打包成功<br>打包数量：${env.SUCC}<br>"
                                }
                                if("${PLATFORM}" == "android"){
                                    msg += "ab包changelist： ${env.ANDROID_CHANGELIST}"
                                }
                                else{
                                    msg += "ab包changelist： ${env.IOS_CHANGELIST}"
                                }
                                emailext to: sendUserString, subject: "【UI打包流水线】执行成功通知 #${env.BUILD_NUMBER}", body: msg
                            }
                            def check_log_android = "jenkins_log/${env.BUILD_NUMBER}/checkPack_android_${env.BUILD_NUMBER}.log"
                            def checkLogAndroidExists = fileExists "$check_log_android"
                            if(checkLogAndroidExists){
                                archiveArtifacts artifacts: "$check_log_android", followSymlinks: false
                            }
                            def check_log_ios = "jenkins_log/${env.BUILD_NUMBER}/checkPack_ios_${env.BUILD_NUMBER}.log"
                            def checkLogIosExists = fileExists "$check_log_ios"
                            if(checkLogIosExists){
                                archiveArtifacts artifacts: "$check_log_ios", followSymlinks: false
                            }

                            def build_log_android = "jenkins_log/${env.BUILD_NUMBER}/buildPack_android_${env.BUILD_NUMBER}.log"
                            def buildLogAndroidExists = fileExists "$build_log_android"
                            if(buildLogAndroidExists){
                                archiveArtifacts artifacts: "$build_log_android", followSymlinks: false
                            }
                            def build_log_ios = "jenkins_log/${env.BUILD_NUMBER}/buildPack_ios_${env.BUILD_NUMBER}.log"
                            def buildLogIosExists = fileExists "$build_log_ios"
                            if(buildLogIosExists){
                                archiveArtifacts artifacts: "$build_log_ios", followSymlinks: false
                            }
                        }
                    }
                }
            }
        }
    }

    post {
        failure {
            node('master') {
            script {
                def msg = """**结果**: UI打包失败
**pipelineID**：${env.PIPELINEID}
**commitID**：${env.COMMITID}
**提交人**：${env.EMAIL}
\n"""
                workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cbbc94ca-4e9b-496c-b844-3ac9d939f2b1',content: msg
            }
          }
        }

        unstable{
            node('master') {
                script{
                    if (isAndroidPackage == "true" || isIosPackage == "true"){
                        def msg=""""""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """
**pipelineID**：${env.PIPELINEID}
**commitID**：${env.COMMITID}
**提交人**：${env.EMAIL}
**安卓**：
[打包日志](${env.ANDROIDPACKAGERESULTURL})
**ab包 changelist**: ${env.ANDROID_CHANGELIST}
**索引表提交：**: ${env.INDEXCOMMITID}
**打包统计**
**打包数量**: ${env.PACKAGEED}
**成功数量**: ${env.SUCC}
\n"""
                            if(env.ERR == null){
                                msg+="""**失败数量**: ${env.ERR}\n"""
                            }
                            else{
                                msg+="""<font color = "#dc143c">**失败数量**: ${env.ERR}</font>\n"""
                            }
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：
[打包日志](${env.IOSPACKAGERESULTURL})
**ab包 changelist**: ${env.IOS_CHANGELIST}
**索引表提交：**: ios不提交索引表
**打包统计**
**打包数量**: ${env.PACKAGEED}
**成功数量**: ${env.SUCC}
\n"""
                            if(env.ERR == null){
                                msg+="""**失败数量**: ${env.ERR}\n"""
                            }
                            else{
                                msg+="""<font color = "#dc143c">**失败数量**: ${env.ERR}</font>\n"""
                            }
                        }
                        if(env.ERRORINFO == "true"){
                            msg+= """
<font color = "#dc143c">**错误信息**: ${error_info}</font>
[错误信息链接](${env.ANDROIDERRORRESULTURL})
\n"""
                        }
                        workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cbbc94ca-4e9b-496c-b844-3ac9d939f2b1',content: msg
                    }
                }
            }
        }


        success {
            node('master') {
                script{
                if (isAndroidPackage == "true" || isIosPackage == "true"){
                     def msg=""""""
                        if (isAndroidPackage == "true")
                        {
                            msg+= """
**pipelineID**：${env.PIPELINEID}
**commitID**：${env.COMMITID}
**提交人**：${env.EMAIL}
**安卓**：
[打包日志](${env.ANDROIDPACKAGERESULTURL})
**ab包 changelist**: ${env.ANDROID_CHANGELIST}
**索引表提交：**: ${env.INDEXCOMMITID}
**打包统计**
**打包数量**: ${env.PACKAGEED}
**成功数量**: ${env.SUCC}
\n"""
                            if(env.ERR == null){
                                msg+="""**失败数量**: ${env.ERR}\n"""
                            }
                            else{
                                msg+="""<font color = "#dc143c">**失败数量**: ${env.ERR}</font>\n"""
                            }
                        }
                        if (isIosPackage == "true")
                        {
                            msg+="""**ios**：
[打包日志](${env.IOSPACKAGERESULTURL})
**ab包 changelist**: ${env.IOS_CHANGELIST}
**索引表提交：**: ios不提交索引表
**打包统计**
**打包数量**: ${env.PACKAGEED}
**成功数量**: ${env.SUCC}
\n"""
                            if(env.ERR == null){
                                msg+="""**失败数量**: ${env.ERR}\n"""
                            }
                            else{
                                msg+="""<font color = "#dc143c">**失败数量**: ${env.ERR}</font>\n"""
                            }
                      }
                      if(env.ERRORINFO == "true"){
                            msg+= """
<font color = "#dc143c">**错误信息**: ${error_info}</font>
[错误信息链接](${env.ANDROIDERRORRESULTURL})
\n"""
                      }
                      workwx.send url:'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cbbc94ca-4e9b-496c-b844-3ac9d939f2b1',content: msg
                    }
                }
            }
        }
    }
}