import logging
import os, sys, ctypes, stat
import datetime

sys.path.append("..")
from p4_tool.p4_tool import P4Tool

STD_OUTPUT_HANDLE = -11
std_out_handle = ctypes.windll.kernel32.GetStdHandle(STD_OUTPUT_HANDLE)

FOREGROUND_RED = 0x0C
FOREGROUND_WHITE = 0x0F

ENV_STAG = "STAGING"
ENV_PROD = "PRODUCT"
ENV_DEV = "DEVELOP"

CURRENT_ENV = ENV_DEV
P4_PORT = "x5_mobile.p4.com:1666"


def checklog(logfile, num=10000):
    # 保留文件中至少num行
    try:
        if os.path.exists(logfile):
            lines = open(logfile, "r").readlines()
            if len(lines) > num + num * 0.5:  # 当文件大于num+500时，只保留最近num行，避免删除重写文件频率太高。
                os.remove(logfile)
                with open(logfile, "w") as f:
                    for line in lines[-num:]:
                        f.write(line)
            else:
                pass
    except:
        debug_print("Wrong! : [fun]checklog.")


def prtlog(logstr="123", linenum="", maxlines=1000):
    # linenum 指运行时所在行号
    logstr = str(logstr)
    dirname, filename = os.path.split(os.path.abspath(sys.argv[0]))
    logfile = os.path.join(dirname, "log.log")
    checklog(logfile, maxlines)
    with open(logfile, "a+") as f:
        lineheader = "%s %s[%s]: " % (datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), filename, linenum)
        f.write(lineheader + logstr + "\n")
        f.close()


class TextureManager:
    def __init__(self, P4User, P4PWD, P4Root):
        self.P4User = P4User
        self.P4PWD = P4PWD
        self.P4Root = P4Root

    def get_P4View(self, P4Client):
        return self.P4View.replace("@@@", P4Client)

    def get_p4Client(self, P4Client):
        P4PORT = P4_PORT
        P4View = [
            "//x5_mobile/mr/art_release/art_src/texture/... //{}/x5_mobile/mr/art_release/art_src/texture/...".format(P4Client),
            "//x5_mobile/mr/onlineupdate/... //{}/x5_mobile/mr/onlineupdate/...".format(P4Client),
            "//x5_mobile/mr/Resources/art_src/texture/... //{}/x5_mobile/mr/Resources/art_src/texture/...".format(P4Client),
            "//x5_mobile/mr/b/... //{}/x5_mobile/mr/b/...".format(P4Client),
            "//x5_mobile/mr/art_release/undefined/... //{}/x5_mobile/mr/art_release/undefined/...".format(P4Client),
            "//x5mplan/resmg/ui/... //{}/table_rule/...".format(P4Client),
        ]
        # P4View = [
        #     "//x5_mobile/mr/art_release_test/art_src/texture/... //{}/x5_mobile/mr/art_release/art_src/texture/...".format(P4Client),
        #     "//x5_mobile/mr/art_release_test/onlineupdate/... //{}/x5_mobile/mr/onlineupdate/...".format(P4Client),
        #     "//x5_mobile/mr/art_release_test/Resources/art_src/texture/... //{}/x5_mobile/mr/Resources/art_src/texture/...".format(P4Client),
        #     "//x5_mobile/mr/art_release_test/b/... //{}/x5_mobile/mr/b/...".format(P4Client),
        #     "//x5_mobile/mr/art_release_test/undefined/... //{}/x5_mobile/mr/art_release/undefined/...".format(P4Client),
        #     "//x5mplan/resmg/ui/... //{}/table_rule/...".format(P4Client)
        # ]
        p4_obj = P4Tool(self.P4User, P4PORT, self.P4PWD, P4Client, self.P4Root)
        p4_obj.init_p4_view(P4View)
        p4_obj.p4.exception_level = 1
        # return P4Service(self.P4User, P4PORT, self.P4PWD, P4Client, self.P4Root, P4View)
        return p4_obj


def debug_print(text, isPrint=False, isWhite=True):
    logging.info(text)
    prtlog(text)


def set_cmd_text_color(color, handle=std_out_handle):
    Bool = ctypes.windll.kernel32.SetConsoleTextAttribute(handle, color)
    return Bool


def resetColor():
    set_cmd_text_color(FOREGROUND_WHITE)


def printDarkRed(mess):
    set_cmd_text_color(FOREGROUND_RED)
    sys.stdout.write("{}\n".format(mess))
    resetColor()


def del_file(filepath):
    filepath = filepath.replace("/", "\\")
    debug_print("del_file filepath:" + filepath, False)
    os.system("rd/s/q {}".format(filepath))


def copy_file(root, filepath):
    filepath = filepath.replace("/", "\\")
    mydir = filecreation(root)
    dirName = os.path.basename(filepath)
    debug_print("copy_file filepath:" + filepath + " mydir：" + mydir + " dirName：" + dirName, True)
    if not filecreation == -1:
        os.system("echo d y| xcopy {source} {des} /e /y /r".format(source=filepath, des=os.path.join(mydir, dirName)))


def filecreation(root):
    try:
        time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        debug_print("filecreation time:" + time, True)
        mydir = os.path.join(root, time)
        if not os.path.exists(mydir):
            debug_print("filecreation mydir:" + mydir, True)
            os.makedirs(mydir)
    except OSError as e:
        debug_print("filecreation error:" + str(e), True)
        return -1
    return mydir


def writeable_files(filepath):
    debug_print("writeable_files filepath:" + filepath, False)
    if os.path.exists(filepath):
        items = os.listdir(filepath)
        for item in items:
            path = os.path.join(filepath, item)
            if os.path.isdir(path):
                writeable_files(path)
            else:
                os.chmod(path, stat.S_IREAD | stat.S_IWRITE)


def modify_p4_view(p4_obj: P4Tool, client, cdn_data_list):
    p4_view = p4_obj.get_p4_view()
    if not isinstance(p4_view, list):
        raise Exception("invalid p4 view")

    cdn_result = set()
    cdn_texture_view = "//x5_mobile/mr/art_release/art_src/texture"
    cdn_texture_client_view = f"//{client}/x5_mobile/mr/art_release/art_src/texture"
    for cdn_data in cdn_data_list:
        path = cdn_data.get("path")
        if not path.startswith(".."):
            continue

        cdn_view_list = cdn_texture_view.split("/")
        cdn_view_client_list = cdn_texture_client_view.split("/")
        for x in path.split("/"):
            if x == "..":
                cdn_view_list.pop()
                continue

            cdn_view_list.append(x)
            cdn_view_client_list.append(x)

        depot_view = f"{'/'.join(cdn_view_list)}/..."
        p4_view.append(f"{depot_view} {'/'.join(cdn_view_client_list)}/...")
        cdn_result.add(depot_view)

    p4_view = list(set(p4_view))
    fetter_index = 0
    for i in range(0, len(p4_view)):
        if "fetter_cg" in p4_view[i]:
            fetter_index = i

    fetter_view = p4_view.pop(fetter_index)
    p4_view.append(fetter_view)

    logging.info(f"p4 view: {p4_view}")
    if p4_obj.set_p4_view(p4_view) != 0:
        raise Exception("set p4 view error")

    return list(cdn_result)
