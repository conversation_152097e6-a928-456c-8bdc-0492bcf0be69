import json
import os
import shutil
import socket
import stat
import subprocess
import sys
import zipfile
from datetime import datetime
from fnmatch import fnmatchcase as match

import redis
import requests
import xlrd

import commonhelper as helper

P4_PORT = "x5_mobile.p4.com:1666"
p4name = "dgm_jenkins"
p4passwd = "x5m12345"
m_root = ""
ROOT_PATH = "//x5_mobile/..."
file_info = {"time": "", "all_count": "0", "p4_count": 0, "unusual_count": "0", "changelist": "", "sign": "0", "send_user": "", "info_num": 0}
start_time = ""


class RedisConfig(object):
    host = "*************"
    port = 6379
    db = 0
    password = ""
    socket_connect_timeout = 7
    socket_keepalive = True
    decode_responses = True


def texture_distribute(p4name, p4passwd, jenkins_desc, undefined_switch):
    resource_dir = m_root + "\\arthub"
    resource_dir = download_from_arthub(resource_dir)
    if undefined_switch == "true":
        helper.debug_print("undefined下载资源时更改目录")
        resource_dir = m_root + r"\x5_mobile\mr\art_release\undefined"
    if resource_dir == "none":
        helper.debug_print("从arthub没有获得新资源则修改流水线状态")
        file_info["sign"] = "1"
        helper.debug_print("resource_dir = none,file_info[sign]:" + file_info["sign"])
        print(json.dumps(file_info))
        return
    helper.debug_print("开始更新P4表单")
    # 从p4下载和读取表
    p4, client = get_p4(p4name, p4passwd)
    helper.debug_print("get_p4")

    table_rule_dir = "//x5mplan/resmg/ui/"

    cdn_base = "cdn-texture-base.xlsx"
    cdn_rule = "cdn-texture-rule.xlsx"
    trunk_base = "trunk-texture-base.xlsx"
    trunk_rule = "trunk-texture-rule.xlsx"
    hotfix_rule = "hotfix-texture-rule.xlsx"

    result = p4.sync("-f", table_rule_dir + cdn_base)
    helper.debug_print("sync_file:" + table_rule_dir + cdn_base + ",result:" + str(result))
    result = p4.sync("-f", table_rule_dir + cdn_rule)
    helper.debug_print("sync_file:" + table_rule_dir + cdn_rule + ",result:" + str(result))
    result = p4.sync("-f", table_rule_dir + hotfix_rule)
    helper.debug_print("sync_file:" + table_rule_dir + hotfix_rule + ",result:" + str(result))
    result = p4.sync("-f", table_rule_dir + trunk_base)
    helper.debug_print("sync_file:" + table_rule_dir + trunk_base + ",result:" + str(result))
    result = p4.sync("-f", table_rule_dir + trunk_rule)
    helper.debug_print("开始读取表单")
    # 读取CDN、hotfix、版本内表单资源
    table_rule_path = m_root + "\\table_rule\\"
    cdn_data_list = read_table(table_rule_path + cdn_rule)
    cdn_base_data_list = read_table(table_rule_path + cdn_base)
    trunk_data_list = read_table(table_rule_path + trunk_base)
    hotfix_data = xlrd.open_workbook(table_rule_path + hotfix_rule)
    hotfix_data_list = []
    hotfix_table = hotfix_data.sheets()[0]
    hotfix_dir_list = []
    hotfix_version_list = []
    edition_data = xlrd.open_workbook(table_rule_path + trunk_rule)
    edition_data_list = []
    edition_table = edition_data.sheets()[0]

    helper.debug_print("开始组织表单数据 热更1")
    for i in range(1, hotfix_table.nrows):
        temp = {}
        config_data = hotfix_table.row_values(i, 0, 3)
        config_data[0] = type_conversion(config_data[0])
        temp.update({"name": config_data[0].lower().replace("#", "[0-9]") + ".png"})
        temp.update({"version": config_data[1]})
        temp.update({"path": config_data[2]})
        hotfix_data_list.append(temp)

    helper.debug_print("开始组织表单数据 热更2")
    for data in hotfix_data_list:
        temp = str(data["version"])
        if temp not in hotfix_version_list:
            hotfix_version_list.append(temp)
            version = str(data["version"]) + "000"
            pre, next = version[0:1], version[1:3]
            version_main = pre + "." + next + "." + "0"
            if data.get("path"):
                hotfix_dir = "//x5_mobile/mr/onlineupdate/" + version_main + "/" + version + "/art_src/texture/{}/...".format(data["path"])
            else:
                hotfix_dir = "//x5_mobile/mr/onlineupdate/" + version_main + "/" + version + "/art_src/texture/..."
            hotfix_dir_list.append(hotfix_dir)

    helper.debug_print("开始组织表单数据 版本内")
    for i in range(1, edition_table.nrows):
        temp = {}
        config_data = edition_table.row_values(i, 0, 3)
        config_data[0] = type_conversion(config_data[0])
        temp.update({"name": config_data[0].lower().replace("#", "[0-9]") + ".png"})
        temp.update({"path": config_data[2]})
        edition_data_list.append(temp)

    max_branch = find_max_branch()
    hotfix_dir = hotfix_dir_list
    cdn_dir = "//x5_mobile/mr/art_release/art_src/texture/..."
    master_dir = "//x5_mobile/mr/Resources/art_src/texture/..."
    branch_dir = "//x5_mobile/mr/b/{}/art_src/texture/...".format(max_branch)
    undefined_dir = "//x5_mobile/mr/art_release/undefined/..."

    # 修改p4映射
    cdn_view_result = helper.modify_p4_view(p4, client, cdn_data_list)
    cdn_view_result.append(cdn_dir)

    helper.debug_print("开始下载P4资源")
    texture_download(p4, cdn_view_result, hotfix_dir, master_dir, branch_dir, undefined_dir)  # 同步远程p4

    helper.debug_print("开始本地分发资源")
    texture_bak(resource_dir, p4, cdn_data_list, cdn_base_data_list, hotfix_data_list, edition_data_list, trunk_data_list)

    helper.debug_print("开始上传P4资源")
    jenkins_desc_list = list()
    jenkins_desc_list.append("uploader:{}".format(file_info["send_user"]))
    jenkins_desc_list.extend(jenkins_desc.split("|"))
    p4_submit_desc = "\n".join(jenkins_desc_list)
    texture_upload(p4, cdn_dir, hotfix_dir, master_dir, branch_dir, undefined_dir, p4_submit_desc)  # 上传远程p4

    print(json.dumps(file_info))


def type_conversion(data):
    if type(data) == float:
        return str(int(data))
    return str(data)


def read_table(table_dir):
    value_data = xlrd.open_workbook(table_dir)
    data_list = []
    value_table = value_data.sheets()[0]

    for i in range(1, value_table.nrows):
        temp = {}
        config_data = value_table.row_values(i, 0, 3)
        config_data[0] = type_conversion(config_data[0])
        temp.update({"name": config_data[0].lower().replace("#", "[0-9]") + ".png"})
        temp.update({"path": config_data[1]})
        data_list.append(temp)
    return data_list


def download_from_arthub(resource_dir):
    #   记录时间
    format_pattern = "%Y-%m-%d %H:%M:%S"
    if os.path.exists(m_root + "\\time.txt"):
        f = open(m_root + "\\time.txt", "r")
        last_time = f.read()
    else:
        last_time = "2021-10-09 10:07:30"
    helper.debug_print("last_time: {last_time}".format(last_time=last_time))
    now_time = datetime.now().strftime(format_pattern)
    global start_time
    start_time = now_time
    helper.debug_print(f"start_time: {start_time}")
    # parent_id：根目录ID
    parent_id = 307433
    # 筛选：更新时间大于起始时间的文件节点ID
    headers = {"Content-Type": "application/json", "publictoken": "4b477", "Accept": "application/json"}
    data = {
        "parent_id": parent_id,
        "offset": 0,
        "count": 500,
        "filter": [
            {"meta": "updated_date", "condition": "x > {last_time}".format(last_time=last_time)},
            {"meta": "node_type", "condition": "x == asset"},
        ],
        "order": {"meta": "name", "type": "ascend"},
        "is_recursive": True,
    }
    data = json.dumps(data)
    # url = "http://arthub-api.h3d.com.cn/trial/data/openapi/v2/core/get-child-node-id-in-range"
    url = "http://arthub-api.h3d.com.cn/x5mobile/data/openapi/v2/core/get-child-node-id-in-range"
    resp = requests.post(url, headers=headers, data=data)
    helper.debug_print(f"{resp}")
    dic = json.loads(resp.text)
    helper.debug_print("dic: {dic}".format(dic=dic))
    now_time = now_time.replace(":", "-")
    file_info["time"] = str(now_time).replace(" ", "_")
    if "nodes" in dic["result"].keys():
        file_list = dic["result"]["nodes"]
    else:
        helper.debug_print("result is null")
        return "none"
    helper.debug_print("file_list: {file_list}".format(file_list=file_list))
    # 从 redis 获取token
    rt = RedisTool()
    token_key = "dgm_reporter_arthubtoken"
    token = rt.get_redis_value(token_key)
    helper.debug_print("redis arthub token: {}".format(token))
    # 通过 arthub 文件node 获取对应的提交人
    art_srv = ArtHubSrv(token)
    account_result, account_list = art_srv.get_account_name_by_nodes(file_list)
    helper.debug_print("account result: {}, account list:{}".format(account_result, account_list))
    user_info_list = list()
    if account_list:
        res, user_info_list = art_srv.get_account_detail_by_account_names(account_list)
        helper.debug_print("request result:{}, asset submit user:{}".format(res, user_info_list))
    file_info["send_user"] = ",".join(user_info_list)
    # 下载文件
    files = (str(file_list)[1:-1]).replace(" ", "")
    url = "http://arthub-api.h3d.com.cn/x5mobile/client_proxy/openapi/v2/client_proxy/zip-and-download?zip_name=temp&depot=x5mobile&id_list={file_list}".format(
        file_list=files
    )
    # url = "http://arthub-api.h3d.com.cn/trial/client_proxy/openapi/v2/client_proxy/zip-and-download?zip_name=temp&depot=trial&id_list={file_list}".format(
    #     file_list=files)
    resp = requests.get(url, headers=headers)
    if not os.path.exists(m_root + "\\arthub_zip"):
        os.makedirs(m_root + "\\arthub_zip")
    texture_zip = m_root + f"\\arthub_zip\\{now_time}.zip"
    with open(texture_zip, "wb") as code:
        code.write(resp.content)
    # 解压文件
    f = zipfile.ZipFile(texture_zip, "r")
    resource_dir = resource_dir + r"\{}".format(now_time)
    if not os.path.exists(resource_dir):
        os.makedirs(resource_dir)
    for file in f.namelist():
        f.extract(file, resource_dir)
    f.close()
    helper.debug_print(f"{resp}")
    return resource_dir


def texture_bak(resource_dir, p4, cdn_data_list, cdn_base_data_list, hotfix_data_list, edition_data_list, trunk_data_list):
    load_resource = {}  # 所有资源
    cdn_resource = {}  # cdn内资源
    hotfix_resource = {}  # 热更内资源
    edition_resource = {}  # 版本内资源
    undefined_resource = {}  # 无规则资源
    repeat_resource = {}  # 重名资源
    delete_resource = {}  # 删除资源
    blank_resource = {}  # 包含空格资源
    max_branch = find_max_branch()
    cdn_path = m_root + r"\x5_mobile\mr\art_release\art_src\texture"
    master_path = m_root + r"\x5_mobile\mr\Resources\art_src\texture"
    branch_path = m_root + r"\x5_mobile\mr\b\{}\art_src\texture".format(max_branch)
    helper.debug_print(f"本次分发本地资源路径为:{resource_dir}")
    for filepath, _, filenames in os.walk(resource_dir):
        for filename in filenames:
            # if filename.endswith("945900028_hp.png"):
            #     continue

            filename = filename.lower()
            # 重名资源进提示名单
            if filename in load_resource.keys():
                os.chmod(load_resource[filename], stat.S_IWRITE)
                os.remove(load_resource[filename])
                repeat_resource[filename] = load_resource[filename]
                del load_resource[filename]

            # 空格直接进undefined
            elif " " in filename:
                blank_resource[filename] = "包含空格，未进行本地分发"

            # 匹配CDN表单规则
            elif CDN_table_rule(cdn_data_list, cdn_path, load_resource, cdn_resource, filename, filepath) == True:
                continue

            # 匹配CDN基础表资源
            elif common_table_rule(cdn_base_data_list, cdn_path, load_resource, cdn_resource, filename, filepath) == True:
                continue

            # 匹配hotfix表单规则
            elif hotfix_table_rule(hotfix_data_list, edition_data_list, trunk_data_list, load_resource, hotfix_resource, filename, filepath) == True:
                continue

            # 匹配版本内表单规则
            elif edition_table_rule(edition_data_list, master_path, branch_path, load_resource, edition_resource, filename, filepath) == True:
                continue

            # 匹配版本内基础资源
            elif common_table_rule(trunk_data_list, master_path, load_resource, edition_resource, filename, filepath) == True:
                common_table_rule(trunk_data_list, branch_path, load_resource, edition_resource, filename, filepath)
                continue

            else:
                v = "\\x5_mobile\\mr\\art_release\\undefined"
                if undefined_switch != "true":
                    copy_resource(m_root, v, load_resource, undefined_resource, filepath, filename)
                else:
                    load_resource[filename] = m_root + v + "\\" + filename.lower()
                    undefined_resource[filename] = m_root + v + "\\" + filename.lower()
    unusual_count = undefined_resource.__len__() + repeat_resource.__len__()
    texture_check(load_resource, undefined_resource, delete_resource, p4)
    file_info["all_count"] = str(load_resource.__len__())
    file_info["unusual_count"] = str(unusual_count)
    file_info["blank_resource"] = len(blank_resource)
    log_generation(cdn_resource, hotfix_resource, edition_resource, undefined_resource, repeat_resource, delete_resource, blank_resource)


def texture_check(load_resource, undefined_resource, delete_resource, p4):
    undefined_dir = r"//x5_mobile/mr/art_release/undefined/"
    for filename in load_resource:
        if filename not in undefined_resource:
            match_rule = m_root + "\\x5_mobile\\mr\\art_release\\undefined\\" + filename
            if os.path.exists(match_rule):
                p4.delete(undefined_dir + filename)
                helper.debug_print(f"本次从undefined删除的资源为:{undefined_dir + filename}")
                delete_resource[filename] = match_rule


def log_generation(cdn_resource, hotfix_resource, edition_resource, undefined_resource, repeat_resource, delete_resource, blank_resource):
    now_time = file_info["time"]
    log_dir = m_root + "\log"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    error_resource = {}
    for i in undefined_resource.keys():
        if i.endswith(".png") == False:
            error_resource[i] = undefined_resource[i]
    f = open(log_dir + f"\\{now_time}_record.txt", "w+")
    f.write("本地资源的分发情况如下：\n\n")
    f.write("1.未定义规则的资源分发情况：\n")
    write_txt(undefined_resource, f)
    if error_resource != {}:
        f.write("****以下文件格式错误，非PNG格式请注意：****\n")
        write_txt(error_resource, f)
    if blank_resource != {}:
        f.write("****以下文件包含空格，未进行本地分发请注意：****\n")
        write_txt(blank_resource, f)
    f.write("2.重名资源的分发情况：\n")
    write_txt(repeat_resource, f)
    f.write("3.新符合规则资源的分发情况：\n")
    write_txt(delete_resource, f)
    f.write("4.cdn内资源的分发情况：\n")
    write_txt(cdn_resource, f)
    f.write("5.热更内资源的分发情况：\n")
    write_txt(hotfix_resource, f)
    f.write("6.版本内资源的分发情况：\n")
    write_txt(edition_resource, f)
    f.close()


def write_txt(dic, f):
    f.write(f"资源数量：{dic.__len__()}\n")
    if dic.__len__() != 0:
        for i, j in dic.items():
            f.write(f"文件名：{i}  文件路径：{j}\n")
    f.write("\n")


def CDN_table_rule(data_list, p4_dir, load_resource, cdn_resource, filename, filepath):
    dir_name = ""
    for data in data_list:
        if match(filename, data["name"]):
            if filename.startswith("zs_9261"):
                dir_name = "\\" + filename[3:12]

            path = data["path"]
            if path.startswith("../"):
                path = path.replace("../", "")

            v = "\\" + path + dir_name
            copy_resource(p4_dir, v, load_resource, cdn_resource, filepath, filename)
            return True
    return False


def common_table_rule(data_list, p4_dir, load_resource, type_resource, filename, filepath):
    for data in data_list:
        if filename == data["name"]:
            v = "\\" + data["path"]
            copy_resource(p4_dir, v, load_resource, type_resource, filepath, filename)
            return True
    return False


def copy_resource(p4_dir, v, load_resource, type_resource, filepath, filename):
    helper.debug_print(
        "copy_resource params, p4_dir: {}, v: {}, load_resource: {}, type_resource: {}, filepath: {}, filename: {}".format(
            p4_dir, v, load_resource, type_resource, filepath, filename
        )
    )
    if not os.path.exists(p4_dir + v):
        os.makedirs(p4_dir + v)
    copy_file(filepath + "\\" + filename, p4_dir + v + "\\" + filename.lower())
    load_resource[filename] = p4_dir + v + "\\" + filename.lower()
    type_resource[filename] = p4_dir + v + "\\" + filename.lower()


def copy_file(ff, file):
    if os.path.exists(file):
        try:
            os.remove(file)
        except:
            os.chmod(file, stat.S_IWRITE)
            os.remove(file)
    shutil.copy(ff, file)


def hotfix_table_rule(data_list, data_list1, data_list2, load_resource, hotfix_resource, filename, filepath):
    # helper.debug_print(
    #     '复制热更资源参数, data_list: {}, data_list1: {}, data_list2: {}, load_resource: {}, hotfix_resource: {}, filename: {}, filepath: {}'.format(
    #         data_list, data_list1, data_list2, load_resource, hotfix_resource, filename, filepath
    #     )
    # )
    for data in data_list:
        if filename == data["name"]:
            version = str(data["version"]) + "000"
            pre = version[0:1]
            next = version[1:3]
            version_main = pre + "." + next + "." + "0"
            hotfix_path = m_root + "\\x5_mobile\\mr\\onlineupdate\\" + version_main + "\\" + version + r"\art_src\texture"
            path = ""
            for data1 in data_list1:
                if match(filename, data1["name"]):
                    path = data1["path"]
            for data2 in data_list2:
                if filename == data2["name"]:
                    path = data2["path"]
            v = "\\" + path
            copy_resource(hotfix_path, v, load_resource, hotfix_resource, filepath, filename)
            return True
    return False


def edition_table_rule(data_list, master_dir, branch_dir, load_resource, edition_resource, filename, filepath):
    max_branch = find_max_branch()
    max_branch = max_branch.replace(".", "")
    for data in data_list:
        if match(filename, data["name"]):
            if filename[0:4].isdigit() and filename[4:5] == "_":
                version = data["name"]
                version_main = version[0:4]
                v = "\\" + data["path"]
                if int(version_main) > int(max_branch):
                    copy_resource(master_dir, v, load_resource, edition_resource, filepath, filename)
                else:
                    copy_resource(branch_dir, v, load_resource, edition_resource, filepath, filename)
                return True
            else:
                v = "\\" + data["path"]
                copy_resource(master_dir, v, load_resource, edition_resource, filepath, filename)
                copy_resource(branch_dir, v, load_resource, edition_resource, filepath, filename)
                return True
    return False


# 获取所有分支需要的路径
gitlab_host = "https://x5mobile-gitlab.h3d.com.cn/"
git_project_path_encode = "dgm/x5mconfig"
git_api_url_get_branches = gitlab_host + "api/v4/projects/%s/repository/branches?per_page=100&page=%s"


# 寻找git库中所有的分支
def get_repository_all_branch_names(git_project_path_encode, page, res):
    url = git_api_url_get_branches % (git_project_path_encode, page)
    curl_cmd = 'curl --header "PRIVATE-TOKEN: o-yNgwVADiQ8CicYdt_4" --ssl-no-revoke "' + url + '"'
    curlRes = subprocess.Popen(curl_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE).stdout.read().decode("utf-8")
    if curlRes == "[]":
        return res
    curlRes = curlRes.replace("null", "None").replace("true", "True").replace("false", "False")
    # logging.info(f"curl result: {curlRes}")
    jsonObj = eval(curlRes)
    for v in jsonObj:
        res.append(v.get("name"))
    page += 1
    get_repository_all_branch_names(git_project_path_encode, page, res)


# 寻找最大分支
def find_max_branch():
    branches = []
    get_repository_all_branch_names(git_project_path_encode.replace("/", "%2F"), 1, branches)
    max_branch = ""
    max_first = 0
    max_second = 0
    for res in branches:
        if res[0].isdigit():
            try:
                elem = res.split(".")
                if elem[2] != "0":
                    continue
                first = int(elem[0])
                second = int(elem[1])
            except Exception:
                pass
            else:
                if first > max_first and second > max_second:
                    first, second, max_branch = max_first, max_second, res
                elif first == max_first and second > max_second:
                    second, max_branch = max_second, res
        else:
            continue
    return max_branch


def get_p4(P4User, P4PWD):
    texture_manager = helper.TextureManager(P4User, P4PWD, m_root)
    hostname = socket.gethostname()
    ip = socket.gethostbyname(hostname)
    # P4Client = str(ip) + "_texture_test"
    P4Client = str(ip) + "_texture"
    p4 = texture_manager.get_p4Client(P4Client)
    return p4, P4Client


def texture_download(p4, cdn_dir, hotfix_dir, master_dir, branch_dir, undefined_dir):
    final_result = False
    status_cdn = 0
    for x in cdn_dir:
        helper.debug_print("开始下载P4资源-cdn:" + x)
        status_cdn = p4.sync("-f", x)

    status_hotfix = 0
    for i in hotfix_dir:
        helper.debug_print("开始下载P4资源-热更:" + i)
        status_hotfix = p4.sync("-f", i)

    helper.debug_print("开始下载P4资源-主支:" + master_dir)
    status_master = p4.sync("-f", master_dir)
    helper.debug_print("开始下载P4资源-分支:" + branch_dir)
    status_branch = p4.sync("-f", branch_dir)
    helper.debug_print("开始下载P4资源-未定义:" + undefined_dir)
    p4.clean("-a", undefined_dir)
    status_undefined = p4.sync("-f", undefined_dir)

    # helper.debug_print('status_cdn:{},status_hotfix:{},status_master:{},status_branch:{},status_undefined:{}'.format(
    #     status_cdn,status_hotfix,status_master,status_branch,status_undefined))

    if status_cdn != -1 and status_hotfix != -1 and status_master != -1 and status_branch != -1 and status_undefined != -1:
        result_str = "下载成功"
        final_result = True
    else:
        result_str = "下载失败"
    data = "result={result}&result_str={result_str}".format(result=final_result, result_str=result_str)
    helper.debug_print("texture_download 下载完成 data:{data} ".format(data=data), False)


# 图片迁入
def texture_upload(p4, cdn_dir, hotfix_dir, master_dir, branch_dir, undefined_dir, p4_submit_desc):
    status_list = []
    status_hotfix_list = []
    status_cdn = p4.reconcile("-f", "-c", "default", "{}".format(cdn_dir))
    status_list.append(status_cdn)
    for i in hotfix_dir:
        status = p4.reconcile("-f", "-c", "default", "{}".format(i))
        status_hotfix_list.append(status)
        helper.debug_print(f"status:{status},hotfix_dir:{i}")
    status_master = p4.reconcile("-f", "-c", "default", "{}".format(master_dir))
    status_list.append(status_master)
    status_branch = p4.reconcile("-f", "-c", "default", "{}".format(branch_dir))
    status_list.append(status_branch)
    status_undefined = p4.reconcile("-f", "-c", "default", "{}".format(undefined_dir))
    status_list.append(status_undefined)
    result_info = []
    status_sign = 1
    # 判断需要提交p4的资源及提交次数
    for i in status_list:
        if i == 0:
            status_sign = 0
            file_info["info_num"] += 1
    # 单独判断热更目录，因为热更里可能会涉及多条p4提交路径
    for j in status_hotfix_list:
        if j == 0:
            status_sign = 0
            file_info["info_num"] += 1
            break
    # undefine目录无需打包需剔除
    if status_undefined == 0:
        file_info["info_num"] -= 1
    file_info["info_num"] = str(file_info["info_num"])
    helper.debug_print(f'需打包的流水线条数: {file_info["info_num"]}')

    if not status_sign == 0:
        for j in status_list + status_hotfix_list:
            if not j == -2:
                status_sign = -1

    if status_sign == 0:
        result_info = p4.submit("-f", "submitunchanged", "-d", p4_submit_desc)
        helper.debug_print(f"result_info:{result_info}")
        if isinstance(result_info, list):
            # if result_info != -1:
            file_info["changelist"] = str(result_info[0]["change"])
            # 计算上传p4不重复的文件名
            temp_dic = {}
            num = result_info[0]["locked"]
            helper.debug_print(f"p4改动的文件数目: {result_info[0]['locked']}")
            for i in result_info[1 : int(num) + 1]:
                j = i["depotFile"].split("/")[-1]
                if j not in temp_dic.values():
                    temp_dic[file_info["p4_count"]] = j
                    file_info["p4_count"] += 1
            file_info["p4_count"] = str(file_info["p4_count"])
            log_supplement("实际上传p4资源情况如下:", result_info)
            if undefined_switch != True:
                log_time()
            helper.debug_print("start_time: {start_time}".format(start_time=start_time))
        else:
            p4.revert("-w", ROOT_PATH)
            file_info["sign"] = "-1"
            helper.debug_print("p4提交文件异常，请联系程序查问题")
            log_supplement("p4提交文件异常，请联系程序查问题", result_info)
            if result_info != -2:
                raise Exception(f"p4提交文件异常，请联系程序查问题, {result_info}")
    elif status_sign == -1:
        helper.debug_print("p4添加文件异常，请联系程序查问题")
        log_supplement("p4添加文件异常，请联系程序查问题", result_info)
        raise Exception(f"p4添加文件异常，请联系程序查问题, {result_info}")
    else:
        result_str = "没有资源上传p4,图片与P4上最新版本相同"
        log_supplement(result_str, result_info)
        if undefined_switch != True:
            log_time()
        helper.debug_print("result_str:{result_str}".format(result_str=result_str))


def log_time():
    helper.debug_print("记录本次提交时间")
    f = open(m_root + "\\time.txt", "w+")
    f.write(str(start_time))
    f.close()


def log_supplement(title, content):
    if isinstance(content, list):
        # 记录实际上传资源
        now_time = file_info["time"]
        log_dir = m_root + "\log"
        f = open(log_dir + f"\\{now_time}_record.txt", "a")
        f.write(f"\n{title}\n\n")
        if content != [] and content != -1:
            for data in content:
                f.write("{}\n".format(data))
        f.close()


class RedisTool(object):
    """
    redis
    """

    def __init__(self, redis_server=None):
        if redis_server is None:
            redis_server = self.connect_redis()
        self.rc = redis_server

    def get_redis_value(self, key):
        """
        通过key获取值
        """
        return self.rc.get(key)

    def set_redis_value(self, key, value=None, expire=None):
        """
        通过key设置值，不过期
        """
        return self.rc.setex(key, expire, value)

    def connect_redis(self):
        """
        连接redis
        """
        redis_conf = RedisConfig
        try:
            rs = redis.Redis(
                host=redis_conf.host,
                port=redis_conf.port,
                db=redis_conf.db,
                password=redis_conf.password,
                socket_connect_timeout=redis_conf.socket_connect_timeout,
                socket_keepalive=redis_conf.socket_keepalive,
                decode_responses=redis_conf.decode_responses,
                max_connections=32,
            )
        except Exception as e:
            raise ValueError(str(e))
        else:
            return rs


class ArtHubSrv(object):
    """
    art hub 类
    """

    def __init__(self, token=None):
        self.arthub_token = token

    def get_account_name_by_nodes(self, node_list):
        """
        获取资源提交人的 account name
        """
        url = "http://arthub-api.h3d.com.cn/x5mobile/data/openapi/v2/core/get-node-brief-by-id"
        data = {"ids": node_list, "meta": ["id", "last_modifier"]}
        helper.debug_print("request url:{}, request data:{}".format(url, data))
        try:
            response = requests.post(url, data=json.dumps(data), headers=self.request_headers(), timeout=10)
            res_data = response.json()
            helper.debug_print("get account name response data: {}".format(res_data))
            if "error" in res_data:
                helper.debug_print("request arthub error, error msg: {}".format(res_data["error"]))
            account_list = [i["last_modifier"] for i in res_data["result"]["items"]]
        except Exception as exp:
            helper.debug_print("request arthub exception, error msg: {}".format(str(exp)))
            return False, None
        return True, account_list

    def get_account_detail_by_account_names(self, account_name_list):
        """
        获取 资源提交人详情
        """
        url = "http://arthub-api.h3d.com.cn/account/account/openapi/v3/core/get-account-detail-by-account-name"
        data = {"account_name": account_name_list, "department_array": []}
        helper.debug_print("request url:{}, request data:{}".format(url, data))
        try:
            response = requests.post(url, data=json.dumps(data), headers=self.request_headers(), timeout=10)
            res_data = response.json()
            helper.debug_print("get account detail response data: {}".format(res_data))
            if "error" in res_data:
                helper.debug_print("request arthub error, error msg: {}".format(res_data["error"]))
            user_info = list()
            for item in res_data["result"]:
                account = item.get("ldap_name")
                if account and account not in user_info:
                    user_info.append(account)
        except Exception as exp:
            helper.debug_print("request arthub exception, error msg: {}".format(str(exp)))
            return False, None
        return True, user_info

    def request_headers(self):
        """
        返回 请求头
        """
        headers = {"Content-Type": "application/json"}
        if self.arthub_token:
            headers["arthubtoken"] = self.arthub_token
        return headers


if __name__ == "__main__":
    """
    主功能：    arthub图片上传p4
    附功能：    p4图片下undefined目录重上传
    m_root：   图片分发的根目录
    jenkins_decs:    获取arthub图片上传人信息
    undefined_switch：   undefined目录是否重上传开关
    """
    m_root = sys.argv[1]
    jenkins_desc = sys.argv[2] if sys.argv[2] else ""
    undefined_switch = sys.argv[3]
    texture_distribute(p4name, p4passwd, jenkins_desc, undefined_switch)
