@Library('h3d_libs') _
import groovy.json.JsonSlurper
def messageSendUserList = ["<EMAIL>", "<EMAIL>", "ji<PERSON><PERSON><PERSON><PERSON>@h3d.com.cn", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "wang<PERSON><EMAIL>"]
def result_map={}

def getBuildUserInfo() {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo "获取不到构建的用户邮箱"
            env.BUILD_USER_EMAIL = ""
        }
    }
}


pipeline {
    agent {
        node{
            label "arthub_texture"
        }
    }
    options {
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         booleanParam(name: 'p4_undefined', defaultValue: false, description: '是否重新分发p4图片下undefined目录')
    }
    // 因为四点到五点p4需要备份数据，流水线会执行失败
    triggers {
      cron('H/30 0-3,5-23 * * *\nH/10 21-23 * * *')
    }
    stages {
        stage('arthub图片上传p4'){
            steps {
                // dir('pyframe-pipeline/project/x5m/art_package/scripts/arthub_texture'){
                dir('gitlab/project/x5m/art_package/scripts/arthub_texture'){
                    script{
                        getBuildUserInfo()
                        def submitP4Desc = "Identity:#Jenkins#${env.JOB_NAME}#${env.BUILD_NUMBER}|link:${env.BUILD_URL}console"
                        echo "${submitP4Desc}"
                        def file_info = bat(returnStdout: true, script: "python texture_upload.py ${env.workspace} \"${submitP4Desc}\" ${params.p4_undefined}").split('\n')
                        println("file_info[2]:" + file_info[2])
                        result_map = readJSON text: file_info[2]
                        txt_time= result_map.time
                        env.SEND_MESSAGE_USER_INFO = result_map.send_user
                        // dir('../../../../'){
                        dir('../../../../../../'){
                            def build_log = "log/${txt_time}_record.txt"
                            def buildLogExists = fileExists "$build_log"
                            if(buildLogExists){
                                archiveArtifacts artifacts: "$build_log", followSymlinks: false
                            }
                        }
                        if(result_map.sign == "1")
                        {
                            currentBuild.result = 'ABORTED'
                        }
                        else if(result_map.sign == "-1")
                        {
                            currentBuild.result = 'ABORTED'
                        }
                        else if(result_map.blank_resource > 0)
                        {
                            currentBuild.result = 'UNSTABLE'
                        }
                        //定义信息模板
                        env.MSGINFO = """
**arthub得到的图片数量:** ${result_map.all_count}
**实际上传p4的图片数量:** ${result_map.p4_count}
"""
                        env.LOGBACKINFO = """
[分发报告](http://jenkins-x5mobile.h3d.com.cn/job/art_package_distrib/job/arthub_texture_upload/${env.BUILD_NUMBER}/artifact/log/${txt_time}_record.txt)"""
                        if (result_map.unusual_count != '0'){
                            env.MSGINFO+= """
<font color = "#dc143c">**异常图片数量:**${result_map.unusual_count}</font>
"""
                        }
                        env.MSGINFO+= """
${env.LOGBACKINFO}"""
                    }
                }
            }
        }
    }
    post {
        failure {
            script {
                if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                    if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                        messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    }
                }
                if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                    messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                }
                def sendUserString = messageSendUserList.join(",")
                workwx.send url:"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb",content: env.MSGINFO
                if (sendUserString != "") {
                    emailext to: "${sendUserString}", subject: 'jenkins-arthub图片上传P4失败', body: env.MSGINFO
                }
            }
        }
        unstable {
            script{
                if (result_map.info_num!='0'){
                    env.MSGINFO += """
**后续将会收到${result_map.info_num}条完成通知，请以完成通知中的changelist提交track**
"""
                }
                if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                    if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                        messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    }
                }
                if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                    messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                }
                def sendUserString = messageSendUserList.join(",")
                workwx.send url:"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb",content:env.MSGINFO
                if (sendUserString != "") {
                    emailext to: "${sendUserString}", subject: 'jenkins-arthub图片上传P4部分失败', body: env.MSGINFO
                }
            }
        }
        success {
            script{
                env.MSGINFO = """
**后续将会收到${result_map.info_num}条完成通知，请以完成通知中的changelist提交track**
"""
                if (env.BUILD_USER_EMAIL && env.BUILD_USER_EMAIL != "") {
                    if (!messageSendUserList.contains("${env.BUILD_USER_EMAIL}".toString())) {
                        messageSendUserList.add("${env.BUILD_USER_EMAIL}".toString())
                    }
                }
                if (env.SEND_MESSAGE_USER_INFO && env.SEND_MESSAGE_USER_INFO != "") {
                    messageSendUserList.add(env.SEND_MESSAGE_USER_INFO)
                }
                def sendUserString = messageSendUserList.join(",")
                if (sendUserString != "") {
                    emailext to: "${sendUserString}", subject: 'jenkins-arthub图片上传打包流程开始', body: env.MSGINFO
                }
            }
        }
    }
}
