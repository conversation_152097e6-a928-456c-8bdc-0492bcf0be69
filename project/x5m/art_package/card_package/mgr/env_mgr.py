# coding=utf-8
from frame import env


class BkEnvMgr:
    def __init__(self) -> None:
        pass

    def get_texture_android(self):
        return env.get("TEXTURE_ANDROID")

    def get_texture_ios(self):
        return env.get("TEXTURE_IOS")

    def get_atlas_android(self):
        return env.get("ATLAS_ANDROID")

    def get_atlas_ios(self):
        return env.get("ATLAS_IOS")

    def get_uploader(self):
        return env.get("ARTHUB_UPLOADER")

    def get_maintainer(self):
        return env.get("MAINTAINER")


class EnvMgr:
    def __init__(self) -> None:
        pass

    def set_nexus_url(self, nexus_url: str):
        env.set({"nexus_url": nexus_url})

    def get_nexus_url(self):
        return env.get("nexus_url")


bk_env_mgr = BkEnvMgr()
env_mgr = EnvMgr()
