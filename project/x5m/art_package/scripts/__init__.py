import configparser

# coding=utf-8
from enum import Enum

import click
from p4_tool.git_tool import GitTool, GitToolManager
from p4_tool.p4_tool import P4Tool, P4ToolManager

from config import *


class LanguageEnum(Enum):
    # python3.6.8 没有 StrEnum
    chin_simp = "chin_simp"
    chin_trad = "chin_trad"


# cli = click.Group()
cli = click.Group(context_settings={"token_normalize_func": lambda name: name.replace("_", "-")})

config = configparser.ConfigParser(allow_no_value=True)
config.read("../config/config.ini")

p4tool = P4Tool(p4_user, p4_host, p4_passwd, "", "")

p4_tool_mgr = P4ToolManager()
p4_tool_mgr.register("x5_mobile", p4tool)
p4_tool_mgr.register("x5m", p4tool)

git_tool_cdn = GitTool("dgm%2Fx5mconfig")
git_tool_week = GitTool("dgm%2Fx5mweek")
git_tool_mgr = GitToolManager()
git_tool_mgr.register("x5mconfig", git_tool_cdn)
git_tool_mgr.register("x5mweek", git_tool_week)
