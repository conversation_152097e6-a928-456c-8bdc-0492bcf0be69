# -*- coding: utf-8 -*-
# @Time    : 2021-08-11 17:11
# <AUTHOR> z<PERSON><PERSON><PERSON>@h3d.com.cn
# @File    : cdn_tool.py
# @Version : 1.0

import time
import hmac
import hashlib
import requests

# 域名前缀
BUCKET_NAME = "x5music"

# 超时时间 单位秒
TIMEOUT_TIME = 80000

# 测试CDN配置信息
# CDN_AUTH_CONFIG = {
#     "SecretID": "QmFzZTY0IGlzIGEgZ2VuZXJp",
#     "SecretKey": "AKIDZfbOA78asKUYBcXFrJD0a1ICvR98JM",
#     "AppID": "1234432101",
#     "UrlEndName": ".sztest.file.tencent-cloud.com",
# }


# 正式CDN配置信息
CDN_AUTH_CONFIG = {
    "SecretID": "23ZqJas28jG94Sav1KEz6OM6",
    "SecretKey": "4Gtr3KYOdBMdKYxcCgbus5PjwoQ0e48jKB",
    "AppID": "40020",
    "UrlEndName": ".sh.gfp.tencent-cloud.com",
}


class CDNUtils(object):
    """
    CDN 操作工具类
    """

    def __init__(self, *args, **kwargs):
        self.port = kwargs["port"] if "port" in kwargs else 80
        if "host" in kwargs:
            self.uri = "http://{}:{}".format(kwargs["host"], self.port)
        if "uri" in kwargs:
            self.uri = kwargs["uri"]

    def upload(self, data, params=None, headers=None):
        """
        上传文件
        """
        try:
            upload_response = requests.put(self.uri, params=params, data=data, headers=headers)
        except Exception as exp:
            return False, str(exp)
        else:
            if str(upload_response.status_code).startswith("2"):
                return True, "success"
            return False, upload_response.text

    def upload_lan_cdn(self, data, file_name):
        """
        上传到内网cdn
        """
        uri = "http://**************:8103"
        self.uri = "{}/{}".format(uri, file_name)
        headers = {"Content-Type": "application/octet-stream"}
        return self.upload(data=data, headers=headers)

    def upload_public_cdn(self, data, file_name):
        """
        上传外网CDN
        """
        self.uri = "http://{}-{}{}/{}".format(BUCKET_NAME, CDN_AUTH_CONFIG["AppID"], CDN_AUTH_CONFIG["UrlEndName"], file_name)
        # 对请求体携带数据加密
        data_sha1 = self._encrypt_request_data(data)
        # 获取请求认证头信息
        auth_sign = self.authorization(data_sha1, file_name, "put")

        headers = {"Authorization": auth_sign, "x-cos-content-sha1": data_sha1}
        return self.upload(data=data, headers=headers)

    def _encrypt_request_data(self, data):
        """
        加密数据 使用 sha1加密
        """
        if isinstance(data, str):
            data = data.encode()
        sha1obj = hashlib.sha1()
        sha1obj.update(data)
        return sha1obj.hexdigest()

    def authorization(self, data_sha1, file_name, request_method, params=None):
        """
        认证
        """
        current_time = int(time.time())
        sign_time = current_time + TIMEOUT_TIME
        key_time = "{};{}".format(current_time, sign_time)
        sign_key = self._encrypt_with_secret_key(CDN_AUTH_CONFIG["SecretKey"], key_time)
        format_header = "host={}-{}{}".format(BUCKET_NAME, CDN_AUTH_CONFIG["AppID"], CDN_AUTH_CONFIG["UrlEndName"])
        if data_sha1:
            format_header += "&x-cos-content-sha1={}".format(data_sha1)
        route_path = "/{}".format(file_name) if file_name else "/"
        format_params = "" if params else ""
        format_string = "{}\n{}\n{}\n{}\n".format(request_method, route_path, format_params, format_header)
        format_string_sha1_hash = self._encrypt_request_data(format_string)
        string_to_sign = "sha1\n{}\n{}\n".format(key_time, format_string_sha1_hash)
        signature = self._encrypt_with_secret_key(sign_key, string_to_sign)
        # Authorization
        auth_sign = "q-sign-algorithm=sha1&q-ak={}&q-sign-time={}&q-key-time={}&q-header-list=host".format(
            CDN_AUTH_CONFIG["SecretID"], key_time, key_time
        )
        if data_sha1 is not None:
            auth_sign += ";x-cos-content-sha1"

        auth_sign += "&q-url-param-list=&q-signature={}".format(signature)
        return auth_sign

    def _encrypt_with_secret_key(self, secret_key, data):
        """
        使用 secret key 加密 data数据
        """
        if isinstance(secret_key, str):
            secret_key = secret_key.encode()
        if isinstance(data, str):
            data = data.encode()
        my_sign = hmac.new(secret_key, data, hashlib.sha1).hexdigest()
        return my_sign


if __name__ == "__main__":
    cu = CDNUtils()
    # import os
    # # path = "E:\\x5_mobile\\x5_mobile\\mr\\art_release\\pack\\levels\\2021-08-20-16-43-23"
    # # path = "E:\\x5_mobile\\x5_mobile\\mr\\art_release\\pack\\levels\\2021-08-20-16-51-42"
    # path = "E:\\x5_mobile\\x5_mobile\\mr\\art_release\\pack\\levels\\2021-08-23-13-16-23"
    # file_list = os.listdir(path)
    # print(file_list)
    # for item in file_list:
    #     file_path = os.path.join(path, item)
    #     print(file_path)
    #     with open(file_path, mode="rb") as fp:
    #         files = fp.read()
    #     a, b = cu.upload_lan_cdn(files, item)
    #     print(a, b)
    # cu.upload_public_cdn(files, "test_20210811.txt")
