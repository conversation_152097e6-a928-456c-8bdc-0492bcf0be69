import os, stat
import csv
import shutil

import logging
import argparse
from .p4_tool.p4_tool import P4Tool

logger = logging.getLogger("jenkins")


class DynamicDataClearTool(object):
    def __init__(self, input_version, input_clear_dir):
        self.p4_cdn_version = input_version
        self.p4_path = "//x5m/res/cdn/pack/"
        self.clear_dir_path = input_clear_dir
        self.workspace_p4_path = "{}{}/".format(self.p4_path, format(self.p4_cdn_version))
        versions = self.p4_cdn_version.split("v")

        # 生成索引文件名
        self.input_main_ver = "v{}".format(versions[1])
        self.input_sub_ver = "v{}".format(versions[2])
        self.res_ver_list_file = "res_list_{}_{}.csv".format(self.input_main_ver, self.input_sub_ver)

        # 需要清理的android和IOS目录
        self.android_del_dirs = "{}{}".format(self.clear_dir_path, "android")
        self.ios_del_dirs = "{}{}".format(self.clear_dir_path, "ios")

        self.android_need_del_paths = list()
        self.ios_need_del_paths = list()
        self.p4_version_dirs = list()
        self.p4_host = "x5_mobile.p4.com:1666"
        self.p4_user = "dgm_jenkins"
        self.p4_passwd = "x5m12345"
        self.p4_workspace = "dgm_jenkins_clear_dirs"
        self.logger = logger

    def parse_version_dir(self, dir_name):
        tmp_version_paths = dir_name.split("/")
        tmp_version = tmp_version_paths[len(tmp_version_paths) - 1]
        tmp_versions = tmp_version.split("v")
        main_version = int(tmp_versions[1])
        sub_version = int(tmp_versions[2])
        return "v{}/v{}".format(main_version, sub_version)

    def rec_p4_version_dirs(self, dirs):
        self.logger_print("统计并且记录P4上的正式资源目录的版本号，目录：{}".format(dirs))
        for dicts in dirs:
            tmp_version = self.parse_version_dir(dicts["dir"])
            if self.p4_version_dirs.count(tmp_version) == 0:
                self.p4_version_dirs.append(tmp_version)

        self.logger_print(len(self.p4_version_dirs))

    def logger_print(self, loginfo):
        # self.logger.info(loginfo)
        print(loginfo)

    def get_p4_tool(self):
        return P4Tool(self.p4_user, self.p4_host, self.p4_passwd, self.p4_workspace, "")

    def get_latest_version(self):
        """
        获取p4上的指定版本号的资源索引表
        """
        p4tool = self.get_p4_tool()
        # 记录p4上的资源版本号目录
        self.rec_p4_version_dirs(p4tool.dirs("{}*".format(self.p4_path)))

        # 资源索引表
        self.ios_res_list_path = "{}{}{}".format(self.workspace_p4_path, "ios/", self.res_ver_list_file)
        self.android_res_list_path = "{}{}{}".format(self.workspace_p4_path, "android/", self.res_ver_list_file)

        self.logger_print("强更资源索引表")
        self.workspace_ios_p4_path = p4tool.sync("-f", self.ios_res_list_path)
        self.workspace_android_p4_path = p4tool.sync("-f", self.android_res_list_path)

        self.logger_print("转换成本地路径的资源")
        self.ios_res_list_path = self.workspace_ios_p4_path[0]["clientFile"].replace("\\", "/")
        self.android_res_list_path = self.workspace_android_p4_path[0]["clientFile"].replace("\\", "/")

        # 获取本地的工作空间目录
        self.workspace_p4_path = "{}{}".format(self.ios_res_list_path.split(self.p4_cdn_version)[0], self.p4_cdn_version)
        self.logger_print(self.workspace_p4_path)
        return True

    def read_res_list_csv_file(self, path):
        """
        读取资源索引表 csv文件
        """
        self.logger_print("读取资源索引表：{}".format(path))
        data_list = list()
        with open(path, "r", encoding="utf-8-sig") as fp:
            csv_dict_obj = csv.DictReader(fp)
            for item in csv_dict_obj:
                if data_list.count(item["res_version"]) == 0:
                    data_list.append(item["res_version"])
            fp.close()
        return data_list

    def get_sub_dirs(self, dir_path):
        """
        获取子目录列表
        """
        dirs_name = list()
        for files in os.listdir(dir_path):  # 不仅仅是文件，当前目录下的文件夹也会被认为遍历到
            tmp_path = "{}/{}".format(dir_path, files)
            if os.path.isdir(tmp_path):
                dirs_name.append(files)
            else:
                self.logger_print("有非目录文件：{}/{}".format(dir_path, files))
        return dirs_name

    def parse_version(self, version):
        tmp_versions = version.split("/")
        tmp_main_ver = int(tmp_versions[0].split("v")[1])
        tmp_sub_ver = int(tmp_versions[1].split("v")[1])
        return tmp_main_ver, tmp_sub_ver

    def get_max_version(self, version_list):
        max_main_ver = 0
        max_sub_ver = 0
        for var in version_list:
            tmp_main_ver, tmp_sub_ver = self.parse_version(var)
            if tmp_main_ver > max_main_ver:
                max_main_ver = tmp_main_ver
                max_sub_ver = tmp_sub_ver
            elif tmp_main_ver == max_main_ver and tmp_sub_ver > max_sub_ver:
                max_sub_ver = tmp_sub_ver
        return max_main_ver, max_sub_ver

    def is_can_del_version(self, max_main_ver, max_sub_ver, version_list, get_version):
        # 判断是否在p4的版本目录
        if self.p4_version_dirs.count(get_version) > 0:
            return False

        # 判断是否在版本库内
        if version_list.count(get_version) > 0:
            return False
        # 判断是否大于正式版本
        tmp_main_ver, tmp_sub_ver = self.parse_version(get_version)
        if tmp_main_ver > max_main_ver:
            return False
        elif tmp_main_ver == max_main_ver and tmp_sub_ver > max_sub_ver:
            return False
        return True

    def rec_del_file_path(self, del_path, rec_paths):
        rec_paths.append(del_path)
        if os.path.isdir(del_path):
            self.logger_print("记录目录：{}".format(del_path))
        else:
            self.logger_print("记录文件：{}".format(del_path))

    def del_file_path(self, del_path):
        """
        删除目录和文件
        """
        if not os.path.exists(del_path):
            return
        if os.path.isdir(del_path):
            shutil.rmtree(del_path)
            self.logger_print("删除目录：{}".format(del_path))
        else:
            os.remove(del_path)
            self.logger_print("删除文件：{}".format(del_path))

    # 统计清理文件
    def stat_clear_plat_dirs(self, plat_path, del_dirs, need_del_paths):
        # 获取资源索引表里的版本号数据
        version_list = self.read_res_list_csv_file(plat_path)
        max_main_ver, max_sub_ver = self.get_max_version(version_list)
        self.logger_print("最大版本号：{}/{}".format(max_main_ver, max_sub_ver))
        self.logger_print("有效版本号：{}".format(version_list))

        # 检查版本是否有效
        if max_main_ver <= 0 and max_sub_ver <= 0:
            self.logger_print("最大版本不正确")
            return False

        # 不仅仅是文件，当前目录下的文件夹也会被认为遍历到
        for files in os.listdir(del_dirs):
            file_path = "{}/{}".format(del_dirs, files)
            if os.path.isdir(file_path):
                sub_dirs = self.get_sub_dirs(file_path)
                # 检查当前目录是否在版本内
                for var_dir in sub_dirs:
                    get_version = "{}/{}".format(files, var_dir)
                    if self.is_can_del_version(max_main_ver, max_sub_ver, version_list, get_version):
                        rm_file_path = "{}/{}".format(del_dirs, get_version)
                        self.rec_del_file_path(rm_file_path, need_del_paths)
            else:
                if os.path.splitext(files)[1] == ".zip" or os.path.splitext(files)[1] == ".md5":
                    split_texts = os.path.splitext(files)[0].split(".")[0].split("_")
                    get_version = "{}/{}".format(split_texts[2], split_texts[3])
                    if self.is_can_del_version(max_main_ver, max_sub_ver, version_list, get_version):
                        rm_file_path = "{}/{}".format(del_dirs, files)
                        self.rec_del_file_path(rm_file_path, need_del_paths)
        return True

    def excu_rm_files(self, need_del_paths):
        for var in need_del_paths:
            self.del_file_path(var)

    # 执行清理资源路径
    def clear_res_dirs(self):
        # 统计需要删除的资源
        if not self.stat_clear_plat_dirs(self.android_res_list_path, self.android_del_dirs, self.android_need_del_paths):
            self.logger_print("Android 可删除文件统计失败")
            return
        if not self.stat_clear_plat_dirs(self.ios_res_list_path, self.ios_del_dirs, self.ios_need_del_paths):
            self.logger_print("Ios 可删除文件统计失败")
            return

        # 执行删除操作
        self.excu_rm_files(self.android_need_del_paths)
        self.excu_rm_files(self.ios_need_del_paths)

    def check_files_path(self):
        self.logger_print("检查资源索引表是否存在")
        if not os.path.exists(self.ios_res_list_path):
            self.logger_print("IOS 资源索引表不存在，path: {}".format(self.ios_res_list_path))
            return False
        if not os.path.exists(self.android_res_list_path):
            self.logger_print("Anroid 资源索引表不存在，path: {}".format(self.android_res_list_path))
            return False

        self.logger_print("检查删除资源目录是否存在")
        if not os.path.exists(self.ios_del_dirs):
            self.logger_print("IOS 清理目录不存在，path: {}".format(self.ios_del_dirs))
            return False

        if not os.path.exists(self.android_del_dirs):
            self.logger_print("Android 清理目录不存在，path: {}".format(self.android_del_dirs))
            return False

        return True

    def clear_tmp_p4_dirs(self):
        os.chmod(self.ios_res_list_path, stat.S_IRWXU)
        self.del_file_path(self.ios_res_list_path)
        os.chmod(self.android_res_list_path, stat.S_IRWXU)
        self.del_file_path(self.android_res_list_path)
        os.chmod(self.workspace_p4_path, stat.S_IRWXU)
        self.del_file_path(self.workspace_p4_path)

    def excu_clear_op(self):
        # 获取指定版本资源索引表
        if not self.get_latest_version():
            return
        # 检查文件路径
        if not self.check_files_path():
            return
        # 执行清理资源操作
        self.logger_print("执行清理资源操作！")
        self.clear_res_dirs()
        # 清理工具下载的临时P4目录
        self.logger_print("删除临时目录！")
        self.clear_tmp_p4_dirs()
        self.logger_print("清理成功！")


if __name__ == "__main__":
    # 版本资源清理
    argParser = argparse.ArgumentParser()
    argParser.add_argument("--cdnversion", default="v27v7288", type=str)  # 版本号
    argParser.add_argument("--cleardir", default="D:/file_services/version_test/dynamic/", type=str)  # 清理目录
    args = argParser.parse_args()
    input_version = args.cdnversion
    input_deldir = args.cleardir
    input_versions = input_version.split("v")
    main_version = int(input_versions[1])
    sub_version = int(input_versions[2])
    if main_version > 0 or sub_version > 0:
        # 执行清理操作
        c_tool = DynamicDataClearTool(input_version, input_deldir)
        c_tool.excu_clear_op()
