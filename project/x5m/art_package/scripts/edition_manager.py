# -*- coding: utf-8 -*-
# @Time    : 2021-06-22 17:57
# <AUTHOR> z<PERSON><PERSON><PERSON>@h3d.com.cn
# @File    : edition_manager.py
# @Version : 1.0

import abc
import logging
import os
import re
from pathlib import Path

from P4 import P4Exception

from __init__ import *
from config import *


class EditionManager(metaclass=abc.ABCMeta):
    def __init__(self, **kwargs):
        self.latest_changelist = ""
        self.file_list = []
        self.prefix = kwargs["prefix"] if "prefix" in kwargs else ""
        self.kind = kwargs["kind"] if "kind" in kwargs else ""
        self.switch = "false"  # 切换分支的状态
        self.depot_user_dict = dict()  # 资源路径与提交人的映射字典
        self.depot_user_list = list()  # 本次更新的提交人信息列表

    def __set_changelist__(self, changelist):
        if self.latest_changelist == "":
            self.latest_changelist = 0
        if int(changelist) > int(self.latest_changelist):
            self.latest_changelist = changelist

    def get_resource_updates(self, path=None) -> list:
        depot_files = self.get_update_files(path)
        logging.info(f"depot file: {depot_files}")
        self.get_updates(depot_files)
        return self.file_list

    @abc.abstractmethod
    def get_updates(self, depot_files: list):
        pass

    def get_p4_tool(self, workspace, root="") -> P4Tool:
        return P4Tool(p4_user, p4_host, p4_passwd, workspace, root)

    def submit_files_to_p4(self, file_paths, root, platform, workspace, desc, source_changelist, bak_line=False):
        if file_paths == "":
            resource_path = os.path.join(root, "x5_mobile", "mobile_dancer", "arttrunk", "client", "AssetBundleTool", "resources.txt")
            with open(resource_path, "r", encoding="UTF-8") as f:
                file_paths = f.read()
        files = file_paths.split(",")
        changelist = self.submit(platform, workspace, files, desc, root)
        print(changelist)
        if changelist == "":
            raise Exception("提交p4报错")

        config.set("P4", self.kind, source_changelist)
        config.write(open("../config/config.ini", "w"))

    def submit_to_p4_branch(self, branch, root, platform, workspace, desc, source_changelist, file_paths):
        if file_paths == "":
            resource_path = os.path.join(root, "x5_mobile", "mobile_dancer", "arttrunk", "client", "AssetBundleTool", "resources.txt")
            with open(resource_path, "r", encoding="UTF-8") as f:
                file_paths = f.read()
        files = file_paths.split(",")
        changelist = self.submit_branch(platform, workspace, branch, desc, root, files)
        print(changelist)
        config.set("P4", self.kind, source_changelist)
        config.write(open("../config/config.ini", "w"))

    @abc.abstractmethod
    def submit(self, platform, workspace: str, files: list, desc, root) -> str:
        pass

    @abc.abstractmethod
    def submit_branch(self, platform, workspace: str, branch: str, desc, root, files) -> str:
        pass

    @staticmethod
    def edit_file(p4_srv: P4Tool, path):
        path_lower = path.lower()
        logging.info("path_lower: {}".format(path_lower))
        if p4_srv.files("-e", path_lower) in [p4_error_code, p4_warning_code]:
            p4_srv.add(path_lower)
        else:
            p4_srv.edit(path_lower)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            view_list = []
            views = ""
            for file in self.file_list:
                index = file.find("art_src")
                if index == -1:
                    continue
                house_index = file.find("art_src/house")
                if house_index == -1:
                    real_path = "assets/resources/art" + file[index + 7 :]
                else:
                    real_path = "assets/staticresources/art/3d" + file[index + 7 :]
                view_list.append(real_path)
            views += ",".join(view_list)
            json_result["update"] = "true"
            json_result["package_list"] = views
            json_result["changelist"] = self.latest_changelist
            send_user_list = list()
            for user in self.depot_user_list:
                if not user.endswith("@h3d.com.cn"):
                    send_user_list.append("{}@h3d.com.cn".format(user))
            json_result["send_user"] = ",".join(send_user_list)
        json_result["switch_status"] = self.switch
        return json_result

    def get_update_files(self, path=None) -> list:
        depot_result = []
        delete_files = []
        if path is None:
            results = self.get_descs_and_changelist_default()
            # logging.info("result: {}".format(results))
        else:
            path = f"{path}/...".replace("\\", "/")
            results = self.get_descs_and_changelist(path)
        for res in results:
            for desc in res:
                for index in range(len(desc["depotFile"])):
                    if desc["action"][index] == "delete" or desc["action"][index] == "move/delete":
                        delete_files.append(desc["depotFile"][index])
                        continue
                    if desc["depotFile"][index] not in depot_result:
                        depot_result.append(desc["depotFile"][index])
                    self.depot_user_dict[desc["depotFile"][index]] = desc["user"]
        logging.info(f"depot_user_dict: {self.depot_user_dict}")
        for del_file in delete_files:
            if del_file in depot_result:
                if p4tool.files("-e", del_file) in [p4_error_code, p4_warning_code]:
                    depot_result.remove(del_file)
                    if del_file in self.depot_user_dict:
                        self.depot_user_dict.pop(del_file)
        logging.info(f"depot_user_dict: {self.depot_user_dict}")
        return depot_result

    def get_descs_and_changelist(self, p4_path) -> list:
        results = []
        need_desc_changelists = []
        latest_info = p4tool.changes("-m", "1", p4_path)
        logging.info("p4 path: {}, latest info: {}".format(p4_path, latest_info))
        if not latest_info:
            return []
        # 增加 判断 分支路径下 该 p4_path 路径下 从 changelist 0 到 最新号，如果提交的changelist 次数 小于等于1, 则认为为新拉的分支
        # if p4_path.startswith("//x5_mobile/mr/b/"):
        #     check_first_changes = p4tool.changes('{0}@{1},{2}'.format(p4_path, 0, latest_info[0]['change']))
        #     if check_first_changes in [p4_error_code, p4_warning_code]:
        #         return []
        #     if len(check_first_changes) <= 1:
        #         # 同时将 config.ini的 配置修改为最新
        #         config.set('P4', self.kind, latest_info[0]['change'])
        #         config.write(open('../config/config.ini', "w"))
        #         self.switch = "true"
        #         return []

        minimum_changelist = self.check_version(p4_path)
        config_changelist = int(config["P4"][self.kind])
        logging.info(f"minimum_changelist: {minimum_changelist}, config_changelist: {config_changelist}")
        if config_changelist < minimum_changelist:
            config.set("P4", self.kind, str(minimum_changelist))

        p4_command = "{0}@{1},{2}".format(p4_path, int(config["P4"][self.kind]) + 1, latest_info[0]["change"])
        change_results = p4tool.changes(p4_command)
        logging.info(f"command: {p4_command}, change_result: {change_results}")
        if change_results in [p4_error_code, p4_warning_code]:
            return []
        for res in change_results:
            need_desc_changelists.append(res["change"])
        for changelist in need_desc_changelists:
            results.append(p4tool.describe("-s", changelist))

        self.__set_changelist__(latest_info[0]["change"])

        return results

    def get_descs_and_changelist_default(self) -> list:
        p4_path = f"{self.prefix}/...".replace("\\", "/")
        results = self.get_descs_and_changelist(p4_path)
        return results

    def check_version(self, check_path, first_changelist="") -> int:
        """
        当check_path中包含合并描述时, 返回最后一个合并的changelist
        当不包含合并描述时, 返回非合并资源changelist - 1
        """
        depot_files_path = "{}{}#head".format(check_path, "" if not first_changelist else "@" + first_changelist + ",")
        logging.info(f"depot_files_path: {depot_files_path}")
        depot_files = p4tool.changes("-m30", "-r", "-l", depot_files_path)
        for file in depot_files:
            desc = file.get("desc")
            if not re.match(re.compile(r"(^(Branching|Merging).+to.+)|(^Populate -o.+)", re.S), desc):
                return int(file.get("change")) - 1

        first_changelist = depot_files[-1].get("change")
        if len(depot_files) == 1:
            return int(first_changelist)

        return self.check_version(check_path, first_changelist)


# 特效测试主支
class EditionEffectResourceManager(EditionManager):
    def __init__(self, prefix=edition_p4_path.format("effect"), kind="edition_effect", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)
        self.c_prefix = "//x5_mobile/mr/Resources/art_src/c"

    def get_updates(self, depot_files: list):
        try:
            c_depot_files = self.get_update_files(self.c_prefix)
            for c_file in c_depot_files:
                if not c_file.startswith(self.c_prefix):
                    continue
                if re.search(r"{}/.*?/art/effect/".format(self.c_prefix), c_file) or re.search(r"{}/.*?/art/camera/".format(self.c_prefix), c_file):
                    if self.depot_user_dict.get(c_file) and self.depot_user_dict[c_file] not in self.depot_user_list:
                        self.depot_user_list.append(self.depot_user_dict[c_file])
                    if c_file.endswith(".FBX") or c_file.endswith(".prefab"):
                        self.file_list.append(c_file)
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                    self.depot_user_list.append(self.depot_user_dict[file])
                if file.endswith(".FBX") or file.endswith(".prefab"):
                    self.file_list.append(file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        """
        提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        platform = "iOS" if platform == "ios" else platform
        for f in files:
            if f.find("assets/resources/art/effect/") == -1 and f.find("assets/resources/art/c/") == -1:
                continue
            if f.find("assets/resources/art/effect/") != -1:
                f = f.replace("assets/resources/art/effect", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art/effect")
            else:
                f = f.replace("assets/resources/art/c", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art/c")

            f = os.path.join(root, f).replace("\\", "/")
            f, _ = os.path.splitext(f)

            # 判断本地文件是否存在，不存在直接忽略
            # 不存在的情况一般是未正常打出对应的包，这些信息会在通知和报告中展示，所以提交时正常出包的内容即可
            # 此时会有程序、美术等人工介入，待修复打包工程或者美术资源后，强制或者自动打包
            if not Path(f).exists():
                continue

            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        logging.info("submit result: {}".format(result))
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]

    def submit_old(self, platform, workspace, files: list, desc, root) -> str:
        """
        提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/effect") == -1:
                continue
            file_name_list = f.rsplit("/", maxsplit=1)
            if len(file_name_list) != 2:
                continue
            file_name = file_name_list[-1]
            file_prefix = os.path.join(root, "x5_mobile", "mr", "art_release", "cs", platform, "assetbundles", "art", "effect", "prefab")
            f, _ = os.path.splitext(file_name)
            file_path = os.path.join(file_prefix, f)
            h3d_file_path = os.path.join(file_prefix, f"{f}.h3dmanifest")
            self.edit_file(p4_srv, file_path)
            self.edit_file(p4_srv, h3d_file_path)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]

    def submit_branch(self, platform, workspace: str, branch: str, desc, root, files) -> str:
        pass


# 特效测试分支
class EditionEffectBranchManager(EditionManager):
    def __init__(self, prefix, kind="edition_effect_branch", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)
        self.branch = kwargs.get("branch")
        self.c_prefix = "//x5_mobile/mr/b/{}/art_src/c".format(self.branch)

    def get_updates(self, depot_files: list):
        try:
            c_depot_files = self.get_update_files(self.c_prefix)
            for c_file in c_depot_files:
                if not c_file.startswith(self.c_prefix):
                    continue
                if re.search(r"{}/.*?/art/effect/".format(self.c_prefix), c_file):
                    if self.depot_user_dict.get(c_file) and self.depot_user_dict[c_file] not in self.depot_user_list:
                        self.depot_user_list.append(self.depot_user_dict[c_file])
                    if c_file.endswith(".FBX") or c_file.endswith(".prefab"):
                        self.file_list.append(c_file)
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                    self.depot_user_list.append(self.depot_user_dict[file])
                if file.endswith(".FBX") or file.endswith(".prefab"):
                    self.file_list.append(file)
        except Exception as error:
            logging.error(error)
            exit(-1)

    def submit_branch(self, platform, workspace, branch: str, desc, root, files) -> str:
        """
        提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        platform = "iOS" if platform == "ios" else platform
        for f in files:
            if f.find("assets/resources/art/effect/") == -1 and f.find("assets/resources/art/c/") == -1:
                continue
            if f.find("assets/resources/art/effect/") != -1:
                f = f.replace("assets/resources/art/effect", f"x5_mobile/mr/b/{branch}/art_release/cs/{platform}/assetbundles/art/effect")
            else:
                f = f.replace("assets/resources/art/c", f"x5_mobile/mr/b/{branch}/art_release/cs/{platform}/assetbundles/art/c")

            f = os.path.join(root, f).replace("\\", "/")
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]

    def submit(self, plartform, workspace: str, files: list, desc, root) -> str:
        pass


# 特效测试周更
class EditionEffectUpdateManager(EditionManager):
    def __init__(self, prefix, kind="edition_effect_resources", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                    self.depot_user_list.append(self.depot_user_dict[file])
                if file.endswith(".FBX") or file.endswith(".prefab"):
                    self.file_list.append(file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit_branch(self, platform, workspace, branch: str, desc, root, files) -> str:
        """
        提交到p4上
        """
        pass

    def submit(self, platform, workspace: str, files: list, desc, root) -> str:
        pass

    def submit_resources(self, workspace, platform, desc, root, major_version, version):
        """
        提交到周更的 resources
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        # 更改为使用reconcile命令判断effect目录下的文件的 增删改 的变动
        file_path = os.path.join(root, "x5_mobile", "mr", "onlineupdate", major_version, version, "client", platform, "assetbundles", "art", "effect")
        p4_check_folder = os.path.join(file_path, "...")

        if p4_srv.reconcile(p4_check_folder) in [p4_error_code, p4_warning_code]:
            return "0"
        else:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return "0"
            else:
                return result[0]["change"]


class EditionCameraManager(EditionManager):
    def __init__(self, prefix=edition_p4_path.format("camera"), kind="edition_camera", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                    self.depot_user_list.append(self.depot_user_dict[file])
                if file.endswith(".FBX") or file.endswith(".prefab"):
                    self.file_list.append(file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit_old(self, platform, workspace, files: list, desc, root) -> str:
        """
        提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        # 更改为使用reconcile命令判断camera目录下的文件的 增删改 的变动
        file_path = os.path.join(root, "x5_mobile", "mr", "art_release", "cs", platform, "assetbundles", "art", "camera", "prefab")
        p4_check_folder = os.path.join(file_path, "...")
        if p4_srv.reconcile(p4_check_folder) in [p4_error_code, p4_warning_code]:
            return "0"
        else:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return "0"
            else:
                return result[0]["change"]

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        """
        提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/camera") == -1:
                continue
            file_name_list = f.rsplit("/", maxsplit=1)
            if len(file_name_list) != 2:
                continue
            file_name = file_name_list[-1]
            file_prefix = os.path.join(root, "x5_mobile", "mr", "art_release", "cs", platform, "assetbundles", "art", "camera", "prefab")
            f, _ = os.path.splitext(file_name)
            file_path = os.path.join(file_prefix, f)
            h3d_file_path = os.path.join(file_prefix, f"{f}.h3dmanifest")
            self.edit_file(p4_srv, file_path)
            self.edit_file(p4_srv, h3d_file_path)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]

    def submit_branch(self, platform, workspace: str, branch: str, desc, root, files) -> str:
        pass


class EditionCameraBranchManager(EditionManager):
    def __init__(self, prefix, kind="edition_camera_branch", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                    self.depot_user_list.append(self.depot_user_dict[file])
                if file.endswith(".FBX") or file.endswith(".prefab"):
                    self.file_list.append(file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit_branch(self, platform, workspace, branch: str, desc, root, files) -> str:
        """
        提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        # 更改为使用reconcile命令判断camera目录下的文件的 增删改 的变动
        file_path = os.path.join(root, "x5_mobile", "mr", "b", branch, "art_release", "cs", platform, "assetbundles", "art", "camera", "prefab")
        p4_check_folder = os.path.join(file_path, "...")

        if p4_srv.reconcile(p4_check_folder) in [p4_error_code, p4_warning_code]:
            return "0"
        else:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return "0"
            else:
                return result[0]["change"]

    def submit(self, platform, workspace: str, files: list, desc, root) -> str:
        pass


class EditionCameraUpdateManager(EditionManager):
    def __init__(self, prefix, kind="edition_camera_resources", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                    self.depot_user_list.append(self.depot_user_dict[file])
                if file.endswith(".FBX") or file.endswith(".prefab"):
                    self.file_list.append(file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit_branch(self, platform, workspace, branch: str, desc, root, files) -> str:
        """
        提交到p4上
        """
        pass

    def submit(self, platform, workspace: str, files: list, desc, root) -> str:
        pass

    def submit_resources(self, workspace, platform, desc, root, major_version, version):
        """
        提交到周更的 resources
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        # 更改为使用reconcile命令判断camera目录下的文件的 增删改 的变动
        file_path = os.path.join(root, "x5_mobile", "mr", "onlineupdate", major_version, version, "client", platform, "assetbundles", "art", "camera")
        p4_check_folder = os.path.join(file_path, "...")

        if p4_srv.reconcile(p4_check_folder) in [p4_error_code, p4_warning_code]:
            return "0"
        else:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return "0"
            else:
                return result[0]["change"]


class EditionBodyPartManager(EditionManager):
    def __init__(self, prefix=edition_p4_path.format("role/bodypart"), kind="edition_bodypart", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)
        self.id_list = []

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if (
                    file.find("bodypart/female/face/") != -1
                    or file.find("bodypart/female/plainBody/") != -1
                    or file.find("bodypart/male/face/") != -1
                    or file.find("bodypart/male/plainBody/") != -1
                    or file.find(self.prefix) == -1
                ):
                    continue
                # if file.startswith(self.prefix) == -1:
                #     continue
                if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                    self.depot_user_list.append(self.depot_user_dict[file])
                dir_name = os.path.dirname(file)
                cid = re.findall(r"[0-9]{10}", dir_name)
                if len(cid) == 0:
                    continue
                if cid[0] not in self.id_list:
                    dirs_result = p4tool.dirs(dir_name)
                    if dirs_result in [p4_error_code, p4_warning_code] or len(dirs_result) == 0:
                        continue
                    id_index = dir_name.find(cid[0])
                    if id_index == -1:
                        continue
                    x5mobile_index = dir_name.find("x5_mobile")
                    if x5mobile_index != -1:
                        self.id_list.append(cid[0])
                        id_path = dir_name[x5mobile_index:id_index] + cid[0]
                        self.file_list.append(id_path)
        except P4Exception as error:
            for e in error.warnings:
                logging.warning(e)
            for e in error.errors:
                logging.error(e)
            exit(-1)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            view_list = []
            views = ""
            for file in self.file_list:
                index = file.find("art_src")
                if index == -1:
                    continue
                real_path = "assets/resources/art" + file[index + 7 :]
                view_list.append(real_path)
            views += ",".join(view_list)
            ids = ""
            ids += ",".join(self.id_list)
            json_result["update"] = "true"
            json_result["idlist"] = ids
            json_result["idpaths"] = views
            json_result["changelist"] = self.latest_changelist
        return json_result

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        """
        提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        # 更改为使用reconcile命令判断camera目录下的文件的 增删改 的变动
        file_path = os.path.join(root, "x5_mobile", "mr", "art_release", "cs", platform, "assetbundles", "art", "role", "bodypart")
        p4_check_folder = os.path.join(file_path, "...")

        if p4_srv.reconcile(p4_check_folder) in [p4_error_code, p4_warning_code]:
            return "0"
        else:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return "0"
            else:
                return result[0]["change"]

    def submit_branch(self, plartform, workspace: str, branch: str, desc, root, files) -> str:
        pass


class EditionLinkManager(EditionBodyPartManager):
    def __init__(self, prefix=edition_p4_path.format("role/link"), kind="edition_link", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if file.find(self.prefix) == -1:
                    continue

                if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                    self.depot_user_list.append(self.depot_user_dict[file])

                if file.endswith(".spe.prefab"):
                    self.file_list.append(file)

        except P4Exception as error:
            for e in error.warnings:
                logging.warning(e)
            for e in error.errors:
                logging.error(e)
            exit(-1)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            view_list = []
            views = ""
            for file in self.file_list:
                index = file.find("art_src")
                if index == -1:
                    continue
                real_path = "assets/resources/art" + file[index + 7 :]
                view_list.append(real_path)
            views += ",".join(view_list)
            ids = ""
            ids += ",".join(self.id_list)
            json_result["update"] = "true"
            json_result["package_list"] = views
            json_result["idpaths"] = views
            json_result["changelist"] = self.latest_changelist
        return json_result

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        """
        提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        # 更改为使用reconcile命令判断camera目录下的文件的 增删改 的变动
        file_path = os.path.join(root, "x5_mobile", "mr", "art_release", "cs", platform, "assetbundles", "art", "role", "link")
        p4_check_folder = os.path.join(file_path, "...")
        if p4_srv.reconcile(p4_check_folder) in [p4_error_code, p4_warning_code]:
            return "0"

        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return "0"
        else:
            return result[0]["change"]

    def submit_branch(self, plartform, workspace: str, branch: str, desc, root, files) -> str:
        pass


class SubmitManager(object):
    """
    用于处理提交p4操作
    """

    def get_p4_tool(self, workspace, root=""):
        return P4Tool(p4_user, p4_host, p4_passwd, workspace, root)

    def submit_reconcile(self, workspace, desc, submit_path, use_new_p4=False):
        """
        使用 p4 reconcile 命令的方式 先判断变化，再提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        # 更改为使用reconcile命令判断目录下的文件的 增删改 的变动
        submit_path = submit_path.replace("/", os.path.sep).replace("\\", os.path.sep)
        p4_check_folder = os.path.join(submit_path, "...")

        if p4_srv.reconcile(p4_check_folder) in [p4_error_code, p4_warning_code]:
            return "0"
        else:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return "0"
            else:
                return result[0]["change"]


class EditionTimelineBranchManager(EditionManager):
    def __init__(self, prefix, kind="edition_timeline_branch", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)

    def get_updates(self, depot_files: list):
        """
        获取timeline目录下的 第二层 7位数字目录
        """
        for file in depot_files:
            if not file.startswith(self.prefix):
                continue
            file_relative_path = file.replace(self.prefix, "")
            package_list = file_relative_path.split("/")
            if len(package_list) >= 3:
                if len(package_list[2]) == 7 and package_list[2].isdigit():
                    letter_path = "{}/{}".format(package_list[1], package_list[2])
                    if letter_path not in self.file_list:
                        self.file_list.append(letter_path)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            view_list = []
            views = ""
            for file in self.file_list:
                view_list.append("assets/resources/art/timeline/{}".format(file))
            views += ",".join(view_list)
            json_result["update"] = "true"
            json_result["package_list"] = views
            json_result["changelist"] = self.latest_changelist
        return json_result

    def submit_branch(self, platform, workspace, branch: str, desc, root, files) -> str:
        """
        提交到p4上
        """
        file_path = os.path.join(root, "x5_mobile", "mr", "b", branch, "art_release", "cs", platform, "assetbundles", "art", "timeline")
        return SubmitManager().submit_reconcile(workspace, desc, file_path)

    def submit(self, platform, workspace: str, files: list, desc, root) -> str:
        pass


class EditionActionsBranchManager(EditionManager):
    def __init__(self, prefix, kind="edition_role/actions_branch", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                if file.endswith(".asset") or file.endswith(".anim") or file.endswith(".FBX") or file.endswith(".meta"):
                    self.file_list.append(file)
        except P4Exception as e:
            for error in e.errors:
                print(error)
            exit(-1)

    def submit_branch(self, platform, workspace, branch: str, desc, root, files) -> str:
        """
        提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        # 更改为使用reconcile命令判断camera目录下的文件的 增删改 的变动
        file_path = os.path.join(root, "x5_mobile", "mr", "b", branch, "art_release", "cs", platform, "assetbundles", "art", "role", "actions")
        p4_check_folder = os.path.join(file_path, "...")

        if p4_srv.reconcile(p4_check_folder) in [p4_error_code, p4_warning_code]:
            return "0"
        else:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return "0"
            else:
                return result[0]["change"]

    def submit(self, platform, workspace: str, files: list, desc, root) -> str:
        pass


class EditionActionsUpdateManager(EditionManager):
    def __init__(self, prefix, kind="edition_actions_resources", **kwargs):
        super().__init__(prefix=prefix, kind=kind, **kwargs)

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                if file.endswith(".asset") or file.endswith(".anim") or file.endswith(".FBX") or file.endswith(".meta"):
                    self.file_list.append(file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit_branch(self, platform, workspace, branch: str, desc, root, files) -> str:
        """
        提交到p4上
        """
        pass

    def submit(self, platform, workspace: str, files: list, desc, root) -> str:
        pass

    def submit_resources(self, workspace, platform, desc, root, major_version, version):
        """
        提交到周更的 resources
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        # 更改为使用reconcile命令判断camera目录下的文件的 增删改 的变动
        file_path = os.path.join(
            root, "x5_mobile", "mr", "onlineupdate", major_version, version, "client", platform, "assetbundles", "art", "role", "actions"
        )
        p4_check_folder = os.path.join(file_path, "...")

        if p4_srv.reconcile(p4_check_folder) in [p4_error_code, p4_warning_code]:
            return "0"
        else:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return "0"
            else:
                return result[0]["change"]


def test_submit_p4():
    """
    测试 提交p4
    """
    pass


if __name__ == "__main__":
    test_submit_p4()
