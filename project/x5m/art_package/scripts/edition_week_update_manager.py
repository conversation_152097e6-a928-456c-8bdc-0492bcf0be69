# -*- coding: utf-8 -*-
# @Time    : 2021-06-29 15:03
# <AUTHOR> z<PERSON><PERSON><PERSON>@h3d.com.cn
# @File    : edition_week_update_manager.py
# @Version : 1.0

import stat
import json
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from ftplib import FTP

import re, os
import logging

from __init__ import *

# 切环境日期, 周几
SWITCH_DATE_1 = 1
SWITCH_DATE_2 = 4


def find_max_cdn_version(cdn_file_name_list: list) -> (int, int):
    first_max_version = 0
    second_max_version = 0
    pattern = re.compile(r"\d+")
    for cdn_file_name in cdn_file_name_list:
        versions = pattern.findall(cdn_file_name)
        if len(versions) != 2:
            continue

        first_version = int(versions[0])
        second_version = int(versions[1])

        if first_version > first_max_version:
            first_max_version = first_version

        if second_version > second_max_version:
            second_max_version = second_version

    return first_max_version, second_max_version


class WeekUpdateManager(object):
    """
    周更版本类
    """

    def __init__(self, hotfix_config, p4_mgr: P4ToolManager, resource: bool, git_mgr: GitToolManager):
        self.modify = hotfix_config["modify"]
        self.weekly_time = int(hotfix_config["weekly_time"])
        self.major_version = hotfix_config["major_version"]
        tmp_version_list = self.major_version.split(".")
        self.sub_version_prefix = f"{tmp_version_list[0]}{tmp_version_list[1]}"
        self.current_weekly_time = int(hotfix_config["current_weekly_time"])
        self.switch = hotfix_config["switch"]
        self.loop_count = int(hotfix_config["loop_count"])
        self.hotfix_change_list = int(hotfix_config["hot_fix_change_list"])
        self.fetter_change_list = int(hotfix_config["fetter_change_list"])
        self.cdn_source_change_list = int(hotfix_config["cdn_source_change_list"])
        self.config_pipeline_id = int(hotfix_config["config_pipeline_id"])
        self.cdn_raw_resource_changelist = ""
        self.resource = resource
        self.p4_tool_mgr = p4_mgr
        self.git_tool_mgr = git_mgr

    def __update_sub_version_prefix(self):
        tmp_version_list = self.major_version.split(".")
        self.sub_version_prefix = f"{tmp_version_list[0]}{tmp_version_list[1]}"

    def __calculate_major_version(self):
        """
        计算主版本号
        :return:
        """
        # 1. 获取p4上所有的主版本号目录
        major_versions = self.p4_tool_mgr.get_p4_tool(online_update_path).get_dir_names(f"{online_update_path}/*")
        larger_versions = []
        for v in major_versions:
            if v <= self.major_version:
                continue
            larger_versions.append(v)

        # 如果计算主版本号得到的结果是空列表，可能是因为P4目录未创建
        if len(larger_versions) == 0:
            raise Exception("计算主版本号失败，请与PM确认P4对应的周更目录是否创建成功")

        larger_versions.sort()
        self.major_version = larger_versions[0]
        self.weekly_time = 1

    def __calculate_second_version(self, next_weekly_time: int):
        if next_weekly_time - self.weekly_time == 1:
            self.weekly_time = next_weekly_time
            self.loop_count = -1
            return

        if self.resource:
            self.weekly_time = next_weekly_time
        elif self.loop_count == 0:
            self.weekly_time = next_weekly_time
            self.loop_count = -1
            return

        self.loop_count = next_weekly_time - self.weekly_time - 1

    def __calculate_next_version(self):
        if self.loop_count > 0:
            self.loop_count = self.loop_count - 1
            return

        larger_weekly_times = []
        online_update_dir_names = self.p4_tool_mgr.get_p4_tool(online_update_path).get_dir_names(
            f"{online_update_path}/{self.major_version}/{self.sub_version_prefix}2.*"
        )
        for dir_name in online_update_dir_names:
            weekly_time = int(dir_name.split(".")[1][0:2]) if int(self.sub_version_prefix) >= 504 else int(dir_name.split(".")[1][0])
            if weekly_time > self.weekly_time:
                larger_weekly_times.append(weekly_time)

        if len(larger_weekly_times) != 0:
            larger_weekly_times.sort()
            self.__calculate_second_version(int(larger_weekly_times[0]))
        else:
            self.__calculate_major_version()

    def switch_version(self):
        """
        切换分支
        参数说明:
            1. self.switch  true:表示切换过分支        false:表示没有切换分支
        如果今天不是周一和周四, 不切换分支
        如果当前的时间处于 0：00 至 4:00, 不切换分支
        """
        if self.switch == "true":
            self.current_weekly_time = 2
        else:
            weekday = datetime.now().isoweekday()
            logging.info(f"weekday: {weekday}, self.modify: {self.modify}")
            logging.info(
                f"before switch the version: major_version: {self.major_version}, weekly_time: {self.weekly_time}, current_weekly_time: {self.current_weekly_time}, switch: {self.switch}"
            )
            # 假设今天是本月的第一周的周一 此时现网的版本是4
            if weekday != SWITCH_DATE_1 and weekday != SWITCH_DATE_2:
                self.modify = "false"
                return

            if self.modify != "false":
                return

            if datetime.now().hour <= 4:
                return
        if self.current_weekly_time >= 2:
            self.__calculate_next_version()
            if self.switch == "true":
                self.current_weekly_time = 0
                self.switch = "false"
            else:
                self.current_weekly_time = 1
        else:
            self.current_weekly_time += 1
        self.modify = "true"
        self.__update_sub_version_prefix()
        logging.info(
            f"after switch the version: major_version: {self.major_version}, weekly_time: {self.weekly_time}, current_weekly_time: {self.current_weekly_time}, switch: {self.switch}"
        )

    def check_hotfix_version(self, hotfix_version) -> bool:
        if len(hotfix_version) < (12 if int(self.sub_version_prefix) >= 504 else 11):
            return False
        pattern = re.compile(r"^[-+]?[-0-9]\d*\.\d*|[-+]?\.?[0-9]\d*$")
        if not pattern.match(hotfix_version):
            return False
        if not hotfix_version.startswith(f"{self.sub_version_prefix}2."):
            return False

        return True

    def calculate_weekly_resource_dir(self) -> (str, bool):
        """
        计算周更资源目录
        :return:
        """
        # weekly_resource_dir_name = f'{self.sub_version_prefix}2.{self.weekly_time}01000'
        weekly_resource_dir_name = (
            f"{self.sub_version_prefix}2.0{self.weekly_time}01000"
            if (int(self.weekly_time) < 10 and int(self.sub_version_prefix) >= 504)
            else f"{self.sub_version_prefix}2.{self.weekly_time}01000"
        )
        logging.info(f"sub_version_prefix: {self.sub_version_prefix}")

        # 1.查看p4上是否存在以****.*开头的热更包  比如4010.1
        # hotfix_packages = self.p4_tool_mgr.get_p4_tool(hotfix_package_path).get_file_names_without_deleted(
        #     f'{hotfix_package_path}/{self.sub_version_prefix}2.{self.weekly_time}*')

        hotfix_packages = self.p4_tool_mgr.get_p4_tool(hotfix_package_path).get_file_names_without_deleted(
            f"{hotfix_package_path}/{self.sub_version_prefix}2.0{self.weekly_time}*"
            if (int(self.weekly_time) < 10 and int(self.sub_version_prefix) >= 504)
            else f"{hotfix_package_path}/{self.sub_version_prefix}2.{self.weekly_time}*"
        )

        logging.info(f"weekly_resource_dir_name: {weekly_resource_dir_name}")
        logging.info(f"hotfix_packages: {hotfix_packages}")
        if len(hotfix_packages) == 0:
            return weekly_resource_dir_name, True

        # 2.查看版本最大的热更包
        max_hotfix_package = ""
        for p in hotfix_packages:
            if not self.check_hotfix_version(p):
                continue
            if p > max_hotfix_package:
                max_hotfix_package = p

        # online_update_versions = self.p4_tool_mgr.get_p4_tool(online_update_path).get_dir_names(
        #     f'{online_update_path}/{self.major_version}/{self.sub_version_prefix}2.{self.weekly_time}*')

        online_update_versions = self.p4_tool_mgr.get_p4_tool(online_update_path).get_dir_names(
            f"{online_update_path}/{self.major_version}/{self.sub_version_prefix}2.0{self.weekly_time}*"
            if (int(self.weekly_time) < 10 and int(self.sub_version_prefix) >= 504)
            else f"{online_update_path}/{self.major_version}/{self.sub_version_prefix}2.{self.weekly_time}*"
        )

        logging.info("online_update_versions: {}".format(online_update_versions))
        if len(online_update_versions) == 0:
            raise Exception(f"请检查p4上的周更目录，是否已经创建好{self.major_version}版本的子目录")
            # return weekly_resource_dir_name, False

        larger_online_update_version = []
        for v in online_update_versions:
            if not self.check_hotfix_version(v):
                continue
            if v > max_hotfix_package:
                larger_online_update_version.append(v)

        if len(larger_online_update_version) == 0:
            if len(online_update_versions) > 0:
                online_update_versions.sort(reverse=True)
                weekly_resource_dir_name = online_update_versions[0]
            return weekly_resource_dir_name, False

        larger_online_update_version.sort()
        weekly_resource_dir_name = larger_online_update_version[0]
        return weekly_resource_dir_name, True

    def calculate_weekly_version(self, weekly_resource_dir_name) -> str:
        files = find_files_in_ftp(hotfix_package_ftp_dir_path)
        # 计算周更版本号参数
        max_weekly_version = ""
        for file in files:
            if not file.startswith(weekly_resource_dir_name[:9] if int(self.sub_version_prefix) >= 504 else weekly_resource_dir_name[:8]):
                continue
            if not self.check_hotfix_version(file):
                continue
            if file > max_weekly_version:
                max_weekly_version = file

        if max_weekly_version == "":
            weekly_version = "{0}000".format(weekly_resource_dir_name[:9] if int(self.sub_version_prefix) >= 504 else weekly_resource_dir_name[:8])
        else:
            weekly_version = Decimal(str(max_weekly_version)) + Decimal("0.0000001" if int(self.sub_version_prefix) >= 504 else "0.000001")

        return str(weekly_version)

    def calculate_hotfix_base_version(self, weekly_version):
        packaged_files = self.p4_tool_mgr.get_p4_tool(hotfix_package_path).get_file_names_without_deleted(
            f"{hotfix_package_path}/{self.sub_version_prefix}2.*"
        )
        if len(packaged_files) == 0:
            hotfix_base_version = (
                f"{self.sub_version_prefix}2.0000000" if int(self.sub_version_prefix) >= 504 else f"{self.sub_version_prefix}2.000000"
            )
            return hotfix_base_version

        lesser_versions = []
        for f in packaged_files:
            if not self.check_hotfix_version(f):
                continue
            if f >= weekly_version:
                continue
            lesser_versions.append(f)

        if len(lesser_versions) == 0:
            hotfix_base_version = (
                f"{self.sub_version_prefix}2.0000000" if int(self.sub_version_prefix) >= 504 else f"{self.sub_version_prefix}2.000000"
            )
            return hotfix_base_version

        lesser_versions.sort(reverse=True)
        hotfix_base_version = lesser_versions[0]
        return hotfix_base_version

    def cdn_package(self) -> bool:
        cdn_raw_resource_changelist = self.p4_tool_mgr.get_p4_tool(cdn_raw_resource_p4_path).get_max_changelist(f"{cdn_raw_resource_p4_path}/...")

        cdn_android_changelist = self.p4_tool_mgr.get_p4_tool(cdn_p4_path).get_max_changelist(f"{cdn_p4_path}/android/...")
        cdn_ios_changelist = self.p4_tool_mgr.get_p4_tool(cdn_p4_path).get_max_changelist(f"{cdn_p4_path}/ios/...")
        if cdn_android_changelist == -1 or cdn_ios_changelist == -1:
            return False

        max_changelist = cdn_android_changelist if cdn_android_changelist > cdn_ios_changelist else cdn_ios_changelist
        if max_changelist <= self.cdn_source_change_list:
            return False

        self.cdn_source_change_list = max_changelist
        self.cdn_raw_resource_changelist = cdn_raw_resource_changelist
        return True

    def hotfix_package(self, namespace, weekly_resource_dir_name) -> bool:
        package = False
        hotfix_changelist = self.p4_tool_mgr.get_p4_tool(online_update_path).get_max_changelist(
            f"{online_update_path}/{self.major_version}/{weekly_resource_dir_name}/..."
        )
        fetter_changelist = self.p4_tool_mgr.get_p4_tool(fetter_path).get_max_changelist(f"{fetter_path}/...".encode("gbk"))

        cs_config_pipeline_id = self.git_tool_mgr.get_git_tool("x5mconfig").get_max_pipelineID("cdn", 0)
        week_config_pipeline_id = self.git_tool_mgr.get_git_tool("x5mweek").get_max_pipelineID(namespace, 0)
        hotfix_config_pipeline_id = self.git_tool_mgr.get_git_tool("x5mconfig").get_max_pipelineID_hotfix("hotfix", 0, weekly_resource_dir_name)

        if cs_config_pipeline_id > self.config_pipeline_id:
            self.config_pipeline_id = cs_config_pipeline_id
            package = True

        if hotfix_config_pipeline_id > self.config_pipeline_id:
            self.config_pipeline_id = hotfix_config_pipeline_id
            package = True

        if week_config_pipeline_id > self.config_pipeline_id:
            self.config_pipeline_id = week_config_pipeline_id
            package = True

        if hotfix_changelist > self.hotfix_change_list:
            self.hotfix_change_list = hotfix_changelist
            package = True
        if fetter_changelist > self.fetter_change_list:
            self.fetter_change_list = fetter_changelist
            package = True

        if namespace == "release" and (self.loop_count != -1 or self.current_weekly_time == 2):
            package = False

        return package

    def to_config(self, cfg):
        cfg.set("HOTFIX", "modify", self.modify)
        cfg.set("HOTFIX", "switch", self.switch)
        cfg.set("HOTFIX", "major_version", self.major_version)
        cfg.set("HOTFIX", "weekly_time", str(self.weekly_time))
        cfg.set("HOTFIX", "current_weekly_time", str(self.current_weekly_time))
        cfg.set("HOTFIX", "loop_count", str(self.loop_count))
        cfg.set("HOTFIX", "hot_fix_change_list", str(self.hotfix_change_list))
        cfg.set("HOTFIX", "fetter_change_list", str(self.fetter_change_list))
        cfg.set("HOTFIX", "cdn_source_change_list", str(self.cdn_source_change_list))
        cfg.set("HOTFIX", "config_pipeline_id", str(self.config_pipeline_id))

    def version_to_config(self, cfg):
        cfg.set("HOTFIX", "modify", self.modify)
        cfg.set("HOTFIX", "switch", self.switch)
        cfg.set("HOTFIX", "major_version", self.major_version)
        cfg.set("HOTFIX", "weekly_time", str(self.weekly_time))
        cfg.set("HOTFIX", "current_weekly_time", str(self.current_weekly_time))


def find_files_in_ftp(ftp_path: str) -> list:
    ftp = FTP()
    ftp.connect(ftp_ip, ftp_port)
    ftp.login(ftp_user, ftp_passwd)

    entries = []

    ftp.cwd(ftp_path)
    ftp.retrlines("NLST", entries.append)
    ftp.quit()
    return entries


@cli.command()
@click.option("--switch", help="切换版本")
def switch_version(switch):
    hotfix_session = config["HOTFIX"]
    mgr = WeekUpdateManager(hotfix_session, p4_tool_mgr, False, git_tool_mgr)
    if switch == "true":
        mgr.switch = "true"
    mgr.switch_version()

    mgr.version_to_config(config)
    config.write(open("../config/config.ini", "w"))


@cli.command()
@click.option("--namespace", help="打包的环境resources，release，online三种")
def calculate_hotfix_versions(namespace):
    hotfix_session = config["HOTFIX"]

    resource = True if namespace == "resources" else False
    mgr = WeekUpdateManager(hotfix_session, p4_tool_mgr, resource, git_tool_mgr)

    mgr.switch_version()

    weekly_resource_dir_name, hotfix_package = mgr.calculate_weekly_resource_dir()

    weekly_version = mgr.calculate_weekly_version(weekly_resource_dir_name)

    hotfix_base_version = mgr.calculate_hotfix_base_version(weekly_version)

    is_cdn_package = mgr.cdn_package()
    hotfix_package = mgr.hotfix_package(namespace, weekly_resource_dir_name) and hotfix_package

    json_result = {
        "major_version": mgr.major_version,
        "namespace": namespace,
        "hotfix_package": str(hotfix_package),
        "weekly_resource": weekly_resource_dir_name,
        "weekly_version": weekly_version,
        "hotfix_base_version": hotfix_base_version,
        "cdn_raw_resource_changelist": mgr.cdn_raw_resource_changelist,
        "cdn_source_change_list": str(mgr.cdn_source_change_list),
        "hotfix_changelist": str(mgr.hotfix_change_list),
        "config_pipeline_id": str(mgr.config_pipeline_id),
    }
    if namespace == "resources":
        json_result["cdn_package"] = str(is_cdn_package)

    print(json_result)
    mgr.to_config(config)
    config.write(open("../config/config.ini", "w"))


@cli.command()
@click.option("--namespace", help="打包的环境resources，release，online三种")
@click.option("--branch", help="打包的分支")
def calculate_hotfix_info(namespace, branch):
    hotfix_session = config["HOTFIX"]
    hotfix_session["major_version"] = branch
    mgr = WeekUpdateManager(hotfix_session, p4_tool_mgr, True, git_tool_mgr)

    branch_dir = branch.replace(".", "")
    branch_dir = branch_dir[0:3] + "2.0101000" if int(branch_dir[0:3]) >= 504 else "2.101000"
    weekly_resource_dir_name = branch_dir

    weekly_version = mgr.calculate_weekly_version(weekly_resource_dir_name)
    hotfix_base_version = mgr.calculate_hotfix_base_version(weekly_version)
    mgr.hotfix_package(namespace, weekly_resource_dir_name)
    json_result = {
        "weekly_resource": weekly_resource_dir_name,
        "weekly_version": weekly_version,
        "hotfix_base_version": hotfix_base_version,
        "hotfix_changelist": str(mgr.hotfix_change_list),
        "config_pipeline_id": str(mgr.config_pipeline_id),
    }

    print(json_result)
    mgr.to_config(config)
    config.write(open("../config/config.ini", "w"))


@cli.command()
@click.option("--package", default="False", help="是否打cdn包")
def cdn_package(package):
    entries = find_files_in_ftp(cdn_ftp_dir_path)
    first_version, second_version = find_max_cdn_version(entries)
    cdn_version = f"v{first_version}v{second_version + 1}"
    res_list_version = f"v{first_version}_v{second_version + 1}"

    if "True" != package:
        cdn_version = f"v{first_version}v{second_version}"
        res_list_version = f"v{first_version}_v{second_version}"

    p4_cdn_file_name_list = []
    dir_results = p4_tool_mgr.get_p4_tool(cdn_package_path).dirs(f"{cdn_package_path}/*")
    for result in dir_results:
        p4_cdn_file_name_list.append(result["dir"].split("/")[-1])

    base_first_version, base_second_version = find_max_cdn_version(p4_cdn_file_name_list)
    cdn_base_version = f"v{base_first_version}v{base_second_version}"

    json_result = {"cdn_version": cdn_version, "cdn_base_version": cdn_base_version, "res_list_version": res_list_version}

    print(json.dumps(json_result))


@cli.command()
@click.option("--folder_path", help="路径")
def clear_config(folder_path):
    files = os.listdir(folder_path)
    for f in files:
        root = folder_path + "/" + f
        if os.path.isdir(root):
            folder = f"{root}/client/crossplatform/config"
            delete_folder(folder)
            folder = f"{root}/server/resources"
            delete_folder(folder)


def delete_folder(folder):
    folder = folder.replace("/", "\\")
    print("delete_folder" + folder)
    if os.path.isdir(folder):
        os.chmod(folder, stat.S_IWRITE)
        os.system("del /f /s /q " + folder)
        print("del /f /s /q " + folder)


def get_project_merge(
    host: str = "https://x5mobile-gitlab.h3d.com.cn/",
    private_token: str = "i6FAUu8yPYST1iJzyH9M",
    project_id: int = 53,
    ref_name: str = "online",
    since: datetime = None,
) -> list:
    """
    获取项目分支中的commit记录信息
    Args:
        host:
        private_token:
        project_id:
        ref_name:
        since:

    Returns:

    """
    logging.info(f"host: {host} project_id: {project_id} ref_name: {ref_name} since: {since}")
    commits = []
    try:
        import gitlab

        gl = gitlab.Gitlab(url=host, private_token=private_token)
        project = gl.projects.get(project_id)
        if since:
            commits = project.commits.list(ref_name=ref_name, since=since)
        else:
            commits = project.commits.list(ref_name=ref_name)
    except Exception as e:
        logging.error(e)
    return commits


weekdays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]


def get_previous_by_day(day_name, start_date=None):
    if start_date is None:
        start_date = datetime.today()
    day_num = start_date.weekday()
    day_num_target = weekdays.index(day_name)
    days_ago = (7 + day_num - day_num_target) % 7
    if days_ago == 0:
        days_ago = 7
    target_date = start_date - timedelta(days=days_ago)
    return target_date


def cal_since_time() -> datetime:
    """
    计算commit提交的开始时间
    Returns:

    """
    last_thur = get_previous_by_day("Thursday").replace(hour=5, minute=16, second=0, microsecond=0)
    now = datetime.now()
    d = (now - last_thur).days
    m = (now - last_thur).seconds
    if d > 7 or (d == 7 and m > 0):
        last_thur = last_thur + timedelta(days=7)
    return last_thur


@cli.command()
def allow_online_packages():
    """
    判断是否允许进行打包
    Returns:

    """
    allow_result = {"allow_package": "false"}
    # 先获取上次分支切换的时间点，按照周四凌晨五点十六分计算
    switch_time = cal_since_time()
    # 获取分支合并信息
    history_commits = get_project_merge(since=switch_time.astimezone(timezone.utc))
    logging.info(f"history_commits: {history_commits}")
    if not history_commits:
        return print(allow_result)

    title_commits = [history_commit.title for history_commit in history_commits]
    logging.info(f"title_commits: {title_commits}")
    if "Merge branch 'release' into online" in title_commits:
        allow_result.update({"allow_package": "true"})
        return print(allow_result)
    return print(allow_result)


# if __name__ == '__main__':
#     wum = WeekUpdateManager()
