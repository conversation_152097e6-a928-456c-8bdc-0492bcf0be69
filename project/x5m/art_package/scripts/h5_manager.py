# -*- coding: utf-8 -*-
# @Time    : 2021-09-27 17:23
# <AUTHOR> z<PERSON><PERSON><PERSON>@h3d.com.cn
# @File    : h5_manager.py


import os
import re
import csv
import time
import binascii
import shutil
import logging

from __init__ import *
from config import *
from ftp_tool import FtpUtils, UploadTencentFtp

logger = logging.getLogger("jenkins")


class H5Manager(object):
    """
    h5 操作类
    """

    def __init__(self):
        self.p4_path = "//x5m/res/h5/"
        self.h5_ftp_path = "/version_test/h5"
        self.ftp_path = "/version_test"
        self.logger = logger

    def get_p4_tool(self, workspace):
        return P4Tool(p4_user, p4_host, p4_passwd, workspace, "")

    def get_latest_version(self):
        """
        获取p4上最新版本号
        """
        p4_path = "{}*".format(self.p4_path)
        dir_names = p4tool.get_dir_names(p4_path)
        valid_list = [i for i in dir_names if re.search(r"^v(\d+)v(\d+)", i)]
        valid_list.sort()
        return valid_list[-1] if valid_list else ""

    def get_ftp_latest_version(self):
        """
        获取ftp上最新版本号
        """
        version = ""
        latest_first_version, latest_send_version = "", ""
        fu = FtpUtils(root_path=self.h5_ftp_path)
        first_package_list = fu.get_files()
        if first_package_list:
            first_package_list.sort()
            latest_first_version = first_package_list[-1]
            ftu = FtpUtils(root_path="{}/{}".format(self.h5_ftp_path, latest_first_version))
            send_package_list = ftu.get_files()
            if send_package_list:
                send_package_list.sort()
                second_version = send_package_list[-1]
            else:
                second_version = "v0"
            latest_send_version = "v{}".format(int(second_version.split("v")[-1]) + 1)
            version = "{}{}".format(latest_first_version, latest_send_version)
        return {"version": version, "first_version": latest_first_version, "second_version": latest_send_version}

    def get_logger(self, log_file):
        """
        记录日志
        """
        logging.basicConfig(
            filename=log_file,
            filemode="w",
            level=logging.INFO,
            format="%(asctime)s.%(msecs)d [%(threadName)s] %(levelname)s %(module)s - %(message)s",
        )

    def get_h5_inc_package(self, source_path, config_path, first_version, second_version, package_path):
        """
        获取 h5 资源增量包
        """
        result_dict = {"result": "false", "source_zip_path": "", "config_path": "", "source_path": "", "log_path": ""}
        start_time = time.time()
        h5_store_path = os.path.join(package_path, "{}{}".format(first_version, second_version))
        if not os.path.exists(h5_store_path):
            os.makedirs(h5_store_path)
        self.get_logger(os.path.join(h5_store_path, "h5.log"))
        self.logger.info("开始资源增量打包处理")
        new_version = "{}/{}".format(first_version, second_version)
        self.logger.info("开始获取新的资源信息")
        new_data_list = self.get_new_data(source_path, new_version)
        self.logger.info("开始读取旧的资源配置表")
        old_data_list = self.read_csv_file(config_path)
        # 增量比对
        insert_list, delete_list, update_list = self.compared(old_data_list, new_data_list)
        self.logger.info("新增的文件信息列表:{}".format(insert_list))
        self.logger.info("删除的文件信息列表:{}".format(delete_list))
        self.logger.info("更新的文件信息列表:{}".format(update_list))
        if not insert_list and not update_list:
            return result_dict
        package_source_dir = os.path.join(h5_store_path, "source")
        package_source_path = os.path.join(package_source_dir, "h5", first_version, second_version)
        new_config_path = os.path.join(h5_store_path, "config", "h5_res_list.csv")
        self.logger.info("开始汇总增量资源包内的文件, 生成增量包路径:{}".format(package_source_dir))
        self.store_inc_source_package(package_source_path, insert_list, update_list, source_path)
        # 压缩文件
        self.zip_source_package(package_source_dir, os.path.join(h5_store_path, "{}{}".format(first_version, second_version)))
        self.logger.info("开始生成新的资源索引表配置, 文件路径: {}".format(new_config_path))
        self.concat_new_config_file(config_path, insert_list, update_list, delete_list, new_config_path)
        self.logger.info("增加打包结束, 用时: {}s".format(time.time() - start_time))
        result_dict["result"] = "true"
        result_dict["source_path"] = package_source_dir
        result_dict["source_zip_path"] = "{}.zip".format(os.path.join(h5_store_path, "{}{}".format(first_version, second_version)))
        result_dict["config_path"] = new_config_path
        result_dict["log_path"] = os.path.join(h5_store_path, "h5.log")
        return result_dict

    def store_inc_source_package(self, store_path, insert_list, update_list, source_path):
        """
        存放增量包资源
        """
        source_dir_path = os.path.dirname(source_path)
        if not os.path.exists(store_path):
            os.makedirs(store_path)
        for update in update_list:
            rel_path = update["res_path"].replace("/", os.path.sep).replace("\\", os.path.sep)
            self.copy_source_file(rel_path, source_dir_path, store_path)
        for insert in insert_list:
            rel_path = insert["res_path"].replace("/", os.path.sep).replace("\\", os.path.sep)
            self.copy_source_file(rel_path, source_dir_path, store_path)

    def concat_new_config_file(self, config_path, insert_list, update_list, delete_list, new_config_path):
        """
        生成新的配置文件
        """
        new_config_dir_name = os.path.dirname(new_config_path)
        if not os.path.exists(new_config_dir_name):
            os.makedirs(new_config_dir_name)
        if os.path.exists(new_config_path):
            os.remove(new_config_path)
        delete_res_path_list = [i["res_path"] for i in delete_list]
        update_res_path_dict = {i["res_path"]: i for i in update_list}
        fieldnames = ["res_version", "res_path", "crc32"]
        with open(config_path, "r") as fp, open(new_config_path, "w", newline="") as new_fp:
            csv_dict_obj = csv.DictReader(fp)
            writer = csv.DictWriter(new_fp, fieldnames=fieldnames)
            writer.writeheader()
            for item in csv_dict_obj:
                # 删除的跳过
                if item["res_path"] in delete_res_path_list:
                    continue
                # 修改的替换
                if item["res_path"] in update_res_path_dict:
                    item = update_res_path_dict[item["res_path"]]
                writer.writerow(item)
            # 写入新增的
            for i in insert_list:
                writer.writerow(i)

    def copy_source_file(self, rel_path, source_dir_path, store_path):
        """
        copy文件
        """
        file_path = os.path.join(source_dir_path, rel_path)
        dst_path = os.path.join(store_path, rel_path)
        dst_dir_path = os.path.dirname(dst_path)
        if not os.path.exists(dst_dir_path):
            os.makedirs(dst_dir_path)
        shutil.copyfile(file_path, dst_path)

    def compared(self, old_list, new_list):
        """
        比对 获取 增删改
        """
        old_list.sort(key=lambda x: x.get("res_path", ""))
        new_list.sort(key=lambda x: x.get("res_path", ""))
        ins_list, del_list, upd_list = [], [], []
        if old_list and new_list:
            while old_list and new_list:
                old_tmp = old_list[0]["res_path"]
                new_tmp = new_list[0]["res_path"]
                if old_tmp > new_tmp:
                    ins_list.append(new_list.pop(0))
                elif old_tmp < new_tmp:
                    del_list.append(old_list.pop(0))
                else:
                    old_section = old_list.pop(0)
                    new_section = new_list.pop(0)
                    if old_section["crc32"] == new_section["crc32"]:
                        continue
                    upd_list.append(new_section)
            if old_list:
                del_list.extend(old_list)
            if new_list:
                ins_list.extend(new_list)
        elif old_list and new_list == []:
            del_list = old_list
        elif old_list == [] and new_list:
            ins_list = new_list
        return ins_list, del_list, upd_list

    def get_new_data(self, source_path, version):
        """
        获取新数据
        """
        new_data_list = list()
        for dir_path, _, filenames in os.walk(source_path):
            for filename in filenames:
                file_path = os.path.join(dir_path, filename)
                relative_path = "texture{}".format(file_path.replace(source_path, "")).replace(os.path.sep, "/")
                file_crc32_value = self.get_file_crc32_value(file_path)
                new_data_list.append({"res_version": version, "res_path": relative_path, "crc32": str(file_crc32_value)})
        return new_data_list

    def get_file_crc32_value(self, file_path):
        """
        获取文件的crc32值
        """
        with open(file_path, "rb") as fp:
            temp = binascii.crc32(fp.read())
            if temp > 0x7FFFFFFF:  # 大于 0xFFFFFFFF的一半：0x7fffffff：即为相对的负数，需要把uint转为int类型
                return -((temp - 1) ^ 0xFFFFFFFF)
            else:
                return temp

    def read_csv_file(self, path):
        """
        读取 csv文件
        """
        data_list = list()
        with open(path, "r") as fp:
            csv_dict_obj = csv.DictReader(fp)
            for item in csv_dict_obj:
                data_list.append({"res_version": item["res_version"], "res_path": item["res_path"], "crc32": item["crc32"]})
        return data_list

    def write_csv_file(self):
        """
        写入csv的示例方法
        """
        data = {"id": "123", "name": "xx", "age": "26"}
        # 加入参数“newline=''” 可以避免隔行添加问题
        with open("123.csv", "w", newline="") as f:
            fieldnames = {"id", "name", "age"}
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerow(data)

    def zip_source_package(self, source_path, base_path):
        """
        压缩文件
        """
        self.logger.info("开始压缩增量资源zip包, 待压缩文件路径:{}".format(source_path))
        shutil.make_archive(base_name=base_path, format="zip", root_dir=source_path)
        self.logger.info("压缩增量资源zip包成功, 压缩包文件路径:{}.zip".format(base_path))

    def upload_h5_ftp(self, source_path, source_zip_path, config_path, h5_version):
        """
        上传文件到ftp
        """
        upload_result = {"upload_source": "false", "upload_zip": "false", "upload_config": "false"}
        if os.path.exists(source_path):
            try:
                FtpUtils(root_path=self.ftp_path).upload_whole_package(source_path)
            except Exception as exp:
                raise ValueError(exp)
            else:
                upload_result["upload_source"] = "true"
        if os.path.exists(config_path):
            try:
                FtpUtils(root_path="{}/update_config/h5_config".format(self.ftp_path)).upload_single_file(config_path)
                FtpUtils(root_path="{}/h5_server_resources/{}".format(self.ftp_path, h5_version)).upload_single_file(config_path)
            except Exception as exp:
                raise ValueError(exp)
            else:
                upload_result["upload_config"] = "true"
        if os.path.exists(source_zip_path):
            try:
                FtpUtils(root_path="{}/h5_server_resources/{}".format(self.ftp_path, h5_version)).upload_single_file(source_zip_path)
            except Exception as exp:
                raise ValueError(exp)
            else:
                upload_result["upload_zip"] = "true"
        return upload_result

    def check_version_exists(self, version):
        """
        校验版本号
        """
        result = {"check_result": "false", "check_msg": ""}
        if version:
            fu = FtpUtils(root_path="{}/h5_server_resources".format(self.ftp_path))
            files = fu.get_files()
            if version in files:
                result["check_result"] = "true"
            else:
                result["check_msg"] = "版本号: {}不存在".format(version)
        else:
            result["check_msg"] = "输入的版本号不能为空"
        return result

    def get_source_from_ftp(self, version, package_path):
        """
        从ftp上拉取资源
        """
        fu = FtpUtils(root_path="{}/h5_server_resources".format(self.ftp_path))
        fu.download_whole_package(version, package_path)

    def upload_tencent_ftp(self, file_path, branch):
        """
        上传 至指定版本的 H5 外网ftp
        """
        fu = UploadTencentFtp()
        if not self.check_public_dirs_exists("/server/x5m_server", branch, reg=r"^\d+\.\d+\.[\d\.]+$"):
            raise ValueError("ftp /server/x5m_server目录下不存在目录{}".format(branch))
        # 判断 /server/x5m_server/<x.xx.x> 目录下有无 h5目录
        ret = self.check_public_dirs_exists("/server/x5m_server/{}".format(branch), "h5")
        if not ret:
            # 则创建 H5目录
            fu.create_public_remote_dir("/server/x5m_server/{}".format(branch), "h5")
        # 上传 文件
        filename = os.path.basename(file_path)
        fu.upload_public_remote_file("/server/x5m_server/{}/h5/{}".format(branch, filename), file_path)

    def check_public_dirs_exists(self, remote_path, dir_name, reg=None):
        """
        外网FTP 校验 tencent ftp上 目录是否存在
        """
        dir_names = UploadTencentFtp().get_public_ftp_dirs(remote_path)
        # 如果传入正则匹配，则过滤筛选目录
        if reg:
            dir_names = [i for i in dir_names if re.search(reg, i)]
        return dir_name in dir_names


if __name__ == "__main__":
    h = H5Manager()
    h.get_latest_version()
