import logging
import os
from enum import Enum
from fnmatch import fnmatchcase
from pathlib import Path
from typing import List

import xlrd
from ..inst import LanguageEnum, P4Tool, p4_host, p4_passwd, p4_user
from ..utils.common import Common


class ResourceTypeEnum(Enum):
    """手游资源类型枚举"""

    texture = "texture"
    effect = "effect"
    action = "act"


class Distributor:
    def __init__(self) -> None:
        self.language = LanguageEnum(os.getenv("LANGUAGE", default=LanguageEnum.chin_simp.value))
        self.p4client: P4Tool = self.__create_p4client()

    def __create_p4client(self):
        return P4Tool(
            user=p4_user,
            port=p4_host,
            passwd=p4_passwd,
            workspace=f"dgm_jenkins_{self.language}_" + Common.get_host_ip(),
            root="",
        )

    @property
    def download_dir(self) -> Path:
        return Path(os.getenv("WORKSPACE", default="./"))

    @property
    def rule_name(self) -> str:
        """独有资源表格名称"""
        return "language_unique_resources.csv"

    def get_rule_p4_path(self, res_type: ResourceTypeEnum, language: LanguageEnum = None):
        depot = "x5mplan"
        if not language:
            language = self.language
        # 简体应该没有这个表
        if language == LanguageEnum.chin_trad:
            depot += "_trad"

        return f"//{depot}/resmg/{res_type.value}/{self.rule_name}"

    def download_rule(self, p4_path: str) -> Path:
        logging.info(f"download p4_path: {p4_path}")
        _path = self.download_dir / Path(p4_path.replace("//", "/")).name
        self.p4client.print(p4_path, str(_path))
        return _path

    def load_rule(self, rule_path: Path):
        logging.info(f"load rule from {rule_path}")
        rule_list = []
        data = xlrd.open_workbook(rule_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            temp.update({str(config_data[0]).strip(): str(config_data[1]).strip()})
            rule_list.append({[temp.replace("#", "[0-9]") for temp in data][0]: data[[temp for temp in data][0]]})

        logging.info(f"rules: {rule_list[:100]} ...")
        return rule_list

    def is_match_rule(self, rule_list, file: str) -> bool:
        for rule in rule_list:
            for rule_name in rule:
                if fnmatchcase(file.split("/")[-1], rule_name):
                    return True
        return False

    def distribute(self, files: List[str], res_type: ResourceTypeEnum):
        """分发公共资源到非简中目录"""
        if not self.language != LanguageEnum.chin_simp:
            return

        for language in LanguageEnum.__members__:
            if language == LanguageEnum.chin_simp:
                continue

            logging.info(f"分发 {language} 开始")

            rule_p4_path = self.get_rule_p4_path(res_type, language)
            rule_path = self.download_rule(rule_p4_path)
            if not rule_path.exists():
                logging.warn(f"下载 {rule_p4_path} 失败")
                continue
            rule_list = self.load_rule(rule_path)
            for file in files:
                # 匹配表格规则，如果匹配了则不需要走分发
                if self.is_match_rule(rule_list, file):
                    continue
                # TODO: 分发上传到指定位置
                logging.info(f"分发: {file}")
            logging.info(f"分发 {language} 结束")
