import logging
import os
import typing
from P4 import P4, P4Exception


class P4Tool(object):
    def __init__(self, user: str, port: str, passwd: str, workspace: str, root: str, encoding="", charset="", exception_level=""):
        self.root = root
        self.p4 = P4()
        # self.p4.logger = logging
        self.p4.port = port
        self.p4.user = user
        self.p4.password = passwd
        self.p4.client = workspace
        self.workspace = workspace
        if encoding:
            self.p4.encoding = encoding
        if charset:
            self.p4.charset = charset
        if exception_level:
            self.p4.exception_level = exception_level

    def __del__(self):
        self.disconnect()
        logging.debug("p4_tool del")

    def connect(self):
        if not self.p4.connected():
            logging.info("connect to p4 server start")
            self.p4.connect()
            logging.info("connect to p4 server success")

    def disconnect(self):
        if self.p4.connected():
            logging.info("disconnect p4 start")
            self.p4.disconnect()
            logging.info("disconnect p4 success")

    def init_p4_view(self, view: list, options=None):
        try:
            self.connect()
            self.p4.run_login(password=self.p4.password)
            p4_client = self.p4.fetch_client(self.p4.client)
            p4_client["Root"] = self.root
            p4_client["View"] = view
            p4_client["Host"] = ""
            if options:
                p4_client["Options"] = options

            self.p4.save_client(p4_client)
            result = 0
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def fetch_files_to_local(self, p4_depots: typing.List[str], force=False) -> typing.List[dict]:
        if not p4_depots:
            return []
        self.connect()
        p4 = self.p4
        client = p4.fetch_client(self.workspace)
        client["Root"] = self.root
        p4.save_client(client)
        p4.client = self.workspace
        try:
            if force:
                p4.run_sync('-f', p4_depots)
            else:
                p4.run_sync(p4_depots)
        except P4Exception as e:
            if e.errors:
                raise e
        return p4.run_fstat('-Op', '-T', 'path,depotFile', p4_depots)

    def get_p4_view(self):
        try:
            self.connect()
            p4_client = self.p4.fetch_client(self.p4.client)
            result = p4_client["View"]
        except P4Exception as error:
            logging.error(error)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        return result

    def set_p4_view(self, view: list):
        try:
            self.connect()
            p4_client = self.p4.fetch_client(self.p4.client)
            p4_client["View"] = view
            self.p4.save_client(p4_client)
            result = 0
        except P4Exception as error:
            logging.error(error)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        return result

    def update_p4_view(self, ext_view: list, replace=False, **kwargs):
        """增量更新p4 client view

        Args:
            ext_view:
            replace:
            **kwargs:

        Returns:

        """
        self.connect()
        self.p4.run_login(**kwargs)
        p4_client = self.p4.fetch_client(self.p4.client)
        view = set(p4_client["View"])
        logging.info(f"update_p4_view: current p4 view: {p4_client['View']}")
        #已经包含view, 不用更新
        if view.issuperset(set(ext_view)):
            return 0
        view.update(set(ext_view))
        p4_client["View"] = list(view)
        logging.info(f"update_p4_view: updated p4 view: {p4_client['View']}")
        return self.p4.save_client(p4_client)

    def init_p4_view_remove(self, views: list, remove_view="//"):
        p4_View = []
        for i in range(len(views)):
            v = self.format_view(views[i], remove_view)
            if v != "":
                p4_View.append(v)
        if len(p4_View) == 0:
            return -1
        try:
            self.connect()
            self.p4.run_login()
            p4_client = self.p4.fetch_client(self.p4.client)
            p4_client["Root"] = self.root
            p4_client["View"] = p4_View
            self.p4.save_client(p4_client)
            result = 0
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1

        # finally:
        #     self.disconnect()
        return result

    def login(self):
        try:
            self.connect()
            self.p4.run_login()
            result = 0
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def files(self, *args, **kwargs):
        try:
            self.connect()
            result = self.p4.run("files", *args, **kwargs)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def dirs(self, *args, **kwargs):
        try:
            self.connect()
            result = self.p4.run("dirs", *args, **kwargs)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def describe(self, *args, **kwargs):
        try:
            self.connect()
            result = self.p4.run("describe", *args, **kwargs)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def changes(self, *args, **kwargs):
        try:
            self.connect()
            result = self.p4.run("changes", *args, **kwargs)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def sync(self, *args, **kwargs):
        logging.info(f"p4 sync {args} {kwargs}")
        try:
            self.connect()
            result = self.p4.run("sync", *args, **kwargs)
        except P4Exception as error:
            logging.error(error)
            raise P4Exception(error)
        # finally:
        #     self.disconnect()
        return result

    def clean(self, *args, **kwargs):
        logging.info(f"p4 clean {args} {kwargs}")
        try:
            self.connect()
            result = self.p4.run("clean", *args, **kwargs)
        except P4Exception as error:
            logging.error(error)
            raise P4Exception(error)
        # finally:
        #     self.disconnect()
        return result

    def edit(self, *args, **kwargs):
        try:
            self.connect()
            result = self.p4.run("edit", *args, **kwargs)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def add(self, *args, **kwargs):
        try:
            self.connect()
            self.p4.run("add", *args, **kwargs)
            result = 0
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def reconcile(self, *args, **kwargs):
        try:
            self.connect()
            ret = self.p4.run("reconcile", *args, **kwargs)
            logging.info(ret)
            result = 0
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def reconcile_ui(self, *args, **kwargs):
        try:
            self.connect()
            ret = self.p4.run("reconcile", *args, **kwargs)
            logging.info(ret)
            result = 0
        except P4Exception as error:
            logging.error(error)
            temp_warnings = []
            for e in self.p4.warnings:
                logging.warning(e)
                if str(e).split(" - ")[-1] in [
                    "can't edit file with pending integrations.",
                    "can't edit exclusive file already opened.",
                    "can't add exclusive file already opened.",
                ]:
                    temp_warnings.append(e)
            if temp_warnings:
                raise Exception(temp_warnings)
            for e in self.p4.errors:
                logging.error(e)
            if self.p4.errors:
                raise Exception(self.p4.errors)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def submit(self, *args, **kwargs):
        try:
            self.connect()
            kwargs["encoding"] = "gbk"
            result = self.p4.run("submit", *args, **kwargs)
            logging.info(result)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            self.revert("//...")
            return -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def revert(self, *args, **kwargs):
        try:
            self.connect()
            result = self.p4.run("revert", *args, **kwargs)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def delete(self, *args, **kwargs):
        try:
            self.connect()
            self.p4.run("delete", *args, **kwargs)
            result = 0
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def diff(self, *args, **kwargs):
        try:
            self.connect()
            result = self.p4.run("diff", *args, **kwargs)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def resolve(self, *args, **kwargs):
        try:
            self.connect()
            result = self.p4.run("resolve", *args, **kwargs)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        # finally:
        #     self.disconnect()
        return result

    def format_view(self, view: str, remove_view):
        if not view.startswith("//"):
            return ""
        p4_view = str.format("{} //{}/{}", view, self.p4.client, view[len(remove_view) :])
        return p4_view

    def get_max_changelist(self, path) -> int:
        try:
            self.connect()
            ret = self.p4.run("changes", "-m", "1", path)
            result = int(ret[0]["change"])
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        except:
            result = -1
        # finally:
        #     self.disconnect()
        return result

    def get_latest_change_list(self, path: str):
        try:
            self.connect()
            ret = self.p4.run("changes", "-m", "1", path)
            result = ret[0]["change"]
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        except:
            result = -1
        # finally:
        # self.disconnect()
        return result

    def get_describe_with_changelist(self, change_list):
        try:
            self.connect()
            result = self.p4.run("describe", "-s", change_list)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        except:
            result = -1
        # finally:
        #     self.disconnect()
        return result

    def get_change_lists(self, path: str, change_list: str, latest_change_list) -> list:
        try:
            result = []
            self.connect()
            change_results = self.p4.run("changes", "{0}@{1},{2}".format(path, change_list, latest_change_list))
            for res in change_results:
                result.append(res["change"])
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            result = -2 if not self.p4.errors and self.p4.warnings else -1
        except:
            result = -1
        # finally:
        #     self.disconnect()
        return result

    def check_file_exist(self, path: str) -> bool:
        try:
            self.connect()
            self.p4.run("files", "-e", path)
        except P4Exception:
            return False
        except:
            return False
        return True

    def check_dir_exist(self, dir_path: str):
        try:
            self.connect()
            if dir_path.endswith("..."):
                dir_path = dir_path.replace("/...", "")
            self.p4.run("dirs", dir_path)
        except P4Exception:
            return False
        except:
            return False
        return True

    def get_dir_names(self, path) -> list:
        dir_names = []
        try:
            self.connect()
            results = self.p4.run("dirs", path)
            for res in results:
                dir_names.append(res["dir"].split("/")[-1])
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            return []
        except:
            return []
        # finally:
        #     self.disconnect()
        return dir_names

    def get_file_names_without_deleted(self, p4_path: str):
        file_names = []
        try:
            self.connect()
            results = self.p4.run("files", "-e", p4_path)
            for res in results:
                file_names.append(os.path.splitext(res["depotFile"].split("/")[-1])[0])
        except Exception as e:
            logging.error(e)
            return []
        except:
            return []
        # finally:
        #     self.disconnect()
        return file_names

    def get_update_files(self, path: str, change_list: str, latest_chang_list: str):
        """
        get_update_files 获取两个changelist组成的区间内更新的文件 (change_list,latest_chang_list]
        :param p4_path: p4路径,需要以// 开头，不支持使用本地路径,使用本地路径时，如果不指定workspace会出现默认workspace的问题
        :param change_list: changelist区间头
        :param latest_chang_list:  changelist区间尾
        :return:
        """
        update_files = []
        if not path.startswith("//"):
            raise Exception("p4路径需要以//开头")
        try:
            # p4_path 不能以... 结尾 dirs命令只支持*
            p4_path_without_suffix = path
            if path.endswith("..."):
                p4_path_without_suffix = path.replace("/...", "")
            else:
                pass
                # p4_path = os.path.join(path, '...').replace('\\', '/')

            self.connect()
            # 先看p4_path是否在p4上存在  如果不存在就会抛异常了 所以不需要额外判断
            self.p4.run("dirs", p4_path_without_suffix)

            change_lists = self.get_change_lists(path, change_list, latest_chang_list)
            for cl in change_lists:
                depot_files = self.get_describe_with_changelist(cl)["depotFile"]
                for depot_file in depot_files:
                    if not self.check_file_exist(depot_file):
                        continue
                    if depot_file not in update_files:
                        update_files.append(depot_file)
        except P4Exception as error:
            logging.error(error)
            for e in self.p4.warnings:
                logging.warning(e)
            for e in self.p4.errors:
                logging.error(e)
            return -2 if not self.p4.errors and self.p4.warnings else -1
        except:
            return -1
        # finally:
        #     self.disconnect()
        return update_files

    def get_subdirectory_name(self, path: str) -> list:
        try:
            temp_dict = []
            get_index = len(path.split("/")) - 1
            results = self.p4.run("files", "-e", path)
            for i in results:
                temp_dict.append(i["depotFile"].split("/")[get_index])
            return list(set(temp_dict))
        except:
            return []


class P4ToolManager(object):
    def __init__(self):
        self.__tool_pool = {}

    def register(self, p4_depot_sign, p4_tool: P4Tool):
        self.__tool_pool[p4_depot_sign] = p4_tool

    @staticmethod
    def __get_p4_depot_sign(p4_path: str):
        p4_depot_sign = p4_path.replace("//", "").split("/")[0]
        return p4_depot_sign

    def get_p4_tool(self, path) -> P4Tool:
        sign = self.__get_p4_depot_sign(path)
        return self.__tool_pool[sign]
