# -*- coding: UTF-8 -*-

import os
import abc
import logging
import re
import shutil
import stat
import typing
from collections import defaultdict
from fnmatch import fnmatchcase as match
from pathlib import Path
import xlrd

from __init__ import *
from language.distributor import Distributor


class ResourceManager(metaclass=abc.ABCMeta):
    def __init__(self, **kwargs):
        self.latest_changelist = ""
        self.file_list = []
        self.prefix = ""
        self.kind = ""
        self.depot_user_dict = defaultdict(list)  # 资源路径与提交人的映射字典
        self.depot_user_list = list()  # 本次更新的提交人信息列表
        self.depot_desc_dict = dict()  # 用于记录提交的文件 描述信息
        self.distributed_resources = ""

    def __set_changelist__(self, changelist):
        if self.latest_changelist == "":
            self.latest_changelist = 0
        if int(changelist) > int(self.latest_changelist):
            self.latest_changelist = changelist

    def get_resource_updates(self, path=None) -> list:
        depot_files = self.get_update_files(path)
        # logging.info(f"depot_files: {depot_files}")
        self.get_updates(depot_files)
        return self.file_list

    @abc.abstractmethod
    def get_updates(self, depot_files: list):
        pass

    def get_p4_tool(self, workspace: str) -> P4Tool:
        return P4Tool(p4_user, p4_host, p4_passwd, workspace, "")

    def submit_files_to_p4(self, file_paths, root, platform, workspace, desc, source_changelist, bak_line=False):
        if file_paths == "":
            resources_path = os.path.join(
                root, "x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/resources.txt".replace("/", os.path.sep).replace("\\", os.path.sep)
            )
            with open(resources_path, "r", encoding="UTF-8") as f:
                file_paths = f.read()
        files = file_paths.split(",")
        if bak_line is False:
            changelist = self.submit(platform, workspace, files, desc, root)
        else:
            changelist = self.submit_bak(platform, workspace, files, desc, root)
        print(changelist)
        print(self.distributed_resources)
        # 待完善逻辑， p4提交报错需要退出流水线
        if changelist == "":
            raise Exception("提交p4报错。请检查打包结果是否正常，或者查看需要提交的文件已经于p4 pending中存在。")

        config.set("P4", self.kind, source_changelist)
        config.write(open("../config/config.ini", "w"))

    @abc.abstractmethod
    def submit(self, platform, workspace: str, files: list, desc, root) -> str:
        pass

    def submit_bak(self, platform, workspace: str, files: list, desc, root) -> str:
        return ""

    @staticmethod
    def edit_file(p4_srv: P4Tool, path):
        path_lower = path.lower().strip()
        logging.info(f"path_lower is {path_lower}")
        if os.path.exists(path_lower):
            if p4_srv.files("-e", path_lower) in [p4_error_code, p4_warning_code]:
                res = p4_srv.add(path_lower)
                logging.info(f"add file {path_lower}, res: {res}")
            else:
                res = p4_srv.edit(path_lower)
                logging.info(f"edit file {path_lower}, res: {res}")
        else:
            logging.warn(f"{path_lower} does not exist")

    @staticmethod
    def write_resources(resources: str, resources_path=None):
        if not resources_path:
            resources_path = os.path.join(os.getenv("WORKSPACE"), "x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/resources.txt")

        path, _ = os.path.splitext(resources_path)
        if not os.path.exists(path):
            os.makedirs(path)

        with open(resources_path, "w", encoding="utf-8") as f:
            f.write(resources)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
            return json_result

        view_list = []
        p4_view_set = set()
        for file in self.file_list:
            index = file.find("art_src")
            if index == -1:
                continue
            house_index = file.find("art_src/house")
            if house_index == -1:
                real_path = "assets/resources/art" + file[index + 7 :]
            else:
                real_path = "assets/staticresources/art/3d" + file[index + 7 :]
            view_list.append(real_path)

            package_dir, package_local_dir = None, None
            if self.kind in ["npc", "beast", "pet"]:
                package_dir = "/".join(file.split("/")[:9])
                package_local_dir = "/".join(real_path.split("/")[:6])

            if self.kind in ["chair", "model", "effect"]:
                package_dir = "/".join(file.split("/")[:8])
                package_local_dir = "/".join(real_path.split("/")[:5])

            if self.kind in ["house"]:
                package_dir = file
                package_local_dir = real_path

            if package_dir and package_local_dir:
                p4_view_set.add(f"{package_dir}/... //${{p4Client}}/x5_mobile/mobile_dancer/arttrunk/client/{package_local_dir}/...")

        views = ",".join(view_list)
        p4_view = ";".join(p4_view_set)
        json_result["update"] = "true"
        json_result["package_list"] = views
        json_result["changelist"] = self.latest_changelist
        json_result["p4_view"] = p4_view

        send_user_list = []
        for user in self.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                send_user_list.append("{}@h3d.com.cn".format(user))
        json_result["send_user"] = ",".join(send_user_list)
        self.write_resources(views)
        return json_result

    def get_update_files(self, path=None) -> list:
        depot_result = []
        delete_files = []
        if path is None:
            results = self.get_descs_and_changelist_default()
        else:
            path = f"{path}/...".replace("\\", "/")
            results = self.get_descs_and_changelist(path)
        logging.info(f"results: {results}")
        for res in results:
            for desc in res:
                for index in range(len(desc["depotFile"])):
                    if desc["action"][index] == "delete" or desc["action"][index] == "move/delete":
                        delete_files.append(desc["depotFile"][index])
                        continue
                    if desc["depotFile"][index] not in depot_result:
                        depot_result.append(desc["depotFile"][index])
                    self.depot_user_dict[desc["depotFile"][index]].append(desc["user"])
                    self.depot_desc_dict[desc["depotFile"][index]] = desc["desc"].strip()
        for del_file in delete_files:
            if del_file in depot_result:
                if p4tool.files("-e", del_file) in [p4_error_code, p4_warning_code]:
                    depot_result.remove(del_file)
                    if del_file in self.depot_user_dict:
                        self.depot_user_dict.pop(del_file)
                        self.depot_desc_dict.pop(del_file)
        return depot_result

    def get_descs_and_changelist(self, p4_path) -> list:
        results = []
        need_desc_changelists = []
        latest_info = p4tool.changes("-m", "1", p4_path)
        if not latest_info:
            return []
        change_results = p4tool.changes("{0}@{1},{2}".format(p4_path, int(config["P4"][self.kind]) + 1, latest_info[0]["change"]))
        if change_results in [p4_error_code, p4_warning_code]:
            return []
        for res in change_results:
            need_desc_changelists.append(res["change"])

        for changelist in need_desc_changelists:
            desc = p4tool.describe("-s", changelist)
            if desc in [p4_error_code, p4_warning_code]:
                return []

            results.append(desc)

        self.__set_changelist__(latest_info[0]["change"])

        return results

    def get_descs_and_changelist_default(self) -> list:
        p4_path = f"{self.prefix}/...".replace("\\", "/")
        results = self.get_descs_and_changelist(p4_path)
        return results

    @staticmethod
    def get_latest_files(path, f=None):
        files = os.listdir(path)
        if f:
            files = filter(f, files)

        return max(files)

    def check_version(self, check_path, first_changelist="") -> int:
        """
        当check_path中包含合并描述时, 返回最后一个合并的changelist
        当不包含合并描述时, 返回非合并资源changelist - 1
        """
        depot_files_path = "{}{}#head".format(check_path, "" if not first_changelist else "@" + first_changelist + ",")
        logging.info(f"depot_files_path: {depot_files_path}")
        depot_files = p4tool.changes("-m30", "-r", "-l", depot_files_path)
        for file in depot_files:
            desc = file.get("desc")
            if not re.match(re.compile(r"(^(Branching|Merging).+to.+)|(^Populate -o.+)", re.S), desc):
                return int(file.get("change")) - 1

        first_changelist = depot_files[-1].get("change")
        if len(depot_files) == 1:
            return int(first_changelist)

        return self.check_version(check_path, first_changelist)

    @staticmethod
    def get_file_size(file_path: str) -> float:
        if os.path.exists(file_path):
            return os.stat(file_path).st_size / 1024 / 1024

        return 0

    def get_large_ab(self, files_path: list) -> list:
        large_ab = []
        for file_path in files_path:
            size = self.get_file_size(file_path)
            logging.info(f"file: {file_path}, size: {size}")
            if size > 1:
                large_ab.append(file_path)

        return large_ab


class ChdResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_chd_p4_path
        self.kind = "chd"

    def get_updates(self, depot_files: list):
        for file_path in depot_files:
            # if not file_path.startswith(self.prefix):
            #     continue
            for user in self.depot_user_dict.get(file_path):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file_path) and self.depot_user_dict[file_path] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file_path])
            if file_path.startswith(os.path.join(self.prefix, "models").replace("\\", "/")):
                if file_path.endswith(".FBX") or file_path.endswith(".prefab"):
                    self.file_list.append(file_path)
            if file_path.startswith(os.path.join(self.prefix, "actions").replace("\\", "/")):
                if file_path.endswith(".FBX"):
                    self.file_list.append(file_path)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
            return json_result

        view_list = []
        special_model_files = []
        p4_view_set = set()
        for file in self.file_list:
            index = file.find("art_src")
            if index == -1:
                continue

            model_file = file[index + 7 :]
            real_path = "assets/resources/art" + model_file
            if "530000001" in model_file or "530100001" in model_file:
                special_model_files.append(real_path)
            view_list.append(real_path)

            # if "art_src/chd/actions" in file:
            #     p4_view_set.add(f"{self.prefix}/actions/... //${{p4Client}}/x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/chd/actions/...")

            package_dir = file.split("/")[8]
            p4_view_set.add(
                f"{self.prefix}/models/{package_dir}/... //${{p4Client}}/x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/chd/models/{package_dir}/..."
            )

        json_result["update"] = "true"
        json_result["package_list"] = ",".join(view_list)
        json_result["special_package_list"] = ",".join(special_model_files)
        json_result["changelist"] = self.latest_changelist
        json_result["p4_view"] = ";".join(p4_view_set)
        send_user_list = list()
        for user in self.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                send_user_list.append("{}@h3d.com.cn".format(user))
        json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        res_platform = "android" if platform == "android" else "iOS"
        for f in files:
            if f.find("assets/resources/art/chd/models") == -1:
                continue
            if "530000001" in f or "530100001" in f:
                f = os.path.join(root, f).replace("\\", "/")
                f = f.replace("assets/resources/art", "x5_mobile/mr/Resources/cs/{0}/assetbundles/art".format(res_platform))
            else:
                f = os.path.join(root, f).replace("\\", "/")
                f = f.replace("assets/resources/art", "x5_mobile/mr/art_release/cs/{0}/assetbundles/art".format(platform))
            path, file_name = os.path.split(f)
            file_name_without_suffix, _ = os.path.splitext(file_name)
            if path.find(f"{file_name_without_suffix}/effect") == -1 and path.find(f"{file_name_without_suffix}/prefab") == -1:
                f = f"{f[:f.rfind('/')]}/prefab/{file_name_without_suffix}"
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]

    def distribute(self, source_path, distribute_path_list, packages):
        """
        分发
        """
        package_list = packages.split(",")
        model_list = list()
        for i in package_list:
            i = i.replace("assets/resources/art/chd/models/", "")
            i_list = i.split("/")
            if i_list and i_list[0] in ["530000001", "530100001"]:
                model_list.append(i_list[0])
        if model_list:
            for dis in distribute_path_list:
                if not os.path.exists(dis):
                    os.makedirs(dis)
                self.distribute_source(source_path, dis, model_list)

    def distribute_source(self, source_path, dis_path, model_list):
        """
        分发
        """
        for dir_path, _, filenames in os.walk(source_path):
            dst_path_item = dir_path.replace(source_path, dis_path)
            if not os.path.exists(dst_path_item):
                os.makedirs(dst_path_item)
            for filename in filenames:
                file_prefix = filename.replace(".h3dmanifest", "") if filename.endswith(".h3dmanifest") else filename
                if file_prefix in model_list:
                    file_path = os.path.join(dir_path, filename)
                    os.chmod(file_path, stat.S_IWRITE)
                    dst_file_path = os.path.join(dst_path_item, filename)
                    if os.path.exists(dst_file_path):
                        os.chmod(dst_file_path, stat.S_IWRITE)
                    shutil.copy(file_path, dst_file_path)


class PetResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_pet_p4_path
        self.kind = "pet"

    def get_updates(self, depot_files: list):
        for file in depot_files:
            lower_file = file.lower()
            if not lower_file.startswith(self.prefix):
                continue
            for user in self.depot_user_dict.get(file):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file])
            if lower_file.startswith(os.path.join(self.prefix, "actions").replace("\\", "/")):
                if lower_file.endswith(".fbx"):
                    self.file_list.append(file)
            if lower_file.startswith(os.path.join(self.prefix, "models").replace("\\", "/")):
                if lower_file.find("/effect/") != -1:
                    continue
                if lower_file.endswith(".fbx") or lower_file.endswith(".prefab"):
                    self.file_list.append(file)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            f = f.lower()
            if f.find("assets/resources/art/pet") == -1:
                continue
            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", "x5_mobile/mr/art_release/cs/{0}/assetbundles/art".format(platform))
            f, _ = os.path.splitext(f)
            if f.find("/pet/models") != -1:
                path, file_name = os.path.split(f)
                if f.find("/prefab/") == -1:
                    f = os.path.join(path, os.path.join("prefab", file_name)).replace("\\", "/")
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]


class NpcResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_npc_p4_path
        self.kind = "npc"

    def get_updates(self, depot_files: list):
        p4_srv = None
        searched_path_list = []
        file_set = set()
        try:
            for file in depot_files:
                for user in self.depot_user_dict.get(file):
                    if user not in self.depot_user_list:
                        self.depot_user_list.append(user)
                # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                #     self.depot_user_list.append(self.depot_user_dict[file])
                if file.startswith(os.path.join(self.prefix, "actions").replace("\\", "/")):
                    if file.endswith(".FBX") or file.endswith(".anim"):
                        file_set.add(file)

                if file.startswith(os.path.join(self.prefix, "models").replace("\\", "/")):
                    search_path = "/".join(file.split("/")[:9])
                    if search_path in searched_path_list:
                        continue
                    logging.info(f"search_path: {search_path}")

                    if not p4_srv:
                        p4_srv = self.get_p4_tool("")
                        p4_srv.p4.exception_level = 1
                        p4_srv.login()

                    files_prefab = p4_srv.files("-e", f"{search_path}/*prefab")
                    files_fbx = []
                    if not files_prefab:
                        files_fbx = p4_srv.files("-e", f"{search_path}/*fbx")

                    for search_result in files_prefab + files_fbx:
                        file_set.add(search_result.get("depotFile"))
        except Exception as e:
            logging.error(e)
            exit(-1)

        self.file_list = list(file_set)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/npc") == -1:
                continue

            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", "x5_mobile/mr/art_release/cs/{0}/assetbundles/art".format(platform))
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]


class BeastResourcePackage(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_beast_p4_path
        self.kind = "beast"

    def get_updates(self, depot_files: list):
        p4_srv = None
        searched_path_list = []
        file_set = set()
        try:
            for file in depot_files:
                for user in self.depot_user_dict.get(file):
                    if user not in self.depot_user_list:
                        self.depot_user_list.append(user)
                # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                #     self.depot_user_list.append(self.depot_user_dict[file])

                # 如果是actions或者actions_expression目录, 直接获取prefab或者anim文件
                if file.startswith("//x5_mobile/mr/art_release/art_src/beast/actions"):
                    if file.endswith(".anim") or file.endswith(".prefab"):
                        file_set.add(file)

                # 如果是model目录
                if file.startswith("//x5_mobile/mr/art_release/art_src/beast/models"):
                    search_path = "/".join(file.split("/")[:9])
                    if search_path in searched_path_list:
                        continue

                    logging.info(f"search_path: {search_path}")
                    if not p4_srv:
                        p4_srv = self.get_p4_tool("")
                        p4_srv.p4.exception_level = 1
                        p4_srv.login()

                    files_prefab = p4_srv.files("-e", f"{search_path}/*prefab", f"{search_path}/.../*prefab")
                    files_anim = p4_srv.files("-e", f"{search_path}/*anim", f"{search_path}/.../*anim")
                    for search_result in files_prefab + files_anim:
                        file_set.add(search_result.get("depotFile"))

                    searched_path_list.append(search_path)
        except Exception as e:
            logging.error(e)
            exit(-1)

        self.file_list = list(file_set)

    def submit(self, platform, workspace: str, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/beast") == -1:
                continue

            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", "x5_mobile/mr/art_release/cs/{0}/assetbundles/art".format(platform))
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]


class EffectResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_effect_p4_path
        self.kind = "effect"

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                for user in self.depot_user_dict.get(file):
                    if user not in self.depot_user_list:
                        self.depot_user_list.append(user)
                # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                #     self.depot_user_list.append(self.depot_user_dict[file])
                if os.path.basename(file).endswith(".prefab"):
                    self.file_list.append(file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/") == -1:
                continue

            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", "x5_mobile/mr/art_release/cs/{0}/assetbundles/art".format(platform))
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)

        logging.info(f"P4 提交结果: {result}")

        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            Distributor().distribute([item["depotFile"] for item in result if "depotFile" in item], "effect")
            return result[0]["change"]


class ChairResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_chair_p4_path
        self.kind = "chair"

    def get_updates(self, depot_files: list):
        p4_srv = None
        search_path = []
        for file_path in depot_files:
            if file_path.startswith(self.prefix):
                takeout_prefix_path = file_path.replace(self.prefix, "")
                res = takeout_prefix_path.split("/")
                if len(res) > 4:
                    continue
                for user in self.depot_user_dict.get(file_path):
                    if user not in self.depot_user_list:
                        self.depot_user_list.append(user)
                # if self.depot_user_dict.get(file_path) and self.depot_user_dict[file_path] not in self.depot_user_list:
                #     self.depot_user_list.append(self.depot_user_dict[file_path])

                file_id = re.search("[0-9]{10}", file_path).group()
                file_root_path = os.path.join(file_path.split(file_id)[0], file_id)
                if file_root_path not in search_path:
                    search_path.append(file_root_path)
                    if not p4_srv:
                        p4_srv = self.get_p4_tool("")
                        p4_srv.p4.exception_level = 1
                        p4_srv.login()
                    search_files = p4_srv.files("-e", f"{file_root_path}/*prefab", f"{file_root_path}/*fbx")
                    for search_file in search_files:
                        self.file_list.append(search_file.get("depotFile"))
        # logging.info(f"file_list: {self.file_list}")
        # self.file_list = []

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/chair") == -1:
                continue

            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", "x5_mobile/mr/art_release/cs/{0}/assetbundles/art".format(platform))
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]


class ModelResourceManager(EffectResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_model_p4_path
        self.kind = "model"

    def read_excel(self, path) -> list:
        """
        读取 excel 表
        """
        data_list = list()
        wb = xlrd.open_workbook(path)
        table = wb.sheets()[0]
        rows = table.nrows
        title_data = table.row_values(0, 0, 2)
        for i in range(1, rows):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            for idx, t in enumerate(title_data):
                try:
                    if t in ["name"]:
                        val = str(int(config_data[idx])).strip()
                    else:
                        val = config_data[idx]
                except:
                    val = config_data[idx]
                temp[t] = val
            data_list.append(temp)
        return data_list

    def patch_cdn_rule(self, filename, data_list):
        """
        检查是否匹配
        """
        for i in data_list:
            ret = re.search(r"^{}".format(i), filename)
            if ret:
                return True

    def patch_hotfix_rule(self, filename, data):
        """ """
        for k, v in data.items():
            ret = re.search(r"^{}".format(k), filename)
            if ret:
                return True, v
        return False, ""

    def get_max_branch(self):
        branch_dir_results = p4tool.dirs(os.path.join(branch_path, "*").replace("\\", "/"))
        max_branch = ""
        max_first = 0
        max_second = 0
        for res in branch_dir_results:
            # noinspection PyBroadException
            try:
                dir_name = res["dir"].split("/")[-1]
                elem = dir_name.split(".")
                if elem[2] != "0":
                    continue
                first = int(elem[0])
                second = int(elem[1])
            except Exception:
                pass
            else:
                if first > max_first and second > max_second:
                    first, second, max_branch = max_first, max_second, dir_name
                elif first == max_first and second > max_second:
                    second, max_branch = max_second, dir_name

        return max_branch

    def distribute_cdn(self, root, f, platform, p4_srv, report_data_dict, filename):
        """
        分发到cdn
        """
        f = os.path.join(root, f).replace("\\", "/")
        f = f.replace("assets/resources/art", "x5_mobile/mr/art_release/cs/{0}/assetbundles/art".format(platform))
        f, _ = os.path.splitext(f)
        f_h3d = "{}.h3dmanifest".format(f)
        report_data_dict[filename] = f
        self.edit_file(p4_srv, f)
        self.edit_file(p4_srv, f_h3d)

    def distribute_hotfix(self, root, f, ab_platform, hotfix_version, p4_srv, report_data_dict, filename, file_local_path):
        """
        分发到hotfix
        """
        main_version = hotfix_version.split(".")[0].rstrip("2")
        main_version = "{}.{}.0".format(main_version[:1], main_version[1:])
        f = os.path.join(root, f).replace("\\", "/")
        f = f.replace(
            "assets/resources/art", "x5_mobile/mr/onlineupdate/{}/{}000/client/{}/assetbundles/art".format(main_version, hotfix_version, ab_platform)
        )
        hotfix_file_dir = os.path.dirname(f)
        f, _ = os.path.splitext(f)
        f_h3d = "{}.h3dmanifest".format(f)
        report_data_dict[filename] = f
        if not os.path.exists(hotfix_file_dir):
            os.makedirs(hotfix_file_dir)
        # 将资源 从 art_release 下 复制到 真正目录下
        file_h3d_local_path = "{}.h3dmanifest".format(file_local_path)
        if os.path.exists(file_local_path):
            os.chmod(file_local_path, stat.S_IWRITE)
            if os.path.exists(f):
                os.chmod(f, stat.S_IWRITE)
            shutil.copy(file_local_path, f)
        if os.path.exists(file_h3d_local_path):
            os.chmod(file_h3d_local_path, stat.S_IWRITE)
            if os.path.exists(f_h3d):
                os.chmod(f_h3d, stat.S_IWRITE)
            shutil.copy(file_h3d_local_path, f_h3d)
        self.edit_file(p4_srv, f)
        self.edit_file(p4_srv, f_h3d)

    def distribute_resources(self, root, f, ab_platform, branch, p4_srv, report_data_dict, filename, file_local_path):
        """
        分发到主支 和 最大分支
        """
        f_base = os.path.join(root, f).replace("\\", "/")
        f = f_base.replace("assets/resources/art", "x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/{0}/assetbundles/art".format(ab_platform))
        resource_file_dir = os.path.dirname(f)
        if not os.path.exists(resource_file_dir):
            os.makedirs(resource_file_dir)
        f, _ = os.path.splitext(f)
        report_data_dict[filename] = f
        f_h3d = "{}.h3dmanifest".format(f)
        f_branch = f_base.replace(
            "assets/resources/art", "x5_mobile/mr/b/{}/ResourcePublish/CDN/SourceFiles/{}/assetbundles/art".format(branch, ab_platform)
        )
        branch_file_dir = os.path.dirname(f_branch)
        if not os.path.exists(branch_file_dir):
            os.makedirs(branch_file_dir)
        f_branch, _ = os.path.splitext(f_branch)
        f_branch_h3d = "{}.h3dmanifest".format(f_branch)
        report_data_dict["{} 分支:{}".format(filename, branch)] = f_branch
        # 将资源 从 art_release 下 复制到 真正目录下
        file_h3d_local_path = "{}.h3dmanifest".format(file_local_path)
        if os.path.exists(file_local_path):
            os.chmod(file_local_path, stat.S_IWRITE)
            if os.path.exists(f):
                os.chmod(f, stat.S_IWRITE)
            shutil.copy(file_local_path, f)
            if os.path.exists(f_branch):
                os.chmod(f_branch, stat.S_IWRITE)
            shutil.copy(file_local_path, f_branch)
        if os.path.exists(file_h3d_local_path):
            os.chmod(file_h3d_local_path, stat.S_IWRITE)
            if os.path.exists(f_h3d):
                os.chmod(f_h3d, stat.S_IWRITE)
            shutil.copy(file_h3d_local_path, f_h3d)
            if os.path.exists(f_branch_h3d):
                os.chmod(f_branch_h3d, stat.S_IWRITE)
            shutil.copy(file_h3d_local_path, f_branch_h3d)
        self.edit_file(p4_srv, f)
        self.edit_file(p4_srv, f_h3d)
        self.edit_file(p4_srv, f_branch)
        self.edit_file(p4_srv, f_branch_h3d)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        ab_platform = "iOS" if platform == "ios" else platform
        base_p4_path = os.path.join(root, "x5mplan", "resmg", "model")
        base_cdn_path = os.path.join(base_p4_path, "cdn-model-base.xlsx")
        cdn_rule_path = os.path.join(base_p4_path, "cdn-model-rule.xlsx")
        hotfix_rule_path = os.path.join(base_p4_path, "hotfix-model-rule.xlsx")
        branch = self.get_max_branch()  # b目录最大资源路径
        data_list_cdn_base = self.read_excel(base_cdn_path)  # 读取的cdn base配置文件信息
        data_list_cdn = self.read_excel(cdn_rule_path)  # 读取的cdn配置文件信息
        data_list_hotfix = self.read_excel(hotfix_rule_path)  # 读取的hotfix配置文件信息
        base_cdn_list = [i["name"] for i in data_list_cdn_base]
        # 转义 cdn规则表, 将 # 替换为[0-9], 将 ？替换为 [a-zA-Z0-9_], * 不替换
        cdn_rule_list = [i["name"].replace("#", "[0-9]").replace("?", "[a-zA-Z0-9_]") for i in data_list_cdn]
        # 转义 热更规则表， 同上
        hotfix_dict = dict()
        for i in data_list_hotfix:
            i["name"] = i["name"].replace("#", "[0-9]").replace("?", "[a-zA-Z0-9_]")
            hotfix_dict[i["name"]] = i["path"]

        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()

        report_data_dict = dict()
        for f in files:
            if f.find("assets/resources/art/model") == -1:
                continue
            filename = f.rsplit("/", maxsplit=1)[1].rstrip(".prefab")
            file_local = os.path.join(root, f).replace("\\", "/")
            file_local_path = file_local.replace("assets/resources/art", "x5_mobile/mr/art_release/cs/{0}/assetbundles/art".format(platform))
            file_local_path, _ = os.path.splitext(file_local_path)
            # 先过 cdn rule表
            patch_result = self.patch_cdn_rule(filename, cdn_rule_list)
            if patch_result:
                # 命中后 分发到 cdn
                self.distribute_cdn(root, f, platform, p4_srv, report_data_dict, filename)
            # 再过 cdn base 表
            else:
                if filename in base_cdn_list:
                    # base cdn的资源分发到cdn
                    self.distribute_cdn(root, f, platform, p4_srv, report_data_dict, filename)
                else:
                    # 然后过 热更表
                    patch_hotfix_result, hotfix_version = self.patch_hotfix_rule(filename, hotfix_dict)
                    if patch_hotfix_result:
                        self.distribute_hotfix(root, f, ab_platform, hotfix_version, p4_srv, report_data_dict, filename, file_local_path)
                    else:
                        # 都不在的 进入 版本内和最大分支
                        self.distribute_resources(root, f, ab_platform, branch, p4_srv, report_data_dict, filename, file_local_path)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            self.model_distrib_report(files, result[0]["change"], report_data_dict, platform)
            return result[0]["change"]

    def model_distrib_report(self, files, changelist, report_dict, platform):
        """
        分发报告
        """
        resources_path = os.getcwd()
        remote_path = resources_path.replace("\\", "/").replace("gitlab/Script/scripts", "log")
        if not os.path.exists(remote_path):
            os.makedirs(remote_path)
        with open(os.path.join(remote_path, "{}_{}.html".format(platform, changelist)), "w+", encoding="utf-8") as obj:
            files_num = len(files)
            obj.write("""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>{}</title></head><body>""".format("分发报告"))
            obj.write(f"<p>资源数量：{files_num}</p>")
            if report_dict:
                for i, j in report_dict.items():
                    obj.write(f"<p>文件名：{i}  文件路径：{j}</p>")
            obj.write("</body></html>")


class CameraResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_camera_p4_path
        self.kind = "camera"

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                if file.endswith(".FBX") or file.endswith(".prefab"):
                    self.file_list.append(file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            view_list = []
            views = ""
            for file in self.file_list:
                index = file.find("art_src")
                if index == -1:
                    continue
                house_index = file.find("art_src/house")
                if house_index == -1:
                    real_path = "assets/resources/art" + file[index + 7 :]
                else:
                    real_path = "assets/staticresources/art/3d" + file[index + 7 :]
                view_list.append(real_path)
            views += ",".join(view_list)
            json_result["update"] = "true"
            json_result["package_list"] = views
            json_result["changelist"] = self.latest_changelist
            send_user_list = list()
            for user in self.depot_user_list:
                if not user.endswith("@h3d.com.cn"):
                    send_user_list.append("{}@h3d.com.cn".format(user))
            json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/camera") == -1:
                continue

            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            f = f.replace("art/camera", "art/camera/prefab")
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]


class BodyPartResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_body_part_p4_path
        self.kind = "bodypart"
        self.id_list = []
        self.p4_path = []

    def get_updates(self, depot_files: list):
        try:
            p4_list = p4tool.dirs(self.prefix + "/*/*/*")
            if p4_list in [p4_error_code, p4_warning_code]:
                p4_list = []
            for res in p4_list:
                self.p4_path.append(res["dir"])

            for file in depot_files:
                # if file.find('bodypart/female/face/') != -1 or file.find('bodypart/female/plainBody/') != -1 or file.find('bodypart/male/face/') != -1 or file.find('bodypart/male/plainBody/') != -1 or file.find('/unencrypt/') != -1 or file.find(self.prefix) == -1:
                if (
                    file.find("bodypart/female/plainBody/") != -1
                    or file.find("bodypart/male/plainBody/") != -1
                    or file.find("/unencrypt/") != -1
                    or file.find(self.prefix) == -1
                ):
                    continue
                # if file.startswith(self.prefix) == -1:
                #     continue
                for user in self.depot_user_dict.get(file):
                    if user not in self.depot_user_list:
                        self.depot_user_list.append(user)
                # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                #     self.depot_user_list.append(self.depot_user_dict[file])
                dir_name = os.path.dirname(file)
                id = re.findall(r"[0-9]{10}", dir_name)
                if len(id) == 0:
                    continue
                if id[0] not in self.id_list:
                    id_index = dir_name.find(id[0])
                    if id_index == -1:
                        continue
                    dir_name = dir_name[0:id_index] + id[0]
                    if dir_name not in self.p4_path:
                        continue
                    self.id_list.append(id[0])
                    x5mobile_index = dir_name.find("x5_mobile")
                    if x5mobile_index != -1:
                        id_path = dir_name[x5mobile_index:]
                        self.file_list.append(id_path)
            self.id_list = list(set(self.id_list))
        except Exception as e:
            logging.error(e)
            exit(-1)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        path_pre = "x5_mobile/mobile_dancer/arttrunk/client"
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            idpaths = ""
            path_list = []
            views = ""
            view_list = set()
            for file in self.file_list:
                index = file.find("art_src")
                if index == -1:
                    continue
                real_path = "assets/resources/art" + file[index + 7 :]
                path_list.append(real_path)

                # temp_file = (file[:-10] + "*" + file[-8:-2] + "*").replace('female', '*').replace('male', '*')
                # temp_real_path = (real_path[:-10] + "*" + real_path[-8:-2] + "*").replace('female', '*').replace('male', '*')

                temp_file = file[:-10] + file[-10:-2] + "*"
                temp_real_path = real_path[:-10] + real_path[-10:-2] + "*"

                view_list.add("//{0}/... //{1}/{2}/{3}/...".format(temp_file, "${p4Client}", path_pre, temp_real_path))

            views += ";".join(view_list)
            idpaths += ",".join(path_list)
            ids = ""
            ids += ",".join(self.id_list)
            json_result["update"] = "true"
            json_result["idlist"] = ids
            json_result["idpaths"] = idpaths
            json_result["views"] = views
            json_result["changelist"] = self.latest_changelist
            send_user_list = list()
            for user in self.depot_user_list:
                if not user.endswith("@h3d.com.cn"):
                    send_user_list.append("{}@h3d.com.cn".format(user))
            json_result["send_user"] = ",".join(send_user_list)
            self.write_resources(ids)
        return json_result

    @staticmethod
    def __get_fail_ids(platform: str, workspace: str) -> list:
        """
        从文本中获取失败ID列表
        """
        fail_ids = []
        try:
            fail_ids_file = f"{platform}_fail_ids.txt"
            logging.info(f"fail_ids_file: {fail_ids_file}")
            p_obj = Path(fail_ids_file)

            if p_obj.exists():
                with open(p_obj, encoding="GB2312") as f:
                    fail_ids_file_content = f.read()
                    logging.info(f"fail_ids_file_content: {fail_ids_file_content}")
                    if fail_ids_file_content:
                        fail_ids = fail_ids_file_content.split(",")
            logging.info(f"fail_ids: {fail_ids}")
        except Exception as e:
            logging.error(f"get_fail_ids failed {e}")
        return fail_ids

    @staticmethod
    def __submit_file_is_in_fail_ids(file: str, fail_ids: list) -> bool:
        """
        检测需要提交的文件是否在失败列表中
        """

        try:
            for fail_id in fail_ids:
                file_id = file.split("/")[-1]
                if file_id == fail_id:
                    logging.warning(f"file {file} contained {fail_id}")
                    return True
        except Exception as e:
            logging.error(f"submit_file_is_in_fail_ids failed {e}")
        return False

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()

        # 获取失败ID列表
        fail_ids = self.__get_fail_ids(platform, workspace)

        # 用临时列表记录需要提交的资源，如果没有需要提交的资源跳过submit步骤
        submit_files = []

        for f in files:
            if f.find("art/role/bodypart") == -1:
                continue

            # 如果需要提交的文件在失败列表内，直接忽略
            in_fail_ids = self.__submit_file_is_in_fail_ids(f, fail_ids)
            if in_fail_ids:
                continue

            submit_files.append(f)

            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            f, _ = os.path.splitext(f)
            if not f[-2:] == "01":
                f = f[0 : len(f) - 2] + "01"
            self.edit_file(p4_srv, f)
            for i in range(10):
                extend_path = f + "_extend" + str(i + 1)
                if os.path.exists(extend_path):
                    self.edit_file(p4_srv, extend_path)
                else:
                    break

        if not submit_files:
            return "没有打包成功的资源需要提交，请查看打包统计"

        result = p4_srv.submit("-d", desc)
        logging.info(f"P4 提交结果: {result}")

        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            # 公共资源分发
            Distributor().distribute([item["depotFile"] for item in result if "depotFile" in item], "link_distrib")
            return result[0]["change"]


class LinkResourceManager(BodyPartResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_link_p4_path
        self.kind = "link"
        self.black_name_list = ["zhulin", "mazhenhao", "maqiangqiang", "luxudong"]

    def get_updates(self, depot_files: list):
        try:
            p4_list = p4tool.dirs(self.prefix + "/*/*")
            if p4_list in [p4_error_code, p4_warning_code]:
                p4_list = []
            for res in p4_list:
                self.p4_path.append(res["dir"])

            for file in depot_files:
                if (
                    file.find("bodypart/female/face/") != -1
                    or file.find("bodypart/female/plainBody/") != -1
                    or file.find("bodypart/male/face/") != -1
                    or file.find("bodypart/male/plainBody/") != -1
                    or file.find("/unencrypt/") != -1
                    or file.find(self.prefix) == -1
                ):
                    continue
                # if file.startswith(self.prefix) == -1:
                #     continue
                for user in self.depot_user_dict.get(file):
                    if user not in self.depot_user_list:
                        if user not in self.black_name_list:
                            self.depot_user_list.append(user)
                # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                #     if self.depot_user_dict[file] not in self.black_name_list:
                #         self.depot_user_list.append(self.depot_user_dict[file])
                dir_name = os.path.dirname(file)
                id = re.findall(r"[0-9]{10}", dir_name)
                if len(id) == 0:
                    continue
                if id[0] not in self.id_list:
                    id_index = dir_name.find(id[0])
                    if id_index == -1:
                        continue
                    dir_name = dir_name[0:id_index] + id[0]
                    if dir_name not in self.p4_path:
                        continue
                    self.id_list.append(id[0])
                    x5mobile_index = dir_name.find("x5_mobile")
                    if x5mobile_index != -1:
                        id_path = dir_name[x5mobile_index:]
                        self.file_list.append(id_path)
            self.id_list = list(set(self.id_list))
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("art/role/link") == -1:
                continue

            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            f, _ = os.path.splitext(f)
            if not f[-2:] == "01":
                f = f[0 : len(f) - 2] + "01"
            self.edit_file(p4_srv, f)
            for i in range(10):
                extend_path = f + "_extend" + str(i + 1)
                if os.path.exists(extend_path):
                    self.edit_file(p4_srv, extend_path)
                else:
                    break
        result = p4_srv.submit("-d", desc)
        logging.info(f"P4 提交结果: {result}")
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            # 公共资源分发
            Distributor().distribute([item["depotFile"] for item in result if "depotFile" in item], "link_distrib")
            return result[0]["change"]


class LinkDistribResourceManager(BodyPartResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_link_p4_path
        self.kind = "link_distrib"
        self.black_name_list = ["zhulin", "mazhenhao", "maqiangqiang", "luxudong"]

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        p4_srv = p4tool
        max_version_b = self.get_max_root_b(p4_srv)  # b目录最大资源路径
        path_pre = "x5_mobile/mobile_dancer/arttrunk/client"
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            idpaths = ""
            path_list = []
            views = ""
            view_list = set()
            for file in self.file_list:
                index = file.find("art_src")
                if index == -1:
                    continue
                real_path = "assets/resources/art" + file[index + 7 :]
                path_list.append(real_path)
                temp_file = (file[:-10] + "*" + file[-8:-2] + "*").replace("female", "*").replace("male", "*")
                temp_real_path = (real_path[:-10] + "*" + real_path[-8:-2] + "*").replace("female", "*").replace("male", "*")
                view_list.add("//{0}/... //{1}/{2}/{3}/...".format(temp_file, "${p4Client}", path_pre, temp_real_path))
            views += ";".join(view_list)
            idpaths += ",".join(path_list)
            ids = ""
            ids += ",".join(self.id_list)
            json_result["update"] = "true"
            json_result["idlist"] = ids
            json_result["idpaths"] = idpaths
            json_result["views"] = views
            json_result["max_version_b"] = max_version_b
            json_result["s_b_path"] = self.get_special_branch(p4_srv)
            json_result["changelist"] = self.latest_changelist
            send_user_list = list()
            for user in self.depot_user_list:
                if not user.endswith("@h3d.com.cn"):
                    send_user_list.append("{}@h3d.com.cn".format(user))
            json_result["send_user"] = ",".join(send_user_list)
            self.write_resources(ids)
        return json_result

    def get_updates(self, depot_files: list):
        try:
            p4_list = p4tool.dirs(self.prefix + "/*/*")
            if p4_list in [p4_error_code, p4_warning_code]:
                p4_list = []
            for res in p4_list:
                self.p4_path.append(res["dir"])

            for file in depot_files:
                if (
                    file.find("bodypart/female/face/") != -1
                    or file.find("bodypart/female/plainBody/") != -1
                    or file.find("bodypart/male/face/") != -1
                    or file.find("bodypart/male/plainBody/") != -1
                    or file.find("/unencrypt/") != -1
                    or file.find(self.prefix) == -1
                ):
                    continue
                # if file.startswith(self.prefix) == -1:
                #     continue
                for user in self.depot_user_dict.get(file):
                    if user not in self.depot_user_list:
                        if user not in self.black_name_list:
                            self.depot_user_list.append(user)
                # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                #     if self.depot_user_dict[file] not in self.black_name_list:
                #         self.depot_user_list.append(self.depot_user_dict[file])
                dir_name = os.path.dirname(file)
                id = re.findall(r"[0-9]{10}", dir_name)
                if len(id) == 0:
                    continue
                if id[0] not in self.id_list:
                    id_index = dir_name.find(id[0])
                    if id_index == -1:
                        continue
                    dir_name = dir_name[0:id_index] + id[0]
                    if dir_name not in self.p4_path:
                        continue
                    self.id_list.append(id[0])
                    x5mobile_index = dir_name.find("x5_mobile")
                    if x5mobile_index != -1:
                        id_path = dir_name[x5mobile_index:]
                        self.file_list.append(id_path)
            self.id_list = list(set(self.id_list))
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        large_ab = []
        file_dic = {}
        p4_srv = p4tool
        max_version_b = self.get_max_root_b(p4_srv)  # b目录最大资源路径
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        cdn_resources = ""
        hotfix_resources = "#"
        special_resources = "#"
        trunk_resources = "#"
        data_list_trunk = self.read_config_trunk_base_table(root)  # 读取的trunk配置文件信息
        logging.info(f"trunk rules: {data_list_trunk}")
        data_list_hotfix = self.read_config_hotfix_table(root)  # 读取的hotfix配置文件信息
        data_list_special = self.read_config_special_branch_table(root)  # 读取的特殊分支配置文件信息
        logging.info(f"special rules: {data_list_special}")
        for f in files:
            if f.find("art/role/link") == -1:
                continue

            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            f, _ = os.path.splitext(f)
            trunk_find = False
            hotfix_find = False
            special_find = False
            for special_data in data_list_special:
                for special_data_name in special_data:
                    if match(f.split("/")[-1], special_data_name):
                        special_find = True
                        break
            for hotfix_data in data_list_hotfix:
                for hotfix_data_name in hotfix_data:
                    if match(f.split("/")[-1], hotfix_data_name):
                        hotfix_find = True
                        break
            for data in data_list_trunk:
                if match(f.split("/")[-1], data):
                    trunk_find = True
                    break
            file_name = f.split("/")[-1]

            # 如果有特殊分支需求，就分发
            if special_find:
                for special_data in data_list_special:
                    for special_data_name in special_data:
                        special_data_branch = special_data[special_data_name]
                        if match(f.split("/")[-1], special_data_name):
                            p4_path_s_b = f.replace("art_release/cs", f"b/{special_data_branch}/ResourcePublish/CDN/SourceFiles")
                            local_path_s_b = "/".join(p4_path_s_b.split("/")[:-1])
                            resources_name_s_b = p4_path_s_b.split("/")[-1]
                            if not os.path.exists(local_path_s_b):
                                os.makedirs(local_path_s_b)
                            real_path_s_b = local_path_s_b + "/" + str(resources_name_s_b)
                            file_dic.update({resources_name_s_b: real_path_s_b})
                            large_abs = self.get_large_ab([real_path_s_b])
                            if large_abs:
                                large_ab += large_abs
                                continue

                            self.delete_chmod(f, real_path_s_b)
                            self.edit_file(p4_srv, real_path_s_b)

                            if hotfix_resources == "#":
                                hotfix_resources += f"{special_data_name}: {file_name}"
                                continue
                            hotfix_resources += f",{special_data_name}: {file_name}"

            if hotfix_find:
                for hotfix_data in data_list_hotfix:
                    for hotfix_data_name in hotfix_data:
                        if match(f.split("/")[-1], hotfix_data_name):
                            big_edit = str(str(hotfix_data[hotfix_data_name])[0]) + "." + str(str(hotfix_data[hotfix_data_name])[1:3]) + ".0"
                            small_edit = str(hotfix_data[hotfix_data_name]) + "000"
                            p4_path = f.replace("art_release/cs", f"onlineupdate/{big_edit}/{small_edit}/client")
                            local_path = "/".join(p4_path.split("/")[:-1])
                            resourcesName = p4_path.split("/")[-1]
                            if not os.path.exists(local_path):
                                os.makedirs(local_path)
                            real_path = local_path + "/" + str(resourcesName)
                            file_dic.update({resourcesName: real_path})
                            large_abs = self.get_large_ab([real_path])
                            if large_abs:
                                large_ab += large_abs
                                continue

                            self.delete_chmod(f, real_path)
                            self.edit_file(p4_srv, real_path)
                            # 将提交的文件放入数组

                            if hotfix_resources == "#":
                                hotfix_resources += f"{small_edit}: {file_name}"
                                continue
                            hotfix_resources += f",{small_edit}: {file_name}"
            elif trunk_find:
                p4_path_b = f.replace("art_release/cs", f"b/{max_version_b}/ResourcePublish/CDN/SourceFiles")
                p4_path_master = f.replace("art_release/cs", f"Resources/ResourcePublish/CDN/SourceFiles")
                local_path_b = "/".join(p4_path_b.split("/")[:-1])
                local_path_master = "/".join(p4_path_master.split("/")[:-1])
                resourcesNameB = p4_path_b.split("/")[-1]
                resourcesNameMaster = p4_path_master.split("/")[-1]
                resourcesNameB_b = str(p4_path_b.split("/")[-1]) + "_b"
                if not os.path.exists(local_path_b):
                    os.makedirs(local_path_b)
                if not os.path.exists(local_path_master):
                    os.makedirs(local_path_master)
                real_path_b = local_path_b + "/" + str(resourcesNameB)
                real_path_master = local_path_master + "/" + str(resourcesNameMaster)
                file_dic.update({resourcesNameB_b: real_path_b})
                file_dic.update({resourcesNameMaster: real_path_master})
                large_abs = self.get_large_ab([real_path_b, real_path_master])
                if large_abs:
                    large_ab += large_abs
                    continue

                self.delete_chmod(f, real_path_b)
                self.delete_chmod(f, real_path_master)
                self.edit_file(p4_srv, real_path_b)
                self.edit_file(p4_srv, real_path_master)

                if trunk_resources == "#":
                    trunk_resources += file_name
                    continue

                trunk_resources += f",{file_name}"
            else:
                if not f[-2:] == "01":
                    f = f[0 : len(f) - 2] + "01"
                self.edit_file(p4_srv, f)

                file_dic.update({f.split("/")[-1]: f})
                for i in range(10):
                    extend_path = f + "_extend" + str(i + 1)
                    if os.path.exists(extend_path):
                        large_abs = self.get_large_ab([extend_path])
                        if large_abs:
                            large_ab += large_abs
                            continue

                        self.edit_file(p4_srv, extend_path)

                        file_dic.update({extend_path.split("/")[-1]: f})
                        if cdn_resources == "":
                            cdn_resources += file_name
                            continue
                        cdn_resources += f",{file_name}"
                    else:
                        break

        self.distributed_resources = cdn_resources.strip() + trunk_resources.strip() + hotfix_resources.strip() + special_resources.strip()
        logging.info(f"large_ab: {large_ab}, current path: {os.getcwd()}")
        large_ab_path = os.path.join(os.getenv("WORKSPACE"), "x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/large_ab.txt")
        with open(large_ab_path, "w", encoding="utf-8") as f:
            f.write("\n".join(large_ab))

        result = p4_srv.submit("-d", desc)
        logging.info(f"P4 提交结果: {result}")

        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            self.link_distrib_report(files, result[0]["change"], file_dic)
            # 公共资源分发
            Distributor().distribute([item["depotFile"] for item in result if "depotFile" in item], "link_distrib")
            return result[0]["change"]

    def link_distrib_report(self, files, changelist_id, file_dic):
        resources_path = os.getcwd()
        remote_path = resources_path.replace("\\", "/").replace("gitlab/Script/scripts", "log")
        if not os.path.exists(remote_path):
            os.makedirs(remote_path)
        with open(remote_path + "/" + str(changelist_id) + ".html", "w+", encoding="utf-8") as obj:
            files_num = len(files)
            obj.write("""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>{}</title></head><body>""".format("分发报告"))
            obj.write(f"<p>资源数量：{files_num}</p>")
            if file_dic.__len__() != 0:
                for i, j in file_dic.items():
                    obj.write(f"<p>文件名：{i}  文件路径：{j}</p>")
            obj.write("</body></html>")

    def read_config_trunk_base_table(self, root) -> list:
        data_list = []
        resource_names = []
        config_path = root + r"\x5mplan\resmg\link\trunk-link-base.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            if isinstance(config_data[0], str):
                config_data[0] = config_data[0].strip()
            temp.update({"name": config_data[0]})
            # temp.update({"path": config_data[1]})
            data_list.append(temp)
        for resourcesName in data_list:
            # resource_names.append({[temp.replace('#', '[0-9]') for temp in resourcesName][0]: resourcesName[[temp for temp in resourcesName][0]]})
            resource_names.append(resourcesName["name"].replace("#", "[0-9]"))
        return resource_names

    def read_config_special_branch_table(self, root) -> list:
        data_list, special_list = [], []
        config_path = root + r"\x5mplan\resmg\link\special-branch-link-rule.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            temp.update({str(config_data[0]).strip(): str(config_data[1]).strip()})
            data_list.append(temp)
        for data in data_list:
            special_list.append({[temp.replace("#", "[0-9]") for temp in data][0]: data[[temp for temp in data][0]]})
        return special_list

    def read_config_hotfix_table(self, root) -> list:
        data_list, hotfix_list = [], []
        config_path = root + r"\x5mplan\resmg\link\hotfix-link-rule.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            temp.update({str(config_data[0]).strip(): str(config_data[1]).strip()})
            data_list.append(temp)
        for data in data_list:
            hotfix_list.append({[temp.replace("#", "[0-9]") for temp in data][0]: data[[temp for temp in data][0]]})
        return hotfix_list

    def get_max_root_b(self, p4_srv) -> str:
        file_map = p4_srv.dirs("//x5_mobile/mr/b/*")
        if file_map in [p4_error_code, p4_warning_code]:
            file_map = []
        all_branch = []
        for file in file_map:
            if re.match(r"(\d+)\.(\d+).(\d+)$", file["dir"].split("/")[-1]) != None:
                all_branch.append(file["dir"].split("/")[-1])
        all_branch.sort(key=lambda x: (int(x.split(".")[0]), int(x.split(".")[1]), int(x.split(".")[2])))
        max_version = all_branch[-1]
        return max_version

    def get_special_branch(self, p4_srv) -> str:
        """
        获取特殊分支
        """
        workspace = os.getenv("WORKSPACE")
        logging.info(f"workspace: {workspace}")
        max_branch = self.get_max_root_b(p4_srv)
        logging.info(f"max_branch: {max_branch}")
        special_data_branches = []
        data_list_special = self.read_config_special_branch_table(workspace)
        for special_data in data_list_special:
            for special_data_name in special_data:
                special_data_branch = special_data[special_data_name]
                if special_data_branch not in special_data_branches and special_data_branch.startswith(max_branch):
                    special_data_branches.append(special_data_branch)
        # if not special_data_branches:
        #     file_maps = p4_srv.dirs(f"//x5_mobile/mr/b/{max_branch}_*")
        #     if file_maps in [p4_error_code, p4_warning_code]:
        #         file_maps = []

        #     for file_map in file_maps:
        #         special_data_branch = file_map["dirs"].split("/")[-1]
        #         if special_data_branch not in special_data_branches:
        #             special_data_branches.append(special_data_branch)
        if special_data_branches:
            return special_data_branches[0]
        else:
            return ""

    @staticmethod
    def delete_chmod(ff, file):
        if os.path.exists(file):
            logging.info(f"delete_chmod vars: {ff}, {file}")
            try:
                os.chmod(file, stat.S_IWRITE)
                os.remove(file)
            except Exception as e:
                logging.warning(f"delete error: {e}")

        if os.path.exists(ff):
            shutil.copy(ff, file)

    def submit_related_file(self, root: str, platform: str, paths: str, desc: str):
        cdn_path = os.path.join(root, "x5mconfig", "cdn")
        if platform == "ios":
            if not os.path.exists(cdn_path):
                os.makedirs(cdn_path)
            os.chdir(cdn_path)
            self.__init_git_cdn(cdn_path)
            if not os.path.exists(os.path.join(cdn_path, "config/custom_accouterment")):
                os.makedirs(os.path.join(cdn_path, "config/custom_accouterment"))
        need_submit = False
        for path in paths.split(","):
            id = path.split("/")[-1]
            file_name = f"custom_{id}.xml"
            full_path = os.path.join(root, "x5_mobile/mobile_dancer/arttrunk/client", path, file_name)
            if not os.path.exists(full_path):
                logging.warn(f"not find related config file `{file_name}`")
                continue
            if platform == "ios":
                need_submit = True
                shutil.move(full_path, os.path.join(cdn_path, f"config/custom_accouterment/{file_name}"))
            else:
                os.remove(full_path)
        if need_submit:
            logging.info(os.path.join(cdn_path, "config/custom_accouterment"))
            os.chdir(os.path.join(cdn_path, "config/custom_accouterment"))
            logging.info("git add .")
            os.system("git add .")
            logging.info(f"git commit -m '{desc}'")
            os.system(f'git commit -m "{desc}"')
            logging.info("git push origin cdn")
            ret = os.system("git push origin cdn")
            if ret != 0:
                raise Exception(f"上传git失败, 错误码: {ret}")
            commit_id = os.popen("git rev-parse HEAD").read()
            logging.info(f"https://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig/-/commit/{commit_id}")

    def __init_git_cdn(self, cdn_path):
        if not os.path.exists(os.path.join(cdn_path, ".git/config")):
            os.system("git clone -b cdn http://oauth2:<EMAIL>/dgm/x5mconfig.git . -q")
        os.system("git clean -xdfq")
        os.system("git reset --hard HEAD -q")
        os.system("git pull origin cdn -q")


class LinkUnencryptResourceManager(LinkResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_link_unencrypt_p4_path
        self.kind = "link_unencrypt"

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                if (
                    file.find("bodypart/female/face/") != -1
                    or file.find("bodypart/female/plainBody/") != -1
                    or file.find("bodypart/male/face/") != -1
                    or file.find("bodypart/male/plainBody/") != -1
                    or file.find(self.prefix) == -1
                ):
                    continue
                for user in self.depot_user_dict.get(file):
                    if user not in self.depot_user_list:
                        self.depot_user_list.append(user)
                # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                #     self.depot_user_list.append(self.depot_user_dict[file])
                # if file.startswith(self.prefix) == -1:
                #     continue
                dir_name = os.path.dirname(file)
                id = re.findall(r"[0-9]{10}", dir_name)
                if len(id) == 0:
                    continue
                if id[0] not in self.id_list:
                    if len(p4tool.dirs(dir_name)) == 0:
                        continue
                    id_index = dir_name.find(id[0])
                    if id_index == -1:
                        continue
                    x5mobile_index = dir_name.find("x5_mobile")
                    if x5mobile_index != -1:
                        self.id_list.append(id[0])
                        id_path = dir_name[x5mobile_index:id_index] + id[0]
                        self.file_list.append(id_path)
        except Exception as e:
            logging.error(e)
            exit(-1)


class ActionResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_action_p4_path
        self.kind = "action"

    def get_updates(self, depot_files: list):
        try:
            for depot_file in depot_files:
                if not depot_file.startswith(self.prefix):
                    continue
                if depot_file.startswith(os.path.join(self.prefix, "ingame_dance_actions_anim").replace("\\", "/")) or depot_file.startswith(
                    os.path.join(self.prefix, "ingame_nodance_actions_anim").replace("\\", "/")
                ):
                    if depot_file.endswith(".act.txt"):
                        self.file_list.append(depot_file)
                elif not (
                    not depot_file.startswith(os.path.join(self.prefix, "actions_anim").replace("\\", "/"))
                    and not depot_file.startswith(os.path.join(self.prefix, "cloth_anim").replace("\\", "/"))
                    and not depot_file.startswith(os.path.join(self.prefix, "actions_anim_expression").replace("\\", "/"))
                ) or depot_file.startswith(os.path.join(self.prefix, "expression").replace("\\", "/")):
                    if depot_file.endswith(".anim"):
                        self.file_list.append(depot_file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/role/action") == -1:
                continue
            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            print(result)
            return result[0]["change"]


class ActionDistribResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_action_distrib_p4_path
        self.kind = "action_distrib"

    @staticmethod
    def __is_anim_expression(file_path: str) -> bool:
        """
        判断传入的P4文件路径是否属于动作表情
        """
        pattern = re.compile(r"//x5_mobile/mr/art_resource/art_src/role/actions/actions_anim_expression")
        if re.search(pattern, file_path):
            return True
        return False

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
            return json_result

        view_list = []
        p4_view_set = set()
        for file in self.file_list:
            real_path = file.replace("split", "actions").replace("//x5_mobile/mr/art_resource/art_src/", "assets/resources/art/")
            view_list.append(real_path)

            # 新增逻辑：由于动作表情资源间无直接关联性，且某些动作表情导入资源耗时较长，所以加入以下逻辑：
            # 先判断是否为动作表情，如果为动作表情，只拉取指定资源而非整个目录
            is_anim_expression = self.__is_anim_expression(file)
            logging.info(f"{file}是否为动作表情: {is_anim_expression}")
            if is_anim_expression:
                package_dir = file
                package_local_dir = real_path
                p4_view_set.add(f"{package_dir} //${{p4Client}}/x5_mobile/mobile_dancer/arttrunk/client/{package_local_dir}")
            else:
                package_dir = "/".join(file.split("/")[:9])
                package_local_dir = "/".join(real_path.split("/")[:6])
                p4_view_set.add(f"{package_dir}/... //${{p4Client}}/x5_mobile/mobile_dancer/arttrunk/client/{package_local_dir}/...")

        json_result["update"] = "true"
        json_result["package_list"] = ",".join(view_list)
        json_result["changelist"] = self.latest_changelist
        json_result["p4_client_path_b"] = self.get_max_root_b(p4tool)
        json_result["p4_client_path_s_b"] = self.get_special_branch(p4tool)
        json_result["p4_view"] = ";".join(p4_view_set)
        send_user_list = list()
        for user in self.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                send_user_list.append("{}@h3d.com.cn".format(user))
        json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def get_updates(self, depot_files: list):
        try:
            for depot_file in depot_files:
                if not depot_file.startswith(self.prefix):
                    continue
                for user in self.depot_user_dict.get(depot_file):
                    if user not in self.depot_user_list:
                        self.depot_user_list.append(user)
                # if self.depot_user_dict.get(depot_file) and \
                #         self.depot_user_dict[depot_file] not in self.depot_user_list:
                #     self.depot_user_list.append(self.depot_user_dict[depot_file])
                action_anim = os.path.join(self.prefix, "actions_anim").replace("\\", "/")
                cloth_anim = os.path.join(self.prefix, "cloth_anim").replace("\\", "/")
                action_anim_expression = os.path.join(self.prefix, "actions_anim_expression").replace("\\", "/")
                expression = os.path.join(self.prefix, "expression").replace("\\", "/")
                xhs_anim = os.path.join(self.prefix, "xhs_actions_anim").replace("\\", "/")
                xhs_anim_expression = os.path.join(self.prefix, "xhs_actions_anim_expression").replace("\\", "/")
                if (
                    depot_file.startswith(os.path.join(self.prefix, "ingame_dance_actions_anim").replace("\\", "/"))
                    or depot_file.startswith(os.path.join(self.prefix, "ingame_nodance_actions_anim").replace("\\", "/"))
                    or depot_file.startswith(os.path.join(self.prefix, "ingame_couple_dance_actions_anim").replace("\\", "/"))
                    or depot_file.startswith(os.path.join(self.prefix, "ingame_nodance_actions_anim").replace("\\", "/"))
                ):
                    if depot_file.endswith(".act.txt"):
                        self.file_list.append(depot_file)
                # elif not (not depot_file.startswith(os.path.join(self.prefix, 'actions_anim').replace('\\', '/')) and
                #           not depot_file.startswith(os.path.join(self.prefix, 'cloth_anim').replace('\\', '/')) and
                #           not depot_file.startswith(os.path.join(self.prefix, 'actions_anim_expression').replace('\\', '/'))) or depot_file.startswith(os.path.join(self.prefix, 'expression').replace('\\', '/')):
                # 当以下面某个目录开头时结果为True
                elif depot_file.startswith((action_anim, cloth_anim, action_anim_expression, expression, xhs_anim, xhs_anim_expression)):
                    if depot_file.endswith(".anim"):
                        self.file_list.append(depot_file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        file_dic = {}
        p4_srv = p4tool
        max_version_b = self.get_max_root_b(p4_srv)  # b目录最大资源路径
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        cdn_resources = ""
        hotfix_resources = "#"
        special_resources = "#"
        trunk_resources = "#"
        data_list_cdn = self.read_config_cdn_table(root)  # 读取的cdn配置文件信息
        data_list_hotfix = self.read_config_hotfix_table(root)  # 读取的hotfix配置文件信息
        data_list_special = self.read_config_special_branch_table(root)  # 读取的特殊分支配置文件信息
        data_list_cdn_base = self.read_config_cdn_base_table(root)  # 读取的cdn base配置文件信息
        for f in files:
            if f.find("assets/resources/art/role/action") == -1:
                continue
            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            ff, _ = os.path.splitext(f)
            if ff.endswith(".act"):
                ff, _ = os.path.splitext(ff)
            cdn_find = False
            hotfix_find = False
            special_find = False
            for data in data_list_cdn:
                if match(ff.split("/")[-1], data):
                    cdn_find = True
                    break

            for hotfix_data in data_list_hotfix:
                for hotfix_data_name in hotfix_data:
                    # logging.info(f"hotfix_data_name: {hotfix_data_name}")
                    if match(ff.split("/")[-1], hotfix_data_name):
                        hotfix_find = True
                        break

            for special_data in data_list_special:
                for special_data_name in special_data:
                    if match(ff.split("/")[-1], special_data_name):
                        special_find = True
                        break

            logging.info(f"------\nf: {f}\nff: {ff}\ncdn_find: {cdn_find}\nhotfix_find: {hotfix_find}\nspecial_find: {special_find}\n------")
            logging.info(f"------\nf: {f}\nff: {ff}\ncdn_find: {cdn_find}\nhotfix_find: {hotfix_find}\nspecial_find: {special_find}\n------")
            file_name = ff.split("/")[-1]
            if cdn_find:
                file_dic.update({file_name: ff})
                self.edit_file(p4_srv, ff)
                if cdn_resources == "":
                    cdn_resources += file_name
                    continue
                cdn_resources += f",{file_name}"
            elif ff.split("/")[-1] in data_list_cdn_base:
                file_dic.update({file_name: ff})
                self.edit_file(p4_srv, ff)
                if cdn_resources == "":
                    cdn_resources += file_name
                    continue
                cdn_resources += f",{file_name}"
            elif hotfix_find:
                for hotfix_data in data_list_hotfix:
                    for hotfix_data_name in hotfix_data:
                        if match(ff.split("/")[-1], hotfix_data_name):
                            big_edit = str(str(hotfix_data[hotfix_data_name])[0]) + "." + str(str(hotfix_data[hotfix_data_name])[1:3]) + ".0"
                            small_edit = str(hotfix_data[hotfix_data_name]) + "000"
                            p4_path = ff.replace("art_release/cs", f"onlineupdate/{big_edit}/{small_edit}/client")
                            local_path = "/".join(p4_path.split("/")[:-1])
                            resourcesName = p4_path.split("/")[-1]
                            if not os.path.exists(local_path):
                                os.makedirs(local_path)
                            real_path = local_path + "/" + str(resourcesName)
                            file_dic.update({resourcesName: real_path})
                            self.delete_chmod(ff, real_path)
                            self.edit_file(p4_srv, real_path)
                            if hotfix_resources == "#":
                                hotfix_resources += f"{small_edit}: {file_name}"
                                continue
                            hotfix_resources += f",{small_edit}: {file_name}"
            else:
                if special_find:
                    for special_data in data_list_special:
                        for special_data_name in special_data:
                            if match(ff.split("/")[-1], special_data_name):
                                special_data_branch = special_data[special_data_name]
                                p4_path_s_b = ff.replace("art_release/cs", f"b/{special_data_branch}/ResourcePublish/CDN/SourceFiles")
                                local_path_s_b = "/".join(p4_path_s_b.split("/")[:-1])
                                resources_name_s_b = p4_path_s_b.split("/")[-1]
                                if not os.path.exists(local_path_s_b):
                                    os.makedirs(local_path_s_b)
                                real_path_s_b = local_path_s_b + "/" + str(resources_name_s_b)
                                file_dic.update({resources_name_s_b: real_path_s_b})
                                self.delete_chmod(ff, real_path_s_b)
                                self.edit_file(p4_srv, real_path_s_b)
                                if special_resources == "#":
                                    special_resources += f"{special_data_branch}: {file_name}"
                                    continue
                                special_resources += f",{special_data_branch}: {file_name}"

                p4_path_b = ff.replace("art_release/cs", f"b/{max_version_b}/ResourcePublish/CDN/SourceFiles")
                p4_path_master = ff.replace("art_release/cs", f"Resources/ResourcePublish/CDN/SourceFiles")
                local_path_b = "/".join(p4_path_b.split("/")[:-1])
                local_path_master = "/".join(p4_path_master.split("/")[:-1])
                resourcesNameB = p4_path_b.split("/")[-1]
                resourcesNameMaster = p4_path_master.split("/")[-1]
                resourcesNameB_b = str(p4_path_b.split("/")[-1]) + "_b"
                if not os.path.exists(local_path_b):
                    os.makedirs(local_path_b)
                if not os.path.exists(local_path_master):
                    os.makedirs(local_path_master)
                real_path_b = local_path_b + "/" + str(resourcesNameB)
                real_path_master = local_path_master + "/" + str(resourcesNameMaster)
                file_dic.update({resourcesNameB_b: real_path_b})
                file_dic.update({resourcesNameMaster: real_path_master})
                self.delete_chmod(ff, real_path_b)
                self.delete_chmod(ff, real_path_master)
                self.edit_file(p4_srv, real_path_b)
                self.edit_file(p4_srv, real_path_master)
                if trunk_resources == "#":
                    trunk_resources += file_name
                    continue

                trunk_resources += f",{file_name}"
            # f, _ = os.path.splitext(f)
            # self.edit_file(p4_srv, f)

        self.distributed_resources = cdn_resources.strip() + trunk_resources.strip() + hotfix_resources.strip() + special_resources.strip()
        logging.info(f"cdn_resources: {cdn_resources}")
        logging.info(f"trunk_resources: {trunk_resources}")
        logging.info(f"hotfix_resources: {hotfix_resources}")
        logging.info(f"special_resources: {special_resources}")
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            self.action_distrib_report(files, result[0]["change"], file_dic)
            return result[0]["change"]

    def action_distrib_report(self, files, changelist_id, file_dic):
        resources_path = os.getcwd()
        remote_path = resources_path.replace("\\", "/").replace("gitlab/Script/scripts", "log")
        if not os.path.exists(remote_path):
            os.makedirs(remote_path)
        with open(remote_path + "/" + str(changelist_id) + ".html", "w+", encoding="utf-8") as obj:
            files_num = len(files)
            obj.write("""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>{}</title></head><body>""".format("分发报告"))
            obj.write(f"<p>资源数量：{files_num}</p>")
            if file_dic.__len__() != 0:
                for i, j in file_dic.items():
                    obj.write(f"<p>文件名：{i}  文件路径：{j}</p>")
            obj.write("</body></html>")

    def download_excel_from_p4(self):
        p4_cdn_excel = "//x5mplan/resmg/act/cdn_action-rule.xlsx"
        p4_cdn_excel_base = "//x5mplan/resmg/act/cdn-action-base.xlsx"
        p4_hotfix_excel = "//x5mplan/resmg/act/hotfix-action-base.xlsx"
        p4_special_branch_excel = "//x5mplan/resmg/act/special-branch-action-rule.xlsx"
        p4tool.sync(p4_cdn_excel)
        p4tool.sync(p4_cdn_excel_base)
        p4tool.sync(p4_hotfix_excel)
        p4tool.sync(p4_special_branch_excel)

    def read_config_cdn_table(self, root) -> list:
        data_list = []
        resource_names = []
        expression_list = []
        config_path = root + r"\x5mplan\resmg\act\cdn_action-rule.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            if isinstance(config_data[0], str):
                config_data[0] = config_data[0].strip()
            temp.update({"name": config_data[0]})
            # temp.update({"path": config_data[1]})
            data_list.append(temp)
        for resourcesName in data_list:
            resource_names.append(resourcesName["name"])
        for resource in resource_names:
            expression_list.append("??00000101_" + resource)
        resourcesList = resource_names + expression_list
        return [res.replace("#", "[0-9]") for res in resourcesList]

    def read_config_cdn_base_table(self, root) -> list:
        # self.download_excel_from_p4()
        data_list = []
        resource_names = []
        config_path = root + r"\x5mplan\resmg\act\cdn-action-base.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            if isinstance(config_data[0], str):
                config_data[0] = config_data[0].strip()
            temp.update({"name": config_data[0]})
            # temp.update({"path": config_data[1]})
            data_list.append(temp)
        for resourcesName in data_list:
            resource_names.append(resourcesName["name"])
        return resource_names

    def read_config_special_branch_table(self, root) -> list:
        data_list, special_list = [], []
        expression_list = []
        config_path = root + r"\x5mplan\resmg\act\special-branch-action-rule.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            temp.update({str(config_data[0]).strip(): str(config_data[1]).strip()})
            data_list.append(temp)
        for data in data_list:
            expression_list.append(
                {
                    ["??00000101_" + resource for resource in data][0]: data[
                        "_".join(["??00000101_" + resource for resource in data][0].split("_")[1:])
                    ]
                }
            )
        dataList = data_list + expression_list
        for data in dataList:
            special_list.append({[temp.replace("#", "[0-9]") for temp in data][0]: data[[temp for temp in data][0]]})
        return special_list

    def read_config_hotfix_table(self, root) -> list:
        # self.download_excel_from_p4()
        data_list, hotfix_list = [], []
        expression_list = []
        config_path = root + r"\x5mplan\resmg\act/hotfix-action-base.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            temp.update({str(config_data[0]).strip(): str(config_data[1]).strip()})
            data_list.append(temp)
        for data in data_list:
            expression_list.append(
                {
                    ["??00000101_" + resource for resource in data][0]: data[
                        "_".join(["??00000101_" + resource for resource in data][0].split("_")[1:])
                    ]
                }
            )
        dataList = data_list + expression_list
        for data in dataList:
            hotfix_list.append({[temp.replace("#", "[0-9]") for temp in data][0]: data[[temp for temp in data][0]]})
        return hotfix_list

    def get_max_root_b(self, p4_srv) -> str:
        file_map = p4_srv.dirs("//x5_mobile/mr/b/*")
        if file_map in [p4_error_code, p4_warning_code]:
            file_map = []
        all_branch = []
        for file in file_map:
            if re.match(r"(\d+)\.(\d+).(\d+)$", file["dir"].split("/")[-1]) != None:
                all_branch.append(file["dir"].split("/")[-1])
        all_branch.sort(key=lambda x: (int(x.split(".")[0]), int(x.split(".")[1]), int(x.split(".")[2])))
        # p4_client_path = '//x5_mobile/mr/b/{}/art_src/role/actions/...'.format(all_branch[-1])
        max_version = all_branch[-1]
        return max_version

    def get_special_branch(self, p4_srv) -> str:
        """
        获取特殊分支
        """
        workspace = os.getenv("WORKSPACE")
        logging.info(f"workspace: {workspace}")
        max_branch = self.get_max_root_b(p4_srv)
        logging.info(f"max_branch: {max_branch}")
        special_data_branches = []
        data_list_special = self.read_config_special_branch_table(workspace)
        for special_data in data_list_special:
            for special_data_name in special_data:
                special_data_branch = special_data[special_data_name]
                if special_data_branch not in special_data_branches and special_data_branch.startswith(max_branch):
                    special_data_branches.append(special_data_branch)
        # else:
        #     file_maps = p4_srv.dirs(f"//x5_mobile/mr/b/{max_branch}_*")
        #     if file_maps in [p4_error_code, p4_warning_code]:
        #         file_maps = []

        #     for file_map in file_maps:
        #         special_data_branch = file_map["dirs"].split("/")[-1]
        #         if special_data_branch not in special_data_branches:
        #             special_data_branches.append(special_data_branch)
        if special_data_branches:
            return special_data_branches[0]
        else:
            return ""

    def get_max_root_onlineupdate(self, p4_srv) -> str:
        file_map = p4_srv.dirs("//x5_mobile/mr/onlineupdate/*")
        if file_map in [p4_error_code, p4_warning_code]:
            file_map = []
        all_branch = []
        for file in file_map:
            if re.match(r"(\d+)\.(\d+).(\d+)$", file["dir"].split("/")[-1]) != None:
                all_branch.append(file["dir"].split("/")[-1])
        all_branch.sort(key=lambda x: (int(x.split(".")[0]), int(x.split(".")[1]), int(x.split(".")[2])))
        # p4_client_path = '//x5_mobile/mr/onlineupdate/{}/*/art_src/role/actions/...'.format(all_branch[-1])
        max_version = all_branch[-1]
        return max_version

    def delete_chmod(self, ff, file):
        if os.path.exists(file):
            try:
                os.remove(file)
            except:
                os.chmod(file, stat.S_IWRITE)
                os.remove(file)
        shutil.copy(ff, file)


class MakeUpDistribResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = kwargs["prefix"] if "prefix" in kwargs else makeup_distrib_p4_path
        self.kind = "makeup_distrib"
        self.makeup_filenames = list()

    def get_updates(self, depot_files: list):
        """
        获取妆容是否打包
        判断 整个具体资源目录是否有变更，如果有变更，自动获取对应的 目录名
        """
        exist_list = list()
        for file in depot_files:
            if not file.startswith(self.prefix):
                continue
            for user in self.depot_user_dict.get(file):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file])
            release_path = file.replace(self.prefix, "").lstrip("/")
            release_list = release_path.split("/")
            logging.info("release_list: {}".format(release_list))
            valid_path = "/".join(release_list[:3])
            if valid_path not in exist_list:
                exist_list.append(valid_path)
                p4_valid_path = "{}/{}".format(self.prefix, valid_path)
                self.file_list.append(p4_valid_path)
                self.makeup_filenames.append(release_list[2])

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
            return json_result

        view_list = []
        p4_view_set = set()
        for file in self.file_list:
            index = file.find("art_src")
            if index == -1:
                continue
            real_path = "assets/resources/art" + file[index + 7 :]
            view_list.append(real_path)

            p4_view_set.add(f"{file}/... //${{p4Client}}/x5_mobile/mobile_dancer/arttrunk/client/{real_path}/...")

        json_result["update"] = "true"
        json_result["package_list"] = ",".join(view_list)
        json_result["unity_package_list"] = ",".join(self.makeup_filenames)
        json_result["changelist"] = self.latest_changelist
        json_result["branch"] = self.get_max_root_b(p4tool)
        json_result["p4_view"] = ";".join(p4_view_set)
        send_user_list = list()
        for user in self.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                send_user_list.append("{}@h3d.com.cn".format(user))
        json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        file_dic = {}
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        cdn_resources = ""
        hotfix_resources = "#"
        trunk_resources = "#"
        max_branch = self.get_max_root_b(p4_srv)  # b目录最大资源路径
        data_list_cdn = self.read_cdn_table(root)  # 读取的cdn规则配置信息
        data_list_hotfix = self.read_hotfix_table(root)  # 读取的hotfix规则配置信息
        for f in files:
            if f.find("assets/resources/art/role/makeup") == -1:
                continue
            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            ff, _ = os.path.splitext(f)
            cdn_find = False
            hotfix_find = False
            for data in data_list_cdn:
                if match(ff.split("/")[-1], data):
                    cdn_find = True
                    break

            for hotfix_data in data_list_hotfix:
                for hotfix_data_name in hotfix_data:
                    if match(ff.split("/")[-1], hotfix_data_name):
                        hotfix_find = True
                        break

            file_name = ff.split("/")[-1]
            if cdn_find:
                file_dic.update({ff.split("/")[-1]: ff})
                self.edit_file(p4_srv, ff)
                if cdn_resources == "":
                    cdn_resources += file_name
                    continue
                cdn_resources += f",{file_name}"
            elif hotfix_find:
                for hotfix_data in data_list_hotfix:
                    for hotfix_data_name in hotfix_data:
                        if match(ff.split("/")[-1], hotfix_data_name):
                            branch = str(str(hotfix_data[hotfix_data_name])[0]) + "." + str(str(hotfix_data[hotfix_data_name])[1:3]) + ".0"
                            version = str(hotfix_data[hotfix_data_name]) + "000"
                            p4_path = ff.replace("art_release/cs", f"onlineupdate/{branch}/{version}/client")
                            local_path = "/".join(p4_path.split("/")[:-1])
                            resourcesName = p4_path.split("/")[-1]
                            if not os.path.exists(local_path):
                                os.makedirs(local_path)
                            real_path = local_path + "/" + str(resourcesName)
                            file_dic.update({resourcesName: real_path})
                            self.delete_chmod(ff, real_path)
                            self.edit_file(p4_srv, real_path)
                            if hotfix_resources == "#":
                                hotfix_resources += f"{version}: {file_name}"
                                continue
                            hotfix_resources += f",{version}: {file_name}"
            else:
                self.upload_p4_branch(ff, file_dic, p4_srv, f"b/{max_branch}")
                self.upload_p4_branch(ff, file_dic, p4_srv, "Resources")
                if trunk_resources == "#":
                    trunk_resources += file_name
                    continue

                trunk_resources += f",{file_name}"

        self.distributed_resources = cdn_resources.strip() + trunk_resources.strip() + hotfix_resources.strip()
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            self.makeup_distrib_report(files, result[0]["change"], file_dic)
            return result[0]["change"]

    def upload_p4_branch(self, ff, file_dic, p4_srv, branch_info):
        p4_path = ff.replace("art_release/cs", f"{branch_info}/ResourcePublish/CDN/SourceFiles")
        local_path = "/".join(p4_path.split("/")[:-1])
        resourcesName = p4_path.split("/")[-1]
        if not os.path.exists(local_path):
            os.makedirs(local_path)
        if branch_info != "Resources":
            file_dic.update({resourcesName + "_b": p4_path})
        else:
            file_dic.update({resourcesName: p4_path})
        self.delete_chmod(ff, p4_path)
        self.edit_file(p4_srv, p4_path)

    def makeup_distrib_report(self, files, changelist_id, file_dic):
        resources_path = os.getcwd()
        remote_path = resources_path.replace("\\", "/").replace("gitlab/Script/scripts", "log")
        if not os.path.exists(remote_path):
            os.makedirs(remote_path)
        with open(remote_path + "/" + str(changelist_id) + ".html", "w+", encoding="utf-8") as obj:
            files_num = len(files)
            obj.write("""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>{}</title></head><body>""".format("分发报告"))
            obj.write(f"<p>资源数量：{files_num}</p>")
            if file_dic.__len__() != 0:
                for i, j in file_dic.items():
                    obj.write(f"<p>文件名：{i}  文件路径：{j}</p>")
            obj.write("</body></html>")

    def read_cdn_table(self, root) -> list:
        data_list = []
        resource_names = []
        config_path = root + r"\x5mplan\resmg\makeup\cdn-makeup-rule.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            if isinstance(config_data[0], str):
                config_data[0] = config_data[0].strip()
            temp.update({"name": config_data[0]})
            data_list.append(temp)
        for resourcesName in data_list:
            resource_names.append(resourcesName["name"])

        return [res.replace("#", "[0-9]") for res in resource_names]

    def read_hotfix_table(self, root) -> list:
        data_list, hotfix_list = [], []
        config_path = root + r"\x5mplan\resmg\makeup\hotfix-makeup-rule.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            temp.update({str(config_data[0]).strip(): str(config_data[1]).strip()})
            data_list.append(temp)
        for data in data_list:
            hotfix_list.append({[temp.replace("#", "[0-9]") for temp in data][0]: data[[temp for temp in data][0]]})
        return hotfix_list

    def get_max_root_b(self, p4_srv) -> str:
        file_map = p4_srv.dirs("//x5_mobile/mr/b/*")
        all_branch = []
        for file in file_map:
            if re.match(r"(\d+)\.(\d+).(\d+)$", file["dir"].split("/")[-1]) is not None:
                all_branch.append(file["dir"].split("/")[-1])
        all_branch.sort(key=lambda x: (int(x.split(".")[0]), int(x.split(".")[1]), int(x.split(".")[2])))
        max_version = all_branch[-1]
        return max_version

    def delete_chmod(self, ff, file):
        if os.path.exists(file):
            try:
                os.remove(file)
            except:
                os.chmod(file, stat.S_IWRITE)
                os.remove(file)
        if os.path.exists(ff):
            shutil.copy(ff, file)


class CameraDistribResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_camera_distrib_p4_path
        self.kind = "camera_distrib"

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        p4_srv = p4tool
        max_version_b = self.get_max_root_b(p4_srv)  # b目录最大资源路径
        # max_version_hotfix = self.get_max_root_onlineupdate(p4_srv)  # onlineupdate目录最大资源路径
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            view_list = []
            views = ""
            for file in self.file_list:
                real_path = file.replace("split", "camera").replace("//x5_mobile/mr/art_resource/art_src/", "assets/resources/art/")
                view_list.append(real_path)
            views += ",".join(view_list)
            json_result["update"] = "true"
            json_result["package_list"] = views
            json_result["changelist"] = self.latest_changelist
            json_result["p4_client_path_b"] = max_version_b
            send_user_list = list()
            for user in self.depot_user_list:
                if not user.endswith("@h3d.com.cn"):
                    send_user_list.append("{}@h3d.com.cn".format(user))
            json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def get_updates(self, depot_files: list):
        try:
            for file in depot_files:
                for user in self.depot_user_dict.get(file):
                    if user not in self.depot_user_list:
                        self.depot_user_list.append(user)
                # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                #     self.depot_user_list.append(self.depot_user_dict[file])
                if not file.startswith(self.prefix):
                    continue
                if file.endswith(".FBX") or file.endswith(".fbx") or file.endswith(".prefab"):
                    self.file_list.append(file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    @staticmethod
    def read_path_map(path: str) -> typing.Dict[str, typing.List[str]]:
        """获取路径映射表"""
        # 此模块只有很少流水线用到了, 防止未安装此模块的流水线挂掉, 仅在此导入
        import pandas as pd

        df = pd.read_excel(path, sheet_name=0, usecols=[0, 1], skiprows=1, header=None)
        result = defaultdict(list)
        [result[row.iloc[0]].append(row.iloc[1]) for _, row in df.iterrows()]
        return dict(result)

    def _submit_map(self, files: typing.List[str], path: str, p4_srv=None):
        """处理镜头的特殊映射,

        如果p4_srv=None则不提交, 仅生成映射
        """
        path_map = self.read_path_map(path)  # 读取的cdn配置文件信息
        # 得到需要处理的文件
        files = filter(lambda ff: any(map(lambda x: match(os.path.splitext(os.path.basename(ff))[0], x), path_map)), files)
        # 构造文件映射表
        file_map = defaultdict(set)
        for ff in files:
            basename, _ = os.path.splitext(os.path.basename(ff))
            # 去掉后缀
            key, _ = os.path.splitext(ff)
            [file_map[key].update(path_map[pat]) for pat in path_map if match(basename, pat)]
        # 构造文件映射列表
        file_map_list: typing.List[typing.List[str]] = []
        for k, x_map in file_map.items():
            file_map_list.extend([(k, _map) for _map in x_map])

        file_dic = {}
        resources = []
        for ff, x_map in file_map_list:
            big_edit = x_map
            p4_path = ff.replace("art_release/cs", f"b/{big_edit}/ResourcePublish/CDN/SourceFiles")
            local_path, resourcesName = os.path.split(p4_path)
            os.makedirs(local_path, exist_ok=True)
            file_dic.update({resourcesName: p4_path})
            logging.info(f"ff: {ff}, p4_path: {p4_path}, p4_srv: {p4_srv}")
            if p4_srv:
                self.delete_chmod(p4_srv, ff, p4_path)
                self.edit_file(p4_srv, p4_path)
            resources.append(f"{big_edit}: {os.path.basename(ff)}")
        return file_dic, ",".join(resources)

    @staticmethod
    def _format_files(files: typing.List[str], root: str, platform: str = "ios") -> typing.List[str]:
        """格式化路径, 并统一转换为linux path"""
        root = os.path.normpath(root)
        files_formatted = set()
        for f in files:
            f = os.path.join(root, os.path.normpath(f))
            f = f.replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            if "art/camera/prefab" not in f:
                f = f.replace("art/camera", "art/camera/prefab")
            files_formatted.add(f)
        return list(files_formatted)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        file_dic = {}
        p4_srv = p4tool
        max_version_b = self.get_max_root_b(p4_srv)  # b目录最大资源路径
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        cdn_resources = ""
        hotfix_resources = "#"
        trunk_resources = "#"
        data_list_cdn = self.read_config_cdn_table(root)  # 读取的cdn配置文件信息
        data_list_hotfix = self.read_config_hotfix_table(root)  # 读取的hotfix配置文件信息
        data_list_cdn_base = self.read_config_cdn_base_table(root)  # 读取的cdn base配置文件信息
        # 格式化文件
        ori_files = files[:]
        files = filter(lambda f: f.find("assets/resources/art/camera") != -1, files)
        files = self._format_files(files, root, platform)
        for f in files:
            ff, _ = os.path.splitext(f)
            cdn_find = False
            hotfix_find = False
            for data in data_list_cdn:
                if match(ff.split("/")[-1], data):
                    cdn_find = True
                    break

            for hotfix_data in data_list_hotfix:
                for hotfix_data_name in hotfix_data:
                    if match(ff.split("/")[-1], hotfix_data_name):
                        hotfix_find = True
                        break
            file_name = ff.split("/")[-1]
            if cdn_find:
                file_dic.update({ff.split("/")[-1]: ff})
                self.edit_file(p4_srv, ff)
                if cdn_resources == "":
                    cdn_resources += file_name
                    continue
                cdn_resources += f",{file_name}"
            elif ff.split("/")[-1] in data_list_cdn_base:
                file_dic.update({ff.split("/")[-1]: ff})
                self.edit_file(p4_srv, ff)
                if cdn_resources == "":
                    cdn_resources += file_name
                    continue
                cdn_resources += f",{file_name}"
            elif hotfix_find:
                for hotfix_data in data_list_hotfix:
                    for hotfix_data_name in hotfix_data:
                        if match(ff.split("/")[-1], hotfix_data_name):
                            big_edit = str(str(hotfix_data[hotfix_data_name])[0]) + "." + str(str(hotfix_data[hotfix_data_name])[1:3]) + ".0"
                            small_edit = str(hotfix_data[hotfix_data_name]) + "000"
                            p4_path = ff.replace("art_release/cs", f"onlineupdate/{big_edit}/{small_edit}/client")
                            local_path = "/".join(p4_path.split("/")[:-1])
                            resourcesName = p4_path.split("/")[-1]
                            if not os.path.exists(local_path):
                                os.makedirs(local_path)
                            real_path = local_path + "/" + str(resourcesName)
                            file_dic.update({resourcesName: real_path})
                            self.delete_chmod(p4_srv, ff, real_path)
                            self.edit_file(p4_srv, real_path)
                            if hotfix_resources == "#":
                                hotfix_resources += f"{small_edit}: {file_name}"
                                continue
                            hotfix_resources += f",{small_edit}: {file_name}"
            else:
                p4_path_b = ff.replace("art_release/cs", f"b/{max_version_b}/ResourcePublish/CDN/SourceFiles")
                p4_path_master = ff.replace("art_release/cs", f"Resources/ResourcePublish/CDN/SourceFiles")
                local_path_b = "/".join(p4_path_b.split("/")[:-1])
                local_path_master = "/".join(p4_path_master.split("/")[:-1])
                resourcesNameB = p4_path_b.split("/")[-1]
                resourcesNameMaster = p4_path_master.split("/")[-1]
                resourcesNameB_b = str(p4_path_b.split("/")[-1]) + "_b"
                if not os.path.exists(local_path_b):
                    os.makedirs(local_path_b)
                if not os.path.exists(local_path_master):
                    os.makedirs(local_path_master)
                real_path_b = local_path_b + "/" + str(resourcesNameB)
                real_path_master = local_path_master + "/" + str(resourcesNameMaster)
                file_dic.update({resourcesNameB_b: real_path_b})
                file_dic.update({resourcesNameMaster: real_path_master})
                logging.info(f"ff: {ff}, real_path_b: {real_path_b}, real_path_master: {real_path_master}")
                self.delete_chmod(p4_srv, ff, real_path_b)
                self.delete_chmod(p4_srv, ff, real_path_master)
                self.edit_file(p4_srv, real_path_b)
                self.edit_file(p4_srv, real_path_master)
                if trunk_resources == "#":
                    trunk_resources += file_name
                    continue

                trunk_resources += f",{file_name}"
            # f, _ = os.path.splitext(f)
            # self.edit_file(p4_srv, f)
        # 附加映射
        _file_dic, _resources = self._submit_map(
            files=files, path=os.path.join(root, "x5mplan", "resmg", "camera", "special-camera-map.xlsx"), p4_srv=p4_srv
        )
        file_dic.update(_file_dic)
        self.distributed_resources = cdn_resources.strip() + trunk_resources.strip() + hotfix_resources.strip() + f"#{_resources}"
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            self.camera_distrib_report(ori_files, result[0]["change"], file_dic)
            return result[0]["change"]

    def camera_distrib_report(self, files, changelist_id, file_dic):
        resources_path = os.getcwd()
        remote_path = resources_path.replace("\\", "/").replace("gitlab/Script/scripts", "log")
        if not os.path.exists(remote_path):
            os.makedirs(remote_path)
        with open(remote_path + "/" + str(changelist_id) + ".html", "w+", encoding="utf-8") as obj:
            files_num = len(files)
            obj.write("""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>{}</title></head><body>""".format("分发报告"))
            obj.write(f"<p>资源数量：{files_num}</p>")
            if file_dic.__len__() != 0:
                for i, j in file_dic.items():
                    obj.write(f"<p>文件名：{i}  文件路径：{j}</p>")
            obj.write("</body></html>")

    def read_config_cdn_table(self, root) -> list:
        data_list = []
        resource_names = []
        config_path = root + r"\x5mplan\resmg\camera\cdn-camera-rule.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            if isinstance(config_data[0], str):
                config_data[0] = config_data[0].strip()
            temp.update({"name": config_data[0]})
            # temp.update({"path": config_data[1]})
            data_list.append(temp)
        for resourcesName in data_list:
            resource_names.append(resourcesName["name"])
        return [res.replace("#", "[0-9]") for res in resource_names]

    def read_config_cdn_base_table(self, root) -> list:
        data_list = []
        resource_names = []
        config_path = root + r"\x5mplan\resmg\camera\cdn-camera-base.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            if isinstance(config_data[0], str):
                config_data[0] = config_data[0].strip()
            temp.update({"name": config_data[0]})
            # temp.update({"path": config_data[1]})
            data_list.append(temp)
        for resourcesName in data_list:
            resource_names.append(resourcesName["name"])
        return resource_names

    def read_config_hotfix_table(self, root) -> list:
        data_list, hotfix_list = [], []
        config_path = os.path.join(root, "x5mplan", "resmg", "camera", "hotfix-camera-rule.xlsx")
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            temp.update({str(config_data[0]).strip(): str(config_data[1]).strip()})
            data_list.append(temp)
        for data in data_list:
            hotfix_list.append({[temp.replace("#", "[0-9]") for temp in data][0]: data[[temp for temp in data][0]]})
        return hotfix_list

    def get_max_root_b(self, p4_srv) -> str:
        file_map = p4_srv.dirs("//x5_mobile/mr/b/*")
        if file_map in [p4_error_code, p4_warning_code]:
            file_map = []
        all_branch = []
        for file in file_map:
            if re.match(r"(\d+)\.(\d+).(\d+)$", file["dir"].split("/")[-1]) != None:
                all_branch.append(file["dir"].split("/")[-1])
        all_branch.sort(key=lambda x: (int(x.split(".")[0]), int(x.split(".")[1]), int(x.split(".")[2])))
        # p4_client_path = '//x5_mobile/mr/b/{}/art_src/camera/...'.format(all_branch[-1])
        max_version = all_branch[-1]
        return max_version

    def get_max_root_onlineupdate(self, p4_srv) -> str:
        file_map = p4_srv.dirs("//x5_mobile/mr/onlineupdate/*")
        if file_map in [p4_error_code, p4_warning_code]:
            file_map = []
        all_branch = []
        for file in file_map:
            if re.match(r"(\d+)\.(\d+).(\d+)$", file["dir"].split("/")[-1]) != None:
                all_branch.append(file["dir"].split("/")[-1])
        all_branch.sort(key=lambda x: (int(x.split(".")[0]), int(x.split(".")[1]), int(x.split(".")[2])))
        # p4_client_path = '//x5_mobile/mr/onlineupdate/{}/*/art_src/camera/...'.format(all_branch[-1])
        max_version = all_branch[-1]
        return max_version

    @staticmethod
    def delete_chmod(p4_srv, ff, file):
        if os.path.exists(file):
            logging.warning(f"delete_chmod vars: {ff}, {file}")
            try:
                os.chmod(file, stat.S_IWRITE)
                os.remove(file)
            except Exception as e:
                logging.warning(f"delete error: {e}")
                # p4_srv.revert(file)
        if os.path.exists(ff):
            shutil.copy(ff, file)


class MusicActionDistribResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_action_distrib_p4_path
        self.kind = "musicact_distrib"

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
            return json_result

        view_list = []
        p4_view_set = set()
        for file in self.file_list:
            real_path = file.replace("split", "actions").replace("//x5_mobile/mr/art_resource/art_src/", "assets/resources/art/")
            view_list.append(real_path)

            package_dir = "/".join(file.split("/")[:9])
            package_local_dir = "/".join(real_path.split("/")[:6])
            p4_view_set.add(f"{package_dir}/... //${{p4Client}}/x5_mobile/mobile_dancer/arttrunk/client/{package_local_dir}/...")

        json_result["update"] = "true"
        json_result["package_list"] = ",".join(view_list)
        json_result["changelist"] = self.latest_changelist
        json_result["p4_client_path_b"] = self.get_max_root_b(p4tool)  # b目录最大资源路径
        json_result["p4_view"] = ";".join(p4_view_set)
        send_user_list = list()
        for user in self.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                send_user_list.append("{}@h3d.com.cn".format(user))
        json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def get_updates(self, depot_files: list):
        avatar_weapon_files = []
        try:
            for file in depot_files:
                if not file.startswith(self.prefix):
                    continue
                for user in self.depot_user_dict.get(file):
                    if user not in self.depot_user_list:
                        self.depot_user_list.append(user)
                # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                #     self.depot_user_list.append(self.depot_user_dict[file])
                if (
                    file.startswith(os.path.join(self.prefix, "avatar_weapon_actions_anim").replace("\\", "/"))
                    or file.startswith(os.path.join(self.prefix, "avatar_weapon_actions_anim_expression").replace("\\", "/"))
                    or file.startswith(os.path.join(self.prefix, "weapon_actions_anim").replace("\\", "/"))
                ):
                    avatar_weapon_file = file.split(".")[0]
                    if avatar_weapon_file not in avatar_weapon_files:
                        avatar_weapon_files.append(avatar_weapon_file)
                        self.file_list.append(file)
        except Exception as e:
            logging.error(e)
            exit(-1)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        file_dic = {}
        p4_srv = p4tool
        max_version_b = self.get_max_root_b(p4_srv)  # b目录最大资源路径
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        cdn_resources = ""
        hotfix_resources = "#"
        trunk_resources = "#"
        data_list_cdn = self.read_config_cdn_table(root)  # 读取的cdn配置文件信息
        data_list_hotfix = self.read_config_hotfix_table(root)  # 读取的hotfix配置文件信息
        data_list_cdn_base = self.read_config_cdn_base_table(root)  # 读取的cdn base配置文件信息
        for f in files:
            if f.find("assets/resources/art/role/action") == -1:
                continue
            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            ff, _ = os.path.splitext(f)
            cdn_find = False
            hotfix_find = False
            for data in data_list_cdn:
                if match(ff.split("/")[-1], data):
                    cdn_find = True
                    break

            for hotfix_data in data_list_hotfix:
                for hotfix_data_name in hotfix_data:
                    if match(ff.split("/")[-1], hotfix_data_name):
                        hotfix_find = True
                        break
            file_name = ff.split("/")[-1]
            if cdn_find:
                file_dic.update({ff.split("/")[-1]: ff})
                self.edit_file(p4_srv, ff)
                if cdn_resources == "":
                    cdn_resources += file_name
                    continue
                cdn_resources += f",{file_name}"
            elif ff.split("/")[-1] in data_list_cdn_base:
                file_dic.update({ff.split("/")[-1]: ff})
                self.edit_file(p4_srv, ff)
                if cdn_resources == "":
                    cdn_resources += file_name
                    continue
                cdn_resources += f",{file_name}"
            elif hotfix_find:
                for hotfix_data in data_list_hotfix:
                    for hotfix_data_name in hotfix_data:
                        if match(ff.split("/")[-1], hotfix_data_name):
                            big_edit = str(str(hotfix_data[hotfix_data_name])[0]) + "." + str(str(hotfix_data[hotfix_data_name])[1:3]) + ".0"
                            small_edit = str(hotfix_data[hotfix_data_name]) + "000"
                            p4_path = ff.replace("art_release/cs", f"onlineupdate/{big_edit}/{small_edit}/client")
                            local_path = "/".join(p4_path.split("/")[:-1])
                            resourcesName = p4_path.split("/")[-1]
                            if not os.path.exists(local_path):
                                os.makedirs(local_path)
                            real_path = local_path + "/" + str(resourcesName)
                            file_dic.update({resourcesName: real_path})
                            self.delete_chmod(ff, real_path)
                            self.edit_file(p4_srv, real_path)
                            if hotfix_resources == "#":
                                hotfix_resources += f"{small_edit}: {file_name}"
                                continue
                            hotfix_resources += f",{small_edit}: {file_name}"
            else:
                p4_path_b = ff.replace("art_release/cs", f"b/{max_version_b}/ResourcePublish/CDN/SourceFiles")
                p4_path_master = ff.replace("art_release/cs", f"Resources/ResourcePublish/CDN/SourceFiles")
                local_path_b = "/".join(p4_path_b.split("/")[:-1])
                local_path_master = "/".join(p4_path_master.split("/")[:-1])
                resourcesNameB = p4_path_b.split("/")[-1]
                resourcesNameMaster = p4_path_master.split("/")[-1]
                resourcesNameB_b = str(p4_path_b.split("/")[-1]) + "_b"
                if not os.path.exists(local_path_b):
                    os.makedirs(local_path_b)
                if not os.path.exists(local_path_master):
                    os.makedirs(local_path_master)
                real_path_b = local_path_b + "/" + str(resourcesNameB)
                real_path_master = local_path_master + "/" + str(resourcesNameMaster)
                file_dic.update({resourcesNameB_b: real_path_b})
                file_dic.update({resourcesNameMaster: real_path_master})
                self.delete_chmod(ff, real_path_b)
                self.delete_chmod(ff, real_path_master)
                self.edit_file(p4_srv, real_path_b)
                self.edit_file(p4_srv, real_path_master)
                if trunk_resources == "#":
                    trunk_resources += file_name
                    continue

                trunk_resources += f",{file_name}"
            # f, _ = os.path.splitext(f)
            # self.edit_file(p4_srv, f)

        self.distributed_resources = cdn_resources.strip() + trunk_resources.strip() + hotfix_resources.strip()
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            self.action_distrib_report(files, result[0]["change"], file_dic)
            return result[0]["change"]

    def action_distrib_report(self, files, changelist_id, file_dic):
        resources_path = os.getcwd()
        remote_path = resources_path.replace("\\", "/").replace("gitlab/Script/scripts", "log")
        if not os.path.exists(remote_path):
            os.makedirs(remote_path)
        with open(remote_path + "/" + str(changelist_id) + ".html", "w+", encoding="utf-8") as obj:
            files_num = len(files)
            obj.write("""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>{}</title></head><body>""".format("分发报告"))
            obj.write(f"<p>资源数量：{files_num}</p>")
            if file_dic.__len__() != 0:
                for i, j in file_dic.items():
                    obj.write(f"<p>文件名：{i}  文件路径：{j}</p>")
            obj.write("</body></html>")

    def read_config_cdn_table(self, root) -> list:
        data_list = []
        resource_names = []
        config_path = root + r"\x5mplan\resmg\act\cdn_action-rule.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            if isinstance(config_data[0], str):
                config_data[0] = config_data[0].strip()
            temp.update({"name": config_data[0]})
            # temp.update({"path": config_data[1]})
            data_list.append(temp)
        for resourcesName in data_list:
            resource_names.append(resourcesName["name"])
        return [res.replace("#", "[0-9]") for res in resource_names]

    def read_config_cdn_base_table(self, root) -> list:
        data_list = []
        resource_names = []
        expression_list = []
        config_path = root + r"\x5mplan\resmg\act\cdn-action-base.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            if isinstance(config_data[0], str):
                config_data[0] = config_data[0].strip()
            temp.update({"name": config_data[0]})
            # temp.update({"path": config_data[1]})
            data_list.append(temp)
        for resourcesName in data_list:
            resource_names.append(resourcesName["name"])
        for resource in resource_names:
            expression_list.append("??00000101_" + resource)
        resourcesList = resource_names + expression_list
        return resourcesList

    def read_config_hotfix_table(self, root) -> list:
        data_list, expression_list, hotfix_list = [], [], []
        config_path = root + r"\x5mplan\resmg\act\hotfix-action-base.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            temp.update({str(config_data[0]).strip(): str(config_data[1]).strip()})
            data_list.append(temp)
        for data in data_list:
            expression_list.append(
                {
                    ["??00000101_" + resource for resource in data][0]: data[
                        "_".join(["??00000101_" + resource for resource in data][0].split("_")[1:])
                    ]
                }
            )
        dataList = data_list + expression_list
        for data in dataList:
            hotfix_list.append({[temp.replace("#", "[0-9]") for temp in data][0]: data[[temp for temp in data][0]]})
        return hotfix_list

    def get_max_root_b(self, p4_srv) -> str:
        file_map = p4_srv.dirs("//x5_mobile/mr/b/*")
        if file_map in [p4_error_code, p4_warning_code]:
            file_map = []
        all_branch = []
        for file in file_map:
            if re.match(r"(\d+)\.(\d+).(\d+)$", file["dir"].split("/")[-1]) is not None:
                all_branch.append(file["dir"].split("/")[-1])
        all_branch.sort(key=lambda x: (int(x.split(".")[0]), int(x.split(".")[1]), int(x.split(".")[2])))
        # p4_client_path = '//x5_mobile/mr/b/{}/art_src/camera/...'.format(all_branch[-1])
        max_version = all_branch[-1]
        return max_version

    def get_max_root_onlineupdate(self, p4_srv) -> str:
        file_map = p4_srv.dirs("//x5_mobile/mr/onlineupdate/*")
        if file_map in [p4_error_code, p4_warning_code]:
            file_map = []
        all_branch = []
        for file in file_map:
            if re.match(r"(\d+)\.(\d+).(\d+)$", file["dir"].split("/")[-1]) is not None:
                all_branch.append(file["dir"].split("/")[-1])
        all_branch.sort(key=lambda x: (int(x.split(".")[0]), int(x.split(".")[1]), int(x.split(".")[2])))
        # p4_client_path = '//x5_mobile/mr/onlineupdate/{}/*/art_src/camera/...'.format(all_branch[-1])
        max_version = all_branch[-1]
        return max_version

    def delete_chmod(self, ff, file):
        if os.path.exists(file):
            try:
                os.remove(file)
            except:
                os.chmod(file, stat.S_IWRITE)
                os.remove(file)

        try:
            shutil.copy(ff, file)
        except Exception as e:
            logging.warning("ff: {}, file: {}, error: {}".format(ff, file, e))
            exit(-1)


class TextureResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = kwargs["prefix"] if "prefix" in kwargs else cdn_texture_path
        self.c_prefix = kwargs["c_prefix"] if "c_prefix" in kwargs else cdn_c_path
        self.kind = kwargs["kind"] if "kind" in kwargs else "texture"

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        view_list = []
        texture_path_list = []
        views = ""
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            for file in self.file_list:
                texture_path_list.append(file[2:])
                view_list.append("{0} //{1}{2}".format(file, "${p4Client}", file[1:]))

            views += ";".join(view_list)
            json_result["update"] = "true"
            json_result["views"] = views
            json_result["texturepaths"] = ",".join(texture_path_list)
            json_result["changelist"] = self.latest_changelist
            send_user_list = list()
            for user in self.depot_user_list:
                logging.info(f"user: {user}")
                if not user.endswith("@h3d.com.cn"):
                    send_user_list.append("{}@h3d.com.cn".format(user))
                else:
                    send_user_list.append(user)
            json_result["send_user"] = ",".join(send_user_list)
            self.write_resources(",".join(texture_path_list))

        # logging.info("json_result: {}".format(json_result))
        return json_result

    def get_updates(self, depot_files: list):
        c_depot_files = self.get_update_files(self.c_prefix)
        logging.info("c_depot_files: {}".format(c_depot_files))
        depot_files.extend(c_depot_files)
        for file in depot_files:
            if not file.endswith(".png"):
                continue

            if not file.startswith(self.prefix) and not file.startswith(self.c_prefix):
                continue

            # 例如: //x5_mobile/mr/Resources/art_src/c/2205ipjzds/texture/transparent/pixel_1024/HYZH_bg11.png
            # 正则表达式匹配结果: c/2205ipjzds/texture
            # 意义: [^/]+表示匹配除了`/`以外的其他字符, 且该字符至少出现一次
            if file.startswith(self.c_prefix) and len(re.findall("c/[^/]+/texture", file)) == 0:
                continue

            if self.depot_user_dict.get(file):
                depot_user = self.depot_user_dict[file]
                if depot_user[0] == "dgm_jenkins" and self.depot_desc_dict.get(file):
                    desc_ret = re.search(r"uploader:(.+?)\nIdentity:#Jenkins#", self.depot_desc_dict[file])
                    if desc_ret:
                        desc_user_list = desc_ret.group(1).strip().split(",")
                        for u in desc_user_list:
                            if u and u not in self.depot_user_list:
                                self.depot_user_list.append(u)
                else:
                    for depot_user in self.depot_user_dict.get(file):
                        if depot_user not in self.depot_user_list:
                            self.depot_user_list.append(depot_user)
            self.file_list.append(file)

    def get_asset_bundle_path(self, path, platform):
        return path.replace("art_src", "cs/{0}/assetbundles".format(platform))

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        # if platform == "android":
        #     try:
        #         self.submit_csv(root, desc, "jenkins-android-texture-package")
        #     except Exception:
        #         pass
        return self.submit_asset_bundle(workspace, files, root, platform, desc)

    def submit_bak(self, platform, workspace, files: list, desc, root) -> str:
        # if platform == 'android':
        #     try:
        #         self.submit_csv(root, desc, 'jenkins-android-texture-bak-package')
        #     except Exception:
        #         pass
        return self.submit_asset_bundle(workspace, files, root, platform, desc)

    def submit_asset_bundle(self, workspace, files, root, platform, desc):
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            f = os.path.join(root, f).replace("\\", "/")
            f = self.get_asset_bundle_path(f, platform)
            f, _ = os.path.splitext(f)
            f += "_mat"
            # TODO 临时处理
            # logging.info(f"file: {f}")
            self.edit_file(p4_srv, f)

        result = p4_srv.submit("-d", desc)
        logging.info(f"P4 提交结果: {result}")
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            # 公共资源分发
            Distributor().distribute([item["depotFile"] for item in result if "depotFile" in item], "link_distrib")
            return result[0]["change"]


class TruncTextureResourceManager(TextureResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = trunc_texture_path
        self.c_prefix = trunc_c_path

    def get_asset_bundle_path(self, path, platform):
        return path.replace("art_src", "ResourcePublish/CDN/SourceFiles/{0}/assetbundles".format(platform))

    def get_p4_tool(self, workspace) -> P4Tool:
        return P4Tool(p4_user, p4_host, p4_passwd, workspace, "")

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        return self.submit_asset_bundle(workspace, files, root, platform, desc)


class BranchTextureResourceManager(TruncTextureResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if len(kwargs) <= 1:
            return
        self.prefix = kwargs["branch_texture"]
        self.c_prefix = kwargs["branch_c"]

    def get_descs_and_changelist(self, p4_path) -> list:
        # 当前打包的changelist需要大于等于minimum_changelist否则将会修改config中的值, 然后再检查更新
        minimum_changelist = self.check_version(p4_path)
        config_changelist = int(config["P4"][self.kind])
        logging.info(f"minimum_changelist: {minimum_changelist}, config_changelist: {config_changelist}")
        if config_changelist < minimum_changelist:
            config.set("P4", self.kind, str(minimum_changelist))

        return super(BranchTextureResourceManager, self).get_descs_and_changelist(p4_path)


class OnlineUpdateTextureResourceManager(TruncTextureResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if len(kwargs) <= 1:
            return
        self.prefix = kwargs["online_update_texture"]
        self.c_prefix = kwargs["online_update_c"]
        self.namespace = kwargs["namespace"]

    def get_asset_bundle_path(self, path, platform):
        return path.replace("art_src", "client/{0}/assetbundles".format(platform))

    def get_p4_tool(self, workspace) -> P4Tool:
        return P4Tool(p4_user, p4_host, p4_passwd, workspace, "")


class HouseResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_house_p4_path
        self.kind = "house"

    def get_updates(self, depot_files: list):
        for file in depot_files:
            if not file.startswith(self.prefix):
                continue
            for user in self.depot_user_dict.get(file):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file])
            if (file.find("jiaju") != -1 and file.find("house_") != -1) or file.find("qiangzhi") != -1:
                if not file.endswith(".prefab"):
                    continue
                folder, _ = os.path.split(file)
                if folder not in self.file_list:
                    self.file_list.append(folder)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/staticresources/art/3d") == -1:
                continue
            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/staticresources/art/3d", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]


class HouseDistribResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_house_distrib_p4_path
        self.kind = "house_distrib"

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
            return json_result

        view_list = []
        p4_view_set = set()
        for file in self.file_list:
            index = file.find("art_src")
            if index == -1:
                continue
            house_index = file.find("art_src/house")
            if house_index == -1:
                pre_path = "assets/resources/art"
            else:
                pre_path = "assets/staticresources/art/3d"

            real_path = pre_path + file[index + 7 :]
            view_list.append(real_path)

            house_fangjian_index = file.find("house/fangjian")
            package_type = file[house_fangjian_index + 15 :].split("/")[0]
            p4_view_set.add(
                f"{file[:house_fangjian_index + 15]}{package_type}/... //${{p4Client}}/x5_mobile/mobile_dancer/arttrunk/client/{pre_path}/house/fangjian/{package_type}/..."
            )

        json_result["update"] = "true"
        json_result["package_list"] = ",".join(view_list)
        json_result["changelist"] = self.latest_changelist
        json_result["max_version_b"] = self.get_max_root_b(p4tool)
        json_result["p4_view"] = ";".join(p4_view_set)
        send_user_list = list()
        for user in self.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                send_user_list.append("{}@h3d.com.cn".format(user))
        json_result["send_user"] = ",".join(send_user_list)
        self.write_resources(",".join(view_list))
        return json_result

    def get_updates(self, depot_files: list):
        for file in depot_files:
            if not file.startswith(self.prefix):
                logging.debug(f"{file} is not start with {self.prefix}, continue")
                continue
            for user in self.depot_user_dict.get(file):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file])
            if (file.find("jiaju") != -1 and file.find("house_") != -1) or file.find("qiangzhi") != -1:
                if not file.endswith(".prefab"):
                    logging.debug(f"{file} is not end with .prefab, continue")
                    continue
                folder, _ = os.path.split(file)
                if folder not in self.file_list:
                    self.file_list.append(folder)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        file_dic = {}
        p4_srv = p4tool
        max_version_b = self.get_max_root_b(p4_srv)  # b目录最大资源路径
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        cdn_resources = ""
        hotfix_resources = "#"
        trunk_resources = "#"
        data_list_trunk = self.read_config_trunk_base_table(root)  # 读取的trunk配置文件信息
        data_list_hotfix = self.read_config_hotfix_table(root)  # 读取的hotfix配置文件信息
        logging.info("files: {}".format(files))
        for f in files:
            if f.find("assets/staticresources/art/3d") == -1:
                continue
            f = os.path.join(root, f).replace("\\", "/")
            ff = f.replace("assets/staticresources/art/3d", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            # ff, _ = os.path.splitext(f)
            trunk_find = False
            hotfix_find = False
            for data in data_list_trunk:
                if match(ff.split("/")[-1], data):
                    trunk_find = True
                    break
                elif ff.split("/")[-2] == "qiangzhi" and "/".join(ff.split("/")[-3:]) == data:
                    trunk_find = True
                    break
            for hotfix_data in data_list_hotfix:
                for hotfix_data_name in hotfix_data:
                    if match(ff.split("/")[-1], hotfix_data_name):
                        hotfix_find = True
                        break
            file_name = ff.split("/")[-1]
            if trunk_find:
                logging.info("type is trunk")
                p4_path_b = ff.replace("art_release/cs", f"b/{max_version_b}/ResourcePublish/CDN/SourceFiles")
                p4_path_master = ff.replace("art_release/cs", f"Resources/ResourcePublish/CDN/SourceFiles")
                local_path_b = "/".join(p4_path_b.split("/")[:-1])
                local_path_master = "/".join(p4_path_master.split("/")[:-1])
                resourcesNameB = p4_path_b.split("/")[-1]
                resourcesNameMaster = p4_path_master.split("/")[-1]
                resourcesNameB_b = str(p4_path_b.split("/")[-1]) + "_b"
                if not os.path.exists(local_path_b):
                    os.makedirs(local_path_b)
                if not os.path.exists(local_path_master):
                    os.makedirs(local_path_master)
                real_path_b = local_path_b + "/" + str(resourcesNameB)
                real_path_master = local_path_master + "/" + str(resourcesNameMaster)
                file_dic.update({resourcesNameB_b: real_path_b})
                file_dic.update({resourcesNameMaster: real_path_master})
                self.delete_chmod(ff, real_path_b)
                self.delete_chmod(ff, real_path_master)
                self.edit_file(p4_srv, real_path_b)
                self.edit_file(p4_srv, real_path_master)
                if trunk_resources == "#":
                    trunk_resources += file_name
                    continue

                trunk_resources += f",{file_name}"
            elif hotfix_find:
                logging.info("type is hotfix")
                for hotfix_data in data_list_hotfix:
                    for hotfix_data_name in hotfix_data:
                        if match(ff.split("/")[-1], hotfix_data_name):
                            big_edit = str(str(hotfix_data[hotfix_data_name])[0]) + "." + str(str(hotfix_data[hotfix_data_name])[1:3]) + ".0"
                            small_edit = str(hotfix_data[hotfix_data_name]) + "000"
                            p4_path = ff.replace("art_release/cs", f"onlineupdate/{big_edit}/{small_edit}/client")
                            local_path = "/".join(p4_path.split("/")[:-1])
                            resourcesName = p4_path.split("/")[-1]
                            if not os.path.exists(local_path):
                                os.makedirs(local_path)
                            real_path = local_path + "/" + str(resourcesName)
                            file_dic.update({resourcesName: real_path})
                            self.delete_chmod(ff, real_path)
                            self.edit_file(p4_srv, real_path)
                            if hotfix_resources == "#":
                                hotfix_resources += f"{small_edit}: {file_name}"
                                continue
                            hotfix_resources += f",{small_edit}: {file_name}"
            else:
                logging.info("else case, type is cdn")
                file_dic.update({ff.split("/")[-1]: ff})
                self.edit_file(p4_srv, ff)
                if cdn_resources == "":
                    cdn_resources += file_name
                    continue
                cdn_resources += f",{file_name}"

        self.distributed_resources = cdn_resources.strip() + trunk_resources.strip() + hotfix_resources.strip()
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            self.house_distrib_report(files, result[0]["change"], file_dic)
            return result[0]["change"]

    def house_distrib_report(self, files, changelist_id, file_dic):
        resources_path = os.getcwd()
        remote_path = resources_path.replace("\\", "/").replace("gitlab/Script/scripts", "log")
        if not os.path.exists(remote_path):
            os.makedirs(remote_path)
        with open(remote_path + "/" + str(changelist_id) + ".html", "w+", encoding="utf-8") as obj:
            files_num = len(files)
            obj.write("""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>{}</title></head><body>""".format("分发报告"))
            obj.write(f"<p>资源数量：{files_num}</p>")
            if file_dic.__len__() != 0:
                for i, j in file_dic.items():
                    obj.write(f"<p>文件名：{i}  文件路径：{j}</p>")
            obj.write("</body></html>")

    def read_config_trunk_base_table(self, root) -> list:
        data_list = []
        resource_names = []
        config_path = root + r"\x5mplan\resmg\house\trunk-house-base.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            if isinstance(config_data[0], str):
                config_data[0] = config_data[0].strip()
            temp.update({"name": config_data[0]})
            # temp.update({"path": config_data[1]})
            data_list.append(temp)
        for resourcesName in data_list:
            resource_names.append(resourcesName["name"])
        return resource_names

    def read_config_hotfix_table(self, root) -> list:
        data_list, hotfix_list = [], []
        config_path = root + r"\x5mplan\resmg\house\hotfix-house-rule.xlsx"
        data = xlrd.open_workbook(config_path)
        table = data.sheets()[0]
        nrow = table.nrows
        for i in range(1, nrow):
            temp = {}
            config_data = table.row_values(i, 0, 2)
            temp.update({str(config_data[0]).strip(): str(config_data[1]).strip()})
            data_list.append(temp)
        for data in data_list:
            hotfix_list.append({[temp.replace("#", "[0-9]") for temp in data][0]: data[[temp for temp in data][0]]})
        return hotfix_list

    def get_max_root_b(self, p4_srv) -> str:
        file_map = p4_srv.dirs("//x5_mobile/mr/b/*")
        if file_map in [p4_error_code, p4_warning_code]:
            file_map = []
        all_branch = []
        for file in file_map:
            if re.match(r"(\d+)\.(\d+).(\d+)$", file["dir"].split("/")[-1]) != None:
                all_branch.append(file["dir"].split("/")[-1])
        all_branch.sort(key=lambda x: (int(x.split(".")[0]), int(x.split(".")[1]), int(x.split(".")[2])))
        max_version = all_branch[-1]
        return max_version

    def delete_chmod(self, ff, file):
        if os.path.exists(file):
            try:
                os.remove(file)
            except:
                os.chmod(file, stat.S_IWRITE)
                os.remove(file)
        shutil.copy(ff, file)


class CamActionResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_cam_action_p4_path
        self.kind = "camera_action"

    def get_updates(self, depot_files: list):
        for file in depot_files:
            if not file.startswith(self.prefix):
                continue
            for user in self.depot_user_dict.get(file):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file])
            if file.endswith(".anim") or file.endswith(".prefab"):
                self.file_list.append(file)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
            return json_result

        view_list = []
        for file in self.file_list:
            real_path = file.replace("split", "actions").replace("//x5_mobile/mr/art_release/art_src/assets/resources/art/")
            view_list.append(real_path)

        views = ",".join(view_list)
        json_result["update"] = "true"
        json_result["package_list"] = views
        json_result["changelist"] = self.latest_changelist
        send_user_list = list()
        for user in self.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                send_user_list.append("{}@h3d.com.cn".format(user))
        json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/role/action") == -1:
                continue
            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art/role/actions", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art/role/actions")
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]


class CamEffectResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_cam_effect_p4_path
        self.kind = "camera_effect"

    def get_updates(self, depot_files: list):
        for file in depot_files:
            if not file.startswith(self.prefix):
                continue
            for user in self.depot_user_dict.get(file):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file])
            if os.path.basename(file).endswith(".prefab"):
                self.file_list.append(file)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
            return json_result

        view_list = []
        p4_view_set = set()
        for file in self.file_list:
            mapping_path = file.replace(cdn_cam_effect_p4_path, "assets/resources/art/effect")
            view_list.append(mapping_path)

            package_dir = file.split("/")[9]
            p4_view_set.add(
                f"{self.prefix}/{package_dir}/... //${{p4Client}}/x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/effect/{package_dir}/..."
            )

        json_result["update"] = "true"
        json_result["package_list"] = ",".join(view_list)
        json_result["changelist"] = self.latest_changelist
        json_result["p4_view"] = ";".join(p4_view_set)
        send_user_list = list()
        for user in self.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                send_user_list.append("{}@h3d.com.cn".format(user))
        json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/") == -1:
                continue

            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", "x5_mobile/mr/art_release/cs/{0}/assetbundles/art".format(platform))
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]


class TimelineResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = cdn_timeline_p4_path
        self.kind = "timeline"
        # self.timeline2_depot_user_list = []

    def get_updates(self, depot_files: list):
        """
        获取本次流水线运行p4上更新的文件
        depot_files = [
            '//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048592/scene/Models/djny_bt.FBX',
            '//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048592/scene/Models/djny_diannao.FBX',
            '//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048592/scene/Models/djny_dibiao.FBX',
            '//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048592/scene/Models/djny_lk.FBX',
            '//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048592/scene/Models/djny_yizi.FBX'
        ]
        """
        for file in depot_files:
            timeline_number = os.getenv("TIMELINE_NUMBER")
            if timeline_number == "1":
                if "zhouyenp" in file or "newmulti" in file:
                    continue
            elif timeline_number == "2":
                if "zhouyenp" not in file and "newmulti" not in file:
                    continue
            file_relative_path = file.replace(self.prefix, "")
            package_list = file_relative_path.split("/")
            if len(package_list) >= 3:
                # 增加双飞类型8
                if len(package_list[2]) in [6, 7, 8] and package_list[2].isdigit():
                    for user in self.depot_user_dict.get(file):
                        logging.info(f"file: {file}")
                        # if "/newmulti/" in file or "/zhouyenp/" in file:
                        #     if user not in self.timeline2_depot_user_list:
                        #         self.timeline2_depot_user_list.append(user)
                        # elif user not in self.depot_user_list:
                        #     self.depot_user_list.append(user)
                        if user not in self.depot_user_list:
                            self.depot_user_list.append(user)
                    # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
                    #     self.depot_user_list.append(self.depot_user_dict[file])

                    daifei_common_action = "shuangfei/daifei_common_action"
                    if package_list[1] == "shuangfei" and daifei_common_action not in self.file_list:
                        self.file_list.append(daifei_common_action)

                    shouchi_common_action = "dongzuoshouchi/shouchi_common_action"
                    if package_list[1] == "dongzuoshouchi" and shouchi_common_action not in self.file_list:
                        self.file_list.append(shouchi_common_action)

                    # letter_path = "qinglvtao/1048592"
                    letter_path = "{}/{}".format(package_list[1], package_list[2])
                    if letter_path not in self.file_list:
                        self.file_list.append(letter_path)
        logging.info(self.depot_user_list)
        # logging.info(self.timeline2_depot_user_list)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
            return json_result

        view_list = []
        p4_view_list = []
        for file in self.file_list:
            local_p4_path = "assets/resources/art/timeline/{}".format(file)
            view_list.append(local_p4_path)
            p4_view_list.append(f"{self.prefix}/{file}/... //${{p4Client}}/x5_mobile/mobile_dancer/arttrunk/client/{local_p4_path}/...")

        views = ",".join(view_list)
        p4_view = ";".join(p4_view_list)
        json_result["update"] = "true"
        json_result["package_list"] = views
        json_result["changelist"] = self.latest_changelist
        json_result["p4_view"] = p4_view
        send_user_list = list()
        for user in self.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                send_user_list.append("{}@h3d.com.cn".format(user))
        json_result["send_user"] = ",".join(send_user_list)
        # 写文件到resources.txt
        self.write_resources(views)
        return json_result

    def submit_files_to_p4(self, file_paths, root, platform, workspace, desc, source_changelist, bak_line=False):
        record_path = os.path.join(root, "x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/timeline")
        latest_record = self.get_latest_files(record_path, lambda x: (x.startswith("ios") or x.startswith("android")) and x.endswith(".txt"))
        logging.info(f"latest file: {latest_record}")
        with open(os.path.join(record_path, latest_record), "r", encoding="utf-8") as f:
            submit_files = f.readlines()

        # root = G:\jenkins_timeline\android\workspace\demos\test_edition_camera_release
        # 提交资源路径G:\jenkins_timeline\android\workspace\demos\test_edition_camera_release\x5_mobile\mr\art_release\cs\android\assetbundles
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for submit_file in submit_files:
            if submit_files == "":
                continue

            submit_file_full_path = os.path.join(root, "x5_mobile/mr/art_release/cs/{}/assetbundles/{}".format(platform, submit_file))
            logging.info("submit_file_full_path: {}".format(submit_file_full_path))
            submit_file_full_path = submit_file_full_path.replace("\\", "/")
            self.edit_file(p4_srv, submit_file_full_path)

        changelist = ""
        result = p4_srv.submit("-d", desc)
        logging.info("submit result: {}".format(result))

        if result not in [p4_error_code, p4_warning_code]:
            changelist = result[0]["change"]
        print(changelist)

        # TODO 更新动作放到持久化配置文件的步骤
        config.set("P4", self.kind, source_changelist)
        config.write(open("../config/config.ini", "w"))

    def submit_video_files(self, workspace, platform, root, view_list, desc):
        video_id_dict = self.__get_timeline_video_ids(root, view_list)
        if not video_id_dict:
            return
        self.__submit_video_to_p4(workspace, platform, root, video_id_dict, desc)
        self.__submit_video_to_git(root, platform, video_id_dict, desc)

    def __get_timeline_video_ids(self, root, view_list) -> dict:
        video_id_dict = {}
        for view in view_list:
            dir = view.strip("...").split("{p4Client}/")[1]
            timeline_dir = os.path.join(root, dir)
            id = re.findall(re.compile(r"\d+", re.S), dir.split("timeline")[1])
            if not id:
                logging.info(f"{dir} not find timeline id")
                continue
            if "video" not in os.listdir(timeline_dir):
                logging.info(f"{dir} not exist video dir")
                continue
            video_id_dict[id[0]] = dir
        return video_id_dict

    def __submit_video_to_p4(self, workspace, platform, root, video_id_dict: dict, desc):
        logging.info(f"start submit video file")
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for video_id in video_id_dict.keys():
            submit_dir = os.path.join(root, "x5_mobile/mr/art_release/cs/{}/assetbundles/art/mp4/timeline/{}/video".format(platform, video_id))
            if os.path.exists(submit_dir):
                shutil.rmtree(submit_dir)
            shutil.copytree(os.path.join(root, video_id_dict[video_id], "video"), submit_dir)
            for file in os.listdir(submit_dir):
                submit_file = os.path.join(submit_dir, file).replace("\\", "/")
                logging.info(f"submit file: {submit_file}")
                self.edit_file(p4_srv, submit_file)
        changelist = ""
        result = p4_srv.submit("-d", desc)
        logging.info("submit video result: {}".format(result))

        if result not in [p4_error_code, p4_warning_code]:
            changelist = result[0]["change"]
        logging.info("submit video changelist: {}".format(changelist))

    def __submit_video_to_git(self, root, platform: str, video_id_dict: dict, desc: str):
        if platform.lower() == "ios":
            return
        x5mconfig_path = os.path.join(root, "x5mconfig", "cdn")
        if not os.path.exists(x5mconfig_path):
            os.makedirs(x5mconfig_path)
        os.chdir(x5mconfig_path)
        self.__init_git_cdn(x5mconfig_path)

        submit_dir = os.path.join(root, "x5mconfig/cdn/config/shared/video")
        if not os.path.exists(submit_dir):
            os.makedirs(submit_dir)
        for video_id in video_id_dict.keys():
            shutil.copy(
                os.path.join(root, f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art/timeline/video/{video_id}/{video_id}_video.xml"),
                submit_dir,
            )
        os.system("git add .")
        os.system('git commit -m "{}"'.format(desc))
        ret = os.system("git push origin cdn")
        if ret != 0:
            raise Exception(f"上传git失败, 错误码: {ret}")
        commit_id = os.popen("git rev-parse HEAD").read()
        logging.info(f"https://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig/-/commit/{commit_id}")

    def __init_git_cdn(self, cdn_path):
        if not os.path.exists(os.path.join(cdn_path, ".git/config")):
            os.system("git clone -b cdn http://oauth2:<EMAIL>/dgm/x5mconfig.git . -q")
        os.system("git clean -xdfq")
        os.system("git reset --hard HEAD -q")
        os.system("git pull origin cdn -q")

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/") == -1:
                continue
            package_list = f.split("/")
            if len(package_list[-1]) == 7 and package_list[-1].isdigit() and package_list[-3] == "timeline":
                package_list.pop(-2)
            f = "/".join(package_list)
            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", "x5_mobile/mr/art_release/cs/{0}/assetbundles/art".format(platform))
            f, _ = os.path.splitext(f)
            f_h3d = "{}.h3dmanifest".format(f)
            self.edit_file(p4_srv, f)
            self.edit_file(p4_srv, f_h3d)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]


class ActionsMasterManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = actions_master_p4_path
        self.kind = "actions_master"

    def get_updates(self, depot_files: list):
        for depot_file in depot_files:
            if not depot_file.startswith(self.prefix):
                continue
            if depot_file.startswith(os.path.join(self.prefix, "ingame_nodance_actions_anim").replace("\\", "/")):
                if depot_file.endswith(".act.txt"):
                    self.file_list.append(depot_file)
            elif not (
                not depot_file.startswith(os.path.join(self.prefix, "actions_anim").replace("\\", "/"))
                and not depot_file.startswith(os.path.join(self.prefix, "cloth_anim").replace("\\", "/"))
                and not depot_file.startswith(os.path.join(self.prefix, "actions_anim_expression").replace("\\", "/"))
            ) or depot_file.startswith(os.path.join(self.prefix, "expression").replace("\\", "/")):
                if depot_file.endswith(".anim"):
                    self.file_list.append(depot_file)

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/role/action") == -1:
                continue
            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art")
            f, _ = os.path.splitext(f)
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]

    def get_p4_tool(self, workspace) -> P4Tool:
        return P4Tool(p4_user, p4_host, p4_passwd, workspace, "")


class MakeUpManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = kwargs["prefix"] if "prefix" in kwargs else makeup_p4_path
        self.kind = "makeup"
        self.makeup_filenames = list()

    def get_updates(self, depot_files: list):
        """
        获取妆容是否打包
        判断 整个具体资源目录是否有变更，如果有变更，自动获取对应的 目录名
        """
        exist_list = list()
        for file in depot_files:
            if not file.startswith(self.prefix):
                continue
            for user in self.depot_user_dict.get(file):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file])
            release_path = file.replace(self.prefix, "").lstrip("/")
            release_list = release_path.split("/")
            valid_path = "/".join(release_list[:3])
            if valid_path not in exist_list:
                exist_list.append(valid_path)
                p4_valid_path = "{}/{}".format(self.prefix, valid_path)
                self.file_list.append(p4_valid_path)
                self.makeup_filenames.append(release_list[2])

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            view_list = []
            views = ""
            for file in self.file_list:
                index = file.find("art_src")
                if index == -1:
                    continue
                real_path = "assets/resources/art" + file[index + 7 :]
                view_list.append(real_path)
            views += ",".join(view_list)
            json_result["update"] = "true"
            json_result["package_list"] = views
            json_result["unity_package_list"] = ",".join(self.makeup_filenames)
            json_result["changelist"] = self.latest_changelist
            send_user_list = list()
            for user in self.depot_user_list:
                if not user.endswith("@h3d.com.cn"):
                    send_user_list.append("{}@h3d.com.cn".format(user))
            json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def get_p4_tool(self, workspace, root="") -> P4Tool:
        return P4Tool(p4_user, p4_host, p4_passwd, workspace, root)

    def submit_files_to_p4(self, file_paths, root, platform, workspace, desc, source_changelist, bak_line=False):
        if file_paths == "":
            with open(f"{root}/x5_mobile/mobile_dancer/arttrunk/client/AssetBundleTool/resources.txt", "r", encoding="UTF-8") as f:
                file_paths = f.read()
        files = file_paths.split(",")
        if "-" in source_changelist:
            branch = "b/" + source_changelist[: source_changelist.rfind("-")]
        else:
            branch = "Resources"
        changelist = self.submit_p4(platform, workspace, files, desc, branch, root)
        print(changelist)
        if "-" in source_changelist:
            source_changelist = source_changelist[source_changelist.rfind("-") + 1 :]

        config.set("P4", self.kind, source_changelist)
        config.write(open("../config/config.ini", "w"))

    def submit_p4(self, platform, workspace, files: list, desc, branch, root) -> str:
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        for f in files:
            if f.find("assets/resources/art/role/makeup") == -1:
                continue
            f = os.path.join(root, f).replace("\\", "/")
            f = f.replace("assets/resources/art", f"x5_mobile/mr/{branch}/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art")
            self.edit_file(p4_srv, f)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        pass


class HouseSceneResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = house_scene_p4_path
        self.kind = "house_scene"

    def get_updates(self, depot_files: list):
        """
        获取符合条件的 Unity文件
        """
        for file in depot_files:
            if not file.startswith(self.prefix):
                logging.debug(f"{file} is not start with {self.prefix}")
                continue
            for user in self.depot_user_dict.get(file):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file])
            if file.endswith(".unity"):
                relative_path = file.replace(self.prefix, "assets/staticresources/art/3d/house/scene")
                self.file_list.append(relative_path)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
            return json_result

        view_list = []
        for file in self.file_list:
            view_list.append(file)

        json_result["update"] = "true"
        json_result["package_list"] = ",".join(view_list)
        json_result["changelist"] = self.latest_changelist
        send_user_list = list()
        for user in self.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                send_user_list.append("{}@h3d.com.cn".format(user))
        json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def submit_to_p4(self, file_paths, root, platform, workspace, desc, branch, hotfix_data, hotfix_workspace, source_changelist):
        """
        提交p4
        """
        files = file_paths.split(",")
        hotfix_data_dict = dict()
        if hotfix_data:
            # 转化切割数据
            hotfix_data_list = hotfix_data.split(",")
            for i in hotfix_data_list:
                key, val = i.split(":")
                hotfix_data_dict[key] = val
        changelist = self.submit_extend(platform, workspace, files, desc, root, branch)
        print(changelist)
        hotfix_changelist = self.hotfix_submit(platform, files, desc, root, hotfix_data_dict, hotfix_workspace)
        print(hotfix_changelist)
        config.set("P4", self.kind, source_changelist)
        config.write(open("../config/config.ini", "w"))

    def submit_extend(self, platform, workspace, files, desc, root, branch):
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        ab_platform = "android" if platform == "android" else "iOS"
        for f in files:
            if f.find("assets/staticresources/art/3d") == -1:
                continue
            filename = f.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            resource_file = f"x5_mobile/mr/Resources/cs/{ab_platform}/assetbundles/art/house/scene/{file_prefix}"
            resource_h3d_file = "{}.h3dmanifest".format(resource_file)
            resource_file_path = os.path.join(root, resource_file).replace("\\", "/")
            resource_h3d_file_path = os.path.join(root, resource_h3d_file).replace("\\", "/")
            self.edit_file(p4_srv, resource_file_path)
            self.edit_file(p4_srv, resource_h3d_file_path)
            cdn_file = f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/art/house/scene/{file_prefix}"
            cdn_h3d_file = "{}.h3dmanifest".format(cdn_file)
            cdn_file_path = os.path.join(root, cdn_file).replace("\\", "/")
            cdn_h3d_file_path = os.path.join(root, cdn_h3d_file).replace("\\", "/")
            self.edit_file(p4_srv, cdn_file_path)
            self.edit_file(p4_srv, cdn_h3d_file_path)
            branch_file = f"x5_mobile/mr/b/{branch}/cs/{ab_platform}/assetbundles/art/house/scene/{file_prefix}"
            branch_h3d_file = "{}.h3dmanifest".format(branch_file)
            branch_file_path = os.path.join(root, branch_file).replace("\\", "/")
            branch_h3d_file_path = os.path.join(root, branch_h3d_file).replace("\\", "/")
            self.edit_file(p4_srv, branch_file_path)
            self.edit_file(p4_srv, branch_h3d_file_path)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]

    def hotfix_submit(self, platform, files, desc, root, hotfix_data_dict, hotfix_workspace):
        """
        热更提交P4
        """
        p4_srv = self.get_p4_tool(hotfix_workspace)
        p4_srv.login()
        ab_platform = "android" if platform == "android" else "iOS"
        flag = False
        for f in files:
            if f.find("assets/staticresources/art/3d") == -1:
                continue
            filename = f.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            if file_prefix in hotfix_data_dict:
                flag = True
                item = hotfix_data_dict[file_prefix]
                main_version = item.split(".")[0].rstrip("2")
                main_version = "{}.{}.0".format(main_version[:1], main_version[1:])
                hotfix_file = f"x5_mobile/mr/onlineupdate/{main_version}/{item}/cs/{ab_platform}/assetbundles/art/house/scene/{file_prefix}"
                hotfix_h3d_file = "{}.h3dmanifest".format(hotfix_file)
                hotfix_file_path = os.path.join(root, hotfix_file).replace("\\", "/")
                hotfix_h3d_file_path = os.path.join(root, hotfix_h3d_file).replace("\\", "/")
                self.edit_file(p4_srv, hotfix_file_path)
                self.edit_file(p4_srv, hotfix_h3d_file_path)
        if flag:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return ""
            else:
                return result[0]["change"]
        return "0"

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        pass

    def get_hotfix_p4_views(self, p4workspace, workspace, data, platform, packages, data_dict):
        """
        获取热更
        """
        local_paths = list()
        views = list()
        for item in data:
            main_version = item.split(".")[0].rstrip("2")
            main_version = "{}.{}.0".format(main_version[:1], main_version[1:])
            views.append(
                f"//x5_mobile/mr/onlineupdate/{main_version}/{item}/client/{platform}/assetbundles/art/house/scene/... //{p4workspace}/x5_mobile/mr/onlineupdate/{main_version}/{item}/cs/{platform}/assetbundles/art/house/scene/..."
            )
            local_view = f"{workspace}/x5_mobile/mr/onlineupdate/{main_version}/{item}/cs/{platform}/assetbundles/art/house/scene"
            local_view = local_view.replace("/", os.path.sep).replace("\\", os.path.sep)
            local_paths.append(local_view)
        return "\n".join(views), ",".join(local_paths)

    def deal_hotfix_data(self, hotfix_data, hotfix_paths):
        """
        处理热更数据
        """
        hotfix_data_dict = dict()
        hotfix_mapping = dict()
        if hotfix_data:
            # 转化切割数据
            hotfix_data_list = hotfix_data.split(",")
            for i in hotfix_data_list:
                key, val = i.split(":")
                hotfix_data_dict[key] = val
            # 映射 热更分支 与 对应 路径
            hotfix_paths = hotfix_paths.replace("/", os.path.sep).replace("\\", os.path.sep)
            hotfix_path_list = hotfix_paths.split(",")

            for item in hotfix_data_dict.values():
                for i in hotfix_path_list:
                    if item in i:
                        hotfix_mapping[item] = i
        return hotfix_data_dict, hotfix_mapping

    def distribute(self, move_list, source_path, distribute_path_list, packages):
        """
        分发
        """
        for dis in distribute_path_list:
            if not os.path.exists(dis):
                os.makedirs(dis)
            for file in move_list:
                source_file_path = os.path.join(source_path, file)
                source_h3d_file_path = os.path.join(source_path, "{}.h3dmanifest".format(file))
                # 文件赋权
                os.chmod(source_file_path, stat.S_IWRITE)
                os.chmod(source_h3d_file_path, stat.S_IWRITE)
                dist_file_path = os.path.join(dis, file)
                dist_h3d_file_path = os.path.join(dis, "{}.h3dmanifest".format(file))
                if os.path.exists(dist_file_path):
                    os.chmod(dist_file_path, stat.S_IWRITE)
                if os.path.exists(dist_h3d_file_path):
                    os.chmod(dist_h3d_file_path, stat.S_IWRITE)
                shutil.copyfile(source_file_path, dist_file_path)
                shutil.copyfile(source_h3d_file_path, dist_h3d_file_path)

    def distribute_hotfix(self, move_list, source_path, hotfix_data, hotfix_paths, packages):
        """
        分发热更
        """
        # 检查有无热更分发, 并处理数据
        hotfix_data_dict, hotfix_mapping = self.deal_hotfix_data(hotfix_data, hotfix_paths)
        for file in move_list:
            if file in hotfix_data_dict:
                source_file_path = os.path.join(source_path, file)
                source_h3d_file_path = os.path.join(source_path, "{}.h3dmanifest".format(file))
                hotfix_path = hotfix_mapping[hotfix_data_dict[file]]
                if not os.path.exists(hotfix_path):
                    os.makedirs(hotfix_path)
                hotfix_file_path = os.path.join(hotfix_path, file)
                hotfix_h3d_file_path = os.path.join(hotfix_path, "{}.h3dmanifest".format(file))
                if os.path.exists(hotfix_file_path):
                    os.chmod(hotfix_file_path, stat.S_IWRITE)
                if os.path.exists(hotfix_h3d_file_path):
                    os.chmod(hotfix_h3d_file_path, stat.S_IWRITE)
                shutil.copyfile(source_file_path, hotfix_file_path)
                shutil.copyfile(source_h3d_file_path, hotfix_h3d_file_path)


class SceneResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.prefix = scene_p4_path
        self.kind = "scene"
        self.special_file_list = list()
        self.c_p4_prefix = "//x5_mobile/mr/art_resource/art_src/scene/c/"
        self.c_ab_prefix = "assets/staticresources/art/3d/stage/c/"
        self.special_scene_dir_name = ["3d", "shequ_01", "sta", "streaming", "xuejingshequ", "zaofangzi"]

    def check_special_dir(self, file):
        """ """
        for i in self.special_scene_dir_name:
            path = "{}/{}/".format(self.prefix, i)
            if file.startswith(path):
                return True

    def get_updates(self, depot_files: list):
        """
        获取符合条件的unity文件
        """
        for file in depot_files:
            if not file.startswith(self.prefix):
                continue
            for user in self.depot_user_dict.get(file):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file])
            # 排除掉社区和雪景社区的自由目录，以及一些特殊的目录
            if self.check_special_dir(file) is True:
                continue
            if file.endswith(".unity"):
                relative_path = file.replace(self.prefix, "assets/staticresources/art/3d/stage")
                # if file.startswith(self.c_p4_prefix):
                #     self.special_file_list.append(relative_path)
                # else:
                self.file_list.append(relative_path)

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            view_list = []
            views = ""
            for file in self.file_list:
                view_list.append(file)
            views += ",".join(view_list)
            json_result["update"] = "true"
            json_result["package_list"] = views
            json_result["changelist"] = self.latest_changelist
            send_user_list = list()
            for user in self.depot_user_list:
                if not user.endswith("@h3d.com.cn"):
                    send_user_list.append("{}@h3d.com.cn".format(user))
            json_result["send_user"] = ",".join(send_user_list)

            self.write_resources(views)
        return json_result

    def submit_to_p4(
        self, file_paths, root, platform, workspace, desc, branch, hotfix_data, hotfix_workspace, special_data, special_workspace, source_changelist
    ):
        """
        提交p4
        """
        files = file_paths.split(",")
        hotfix_data_dict = dict()
        if hotfix_data:
            # 转化切割数据
            hotfix_data_list = hotfix_data.split(",")
            for i in hotfix_data_list:
                key, val = i.split(":")
                hotfix_data_dict[key] = val

        special_data_dict = dict()
        if special_data:
            # 转化切割数据
            special_data_list = special_data.split(",")
            for i in special_data_list:
                key, val = i.split(":")
                special_data_dict[key] = val

        ab_dict = dict()
        for i in files:
            status = i.startswith(self.c_ab_prefix)
            filename = i.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            local_real_path = i.replace(self.c_ab_prefix, "").rstrip(filename).rstrip("/")
            ab_dict[file_prefix] = {"is_c": status, "c_prefix": local_real_path}
        changelist = self.submit_extend(platform, workspace, files, desc, root, branch, ab_dict, special_data_dict)
        print(changelist)
        hotfix_changelist = self.hotfix_submit(platform, files, desc, root, hotfix_data_dict, hotfix_workspace, ab_dict, special_data_dict)
        print(hotfix_changelist)
        special_branch_changlist = self.special_branch_submit(platform, files, desc, root, special_data_dict, special_workspace, ab_dict)
        print(special_branch_changlist)
        config.set("P4", self.kind, source_changelist)
        config.write(open("../config/config.ini", "w"))

    def is_speical_branch(self, path: str) -> bool:
        """
        判断分发规则是否为特殊分支
        """
        logging.info(f"path: {path}")
        pattern = re.compile(r"^\d+\.\d+$")
        path = str(path).strip()
        ret = re.findall(pattern=pattern, string=path)
        if ret:
            return False
        return True

    def is_special_branch_dis(self, file: str, hotfix_data_dict: dict) -> bool:
        """
        是否为特殊分支分发规则
        """
        branch_info = hotfix_data_dict.get(file)
        logging.info(f"branch_info: {branch_info}")
        if branch_info and self.is_speical_branch(branch_info):
            return True
        return False

    def submit_extend(self, platform, workspace, files, desc, root, branch, ab_dict, special_data_dict):
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        ab_platform = "android" if platform == "android" else "iOS"
        for f in files:
            if f.find("assets/staticresources/art/3d") == -1:
                continue
            filename = f.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            if ab_dict[file_prefix]["is_c"]:
                # c目录
                path = "c/{}/scene".format(ab_dict[file_prefix]["c_prefix"]).replace("/", os.path.sep).replace("\\", os.path.sep)
            else:
                path = "scene"

            # 如果是特殊分支，不分发主支、分支和CDN
            if self.is_special_branch_dis(file_prefix, special_data_dict):
                continue
            resource_file = f"x5_mobile/mr/Resources/cs/{ab_platform}/assetbundles/{path}/{file_prefix}"
            resource_h3d_file = "{}.h3dmanifest".format(resource_file)
            resource_file_path = os.path.join(root, resource_file).replace("\\", "/")
            resource_h3d_file_path = os.path.join(root, resource_h3d_file).replace("\\", "/")
            self.edit_file(p4_srv, resource_file_path)
            self.edit_file(p4_srv, resource_h3d_file_path)
            cdn_file = f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/{path}/{file_prefix}"
            cdn_h3d_file = "{}.h3dmanifest".format(cdn_file)
            cdn_file_path = os.path.join(root, cdn_file).replace("\\", "/")
            cdn_h3d_file_path = os.path.join(root, cdn_h3d_file).replace("\\", "/")
            self.edit_file(p4_srv, cdn_file_path)
            self.edit_file(p4_srv, cdn_h3d_file_path)
            branch_file = f"x5_mobile/mr/b/{branch}/cs/{ab_platform}/assetbundles/{path}/{file_prefix}"
            branch_h3d_file = "{}.h3dmanifest".format(branch_file)
            branch_file_path = os.path.join(root, branch_file).replace("\\", "/")
            branch_h3d_file_path = os.path.join(root, branch_h3d_file).replace("\\", "/")
            self.edit_file(p4_srv, branch_file_path)
            self.edit_file(p4_srv, branch_h3d_file_path)
        result = p4_srv.submit("-d", desc)
        if result in [p4_error_code, p4_warning_code]:
            return ""
        else:
            return result[0]["change"]

    def hotfix_submit(self, platform, files, desc, root, hotfix_data_dict, hotfix_workspace, ab_dict, special_data_dict):
        """
        热更提交P4
        """
        p4_srv = self.get_p4_tool(hotfix_workspace)
        p4_srv.login()
        ab_platform = "android" if platform == "android" else "iOS"
        flag = False
        for f in files:
            if f.find("assets/staticresources/art/3d") == -1:
                continue
            filename = f.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            if ab_dict[file_prefix]["is_c"]:
                # c目录
                path = "c/{}/scene".format(ab_dict[file_prefix]["c_prefix"]).replace("/", os.path.sep).replace("\\", os.path.sep)
            else:
                path = "scene"

            # 如果是特殊分支分发，跳过
            if self.is_special_branch_dis(file_prefix, special_data_dict):
                continue

            if file_prefix in hotfix_data_dict:
                flag = True
                item = hotfix_data_dict[file_prefix]
                main_version = item.split(".")[0].rstrip("2")
                main_version = "{}.{}.0".format(main_version[:1], main_version[1:])
                hotfix_file = f"x5_mobile/mr/onlineupdate/{main_version}/{item}/cs/{ab_platform}/assetbundles/{path}/{file_prefix}"
                hotfix_h3d_file = "{}.h3dmanifest".format(hotfix_file)
                hotfix_file_path = os.path.join(root, hotfix_file).replace("\\", "/")
                hotfix_h3d_file_path = os.path.join(root, hotfix_h3d_file).replace("\\", "/")
                self.edit_file(p4_srv, hotfix_file_path)
                self.edit_file(p4_srv, hotfix_h3d_file_path)
        if flag:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return ""
            else:
                return result[0]["change"]
        return "0"

    def special_branch_submit(self, platform, files, desc, root, special_data_dict, special_workspace, ab_dict):
        """
        提交特殊分支
        """
        p4_srv = self.get_p4_tool(special_workspace)
        p4_srv.login()
        ab_platform = "android" if platform == "android" else "iOS"
        flag = False
        for f in files:
            if f.find("assets/staticresources/art/3d") == -1:
                continue
            filename = f.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            if ab_dict[file_prefix]["is_c"]:
                # c目录
                path = "c/{}/scene".format(ab_dict[file_prefix]["c_prefix"]).replace("/", os.path.sep).replace("\\", os.path.sep)
            else:
                path = "scene"

            # 如果不是特殊分支，跳过
            if not self.is_special_branch_dis(file_prefix, special_data_dict):
                continue
            if file_prefix in special_data_dict:
                flag = True
                item = special_data_dict[file_prefix]
                branch = item.strip()
                branch_file = f"x5_mobile/mr/b/{branch}/cs/{ab_platform}/assetbundles/{path}/{file_prefix}"
                branch_h3d_file = "{}.h3dmanifest".format(branch_file)
                branch_file_path = os.path.join(root, branch_file).replace("\\", "/")
                branch_h3d_file_path = os.path.join(root, branch_h3d_file).replace("\\", "/")
                self.edit_file(p4_srv, branch_file_path)
                self.edit_file(p4_srv, branch_h3d_file_path)
        if flag:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return ""
            else:
                return result[0]["change"]
        return "0"

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        pass

    def get_hotfix_p4_views(self, p4workspace, workspace, data, platform, packages, data_dict):
        """
        获取热更
        """
        local_paths = list()
        views = list()
        package_list = packages.split(",")
        c_version_list, version_list = list(), list()
        for i in package_list:
            filename = i.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            if file_prefix in data_dict:
                item = data_dict[file_prefix]
                main_version = item.split(".")[0].rstrip("2")
                main_version = "{}.{}.0".format(main_version[:1], main_version[1:])
                # 判断是否为c目录
                if i.startswith(self.c_ab_prefix):
                    local_real_path = i.replace(self.c_ab_prefix, "").rstrip(filename).rstrip("/")
                    local_view = f"{workspace}/x5_mobile/mr/onlineupdate/{main_version}/{item}/cs/{platform}/assetbundles/c/{local_real_path}/scene"
                    if item in c_version_list:
                        continue
                    c_version_list.append(item)
                    views.append(
                        f"//x5_mobile/mr/onlineupdate/{main_version}/{item}/client/{platform}/assetbundles/c/*/scene/... //{p4workspace}/x5_mobile/mr/onlineupdate/{main_version}/{item}/cs/{platform}/assetbundles/c/*/scene/..."
                    )
                else:
                    if item in version_list:
                        continue
                    version_list.append(item)
                    views.append(
                        f"//x5_mobile/mr/onlineupdate/{main_version}/{item}/client/{platform}/assetbundles/scene/... //{p4workspace}/x5_mobile/mr/onlineupdate/{main_version}/{item}/cs/{platform}/assetbundles/scene/..."
                    )
                    local_view = f"{workspace}/x5_mobile/mr/onlineupdate/{main_version}/{item}/cs/{platform}/assetbundles/scene"
                local_view = local_view.replace("/", os.path.sep).replace("\\", os.path.sep)
                local_paths.append(local_view)
        return "\n".join(views), ",".join(local_paths)

    def get_special_branch_views(self, p4workspace, workspace, platform, packages, data_dict):
        """
        获取特殊分支
        """
        local_paths = list()
        views = list()
        package_list = packages.split(",")
        c_version_list, version_list = list(), list()
        for i in package_list:
            filename = i.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            if file_prefix in data_dict:
                item = data_dict[file_prefix]
                # 判断是否为c目录
                if i.startswith(self.c_ab_prefix):
                    local_real_path = i.replace(self.c_ab_prefix, "").rstrip(filename).rstrip("/")
                    local_view = f"{workspace}/x5_mobile/mr/b/{item}/cs/{platform}/assetbundles/c/{local_real_path}/scene"
                    if item in c_version_list:
                        continue
                    c_version_list.append(item)
                    views.append(
                        f"//x5_mobile/mr/b/{item}/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/c/*/scene/... //{p4workspace}/x5_mobile/mr/b/{item}/cs/{platform}/assetbundles/c/*/scene/..."
                    )
                else:
                    if item in version_list:
                        continue
                    version_list.append(item)
                    views.append(
                        f"//x5_mobile/mr/b/{item}/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/scene/... //{p4workspace}/x5_mobile/mr/b/{item}/cs/{platform}/assetbundles/scene/..."
                    )
                    local_view = f"{workspace}/x5_mobile/mr/b/{item}/cs/{platform}/assetbundles/scene"
                local_view = local_view.replace("/", os.path.sep).replace("\\", os.path.sep)
                local_paths.append(local_view)
        return "\n".join(views), ",".join(local_paths)

    def check_move_source_exists(self, packages, source_path):
        """
        检查 待 移动的ab资源是否存在
        """
        flag, msg = False, ""
        package_list = packages.split(",")
        source_package_list = os.listdir(source_path)
        move_list = list()
        try:
            for i in package_list:
                filename = i.rsplit("/", maxsplit=1)[-1]
                file_prefix = filename.replace(".unity", "")
                if file_prefix not in source_package_list:
                    continue
                    # raise ValueError("目录{}不存在{}".format(source_path, file_prefix))
                if "{}.h3dmanifest".format(file_prefix) not in source_package_list:
                    raise ValueError("目录{}不存在{}".format(source_path, "{}.h3dmanifest".format(file_prefix)))
                move_list.append(file_prefix)
            if not move_list:
                raise ValueError("没有需要移动的资源")
            flag = True
        except Exception as exp:
            msg = str(exp)
        return flag, msg, move_list

    def distribute(self, move_list, source_path, distribute_path_list, packages):
        """
        分发
        """
        ab_dict = dict()
        package_list = packages.split(",")
        for i in package_list:
            status = i.startswith(self.c_ab_prefix)
            filename = i.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            local_real_path = i.replace(self.c_ab_prefix, "").rstrip(filename).rstrip("/")
            ab_dict[file_prefix] = {"is_c": status, "c_prefix": local_real_path}
        for dis in distribute_path_list:
            if not os.path.exists(dis):
                os.makedirs(dis)
            for file in move_list:
                if ab_dict[file]["is_c"]:
                    # 表示是c目录下的ab文件
                    path = "{}/c/{}/scene".format(dis, ab_dict[file]["c_prefix"]).replace("/", os.path.sep).replace("\\", os.path.sep)
                else:
                    path = "{}/scene".format(dis).replace("/", os.path.sep).replace("\\", os.path.sep)
                if not os.path.exists(path):
                    os.makedirs(path)
                source_file_path = os.path.join(source_path, file)
                source_h3d_file_path = os.path.join(source_path, "{}.h3dmanifest".format(file))
                # 文件赋权
                os.chmod(source_file_path, stat.S_IWRITE)
                os.chmod(source_h3d_file_path, stat.S_IWRITE)
                dist_file_path = os.path.join(path, file)
                dist_h3d_file_path = os.path.join(path, "{}.h3dmanifest".format(file))
                if os.path.exists(dist_file_path):
                    os.chmod(dist_file_path, stat.S_IWRITE)
                if os.path.exists(dist_h3d_file_path):
                    os.chmod(dist_h3d_file_path, stat.S_IWRITE)
                shutil.copyfile(source_file_path, dist_file_path)
                shutil.copyfile(source_h3d_file_path, dist_h3d_file_path)

    def distribute_hotfix(self, move_list, source_path, hotfix_data, hotfix_paths, packages):
        """
        分发热更
        """
        ab_dict = dict()
        package_list = packages.split(",")
        for i in package_list:
            status = i.startswith(self.c_ab_prefix)
            filename = i.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            local_real_path = i.replace(self.c_ab_prefix, "").rstrip(filename).rstrip("/")
            ab_dict[file_prefix] = {"is_c": status, "c_prefix": local_real_path}
        # 检查有无热更分发, 并处理数据
        hotfix_mapping = self.deal_hotfix_data(hotfix_data, hotfix_paths, ab_dict)
        for file in move_list:
            if file in hotfix_mapping:
                hotfix_path = hotfix_mapping[file]
                if not os.path.exists(hotfix_path):
                    os.makedirs(hotfix_path)
                source_file_path = os.path.join(source_path, file)
                source_h3d_file_path = os.path.join(source_path, "{}.h3dmanifest".format(file))
                hotfix_file_path = os.path.join(hotfix_path, file)
                hotfix_h3d_file_path = os.path.join(hotfix_path, "{}.h3dmanifest".format(file))
                if os.path.exists(hotfix_file_path):
                    os.chmod(hotfix_file_path, stat.S_IWRITE)
                if os.path.exists(hotfix_h3d_file_path):
                    os.chmod(hotfix_h3d_file_path, stat.S_IWRITE)
                shutil.copyfile(source_file_path, hotfix_file_path)
                shutil.copyfile(source_h3d_file_path, hotfix_h3d_file_path)

    def distribute_special_branch(self, move_list, source_path, hotfix_data, hotfix_paths, packages):
        """
        分发特殊分支
        """
        ab_dict = dict()
        package_list = packages.split(",")
        for i in package_list:
            status = i.startswith(self.c_ab_prefix)
            filename = i.rsplit("/", maxsplit=1)[-1]
            file_prefix = filename.replace(".unity", "")
            local_real_path = i.replace(self.c_ab_prefix, "").rstrip(filename).rstrip("/")
            ab_dict[file_prefix] = {"is_c": status, "c_prefix": local_real_path}
        # 检查有无特殊分支分发, 并处理数据
        hotfix_mapping = self.deal_special_branch_data(hotfix_data, hotfix_paths, ab_dict)
        for file in move_list:
            if file in hotfix_mapping:
                hotfix_path = hotfix_mapping[file]
                if not os.path.exists(hotfix_path):
                    os.makedirs(hotfix_path)
                source_file_path = os.path.join(source_path, file)
                source_h3d_file_path = os.path.join(source_path, "{}.h3dmanifest".format(file))
                hotfix_file_path = os.path.join(hotfix_path, file)
                hotfix_h3d_file_path = os.path.join(hotfix_path, "{}.h3dmanifest".format(file))
                if os.path.exists(hotfix_file_path):
                    os.chmod(hotfix_file_path, stat.S_IWRITE)
                if os.path.exists(hotfix_h3d_file_path):
                    os.chmod(hotfix_h3d_file_path, stat.S_IWRITE)
                shutil.copyfile(source_file_path, hotfix_file_path)
                shutil.copyfile(source_h3d_file_path, hotfix_h3d_file_path)

    def deal_special_branch_data(self, hotfix_data, hotfix_paths, ab_dict):
        """
        处理特殊分支数据
        """
        hotfix_data_dict = dict()
        hotfix_mapping = dict()
        if hotfix_data:
            # 转化切割数据
            hotfix_data_list = hotfix_data.split(",")
            for i in hotfix_data_list:
                key, val = i.split(":")
                hotfix_data_dict[key] = val
            # 映射 热更分支 与 对应 路径
            hotfix_paths = hotfix_paths.replace("/", os.path.sep).replace("\\", os.path.sep)
            hotfix_path_list = hotfix_paths.split(",")
            for k, v in hotfix_data_dict.items():
                for i in hotfix_path_list:
                    if ab_dict[k]["is_c"]:
                        # c目录
                        path = "assetbundles/c/{}/scene".format(ab_dict[k]["c_prefix"]).replace("/", os.path.sep).replace("\\", os.path.sep)
                    else:
                        path = "assetbundles/scene".replace("/", os.path.sep).replace("\\", os.path.sep)
                    if v in i and path in i:
                        hotfix_mapping[k] = i
        return hotfix_mapping

    def deal_hotfix_data(self, hotfix_data, hotfix_paths, ab_dict):
        """
        处理热更数据
        """
        hotfix_data_dict = dict()
        hotfix_mapping = dict()
        if hotfix_data:
            # 转化切割数据
            hotfix_data_list = hotfix_data.split(",")
            for i in hotfix_data_list:
                key, val = i.split(":")
                hotfix_data_dict[key] = val
            # 映射 热更分支 与 对应 路径
            hotfix_paths = hotfix_paths.replace("/", os.path.sep).replace("\\", os.path.sep)
            hotfix_path_list = hotfix_paths.split(",")
            for k, v in hotfix_data_dict.items():
                for i in hotfix_path_list:
                    if ab_dict[k]["is_c"]:
                        # c目录
                        path = "assetbundles/c/{}/scene".format(ab_dict[k]["c_prefix"]).replace("/", os.path.sep).replace("\\", os.path.sep)
                    else:
                        path = "assetbundles/scene".replace("/", os.path.sep).replace("\\", os.path.sep)
                    if v in i and path in i:
                        hotfix_mapping[k] = i
        return hotfix_mapping


class StreamSceneResourceManager(ResourceManager):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.shequ_dir_name = "shequ_01"
        self.xuejingshequ_dir_name = "xuejingshequ"
        self.prefix = "{}/{}".format(scene_p4_path, self.shequ_dir_name)
        self.kind = "stream_scene"
        self.package_type = ""
        self.xuejing_prefix = "{}/{}".format(scene_p4_path, self.xuejingshequ_dir_name)

    def get_updates(self, depot_files: list):
        """
        获取符合条件的 Unity文件
        """
        xuejing_depot_files = self.get_update_files(self.xuejing_prefix)
        depot_files.extend(xuejing_depot_files)
        for file in depot_files:
            if not file.startswith(self.prefix) and not file.startswith(self.xuejing_prefix):
                continue
            for user in self.depot_user_dict.get(file):
                if user not in self.depot_user_list:
                    self.depot_user_list.append(user)
            # if self.depot_user_dict.get(file) and self.depot_user_dict[file] not in self.depot_user_list:
            #     self.depot_user_list.append(self.depot_user_dict[file])
            if file.endswith(".unity"):
                # 判断是雪景社区打包 还是普通社区打包, 如果两着都有更新的时候, 只打普通社区
                replace_path = file.replace(scene_p4_path, "")
                if replace_path.startswith("/{}/".format(self.shequ_dir_name)):
                    self.file_list.append(1)
                    self.package_type = self.shequ_dir_name
                    # 通过return 来控制, 社区和雪景社区都打包时, 优先打包社区
                    return
                else:
                    self.file_list.append(1)
                    self.package_type = self.xuejingshequ_dir_name

    def archive_and_dump_json(self) -> dict:
        json_result = {}
        if len(self.file_list) == 0:
            json_result["update"] = "false"
        else:
            json_result["update"] = "true"
            json_result["package_type"] = self.package_type
            json_result["changelist"] = self.latest_changelist
            send_user_list = list()
            for user in self.depot_user_list:
                if not user.endswith("@h3d.com.cn"):
                    send_user_list.append("{}@h3d.com.cn".format(user))
            json_result["send_user"] = ",".join(send_user_list)
        return json_result

    def submit_to_p4(self, file_paths, root, platform, workspace, desc, branch, package_type, stream_dir_name, source_changelist):
        """
        提交p4
        """
        rel_platform = "android" if platform == "android" else "iOS"
        resource_path = os.path.join(root, f"x5_mobile/mr/Resources/cs/{rel_platform}/assetbundles/{stream_dir_name}")
        branch_path = os.path.join(root, f"x5_mobile/mr/b/{branch}/cs/{rel_platform}/assetbundles/{stream_dir_name}")
        cdn_path = os.path.join(root, f"x5_mobile/mr/art_release/cs/{platform}/assetbundles/streaming_stage/{package_type}")

        changelist = self.submit_reconcile(workspace, desc, resource_path, branch_path, cdn_path)
        print(changelist)

        config.set("P4", self.kind, source_changelist)
        config.write(open("../config/config.ini", "w"))

    def submit_reconcile(self, workspace, desc, resource_path, branch_path, cdn_path):
        """
        使用 p4 reconcile 命令的方式 先判断变化，再提交到p4上
        """
        p4_srv = self.get_p4_tool(workspace)
        p4_srv.login()
        # 更改为使用reconcile命令判断目录下的文件的 增删改 的变动
        resource_path = resource_path.replace("/", os.path.sep).replace("\\", os.path.sep)
        branch_path = branch_path.replace("/", os.path.sep).replace("\\", os.path.sep)
        cdn_path = cdn_path.replace("/", os.path.sep).replace("\\", os.path.sep)
        p4_check_folder = os.path.join(resource_path, "...")
        p4_check_folder1 = os.path.join(branch_path, "...")
        p4_check_folder2 = os.path.join(cdn_path, "...")

        if p4_srv.reconcile(p4_check_folder) in [p4_error_code, p4_warning_code]:
            return ""
        elif p4_srv.reconcile(p4_check_folder1) in [p4_error_code, p4_warning_code]:
            return ""
        elif p4_srv.reconcile(p4_check_folder2) in [p4_error_code, p4_warning_code]:
            return ""
        else:
            result = p4_srv.submit("-d", desc)
            if result in [p4_error_code, p4_warning_code]:
                return ""
            else:
                return result[0]["change"]

    def submit(self, platform, workspace, files: list, desc, root) -> str:
        pass

    def distribute_source(self, source_path, dis_path):
        """
        分发
        """
        for dir_path, _, filenames in os.walk(source_path):
            dst_path_item = dir_path.replace(source_path, dis_path)
            if not os.path.exists(dst_path_item):
                os.makedirs(dst_path_item)
            for filename in filenames:
                file_path = os.path.join(dir_path, filename)
                os.chmod(file_path, stat.S_IWRITE)
                shutil.copy(file_path, os.path.join(dst_path_item, filename))

    def distribute(self, move_list, source_path, distribute_path_list, packages):
        """
        分发
        """
        for dis in distribute_path_list:
            if not os.path.exists(dis):
                os.makedirs(dis)
            self.distribute_source(source_path, dis)

    def distribute_hotfix(self, move_list, source_path, hotfix_data, hotfix_paths, packages):
        """
        分发热更
        """
        pass

    def check_move_source_exists(self, distribute_path_list, source_path):
        """
        校验 打包目录 以及 待分发的目录
        """
        # 检查原资源目录下是否有文件
        source_list = os.listdir(source_path)
        try:
            if "chunk" not in source_list or "scene" not in source_list:
                raise ValueError("请检查{}下是否ab资源完整".format(source_path))
            # 清空待分发的目录下资源
            for i in distribute_path_list:
                if os.path.exists(i) and not os.listdir(i):
                    shutil.rmtree(i)
        except Exception as exp:
            print(2222, exp)
            return False, str(exp), list()
        return True, "", list()
