# -*- coding: UTF-8 -*-
import unittest
import os
from unittest import TestCase
from resource_manager import CameraDistribResourceManager


class CameraDistribResourceManagerTest(TestCase):
    def setUp(self) -> None:
        super().setUp()
        self.mrg = CameraDistribResourceManager()

    def tearDown(self) -> None:
        del self.mrg
        return super().tearDown()

    def test_read_config_hotfix_table(self):
        p4_client = r'/home/<USER>/repos/p4'
        result = self.mrg.read_config_hotfix_table(p4_client)
        print(result)
        self.assertIsNotNone(result)

    def test__submit_map(self):
        files = 'assets/resources/art/camera/mv01_danren_c_kaichang_f_cam.FBX,assets/resources/art/camera/mv01_danren_c_zhanshi_f_cam.FBX,assets/resources/art/camera/luzhi_dongcidaci_f_shape4_cam.FBX,assets/resources/art/camera/luzhi_dongcidaci_m_shape4_cam.FBX,assets/resources/art/camera/luzhi_gaodingshow_f_shape4_cam.FBX'
        files = files.split(',')
        root = r'E:\test'
        files = self.mrg._format_files(files=files, root=root, platform='android')
        rule_path = r'C:\Users\<USER>\Downloads\special-camera-map.xlsx'
        result, resource = self.mrg._submit_map(files=files, path=rule_path)
        self.assertTrue(result)

if __name__ == '__main__':
    unittest.main()