# -*- coding: UTF-8 -*-
import os.path
import requests
import inspect
import re
import stat
import xlrd
import json
import os
import shutil
import logging

import click
import sys
from pathlib import Path

# 脚本环境初始化
if __name__ == '__main__' and __package__ is None:
    # DIR: pyframe-pipeline
    DIR = Path(os.path.abspath(__file__)).parent.parent.parent.parent.parent
    sys.path.append(str(DIR.resolve())) if DIR not in sys.path else None
    __package__ = 'project.x5m.art_package.scripts'

from . import config
from . import edition_manager
from . import resource_manager
from .cdn_tool import CDNUtils
from .edition_week_update_manager import WeekUpdateManager
from .ftp_tool import FtpUtils
from .h5_manager import H5Manager
from .shader_manager import ShaderManager
from .inst import cli, config as conf, p4_tool_mgr, git_tool_mgr, p4tool
from .p4_tool import P4Tool

@cli.command()
@click.option("--first_changelist", default=0, help="手动指定的p4的changelist")
def get_trunc_texture_updates(first_changelist):
    if first_changelist > 0:
        conf.set("P4", "first_changelist", str(first_changelist))
        conf.write(open("../config/config.ini", "w"))
        # if first_changelist > int(config['P4']['texture']):
        conf.set("P4", "texture", str(first_changelist))

    trunc_texture_mgr = resource_manager.TruncTextureResourceManager()
    trunc_texture_mgr.get_resource_updates()
    result = trunc_texture_mgr.archive_and_dump_json()
    print(result)


@cli.command()
@click.option("--branch", default="", help="//x5_mobile/mr/b下的不带后缀的版本号最大的分支")
@click.option("--first_changelist", default=0, help="手动指定的p4的changelist")
def get_branch_texture_updates(first_changelist, branch):
    if first_changelist > 0:
        conf.set("P4", "first_changelist", str(first_changelist))
        conf.write(open("../config/config.ini", "w"))
        # if first_changelist > int(config['P4']['texture']):
        conf.set("P4", "texture", str(first_changelist))

    branch_texture_mgr = resource_manager.BranchTextureResourceManager(branch_texture=config.branch_texture_path.format(branch), branch_c=config.branch_c_path.format(branch))
    branch_texture_mgr.get_resource_updates()
    result = branch_texture_mgr.archive_and_dump_json()
    print(result)


@cli.command()
@click.option("--branch", default="", help="//x5_mobile/mr/b下的不带后缀的版本号最大的分支")
@click.option("--tp", help="type 要查看哪个资源类型是否有更新")
@click.option("--first_changelist", default=0, help="手动指定的p4的changelist")
def get_branch_edition_updates(first_changelist, tp, branch):
    """
    获取版本资源的分支状态
    """
    switcher = {
        # 版本内的相机资源
        "edition_camera_branch": {"class_name": edition_manager.EditionCameraBranchManager, "p4_file_name": "camera"},
        # 版本内timeline
        "edition_timeline_branch": {"class_name": edition_manager.EditionTimelineBranchManager, "p4_file_name": "timeline"},
        # 版本内的特效资源分支
        "edition_effect_branch": {"class_name": edition_manager.EditionEffectBranchManager, "p4_file_name": "effect"},
        # 动作打包分支
        "edition_role/actions_branch": {"class_name": edition_manager.EditionActionsBranchManager, "p4_file_name": "role/actions"},
    }
    mach_dict = switcher.get(tp)
    if first_changelist > 0:
        conf.set("P4", tp, str(first_changelist))
        conf.write(open("../config/config.ini", "w"))
        config_edition_branch_name = "edition_{}_branch".format(mach_dict["p4_file_name"])
        if first_changelist > int(conf["P4"][config_edition_branch_name]):
            conf.set("P4", config_edition_branch_name, str(first_changelist))
    branch_mgr = mach_dict["class_name"](config.edition_p4_branch_path.format(branch, mach_dict["p4_file_name"]), branch=branch)
    branch_mgr.get_resource_updates()
    result = branch_mgr.archive_and_dump_json()
    print(json.dumps(result))


@cli.command()
@click.option("--branch", default="", help="//x5_mobile/mr/b下的不带后缀的版本号最大的分支")
@click.option("--tp", help="type 要查看哪个资源类型是否有更新")
@click.option("--first_changelist", default=0, help="手动指定的p4的changelist")
def get_branch_general_updates(first_changelist, tp, branch):
    """
    获取版本资源的分支状态
    """
    switcher = {
        "makeup": {"class_name": resource_manager.MakeUpManager, "p4_file_name": "makeup", "prefix": config.makeup_p4_branch_path.format(branch, "makeup")},
    }
    mach_dict = switcher.get(tp)
    if first_changelist > 0:
        conf.set("P4", "first_changelist", str(first_changelist))
        conf.write(open("../config/config.ini", "w"))
        config_branch_name = "{}_branch".format(mach_dict["p4_file_name"])
        if first_changelist > int(conf["P4"][config_branch_name]):
            conf.set("P4", config_branch_name, str(first_changelist))
    branch_mgr = mach_dict["class_name"](**mach_dict)
    branch_mgr.get_resource_updates()
    result = branch_mgr.archive_and_dump_json()
    print(json.dumps(result))


@cli.command()
@click.option("--tp", help="type 要查看哪个资源类型是否有更新")
@click.option("--first_changelist", default=0, help="手动指定的p4的changelist")
@click.option("--resources", default="", help="原始P4资源路径")
def get_updates(tp, first_changelist, resources):
    switcher = {
        "camera": lambda: resource_manager.CameraResourceManager(),
        "action": lambda: resource_manager.ActionResourceManager(),
        "bodypart": lambda: resource_manager.BodyPartResourceManager(),
        "chair": lambda: resource_manager.ChairResourceManager(),
        "chd": lambda: resource_manager.ChdResourceManager(),
        "effect": lambda: resource_manager.EffectResourceManager(),
        "house": lambda: resource_manager.HouseResourceManager(),
        # 房间资源分发打包
        "house_distrib": lambda: resource_manager.HouseDistribResourceManager(),
        "link": lambda: resource_manager.LinkResourceManager(),
        # 挂件资源分发打包
        "link_distrib": lambda: resource_manager.LinkDistribResourceManager(),
        "model": lambda: resource_manager.ModelResourceManager(),
        "npc": lambda: resource_manager.NpcResourceManager(),
        "beast": lambda: resource_manager.BeastResourcePackage(),
        "pet": lambda: resource_manager.PetResourceManager(),
        "texture": lambda: resource_manager.TextureResourceManager(),
        "timeline": lambda: resource_manager.TimelineResourceManager(),
        "camera_action": lambda: resource_manager.CamActionResourceManager(),
        "camera_effect": lambda: resource_manager.CamEffectResourceManager(),
        "edition_camera": lambda: edition_manager.EditionCameraManager(),
        "edition_bodypart": lambda: edition_manager.EditionBodyPartManager(),
        "edition_link": lambda: edition_manager.EditionLinkManager(),
        "link_unencrypt": lambda: resource_manager.LinkUnencryptResourceManager(),
        "edition_effect": lambda: edition_manager.EditionEffectResourceManager(),
        # 动作打包主支
        "actions_master": lambda: resource_manager.ActionsMasterManager(),
        "makeup": lambda: resource_manager.MakeUpManager(),
        "makeup_distrib": lambda: resource_manager.MakeUpDistribResourceManager(),
        "action_distrib": lambda: resource_manager.ActionDistribResourceManager(),
        "house_scene": lambda: resource_manager.HouseSceneResourceManager(),
        "scene": lambda: resource_manager.SceneResourceManager(),
        "stream_scene": lambda: resource_manager.StreamSceneResourceManager(),
        "camera_distrib": lambda: resource_manager.CameraDistribResourceManager(),
        "ingame_prefabs": lambda: resource_manager.MVInGameBaseResourceManager(p4_tool=p4tool),
        # 动作打包avatar
        "musicact_action_distrib": lambda: resource_manager.MusicActionDistribResourceManager(),
    }
    if first_changelist > 0:
        conf.set("P4", tp, str(first_changelist))
        logging.info(f"update first_changelist:{first_changelist} done")
    resource_mgr = switcher.get(tp)()
    resource_mgr.get_resource_updates()
    logging.info(f"need_package: {resource_mgr.file_list}")
    json_result = resource_mgr.archive_and_dump_json()
    print(json.dumps(json_result))

    full_job_name = os.getenv("JOB_NAME").replace("/", "%2F")
    job_name = os.getenv("JOB_NAME").split("/")[-1]
    jenkins_url = os.getenv("JENKINS_URL")
    build_number = os.getenv("BUILD_NUMBER")
    # 流水线状态信息
    pipeline_status = dict(
        category=config.kind_mapping.get(resource_mgr.kind),
        bundle_tool="JENKINS",
        url=f"{jenkins_url}blue/organizations/jenkins/{full_job_name}/detail/{job_name}/{build_number}/pipeline",
        build_id=build_number,
        min_change=int(conf["P4"][resource_mgr.kind]),
        max_change=int(resource_mgr.latest_changelist or 0),
        estimated_runtime=-1655204400,
        timestamp=-1654790400,
        status="#status#",
    )
    print(json.dumps(pipeline_status))

    # # timeline2号线获取资源提交人
    # if tp == "timeline":
    #     print(";".join(resource_mgr.timeline2_depot_user_list))


@cli.command()
@click.option("--tp", help="type 要查看哪个资源类型是否有更新")
def get_ab_bak_updates(tp):
    """
    获取打ab包备用流水线的 是否检查
    """
    switcher = {
        "texture": {
            "class_name": resource_manager.TextureResourceManager,
            "prefix": "//x5_mobile/mr/art_release_test/bak/art_src/texture",
            "c_prefix": "//x5_mobile/mr/art_release_test/bak/art_src/c",
        }
    }
    switch_dict = switcher.get(tp)
    cls = switch_dict.pop("class_name")
    resource_mgr = cls(**switch_dict)
    resource_mgr.get_resource_updates()
    json_result = resource_mgr.archive_and_dump_json()
    print(json.dumps(json_result))


@cli.command()
@click.option("--workspace", help="p4 workspace名称")
@click.option("--platform", help="android或者ios")
@click.option("--file_paths", default="", help="需要提交的文件列表,以逗号分割")
@click.option("--desc", help="提交时的描述")
@click.option("--root", help="client根路径")
@click.option("--tp", help="type 要查看哪个资源类型是否有更新")
@click.option("--bak_status", default="false", help="是否为分支")
@click.option("--changelist", help="打包资源的changelist, 用于计入config.ini")
def submit_files_to_p4(workspace, platform, file_paths, desc, root, tp, bak_status, changelist):
    switcher = {
        "camera": lambda: resource_manager.CameraResourceManager(),
        "action": lambda: resource_manager.ActionResourceManager(),
        "bodypart": lambda: resource_manager.BodyPartResourceManager(),
        "chair": lambda: resource_manager.ChairResourceManager(),
        "chd": lambda: resource_manager.ChdResourceManager(),
        "effect": lambda: resource_manager.EffectResourceManager(),
        "house": lambda: resource_manager.HouseResourceManager(),
        # 房间资源分发打包
        "house_distrib": lambda: resource_manager.HouseDistribResourceManager(),
        "link": lambda: resource_manager.LinkResourceManager(),
        # 挂件资源分发打包
        "link_distrib": lambda: resource_manager.LinkDistribResourceManager(),
        "model": lambda: resource_manager.ModelResourceManager(),
        "npc": lambda: resource_manager.NpcResourceManager(),
        "beast": lambda: resource_manager.BeastResourcePackage(),
        "pet": lambda: resource_manager.PetResourceManager(),
        "texture": lambda: resource_manager.TextureResourceManager(),
        "release_online_update_texture": lambda: resource_manager.OnlineUpdateTextureResourceManager(namespace="release"),
        "resource_online_update_texture": lambda: resource_manager.OnlineUpdateTextureResourceManager(namespace="resources"),
        "online_online_update_texture": lambda: resource_manager.OnlineUpdateTextureResourceManager(namespace="online"),
        "trunk_texture": lambda: resource_manager.TruncTextureResourceManager(),
        "branch_texture": lambda: resource_manager.BranchTextureResourceManager(),
        "edition_camera": lambda: edition_manager.EditionCameraManager(),
        "edition_bodypart": lambda: edition_manager.EditionBodyPartManager(),
        "edition_link": lambda: edition_manager.EditionLinkManager(),
        "camera_action": lambda: resource_manager.CamActionResourceManager(),
        "camera_effect": lambda: resource_manager.CamEffectResourceManager(),
        "link_unencrypt": lambda: resource_manager.LinkUnencryptResourceManager(),
        "timeline": lambda: resource_manager.TimelineResourceManager(),
        "edition_effect": lambda: edition_manager.EditionEffectResourceManager(),
        "makeup": lambda: resource_manager.MakeUpManager(),
        "makeup_distrib": lambda: resource_manager.MakeUpDistribResourceManager(),
        "ingame_prefabs": lambda: resource_manager.MVInGameBaseResourceManager(p4_tool=p4tool, config=conf),
        # 动作打包主支
        "actions_master": lambda: resource_manager.ActionsMasterManager(),
        # 动作分发
        "action_distrib": lambda: resource_manager.ActionDistribResourceManager(),
        # 镜头分发
        "camera_distrib": lambda: resource_manager.CameraDistribResourceManager(),
        # 动作avatar、weapon分发
        "musicact_action_distrib": lambda: resource_manager.MusicActionDistribResourceManager(),
    }
    resource_mgr = switcher.get(tp)()
    desc = desc.replace("|", "\n")
    # 对于ab打包的备用线, 提交时，使用另一个方法，因为备用线与正式线的资源路径不同，所以使用的workspace是不同了，彼此不会造成影响
    resource_mgr.submit_files_to_p4(file_paths, root, platform, workspace, desc, changelist, bak_line=False if bak_status == "false" else True)


@cli.command()
@click.option("--platform", help="android或者ios")
@click.option("--root", help="client根路径")
@click.option("--file_paths", help="待提交文件列表")
@click.option("--desc", help="提交时的描述")
@click.option("--tp", help="type")
def submit_related_file(root: str, platform: str, file_paths: str, desc: str, tp: str):
    switcher = {
        "link_distrib": lambda: resource_manager.LinkDistribResourceManager(),
        "bodypart": lambda: resource_manager.BodyPartResourceManager(),
    }
    mgr = switcher.get(tp)()
    mgr.submit_related_file(root, platform.lower(), file_paths, desc)


@cli.command()
@click.option("--workspace", help="p4 workspace名称")
@click.option("--platform", help="android或者ios")
@click.option("--root", help="client根路径")
@click.option("--view_env", help="p4 view", default="")
@click.option("--desc", help="提交时的描述")
@click.option("--view", help="p4 view", default="")
def submit_video_files(workspace, platform, root, view_env, desc, view=""):
    if view:
        view_list = view.split(";")
    else:
        view_list = os.getenv(view_env, "").split(";")

    if not view_list:
        return
    resource_mgr = resource_manager.TimelineResourceManager()
    resource_mgr.submit_video_files(workspace, platform, root, view_list, desc)


@cli.command()
@click.option("--workspace", help="p4 workspace名称")
@click.option("--platform", help="android或者ios")
@click.option("--desc", help="提交时的描述")
@click.option("--root", help="client根路径")
@click.option("--tp", help="type 要查看哪个资源类型是否有更新")
@click.option("--branch", help="type 要提交的分支")
@click.option("--changelist", help="原资源p4 changelist号")
@click.option("--file_paths", default="", help="需要提交的文件列表,以逗号分割")
def edition_branch_submit_to_p4(workspace, platform, branch, desc, changelist, root, tp, file_paths):
    """
    版本内的资源打包提交
    """
    switcher = {
        # 版本内的相机资源分支
        "edition_camera_branch": {"class_name": edition_manager.EditionCameraBranchManager, "p4_file_name": "camera"},
        "edition_timeline_branch": {"class_name": edition_manager.EditionTimelineBranchManager, "p4_file_name": "timeline"},
        "edition_effect_branch": {"class_name": edition_manager.EditionEffectBranchManager, "p4_file_name": "effect"},
        # 动作打包主支
        "edition_role/actions_branch": {"class_name": edition_manager.EditionActionsBranchManager, "p4_file_name": "role/actions"},
    }
    mach_dict = switcher.get(tp)
    branch_mgr = mach_dict["class_name"](config.edition_p4_branch_path.format(branch, mach_dict["p4_file_name"]), branch=branch)
    desc = desc.replace("|", "\n")
    branch_mgr.submit_to_p4_branch(branch, root, platform, workspace, desc, changelist, file_paths)


@cli.command()
@click.option("--workspace", help="p4 workspace名称")
@click.option("--platform", help="android或者ios")
@click.option("--desc", help="提交时的描述")
@click.option("--root", help="client根路径")
@click.option("--tp", help="type 要查看哪个资源类型是否有更新")
@click.option("--major_version", help="周更的主分支")
@click.option("--versions", help="待提交的周更分支")
@click.option("--source_changelist", help="原资源p4 changelist")
def edition_week_update_submit_to_p4(workspace, platform, desc, root, tp, major_version, versions, source_changelist):
    """
    版本内周更资源的打包
    """
    version_list = versions.split(";")
    if not version_list:
        raise ValueError("待提交的周更分支不能为空")
    switcher = {
        # 版本内的相机资源周更resources
        "edition_camera_resources": {"class_name": edition_manager.EditionCameraUpdateManager, "p4_file_name": "camera"},
        # 版本内的特效资源周更resources
        "edition_effect_resources": {"class_name": edition_manager.EditionEffectUpdateManager, "p4_file_name": "effect"},
        "edition_actions_resources": {"class_name": edition_manager.EditionActionsUpdateManager, "p4_file_name": "role/actions"},
    }
    mach_dict = switcher.get(tp)
    kind = f"edition_{mach_dict['p4_file_name']}_week_update"
    version_change_dict = dict()
    ab_package_max_change_list = "0"
    desc = desc.replace("|", "\n")
    for version in version_list:
        prefix = f"{config.online_update_path}/{major_version}/{version}/art_src/{mach_dict['p4_file_name']}"
        branch_mgr = mach_dict["class_name"](prefix, kind)
        change_list = branch_mgr.submit_resources(workspace, platform, desc, root, major_version, version)
        version_change_dict[version] = change_list
        if change_list > ab_package_max_change_list:
            ab_package_max_change_list = change_list
    conf.set("P4", kind, source_changelist)
    conf.write(open("../config/config.ini", "w"))
    print(json.dumps(version_change_dict))
    print(ab_package_max_change_list)


def find_latest_online_update_dir(online_update_major_version_path, subject_matter_version) -> list:
    """
    查找当前主版本号下 所有比周更目录大的周更版本号
    """
    latest_dir_name = []
    branch_num = str(subject_matter_version)[0:3]
    # 首先需要判断标的物版本号  是不是符合规范的
    if not subject_matter_version.endswith("000") or len(subject_matter_version) != (12 if int(branch_num) >= 504 else 11):
        raise Exception("周更版本号不符合规范")
    if not online_update_major_version_path.endswith("*"):
        online_update_major_version_path = os.path.join(online_update_major_version_path, "*").replace("\\", "/")
    dir_results = p4tool.dirs(online_update_major_version_path)
    if dir_results in [config.p4_error_code, config.p4_warning_code]:
        dir_results = []
    for res in dir_results:
        if os.path.split(res["dir"])[1] >= subject_matter_version:
            latest_dir_name.append(res["dir"])

    return latest_dir_name


@cli.command()
@click.option("--namespace", default="resources", help="需要查看p4上某个路径是否更新")
@click.option("--workspace", default="${p4Client}", help="需要查看p4上某个路径是否更新")
@click.option("--first_changelist", default=0, help="手动指定的p4的changelist")
def check_onlineupdate_texture_update(namespace, workspace, first_changelist):
    hotfix_session = conf["HOTFIX"]

    if first_changelist > 0:
        conf.set("P4", "first_changelist", str(first_changelist))
        conf.write(open("../config/config.ini", "w"))
        if first_changelist > int(conf["P4"]["texture"]):
            conf.set("P4", "texture", str(first_changelist))

    resource = True if namespace == "resources" else False
    mgr = WeekUpdateManager(hotfix_session, p4_tool_mgr, resource, git_tool_mgr)
    mgr.switch_version()
    mgr.to_config(conf)
    conf.write(open("../config/config.ini", "w"))
    weekly_resource_dir_name, _ = mgr.calculate_weekly_resource_dir()

    files = []
    json_result = {}
    version_list = []

    # # 2. 获取周更资源目录
    current_major_version_p4_path = os.path.join(config.online_update_path, mgr.major_version).replace("\\", "/")

    current_source_p4_dir_name = os.path.join(current_major_version_p4_path, weekly_resource_dir_name).replace("\\", "/")
    # 3. 根据环境会存在不同的处理方法
    # 3.1 如果是resource  那么需要将周更资源目录  以及在主版本号下的所有大于周更资源目录的路径都判断是否需要打包
    # 如果是resource 需要将所有的都找出来
    max_changelist = ""
    latest_paths = []

    if namespace == "resources":
        # 查找所有大于当前版本号的目录
        latest_paths = find_latest_online_update_dir(current_major_version_p4_path, weekly_resource_dir_name)
    # 3.2 如果不是resource  那么只需要判断当前周更资源目录是否需要打包就可以了
    else:
        latest_paths.append(current_source_p4_dir_name)
        if namespace == "release" and mgr.current_weekly_time == 2:
            json_result = {"update": "false"}
            print(json.dumps(json_result))
            return
        # p4上获取最新的changelist

    logging.info("latest_paths: {}".format(latest_paths))
    send_user_list = list()
    for path in latest_paths:
        version_list.append(os.path.split(path)[1])
        texture_path = os.path.join(path, "art_src/texture").replace("\\", "/")
        c_path = os.path.join(path, "art_src/c").replace("\\", "/")
        online_update_texture_mgr = resource_manager.OnlineUpdateTextureResourceManager(
            online_update_texture=texture_path, online_update_c=c_path, namespace=namespace
        )
        files.extend(online_update_texture_mgr.get_resource_updates())
        for user in online_update_texture_mgr.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                user_h3d = "{}@h3d.com.cn".format(user)
                if user_h3d not in send_user_list:
                    send_user_list.append(user_h3d)
        if online_update_texture_mgr.latest_changelist > max_changelist:
            max_changelist = online_update_texture_mgr.latest_changelist
    changelist = max_changelist
    texture_path_list = []
    view_list = []
    views = ""
    if len(files) == 0 or changelist <= conf["P4"]["first_texture_changelist"]:
        json_result["update"] = "false"
    else:
        for file in files:
            texture_path_list.append(file[2:])
            view_list.append("{0} //{1}{2}".format(file, workspace, file[1:]))
        versions = ";".join(version_list)
        views += ";".join(view_list)
        json_result["update"] = "true"
        json_result["major_version"] = mgr.major_version
        json_result["versions"] = versions
        json_result["views"] = views
        json_result["texture_paths"] = ",".join(texture_path_list)
        json_result["changelist"] = changelist
        json_result["send_user"] = ",".join(send_user_list)
    print(json.dumps(json_result))


@cli.command()
@click.option("--namespace", default="resources", help="检查周更是哪个线是否更新")
@click.option("--workspace", default="${p4Client}", help="需要查看p4上某个路径是否更新")
@click.option("--tp", help="type 要查看哪个资源类型是否有更新")
@click.option("--first_changelist", default=0, help="手动指定的p4的changelist")
def check_edition_online_update(namespace, workspace, tp, first_changelist):
    """
    检查 版本内周更 是否需要打包
    """
    hotfix_session = conf["HOTFIX"]
    kind = f"edition_{tp}_week_update"
    if first_changelist > 0 and first_changelist > int(conf["P4"][kind]):
        conf.set("P4", kind, str(first_changelist))

    resource = True if namespace == "resources" else False
    mgr = WeekUpdateManager(hotfix_session, p4_tool_mgr, resource, git_tool_mgr)
    mgr.switch_version()
    mgr.to_config(conf)
    conf.write(open("../config/config.ini", "w"))
    weekly_resource_dir_name, _ = mgr.calculate_weekly_resource_dir()

    files = []
    json_result = {}
    version_list = []

    # # 2. 获取周更资源目录
    current_major_version_p4_path = os.path.join(config.online_update_path, mgr.major_version).replace("\\", "/")

    current_source_p4_dir_name = os.path.join(current_major_version_p4_path, weekly_resource_dir_name).replace("\\", "/")
    # 3. 根据环境会存在不同的处理方法
    # 3.1 如果是resource  那么需要将周更资源目录  以及在主版本号下的所有大于周更资源目录的路径都判断是否需要打包
    # 如果是resource 需要将所有的都找出来
    max_changelist = ""
    latest_paths = []

    if namespace == "resources":
        # 查找所有大于当前版本号的目录
        latest_paths = find_latest_online_update_dir(current_major_version_p4_path, weekly_resource_dir_name)
    # 3.2 如果不是resource  那么只需要判断当前周更资源目录是否需要打包就可以了
    else:
        latest_paths.append(current_source_p4_dir_name)
        if namespace == "release" and mgr.current_weekly_time == 2:
            json_result = {"update": "false"}
            print(json.dumps(json_result))
            return
        # p4上获取最新的changelist
    switcher = {
        # 版本内的相机资源
        "camera": edition_manager.EditionCameraUpdateManager,
        # 版本内的特效资源
        "effect": edition_manager.EditionEffectUpdateManager,
        # 版本内的动作资源
        "role/actions": edition_manager.EditionActionsUpdateManager,
    }
    mgr_class = switcher.get(tp)
    send_user_list = list()
    for path in latest_paths:
        version_list.append(os.path.split(path)[1])
        source_path = os.path.join(path, f"art_src/{tp}").replace("\\", "/")
        source_mgr = mgr_class(prefix=source_path, kind=kind)
        files.extend(source_mgr.get_resource_updates())
        for user in source_mgr.depot_user_list:
            if not user.endswith("@h3d.com.cn"):
                user_h3d = "{}@h3d.com.cn".format(user)
                if user_h3d not in send_user_list:
                    send_user_list.append(user_h3d)
        if source_mgr.latest_changelist > max_changelist:
            max_changelist = source_mgr.latest_changelist
    changelist = max_changelist
    texture_path_list = []
    view_list = []
    views = ""
    # if len(files) == 0 or changelist <= config['P4']['first_texture_changelist']:
    if len(files) == 0:
        json_result["update"] = "false"
    else:
        for file in files:
            index = file.find("art_src")
            if index == -1:
                continue
            real_path = "assets/resources/art" + file[index + 7 :]
            texture_path_list.append(real_path)
            view_list.append("{0} //{1}{2}".format(file, workspace, f"/x5_mobile/mobile_dancer/arttrunk/client/{real_path}"))
        versions = ";".join(version_list)
        views += ";".join(view_list)
        json_result["update"] = "true"
        json_result["major_version"] = mgr.major_version
        json_result["versions"] = versions
        json_result["views"] = views
        json_result["source_paths"] = ",".join(texture_path_list)
        json_result["changelist"] = changelist
        json_result["send_user"] = ",".join(send_user_list)
    print(json.dumps(json_result))


@cli.command()
def get_mr_latest_branch():
    """
    获取p4路径//x5_mobile/mr/b/ 下最大的分支 目前支队形如4.04.0这样的分支进行判断
    """
    branch_dir_results = p4tool.dirs(os.path.join(config.branch_path, "*").replace("\\", "/"))
    max_branch = ""
    max_first = 0
    max_second = 0
    for res in branch_dir_results:
        # noinspection PyBroadException
        try:
            dir_name = res["dir"].split("/")[-1]
            elem = dir_name.split(".")
            if elem[2] != "0":
                continue
            first = int(elem[0])
            second = int(elem[1])
        except Exception:
            pass
        else:
            if first > max_first and second > max_second:
                first, second, max_branch = max_first, max_second, dir_name
            elif first == max_first and second > max_second:
                second, max_branch = max_second, dir_name

    print(max_branch)


@cli.command()
@click.option("--path", help="打包工具工程目录")
def upload_package_result(path):
    log_path = os.path.join(path, r"AssetBundleTool\AssetBundleToolLogs")
    paths = os.listdir(log_path)
    latest_time = ""
    latest_log_path = ""
    latest_filename = ""
    for p in paths:
        if not p.endswith(".html"):
            continue
        name, _ = os.path.splitext(p)
        if name > latest_time:
            latest_time = name
            latest_filename = p
            latest_log_path = p
    latest_log_path = os.path.join(log_path, latest_log_path)

    files = [("file", (latest_filename, open(latest_log_path, "rb"), "text/html"))]
    response = requests.post(conf["PACKAGE"]["url"], files=files)
    result = "false"
    result_id = ""
    if response.status_code == 200:
        try:
            ids = json.loads(response.text).get("result").get("id")
        except:
            pass
        else:
            result = "true"
            result_id = ids
    print(result_id)
    print(result)


@cli.command()
@click.option("--path", help="打包工具工程目录")
@click.option("--platform", help="平台")
def get_package_result(path, platform):
    log_path = os.path.join(path, r"AssetBundleTool\AssetBundleToolLogs")
    paths = os.listdir(log_path)
    latest_time = ""
    latest_filename = ""
    for p in paths:
        if not p.endswith(".html") and not p.endswith(".txt"):
            continue
        if p.startswith("backup"):
            continue
        name, _ = os.path.splitext(p)
        if name > latest_time:
            latest_time = name
            latest_filename = p

    logging.info(f"latest_filename: {latest_filename}")
    name_new = "backup_" + platform + "_" + latest_filename.replace(" ", "-").replace("：", "-")
    shutil.copyfile(log_path + "/" + latest_filename, log_path + "/" + name_new)
    print(name_new)


@cli.command()
@click.option("--path", help="打包工具工程目录")
@click.option("--platform", help="平台")
def get_and_modify_package_result(path, platform):
    log_path = os.path.join(path, r"AssetBundleTool\AssetBundleToolLogs")
    paths = os.listdir(log_path)
    latest_time = ""
    latest_filename = ""
    for p in paths:
        if not p.endswith(".html") and not p.endswith(".txt"):
            continue
        if p.startswith("backup"):
            continue
        name, _ = os.path.splitext(p)
        if name > latest_time:
            latest_time = name
            latest_filename = p

    logging.info(f"latest_filename: {latest_filename}")
    name_new = "backup_" + platform + "_" + latest_filename.replace(" ", "-").replace("：", "-")
    shutil.copyfile(log_path + "/" + latest_filename, log_path + "/" + name_new)

    with open(f"{path}/AssetBundleTool/large_ab.txt", "r", encoding="utf-8") as f:
        large_ab = f.readlines()

    stable = True
    if large_ab:
        stable = False
        html_template_a = '<p style="color:#000000">检测到以下资源大于1M</p>'
        html_template_b = '<p style="color:#000000">{}</p>'.format("_" * 95)
        with open(f"{log_path}/{name_new}", "rb+") as f:
            html_bytes = f.read()
            html_string = html_bytes.decode("utf-8")
            body, p_tags = html_string.split("<p", 1)
            body += html_template_a
            for ab in large_ab:
                body += f'<p style="color:red">{ab}</p>'

            body += html_template_b
            body += "<p"
            body += p_tags
            f.seek(0)
            f.write(body.encode("utf-8"))

    print(name_new)
    print(stable)


@cli.command()
@click.option("--path", help="打包工具工程目录")
@click.option("--namespace", help="检查周更是哪个线是否更新")
def get_cdn_hotfix_log(namespace, path):
    paths = os.listdir(path)
    latest_time = ""
    latest_filename = ""
    for p in paths:
        if not p.endswith(".txt"):
            continue
        if p.startswith("{}".format(namespace)):
            continue
        name, _ = os.path.splitext(p)
        if name > latest_time:
            latest_time = name
            latest_filename = p
    # name_new = latest_filename.replace(' ', '-').replace('：', '-') + '_' + namespace
    name_new = namespace + "_" + latest_filename
    os.chdir(path)
    os.rename(latest_filename, name_new)
    print(name_new)


# 无用
# @cli.command()
# def test_p4_login():
#     p4tool.p4.connect()
#     p4tool.login()
#     for index in range(10000):
#         print(f'login index:{index}')
#         p4tool.dirs('//x5_mobile/mr/onlineupdate/*')
#         print(f'login end:{index}')
#     p4tool.p4.disconnect()


def __filter_extend(success: list, fail: list) -> list:
    """
    在成功列表中过滤掉只有extend类型成功的id
    """
    pattern = re.compile(r"(\d+)_extend\d$")
    temp_list = []

    for i in success:
        ret = re.findall(pattern=pattern, string=i)
        if ret:
            true_success = ret[0]
            if true_success not in fail:
                temp_list.append(i)
        else:
            temp_list.append(i)
    logging.info(f"temp_list: {temp_list}")
    return temp_list


def __print_package_result(read_lines: list) -> (list, list):
    """
    根据报告匹配出成功和失败的ID信息,目前仅做了服装、饰品、TimeLine兼容测试,其他类型可能需要重新匹配
    特殊的，只能单独匹配的，单独写pattern，其他写在success或fail的通用pattern中
    """
    success_ids = []
    fail_ids = []
    success_pattern = re.compile(r"^\[info]\s+\d+\s.*?(\d+$|\d+_extend\d$)")
    body_part_fail_pattern = re.compile(r"^\[Error]\d+\s(\d+$)")
    fail_pattern = re.compile(r"^\[Error]\s+\d+\s.*?(\d+$|\d+_extend\d$)")
    timeline_fail_pattern = re.compile(r"^\[Error].*?(\d{6,}).*?")
    for readline in read_lines:
        suc = re.findall(success_pattern, readline)
        if suc:
            success_ids.extend(suc)

        body_fail = re.findall(body_part_fail_pattern, readline)
        if body_fail:
            fail_ids.extend(body_fail)

        fail = re.findall(fail_pattern, readline)
        if fail:
            fail_ids.extend(fail)

        time_fail = re.findall(timeline_fail_pattern, readline)
        if time_fail:
            fail_ids.extend(time_fail)

    # 将失败的从成功的列表中去除
    temp_list = []
    for success_id in success_ids:
        if success_id not in fail_ids:
            temp_list.append(success_id)

    # 将只有exten成功的id过滤掉
    final_success_ids = __filter_extend(temp_list, fail_ids)

    return list(set(final_success_ids)), list(set(fail_ids))


@cli.command()
@click.option("--path", help="打包工具工程目录")
def print_package_result(path):
    log_path = os.path.join(path, r"AssetBundleTool\AssetBundleToolLogs")
    paths = os.listdir(log_path)
    latest_time = ""
    latest_log_path = ""
    for p in paths:
        if not p.endswith(".txt"):
            continue
        name, _ = os.path.splitext(p)
        if name > latest_time:
            latest_time = name
            latest_log_path = p
    latest_log_path = os.path.join(log_path, latest_log_path)
    json_result = {}
    with open(latest_log_path, "r", encoding="UTF-8") as f:
        total_count = 0
        succ_count = 0
        failed_count = 0
        error_count = 0
        all_lines_in_file = f.readlines()
        for index in range(len(all_lines_in_file)):
            if all_lines_in_file[index].startswith("[Info]Build Input Count"):
                first_index = all_lines_in_file[index].find("[", 6)
                second_index = all_lines_in_file[index].find("]", 6)
                total_count += int(all_lines_in_file[index][first_index + 1 : second_index])
            if all_lines_in_file[index].startswith("[Info]Build Secceed  Count="):
                first_index = all_lines_in_file[index].find("[", 6)
                second_index = all_lines_in_file[index].find("]", 6)
                succ_num = int(all_lines_in_file[index][first_index + 1 : second_index])
                succ_count += succ_num
                while succ_num > 0:
                    index += 1
                    succ_num -= 1
                    if all_lines_in_file[index][len("[info]") : len(all_lines_in_file[index]) - 2].find("global_dependeencies") != -1:
                        succ_count -= 1

            if all_lines_in_file[index].startswith("[Info]Build Failed Count"):
                first_index = all_lines_in_file[index].find("==", 6)
                second_index = all_lines_in_file[index].find("]", 6)
                failed_num = int(all_lines_in_file[index][first_index + 2 : second_index])
                failed_count += failed_num
            if all_lines_in_file[index].startswith("[Error]"):
                error_count += 1

        # 根据打包报告解析出成功和失败ID信息
        success_ids, fail_ids = __print_package_result(all_lines_in_file)

    json_result["upload_p4"] = "true"
    if succ_count == 0:
        json_result["upload_p4"] = "false"
    json_result["total_count"] = total_count
    json_result["succ_count"] = succ_count
    json_result["failed_count"] = failed_count
    json_result["error_count"] = error_count
    json_result["success_ids"] = ",".join(success_ids)
    json_result["fail_ids"] = ",".join(fail_ids)
    print(json.dumps(json_result))


@cli.command()
@click.option("--path", help="分发工具日志目录")
def get_distribute_result(path):
    """
    统计分发工具生成的日志信息
    """
    json_result = {"distribute_result": "false", "distribute_log_name": ""}
    if os.path.exists(path):
        paths = os.listdir(path)
        latest_time = ""
        latest_log_path = ""
        for p in paths:
            if not p.endswith(".log.txt"):
                continue
            name = p[: p.index(".log.txt")]
            if name > latest_time:
                latest_time = name
                latest_log_path = p
        json_result["distribute_log_name"] = latest_log_path
        latest_log_path = os.path.join(path, latest_log_path)
        with open(latest_log_path, "r", encoding="UTF-8") as fp, open("{}.html".format(latest_log_path), "w", encoding="utf-8") as wfp:
            wfp.write("""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>{}</title></head><body>""".format(latest_time))
            lines = fp.readlines()
            for line in lines:
                if line.strip().startswith("[Info]拷贝文件数量"):
                    count_result = re.search(r"^\[Info\]拷贝文件数量:(\d+)", line)
                    if count_result:
                        json_result["copy_total"] = count_result.group(1)
                elif line.strip() == "[Info]分发成功":
                    json_result["distribute_result"] = "true"
                wfp.write("<p>{}</p>".format(line))
            wfp.write("</body></html>")

    print(json.dumps(json_result))


@cli.command()
@click.option("--path", help="时间戳文件目录")
def get_latest_timestamp_package(path):
    """
    获取最新的时间戳目录
    """
    latest_package_name = ""
    if os.path.exists(path):
        package_list = os.listdir(path)
        for package in package_list:
            if os.path.isfile(os.path.join(path, package)):
                continue
            if package == "whole":
                continue
            if package > latest_package_name:
                latest_package_name = package
    print(latest_package_name)


@cli.command()
@click.option("--path", help="jenkins workspace目录")
@click.option("--package_name", help="时间目录名称")
def package_store_trans_log(path, package_name):
    """
    将package 存入日志中
    """
    if not os.path.exists(path):
        raise ValueError("目录不存在")
    translog_path = os.path.join(path, "translog.log")
    if not os.path.exists(translog_path):
        # 不存在创建  由于windows上python的os模块无法使用 os.mknod创建文件，所以使用打开文件的方式创建文件
        with open(translog_path, mode="a+"):
            pass
    # 文件赋权
    os.chmod(translog_path, stat.S_IWRITE)
    # 开始写入日志
    with open(translog_path, mode="a+", encoding="utf-8") as fp:
        fp.write("{}\n".format(package_name))


@cli.command()
@click.option("--workspace", help="p4 workspace名称")
@click.option("--desc", help="提交时的描述")
@click.option("--submit_path", help="p4 workspace对应到本地的映射提交路径")
def package_submit_to_p4(workspace, desc, submit_path):
    """
    提交指定目录下的P4文件
    """
    desc = desc.replace("|", "\n")
    p4changelist = edition_manager.SubmitManager().submit_reconcile(workspace, desc, submit_path)
    print(p4changelist)


@cli.command()
@click.option("--path", help="pack/levels本地目录")
@click.option("--package_names", help="时间目录名称")
@click.option("--cdn_type", help="cdn的类型, 内网：lan, 外网：public")
def upload_files_to_cdn(path, package_names, cdn_type):
    """
    上传到cdn
    """
    if cdn_type not in ["lan", "public"]:
        raise ValueError("cdn的类型错误")

    logging.info("path: {}".format(path))
    path = path.replace("/", os.path.sep).replace("\\", os.path.sep)
    total, success_total, failed_total = 0, 0, 0
    failed_file_names = list()
    package_name_list = package_names.split(",")
    package_name_list = [i for i in package_name_list if i]
    for package_name in package_name_list:
        if os.path.exists(os.path.join(path, package_name)):
            files_list = os.listdir(os.path.join(path, package_name))
            for item in files_list:
                file_path = os.path.join(path, package_name, item)
                if os.path.isfile(file_path):
                    total += 1
                    # 上传CDN
                    with open(file_path, mode="rb") as fp:
                        files = fp.read()
                    if cdn_type == "lan":
                        result, error = CDNUtils().upload_lan_cdn(files, item)
                        logging.info("result: {}, error: {}".format(result, error))
                    else:
                        result, _ = CDNUtils().upload_public_cdn(files, item)
                    if result:
                        success_total += 1
                    else:
                        failed_total += 1
                        failed_file_names.append(item)
    json_result = {
        "upload_total": total,
        "upload_success_total": success_total,
        "upload_failed_total": failed_total,
        "failed_names": ",".join(failed_file_names),
    }
    print(json.dumps(json_result))


@cli.command()
@click.option("--path", help="上传文件本地目录")
@click.option("--root_path", default="/version_test/music_config", help="上传ftp的路径目录")
def upload_files_to_ftp(path, root_path):
    """
    上传到ftp
    """
    json_result = {"upload_ftp": "false"}
    path = path.replace("/", os.path.sep).replace("\\", os.path.sep)
    if os.path.exists(path):
        fu = FtpUtils(root_path=root_path)
        fu.upload_package_files(path)
        json_result["upload_ftp"] = "true"
    print(json.dumps(json_result))


@cli.command()
@click.option("--path", help="pack/levels本地目录")
def check_upload_cdn(path):
    """
    检查是否需要上传cdn，同时返回需要上传的时间目录，多个之间使用逗号分割
    """
    json_result = {"upload_cdn": "false"}
    path = path.replace("/", os.path.sep).replace("\\", os.path.sep)
    translog_path = os.path.join(path, "translog.log")
    if os.path.exists(translog_path) and os.path.isfile(translog_path):
        with open(translog_path, mode="r", encoding="utf-8") as fp:
            lines = fp.readlines()
            lines = list(set([i.strip() for i in lines if i.strip()]))
            if lines:
                json_result["upload_cdn"] = "true"
                json_result["package_names"] = ",".join(lines)
    print(json.dumps(json_result))


@cli.command()
@click.option("--path", help="pack/levels本地目录")
def delete_trans_log(path):
    """
    删除 translog日志
    """
    translog_path = os.path.join(path, "translog.log")
    if os.path.exists(translog_path) and os.path.isfile(translog_path):
        os.chmod(translog_path, stat.S_IWRITE)
        os.remove(translog_path)


@cli.command()
@click.option("--workspace", help="jenkins workspace")
def move_timeline_resources(workspace):
    """
    移动timeline流水线的层级目录
    """
    resource_path = os.path.join(workspace, "resources/art_src/timeline")
    resource_path = resource_path.replace("/", os.path.sep)
    dst_path = os.path.join(workspace, "x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/timeline")
    dst_path = dst_path.replace("/", os.path.sep)
    if not os.path.exists(dst_path):
        os.makedirs(dst_path)
    first_package_list = os.listdir(resource_path)
    for i in first_package_list:
        package_path = os.path.join(resource_path, i)
        if os.path.isdir(package_path):
            # 移动全是字母的目录下文件
            if i.isalpha():
                package_list = os.listdir(package_path)
                for item in package_list:
                    source_path = os.path.join(package_path, item)
                    for dir_path, _, filenames in os.walk(source_path):
                        dst_path_item = dir_path.replace(package_path, dst_path)
                        if not os.path.exists(dst_path_item):
                            os.makedirs(dst_path_item)
                        for filename in filenames:
                            file_path = os.path.join(dir_path, filename)
                            os.chmod(file_path, stat.S_IWRITE)
                            shutil.copy(file_path, os.path.join(dst_path_item, filename))
        else:
            # 不是文件夹的直接移动
            os.chmod(package_path, stat.S_IWRITE)
            shutil.copyfile(package_path, os.path.join(dst_path, i))


@cli.command()
@click.option("--version_name", help="shader版本号")
@click.option("--git_commit_check", default="false", help="是否开启git提交")
@click.option("--changelist", help="p4 changelist号")
def get_shader_updates(version_name, git_commit_check, changelist):
    """
    校验shader当前版本号是否存在，是否需要更新
    """
    if changelist:
        result = ShaderManager().get_version_by_changelist(changelist)
    else:
        result = ShaderManager().get_updates(version_name, True if git_commit_check == "true" else False)
    print(json.dumps(result))


@cli.command()
@click.option("--workspace", help="p4 workspace名称")
@click.option("--desc", help="提交时的描述")
@click.option("--submit_path", help="p4 workspace对应到本地的映射提交路径")
@click.option("--version_name", help="shader版本号")
@click.option("--pipeline_id", help="pipeline id")
def shader_submit_to_p4(workspace, desc, submit_path, version_name, pipeline_id):
    """
    提交指定目录下的P4文件
    """
    desc = desc.replace("|", "\n")
    p4changelist = edition_manager.SubmitManager().submit_reconcile(workspace, desc, submit_path, use_new_p4=True)
    if p4changelist == "0":
        exit(-1)

    print(p4changelist)
    # config.set('GIT', version_name, pipeline_id)
    # config.write(open('../config/config.ini', "w"))


@cli.command()
@click.option("--file_path", help="shader资源目录")
@click.option("--version_name", help="shader版本号")
@click.option("--filename", help="shader压缩文件名")
def unzip_shader_file(file_path, version_name, filename):
    """
    解压shader文件
    """
    file_path = file_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    shader_file_path = os.path.join(file_path, version_name, filename)
    if os.path.exists(shader_file_path) and os.path.isfile(shader_file_path):
        ShaderManager().unzip(shader_file_path, os.path.join(file_path, version_name))


@cli.command()
@click.option("--file_path", help="shader解压后文件目录")
def check_unzip_shader_file(file_path):
    """
    校验解压shader文件
    """
    json_result = {"check_result": "false", "check_msg": ""}
    file_path = file_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    shader_md5_file_path = os.path.join(file_path, "shader.md5")
    if os.path.exists(shader_md5_file_path) and os.path.isfile(shader_md5_file_path):
        check_result, check_msg = ShaderManager().check_unzip_file_valid(file_path, shader_md5_file_path)
        json_result["check_result"] = "true" if check_result else "false"
        json_result["check_msg"] = check_msg
    print(json.dumps(json_result))


@cli.command()
@click.option("--filepath", help="文件路径")
def read_file(filepath):
    """
    读取文件
    """ ""
    content = ""
    filepath = filepath.replace("/", os.path.sep).replace("\\", os.path.sep)
    if os.path.exists(filepath) and os.path.isfile(filepath):
        with open(filepath, mode="r", encoding="utf-8") as fp:
            content = fp.read()
            content = content.strip()
    print(content)


@cli.command()
@click.option("--project_id", help="工程id值")
@click.option("--page_num", default=1, help="git 分支的分页数, 第几页")
@click.option("--private_token", default="********************", help="gitlab api token值")
def get_git_valid_branch_name(project_id, page_num, private_token):
    """
    获取工程下符合条件的分支名称
    """
    json_result = {"result": "false", "error_msg": "", "branches_name": ""}
    gitlab_host = "https://x5mobile-gitlab.h3d.com.cn/"
    json_data = []
    try:
        while True:
            request_url = "{}api/v4/projects/{}/repository/branches?per_page=100&page={}&private_token={}".format(
                gitlab_host, project_id, page_num, private_token
            )
            response = requests.get(request_url)
            response_json_data = response.json()
            # logging.info(f"request_url: {request_url}")

            # if 100 >= len(response_json_data) > 0:
            # json_data = response_json_data
            json_data += response_json_data
            if len(response_json_data) < 100:
                break

            page_num += 1

    except Exception as exp:
        json_result["error_msg"] = str(exp)
    else:
        # logging.info(f"json_data: {json_data}")
        if response.status_code == 200:
            branches_name = list()
            data_dict = dict()
            version_list = list()
            for item in json_data:
                # ret = re.search(r"^(\d.*?\.\d.*?\.\d).*", item.get("name", ""))
                ret = re.search(r"^(\d.*?\.\d.*?\.\d+)$", item.get("name", ""))
                if ret:
                    aim_version = ret.group(1)
                    if aim_version in data_dict:
                        data_dict[aim_version].append(item["name"])
                    else:
                        data_dict[aim_version] = [item["name"]]
                        version_list.append(aim_version)
            if "4.14.0" in version_list:
                version_list.remove("4.14.0")
            # 获取最高版本号
            version_list.sort(reverse=True)
            if version_list:
                latest_version = version_list[0]
                branches_name = data_dict[latest_version]
            branches_name.append("master")
            json_result["result"] = "true"
            json_result["branches_name"] = ",".join(branches_name)
        else:
            json_result["error_msg"] = json_data.get("message", "")

    print(json.dumps(json_result))


@cli.command()
@click.option("--version_name", help="shader版本号or 大版本号")
@click.option("--action", default="get", help="get or check")
def check_or_get_shader_version(version_name, action):
    """
    校验 或者 获取shader值
    """ ""
    json_result = {"result": "false", "version_name": "", "submit_user": ""}
    if action == "get":
        result, version_name, submit_user = ShaderManager().get_latest_version(version_name)
        json_result["version_name"] = version_name
    else:
        result, submit_user = ShaderManager().check_version(version_name)
        json_result["version_name"] = version_name
    json_result["result"] = "true" if result else "false"
    json_result["submit_user"] = submit_user
    print(json.dumps(json_result))


@cli.command()
@click.option("--version_name", help="传入版本号")
@click.option("--filepath", help="文件路径")
def get_shader_commit_status(filepath, version_name):
    """
    // 如果传入了版本号, 则与各个分支进行对比的shader.version中的大版本号对比,
    // 如果不一致，则获取符合条件的版本号, 如果一致，则使用该版本号
    // 如果没有传入版本号，则直接获取符合自己的版本号

    """
    json_result = {"commit_status": "false", "shader_version": ""}
    version_list = version_name.split(".")
    main_version = version_list[0]
    filepath = filepath.replace("/", os.path.sep).replace("\\", os.path.sep)
    if os.path.exists(filepath) and os.path.isfile(filepath):
        with open(filepath, mode="r", encoding="utf-8") as fp:
            content = fp.read()
            content = content.strip()
        if content:
            # 校验shader.version值是否合法
            try:
                int(content)
                if not content.isdigit():
                    raise ValueError("传入的值不合法")
            except:
                print(json.dumps(json_result))
                return
            if main_version:
                # 有传入值
                if main_version == content:
                    print("main_version==content, main_version={}".format(main_version))
                    json_result["commit_status"] = "true"
                    json_result["shader_version"] = version_name
                elif main_version > content:
                    print("main_version>content, main_version={}, content={}".format(main_version, content))
                    result, lat_version_name, _ = ShaderManager().get_latest_version(content)
                    if result:
                        json_result["commit_status"] = "true"
                        json_result["shader_version"] = lat_version_name
            else:
                print("main_version is None")
                # 无传入值
                result, lat_version_name, _ = ShaderManager().get_latest_version(content)
                if result:
                    json_result["commit_status"] = "true"
                    json_result["shader_version"] = lat_version_name
    print(json.dumps(json_result))


@cli.command()
@click.option("--tp", help="类型")
def get_h5_latest_version(tp):
    """
    获取最新的H5版本号
    """
    if tp == "p4":
        print(H5Manager().get_latest_version())
    elif tp == "ftp":
        print(json.dumps(H5Manager().get_ftp_latest_version()))


@cli.command()
@click.option("--source_path", help="h5资源目录")
@click.option("--config_file_path", help="资源索引配置表文件路径")
@click.option("--first_version", help="存放到ftp的首位版本号")
@click.option("--second_version", help="存放到ftp的第二位版本号")
@click.option("--package_path", help="新生成文件目录的路径")
def get_h5_inc_package(source_path, config_file_path, first_version, second_version, package_path):
    """
    获取h5增量包
    """
    source_path = source_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    config_file_path = config_file_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    package_path = package_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    result = H5Manager().get_h5_inc_package(source_path, config_file_path, first_version, second_version, package_path)
    print(json.dumps(result))


@cli.command()
@click.option("--source_path", help="h5原资源文件本地目录")
@click.option("--source_zip_path", help="h5增量zip包文件目录")
@click.option("--config_path", help="h5配置文件目录")
@click.option("--h5_version", help="h5版本名称")
def upload_h5_to_ftp(source_path, source_zip_path, config_path, h5_version):
    """
    上传到ftp
    """
    source_path = source_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    source_zip_path = source_zip_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    config_path = config_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    json_result = H5Manager().upload_h5_ftp(source_path, source_zip_path, config_path, h5_version)
    print(json.dumps(json_result))


@cli.command()
@click.option("--version", help="h5版本名称")
def check_input_h5version(version):
    """
    校验输入的 h5 版本
    """
    json_result = H5Manager().check_version_exists(version)
    print(json.dumps(json_result))


@cli.command()
@click.option("--version", help="h5版本名称")
@click.option("--path", help="拉取到指定目录")
def get_h5_source_from_ftp(version, path):
    """
    拉取指定版本的资源包到指定目录
    """
    path = path.replace("/", os.path.sep).replace("\\", os.path.sep)
    H5Manager().get_source_from_ftp(version, path)


@cli.command()
@click.option("--branch", help="分支版本号")
@click.option("--file_path", help="拉取到指定目录")
def upload_to_public_ftp(file_path, branch):
    """
    上传到腾讯服务ftp
    """
    file_path = file_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    json_result = {"upload_result": "false", "upload_msg": ""}
    if os.path.exists(file_path) and os.path.isfile(file_path):
        try:
            H5Manager().upload_tencent_ftp(file_path, branch)
        except Exception as exp:
            json_result["upload_msg"] = "上传外网FTP失败, 失败信息:{}".format(str(exp))
        else:
            json_result["upload_result"] = "true"
    else:
        json_result["upload_msg"] = "{}文件不存在或者不是一个文件".format(file_path)
    print(json.dumps(json_result))


@cli.command()
@click.option("--source_path", help="分发的原资源路径")
@click.option("--distribute_paths", help="分发的终点路径,多个使用逗号分割")
@click.option("--packages", help="要分发的资源")
@click.option("--hotfix_paths", help="热更的p4 views")
@click.option("--hotfix_data", help="热更的p4 views")
@click.option("--special_paths", default="", help="特殊分支的 path路径")
@click.option("--special_data", default="", help="特殊分支p4 views")
@click.option("--tp", help="类型")
def distribute_scene_source(source_path, distribute_paths, packages, hotfix_paths, hotfix_data, special_paths, special_data, tp):
    """
    分发资源
    将资源复制到对应目录下
    """
    switcher = {
        "house_scene": lambda: resource_manager.HouseSceneResourceManager(),
        "scene": lambda: resource_manager.SceneResourceManager(),
        "stream_scene": lambda: resource_manager.StreamSceneResourceManager(),
    }
    mgr = switcher.get(tp)()
    json_result = {"result": "false", "msg": ""}
    source_path = source_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    distribute_paths = distribute_paths.replace("/", os.path.sep).replace("\\", os.path.sep)
    distribute_path_list = distribute_paths.split(",")
    if not os.path.exists(source_path):
        json_result["msg"] = "资源源路径不存在"
    else:
        if tp == "stream_scene":
            check_result, msg, move_list = mgr.check_move_source_exists(distribute_path_list, source_path)
        else:
            # 处理packages
            check_result, msg, move_list = resource_manager.SceneResourceManager().check_move_source_exists(packages, source_path)
        if not check_result:
            json_result["msg"] = str(msg)
            print(json.dumps(json_result))
            return
        # 如果是普通场景，尝试分发特殊分支
        if tp == "scene":
            # 分发特殊分支
            mgr.distribute_special_branch(move_list, source_path, special_data, special_paths, packages)
        # 分发
        mgr.distribute(move_list, source_path, distribute_path_list, packages)
        # 分发热更
        mgr.distribute_hotfix(move_list, source_path, hotfix_data, hotfix_paths, packages)
        json_result["result"] = "true"
        json_result["distribute_abs"] = ",".join(move_list)
    print(json.dumps(json_result))


@cli.command()
@click.option("--special_branch_config_path", help="分发的原资源路径")
@click.option("--tp", help="资源类型")
@click.option("--platform", help="平台")
@click.option("--workspace", help="工作空间")
@click.option("--p4workspace", help="p4工作空间")
@click.option("--packages", help="要分发的资源")
def check_distribute_special_branch(special_branch_config_path, tp, platform, workspace, packages, p4workspace):
    """
    检查是否分发特殊分支
    """
    switcher = {
        "scene": lambda: resource_manager.SceneResourceManager(),
    }
    json_result = {"check_result": "false", "msg": ""}
    special_branch_config_path = special_branch_config_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    if os.path.exists(special_branch_config_path):
        os.chmod(special_branch_config_path, stat.S_IWRITE)
        data_list = list()
        data_dict = dict()
        wb = xlrd.open_workbook(special_branch_config_path)
        table = wb.sheets()[0]
        rows = table.nrows
        title_data = table.row_values(0, 0, 2)
        if "name" in title_data and "path" in title_data:
            for i in range(1, rows):
                temp = {}
                config_data = table.row_values(i, 0, 2)
                # 去除两边空格
                for i, v in enumerate(config_data):
                    if isinstance(v, str):
                        config_data[i] = v.strip()
                temp.update({title_data[0]: config_data[0]})
                temp.update({title_data[1]: config_data[1]})
                data_list.append(temp)
            package_list = packages.split(",")
            ab_list = list()
            for i in package_list:
                filename = i.rsplit("/", maxsplit=1)[-1]
                file_prefix = filename.replace(".unity", "")
                ab_list.append(file_prefix)
            version_list = list()
            for item in data_list:
                # 处理 excel name 列 为数字时，excel会读取为一个小数
                if "name" in item:
                    try:
                        item["name"] = str(int(item["name"]))
                    except:
                        item["name"] = str(item["name"])
                if item["name"] in ab_list:
                    item["path"] = "{}".format(str(item["path"]))
                    data_dict[item["name"]] = str(item["path"])
                    if str(item["path"]) not in version_list:
                        version_list.append(str(item["path"]))
            if data_dict:
                data_string_list = list()
                for key, val in data_dict.items():
                    data_string_list.append("{}:{}".format(key, val))
                json_result["check_result"] = "true"
                json_result["data_dict"] = ",".join(data_string_list)
                json_result["special_branch_p4_views"], json_result["special_branch_local_paths"] = switcher.get(tp)().get_special_branch_views(
                    p4workspace, workspace, platform, packages, data_dict
                )
        else:
            json_result["msg"] = "excel表头不合法"
    else:
        json_result["msg"] = "excel文件不存在"
    print(json.dumps(json_result))


@cli.command()
@click.option("--hotfix_config_path", help="分发的原资源路径")
@click.option("--tp", help="资源类型")
@click.option("--platform", help="平台")
@click.option("--workspace", help="工作空间")
@click.option("--p4workspace", help="p4工作空间")
@click.option("--packages", help="要分发的资源")
def check_distribute_hotfix(hotfix_config_path, tp, platform, workspace, packages, p4workspace):
    """
    检查是否分发热更
    """
    switcher = {
        "house_scene": lambda: resource_manager.HouseSceneResourceManager(),
        "scene": lambda: resource_manager.SceneResourceManager(),
    }
    json_result = {"check_result": "false", "msg": ""}
    hotfix_config_path = hotfix_config_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    if os.path.exists(hotfix_config_path):
        os.chmod(hotfix_config_path, stat.S_IWRITE)
        data_list = list()
        data_dict = dict()
        wb = xlrd.open_workbook(hotfix_config_path)
        table = wb.sheets()[0]
        rows = table.nrows
        title_data = table.row_values(0, 0, 2)
        if "name" in title_data and "path" in title_data:
            for i in range(1, rows):
                temp = {}
                config_data = table.row_values(i, 0, 2)
                # 去除两边空格
                for i, v in enumerate(config_data):
                    if isinstance(v, str):
                        config_data[i] = v.strip()
                temp.update({title_data[0]: config_data[0]})
                temp.update({title_data[1]: config_data[1]})
                data_list.append(temp)
            package_list = packages.split(",")
            ab_list = list()
            for i in package_list:
                filename = i.rsplit("/", maxsplit=1)[-1]
                file_prefix = filename.replace(".unity", "")
                ab_list.append(file_prefix)
            version_list = list()
            for item in data_list:
                # 处理 excel name 列 为数字时，excel会读取为一个小数
                if "name" in item:
                    try:
                        item["name"] = str(int(item["name"]))
                    except:
                        item["name"] = str(item["name"])
                if item["name"] in ab_list:
                    item["path"] = "{}000".format(str(item["path"]))
                    data_dict[item["name"]] = str(item["path"])
                    if str(item["path"]) not in version_list:
                        version_list.append(str(item["path"]))
            if data_dict:
                data_string_list = list()
                for key, val in data_dict.items():
                    data_string_list.append("{}:{}".format(key, val))
                json_result["check_result"] = "true"
                json_result["data_dict"] = ",".join(data_string_list)
                json_result["hotfix_p4_views"], json_result["hotfix_local_paths"] = switcher.get(tp)().get_hotfix_p4_views(
                    p4workspace, workspace, version_list, platform, packages, data_dict
                )
        else:
            json_result["msg"] = "excel表头不合法"
    else:
        json_result["msg"] = "excel文件不存在"
    print(json.dumps(json_result))


@cli.command()
@click.option("--workspace", help="p4 workspace名称")
@click.option("--platform", help="android或者ios")
@click.option("--file_paths", default="", help="需要提交的文件列表,以逗号分割")
@click.option("--desc", help="提交时的描述")
@click.option("--root", help="client根路径")
@click.option("--tp", help="type 要查看哪个资源类型是否有更新")
@click.option("--branch", help="分支")
@click.option("--hotfix_data", help="热更数据 or 流式场景的场景类型")
@click.option("--hotfix_workspace", help="热更p4 workspace or 流式场景的 streaming dir name")
@click.option("--special_data", default="", help="特殊分支p4 views")
@click.option("--special_workspace", default="", help="特殊分支的 p4 workspace")
@click.option("--changelist", help="原资源的changelist")
def scene_submit_files_to_p4(
    workspace, platform, file_paths, desc, root, tp, branch, hotfix_data, hotfix_workspace, special_data, special_workspace, changelist
):
    switcher = {
        "house_scene": lambda: resource_manager.HouseSceneResourceManager(),
        "scene": lambda: resource_manager.SceneResourceManager(),
        "stream_scene": lambda: resource_manager.StreamSceneResourceManager(),
    }
    resource_mgr = switcher.get(tp)()
    desc = desc.replace("|", "\n")
    resource_mgr.submit_to_p4(
        file_paths, root, platform, workspace, desc, branch, hotfix_data, hotfix_workspace, special_data, special_workspace, changelist
    )


@cli.command()
@click.option("--source_path", help="分发的原资源路径")
@click.option("--distribute_paths", help="分发的终点路径,多个使用逗号分割")
@click.option("--packages", help="要分发的资源")
@click.option("--tp", help="类型")
def distribute_source_package(source_path, distribute_paths, packages, tp):
    """
    分发资源
    """
    switcher = {
        "chd": lambda: resource_manager.ChdResourceManager(),
    }
    resource_mgr = switcher.get(tp)()
    json_result = {"result": "false", "msg": ""}
    source_path = source_path.replace("/", os.path.sep).replace("\\", os.path.sep)
    distribute_paths = distribute_paths.replace("/", os.path.sep).replace("\\", os.path.sep)
    distribute_path_list = distribute_paths.split(",")
    if not os.path.exists(source_path):
        json_result["msg"] = "资源源路径不存在"
    else:
        try:
            resource_mgr.distribute(source_path, distribute_path_list, packages)
        except Exception as exp:
            json_result["msg"] = str(exp)
        else:
            json_result["result"] = "true"
    print(json.dumps(json_result, ensure_ascii=False))


@cli.command()
@click.option("--path", help="打包工具工程目录")
def analyze_cdn_hotfix_log(path):
    """
    分析releaseTool的日志
    Returns:

    """
    # 首先过滤出最新的日志文件
    p = resource_manager.Path(path)
    log_files = p.rglob("*.txt")
    latest_log = max(log_files, key=os.path.getctime)

    # 分析日志内容
    error_code = -1
    error_msg = ""
    with open(latest_log) as file:
        for line in file:
            # for line in list(file)[::-1]:
            if "[error]:" in line and "exception" in line:
                error_msg = line.split("exception:")[-1]
            if "OnCompleteDealResult result" in line:
                error_code = line.split(":")[-1]
    print(error_code)
    print(error_msg)


@cli.command()
@click.option("--p4_root", help="p4 root")
@click.option("--workspace", help="p4 workspace name")
@click.option("--p4_view", help="p4 view")
@click.option("--force_sync", help="force sync p4 or not")
@click.option("--resource", help="使用哪个资源模板")
@click.option("--branch", help="需要更新的分支")
def p4_sync(p4_root: str, workspace: str, force_sync, p4_view: str = None, resource:str="", branch=""):
    """
    sync perforce data
    """
    if resource:
        # 注意资源名称, 错误的会报异常IndexError
        name, R_MGR = inspect.getmembers(resource_manager, lambda obj: isinstance(obj, type) and issubclass(obj, resource_manager.ResourceManager) and getattr(obj, 'kind') == resource)[0]
        p4_view_list = R_MGR.get_p4_view(workspace, branch=branch)
    else:
        if not p4_view:
            with open(f"{p4_root}/p4_view.txt", "r", encoding="utf-8") as f:
                p4_view = f.read()
                logging.info(f"p4_view: {p4_view}")
        p4_view_list = []
        for x in p4_view.strip().split("#"):
            res = x.replace("${P4_CLIENT}", workspace)
            if res:
                p4_view_list.append(res.strip())
    logging.info(p4_view_list)
    p4_tool = P4Tool(config.p4_user, config.p4_host, config.p4_passwd, workspace, p4_root)
    p4_tool.p4.exception_level = 1
    p4_tool.init_p4_view(p4_view_list, options="allwrite clobber nocompress unlocked nomodtime rmdir")

    for view in p4_view_list:
        if force_sync == "true":
            p4_tool.sync("-f", view.split(" ")[0])
        else:
            p4_tool.sync(view.split(" ")[0])


@cli.command()
@click.option("--p4_root", help="p4 root")
@click.option("--workspace", help="p4 workspace name")
@click.option("--p4_view", help="p4 view")
@click.option("--force_sync", help="force sync p4 or not")
def p4_sync_and_clean(p4_root: str, workspace: str, force_sync, p4_view: str = None):
    """
    sync and clean perforce data
    """
    if not p4_view:
        with open(f"{p4_root}/p4_view.txt", "r", encoding="utf-8") as f:
            p4_view = f.read()

    p4_view_list = []
    for x in p4_view.strip().split("#"):
        res = x.replace("${P4_CLIENT}", workspace)
        if res:
            p4_view_list.append(res.strip())

    logging.info(p4_view_list)
    p4_tool = P4Tool(config.p4_user, config.p4_host, config.p4_passwd, workspace, p4_root)
    p4_tool.p4.exception_level = 1
    p4_tool.init_p4_view(p4_view_list, options="allwrite clobber nocompress unlocked nomodtime rmdir")

    for view in p4_view_list:
        if force_sync == "true":
            p4_tool.sync("-f", view.split(" ")[0])
        else:
            p4_tool.sync(view.split(" ")[0])

    for view in p4_view_list:
        p4_tool.clean("-d", f"{view.split(' ')[0]}")


@cli.command()
@click.option("--jenkins_workspace", help="jenkins workspace")
@click.option("--project_path", help="project path")
@click.option("--assets", help="assets")
def export_and_commit_scene_config(jenkins_workspace: str, project_path: str, assets: str):
    x5mconfig_path = os.path.join(jenkins_workspace, "x5mconfig", "config", "server", "community", "extend_game")
    if not os.path.exists(x5mconfig_path):
        os.makedirs(x5mconfig_path)

    assets_list = assets.split(",")
    for asset in assets_list:
        if not asset.endswith("unity"):
            continue

        command = f'Unity.exe -quit -batchmode -logFile - -projectPath "{project_path}" -executeMethod NavMeshExport.OpenSceneAndExport -buildTarget android scene_path={asset} output_path={x5mconfig_path}'
        logging.info(command)
        os.system(command)

    commit_message = os.getenv("BUILD_URL")
    os.chdir(x5mconfig_path)
    os.system("git reset --hard HEAD")
    os.system("git pull origin cdn")
    os.system("git add .")
    os.system("git commit -m '{}'".format(commit_message))
    ret = os.system("git push origin cdn")
    if ret != 0:
        raise Exception(f"上传git失败, 错误码: {ret}")

    commit_id = os.popen("git rev-parse HEAD").read()
    print(f"https://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig/-/commit/{commit_id}")


@cli.command()
@click.option("--branch", help="用户填入的branch参数")
def git_branches(branch: str):
    """
    获取远程git仓库所有分支
    """
    import gitlab

    __x5m_url = "http://x5mobile-gitlab.h3d.com.cn/"
    __username = "<EMAIL>"
    __password = "maintainer123"
    __token = "o-yNgwVADiQ8CicYdt_4"
    project = "dgm/x5mobile"
    gl = gitlab.Gitlab(__x5m_url, __token)
    __project = gl.projects.get(project)
    # 获取所有分支
    ret = __project.branches.list(all=True)
    branches = []
    if len(ret) != 0:
        branches = [b.name for b in ret]
    if branch not in branches:
        print("输入的branch分支不存在")
        exit(-1)


@cli.command()
@click.option("--workspace", help="p4 workspace")
def sync_timeline_config(workspace: str):
    from timeline.sync_excel import P4ClientTool

    p4_client = P4ClientTool(workspace)
    p4_client.sync_timeline_config()


@cli.command()
@click.option("--changelist", help="此次打包的原始资源changelist")
def update_timeline_persistent_config(changelist: str):
    try:
        conf.set("P4", "timeline", changelist)
        conf.write(open("../config/config.ini", "w"))
        logging.info(f"changelist:{changelist}")
    except Exception as e:
        logging.error(f"更新配置报错: {e}")


@cli.command()
@click.option("--platform", help="平台信息")
def get_npc_changelist(platform: str):
    from gitlab import Gitlab
    import configparser

    gl = Gitlab(
        url="https://x5mobile-gitlab.h3d.com.cn/",
        private_token="********************",
    )
    project = gl.projects.get(94)
    file = project.files.get(file_path=f"config.ini", ref=f"npc-{platform}")
    file_content = file.decode().decode("unicode_escape")  # decode file content if needed
    cg = configparser.ConfigParser(allow_no_value=True)
    cg.read_string(file_content)
    print(cg.get("P4", "npc"))


@cli.command()
@click.option("--workspace", help="p4 workspace")
@click.option("--views", help="p4 workspace", type=click.STRING)
def write_resources(workspace, views):
    views = json.loads(views)
    logging.info("views: {}".format(views))
    resources = []
    pattern = re.compile(r"^//.*?(assets.*?)/\.\.\.$")
    for view in views:
        if "npc" in view:
            continue
        ret = re.findall(pattern, view)
        if ret:
            resources.append(ret[0])
    logging.info("resources: {}".format(resources))

    resources_file = os.path.join(workspace, r"x5_mobile\mobile_dancer\arttrunk\client\AssetBundleTool\resources.txt")
    with open(resources_file, "w", encoding="utf-8") as f:
        f.write(",".join(resources))


@cli.command()
@click.option("--workspace", help="p4 workspace", type=click.STRING)
@click.option("--view_env", help="p4 view", type=click.STRING, default="")
@click.option("--view", help="p4 view", default="")
def fill_npc_p4_view(workspace, view_env="", view:str=""):
    from timeline.sync_excel import P4ClientTool
    from timeline.get_timeline_view import get_huo_dong_p4_view
    if view:
        view_list = view.split(";")
    else:
        view_list = os.getenv(view_env, "").split(";")
    if not view_list:
        logging.info(view_list)
        print(";".join(view_list))
        return
    # 根据活动依赖表获取所有活动的相关的资源ID
    view_list = []
    ids = set(re.findall(re.compile(r"npc_\d{10}|\d{7,8}", re.S), os.getenv(view_env, "")))
    p4_client = P4ClientTool(workspace)
    huo_dong_excel_path = os.path.join(p4_client.p4_root, "timeline_config/huodong_dependence.xlsx")
    p4_view_list = get_huo_dong_p4_view(list(ids), huo_dong_excel_path)
    t_view_prefix = "//x5_mobile/mr/art_release/art_src/timeline"
    t_view_suffix = "//${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/assets/resources/art/timeline"
    n_view_prefix = "//x5_mobile/mr/art_release/art_src"
    n_view_suffix = "//${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/assets/resources/art"
    for p4_view in p4_view_list:
        for view in p4_view:
            logging.info(f"view: {view}")
            if view.startswith("npc"):
                full_view = [
                    f"{n_view_prefix}/{view}/... {n_view_suffix}/{view}/...",
                ]
            else:
                full_view = [
                    f"{t_view_prefix}/{view}/... {t_view_suffix}/{view}/...",
                ]
            view_list.extend(full_view)
    print(";".join(view_list))


@cli.command()
@click.option("--workspace", help="p4 workspace")
@click.option("--view_env", help="p4 view", type=click.STRING)
@click.option("--view", help="p4 view", default="")
def fill_p4_view(workspace: str, view_env: str = "", view: str = ""):
    from timeline.sync_excel import P4ClientTool
    from timeline.get_timeline_view import get_p4_view
    from timeline.get_npc_view import get_npc_view_list
    if not view:
        view_list = os.getenv(view_env, "").split(";")
    else:
        view_list = view.split(";")
    if not view_list:
        logging.info(view_list)
        print(";".join(view_list))
        return

    p4_client = P4ClientTool(workspace)
    ids = set(re.findall(re.compile(r"\d{7,8}", re.S), os.getenv("TM_P4_VIEW", "")))
    excel_path = os.path.join(p4_client.p4_root, "timeline_config/newmulti_dependence.xlsx")
    p4_view_list = get_p4_view(list(ids), excel_path)
    view_prefix = "//x5_mobile/mr/art_release/art_src/timeline"
    view_suffix = "//${p4Client}/x5_mobile/mobile_dancer/arttrunk/client/assets/resources/art/timeline"
    for p4_view in p4_view_list:
        timeline_number = os.getenv("TIMELINE_NUMBER")
        if timeline_number == "1":
            if any("newmulti" in view for view in p4_view):
                continue
        elif timeline_number == "2":
            if all("newmulti" not in view for view in p4_view):
                continue
        for view in p4_view:
            full_view = f"{view_prefix}/{view}/... {view_suffix}/{view}/..."
            view_list.append(full_view)

    logging.info(f"before parse: {view_list}")
    view_list = get_npc_view_list(view_list)
    logging.info(f"after parse: {view_list}")

    print(";".join(view_list))


@cli.command()
@click.option("--workspace", help="p4 workspace")
def copy_timeline_config(workspace: str):
    from timeline.sync_excel import P4ClientTool

    p4_client = P4ClientTool(workspace)
    src = os.path.join(p4_client.p4_root, "timeline_config/newmulti_dependence.xlsx")
    dst = os.path.join(p4_client.p4_root, "x5_mobile/mobile_dancer/arttrunk/client/Assets/resources/Art/timeline")
    logging.info(f"src: {src}, dst: {dst}")
    shutil.copy(src, dst)


@cli.command()
@click.option("--resource", help="P4资源路径")
def timeline_get_updates(resource: str):
    from timeline.get_timeline_updates import get_updates

    get_updates(resource)


@cli.command()
@click.option("--resource", help="P4资源路径")
def record_fail(resource: str):
    from timeline.record_fail_pkg import record

    record(resource)


@cli.command()
@click.option("--p4_root", help="p4 root")
@click.option("--tp", help="类型")
def distribute_precheck(p4_root: str, tp: str):
    """
    分发热更资源前预检查
    """
    result_map = {"state": "true", "msg": ""}
    hotfix_distrib_file_map = {
        "action_distrib": "act/hotfix-action-base.xlsx",
        "camera_distrib": "camera/hotfix-camera-rule.xlsx",
        # "chd": "chd/hotfix-action-base.xlsx",
        "house_distrib": "house/hotfix-house-rule.xlsx",
        "island_distrib": "island/hotfix-small-island-rule.xlsx",
        "link_distrib": "link/hotfix-link-rule.xlsx",
        "makeup_distrib": "makeup/hotfix-makeup-rule.xlsx",
        "musicact_action_distrib": "act/hotfix-action-base.xlsx",
        # "model": "model/hotfix-model-rule.xlsx",
        # "npc": "npc/hotfix-action-base.xlsx",
        "house_scene": "scene/hotfix-house-scene.xlsx",
        "scene": "scene/hotfix-scene.xlsx",
        "texture": "ui/hotfix-texture-rule.xlsx",
    }
    hotfix_distrib_file = hotfix_distrib_file_map.get(tp, False)
    if not hotfix_distrib_file:
        result_map["state"] = "false"
        result_map["msg"] = "分发类型不存在"
        print(json.dumps(result_map))
        return
    hotfix_distrib_file_path = os.path.join(p4_root, "x5mplan/resmg/", hotfix_distrib_file)
    logging.info(f"hotfix_distrib_file_path: {hotfix_distrib_file_path}")
    data = xlrd.open_workbook(hotfix_distrib_file_path)
    table = data.sheets()[0]
    err = []
    for i in range(1, table.nrows):
        config_data = table.row_values(i, 1, 2)
        distrib_file_path = str(config_data[0])
        big_edit = distrib_file_path[0] + "." + distrib_file_path[1:3] + ".0"
        small_edit = distrib_file_path + "000"
        p4_path = f"//x5_mobile/mr/onlineupdate/{big_edit}/{small_edit}/*"
        if not p4tool.dirs(p4_path):
            err.append(f"p4 path {p4_path} not exists or empty")
    if err:
        result_map["state"] = "false"
        result_map["msg"] = "\n".join(err)
    print(json.dumps(result_map))


if __name__ == "__main__":
    p4tool.login()
    cli()
