# -*- coding: utf-8 -*-
# @Time    : 2021-08-31 17:23
# <AUTHOR> z<PERSON><PERSON><EMAIL>
# @File    : shader_manager.py
# @Version : 1.0


import os
import re
import hashlib
import shutil
import requests

from __init__ import *
from config import *


class ShaderManager(object):
    """
    shader 操作类
    """

    def __init__(self):
        self.p4_path = "//x5_mobile/mr/art_release/shader/"

    def get_new_p4_tool(self, workspace):
        return P4Tool(p4_user, p4_host, p4_passwd, workspace, "")

    def get_updates(self, version_name, git_commit_check):
        """
        获取
        """
        result = {"update": "false", "msg": "", "zip_file_name": "", "pipeline_id": "", "commit_status": "", "submit_user": ""}
        exists, file_name, result["submit_user"] = self.check_version_valid(version_name)
        result["zip_file_name"] = file_name
        if exists:
            if git_commit_check:
                request_result, msg_or_id = self.get_pipeline_no()
                if request_result:
                    current_version_pid = config["GIT"].get(version_name)
                    if current_version_pid is None:
                        result["update"] = "true"
                        result["pipeline_id"] = msg_or_id
                        result["msg"] = "success"
                        result["commit_status"] = "true"
                    else:
                        if int(msg_or_id) > int(current_version_pid):
                            result["update"] = "true"
                            result["pipeline_id"] = msg_or_id
                            result["msg"] = "success"
                            result["commit_status"] = "true"
                        else:
                            result["pipeline_id"] = current_version_pid
                            result["msg"] = "git上未提交"
                            result["commit_status"] = "false"
                else:
                    result["msg"] = msg_or_id
            else:
                result["update"] = "true"
        else:
            result["msg"] = "shader版本:{}在p4上对应的目录不合规或不存在".format(version_name)
        return result

    def get_pipeline_no(self):
        """
        获取arttrunk工程是否有更新
        """
        # arttrunk的工程id为34
        project_id = 34
        request_url = "https://x5mobile-gitlab.h3d.com.cn/api/v4/projects/{}/repository/commits/master/?" "private_token=i6FAUu8yPYST1iJzyH9M".format(
            project_id
        )
        try:
            response = requests.get(request_url)
            json_data = response.json()
            return True, json_data["last_pipeline"]["id"]
        except Exception as exp:
            return False, str(exp)

    def check_version(self, version_name):
        """
        校验版本号
        """
        result = False
        p4_srv = self.get_new_p4_tool("")
        if self.check_version_exists(version_name, p4_srv):
            return self.check_version_complete(version_name, p4_srv)
        return result, ""

    def check_version_complete(self, version_name, p4_srv):
        """
        校验 version 所在目录下的文件是否完整，包括zip文件, android, ios, windows ab文件
        """
        files_list = p4_srv.files("-e", "{}{}/...".format(self.p4_path, version_name))
        files = list()
        shader_changelist = ""
        submit_user = ""
        for res in files_list:
            file_item = res["depotFile"].replace("{}{}/".format(self.p4_path, version_name), "")
            files.append(file_item)
            if file_item == "shader.zip":
                shader_changelist = res["change"]
        des = p4_srv.describe("-s", shader_changelist)
        if des:
            submit_user = des[0]["user"]
            if not submit_user.endswith("@h3d.com.cn"):
                submit_user = "{}@h3d.com.cn".format(submit_user)
        if (
            "android/assetbundles/global_dependeencies/shaders" in files
            and "iOS/assetbundles/global_dependeencies/shaders" in files
            and "windows/assetbundles/global_dependeencies/shaders" in files
            and "shader.zip" in files
        ):
            return True, submit_user
        return False, submit_user

    def get_latest_version(self, version_prefix=None):
        """
        获取最新分支
        """
        result = False
        latest_version = ""
        p4_srv = self.get_new_p4_tool("")
        p4_path = "{}*".format(self.p4_path)
        dir_names = p4_srv.get_dir_names(p4_path)
        valid_version = list()
        submit_user = ""
        for dirs in dir_names:
            check_complete_result, submit_user = self.check_version_complete(dirs, p4_srv)
            if check_complete_result:
                valid_version.append(dirs)
        if valid_version:
            if version_prefix:
                valid_version = [i for i in valid_version if i.startswith(version_prefix)]
            if valid_version:
                valid_version.sort()
                latest_version = valid_version[-1]
                result = True
        return result, latest_version, submit_user

    def check_version_exists(self, version_name, p4_srv):
        """
        校验版本号存在
        """
        p4_path = "{}*".format(self.p4_path)
        dir_names = p4_srv.get_dir_names(p4_path)
        return version_name in dir_names

    def check_version_valid(self, version_name):
        """
        校验版本号的目录存在，并且该目录下 有且仅有一个zip文件
        """
        result = False
        file_name = ""
        submit_user = ""
        p4_srv = self.get_new_p4_tool("")
        if self.check_version_exists(version_name, p4_srv):
            zip_files = list()
            files_list = p4_srv.files("-e", "{}{}/...".format(self.p4_path, version_name))
            if files_list in [p4_error_code, p4_warning_code]:
                return result, file_name
            for res in files_list:
                file_item = res["depotFile"].rsplit("/", maxsplit=1)
                if file_item:
                    file_name = file_item[-1]
                    if file_name.endswith(".zip"):
                        zip_files.append(file_name)
                        shader_changelist = res["change"]
                        des = p4_srv.describe("-s", shader_changelist)
                        if des:
                            submit_user = des[0]["user"]
                            if not submit_user.endswith("@h3d.com.cn"):
                                submit_user = "{}@h3d.com.cn".format(submit_user)
            if zip_files and len(zip_files) == 1:
                result = True
                file_name = zip_files[0]
        return result, file_name, submit_user

    def unzip(self, file_path, target_path):
        """
        解压文件到指定目录
        """
        shutil.unpack_archive(filename=file_path, extract_dir=target_path, format="zip")

    def check_unzip_file_valid(self, root_path, md5_file_path):
        """
        校验解压后文件
        """
        check_result, msg = False, ""
        with open(md5_file_path, "r", encoding="utf-8") as fp:
            lines = fp.readlines()
            for line in lines:
                if line.strip():
                    try:
                        path, md5_val = line.split("|")
                        m = self.get_file_md5(os.path.join(root_path, path))
                        if m.strip() != md5_val.strip():
                            raise ValueError("md5值异常, 异常文件路径:{}, 异常文件原MD5值:{}, 计算后MD5值:{}".format(path, md5_val.strip(), m.strip()))
                    except Exception as exp:
                        msg = str(exp)
            else:
                # 全部文件正常
                check_result = True
        return check_result, msg

    def get_file_md5(self, file_path):
        """
        计算文件md5值
        """
        with open(file_path, "rb") as fp:
            return hashlib.md5(fp.read()).hexdigest()

    def get_version_by_changelist(self, change_list):
        """
        通过chang list获取shader版本
        """
        result = {"update": "false", "version_name": "", "msg": "", "submit_user": ""}
        try:
            p4_srv = self.get_new_p4_tool("")
            change_results = p4_srv.describe("-s", int(change_list))
            if not change_results:
                raise ValueError("p4 changelist号:{}无效".format(change_list))
            desc_dict = change_results[0]
            submit_user = desc_dict["user"]
            if not submit_user.endswith("@h3d.com.cn"):
                submit_user = "{}@h3d.com.cn".format(submit_user)
            result["submit_user"] = submit_user
            depot_status_dict = dict(zip(desc_dict["depotFile"], desc_dict["action"]))
            for k, v in depot_status_dict.items():
                # 确认本地提交的文件不是删除状态
                if k.startswith(self.p4_path) and k.endswith(".zip") and v not in ["delete", "move/delete"]:
                    ret = re.search(r"^{}(\d.*?\.\d*).*?/".format(self.p4_path), k)
                    if ret:
                        version_name = ret.group(1)
                        result["update"] = "true"
                        result["version_name"] = version_name
                        break
        except Exception as exp:
            result["msg"] = str(exp)
        return result
