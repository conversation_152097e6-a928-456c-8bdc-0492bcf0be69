import xlrd


def excel_to_dict(file_path: str) -> dict:
    """
    {
        "huodong": [
            {
                "名称": "活动9330002",
                "路径1": "huodong/9330002",
                "路径2": "newmulti/1048577",
                "路径3": "qinglvtao/1048575",
                "路径4": "qinglvtao/1048576",
                "路径5": "npc/models/npc_0408857701",
                "路径6": "npc/models/npc_1408857701",
                "路径7": "npc/models/npc_1408857601",
                "路径8": "npc/models/npc_1408857501",
                "路径9": "npc/models/npc_0408857601",
                "路径10": "npc/models/npc_0408857501",
            }
        ]
    }
    """
    book = xlrd.open_workbook(file_path)
    sheets = book.sheets()
    excel_data = {}
    for sheet in sheets:
        sheet_data_list = []
        for x in range(1, sheet.nrows):
            cell_data_dict = {}
            for y in range(sheet.ncols):
                value = sheet.col_values(colx=y)[x]
                if value:
                    row_value = sheet.row_values(rowx=0)[y]
                    col_value = sheet.col_values(colx=y)[x]
                    if isinstance(row_value, str):
                        row_value = row_value.strip()
                    if isinstance(col_value, str):
                        col_value = col_value.strip()
                    cell_data_dict[row_value] = col_value

            sheet_data_list.append(cell_data_dict)

        excel_data[sheet.name.strip()] = sheet_data_list

    return excel_data


def parse_path(path_data: dict) -> list:
    path = []
    for _, data in path_data.items():
        if data:
            full_path = []
            path_prefix = []
            for index in range(len(data)):
                # 获取路径前缀, 例如["newmulti", "qinglvtao", "qinglvtao"]
                if index == 0:
                    path_prefix = list(data[index].values())[1:]
                    continue

                # 重新组织路径后, [["newmulti/123"], ["qinglvtao/456"], ["qinglvtao/789"]]
                full_path.append(
                    list(
                        map(
                            lambda y: "/".join(y),
                            # 将路径前缀与id组合(("newmulti", "123"), ("qinglvtao", "456"), ("qinglvtao", "789"))
                            zip(
                                path_prefix,
                                # 获取id列表, 例如["123", "456", "789"]
                                map(
                                    lambda x: str(int(x)),
                                    list(data[index].values())[1:],
                                ),
                            ),
                        )
                    )
                )

            path.append(full_path)

    return path


def parse_huo_dong_path(path_data: dict) -> list:
    path = []
    for _, data in path_data.items():
        if data:
            full_path = []
            for index in range(len(data)):
                full_path.append(list(data[index].values())[1:])
            path.append(full_path)
    return path


def get_huo_dong_p4_view(npc_ids: list, excel_path: str) -> list:
    excel_data = excel_to_dict(excel_path)
    path = parse_huo_dong_path(excel_data)
    p4_view_list = []
    for npc_id in npc_ids:
        for sheets in path:
            for data in sheets:
                p4_view_list.append(data) if [x for x in data if x.split("/")[1] == npc_id and data not in p4_view_list] else ...
    return p4_view_list


def get_p4_view(timeline_ids: list, excel_path: str) -> list:
    excel_data = excel_to_dict(excel_path)
    path = parse_path(excel_data)

    p4_view_list = []
    for timeline_id in timeline_ids:
        for sheets in path:
            for data in sheets:
                p4_view_list.append(data) if [x for x in data if x.split("/")[1] == timeline_id and data not in p4_view_list] else ...

    return p4_view_list


if __name__ == "__main__":
    # excel = r"C:\Users\<USER>\Downloads\huodong_dependence.xlsx"
    excel = r"/Users/<USER>/Downloads/huodong_dependence.xlsx"
    data = excel_to_dict(excel)
    print(data)
    # excel = r"C:\Users\<USER>\Downloads\newmulti_dependence.xlsx"
    # view_list = get_huo_dong_p4_view(["9330002"], excel)
    # for view in view_list:
    #     print(view)
