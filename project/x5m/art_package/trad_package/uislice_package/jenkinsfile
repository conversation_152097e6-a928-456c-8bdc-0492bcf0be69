node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "dgm_pub_app_android_192.168.4.198"
        }
    }

    parameters {
        string(name: 'BUILD_DIR', defaultValue: 'G:\\trad_pack\\uislice', description: '构建目录，无特殊情况无需修改')
    }
    environment {
        PYFRAME_PYTHON = 'python.exe'
    }

    stages {
        stage('查找python路径') {
            steps {
                script {
                    // 检查 conda 是否可用
                    def isCondaAvailable = bat(script: 'conda --version', returnStatus: true) == 0

                    if (isCondaAvailable) {
                        echo "Using conda Python environment"
                        PYFRAME_PYTHON = 'conda run -n pyf368 python'
                        bat(script: """
                        conda env list | findstr pyf368 || conda create -y -n pyf368 python=3.6.8
                        """)
                    } else {
                        echo "Using system Python3"
                        PYFRAME_PYTHON = 'python.exe'
                    }
                }
            }
        }
        stage("安装python依赖") {
            steps {
                script {
                    bat(
                        script: """
                            ${PYFRAME_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
            }
        }
        stage("准备") {
            steps {
                script {
                    bat(script: "${PYFRAME_PYTHON} x5m.py trad_package --resource=uislice --job=prepare")
                }
            }
        }
        stage("查找需要生成的图集") {
            steps {
                script {
                    bat(script: "${PYFRAME_PYTHON} x5m.py trad_package --resource=uislice --job=find_need_pack")
                }
            }
        }
        stage("记录当前图集 meta") {
            steps {
                script {
                    bat(script: "${PYFRAME_PYTHON} x5m.py trad_package --resource=uislice --job=record_meta")
                }
            }
        }
        stage("生成图集") {
            steps {
                script {
                    bat(script: "${PYFRAME_PYTHON} x5m.py trad_package --resource=uislice --job=pack_resources")
                }
            }
        }
        stage("检查图集 meta") {
            steps {
                script {
                    bat(script: "${PYFRAME_PYTHON} x5m.py trad_package --resource=uislice --job=check_meta")
                }
            }
        }
        stage("提交图集") {
            steps {
                script {
                    bat(script: "${PYFRAME_PYTHON} x5m.py trad_package --resource=uislice --job=commit_resources")
                }
            }
        }
    }
    // post {
    //     success {
    //         script {
    //             bat(script: "${PYFRAME_PYTHON} x5m.py trad_package --resource=texture --job=on_success")
    //         }
    //     }
    //     failure {
    //         script {
    //             bat(script: "${PYFRAME_PYTHON} x5m.py trad_package --resource=texture --job=on_failure")
    //         }
    //     }
    //     aborted {
    //         script {
    //             bat(script: "${PYFRAME_PYTHON} x5m.py trad_package --resource=texture --job=on_canceled")
    //         }
    //     }
    // }
}
