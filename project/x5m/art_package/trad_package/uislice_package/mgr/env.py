from pathlib import Path

from frame import common, env


class JenkinsEnvMgr:
    def __init__(self) -> None:
        pass

    def get_changelist(self) -> str:
        return env.get("CHANGELIST", "head")

    def get_build_dir(self) -> Path:
        return Path(env.get("BUILD_DIR"))

    def get_force_update(self) -> bool:
        return common.str2bool(env.get("FORCE_UPDATE", "true"))

    # def get_language(self) -> str:
    #     return env.get("LANGUAGE")


env_mgr = JenkinsEnvMgr()
