from project.x5m.art_package_mgr.base_mgr.package_mgr import config


DIR_PREFIX = "/role/actions"

# Unity的支持配置，用于执行连续构建，单测等任务
EXECUTE_METHOD = "H3DBuildTools.BuildAction"

# p4 view
SOURCE_P4_VIEW_LIST = [
    f"{config.p4_views.get('art_resources_src')}/role/actions/action-expression/...",
    f"{config.p4_views.get('resmg_root')}/act/...",
]


# trad p4 view
SOURCE_TRAD_P4_VIEW_LIST = [
    f"{config.p4_trad_views.get('art_resources_src')}/role/actions/action-expression/...",
    f"{config.p4_trad_views.get('trad_resmg_root')}/act/...",
]
