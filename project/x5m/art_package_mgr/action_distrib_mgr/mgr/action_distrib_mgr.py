"""
动作分发管理模块
"""
import collections
import re
from typing import List, Dict

from frame import *

from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr, env_mgr
from project.x5m.art_package_mgr.base_mgr.enum_mgr import DisTributeEnum
from project.x5m.art_package_mgr.action_distrib_mgr.mgr import *
from project.x5m.art_package_mgr.action_distrib_mgr.mgr.utils import (
    render_source_p4_view_list,
    render_ab_p4_view_list,
    set_ingame_path,
    get_ingame_path,
    set_ingame_dir_path,
    get_ingame_dir_path,
    set_ingame_prefix_path,
    get_ingame_prefix_path,
)


class ActionDistribMgr(DistributePackageMgr):
    def __init__(self, **kwargs):
        super().__init__(kind=ResourceTypeEnum.ACTION_DISTRIB, **kwargs)
        # 变更资源路径前缀
        # 资源拉去到的路径前缀
        self._resource_dir_prefix = f"{config.p4_views.get('art_resources_src')}{DIR_PREFIX}"
        # 打包命令执行的静态方法
        self._execute_method = EXECUTE_METHOD
        # 打包拉取的资源
        self._b_path = self.p4_mgr.get_last_branch()
        self._const_source_p4_views = render_source_p4_view_list()
        self._const_ab_p4_views = render_ab_p4_view_list(self._platform, self._b_path)
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s.*?/actions/([\w\/]+$)")
        self._file_suffix = ".act.txt"
        self._ab_view = f"{config.p4_views.get('cdn_root')}/cooked_output/{self._platform}/assetbundles/"

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        """过滤满足条件的资源路径

        Args:
            source_paths: 资源路径列表
        Returns:
            满足条件的资源路径
        """
        depots = set()
        paths = self._render_p4_view_paths()
        ingame_dir_paths, ingame_prefixs = set(), set()
        for fp in source_paths:
            # 首先校验是否是对应的资源前缀，如果不满足，则直接跳过
            if not fp.startswith(self._resource_dir_prefix):
                continue
            # 获取路径，并按照规则匹配路径是否满足条件，如果满足条件追加到记录中
            # 记录文件，然后按照非前缀的内容进行匹配
            if fp.startswith((paths["ingame_dance_actions_anim"], paths["ingame_nodance_actions_anim"], paths["ingame_couple_dance_actions_anim"])):
                # 以最右侧分割1次
                ingame_dir_paths.add(fp.rsplit("/", 1)[0])
                if fp.endswith(self._file_suffix):
                    ingame_prefixs.add(fp.split(".")[0])
                    depots.add(fp)
                continue

            if fp.startswith(
                (
                    paths["actions_anim"],
                    paths["cloth_anim"],
                    paths["actions_anim_expression"],
                    paths["expression"],
                    paths["xhs_actions_anim"],
                    paths["xhs_actions_anim_expression"],
                )
            ) and fp.endswith(".anim"):
                depots.add(fp)

        # 在处理ingame的文件
        suffix_path = [
            "_root.anim.meta",
            "_root.anim",
            "_nrot.anim.meta",
            "_nrot.anim",
            "_high_shape4_m.anim.meta",
            "_high_shape4_m.anim",
            "_high_shape4_f.anim",
            "_high_shape4_f.anim.meta",
            "_high_m.anim.meta",
            "_high_m.anim",
            "_high_f.anim.meta",
            "_high_f.anim",
        ]
        _p4_views = set()
        for i in ingame_prefixs:
            for j in suffix_path:
                _p4_views.add(f"{i}{j}")
        log.info("p4 views %s", _p4_views)
        # 设置要拉取的p4的路径
        set_ingame_path(list(_p4_views))

        # 添加拉取的路径，用于后面匹配前缀
        set_ingame_dir_path(list(ingame_dir_paths))
        set_ingame_prefix_path(list(ingame_prefixs))
        return depots

    def _render_p4_view_paths(self):
        """渲染路径"""
        return {
            "actions_anim": self._render_p4_view_path("actions_anim"),
            "cloth_anim": self._render_p4_view_path("cloth_anim"),
            "actions_anim_expression": self._render_p4_view_path("actions_anim_expression"),
            "expression": self._render_p4_view_path("expression"),
            "xhs_actions_anim": self._render_p4_view_path("xhs_actions_anim"),
            "xhs_actions_anim_expression": self._render_p4_view_path("xhs_actions_anim_expression"),
            "ingame_dance_actions_anim": self._render_p4_view_path("ingame_dance_actions_anim"),
            "ingame_nodance_actions_anim": self._render_p4_view_path("ingame_nodance_actions_anim"),
            "ingame_couple_dance_actions_anim": self._render_p4_view_path("ingame_couple_dance_actions_anim"),
        }

    def _fill_source_p4_views(self):
        super()._fill_source_p4_views()
        self.p4_mgr.add_p4_views(get_ingame_path())
        # 拉取目录进行路径的匹配
        ingame_dir_path = get_ingame_dir_path()
        ingame_prefix_path = get_ingame_prefix_path()
        log.info("_ingame_dir_path: %s", ingame_dir_path)
        all_paths = set()
        for ingame_dir in ingame_dir_path:
            try:
                files = self.p4_mgr.p4.run("files", "-i", "-e", f"{ingame_dir}/...")
                for f in files:
                    depot_file = f.get("depotFile")
                    if not depot_file:
                        continue
                    all_paths.add(depot_file)
            except Exception:
                pass
        # 进行文件匹配
        p4_view_path = set()
        for _prefix in ingame_prefix_path:
            low_prefix = _prefix.lower()
            for _file_path in all_paths:
                # 进行前缀匹配，并且忽略满足`.act.txt`和`.act.txt.meta`后缀的文件
                if (_file_path.lower().startswith(low_prefix)) and (not _file_path.endswith((self._file_suffix, f"{self._file_suffix}.meta"))):
                    p4_view_path.add(_file_path)

        self.p4_mgr.add_p4_views(list(p4_view_path))
        log.info("ingame_prefix_path: %s", p4_view_path)

    def _render_p4_view_path(self, subfix_name: str) -> str:
        """渲染p4 view路径"""
        return f"{self._resource_dir_prefix.rstrip('/')}/{subfix_name}"

    def _make_resouces_file(self):
        """组装打包命令需要的文件路径"""
        super()._make_resouces_file(p4_view_type="art_resources_src")

    def _default_distribute(self) -> List[str]:
        return [
            config.p4_views["resources_root"].format(branch="Resources"),
            config.p4_views["resources_root"].format(branch=f"b/{self._b_path}"),
        ]

    def _parse_id_to_filename(self, id: str) -> str:
        return id.split("/")[-1]

    def _get_distruibute_dict(self) -> Dict:
        success_ids = env_mgr.get_report().get("success_ids")
        dist_destinations = collections.defaultdict(list)
        for id in success_ids:
            for distribute in self._distribute_config:
                ret = distribute.match(self._parse_id_to_filename(id))
                if ret:
                    self._distribute_map[distribute.distribute_type].append((id, [ret]))
                    dist_destinations[str(distribute.distribute_type)].append(id)
                    break
            else:
                self._distribute_map[DisTributeEnum.DEFAULT].append((id, self._default_distribute()))
                dist_destinations[str(DisTributeEnum.DEFAULT)].append(id)
        log.info("dist_destinations: %s", dist_destinations)
        env_mgr.set_dist_destinations(dist_destinations)
        log.info("distribute_map: %s", self._distribute_map)
        return self._distribute_map

    def _get_file_root(self, dis_type: DisTributeEnum, root: str) -> str:
        if dis_type == DisTributeEnum.CDNBASE or dis_type == DisTributeEnum.CDNRULE:
            return f"{root}/cooked/{self._platform}/assetbundles/art{DIR_PREFIX}/"
        if dis_type == DisTributeEnum.SPECIAL:
            return f"{config.p4_views.get('resources_root').format(branch='b')}/{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art{DIR_PREFIX}/"
        if dis_type == DisTributeEnum.HOTFIX:
            return f"{root}/client/{self._platform}/assetbundles/art{DIR_PREFIX}/"
        if dis_type == DisTributeEnum.DEFAULT:
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art{DIR_PREFIX}/"
        raise PyframeException(f"Invalid distribute type {dis_type}")

    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            if distribute_type == DisTributeEnum.SPECIAL:
                for distribute in self._distribute_config:
                    if distribute.distribute_type == DisTributeEnum.SPECIAL:
                        special_branch = self._get_special_branch(distribute.distribute_rule.items())
                        if special_branch:
                            views.add(
                                f"{config.p4_views['resources_root'].format(branch=f'b/{special_branch}')}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art{DIR_PREFIX}/"
                            )
            for _, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    file_root = self._get_file_root(distribute_type, path_root)
                    views.add(file_root)
        log.info("ab views: %s", views)
        self.p4_mgr.add_p4_views(list(views))

    def _get_special_branch(self, data_list_special) -> str:
        max_branch = self._b_path
        special_data_branches = []
        for special_data in data_list_special:
            for special_data_name in special_data:
                special_data_branch = special_data[special_data_name]
                if special_data_branch not in special_data_branches and special_data_branch.startswith(max_branch):
                    special_data_branches.append(special_data_branch)
        if special_data_branches:
            return special_data_branches[0]
        return ""

    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    file_root = self._get_file_root(distribute_type, path_root)
                    view = f"{file_root}{id}"
                    self._submit_ab_views.append(view)
                    src = f"{self.ab_path}art{DIR_PREFIX}/{id}"
                    _view = self.p4_mgr.parse_p4view_to_localpath(file_root)
                    log.info("src_path: %s, _views path: %s", src.rstrip("/"), f"{_view}{id}".rstrip("/"))
                    if src.rstrip("/") != f"{_view}{id}".rstrip("/"):
                        path_mgr.copy(src, f"{_view}{id}")
        log.info("dist_ab_file: %s", self._submit_ab_views)
