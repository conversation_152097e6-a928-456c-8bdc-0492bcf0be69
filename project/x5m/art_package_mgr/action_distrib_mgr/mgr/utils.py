from typing import List

from frame import *

from project.x5m.art_package_mgr.base_mgr.package_mgr import config, LanguageEnum
from project.x5m.art_package_mgr.base_mgr.env_mgr import env_mgr

from . import SOURCE_P4_VIEW_LIST, SOURCE_TRAD_P4_VIEW_LIST


def render_ab_p4_view_list(platform: str, b_path: str, language: str = LanguageEnum.CHIN_SIMP.value) -> List[str]:
    """渲染资产包P4视图列表

    NOTE: 暂时不考虑繁中
    """
    if language == LanguageEnum.CHIN_TRAD.value:
        return []
    return [
        f"{config.p4_views.get('cdn_resources')}/{platform}/assetbundles/art/role/actions/...",
        f"{config.p4_views.get('resources_root').format(branch='b')}/{b_path}/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art/role/actions/...",
        f"{config.p4_views.get('resources_root').format(branch='b')}/7.04.0_dance/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art/role/actions/...",
        f"{config.p4_views.get('cdn_root')}/cooked/{platform}/assetbundles/art/role/actions/...",
    ]


def render_source_p4_view_list(language: str = LanguageEnum.CHIN_SIMP.value) -> List[str]:
    """渲染源文件P4视图列表

    NOTE: 暂时不考虑繁中
    """
    if language == LanguageEnum.CHIN_TRAD.value:
        return SOURCE_TRAD_P4_VIEW_LIST
    return SOURCE_P4_VIEW_LIST


def set_ingame_path(path_list: List) -> None:
    platform = env_mgr.get_platform()
    _path_list = env.get(f"{platform}_ingame_path") or []
    _path_list.extend(path_list)
    env.set({f"{platform}_ingame_path": _path_list})


def get_ingame_path(platform: str = None) -> List:
    if not platform:
        platform = env_mgr.get_platform()
    return env.get(f"{platform}_ingame_path") or []


def set_ingame_dir_path(dir_path_list: List, platform: str = None) -> None:
    """设置目录路径，用于后续取对应的资源"""
    if not platform:
        platform = env_mgr.get_platform()
    _path_list = env.get(f"{platform}_ingame_dir_path") or []
    _path_list.extend(dir_path_list)
    # 去除重复
    env.set({f"{platform}_ingame_dir_path": list(set(_path_list))})


def get_ingame_dir_path(platform: str = None) -> List:
    """获取目录路径"""
    if not platform:
        platform = env_mgr.get_platform()
    return env.get(f"{platform}_ingame_dir_path") or []


def set_ingame_prefix_path(prefix_path_list: List, platform: str = None) -> None:
    """设置前缀路径，用于后续取对应的资源"""
    if not platform:
        platform = env_mgr.get_platform()
    _path_list = env.get(f"{platform}_ingame_prefix_path") or []
    _path_list.extend(prefix_path_list)
    # 去除重复
    env.set({f"{platform}_ingame_prefix_path": list(set(_path_list))})


def get_ingame_prefix_path(platform: str = None) -> List:
    """获取前缀路径"""
    if not platform:
        platform = env_mgr.get_platform()
    return env.get(f"{platform}_ingame_prefix_path") or []
