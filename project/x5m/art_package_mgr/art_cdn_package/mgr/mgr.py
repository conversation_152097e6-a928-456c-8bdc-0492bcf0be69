import re
from typing import List

from frame import *

from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, PackageMgr, config, X5mGitMgr, env_mgr, LanguageEnum, PlatformEnum

from project.x5m.art_package_mgr.art_cdn_package.mgr import DIR_PREFIX, EXECUTE_METHOD, utils


class ArtCdnPackageMgr(PackageMgr):
    """
    NOTE: 打包时，没有从resources.txt去读，直接读取的`assets/ab_resources`下的文件
    """

    def __init__(self, **kwargs):
        super().__init__(kind=ResourceTypeEnum.ART_CDN, **kwargs)
        self._resource_dir_prefix = f"{config.p4_views.get('art_resource_root')}{DIR_PREFIX}"
        self._resource_c_dir_prefix = f"{config.p4_views.get('art_resource_root')}/c"
        self._const_ab_p4_views = utils.render_ab_p4_view_list(self._platform)
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s.*?texture/([\w\/]+$)")
        # 需要拷贝
        self._src_config_path = f"{self._workspace}/x5mconfig/{self._x5mconfig_branch}/config/card/card_decorate_wh.csv"
        self._dst_config_path = (
            f"{self._workspace}/arttrunk/{self._arttrunk_branch}/mr{config.p4_view_lang_flag}/art_release/cs/config/card/card_decorate_wh.csv"
        )
        self._execute_method = EXECUTE_METHOD
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb"

    def _clean_other(self):
        """清理其他的资源"""
        _path = f"{self._workspace}/arttrunk/{config.arttrunk_branch}/mr{config.p4_view_lang_flag}/art_release/art_src/texture"
        self._safe_clean(_path, "开始清理art_release下资源")

    def _check_package_by_changelist(self, changelist: int) -> bool:
        """根据路径检查是否需要打包"""
        _resource_dir_prefix = self._resource_dir_prefix
        self._resource_dir_prefix = self._resource_c_dir_prefix
        c_need_package = super()._check_package_by_changelist(changelist)
        _min_change_list = env_mgr.get_changelist()
        self._resource_dir_prefix = _resource_dir_prefix
        need_package = super()._check_package_by_changelist(changelist)
        _change_list = env_mgr.get_changelist()
        # 如果主路径不需要更改，则设置为上次的changelist
        if not need_package:
            env_mgr.set_changelist(changelist=str(changelist))
        # 如果都需要变动，则按照最小的changelist进行设置
        else:
            if _min_change_list > _change_list:
                _min_change_list = _change_list
            env_mgr.set_changelist(changelist=str(_min_change_list))

        if need_package or c_need_package:
            return True
        return False

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for file in source_paths:
            # 校验后缀，路径，及包含的路径的匹配规则
            if not self._check_source_path(source_path=file, patterns=".png"):
                continue
            if not (file.startswith(self._resource_dir_prefix) or file.startswith(self._resource_c_dir_prefix)):
                continue
            # 例如: //x5_mobile/mr/Resources/art_src/c/2205ipjzds/texture/transparent/pixel_1024/HYZH_bg11.png
            # 正则表达式匹配结果: c/2205ipjzds/texture
            # 意义: [^/]+表示匹配除了`/`以外的其他字符, 且该字符至少出现一次
            if file.startswith(self._resource_c_dir_prefix) and len(re.findall("c/[^/]+/texture", file)) == 0:
                continue
            depots.add(file)
        return depots

    def _fill_source_p4_views(self):
        """
        填充要打包的资源p4view
        """
        p4_views = set()
        for fp in env_mgr.get_source_paths():
            _fp = fp.replace(
                f"//x5_mobile/mr{config.p4_view_lang_flag}",
                f"//{self._kwargs['client']}/arttrunk/{config.arttrunk_branch}/mr{config.p4_view_lang_flag}",
            )
            view = f"{fp} {_fp}"
            p4_views.add(view)
        log.info("source p4 views %s", p4_views)
        self.p4_mgr.add_p4_views(list(p4_views), format_view=False)

    def _update_git(self) -> None:
        """更新git, arttrunk和配置仓库"""
        self.arttrunk_git_mgr.update()
        self._update_config_git()
        # 复制到ab的输出目录
        path_mgr.xcopy(self._src_config_path, self._dst_config_path, dst_is_file=True)

    def _update_config_git(self) -> None:
        """拉取 config 仓库，unity build完之后，会对比card_decorate_wh.csv文件，然后会对文件内容删增

        NOTE: 这里的路径相对unity的路径如下
        F:\DGM\arttrunk\mobile_dancer\arttrunk\client\Assets"/../../../../mr/art_release/cs/config/card/card_decorate_wh.csv"
        """
        self._git_mgr = X5mGitMgr(self._workspace, "x5mconfig", self._x5mconfig_branch)
        self._git_mgr.update(clean=True)

    def _copy_product(self):
        """把文件card_decorate_wh.csv复制到config目录下，然后提交"""
        path_mgr.xcopy(self._dst_config_path, self._src_config_path, dst_is_file=True)

    def _submit_git(self):
        # 因为仓库只有一个，ios和android生成的是同一个，因此，仅上传一次即可
        if self._platform != PlatformEnum.IOS:
            return
        # NOTE: 针对繁中不要提交，待和 PM 确定后分支及目录后，再进行提交
        if self._language == LanguageEnum.CHIN_TRAD:
            return
        self._git_mgr.submit_path(self._desc, self._src_config_path)

    def _make_resouces_file(self):
        super()._make_resouces_file()
        # 创建ab_resources/texture软链到resources/Art/texture
        src_local_path = os.path.join(self._project_path, "Assets/ab_resources/texture")
        dst_local_path = os.path.join(
            self._workspace, "arttrunk", config.arttrunk_branch, f"mr{config.p4_view_lang_flag}", "art_release", "art_src", "texture"
        )
        path_mgr.soft_link(dst_local_path, src_local_path)

    def _get_package_cmd(self) -> str:
        """
        获取打包命令
        """
        cmd_line = f"{self._platform.package_tool} "
        cmd_line += f"-quit -batchmode -projectPath {self._project_path} -logFile {self._log_file} "
        cmd_line += f"-executeMethod {self._execute_method} -buildTarget {self._platform} "
        # TODO: 能否统一命令参数，unity的使用说明
        cmd_line += f"outpath={self.ab_path} "
        return cmd_line.replace("/", "\\")

    def _get_submit_p4_view_ab(self) -> List:
        # 读取resources.txt文件，获取ab对应的p4 view
        files = set()
        # 获取成功的 success id
        # success id format: item_icon/0411118501_icon_mat
        success_ids = [sid.split("/")[-1] for sid in env_mgr.get_report().get("success_ids") or []]
        source_path = env_mgr.get_source_paths()
        for sp in source_path:
            _sp = sp.replace(config.p4_views.get("art_resource_root"), f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles")
            fpath, _ = os.path.splitext(_sp)
            # 追加 `_mat` 后缀
            fpath += "_mat"
            # 拆分路径，注意区分大小写
            _prefix, _suffix = fpath.rsplit("/", 1)
            _suffix = _suffix.lower()
            if _suffix not in success_ids:
                continue
            files.add(f"{_prefix}/{_suffix}")

        log.info("submit p4 views %s", files)
        return list(files)
