from typing import Dict, List, Optional, Tuple
import time

from frame import *

from project.x5m.art_package_mgr.base_mgr.enum_mgr import ResourceTypeEnum, DisTributeEnum, LanguageEnum
from project.x5m.art_package_mgr.base_mgr.env_mgr import env_mgr, global_env_mgr
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr
from project.x5m.art_package_mgr.base_mgr.package_mgr import config

from . import DIR_PREFIX, utils


class ArthubUploadP4Mgr(DistributePackageMgr):
    def __init__(self, **kwargs):
        super().__init__(kind=ResourceTypeEnum.ARTHUB_UPLOAD_P4, **kwargs)
        self._arthub_dir = f"{self._workspace.rstrip('/')}/arthub/"
        self._resource_local_dir_prefix = f"//{self._kwargs['client']}/arthub/"
        self.deploy_env = env_mgr.get_deploy_env()
        self._resource_dir_prefix = utils.get_resource_dir_prefix(self._language)
        if self._language == LanguageEnum.CHIN_TRAD:
            self._b_path = self.p4_mgr.get_last_branch(filter="^\d+\.\d+\.\d+_tc$")
        else:
            self._b_path = self.p4_mgr.get_last_branch()
        self._const_source_p4_views = utils.render_source_p4_view_list(self._kwargs["client"], self._b_path)
        self._submit_p4_views = self._const_source_p4_views
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb"

    def _filter_source_paths(self, source_paths: List) -> List:
        """过滤需要上传的资源"""
        depots = set()
        space_files = []
        not_png_files = []
        for fp in source_paths:
            if not fp.startswith(self._resource_dir_prefix):
                continue
            file_name = fp.split("/")[-1]
            # 匹配空的场景
            if " " in fp:
                space_files.append(fp)
            # 匹配不是以 png 或 PNG结尾的路径
            elif not file_name.endswith(".png") and not file_name.endswith(".PNG"):
                not_png_files.append(file_name)
            # 过滤出文件名不包含空格，以 `.png` 或者 `.PNG`结尾的文件
            else:
                depots.add(fp)
        utils.set_space_files(space_files)
        utils.set_not_png_files(not_png_files)
        return list(depots)

    def check_package(self, stop_current_build: bool = True, force_by_id: bool = True) -> bool:
        flag = super().check_package(stop_current_build=stop_current_build, force_by_id=force_by_id)
        file_info = {
            "end_time": time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime()),
            "all_count": "0",
            "p4_count": 0,
            "repeat_count": "0",
            "changelist": "",
            "sign": "1",
        }
        env_mgr.set_file_info(file_info)
        # 处理异常
        space_files = utils.get_space_files()
        if space_files:
            log.error("文件名包含空格: %s", space_files)
            msg = "下面文件名包含空格, 请相关人员修改后重新上传:\n"
            for space_file in space_files:
                msg += f"{space_file}\n"
            raise PyframeException(message=msg)
        not_png_files = utils.get_not_png_files()
        if not_png_files:
            log.error("文件不是png %s", not_png_files)
            msg = "下面文件不是png, 请相关人员修改后重新上传:\n"
            for not_png_file in not_png_files:
                msg += f"{not_png_file}\n"
            # raise PyframeException(message=msg)
        env_mgr.set_file_info(file_info)
        return flag

    def _fill_source_p4_views(self) -> None:
        p4_views = []
        file_info = env_mgr.get_file_info()
        for file in env_mgr.get_source_paths():
            file_name = file.split("/")[-1]
            view = f"{file} {self._resource_local_dir_prefix}{file_info['end_time']}/{file_name}"
            p4_views.append(view)
        log.info("pull p4 views: %s", p4_views)

        self._submit_p4_views.extend(p4_views)
        self.p4_mgr.add_p4_views(p4_views, format_view=False)

    def _fill_hotfix_p4_views(self) -> None:
        # 针对繁中，跳过处理
        if self._language == LanguageEnum.CHIN_TRAD:
            return
        dist = None
        for distribute in self._distribute_config:
            if distribute.distribute_type != DisTributeEnum.HOTFIX:
                continue
            dist = distribute
        if not dist:
            return

        depots = set()
        depot_dirs = set()
        for name, path in dist.distribute_rule.items():
            # 忽略以`??`开头的文件
            if name.startswith("??"):
                continue
            depots.add(f"{path}/art_src{DIR_PREFIX}/... //{self._kwargs['client']}/{path.lstrip('/')}/art_src{DIR_PREFIX}/...")
            depot_dirs.add(f"{path}/art_src{DIR_PREFIX}/...")

        log.info("hotfix_depots: %s", depots)
        # self.p4.append_view(list(depots))
        self.p4_mgr.add_p4_views(list(depots), format_view=False)
        self._submit_p4_views.extend(list(depots))
        env_mgr.set_hotfix_dir_list(list(depot_dirs))
        utils.add_p4_view_paths(list(depots))

    def _fill_cdn_rule_p4_views(self) -> None:
        dist = None
        for distribute in self._distribute_config:
            if distribute.distribute_type != DisTributeEnum.CDNRULE:
                continue
            dist = distribute
        if not dist:
            return

        depots = set()
        dot_dots = set()
        for _, path in dist.distribute_rule.items():
            if path.startswith(".."):
                dot_dots.add(path)
        log.info("dot_dot_list: %s", dot_dots)
        for path in dot_dots:
            useful_path = path.replace("../", "")
            depots.add(
                f"{config.p4_views['art_resource_root']}/{useful_path}/... //{self._kwargs['client']}/{config.p4_views['art_resource_root'].lstrip('/')}/{useful_path}/..."
            )

        log.info("cdn_rule_depots: %s", depots)
        # self.p4.append_view(list(depots))
        self._submit_p4_views.extend(list(depots))
        utils.add_p4_view_paths(list(depots))
        self.p4_mgr.add_p4_views(list(depots), format_view=False)

    def _fill_const_source_p4_views(self):
        """
        填充常量p4view
        """
        # self.p4.append_view(self._const_source_p4_views)
        self.p4_mgr.add_p4_views(self._const_source_p4_views, format_view=False)

    def _update_p4(self) -> None:
        """更新p4"""
        self._fill_hotfix_p4_views()
        self._fill_cdn_rule_p4_views()
        self._fill_const_source_p4_views()
        self._fill_source_p4_views()
        self.p4_mgr.sync_all(changelist=env_mgr.get_changelist(), force=env_mgr.get_force())

    def update_git_and_p4(self) -> None:
        if not env_mgr.get_need_package():
            return
        self._update_p4()

    def _get_distruibutes(self) -> Tuple:
        cdn_texture_rules, cdn_texture_bases, hotfix_datas, trunk_texture_rules, trunk_texture_bases = {}, {}, {}, {}, {}
        for dist in self._distribute_config:
            if dist.distribute_type == DisTributeEnum.CDNRULE:
                cdn_texture_rules = dist.distribute_rule
            if dist.distribute_type == DisTributeEnum.CDNBASE:
                cdn_texture_bases = dist.distribute_rule
            if dist.distribute_type == DisTributeEnum.HOTFIX:
                hotfix_datas = dist.distribute_rule
            if dist.distribute_type == DisTributeEnum.TRUNKRULE:
                trunk_texture_rules = dist.distribute_rule
            if dist.distribute_type == DisTributeEnum.TRUNKBASE:
                trunk_texture_bases = dist.distribute_rule
        return cdn_texture_rules, cdn_texture_bases, hotfix_datas, trunk_texture_rules, trunk_texture_bases

    def _delete_undefined_resource(self, all_resource: Dict, undefined_resource: Dict, delete_resource: Dict, undefined_path: str) -> None:
        undefined_dir = undefined_path.split("/...")[0]
        for filename in all_resource:
            if filename in undefined_resource:
                continue
            match_rule = os.path.join(self._workspace, undefined_dir.split("//")[-1], filename)
            if os.path.exists(match_rule):
                self.p4_mgr.p4.delete(path=os.path.join(undefined_dir, filename))
                log.info("本次从p4 undefined目录删除的资源为 %s", os.path.join(undefined_dir, filename))
                delete_resource[filename] = match_rule

    def _copy_resource(self) -> None:
        all_resource = {}  # 所有资源
        cdn_resource = {}  # cdn内资源
        hotfix_resource = {}  # 热更内资源
        edition_resource = {}  # 版本内资源
        undefined_resource = {}  # 无规则资源
        repeat_resource = {}  # 重名资源
        delete_resource = {}  # 删除资源
        cdn_texture_rules, cdn_texture_bases, hotfix_datas, trunk_texture_rules, trunk_texture_bases = self._get_distruibutes()

        cdn_path, master_path, branch_path, undefined_path = utils.get_cdn_texture_dirs(self._b_path)
        cdn_path = os.path.join(self._workspace, cdn_path.split("//")[-1].split("/...")[0])
        branch_path = os.path.join(self._workspace, branch_path.split("//")[-1].split("/...")[0])
        # trad 场景不需要处理master
        if master_path:
            master_path = os.path.join(self._workspace, master_path.split("//")[-1].split("/...")[0])

        log.info("cdn_path: %s, master_path: %s, branch_path: %s, undefined_path: %s", cdn_path, master_path, branch_path, undefined_path)

        file_info = env_mgr.get_file_info()
        for filepath, _, filenames in os.walk(f"{self._arthub_dir}{file_info['end_time']}"):
            log.info("filepath: %s, filenames: %s", filepath, filenames)
            for filename in filenames:
                filename = filename.lower()
                # 重名资源进提示名单
                if filename in all_resource.keys():
                    path_mgr.rm(path=all_resource[filename])
                    repeat_resource[filename] = all_resource[filename]
                    del all_resource[filename]
                elif utils.match_black_list(filename):
                    log.info("black list: %s", filename)
                    continue
                elif utils.match_cdn_texture_rule(
                    data=cdn_texture_rules,
                    p4_dir=cdn_path,
                    all_resource=all_resource,
                    type_resource=cdn_resource,
                    filename=filename,
                    filepath=filepath,
                ):
                    log.info("匹配CDN表单规则: %s", filename)
                    continue
                elif utils.match_cdn_texture_base(
                    data=cdn_texture_bases,
                    p4_dir=cdn_path,
                    all_resource=all_resource,
                    type_resource=cdn_resource,
                    filename=filename,
                    filepath=filepath,
                ):
                    log.info("匹配CDN表单基准: %s", filename)
                    continue
                elif utils.match_hotfix_data(
                    data=hotfix_datas,
                    data1=trunk_texture_rules,
                    data2=trunk_texture_bases,
                    all_resource=all_resource,
                    hotfix_resource=hotfix_resource,
                    filename=filename,
                    filepath=filepath,
                    workspace=self._workspace,
                ):
                    log.info("匹配热更资源: %s", filename)
                    continue
                elif utils.match_trunk_texture_rule(
                    data=trunk_texture_rules,
                    master_dir=master_path,
                    branch_dir=branch_path,
                    all_resource=all_resource,
                    edition_resource=edition_resource,
                    filename=filename,
                    filepath=filepath,
                    max_branch=self._b_path,
                ):
                    log.info("匹配版本内表单规则: %s", filename)
                    continue
                elif utils.match_trunk_texture_base(
                    data=trunk_texture_bases,
                    p4_dir=master_path,
                    all_resource=all_resource,
                    type_resource=edition_resource,
                    filename=filename,
                    filepath=filepath,
                ):
                    utils.match_trunk_texture_base(
                        data=trunk_texture_bases,
                        p4_dir=branch_path,
                        all_resource=all_resource,
                        type_resource=edition_resource,
                        filename=filename,
                        filepath=filepath,
                    )
                    log.info("匹配版本内基础资源: %s", filename)
                    continue
                else:
                    log.info("undefined资源: %s", filename)
                    version = undefined_path.split("//")[-1].split("/...")[0]
                    if not env_mgr.get_p4_undefined():
                        utils.copy_resource(self._workspace, version, all_resource, undefined_resource, filepath, filename)
                    else:
                        all_resource[filename] = os.path.join(self._workspace, version, filename)
                        undefined_resource[filename] = os.path.join(self._workspace, version, filename)
        # 删除资源，并记录
        self._delete_undefined_resource(all_resource, undefined_resource, delete_resource, undefined_path)
        # 记录对应的日志
        file_info["all_count"] = str(len(all_resource))
        file_info["repeat_count"] = str(len(repeat_resource))
        env_mgr.set_file_info(file_info)
        # 生成输出日志
        utils.generate_report_log(
            cdn_resource, hotfix_resource, edition_resource, undefined_resource, repeat_resource, delete_resource, self._workspace
        )
        if len(undefined_resource) > 0:
            undefines = []
            for _, path in undefined_resource.items():
                undefines.append(path)
            msg = f"存在未定义文件:\n"
            msg += "\n".join(undefines)
            raise PyframeException(msg)

    def package(self):
        if not env_mgr.get_need_package():
            return
        self._copy_resource()

    def _reconcile(self, cdn_dir: str, master_dir: str, branch_dir: str, undefined_dir: str) -> List:
        _path_list = utils.get_p4_view_paths()
        _path_list.extend(self._submit_p4_views)
        log.info("submit p4 views: %s", self._submit_p4_views)
        self.p4_mgr.set_p4_views(list(set(_path_list)), format_view=False)
        reconciles = []
        ret_cdn = self.p4_mgr.p4.reconcile(path=cdn_dir)
        if ret_cdn:
            reconciles.extend(ret_cdn)
        for h_dir in env_mgr.get_hotfix_dir_list():
            ret_hotfix = self.p4_mgr.p4.reconcile(path=h_dir)
            if ret_hotfix:
                reconciles.extend(ret_hotfix)
        # 如果master存在，做处理
        if master_dir:
            ret_master = self.p4_mgr.p4.reconcile(path=master_dir)
            if ret_master:
                reconciles.extend(ret_master)
        ret_branch = self.p4_mgr.p4.reconcile(path=branch_dir)
        if ret_branch:
            reconciles.extend(ret_branch)
        ret_undefined = self.p4_mgr.p4.reconcile(path=undefined_dir)
        if ret_undefined:
            reconciles.extend(ret_undefined)
        return reconciles

    def _compose_desc(self) -> str:
        # 原始资源提交人、流水线名称、流水线编号、流水线链接
        users = env_mgr.get_users()
        desc = f"--Auto--| uploader: {','.join(users)} | "
        desc += f"Identity:#Jenkins#{env.pipeline.function_name()}#{env.pipeline.build_num()} | "
        desc += f"link:{env.pipeline.build_url()}"
        return desc

    def _submit_p4(self):
        _dir_list = utils.get_cdn_texture_dirs(self._b_path)
        # 匹配到dir
        cdn_dir, master_dir, branch_dir, undefined_dir = _dir_list
        reconciles = self._reconcile(cdn_dir, master_dir, branch_dir, undefined_dir)
        log.info(
            "reconcile done, cdn_dir: %s, master_dir: %s, branch_dir: %s, undefined_dir: %s, hotfix_dir: %s",
            cdn_dir,
            master_dir,
            branch_dir,
            undefined_dir,
            env_mgr.get_hotfix_dir_list(),
        )
        log.info("reconciles: %s", reconciles)

        file_info = env_mgr.get_file_info()
        if reconciles:
            try:
                ret_submit = self.p4_mgr.p4.submit(self._compose_desc(), revert_if_failed=True)
                log.info("实际上传p4资源情况如下: %s", ret_submit)
                # 拼接日志
                utils.add_log("实际上传p4资源情况如下:", ret_submit)
                file_info["p4_count"] = str(len(reconciles))
                file_info["changelist"] = str(ret_submit[0]["change"])
                art_id_list = []
                for ret in ret_submit:
                    # NOTE: 会出现乱码字符串，而非字典格式
                    # 非字典格式，则跳过
                    if not isinstance(ret, Dict):
                        continue
                    fp = ret.get("depotFile")
                    if not fp:
                        continue
                    art_id_list.append(fp.split("/")[-1].split(".")[0])
                utils.set_upload_p4_filenames(art_id_list)
            except Exception as e:
                if "该文件已经被锁定，无法进行修改或者操作，请联系PM进行解锁" in str(e):
                    mentioned_list = env_mgr.submitters
                    mentioned_list.append("<EMAIL>")
                    mentioned_list.append("<EMAIL>")
                else:
                    mentioned_list = MAINTAINER_LIST
                raise PyframeException(message=str(e))
        else:
            nothing_msg = "没有改动的文件, 不需要提交"
            log.info(nothing_msg)
            # 兼容处理
            file_info["p4_count"] = "0"
            utils.add_log(nothing_msg)
        env_mgr.set_file_info(file_info)

    def submit(self):
        if not env_mgr.get_need_package():
            return
        self._submit_p4()
        s_changelist = env_mgr.get_changelist()
        global_env_mgr.set_last_changelist(resource=self._kind, changelist=s_changelist)

    def always(self):
        if not env_mgr.get_need_package():
            return
        self._clean()
        # 上传日志，便于通知后的查询
        utils.upload_log()

    def get_msg(self, pipeline_status: Optional[str] = None) -> Tuple:
        msg = ""
        file_info = env_mgr.get_file_info()
        filenames = utils.get_upload_p4_filenames()
        if file_info:
            msg += f"**arthub得到的图片数量**: {file_info['all_count']}\n"
            msg += f"**实际上传p4的图片数量**: {file_info['p4_count']}\n"
            if file_info["repeat_count"] != "0":
                msg += f"**异常图片数量**: {file_info['repeat_count']}\n"
            msg += f"**实际上传p4的图片ID**: {','.join(filenames)}\n"
        log_url = utils.get_log_url()
        if log_url:
            log_name = log_url.split("/")[-1]
            msg += f"**分发报告:** [{log_name}]({log_url})\n"
        submitters = env_mgr.get_users()
        if submitters:
            submitters = list(set(submitters))
            submitters_str = ",".join(submitters)
            msg += f"**提交人:** {submitters_str}\n"

        return msg
