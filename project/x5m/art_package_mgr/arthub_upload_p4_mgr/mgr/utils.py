import os
from typing import Dict, List, Optional
from fnmatch import fnmatchcase as match

from frame import *

from project.x5m.art_package_mgr.base_mgr.package_mgr import config, LanguageEnum
from project.x5m.art_package_mgr.base_mgr.env_mgr import env_mgr


from . import DIR_PREFIX


def get_resource_dir_prefix(language: str = LanguageEnum.CHIN_SIMP.value) -> str:
    """根据语言获取资源路径"""
    log.info("current language: %s, type_is: %s", language, type(language))
    if language == LanguageEnum.CHIN_TRAD:
        return "//美术资源/炫舞手游-ui/7-UI常规资源_trad/新常规资源"
    return "//美术资源/炫舞手游-ui/6-UI常规资源/新常规资源"


def get_cdn_texture_dirs(max_branch: str) -> List[str]:
    p4_view_attr = "p4_views"
    master_dir = f"{getattr(config, p4_view_attr, {}).get('resources_root').format(branch='Resources')}/art_src{DIR_PREFIX}/..."
    if env_mgr.get_language() == LanguageEnum.CHIN_TRAD:
        p4_view_attr = "p4_trad_views"
        # 繁中不处理master分支
        master_dir = ""

    return [
        f"{getattr(config, p4_view_attr, {}).get('art_resource_root')}{DIR_PREFIX}/...",
        master_dir,
        f"{getattr(config, p4_view_attr, {}).get('resources_root').format(branch='b/'+max_branch)}/art_src{DIR_PREFIX}/...",
        f"{getattr(config, p4_view_attr, {}).get('resources_root').format(branch='art_release')}/undefined/...",
    ]


def render_source_p4_view_list(client: str, b_path: str) -> List[str]:
    """渲染源文件P4视图列表"""
    _path_list = get_cdn_texture_dirs(b_path)
    # 映射处理
    ret = []
    for path in _path_list:
        if not path:
            continue
        ret.append(f"{path} //{client}/{path.split('//')[-1]}")
    return ret


def match_black_list(filename: str) -> bool:
    """判断是否再忽略的名单中"""
    black_list = env_mgr.get_exclude_file()

    for i in black_list:
        if match(filename, i):
            return True
    return False


def copy_resource(p4_dir: str, version: str, all_resource: dict, type_resource: dict, filepath: str, filename: str) -> None:
    path_mgr.mkdir(os.path.join(p4_dir, version))
    path_mgr.copy(src=os.path.join(filepath, filename), dst=os.path.join(p4_dir, version, filename.lower()))
    all_resource[filename] = os.path.join(p4_dir, version, filename.lower())
    type_resource[filename] = os.path.join(p4_dir, version, filename.lower())


def match_cdn_texture_rule(data: Dict, p4_dir: str, all_resource: dict, type_resource: dict, filename: str, filepath: str) -> bool:
    dir_name = ""
    for name, path in data.items():
        if match(filename, f"{name.lower()}.png"):
            if filename.startswith("zs_9261"):
                dir_name = filename[3:12]

            if path.startswith("../"):
                path = path.replace("../", "")

            version = os.path.join(path, dir_name)
            copy_resource(p4_dir, version, all_resource, type_resource, filepath, filename)
            return True
    return False


def match_cdn_texture_base(data: Dict, p4_dir: str, all_resource: dict, type_resource: dict, filename: str, filepath: str) -> bool:
    for name, path in data.items():
        if filename != f"{name.lower()}.png":
            continue
        copy_resource(p4_dir, path, all_resource, type_resource, filepath, filename)
        return True
    return False


def match_hotfix_data(
    data: Dict, data1: Dict, data2: Dict, all_resource: dict, hotfix_resource: dict, filename: str, filepath: str, workspace: str
) -> bool:
    # 获取不带路径的资源名
    _filename, _ = os.path.splitext(filename)
    _filename = _filename.split("/")[-1]

    for name, _hotfix_path in data.items():
        if _filename != name.lower():
            continue
        path = ""
        _path = "\\".join(_hotfix_path.split("/")[2:])
        hotfix_path = os.path.join(workspace, f"{_path}\\art_src\\texture")
        for data1_name, data1_path in data1.items():
            if match(_filename, data1_name.lower()):
                path = data1_path
        for data2_name, data2_path in data2.items():
            if _filename == data2_name.lower():
                path = data2_path
        copy_resource(hotfix_path, path, all_resource, hotfix_resource, filepath, filename)
        return True
    return False


def match_trunk_texture_rule(
    data: Dict, master_dir: str, branch_dir: str, all_resource: dict, edition_resource: dict, filename: str, filepath: str, max_branch: str
) -> bool:
    max_branch = max_branch.replace(".", "")
    for name, path in data.items():
        name += ".png"
        if not match(filename, name.lower()):
            continue
        if filename[0:4].isdigit() and filename[4:5] == "_":
            version_main = name[0:4]
            if int(version_main) > int(max_branch):
                _branch = master_dir
            else:
                _branch = branch_dir
            copy_resource(_branch, path, all_resource, edition_resource, filepath, filename)
            return True
        else:
            copy_resource(master_dir, path, all_resource, edition_resource, filepath, filename)
            copy_resource(branch_dir, path, all_resource, edition_resource, filepath, filename)
            return True
    return False


def match_trunk_texture_base(data: Dict, p4_dir: str, all_resource: dict, type_resource: dict, filename: str, filepath: str) -> bool:
    for name, path in data.items():
        if filename != f"{name.lower()}.png":
            continue
        copy_resource(p4_dir, path, all_resource, type_resource, filepath, filename)
        return True
    return False


def set_report_log(report_log: str) -> None:
    """设置日志环境变量"""
    return env.set({"report_log": report_log})


def get_report_log() -> str:
    report_log = env.get("report_log", "")
    if not report_log:
        log.error("report_log is null")
    return report_log


def add_p4_view_paths(path_list: List[str]) -> None:
    _path = env.get("p4_view_paths") or []
    _path.extend(path_list)
    return env.set({"p4_view_paths": _path})


def get_p4_view_paths() -> List[str]:
    return env.get("p4_view_paths") or []


def generate_report_log(
    cdn_resource: dict,
    hotfix_resource: dict,
    edition_resource: dict,
    undefined_resource: dict,
    repeat_resource: dict,
    delete_resource: dict,
    workspace: str,
) -> None:
    """生成日志文件，用于输出和通知使用"""
    log_dir = os.path.join(workspace, "report_log")
    path_mgr.mkdir(log_dir)

    error_resource = {}
    for unkey in undefined_resource.keys():
        if not unkey.endswith(".png"):
            error_resource[unkey] = undefined_resource[unkey]

    def write_txt(src_dict: Dict, file):
        file.write(f"资源数量：{len(src_dict)}\n")
        if len(src_dict) != 0:
            for key, value in src_dict.items():
                file.write(f"文件名：{key}  文件路径：{value}\n")
        file.write("\n")

    log_file = os.path.join(log_dir, "record.txt")
    file = open(log_file, "w+", encoding="GBK")
    file.write("本地资源的分发情况如下：\n\n")
    file.write("1.未定义规则的资源分发情况：\n")
    write_txt(undefined_resource, file)
    file.write("2.重名资源的分发情况：\n")
    write_txt(repeat_resource, file)
    file.write("3.新符合规则资源的分发情况：\n")
    write_txt(delete_resource, file)
    file.write("4.cdn内资源的分发情况：\n")
    write_txt(cdn_resource, file)
    file.write("5.热更内资源的分发情况：\n")
    write_txt(hotfix_resource, file)
    file.write("6.版本内资源的分发情况：\n")
    write_txt(edition_resource, file)
    file.close()
    set_report_log(log_file)


def add_log(title: str, content: Optional[str] = None) -> None:
    with open(get_report_log(), "a", encoding="GBK", errors="ignore") as f:
        f.write(f"\n{title}\n\n")
        # 如果不需要处理，则直接返回
        if not content:
            return
        for data in content:
            f.write(f"{data}\n")


def get_log_url() -> str:
    return env.get("log_url")


def set_log_url(log_url: str) -> None:
    return env.set({"log_url": log_url})


def set_upload_p4_filenames(name_list: List) -> None:
    return env.set({"upload_p4_filenames": name_list})


def get_upload_p4_filenames() -> List:
    return env.get("upload_p4_filenames") or []


def set_space_files(path: List) -> None:
    return env.set({"space_paths": path})


def get_space_files() -> List:
    return env.get("space_paths") or []


def set_not_png_files(path: List) -> None:
    return env.set({"not_png_files": path})


def get_not_png_files() -> List:
    return env.get("not_png_files") or []


def upload_log() -> None:
    """上传日志"""
    log_path = get_report_log()
    if not log_path:
        return
    log_url = advance.upload_pipeline_log(path=log_path)
    log.info("upload log success, log_url: %s", log_url)
    set_log_url(log_url)


def process_user_list(users: List[str]) -> List[str]:
    """用户邮箱特殊处理"""
    ret = []
    for user in users:
        # 特殊处理
        if user == "<EMAIL>":
            ret.append("<EMAIL>")
        else:
            ret.append(user)
    return ret
