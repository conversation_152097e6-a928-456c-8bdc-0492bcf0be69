from project.x5m import Config
from frame import common, env
import os


class PackageConfig(Config):
    # 已上线资源打包流水线，走正式环境
    release_resource = {
        "timeline",
        "npc",
        "bodypart",
        "theme",
        "link_distrib",
        "island_distrib",
        "scene",
        "house_scene",
        "stream_scene",
        "chd",
        "house_distrib",
        "model",
        "pet",
        "chair",
        "effect",
        "action_distrib",
        "beast",
        "music_action_distrib",
        "camera_distrib",
        "camera_action",
        "camera_effect",
        "link_unencrypt",
        "arthub_upload_p4",
        "card",
        "makeup_distrib",
        "game_icon_upload_git",
        "ingame_prefabs",
        "texture",
        "edition_effect",
        "art_cdn",
        "online_update_online_texture",
        "online_update_release_texture",
        "online_update_resources_texture",
        "music_effect",
        "online_update_texture",
    }
    # 鸿蒙走测试环境
    dev_platform = {
        "openharmony",
    }

    is_dev = common.str2bool(env.get("DEV", True))
    if env.get("RESOURCE", "") in release_resource:
        is_dev = False
    if os.environ.get("PLATFORM") in dev_platform:
        is_dev = True
    language = env.get("LANGUAGE", "chin_simp")
    p4_view_lang_flag = "" if language == "chin_simp" else "_trad"

    # 简中路径
    p4_views = {
        "cdn_root": f"//x5m/res{p4_view_lang_flag}/cdn",
        "art_resource_root": f"//x5_mobile/mr{p4_view_lang_flag}/art_release/art_src",
        "resources_root": f"//x5_mobile/mr{p4_view_lang_flag}/{{branch}}",
        "hotfix_root": "//x5_mobile/mr/onlineupdate/{branch}/{sub_branch}",
        "resmg_root": "//x5mplan/resmg",
        "cdn_resources": "//x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles",
        "onlineupdate_root": f"//x5_mobile/mr{p4_view_lang_flag}/onlineupdate",
        "art_resources_src": f"//x5_mobile/mr{p4_view_lang_flag}/art_resource/art_src",
        "branch_resource_src": f"//x5_mobile/mr{p4_view_lang_flag}/{{branch}}/art_src",
        "branch_cdn_resources": f"//x5_mobile/mr{p4_view_lang_flag}/{{branch}}/ResourcePublish/CDN/SourceFiles",
    }

    # 繁中路径
    p4_trad_views = {
        "cdn_root": "//x5m/res_trad/cdn",
        "art_resource_root": "//x5_mobile/mr_trad/art_release/art_src",
        "resources_root": "//x5_mobile/mr_trad/{branch}",
        "art_resources_src": "//x5_mobile/mr_trad/art_resource/art_src",
        "branch_resource_src": "//x5_mobile/mr_trad/{branch}/art_src",
        "branch_cdn_resources": "//x5_mobile/mr_trad/{branch}/ResourcePublish/CDN/SourceFiles",
    }

    if is_dev:
        p4_views.update(
            {
                "cdn_root": "//x5m/res/cdn_harmony",
            }
        )

    arttrunk_branch = "release"

    disk_threshold = 30  # 磁盘空间阈值,默认30G

    back_up_days = 100  # 中间产物备份保留天数

    max_changelist_size = 1000  # 每次最多获取的changelist数量

    debug_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c6204240-5b16-4d57-a81d-0945b4a9a39e"

    """
    分发相关配置
    """
    cdn_rule_file_map = {
        "action_distrib": "act/cdn_action-rule.xlsx",
        "model": "model/cdn-model-rule.xlsx",
        "music_action_distrib": "act/cdn_action-rule.xlsx",
        "camera_distrib": "camera/cdn-camera-rule.xlsx",
        "arthub_upload_p4": "ui/cdn-texture-rule.xlsx",
        "makeup_distrib": "makeup/cdn-makeup-rule.xlsx",
    }
    cdn_base_file_map = {
        "action_distrib": "act/cdn-action-base.xlsx",
        "model": "model/cdn-model-base.xlsx",
        "music_action_distrib": "act/cdn-action-base.xlsx",
        "camera_distrib": "camera/cdn-camera-base.xlsx",
        "arthub_upload_p4": "ui/cdn-texture-base.xlsx",
    }
    trunk_rule_file_map = {
        "arthub_upload_p4": "ui/trunk-texture-rule.xlsx",
    }
    turnk_base_file_map = {
        "link_distrib": "link/trunk-link-base.xlsx",
        "house_distrib": "house/trunk-house-base.xlsx",
        "arthub_upload_p4": "ui/trunk-texture-base.xlsx",
    }
    hotfix_distrib_file_map = {
        "action_distrib": "act/hotfix-action-base.xlsx",
        "camera_distrib": "camera/hotfix-camera-rule.xlsx",
        # "chd": "chd/hotfix-action-base.xlsx",
        "house_distrib": "house/hotfix-house-rule.xlsx",
        "island_distrib": "island/hotfix-small-island-rule.xlsx",
        "link_distrib": "link/hotfix-link-rule.xlsx",
        "makeup_distrib": "makeup/hotfix-makeup-rule.xlsx",
        "musicact_action_distrib": "act/hotfix-action-base.xlsx",
        "model": "model/hotfix-model-rule.xlsx",
        # "npc": "npc/hotfix-action-base.xlsx",
        "house_scene": "scene/hotfix-house-scene.xlsx",
        "scene": "scene/hotfix-scene.xlsx",
        "texture": "ui/hotfix-texture-rule.xlsx",
        "theme": "emoticon/hotfix-emo-rule.xlsx",
        "music_action_distrib": "act/hotfix-action-base.xlsx",
        "arthub_upload_p4": "ui/hotfix-texture-rule.xlsx",
        "edition_effect": "resource_effect/hotfix-effect-rule.xlsx",
    }
    special_file_map = {
        "link_distrib": "link/special-branch-link-rule.xlsx",
        "island_distrib": "island/special-branch-small-island-rule.xlsx",
        "scene": "scene/special-branch-scene.xlsx",
        "action_distrib": "act/special-branch-action-rule.xlsx",
        "camera_distrib": "camera/special-camera-map.xlsx",
        "ingame_prefabs": "ingame_prefabs/special-rule-map.xlsx",
    }

    # 上传操作的类型标识
    upload_resource_list = ["arthub_upload_p4", "game_icon_upload_git"]
    # 针对流水线需要打包的额外通知人
    notice_extra_user_list = ["<EMAIL>", "<EMAIL>"]


config = PackageConfig()
