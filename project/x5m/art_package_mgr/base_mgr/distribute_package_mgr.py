import collections

import xlrd
from fnmatch import fnmatchcase as fnmatch

from frame import *
from project.x5m.art_package_mgr.base_mgr import config
from project.x5m.art_package_mgr.base_mgr.package_mgr import *


class DistributeMgr:
    def __init__(self, distribute_type: DisTributeEnum, distribute_view: str, p4_mgr: P4Mgr):
        # 分发类型
        self.distribute_type = distribute_type
        # 分发配置文件
        self.distribute_view = f"{config.p4_views.get('resmg_root')}/{distribute_view}"
        self.distribute_file = p4_mgr.parse_p4view_to_localpath(self.distribute_view)
        # 分发规则
        self.distribute_rule = {}
        self.__p4_mgr = p4_mgr
        self.__sync()
        self.__load_distribute_rule()

    def __load_distribute_rule(self):
        if self.distribute_type == DisTributeEnum.HOTFIX:
            self.__load_hotfix_rule()
        if self.distribute_type == DisTributeEnum.TRUNKBASE:
            self.__load_trunk_base_rule()
        if self.distribute_type == DisTributeEnum.TRUNKRULE:
            self.__load_trunk_rule()
        if self.distribute_type == DisTributeEnum.SPECIAL:
            self.__load_special_branch_rule()
        if self.distribute_type == DisTributeEnum.CDNBASE:
            self.__load_cdn_base()
        if self.distribute_type == DisTributeEnum.CDNRULE:
            self.__load_cdn_rule()

    def __load_hotfix_rule(self):
        log.info(f"hotfix_distrib_file_path: {self.distribute_file}")
        data = xlrd.open_workbook(self.distribute_file)
        table = data.sheets()[0]
        first_row = table.row_values(0)
        if first_row and first_row[0] not in ["name", "资源名"] and first_row[1] not in ["path", "版本号"]:
            log.warning("热更分发表格式不正确，第一行第一列的值不等于name，且第二列的值不等于path")
            return None
        for i in range(1, table.nrows):
            name, version = table.row_values(i, 0, 2)
            if isinstance(name, float):
                name = str(name)[:-2]
            version = str(version)
            big_edit = version[0] + "." + version[1:3] + ".0"
            small_edit = version + "000"
            _path = config.p4_views.get("hotfix_root").format(branch=big_edit, sub_branch=small_edit)
            self.distribute_rule[name.strip()] = _path
            self.distribute_rule[f"??00000101_{name.strip()}"] = _path

    def __load_trunk_base_rule(self):
        log.info(f"trunk_base_distrib_file_path: {self.distribute_file}")
        data = xlrd.open_workbook(self.distribute_file)
        table = data.sheets()[0]
        for i in range(1, table.nrows):
            config_data = table.row_values(i, 0, 2)
            name = str(config_data[0]).strip().replace("#", "[0-9]")
            try:
                path = str(config_data[1]).strip()
            except Exception:
                path = True
            self.distribute_rule[name] = path

    def __load_trunk_rule(self):
        log.info("trunk_rule_path: %s", self.distribute_file)
        data = xlrd.open_workbook(self.distribute_file)
        table = data.sheets()[0]
        for i in range(1, table.nrows):
            # NOTE: 这里取得是第3列，因为第2列是功能说明
            # 这里需要优化，可以支持传递对应的列数，避免不同类型获取数据不一样
            config_data = table.row_values(i, 0, 3)
            name = str(config_data[0]).strip().replace("#", "[0-9]")
            try:
                path = str(config_data[2]).strip()
            except Exception:
                path = True
            self.distribute_rule[name] = path

    def __load_special_branch_rule(self):
        log.info(f"special_branch_distrib_file_path: {self.distribute_file}")
        data = xlrd.open_workbook(self.distribute_file)
        table = data.sheets()[0]
        first_row = table.row_values(0)
        # NOTE: 兼容特殊文件的处理，有些标头使用的是中文
        if first_row and first_row[0] not in ["name", "文件名称"] and first_row[1] not in ["path", "分支名称"]:
            log.warning("特殊分发表格式不正确，第一行第一列的值不等于name，且第二列的值不等于path")
            return None
        for i in range(1, table.nrows):
            config_data = table.row_values(i, 0, 2)
            name = config_data[0]
            if isinstance(name, float):
                name = str(name)[:-2]
            name = str(name).strip().replace("#", "[0-9]")
            self.distribute_rule[name] = str(config_data[1]).strip()
            self.distribute_rule[f"??00000101_{name}"] = str(config_data[1]).strip()

    def __load_cdn_base(self):
        log.info("load cdn base %s", self.distribute_file)
        data = xlrd.open_workbook(self.distribute_file)
        table = data.sheets()[0]
        for i in range(1, table.nrows):
            config_data = table.row_values(i, 0, 2)
            name = str(config_data[0]).strip()
            try:
                path = str(config_data[1]).strip()
            except Exception:
                path = True
            self.distribute_rule[name] = path

    def __load_cdn_rule(self):
        log.info("load cdn rule %s", self.distribute_file)
        data = xlrd.open_workbook(self.distribute_file)
        table = data.sheets()[0]
        for i in range(1, table.nrows):
            config_data = table.row_values(i, 0, 2)
            name = str(config_data[0]).strip().replace("#", "[0-9]")
            try:
                path = str(config_data[1]).strip()
            except Exception:
                path = True
            self.distribute_rule[name] = path
            self.distribute_rule[f"??00000101_{name}"] = path

    def __sync(self):
        self.__p4_mgr.set_p4_views(self.distribute_view)
        self.__p4_mgr.p4.sync_all(force=True)

    def match(self, filename: str):
        if not self.distribute_rule:
            self.__load_distribute_rule()
        if self.distribute_type == DisTributeEnum.CDNBASE:
            return self._match_cdn_base(filename)
        if self.distribute_type == DisTributeEnum.CDNRULE:
            return self._match_cdn_rule(filename)
        if self.distribute_type == DisTributeEnum.TRUNKBASE:
            return self._match_trunk_base(filename)
        if self.distribute_type == DisTributeEnum.TRUNKRULE:
            return self._match_trunk_rule(filename)
        if self.distribute_type == DisTributeEnum.HOTFIX:
            return self._match_hotfix_rule(filename)
        if self.distribute_type == DisTributeEnum.SPECIAL:
            return self._match_special_branch_rule(filename)
        return False

    def _match_cdn_base(self, filename):
        ret = self.distribute_rule.get(filename, False)

        return f"{config.p4_views.get('cdn_root')}" if ret else ret

    def _match_cdn_rule(self, filename):
        data_list = self.distribute_rule.keys()
        for data in data_list:
            if fnmatch(filename, data):
                return f"{config.p4_views.get('cdn_root')}"
        return False

    def _match_trunk_base(self, filename):
        data_list = self.distribute_rule.keys()
        for data in data_list:
            if fnmatch(filename, data):
                return True
        return False

    def _match_trunk_rule(self, filename):
        return False

    def _match_hotfix_rule(self, filename):
        log.info(f"hotfix distribute_rule: {self.distribute_rule}")
        # TODO: 这里多次全匹配，会很慢，建议优化(其它类似)
        for key, val in self.distribute_rule.items():
            if fnmatch(filename, key):
                return val
        return False

    def _match_special_branch_rule(self, filename):
        log.info(f"special branch distribute_rule: {self.distribute_rule}")
        for key, val in self.distribute_rule.items():
            if fnmatch(filename, key):
                return val
        return False

    def __str__(self):
        return f"{self.distribute_type} mgr"


class DistributePackageMgr(PackageMgr):
    def __init__(self, kind, *args, **kwargs) -> None:
        super().__init__(kind, *args, **kwargs)
        self._distribute_ab_views = []
        self._distribute_config: List[DistributeMgr] = []
        self._submit_ab_views = []
        self._distribute_map = collections.defaultdict(list)
        for dis in DisTributeEnum:
            distribute_file = dis.distribute_file(self._kind)
            if distribute_file:
                self._distribute_config.append(DistributeMgr(dis, distribute_file, self.p4_mgr))

    def _other_check(self):
        self.__distribute_precheck()

    def __distribute_precheck(self):
        """
        分发热更资源前预检查
        """
        hotfix_distrib = None
        for distribute in self._distribute_config:
            if distribute.distribute_type == DisTributeEnum.HOTFIX:
                hotfix_distrib = distribute
                break
        else:
            return
        err = []
        for path in hotfix_distrib.distribute_rule.values():
            p4_path = f"{path}*"
            if not self.p4.dirs(p4_path):
                err.append(f"p4 path {p4_path} not exists or empty")
        if err:
            err_msg = "\n".join(err)
            raise PyframeException(f"分发资源路径不存在: {err_msg}")

    def _dist_ab(self):
        self._get_distruibute_dict()
        self._sync_dist()
        self._dist_ab_file()

    def _sync_dist(self):
        self._fill_dist_p4_views()
        self.p4_mgr.sync_all(force=True)

    def _fill_dist_p4_views(self):
        pass

    def _dist_ab_file(self):
        pass

    def _get_distruibute_dict(self):
        success_ids = env_mgr.get_report().get("success_ids")
        dist_destinations = collections.defaultdict(list)
        for id in success_ids:
            for distribute in self._distribute_config:
                ret = distribute.match(self._parse_id_to_filename(id))
                if ret:
                    self._distribute_map[distribute.distribute_type].append((id, [ret]))
                    if distribute.distribute_type == DisTributeEnum.SPECIAL:
                        dist_destinations[f"{str(distribute.distribute_type)}({ret})"].append(id)
                        self._is_special_branch = True
                    else:
                        dist_destinations[str(distribute.distribute_type)].append(id)
                    break
            else:
                self._distribute_map[DisTributeEnum.DEFAULT].append((id, self._default_distribute()))
                dist_destinations[str(DisTributeEnum.DEFAULT)].append(id)
        log.info(f"dist_destinations: {dist_destinations}")
        env_mgr.set_dist_destinations(dist_destinations)
        return self._distribute_map

    def _default_distribute(self) -> List[str]:
        return [f"{config.p4_views['resources_root'].format(branch='Resources')}"]

    def _parse_id_to_filename(self, id: str) -> str:
        return id

    def _get_submit_p4_view_ab(self):
        return self._submit_ab_views
