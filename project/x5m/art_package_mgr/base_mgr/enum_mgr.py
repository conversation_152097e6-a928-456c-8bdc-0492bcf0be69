from enum import Enum
from typing import Optional

from frame import PyframeException
from project.x5m.art_package_mgr.base_mgr import config


class PlatformEnum(Enum):
    ANDROID = "android"
    IOS = "ios"
    OPENHARMONY = "openharmony"

    def __str__(self):
        return self.value

    @staticmethod
    def get_platform(platform: str):
        platform = platform.lower()
        for platform_enum in PlatformEnum:
            if platform_enum.value == platform:
                return platform_enum
        raise PyframeException(f"不支持的平台{platform}")

    @property
    def package_tool(self):
        if self == PlatformEnum.OPENHARMONY:
            return "tuanjie"
        return "Unity"

    @property
    def build_target(self):
        return self.value


class LanguageEnum(Enum):
    CHIN_SIMP = "chin_simp"
    CHIN_TRAD = "chin_trad"

    def __str__(self) -> str:
        return self.value

    @staticmethod
    def get_language(language: str):
        language = language.lower()
        for language_enum in LanguageEnum:
            if language_enum.value == language:
                return language_enum
        raise PyframeException(f"不支持的语言{language}")

    @property
    def build_language(self):
        return self.value


class FileEnum(Enum):
    PNG = ".png"
    JPG = ".jpg"
    JPEG = ".jpeg"
    OTHER = "other"
    DIGIT = "digit"

    def __str__(self) -> str:
        return self.name.lower()

    @staticmethod
    def get_file_type(file: str):
        file = file.lower()
        if file.isdigit():
            return FileEnum.DIGIT
        for file_type in FileEnum:
            if file.endswith(file_type.value):
                return file_type
        return FileEnum.OTHER

    @property
    def is_image(self):
        return self in {FileEnum.PNG, FileEnum.JPG, FileEnum.JPEG}

    @property
    def file_name_is_digit(self):
        return self == FileEnum.DIGIT

    @property
    def max_filesize(self):
        # 图片类型的阈值时30M其他的不知道，暂时也设置为30M
        if self.is_image:
            # 30M
            return 30
        if self.file_name_is_digit:
            return 1
        return 30


class ResourceTypeEnum(Enum):
    TIMELINE = "timeline"
    NPC = "npc"
    BODYPART = "bodypart"
    THEME = "theme"
    LinkDistrib = "link_distrib"
    IslandDistrib = "island_distrib"
    Scene = "scene"
    HouseScene = "house_scene"
    ACTION_DISTRIB = "action_distrib"
    StreamScene = "stream_scene"
    CHD = "chd"
    HouseDistrib = "house_distrib"
    Model = "model"
    Pet = "pet"
    Chair = "chair"
    Effect = "effect"
    Beast = "beast"
    MUSIC_ACTION_DISTRIB = "music_action_distrib"
    CAMERA_DISTRIB = "camera_distrib"
    CAMERA_ACTION = "camera_action"
    CAMERA_EFFECT = "camera_effect"
    LINK_UNENCRYPT = "link_unencrypt"
    ARTHUB_UPLOAD_P4 = "arthub_upload_p4"
    CARD = "card"
    MAKEUP_DISTRIB = "makeup_distrib"
    GAME_ICON_UPLOAD_GIT = "game_icon_upload_git"
    INGAME_PREFABS = "ingame_prefabs"
    TEXTURE = "texture"
    EDITION_EFFECT = "edition_effect"
    ART_CDN = "art_cdn"
    ONLINE_UPDATE_ONLINE_TEXTURE = "online_update_online_texture"
    ONLINE_UPDATE_RELEASE_TEXTURE = "online_update_release_texture"
    ONLINE_UPDATE_RESOURCES_TEXTURE = "online_update_resources_texture"
    MUSIC_EFFECT = "music_effect"
    ONLINE_UPDATE_TEXTURE = "online_update_texture"
    CHANGELIST_INITIALIZATION = "changelist_initialization"
    KSong = "ksong"

    def __str__(self) -> str:
        return self.value

    @staticmethod
    def get_resource_type(resource: str):
        resource = resource.lower()
        for resource_type in ResourceTypeEnum:
            if resource_type.value == resource:
                return resource_type
        raise PyframeException(f"不支持的打包资源类型{resource}")

    @property
    def hotfix_distrib_file(self):
        if self.value in config.hotfix_distrib_file_map:
            return config.hotfix_distrib_file_map[self.value]
        return None

    @property
    def cdn_rule_file(self):
        return config.cdn_rule_file_map.get(self.value)

    @property
    def cdn_base_file(self):
        return config.cdn_base_file_map.get(self.value)

    @property
    def trunk_rule_file(self):
        return config.trunk_rule_file_map.get(self.value)

    @property
    def trunk_base_file(self):
        return config.turnk_base_file_map.get(self.value)

    @property
    def special_file(self):
        return config.special_file_map.get(self.value)


class DisTributeEnum(Enum):
    CDNBASE = 0
    CDNRULE = 1
    HOTFIX = 2
    TRUNKRULE = 3
    TRUNKBASE = 4
    SPECIAL = 5
    DEFAULT = 6

    def __str__(self):
        return self.name.lower()

    def __repr__(self) -> str:
        return self.name.lower()

    def distribute_file(self, source: ResourceTypeEnum) -> Optional[str]:
        if self == DisTributeEnum.CDNBASE:
            return source.cdn_base_file
        if self == DisTributeEnum.CDNRULE:
            return source.cdn_rule_file
        if self == DisTributeEnum.HOTFIX:
            return source.hotfix_distrib_file
        if self == DisTributeEnum.TRUNKBASE:
            return source.trunk_base_file
        if self == DisTributeEnum.TRUNKRULE:
            return source.trunk_rule_file
        if self == DisTributeEnum.SPECIAL:
            return source.special_file
        return None


class PipelineUpdateTypeEum(Enum):
    TRUNK = "主支"
    BRANCH = "分支"
    WEEK_UPDATE = "周更"
