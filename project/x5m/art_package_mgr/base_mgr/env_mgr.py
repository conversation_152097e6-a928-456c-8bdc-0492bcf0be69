import os
from typing import Dict, List
from frame import env, common
from project.x5m.art_package_mgr.base_mgr.enum_mgr import PlatformEnum, LanguageEnum, ResourceTypeEnum
from project.x5m.art_package_mgr.base_mgr import config


class GlobalEnvMgr:
    @staticmethod
    def set_last_changelist(resource: ResourceTypeEnum, changelist: str):
        if not changelist.isdigit():
            return
        function_name = env.pipeline.function_name()
        last_changelist = GlobalEnvMgr.get_last_changelist(resource)
        platform = EnvMgr.get_platform()
        if not last_changelist:
            env.set_global({f"{function_name}-{resource}-{platform}": str(changelist)})
            return
        last_changelist = int(last_changelist)
        changelist = int(changelist)
        if changelist < last_changelist:
            return
        # changelist = min(changelist, last_changelist + config.max_changelist_size)
        env.set_global({f"{function_name}-{resource}-{platform}": str(changelist)})

    @staticmethod
    def get_last_changelist(resource: ResourceTypeEnum):
        platform = EnvMgr.get_platform()
        function_name = env.pipeline.function_name()
        return env.get_global(f"{function_name}-{resource}-{platform}")


class EnvMgr:
    @staticmethod
    def get_workspace():
        return os.path.abspath(env.pipeline.workspace())

    @staticmethod
    def set_ip():
        platform = EnvMgr.get_platform()
        ip = common.get_host_ip()
        env.set({f"{platform}_ip": ip})

    @staticmethod
    def get_ip(platform: str = None) -> str:
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_ip", "")

    @staticmethod
    def is_dev() -> bool:
        """
        是否为开发环境,默认为true
        return: bool default: True
        """
        return config.is_dev

    @staticmethod
    def get_first_changelist():
        return env.get("FIRST_CHANGELIST", "-1")

    @staticmethod
    def get_language():
        language = env.get("LANGUAGE", "chin_simp")
        return LanguageEnum.get_language(language)

    @staticmethod
    def get_platform() -> PlatformEnum:
        # 默认赋值ios  主要是给消息节点使用，消息节点没有平台信息
        platform = os.environ.get("PLATFORM") or "ios"

        return PlatformEnum.get_platform(platform)

    @staticmethod
    def get_force_ids() -> List[str]:
        force_ids: str = env.get("FORCE_IDS", "")
        force_ids = force_ids.strip().replace(" ", "").replace("\n", ",")
        if not force_ids:
            return []
        force_ids_list = [x for x in force_ids.split(",") if x]
        return force_ids_list

    @staticmethod
    def get_branch_name() -> str:
        branch_name = env.get("BRANCH_NAME", "")
        return branch_name

    @staticmethod
    def get_namespace() -> str:
        return env.get("NAMESPACE", "")

    @staticmethod
    def get_resource() -> str:
        return env.get("RESOURCE", "")

    @staticmethod
    def get_trunk_branch_weekupdate() -> str:
        return env.get("TRUNK_BRANCH_WEEKUPDATE", "")

    @staticmethod
    def get_pipeline_id() -> str:
        return env.get("PIPELINE_ID", "")

    @staticmethod
    def get_sound_bound_ver() -> str:
        return env.get("SOUND_BOUND_VER", "")

    @staticmethod
    def get_sound_res_ver() -> str:
        return env.get("SOUND_RES_VER", "")

    @staticmethod
    def set_commit_id(commit_id: str):
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_commit_id": commit_id})

    @staticmethod
    def get_commit_id(platform: str = None) -> str:
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_commit_id", "")

    @staticmethod
    def set_download_files(download_files: list):
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_download_files": download_files})

    @staticmethod
    def get_download_files(platform: str = None) -> list:
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_download_files", [])

    @staticmethod
    def set_need_package(package: bool):
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_need_package": package})

    @staticmethod
    def get_need_package(platform: str = None) -> bool:
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_need_package")

    @staticmethod
    def set_source_paths(source_paths: list) -> None:
        platform = EnvMgr.get_platform()
        source_paths = list(set(source_paths))
        env.set({f"{platform}_source_paths": source_paths})

    @staticmethod
    def update_source_paths(source_paths: list) -> None:
        platform = EnvMgr.get_platform()
        source_paths.extend(env.get(f"{platform}_source_paths", []))
        source_paths = list(set(source_paths))
        env.set({f"{platform}_source_paths": source_paths})

    @staticmethod
    def get_source_paths() -> list:
        platform = EnvMgr.get_platform()
        return env.get(f"{platform}_source_paths", [])

    @staticmethod
    def set_users(users: list) -> None:
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_users": list(set(users))})

    @staticmethod
    def update_users(users: list) -> None:
        platform = EnvMgr.get_platform()
        users.extend(env.get(f"{platform}_users", []))
        env.set({f"{platform}_users": list(set(users))})

    @staticmethod
    def get_users(platform: str = None) -> list:
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_users", [])

    @staticmethod
    def set_changelist(changelist: str) -> None:
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_changelist": changelist})

    @staticmethod
    def update_changelist(changelist: str) -> None:
        platform = EnvMgr.get_platform()
        if int(env.get(f"{platform}_changelist", "0")) > int(changelist):
            return
        env.set({f"{platform}_changelist": changelist})

    @staticmethod
    def get_changelist(platform: str = None) -> str:
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_changelist", "head")

    @staticmethod
    def set_changelist_by_key(changelist: str, key: str) -> None:
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_{key}_changelist": changelist})

    @staticmethod
    def get_changelist_by_key(key: str, platform: str = None) -> str:
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_{key}_changelist", "head")

    @staticmethod
    def get_force():
        force = env.get("FORCE")
        # NOTE: 为兼容历史，不为指定`False`类型的，则都为 True
        if force in ["false", "False", False]:
            return False
        return True

    @staticmethod
    def set_report(report: dict):
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_report": report})

    @staticmethod
    def get_report(platform: str = None) -> dict:
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(
            f"{platform}_report",
            {
                "total_count": 0,
                "succ_count": 0,
                "failed_count": 0,
                "error_count": 0,
                "success_ids": list(),
                "error_ids": list(),
                "error_infos": list(),
            },
        )

    @staticmethod
    def set_log(log_path: str):
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_log": log_path})

    @staticmethod
    def get_log(platform: str = None):
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_log")

    @staticmethod
    def set_package_report(package_report: str):
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_package_report": package_report})

    @staticmethod
    def get_package_report(platform: str = None):
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_package_report", "")

    @staticmethod
    def set_submit_changelist(changelist: list):
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_submit_changelist": changelist})

    @staticmethod
    def get_submit_changelist(platform: str = None):
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_submit_changelist")

    @staticmethod
    def get_last_branch():
        return env.get(f"last_branch")

    @staticmethod
    def set_last_branch(branch: str):
        env.set({"last_branch": branch})

    @staticmethod
    def set_dist_destinations(destinations: list):
        platform = EnvMgr.get_platform()
        env.set({f"{platform}_dist_destinations": destinations})

    @staticmethod
    def get_dist_destinations(platform: str = None):
        if not platform:
            platform = EnvMgr.get_platform()
        return env.get(f"{platform}_dist_destinations", [])

    @staticmethod
    def get_scene_force():
        env_force = env.get("SCENE_FORCE", False)
        if not env_force or env_force == "false":
            return False
        return True

    @staticmethod
    def set_package_type(package_type: str):
        platform = EnvMgr.get_platform()
        return env.set({f"{platform}_package_type": package_type})

    @staticmethod
    def get_package_type():
        platform = EnvMgr.get_platform()
        return env.get(f"{platform}_package_type", "")

    @staticmethod
    def get_deploy_env() -> str:
        """获取部署环境

        NOTE: 如果不传递或者传递为空，则都为prod环境
        """
        return env.get("DEPLOY_ENV") or "prod"

    @staticmethod
    def get_exclude_file() -> List:
        """获取需要跳过的文件"""
        exclude_file = env.get("EXCLUDE_FILE") or ""
        # 英文半角逗号分隔，如果不存在，则忽略
        return [f for f in exclude_file.split(",") if f]

    @staticmethod
    def get_p4_undefined() -> bool:
        need_p4_undefined = env.get("P4_UNDEFINED") or False
        if (need_p4_undefined == "false") or (not need_p4_undefined):
            return False
        return True

    @staticmethod
    def set_hotfix_dir_list(hotfix_dir_list: List) -> None:
        platform = EnvMgr.get_platform()
        return env.set({f"{platform}_hotfix_dir_list": hotfix_dir_list})

    @staticmethod
    def get_hotfix_dir_list() -> List:
        platform = EnvMgr.get_platform()
        return env.get(f"{platform}_hotfix_dir_list", [])

    @staticmethod
    def set_latest_paths(namespace: str, latest_paths: List):
        platform = EnvMgr.get_platform()
        return env.set({f"{platform}_{namespace}_latest_paths": latest_paths})

    @staticmethod
    def get_latest_paths(namespace: str) -> List:
        platform = EnvMgr.get_platform()
        return env.get(f"{platform}_{namespace}_latest_paths", [])

    @staticmethod
    def set_common_texture_paths(common_texture_paths: List):
        platform = EnvMgr.get_platform()
        return env.set({f"{platform}_common_texture_paths": common_texture_paths})

    @staticmethod
    def get_common_texture_paths() -> List:
        platform = EnvMgr.get_platform()
        return env.get(f"{platform}_common_texture_paths", [])

    @staticmethod
    def set_file_info(file_info: Dict) -> None:
        platform = EnvMgr.get_platform()
        return env.set({f"{platform}_file_info": file_info})

    @staticmethod
    def get_file_info() -> Dict:
        platform = EnvMgr.get_platform()
        return env.get(f"{platform}_file_info", {})

    @staticmethod
    def get_jenkins_blue_ocean_url() -> str:
        _url = env.get("RUN_CHANGES_DISPLAY_URL") or ""
        if not _url:
            return ""
        # 拼装路径
        return _url.replace("page=changes", "page=pipeline")

    @staticmethod
    def set_pipeline_running_msg_flag() -> None:
        return env.set({"pipeline_running_msg_flag": True})

    @staticmethod
    def get_pipeline_running_msg_flag() -> bool:
        return env.get("pipeline_running_msg_flag", False)


env_mgr = EnvMgr()
global_env_mgr = GlobalEnvMgr()
