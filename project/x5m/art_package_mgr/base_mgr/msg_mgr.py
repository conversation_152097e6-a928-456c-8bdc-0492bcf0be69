from typing import List, Optional

from frame import wechat, env, log, PipelineStatus
from project.x5m.art_package_mgr.base_mgr import config
from project.x5m.art_package_mgr.base_mgr.env_mgr import env_mgr
from project.x5m.art_package_mgr.base_mgr.enum_mgr import PlatformEnum


class MsgMgr:
    @staticmethod
    def send_debug_msg(content: str):
        wechat.send_multicast_post_debug(webhook=config.debug_webhook, content=content)

    @staticmethod
    def get_msg(pipeline_status: Optional[str] = None) -> str:
        all_msg = []
        for platform in PlatformEnum:
            msg = ""
            if platform == PlatformEnum.OPENHARMONY and not env_mgr.is_dev():
                continue
            msg += f"# **<font color='orange'>{platform}</font>**: "
            if not env_mgr.get_need_package(platform):
                msg += "不需要打包\n"
                continue
            report_detail = env_mgr.get_report(platform)
            submit_change_list = env_mgr.get_submit_changelist(platform)
            if report_detail["succ_count"] == 0:
                msg += "打包失败\n"
            elif report_detail["failed_count"] != 0 or report_detail["error_count"] != 0:
                msg += "打包部分成功\n"
            # NOTE: 当流水线失败时，并且没有上传p4产物，认为本次是失败的，避免误解
            # 但是因为用户可能会通过ID进行搜索，所以仍然返回打包步骤的信息
            elif pipeline_status == PipelineStatus.FAILURE.value:
                if not submit_change_list:
                    msg += "打包失败\n"
            else:
                msg += "打包成功\n"
            msg += f"> **打包机ip**: {env_mgr.get_ip(platform)}\n"
            change_list = env_mgr.get_changelist(platform)
            if change_list != "head":
                msg += f"> **原始资源 changelist**: {change_list}\n"
            if submit_change_list:
                msg += f"> **AB 资源 changelist**: {submit_change_list}\n"
            else:
                error_msg = env.get("PYFRAME_EXCEPTION_MESSAGE")
                if error_msg:
                    msg += ""
                else:
                    msg += f"> **AB 资源**: 与P4服务器文件一致\n"

            msg += f"> **成功数量**: {report_detail['succ_count']}\n"
            # 单个key的长度限制 150 字符, 经验值
            default_key_content_length = 150
            if report_detail["success_ids"]:
                # 处理返回，仅展示最后一级内容
                success_ids = set([_id.split("/")[-1] for _id in report_detail["success_ids"]])
                # 限制一下成功id的长度，消息过长，导致当前平台的消息展示不全
                msg += f"> **成功ID**: {','.join(success_ids)[:default_key_content_length]}\n"
            if report_detail["failed_count"] != 0:
                msg += f"> **失败数量**: {report_detail['failed_count']}\n"
            if report_detail["error_count"] != 0:
                msg += f"> **出错数量**: {report_detail['error_count']}\n"
            if report_detail["error_ids"]:
                msg += f"> **失败ID**: {','.join(report_detail['error_ids'])[:default_key_content_length]}\n"
                error_infos = report_detail["error_infos"]
                if error_infos:
                    msg += f"> **失败原因**: {error_infos[0].strip()}\n"
            dist_destinations = env_mgr.get_dist_destinations()
            log.info("dist_destinations: %s", dist_destinations)
            if dist_destinations:
                for path, ids in dist_destinations.items():
                    log.info(f"path, ids: {path}, {ids}")
                    msg += f"> **{path}**: {','.join(ids)[:default_key_content_length]}\n"
            report_url = env_mgr.get_package_report(platform)
            if report_url:
                msg += f"> **打包报告**: [打包报告]({report_url})\n"
            log_url = env_mgr.get_log(platform)
            if log_url:
                msg += f"> **中间产物**: [中间产物压缩包]({log_url})\n"
            # NOTE: 限制单平台的消息长度为 1000 字符，避免前面平台展示内容过长，影响后面平台展示
            if len(msg) > 1000:
                msg = msg[:1000] + "..."
            all_msg.append(msg)
        msg = "\n".join(all_msg)
        return msg.replace("\\", "/")

    @staticmethod
    def get_user() -> list:
        user = []
        for platform in PlatformEnum:
            user.extend(env_mgr.get_users(platform))
        return list(set(user))

    @staticmethod
    def get_pm_users() -> List[str]:
        return ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
