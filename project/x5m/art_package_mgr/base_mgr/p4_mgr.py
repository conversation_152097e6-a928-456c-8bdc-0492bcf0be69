import re
from typing import List, Union
from frame import P4Client, log, PyframeException, path_mgr, os, env
from project.x5m.art_package_mgr.base_mgr import config
from project.x5m.art_package_mgr.base_mgr.env_mgr import env_mgr


class P4Mgr:
    def __init__(self, client, root, **kwargs):
        """
        Args:
            client (str): p4 client
            root (str): p4 root
            host (str): p4 host
            username (str): p4 username
            password (str): p4 password
            charset (str): p4 charset 字符集，默认CP936
            options (dict): p4 options
                allwrite: False拉取下来的文件只读，True拉取下来的文件可写。默认False
                clobber: False不要覆盖已经修改的文件，True覆盖已经修改的文件。默认True
                compress: False传输时不压缩，True传输时压缩。默认False
                locked: 暂不明白
                modtime: False拉取的文件时间是拉取时间，True拉取的文件是用户提交的时间。默认False
                rmdir: False不删除空文件夹，True删除空文件夹。默认False
        """
        self.__host = kwargs.get("host", config.P4_CONFIG_JENKINS.get("host"))
        self.__username = kwargs.get("username", config.P4_CONFIG_JENKINS.get("username"))
        self.__password = kwargs.get("password", config.P4_CONFIG_JENKINS.get("password"))
        self.__charset = kwargs.get("charset", P4Client.Charset.CP936)
        self.__client = client
        self.__root = root
        self.p4 = P4Client(
            host=self.__host,
            username=self.__username,
            password=self.__password,
            client=self.__client,
            charset=self.__charset,
        )
        self.__options = kwargs.get("options", dict())
        if self.__options:
            self.p4.set_options(**self.__options)
        self.__options_str = self.p4.get_options()
        self.p4.set_root(self.__root)
        self.__p4views = []

        self._is_dev = env_mgr.is_dev()
        if self._is_dev:
            self.dev_p4 = P4Client(
                **config.P4_CONFIG_TEST,
                client=self.__client,
                charset=self.__charset,
            )
            if self.__options:
                self.dev_p4.set_options(**self.__options)
            self.__dev_root = os.path.join(self.__root, "dev")
            self.dev_p4.set_root(self.__dev_root)
        # 获取语言，然后根据语言设置p4 mr的后缀
        self._lang = env.get("LANGUAGE", "chin_simp")
        self._p4_mr_suffix = "_trad" if self._lang == "chin_trad" else ""

    @property
    def p4_views(self) -> list:
        return self.__p4views

    def sync_all(self, changelist: str = "head", force: bool = False):
        self.p4.sync_all(changelist, force=force)
        if self._is_dev:
            views: list = self.p4.get_view()
            dev_views = []
            for view in views:
                view = f"//testdepot/{view[2:]}"
                dev_views.append(view)
            self.dev_p4.set_view(dev_views)
            self.dev_p4.sync_all(changelist="head", force=force)

    def clean(self, a: bool = False, d: bool = False):
        views: list = self.p4.get_view()
        for view in views:
            view = view.strip()
            path = view.split(" ")[0]
            if not path.startswith("-"):
                self.p4.clean(path=path, a=a, d=d)
        if self._is_dev:
            for view in views:
                view = f"//testdepot/{view[2:]}"
                view = view.strip()
                path = view.split(" ")[0]
                if not path.startswith("-"):
                    self.dev_p4.clean(path=path, a=a, d=d)

    def submit(self, views: List[str], desc: str, revert_if_failed: bool = False):
        """
        Args:
            views (List[str]): 待提交文件view列表, 目录需要 '/' 结尾，文件不能带 '/'
            desc (str): 提交时描述
            revert_if_failed (bool, optional): 提交失败时是否revert. Defaults to False.
        """
        if not self._is_dev:
            return self._submit(views, desc, revert_if_failed)
        return self._submit_dev(views, desc, revert_if_failed)

    def _reconcile(self, views: List[str]):
        depotsfile = []
        for view in views:
            if view.endswith("/"):
                view = view[:-1]
            file = self.parse_p4view_to_localpath(view)
            log.info("view: %s, local file: %s", view, file)
            if not os.path.exists(file):
                continue
            log.info(os.path.isdir(file))
            if os.path.isdir(file):
                for sub_file in os.listdir(file):
                    depotsfile.extend(self._reconcile([f"{view}/{sub_file}"]))
            else:
                exist = self.p4.files(view)
                log.info("p4 exit: %s", exist)
        
                if not exist:
                    depotsfile.extend(self.p4.add(file))
                else:
                    depotsfile.extend(self.p4.edit(file))
        return depotsfile

    def _reconcile_dev(self, views: List[str]):
        depotsfile = []
        for view in views:
            if view.endswith("/"):
                view = view[:-1]
            file = self.parse_p4view_to_localpath(view)
            log.info(f"_reconcile_dev file: {file}")
            if not os.path.exists(file):
                continue
            if os.path.isdir(file):
                for sub_file in os.listdir(file):
                    depotsfile.extend(self._reconcile_dev([f"{view}/{sub_file}"]))
            else:
                dev_file = file.replace(self.__root, self.__dev_root)
                dev_view = f"//testdepot/{view[2:]}"
                path_mgr.xcopy(file, dev_file, dst_is_file=True)
                exist = self.dev_p4.files(dev_view)
                if not exist:
                    depotsfile.extend(self.dev_p4.add(dev_file))
                else:
                    depotsfile.extend(self.dev_p4.edit(dev_file))

        return depotsfile

    def _submit(self, views: List[str], desc: str, revert_if_failed: bool = False):
        submit_files = self._reconcile(views)
        if not submit_files:
            log.info("没有需要提交的文件")
            return None
        else:
            log.info(f"待提交文件列表: {submit_files}")
        return self.p4.submit(desc, revert_if_failed)

    def _submit_dev(self, views: List[str], desc: str, revert_if_failed: bool = False):
        submit_files = self._reconcile_dev(views)
        if not submit_files:
            log.info("没有需要提交的文件")
            return None
        else:
            log.info(f"待提交文件列表: {submit_files}")
        return self.dev_p4.submit(desc, revert_if_failed)

    def set_p4_views(self, p4_views: Union[str, List[str]], format_view=True):
        if isinstance(p4_views, str):
            p4_views = [p4_views]
        if format_view:
            p4_views = self.p4_view_format(p4_views)
        self.__p4views = list(set(p4_views))
        self.p4.set_view(self.__p4views)

    def add_p4_views(self, p4_views: Union[str, List[str]], format_view=True):
        if isinstance(p4_views, str):
            p4_views = [p4_views]
        if format_view:
            p4_views = self.p4_view_format(p4_views)
        self.__p4views.extend(p4_views)
        self.__p4views = list(set(self.__p4views))
        self.p4.set_view(self.__p4views)

    def p4_view_format(self, source_views: Union[str, List[str]]) -> List[str]:
        """
        p4 view格式化
        从p4的文件路径列表转换为p4 view列表
        """
        if isinstance(source_views, str):
            source_views = [source_views]
        views = set()
        for source_view in source_views:
            if source_view.endswith("/"):
                source_view += "..."
            view = f"{source_view} //{self.parse_p4view(source_view).format(client=self.__client)}"
            views.add(view)
        return list(views)

    def parse_p4view(self, source_view: str) -> str:
        """
        从p4 view转换为p4本地文件路径
        //depot/xxxx -> {client}/xxx
        {client}用于format为p4 client或者p4root
        """
        # //x5_mobile/mr/art_release/art_src 把原始资源映射到 arttrunk/release/mobile_dancer/arttrunk/client/Assets/resources/Art
        if source_view.startswith(config.p4_views.get("art_resource_root")):
            view = f"{{client}}/{source_view.replace(config.p4_views.get('art_resource_root'), f'arttrunk/{config.arttrunk_branch}/mobile_dancer/arttrunk/client/Assets/resources/Art')}"
        # //x5_mobile/mr/art_resource/art_src 把原始资源映射到 arttrunk/release/mobile_dancer/arttrunk/client/Assets/resources/Art
        elif source_view.startswith(config.p4_views.get("art_resources_src")):
            view = f"{{client}}/{source_view.replace(config.p4_views.get('art_resources_src'), f'arttrunk/{config.arttrunk_branch}/mobile_dancer/arttrunk/client/Assets/resources/Art')}"
        # //x5m/res/cdn 把cdn资源映射到 arttrunk/release/mr/art_release/cs
        elif source_view.startswith(config.p4_views.get("cdn_root")):
            view = f"{{client}}/{source_view.replace(config.p4_views.get('cdn_root'), f'arttrunk/{config.arttrunk_branch}/mr{self._p4_mr_suffix}/art_release/cs')}"
        elif source_view.startswith("//x5_mobile/mr_trad"):
            view = f"{{client}}/{source_view.replace('//x5_mobile/mr_trad', f'arttrunk/{config.arttrunk_branch}/mr_trad')}"
        # //x5_mobile/mr 把原始资源映射到 arttrunk/release/mr
        elif source_view.startswith("//x5_mobile/mr"):
            view = f"{{client}}/{source_view.replace('//x5_mobile/mr', f'arttrunk/{config.arttrunk_branch}/mr')}"
        else:
            view = f"{{client}}/{source_view.replace('//', '')}"
        return view

    def parse_p4view_to_localpath(self, source_view: str) -> str:
        if source_view.endswith("..."):
            source_view = source_view[:-3]
        return self.parse_p4view(source_view).format(client=self.__root)

    def parse_p4view_list_to_localpath(self, source_views: List[str]) -> List[str]:
        return [self.parse_p4view_to_localpath(source_view) for source_view in source_views]

    def get_branchs(self, filter: str = "^[0-9]\.[0-9][0-9]\.0$", num: int = 1) -> List[str]:
        branchs = self.p4.dirs(f"//x5_mobile/mr{self._p4_mr_suffix}/b/*")
        branchs = [
            branch.replace(f"//x5_mobile/mr{self._p4_mr_suffix}/b/", "")
            for branch in branchs
            if re.match(filter, branch.replace(f"//x5_mobile/mr{self._p4_mr_suffix}/b/", ""))
        ]
        log.info(f"After filter {filter}, Branchs: {branchs}")
        if len(branchs) < num:
            raise PyframeException(f"find last {num} branchs error, Branchs less than {num}")
        return branchs[-num:]

    def get_last_branch(self, filter: str = "^[0-9]\.[0-9][0-9]\.0$") -> str:
        branchs = self.get_branchs(filter, 1)
        log.info(f"After filter {filter}, Branchs: {branchs}")
        return branchs[-1]

    def get_last_trad_branch(self, filter: str = "^[0-9]\.[0-9][0-9]\.0_tc$") -> str:
        # //x5_mobile/mr/b/7.12.0_tc, 取7.12.0_tc
        branchs = self.get_branchs(filter, 1)
        log.info(f"After filter {filter}, Branchs: {branchs}")
        return branchs[-1]

    def get_last_hotfix_branch(self) -> List[str]:
        pass

    def info(self) -> None:
        log.info(f"p4 host: {self.__host}")
        log.info(f"p4 username: {self.__username}")
        log.info(f"p4 password: {self.__password}")
        log.info(f"p4 client: {self.__client}")
        log.info(f"p4 root: {self.__root}")
        log.info(f"p4 charset: {self.__charset}")
        log.info(f"p4 views: {self.__p4views}")
        log.info(f"p4 options: {self.__options_str}")

    def __display_p4_info(self):
        """
        输出p4的信息
        """
        info = self.p4.run("info")
        for k, v in info[0].items():
            log.info(f"{k}: {v}")

    def __str__(self):
        return f"p4 info:\nhost: {self.__host}\nclient: {self.__client}\nroot: {self.__root}\ncharset: {self.__charset}\nviews: {self.__p4views}\noptions: {self.__options_str}"
