import datetime
import json
import re
import time
import subprocess
from typing import List, Optional, Tuple, Union
from frame import *
from project.x5m.art_package_mgr.base_mgr.env_mgr import env_mgr, global_env_mgr
from project.x5m.art_package_mgr.base_mgr.msg_mgr import MsgMgr
from project.x5m.art_package_mgr.base_mgr.p4_mgr import P4Mgr
from project.x5m.art_package_mgr.base_mgr.git_mgr import X5mGitMgr
from project.x5m.art_package_mgr.base_mgr.enum_mgr import *
from project.x5m.art_package_mgr.base_mgr import config


class PackageMgr:
    def __init__(self, kind: ResourceTypeEnum, *args, **kwargs) -> None:
        """
        Args:
            kind: 美术资源打包类型
            root: p4根目录
            client: p4 client
            workspace: jenkins workspace
            arttrunk_branch: arttrunk分支
        """
        self._kind: ResourceTypeEnum = kind
        self._debug = env_mgr.is_dev()
        self._platform = env_mgr.get_platform()
        self._language = env_mgr.get_language()
        self._kwargs = kwargs
        self._workspace = self._kwargs.get("workspace") or env_mgr.get_workspace()

        root = self._kwargs.get("root") or env_mgr.get_workspace()
        client = self._kwargs.get("client") or f"jenkins-{self._kind}-{self._platform}-{common.get_host_ip()}"
        self._kwargs["client"] = client
        self._kwargs["root"] = root
        self._p4_options = {
            "allwrite": True,
            "clobber": True,
            "compress": False,
            "locked": False,
            "modtime": False,
            "rmdir": True,
        }
        self._kwargs["options"] = self._p4_options
        self.__p4_mgr: P4Mgr = None

        self._arttrunk_branch = self._kwargs.get("arttrunk_branch") or config.arttrunk_branch
        self.__arttrunk_git_mgr: X5mGitMgr = None
        # 根据是否为繁中，确定`config`仓库使用的是cdn-trad还是cdn分支
        self._x5mconfig_branch = "cdn-trad" if self._language == LanguageEnum.CHIN_TRAD else "cdn"

        """
        路径
        """
        # 打包工具所在路径
        self._project_path = os.path.join(self._workspace, "arttrunk", self._arttrunk_branch, "mobile_dancer", "arttrunk", "client")
        # 打包资源路径
        self._project_art_path = os.path.join(self._project_path, "Assets", "resources", "Art")
        # 中间产物根路径
        self._report_root = os.path.join(self._project_path, "AssetBundleTool")
        # 打包报告路径
        self._report_path = os.path.join(self._report_root, "AssetBundleToolLogs")
        # 打包日志路径
        self._log_root = os.path.join(self._report_root, "PackageToolLogs")
        # 打包日志文件
        self._log_file = os.path.join(self._log_root, f"{self._platform}_{env.pipeline.build_num()}.log")
        # 原始资源列表路径
        self._resources_path = os.path.join(self._report_root, "resources.txt")
        # 输出ab包的路径
        self._ab_view = f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/"
        self._ab_path = ""
        # 记录打包要提交产物的目录
        self._record_path = os.path.join(self._report_root, "PackageToolLogs")
        # 中间产物备份地址
        self._backup_root = os.path.join(self._workspace, "backup")
        self._backup_path = os.path.join(self._backup_root, f"{self._kind}_{self._platform}_{env.pipeline.build_num()}.zip")

        """
        这部分要在子类中赋值
        """
        # 判断是否需要打包的路径
        # 默认为//x5_mobile/mr/art_release/art_src/xxx/...
        self._resource_dir_prefix = f"{config.p4_views.get('art_resource_root')}/{self._kind}"
        # c路径//x5_mobile/mr/art_release/art_src/c
        self._c_resource_dir_prefix = ""
        # 每次打包都会拉的p4 view
        self._const_source_p4_views = []
        self._const_ab_p4_views = []
        # 打包工具接口，默认为H3DBuildTools.BuildArt
        self._execute_method = "H3DBuildTools.BuildArt"
        # 解析日志中成功id的pattern
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s.*?(\d+$|\d+_extend\d$)")
        # 解析日志中失败id的pattern
        self._fail_pattern = [
            re.compile(r"^\[Error]\s+\d+\s.*?(\d+$|\d+_extend\d$)"),
            re.compile(r"^\[Error]\d+\s(\d+$)"),
            re.compile(r"^\[Error].*?(\d{6,}).*?"),
        ]
        # 群通知 webhook
        self._webhook = ""
        # unity版本, 默认为2022.3.27f1，可以在各流水线里重写
        self._unity_version = "2022.3.27f1"
        # 可以忽略的p4的操作 branch
        self._p4_ingore_action_list = ["delete", "move/delete", "branch", "integrate", "merge", "revert"]

    @property
    def webhook(self):
        if self._debug:
            return config.debug_webhook
        return self._webhook

    @property
    def p4_mgr(self) -> P4Mgr:
        if self.__p4_mgr == None:
            self.__p4_mgr = P4Mgr(**self._kwargs)
        return self.__p4_mgr

    @property
    def arttrunk_git_mgr(self) -> X5mGitMgr:
        if self.__arttrunk_git_mgr == None:
            self.__arttrunk_git_mgr = X5mGitMgr(self._workspace, "arttrunk", self._arttrunk_branch)
        return self.__arttrunk_git_mgr

    @property
    def ab_path(self) -> str:
        if self._ab_path == "":
            self._ab_path = self.p4_mgr.parse_p4view_to_localpath(self._ab_view)
        return self._ab_path

    @property
    def p4(self) -> P4Client:
        return self.p4_mgr.p4

    def prepare(self):
        """
        准备阶段，主要做环境检查，参数检查等
        """
        self._kill()
        self._env_check()
        self._param_check()
        self._other_check()

    def _kill(self):
        """
        杀掉浏览器进程
        每次打包结束会打开浏览器，长时间不关闭浏览器会导致系统越来越卡
        """
        proc_name = "msedge.exe"
        if proc_mgr.exists_proc(proc_name):
            log.info(f"process {proc_name} exist, try to kill it")
            try:
                proc_mgr.kill_proc(proc_name)
            except Exception as e:
                log.error("failed to kill msedge.exe: %s", e)
        else:
            log.info(f"process {proc_name} not exist")

    def _env_check(self):
        """
        环境检查
        """
        env_mgr.set_ip()
        log.info(f"当前主机IP: {common.get_host_ip()}")
        log.info(f"当前CPU核心数: {common.get_local_cpu_count()}")
        log.info(f"当前工作目录: {os.getcwd()}")
        log.info(f"当前工作空间: {self._workspace}")
        log.info(f"当前打包类型: {self._kind}")
        log.info(f"当前打包平台: {self._platform}")
        log.info(f"当前打包模式: {['正式','测试'][self._debug]}")
        self.p4_mgr.info()
        free_disk = common.get_disk_free_size()
        if free_disk < config.disk_threshold:
            log.warning(f"磁盘空间不足{config.disk_threshold}G，请检查磁盘空间")
        else:
            log.info(f"当前磁盘剩余空间: {free_disk}G")
        self.__check_git_version()
        self.__check_build_tools_version()

    def _param_check(self):
        """
        参数检查及预处理
        """
        pass

    def _other_check(self):
        """
        其他预检查
        """
        pass

    # 检查git版本
    def __check_git_version(self):
        try:
            result = subprocess.run(["git", "--version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
            if result.returncode == 0:
                version_s = result.stdout.strip().split(" ")[-1]
                log.info(f"Git version: {version_s}")
                version = ".".join(version_s.split(".")[:2])
                # NOTE: 2.40及以上的git版本，不支持http重定向到https
                if version >= "2.40":
                    raise PyframeException(f"Current Git version: {version_s}, greater than 2.40")
            else:
                log.error(f"Error getting Git version: {result.stderr}")
        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            log.error(f"Error getting Git version: {e}")

    def __check_unity_version(self):
        """
        检查unity版本
        """
        try:
            result = subprocess.run(["Unity", "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                log.info(f"Unity version: {version}")
                if version != self._unity_version:
                    # raise PyframeException(f"Current Unity version: {version}, not equal to {self._unity_version}")
                    log.error(f"Current Unity version: {version}, not equal to {self._unity_version}")
            else:
                log.error(f"Error getting Unity version: {result.stderr}")
        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            log.error(f"Error getting Unity version: {e}")

    def __check_tuanjie_engine_version(self):
        """
        鸿蒙打包检查tuanjie引擎版本
        """
        pass

    def __check_build_tools_version(self):
        """
        检查打包工具版本
        """
        if self._platform == PlatformEnum.OPENHARMONY:
            self.__check_tuanjie_engine_version()
        else:
            self.__check_unity_version()

    def check_package(self, stop_current_build: bool = True, force_by_id: bool = True) -> bool:
        """
        检查是否需要打包
        Args:
            stop_current_build: 是否停止当前构建
            force_by_id: 是否通过id强制打包
        """
        force_id = env_mgr.get_force_ids()
        if force_by_id and force_id:
            self._check_package_by_ids(force_id)
            log.info(f"{self._platform}平台强制打包id列表: {', '.join(force_id)}")
            return True
        changelist = self._get_changelist()
        # 判断指定路径下的资源是否有更新
        if not self._check_package_by_changelist(changelist):
            log.info(f"{self._platform}平台{self._kind}资源无更新，无需打包")
            if stop_current_build:
                self.__unpakaged_save_changelist()
                self.__stop_current_build()
            return False
        # 按照平台设置需要打包标识
        env_mgr.set_need_package(True)
        # 如果需要打包，添加通知操作，以便于用户知道流水线已经在执行
        self._send_pipelie_running_msg()
        log.info(f"{self._platform}平台{self._kind}资源有更新，需要打包")
        return True

    def _send_pipelie_running_msg(self) -> None:
        """发送流水线在运行通知"""
        if self._debug:
            return
        # 限制仅有一个通知
        if env_mgr.get_pipeline_running_msg_flag():
            return
        env_mgr.set_pipeline_running_msg_flag()
        # 获取当前流水线的 blue ocean url
        # TODO: 暂时没有想到好的处理方式
        action = "上传" if env.get("RESOURCE", "") in config.upload_resource_list else "打包"
        blue_ocean_url = env_mgr.get_jenkins_blue_ocean_url()
        msg = f"您的资源正在{action}中，点击链接可以查看{action}进度\n"
        msg += f"链接: [{env.pipeline.pipeline_name()}]({blue_ocean_url})\n"
        msg += f"构建号: {env.pipeline.build_id()}\n"
        msg += f"资源列表: \n"
        # 拼装需要打包的文件
        for fp in env_mgr.get_source_paths():
            msg += f"{fp}\n"
        # 发送通知，失败不影响其它
        try:
            user_list = env_mgr.get_users()
            user_list.extend(config.notice_extra_user_list)
            wechat.send_unicast_msg(user_list=user_list, content=msg)
        except Exception as e:
            log.error("error sending wechat msg: %s", e)

    def __unpakaged_save_changelist(self):
        """
        指定changelist下没有资源更新，记录changelist
        """
        s_changelist = env_mgr.get_changelist()
        if s_changelist == "head":
            last_changelist = global_env_mgr.get_last_changelist(resource=self._kind)
            if not last_changelist:
                return
            last_changelist = int(last_changelist)
            s_changelist = last_changelist + config.max_changelist_size
        remote_laster_changelist = self.p4.get_latest_changes(f"{self._resource_dir_prefix}/...").get("change", "0")
        if int(s_changelist) >= int(remote_laster_changelist):
            return
        log.info(f"本地最新changelist: {s_changelist}, 远程最新changelist: {remote_laster_changelist}")
        global_env_mgr.set_last_changelist(resource=self._kind, changelist=str(s_changelist))

    def __stop_current_build(self):
        env_mgr.set_need_package(False)
        for platform in PlatformEnum:
            if platform == PlatformEnum.OPENHARMONY and not self._debug:
                continue
            index = 1
            while env_mgr.get_need_package(platform) is None:
                if index > 60:
                    break
                time.sleep(1)
                index += 1
            if env_mgr.get_need_package(platform):
                return
        pipeline_mgr.stop_current_build()

    def _get_lastest_changelist(self) -> str:
        """获取最新的 changelist"""
        # 获取最新的一条作为初始的 changelist
        # NOTE: 因为是初始数据，这里进需要拿到主路径的 changelist 即可
        changes = self.p4.get_changes(f"{self._resource_dir_prefix}/...", max=1)
        default_changelist = "-1"
        # 如果没有资源，则返回默认值
        if not changes:
            return default_changelist
        # 获取对应的 changelist
        changelist = changes[0].get("change")
        return changelist or default_changelist

    def _get_changelist(self) -> int:
        """根据上次的打包版本号，获取本次打包ID"""
        first_changelist = env_mgr.get_first_changelist()
        last_changelist = global_env_mgr.get_last_changelist(resource=self._kind)
        if first_changelist != "-1":
            if not last_changelist:
                global_env_mgr.set_last_changelist(resource=self._kind, changelist=first_changelist)
            return int(first_changelist) + 1
        if not last_changelist:
            # DB 中没有上次的记录，说明是初始化，则从P4直接获取最新的一条记录
            last_changelist = self._get_lastest_changelist()
            if last_changelist == "-1":
                raise PyframeException("first_changelist为空或默认值-1,且数据库中上次打包版本号也为空,无法打包")
            # 设置 db 记录
            global_env_mgr.set_last_changelist(resource=self._kind, changelist=last_changelist)
            
        return int(last_changelist) + 1

    def _check_package_by_ids(self, ids: list) -> None:
        """
        获取指定id列表的资源是否有更新，走强打逻辑
        """
        depot_files = set()
        for id in ids:
            source_path = self._get_source_paths_by_id(id)
            depot_file = self._filter_source_paths([source_path])
            if not depot_file:
                continue
            depot_files.update(depot_file)
        if not depot_files:
            raise PyframeException(f"根据强制打包id列表{ids}没有找到对应的depotfile")
        env_mgr.set_source_paths(source_paths=list(depot_files))
        env_mgr.set_users(["dgm_jenkins"])

    def _get_source_paths_by_id(self, id: str) -> str:
        if id.startswith(self._resource_dir_prefix):
            return id
        return f"{self._resource_dir_prefix}/{id}"

    def _check_package_by_changelist(self, changelist: int) -> bool:
        """
        检查指定路径下资源是否有更新,子类有特殊逻辑可以重写这部分
        """
        if self.__history_source:
            changes = self.p4.get_changes(
                f"{self._resource_dir_prefix}/...@{changelist},{int(changelist)+config.max_changelist_size}", max=config.max_changelist_size
            )
            # c路径变化//x5_mobile/mr/art_release/art_src/c
            if self._c_resource_dir_prefix:
                c_changes = self.p4.get_changes(
                    f"{self._c_resource_dir_prefix}/...@{changelist},{int(changelist)+config.max_changelist_size}", max=config.max_changelist_size
                )
                if c_changes:
                    changes.extend(c_changes)
        else:
            changes = self.p4.get_changes(f"{self._resource_dir_prefix}/...@{changelist},now", max=config.max_changelist_size)
            # c路径变化//x5_mobile/mr/art_release/art_src/c
            if self._c_resource_dir_prefix:
                c_changes = self.p4.get_changes(f"{self._c_resource_dir_prefix}/...@{changelist},now", max=config.max_changelist_size)
                if c_changes:
                    changes.extend(c_changes)
        log.info(f"changes: {changes}")
        if len(changes) == 0:
            return False
        depot_files = set()
        users = set()
        # 每个souces_path对应的最新action,解决先add后delete的问题
        source_path_acition_dict = {}
        # 记录每个资源对应本地打包的change,用于记录历史资源打包的资源以及对应change
        source_path_change_dct = {}
        # changes format: [{'change': 'xxx', 'time': '1728528241', 'user': 'xxx', 'client': 'xxx', 'status': 'submitted', 'changeType': 'public', 'path': 'xxx', 'desc': 'xxx'}]
        changes = sorted(changes, key=lambda c: int(c["change"]))
        for change in changes:
            changeid: str = change.get("change")
            changed_files: dict = self.p4.get_files_by_changelist(changeid, depotfile_only=False)
            source_paths: list = changed_files.get("depotFile", [])
            action = changed_files.get("action", [])
            for i in range(len(source_paths) - 1, -1, -1):
                if source_paths[i] not in source_path_acition_dict:
                    source_path_acition_dict[source_paths[i]] = action[i]
                # 如果操作满足条件，则标识路径已经删除
                if action[i] in self._p4_ingore_action_list:
                    depot_files.discard(source_paths[i])
                    source_paths.pop(i)
            log.info(f"source_paths: {source_paths}")
            source_path = self._filter_source_paths(source_paths)
            log.info(f"source_path: {source_path}")
            if not source_path:
                continue
            # tmp
            changeid = int(changeid)
            for source in source_path:
                if source in source_path_change_dct:
                    if changeid > source_path_change_dct[source]:
                        source_path_change_dct[source] = changeid
                else:
                    source_path_change_dct[source] = changeid
            depot_files.update(source_path)
            # tmp end

            user: str = change.get("user")
            # TODO: 固定后缀，可以设置为常量
            if not user.endswith("h3d.com.cn"):
                user = f"{user}@h3d.com.cn"
            users.add(user)
        if self.__history_source:
            env.set({"source_path_change_dct": source_path_change_dct})
        # depot_files = self._filter_packaged_source(depot_files)
        log.info(f"depot_files: {depot_files}")
        if not depot_files:
            return False
        env_mgr.update_source_paths(source_paths=list(depot_files))
        env_mgr.update_users(users=list(users))
        # 取最大的changelist
        changes = [change.get("change") for change in changes]
        env_mgr.set_changelist(changelist=max(changes, key=int))
        return True

    def _check_source_path(self, source_path: str, patterns: Union[Tuple, List, str], action: Optional[str] = "endswith") -> bool:
        """检查资源是否满足指定的匹配规则

        Args:
            source_path: 资源路径
            patterns: 匹配内容
            action: 匹配动作，包含: endswith, startswith，默认是endswith，判断以过滤规则结尾
        Returns:
            True: 匹配成功
            False: 匹配失败
        """
        # 转换为元组
        if isinstance(patterns, str):
            patterns = (patterns,)
        # 判断过滤规则是否为 list 或 tuple；如果不是，则返回False
        if type(patterns) not in [list, tuple]:
            log.error("filter_patterns is not list or tuple, current type: %s", type(patterns))
            return False
        # 判断动作是否在支持的范围内
        support_actions = ["endswith", "startswith"]
        # 如果动作不在支持的范围, 则返回 False
        if action not in support_actions:
            log.error("not support action: %s", action)
            return False
        # 转换为元组
        if isinstance(patterns, list):
            patterns = tuple(patterns)
        # 过滤规则转换为为小写
        patterns = tuple(fp.lower() for fp in patterns)
        # 判断是否以指定后缀结尾
        try:
            if getattr(source_path.lower(), action)(patterns):
                return True
        except Exception as e:
            log.error("source_path: %s, action: %s, patterns: %s, error: %s", source_path, action, patterns, e)
        return False

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for file in source_paths:
            if file.startswith(self._resource_dir_prefix):
                depots.add(file)
        return depots

    def update_git_and_p4(self) -> None:
        """
        更新git和p4
        """
        # 通过前面步骤检测changelist判断是否有资源更新，如果有，继续后面步骤，否则，直接返回
        if not env_mgr.get_need_package():
            return
        self._clean_resource()
        self._update_git()
        self._update_p4()

    def _clean_resource(self) -> None:
        """
        清理资源
        """
        self._safe_clean(self._project_art_path, "开始清理原始资源")
        self._safe_clean(self.ab_path, "开始清理ab资源")
        self._safe_clean(self._report_root, "开始清理打包日志")
        self._clean_other()

    def _clean_other(self):
        pass

    def _safe_clean(self, path: str, info: str = None) -> None:
        """
        安全清理目录
        """
        if info:
            log.info(info)
        if path_mgr.exists(path):
            path_mgr.rm(path)

    def _update_git(self) -> None:
        """
        更新git, arttrunk和配置仓库
        """
        self.arttrunk_git_mgr.update()
        self._update_config_git()

    def _update_config_git(self) -> None:
        """
        更新配置仓库
        """
        pass

    def _update_p4(self) -> None:
        """
        更新p4
        """
        # 主要是拉资源路径，资源拉到对应的change
        self._fill_other_p4_views()
        self._fill_source_p4_views()
        self._fill_const_source_p4_views()
        self.p4_mgr.sync_all(changelist=env_mgr.get_changelist(), force=env_mgr.get_force())
        # 如果 ab 包的路径为空，则不需要处理，直接跳过
        if not self._const_ab_p4_views:
            return
        # 这个主要是拉ab包路径，ab包每次都拉到head
        self._fill_const_ab_p4_views()
        self.p4_mgr.sync_all(force=env_mgr.get_force())

    def _fill_source_p4_views(self):
        """
        填充要打包的资源p4view
        """
        self.p4_mgr.add_p4_views(env_mgr.get_source_paths())

    def _fill_other_p4_views(self):
        """
        填充其他p4view
        """
        pass

    def _fill_const_source_p4_views(self):
        """
        填充常量p4view
        """
        self.p4_mgr.add_p4_views(self._const_source_p4_views)

    def _fill_const_ab_p4_views(self):
        """
        填充常量p4view
        """
        self.p4_mgr.set_p4_views(self._const_ab_p4_views)

    def package(self):
        if not env_mgr.get_need_package():
            return
        self._input_check()
        self._package()
        self._calculate_package_result()

    def _input_check(self):
        """
        输入检查
        """
        self._input_file_size_check()

    def _input_file_size_check(self):
        """
        输入文件大小检查
        """
        unlimmited_file_dct = {}
        for path in env_mgr.get_source_paths():
            local_paths = self.p4_mgr.parse_p4view_to_localpath(path)
            for dir_path, _, file_names in os.walk(local_paths):
                for file_name in file_names:
                    file = os.path.join(dir_path, file_name)
                    file_type = FileEnum.get_file_type(file)
                    file_size = os.path.getsize(file) / 1024 / 1024
                    if file_size > file_type.max_filesize:
                        unlimmited_file_dct[file] = file_size
        if not unlimmited_file_dct:
            return
        msg = f"以下文件超过最大限制大小{FileEnum.JPEG.max_filesize}M\n"
        for k, v in unlimmited_file_dct.items():
            msg += f"{k}: {v:.2f}M\n"
        log.warning(msg)

    def _package(self):
        """
        打包
        """
        self._make_resouces_file()
        cmd_line = self._get_package_cmd()
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _make_resouces_file(self, p4_view_type: str = "art_resource_root"):
        """
        输入路径有两种方式，现在采用第二种方式
        1. 传path=xxxx
        2. 把相关路径写在AssetBundleTool/resources.txt文件中
        """
        # 如果传递为空或None，则设置为默认为 `art_resource_root`
        if not p4_view_type:
            p4_view_type = "art_resource_root"
        # 创建日志报告目录
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                path = path.replace(config.p4_views.get(p4_view_type), "Assets/resources/Art").strip("/")
                lines.append(path)
            f.write(",".join(lines))

    def _get_package_cmd(self) -> str:
        """
        获取打包命令
        """
        cmd_line = f"{self._platform.package_tool} "
        cmd_line += f"-quit -batchmode -projectPath {self._project_path} -logFile {self._log_file} "
        cmd_line += f"-executeMethod {self._execute_method} -buildTarget {self._platform} "
        cmd_line += f"out_path={self.ab_path} "
        return cmd_line.replace("/", "\\")

    def _calculate_package_result(self):
        """
        计算打包结果
        """
        reports = path_mgr.glob(self._report_path, "*.txt")
        if not reports:
            raise PyframeException(f"获取打包结果失败，请检查查看打包日志")
        report = reports[-1]
        with open(report, "r", encoding="utf-8") as f:
            content = f.readlines()
            # 解析打包结果
            report_detail = self._get_report_detail(content)
            # 记录打包结果
            log.debug(f"platform: {self._platform}, report_detail: {report_detail}")
            env_mgr.set_report(report=report_detail)

    def _get_report_detail(self, read_lines: List[str]) -> dict:
        """
        获取打包结果
        """
        success_pattern = self._success_pattern
        fail_patterns = self._fail_pattern
        total_count = 0
        succ_count = 0
        failed_count = 0
        error_count = 0
        success_ids = set()
        error_ids = set()
        error_info = set()
        index = 0
        while index < len(read_lines):
            if read_lines[index].startswith("[Info]Build Input Count"):
                first_index = read_lines[index].find("[", 6)
                second_index = read_lines[index].find("]", 6)
                total_count += int(read_lines[index][first_index + 1 : second_index])
            elif read_lines[index].startswith("[Info]Build Secceed  Count="):
                first_index = read_lines[index].find("[", 6)
                second_index = read_lines[index].find("]", 6)
                succ_num = int(read_lines[index][first_index + 1 : second_index])
                succ_count += succ_num
                while succ_num > 0:
                    index += 1
                    succ_num -= 1
                    if read_lines[index].find("global_dependeencies") != -1:
                        succ_count -= 1
                        total_count -= 1
                    if success_pattern:
                        suc = re.findall(success_pattern, read_lines[index])
                        if suc:
                            success_ids.update(suc)
            elif read_lines[index].startswith("[Info]Build Failed Count"):
                first_index = read_lines[index].find("==", 6)
                second_index = read_lines[index].find("]", 6)
                failed_num = int(read_lines[index][first_index + 2 : second_index])
                failed_count += failed_num
            elif read_lines[index].startswith("[Error]"):
                error_count += 1
                for fail_pattern in fail_patterns:
                    fail = re.findall(fail_pattern, read_lines[index])
                    if fail:
                        error_ids.update(fail)
                        error_info.add(read_lines[index][7:])
            index += 1

        # 过滤出仅归属成功列表的记录
        temp_list = set([success_id for success_id in success_ids if success_id not in error_ids])

        # 将只有extend成功的id过滤掉
        final_success_ids = self._filter_extend(temp_list, error_ids)
        report_detail = {
            "total_count": total_count,
            "succ_count": succ_count,
            "failed_count": failed_count,
            "error_count": error_count,
            "success_ids": list(final_success_ids),
            "error_ids": list(error_ids),
            "error_infos": list(error_info),
        }
        return report_detail

    def _filter_extend(self, success: set, fail: set) -> list:
        """
        在成功列表中过滤掉只有extend类型成功的id
        """
        pattern = re.compile(r"(\d+)_extend\d$")
        temp_list = []

        for i in success:
            ret = re.findall(pattern=pattern, string=i)
            if ret:
                if ret[0] not in fail:
                    temp_list.append(i)
            else:
                temp_list.append(i)
        return temp_list

    def submit(self):
        if not env_mgr.get_need_package():
            return
        self.__get_desc()
        self._output_check()
        # 提交前再拉一次配置仓库，降低冲突的可能性
        self._update_config_git()
        self._copy_product()
        self._submit_git()
        self._dist_ab()
        self._submit_p4()
        self._save_changelist()

    def _save_changelist(self):
        """
        记录最后一次打包的changelist
        """
        # 获取失败的资源
        report_detail = env_mgr.get_report()
        # 按照现有流程，只要流水线没有异常，都记录id继续往下走
        # 记录最后一次打包的changelist
        s_changelist = env_mgr.get_changelist()
        global_env_mgr.set_last_changelist(resource=self._kind, changelist=s_changelist)
        if report_detail["succ_count"] == 0:
            raise PyframeException(f"本次打包失败，请修改后重新提交资源")
        if report_detail["failed_count"] != 0 or report_detail["error_count"] != 0:
            raise PyframeException(f"存在打包失败的资源，请修改后重新提交资源")

    def __get_desc(self):
        # 原始资源提交人、流水线名称、流水线编号、流水线链接
        users = env_mgr.get_users()
        desc = f"--Auto--| uploader: {','.join(users)} | "
        desc += f"Identity:#Jenkins#{env.pipeline.function_name()}#{env.pipeline.build_num()} | "
        desc += f"link:{env.pipeline.build_url()}"
        self._desc = desc

    def _output_check(self):
        """
        输出检查
        """
        pass

    def _copy_product(self):
        """
        复制产物到指定位置
        """
        pass

    def _dist_ab(self):
        """
        分发产物，主要是ab包
        """
        pass

    def _submit_git(self):
        """
        提交git,主要是除了ab包之外的相关配置文件
        """
        pass

    def _get_submit_p4_view_ab(self):
        """
        获取待提交的ab包 p4view
        """
        return [self._ab_view]

    def _get_submit_p4_view_other(self):
        """
        获取待提交的其他文件view
        """
        return []

    def _submit_p4(self):
        """
        提交p4,主要是ab包
        """
        files = []
        files.extend(self._get_submit_p4_view_ab())
        files.extend(self._get_submit_p4_view_other())
        log.info(f"待提交文件: {files}")
        if not files:
            log.warning("没有需要提交的文件")
        else:
            submit_rets = self.p4_mgr.submit(views=files, desc=self._desc, revert_if_failed=True)
            if submit_rets:
                submitted_change = max(int(submit_rets[0].get("change")), int(submit_rets[-1].get("submittedChange", 0)))
                env_mgr.set_submit_changelist(changelist=str(submitted_change))
            # 返回 None 时，进行通知
            if submit_rets is None:
                report_detail = env_mgr.get_report()
                # NOTE: 加一层判断，当打包失败时，跳过通知
                if report_detail.get("failed_count") != 0 or report_detail.get("error_count") != 0:
                    return
                # 捕获异常，有问题不影响主流程
                try:
                    blue_ocean_url = env_mgr.get_jenkins_blue_ocean_url()
                    msg = "注意: 资源提交P4有可能出现问题，建议进行检查\n"
                    msg += f"链接: [{env.pipeline.pipeline_name()}]({blue_ocean_url})\n"
                    wechat.send_abnormal_unicast_msg(user_list=config.notice_extra_user_list, content=msg, add_maintainer=False)
                except Exception as e:
                    log.error("submit p4 notice error: %s", e)

    def always(self):
        if not env_mgr.get_need_package():
            return
        self._backup()
        self._upload()
        self._clean()

    def _backup(self):
        """
        备份中间产物，包括日志、报告等
        """
        reports = path_mgr.glob(self._report_path, "*.html")
        if not reports:
            log.warning("无打包报告生成")
        else:
            report = reports[0]
            renamed_report = rf"{self._report_path}\{self._kind}_{self._platform}_report_{env.pipeline.build_num()}.html"
            log.info(f"重命名打包报告为: {renamed_report}")
            path_mgr.move(report, renamed_report)
        if not path_mgr.exists(self._backup_root):
            path_mgr.mkdir(self._backup_root)
        if os.listdir(self._report_root):
            tar.compress(self._report_root, self._backup_path)

        # 针对鸿蒙历史资源的bakcup  正式上线可以删掉
        if self.__history_source:
            self._backup_success_source()
            self._backup_error_source()

    def _upload(self):
        if path_mgr.exists(self._backup_path):
            url = advance.upload_pipeline_log(self._backup_path)
            if url:
                env_mgr.set_log(url)
        report_path = rf"{self._report_path}\{self._kind}_{self._platform}_report_{env.pipeline.build_num()}.html"
        if path_mgr.exists(report_path):
            report_url = advance.upload_pipeline_log(report_path)
            if report_url:
                env_mgr.set_package_report(report_url)

    def _clean(self):
        """
        清理备份目录，默认保留 config.back_up_days(100) 天
        """
        today = datetime.date.today()
        beyond_date = today + datetime.timedelta(days=-config.back_up_days)

        for file in os.listdir(self._backup_root):
            file_path = os.path.join(self._backup_root, file)
            if self.__get_create_time(file_path) < beyond_date:
                path_mgr.rm(file_path)

    def __get_create_time(self, path: str):
        timestamp = os.path.getctime(path)
        return datetime.datetime.fromtimestamp(timestamp).date()

    def get_msg_user(self, pipeline_status: Optional[str] = None):
        """获取通知消息和通知用户
        如果消息内容不满足，可以自定义
        """
        content = self.get_msg(pipeline_status=pipeline_status)
        user = MsgMgr.get_user()
        user.extend(MsgMgr.get_pm_users())
        user = list(set(user))
        user = self.__user_email_process(user)
        return content, user

    def get_msg(self, pipeline_status: Optional[str] = None) -> str:
        return MsgMgr.get_msg(pipeline_status=pipeline_status)

    def __user_email_process(self, users: List[str]) -> List[str]:
        """
        用户邮箱特殊处理
        """
        ret = []
        for user in users:
            if user == "<EMAIL>":
                ret.append("<EMAIL>")
            else:
                ret.append(user)
        return ret

    """
    # 针对历史资源的特殊处理
    """

    @property
    def __history_source(self):
        history_source_set = {ResourceTypeEnum.TIMELINE, ResourceTypeEnum.BODYPART, ResourceTypeEnum.NPC}
        if self._platform != PlatformEnum.OPENHARMONY:
            return False
        if self._kind not in history_source_set:
            return False
        return True

    def _backup_success_source(self):
        self.__backup_depot_file("packaged.json", "success_ids")

    def _backup_error_source(self):
        self.__backup_depot_file("unpackaged.json", "error_ids")

    def __backup_depot_file(self, filename, status):
        filename = f"{self._kind}_{filename}"
        file_map = {}
        source_file = os.path.join(self._workspace, filename)
        if path_mgr.exists(source_file):
            with open(source_file, "r", encoding="utf-8") as file:
                file_map = json.load(file)
        source_path_change_dct = env.get("source_path_change_dct", {})
        report_detail = env_mgr.get_report()
        status_ids = set(report_detail[status])
        for status_id in status_ids:
            for depot_file in source_path_change_dct.keys():
                id = re.findall(re.compile(r"\d{6,}", re.S), depot_file)
                if not id:
                    continue
                if id[0] != status_id:
                    continue
                change = source_path_change_dct[depot_file]
                if depot_file in file_map:
                    if change > int(file_map[depot_file]):
                        file_map[depot_file] = change
                else:
                    file_map[depot_file] = change
                break
        with open(source_file, "w", encoding="utf-8") as file:
            json.dump(file_map, file, indent=4)

    def _filter_packaged_source(self, depot_files: set) -> set:
        """
        过滤已经打过包的资源
        """
        if self._platform != PlatformEnum.OPENHARMONY:
            return
        packaged_source_file = os.path.join(self._workspace, "packaged.json")
        if not path_mgr.exists(packaged_source_file):
            return depot_files
        with open(packaged_source_file, "r", encoding="utf-8") as file:
            packaged_file_map = json.load(file)
        res = set()
        for depot_file in depot_files:
            if depot_file in packaged_file_map:
                last_package_filelist = packaged_file_map[depot_file]
                change = self.p4.filelog_latest(depot_file).get("change")
                if change <= last_package_filelist:
                    continue
            res.add(depot_file)
        return res

    def _get_large_ab(self, files_path: list) -> list:
        large_ab = []
        for file_path in files_path:
            file_type = FileEnum.get_file_type(file_path)
            file_size = os.path.getsize(file_path) / 1024 / 1024
            if file_size > file_type.max_filesize:
                large_ab.append(file_path)
        return large_ab
