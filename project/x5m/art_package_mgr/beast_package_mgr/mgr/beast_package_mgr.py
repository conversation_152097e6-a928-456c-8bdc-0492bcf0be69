import os
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, ResourceTypeEnum, config, env_mgr


class BeastPackageMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.Beast, **kwargs)
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/beast/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: list) -> set:
        depots = set()
        searched_path_list = []
        for file in source_paths:
            file: str
            if not file.startswith(self._resource_dir_prefix):
                continue
            if file.startswith(f"{self._resource_dir_prefix}/actions"):
                if file.lower().endswith(".anim") or file.lower().endswith(".prefab"):
                    depots.add(file)
            if file.startswith(f"{self._resource_dir_prefix}/models"):
                search_path = "/".join(file.split("/")[:9])
                if search_path in searched_path_list:
                    continue
                files_prefab = self.p4.files(f"{search_path}/*prefab") + self.p4.files(f"{search_path}/.../*prefab")
                files_anim = self.p4.files(f"{search_path}/*anim") + self.p4.files(f"{search_path}/.../*anim")
                for search_result in files_prefab + files_anim:
                    depots.add(search_result)
        return depots

    def _fill_source_p4_views(self):
        views = set()
        for path in env_mgr.get_source_paths():
            base_path = path.rsplit("/", 1)[0]
            new_path = base_path + "/..."
            views.add(new_path)
        self.p4_mgr.add_p4_views(list(views))

    def _get_submit_p4_view_ab(self):
        files = set()
        with open(self._resources_path, "r", encoding="UTF-8") as f:
            file_paths = f.read()
        resources = file_paths.split(",")
        for submit_file in resources:
            submit_file: str
            if submit_file.lower().find("assets/resources/art/beast") == -1:
                continue

            submit_file = submit_file.lower().replace("assets/resources/art", self._ab_view + "art")
            file, _ = os.path.splitext(submit_file)
            files.add(file)
        return list(files)
