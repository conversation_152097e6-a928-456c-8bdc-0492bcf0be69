import re
from typing import List
from frame import *

from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, config, env_mgr, X5mGitMgr, ResourceTypeEnum, PlatformEnum


class BodypartPackageMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(kind=ResourceTypeEnum.BODYPART, **kwargs)
        self._resource_dir_prefix = f"{config.p4_views.get('art_resource_root')}/role/bodypart"
        self._execute_method = "H3DBuildTools.BuildBodyPartOrLink"
        self._const_source_p4_views = [f"{config.p4_views.get('art_resource_root')}/role/cloth_show/..."]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/role/bodypart/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"
        #  因为资源名称类型增加，暂时会包含下面，后续增加再调整
        """
        1 1408901501
        2 1408901501_bonedata_extend1
        3 1408901501_bonedata_extend2
        4 1408901501_bonedata_extend3
        5 1408901501_collider_extend1
        6 1408901501_collider_extend2
        7 1408901501_collider_extend3
        8 1408901501_extend1
        9 1408901501_extend2
        10 1408901501_extend3
        """
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s.*?(\d+$|\d+_extend\d$|\d+_collider_extend\d$|\d+_bonedata_extend\d$)")

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for file in source_paths:
            file: str
            if not file.startswith(self._resource_dir_prefix):
                continue
            if file.find("bodypart/female/plainBody/") != -1 or file.find("bodypart/male/plainBody/") != -1 or file.find("/unencrypt/") != -1:
                continue
            dir_name = os.path.dirname(file)
            file_relative_path = dir_name.replace(self._resource_dir_prefix, "")
            package_list = file_relative_path.split("/")
            # self._resource_dir_prefix + "/*/*/*"
            if len(package_list) < 4:
                continue
            id = re.findall(r"[0-9]{10}", dir_name)
            if not id:
                continue
            id_index = dir_name.find(id[0])
            if id_index == -1:
                continue
            dir_name = dir_name[0:id_index] + id[0] + "/"
            depots.add(dir_name)
        return depots

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _fill_source_p4_views(self):
        views = []
        for path in env_mgr.get_source_paths():
            # id后两位改成*
            views.append(path[:-3] + "*/")
        self.p4_mgr.add_p4_views(views)

    def _make_resouces_file(self):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                bodypart_id = path.split("/")[-2]
                lines.append(bodypart_id)
            f.write(",".join(lines))

    def _update_config_git(self) -> None:
        git_mgr = X5mGitMgr(self._workspace, "x5mconfig", "cdn")
        git_mgr.update(clean=True)
        # last_branch = git_mgr.get_last_branch()
        # last_git_mgr = X5mGitMgr(self._workspace, "x5mconfig", last_branch)
        # last_git_mgr.update()
        self._git_mgrs: list[X5mGitMgr] = [git_mgr]

    def _copy_product(self):
        for git_mgr in self._git_mgrs:
            submit_file_path = os.path.join(self._workspace, f"x5mconfig/{git_mgr.branch}/config/shared/customdyeing")
            if not path_mgr.exists(submit_file_path):
                path_mgr.mkdir(submit_file_path)
            for file in env_mgr.get_source_paths():
                path = self.p4_mgr.parse_p4view_to_localpath(file)
                if not os.path.exists(path):
                    continue
                for filename in os.listdir(path):
                    if filename.endswith(".xml") and "_custom_dye" in filename:
                        log.info(f"need submit related config file `{filename}`, full path: `{path}`")
                        src = os.path.join(path, filename)
                        path_mgr.xcopy(src, submit_file_path, dst_is_file=False)

    def _submit_git(self):
        # git只用提交一次
        if self._platform != PlatformEnum.IOS:
            return
        for git_mgr in self._git_mgrs:
            git_mgr.submit_path(self._desc, os.path.join(self._workspace, f"x5mconfig/{git_mgr.branch}/config/shared/customdyeing"))

    def _get_submit_p4_view_ab(self):
        files = []
        failed_id = env_mgr.get_report()["error_ids"]
        for submit_file in env_mgr.get_source_paths():
            submit_file: str
            if submit_file == "":
                continue
            id = submit_file.split("/")[-2]
            if id in failed_id:
                continue
            # submit_file = self.p4_mgr.parse_p4view_to_localpath(submit_file.replace(self._resource_dir_prefix, self._ab_view + "art/role/bodypart"))
            f, _ = os.path.splitext(submit_file.replace(self._resource_dir_prefix, self._ab_view + "art/role/bodypart"))
            if f[-3:] == "01/":
                files.append(f)
                f = f[:-1]
                for i in range(10):
                    # extend_path = f"{f}_extend{i + 1}/"
                    extend_paths = [f"{f}_extend{i + 1}/", f"{f}_collider_extend{i + 1}/", f"{f}_bonedata_extend{i + 1}/"]
                    files.extend(extend_paths)
            else:
                f = f[0:-3] + "01/"
                files.append(f)
        return files
