node {
    // 使用 BuildUser 包装器设置环境域
    wrap([$class: 'BuildUser']) {
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

pipeline {
    agent none
    // 设置全局选项
    options {
        timestamps()
        disableConcurrentBuilds()
        durabilityHint('PERFORMANCE_OPTIMIZED')
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder(logRotator(numToKeepStr: env.numsToKeepStr, daysToKeepStr: env.daysToKeepStr))
    }
    // 设置允许输入的参数
    parameters {
        text(name: 'first_changelist', defaultValue: '-1', description: '输入开始打包的changelist')
        booleanParam(name: 'dev', defaultValue: true, description: '是否为开发模式')
        text(name: 'force_ids', defaultValue: '', description: '强制打包id列表, 多个id使用英文半角逗号隔开')
    }
    // NOTE: 长时间没有资源变动，先禁用周期打包
    // 流水线配置中设置对应的执行周期，避免因为P4备份时间变更，影响构建
    // triggers {
    //     cron(env.BUILD_PERIOD)
    // }
    // 设置流水线对应的环境变量
    environment {
        PIPELINE_PYTHON = 'pipeline-python'
        RESOURCE = 'camera_action'
    }

    // 定义流水线步骤
    stages {
        stage('美术资源打包') {
            matrix {
                agent {
                    node {
                        label "art_package_${platform}"
                        customWorkspace "${env.workspace}/.."
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'android', 'ios'  // 暂不需要添加 harmony 平台
                    }
                }
                stages {
                    // 安装python脚本环境依赖
                    stage('更新流水线依赖') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    bat(script: "${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q")
                                }
                            }
                        }
                    }
                    // 准备工作，清理目录，检查参数，检查依赖工具
                    stage('准备') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=prepare")
                                }
                            }
                        }
                    }
                    // 根据changelist，判断是否需要打包
                    stage('检查是否需要打包') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=check_package")
                                }
                            }
                        }
                    }
                    // 更新git和p4资源
                    stage('获取p4资源') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=update_git_and_p4")
                                }
                            }
                        }
                    }
                    // 进行打包
                    stage('调用打包命令') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=package")
                                }
                            }
                        }
                    }
                    // 提交打包后的产物
                    stage('提交产物') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=submit")
                                }
                            }
                        }
                    }
                }
                // 定义矩阵的后置处理
                post {
                    always {
                        dir('pyframe-pipeline') {
                            script {
                                bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_always")
                            }
                        }
                    }
                }
            }
        }
    }
    // 统一的后置处理
    post {
        unstable {
            node('art_package_ios') {
                // 默认路径为 {slave workspace}/workspace/job_name  所以需要加两个 /..
                withEnv(["WORKSPACE=${env.WORKSPACE}/../.."]) {
                    dir("${env.workspace}/pyframe-pipeline") {
                        script {
                            bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_unstable")
                        }
                    }
                }
            }
        }
        success {
            node('art_package_ios') {
                withEnv(["WORKSPACE=${env.WORKSPACE}/../.."]) {
                    dir("${env.workspace}/pyframe-pipeline") {
                        script {
                            bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_success")
                        }
                    }
                }
            }
        }
        failure {
            node('art_package_ios') {
                withEnv(["WORKSPACE=${env.WORKSPACE}/../.."]) {
                    dir("${env.workspace}/pyframe-pipeline") {
                        script {
                            bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_failure")
                        }
                    }
                }
            }
        }
    }
}
