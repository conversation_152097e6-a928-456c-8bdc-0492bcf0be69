from project.x5m.art_package_mgr.base_mgr.package_mgr import config


DIR_PREFIX = "/role/split/camera_anim"

# Unity的支持配置，用于执行连续构建，单测等任务
EXECUTE_METHOD = "H3DBuildTools.BuildAction"

# p4 view
SOURCE_P4_VIEW_LIST = [
    f"{config.p4_views.get('art_resource_root')}{DIR_PREFIX}/...",
]


# trad p4 view
SOURCE_TRAD_P4_VIEW_LIST = [
    f"{config.p4_trad_views.get('art_resource_root')}{DIR_PREFIX}/...",
]
