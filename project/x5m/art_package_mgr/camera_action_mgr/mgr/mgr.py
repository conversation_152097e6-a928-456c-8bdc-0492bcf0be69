from typing import List

from frame import *

from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr
from project.x5m.art_package_mgr.camera_action_mgr.mgr import *
from project.x5m.art_package_mgr.camera_action_mgr.mgr.utils import render_source_p4_view_list, render_ab_p4_view_list


class CameraActionMgr(DistributePackageMgr):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.CAMERA_ACTION, *args, **kwargs)
        # 资源拉去到的路径前缀
        self._resource_dir_prefix = f"{config.p4_views.get('art_resource_root')}{DIR_PREFIX}"
        # 打包命令执行的静态方法
        self._execute_method = EXECUTE_METHOD
        # 打包拉取的资源
        self._b_path = self.p4_mgr.get_last_branch()
        self._const_source_p4_views = render_source_p4_view_list()
        self._const_ab_p4_views = render_ab_p4_view_list(self._platform, self._b_path)
        self._log_file = os.path.join(self._log_root, f"{self._platform}_{env.pipeline.build_num()}.log")

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for fp in source_paths:
            # 首先校验是否是对应的资源前缀，如果不满足，则直接跳过
            if not fp.startswith(self._resource_dir_prefix):
                continue
            # 获取路径，并按照规则匹配路径是否满足条件，如果满足条件追加到记录中
            if fp.endswith((".anim", ".prefab")):
                depots.add(fp)
        return depots
