from typing import List

from project.x5m.art_package_mgr.base_mgr.package_mgr import config, LanguageEnum

from . import SOURCE_P4_VIEW_LIST, SOURCE_TRAD_P4_VIEW_LIST


def render_ab_p4_view_list(platform: str, b_path: str, language: str = LanguageEnum.CHIN_SIMP.value) -> List[str]:
    """渲染资产包P4视图列表

    NOTE: 暂时不考虑繁中
    """
    if language == LanguageEnum.CHIN_TRAD.value:
        return []
    return [
        f"{config.p4_views.get('cdn_root')}/cooked/{platform}/assetbundles/art/role/split/camera_anim/...",
    ]


def render_source_p4_view_list(language: str = LanguageEnum.CHIN_SIMP.value) -> List[str]:
    """渲染源文件P4视图列表

    NOTE: 暂时不考虑繁中
    """
    if language == LanguageEnum.CHIN_TRAD.value:
        return SOURCE_TRAD_P4_VIEW_LIST
    return SOURCE_P4_VIEW_LIST
