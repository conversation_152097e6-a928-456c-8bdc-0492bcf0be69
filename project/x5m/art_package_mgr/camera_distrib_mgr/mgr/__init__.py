from project.x5m.art_package_mgr.base_mgr.package_mgr import config


DIR_PREFIX = "/camera"
CAMERA_SPECIAL_FILE = f"{config.p4_views['resmg_root']}/camera/special-camera-map.xlsx"

# Unity的支持配置，用于执行连续构建，单测等任务
EXECUTE_METHOD = "H3DBuildTools.BuildArt"

# p4 view
SOURCE_P4_VIEW_LIST = [
    # f"{config.p4_views.get('art_resources_src')}{DIR_PREFIX}/...",
    f"{config.p4_views.get('resmg_root')}{DIR_PREFIX}/...",
]


# trad p4 view
SOURCE_TRAD_P4_VIEW_LIST = [
    f"{config.p4_trad_views.get('art_resources_src')}{DIR_PREFIX}/...",
    f"{config.p4_trad_views.get('trad_resmg_root')}{DIR_PREFIX}/...",
]
