import re
from collections import defaultdict
from typing import List

from frame import *

from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr, env_mgr
from project.x5m.art_package_mgr.base_mgr.enum_mgr import DisTributeEnum
from project.x5m.art_package_mgr.camera_distrib_mgr.mgr import *
from project.x5m.art_package_mgr.camera_distrib_mgr.mgr.utils import render_source_p4_view_list, render_ab_p4_view_list


class CameraDistribMgr(DistributePackageMgr):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.CAMERA_DISTRIB, *args, **kwargs)
        # 资源拉去到的路径前缀
        self._resource_dir_prefix = f"{config.p4_views.get('art_resources_src')}{DIR_PREFIX}"
        # 打包命令执行的静态方法
        self._execute_method = EXECUTE_METHOD
        # 打包拉取的资源
        self._b_path = self.p4_mgr.get_last_branch()
        self._const_source_p4_views = render_source_p4_view_list()
        self._const_ab_p4_views = render_ab_p4_view_list(self._platform, self._b_path)
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s.*?/camera/([\w\/]+$)")
        self._ab_view = f"{config.p4_views.get('cdn_root')}/cooked_output/{self._platform}/assetbundles/"

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        """
        过滤满足条件的资源路径

        Args:
            source_paths: 资源路径列表
        Returns:
            满足条件的资源路径
        """
        depots = set()
        for fp in source_paths:
            # 首先校验是否是对应的资源前缀，如果不满足，则直接跳过
            if not fp.startswith(self._resource_dir_prefix):
                continue
            # 获取路径，并按照规则匹配路径是否满足条件，如果满足条件追加到记录中
            if fp.endswith((".FBX", ".fbx", ".prefab")):
                depots.add(fp)
        return depots

    def _default_distribute(self) -> List[str]:
        return [
            config.p4_views["resources_root"].format(branch="Resources"),
            config.p4_views["resources_root"].format(branch=f"b/{self._b_path}"),
        ]

    def _make_resouces_file(self):
        """组装打包命令需要的文件路径"""
        super()._make_resouces_file(p4_view_type="art_resources_src")

    def _get_distruibute_dict(self):
        """获取分发路径"""
        success_ids = env_mgr.get_report().get("success_ids")
        dist_destinations = defaultdict(list)
        for id in success_ids:
            for distribute in self._distribute_config:
                _id = id.split("/")[-1]
                ret = distribute.match(_id)
                if ret:
                    self._distribute_map[distribute.distribute_type].append((id, [ret]))
                    dist_destinations[str(distribute.distribute_type)].append(id)
                    break
            else:
                self._distribute_map[DisTributeEnum.DEFAULT].append((id, self._default_distribute()))
                dist_destinations[str(DisTributeEnum.DEFAULT)].append(id)
        log.info("dist_destinations: %s", dist_destinations)
        env_mgr.set_dist_destinations(dist_destinations)
        log.info("distribute_map: %s", self._distribute_map)
        return self._distribute_map

    def _get_special_branch(self, data_list_special) -> str:
        max_branch = self._b_path
        special_data_branches = []
        for special_data in data_list_special:
            for special_data_name in special_data:
                special_data_branch = special_data[special_data_name]
                if special_data_branch not in special_data_branches and special_data_branch.startswith(max_branch):
                    special_data_branches.append(special_data_branch)
        if special_data_branches:
            return special_data_branches[0]
        return ""

    def _get_file_root(self, dis_type: DisTributeEnum, root: str) -> str:
        if dis_type == DisTributeEnum.CDNBASE or dis_type == DisTributeEnum.CDNRULE:
            return f"{root}/cooked/{self._platform}/assetbundles/art{DIR_PREFIX}/"
        if dis_type == DisTributeEnum.SPECIAL:
            return f"{config.p4_views.get('resources_root').format(branch='b')}/{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art{DIR_PREFIX}/"
        if dis_type == DisTributeEnum.HOTFIX:
            return f"{root}/client/{self._platform}/assetbundles/art{DIR_PREFIX}/"
        if dis_type == DisTributeEnum.DEFAULT:
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art{DIR_PREFIX}/"
        raise PyframeException(f"Invalid distribute type {dis_type}")

    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            if distribute_type == DisTributeEnum.SPECIAL:
                for distribute in self._distribute_config:
                    if distribute.distribute_type == DisTributeEnum.SPECIAL:
                        special_branch = self._get_special_branch(distribute.distribute_rule.items())
                        if special_branch:
                            views.add(
                                f"{config.p4_views['resources_root'].format(branch=f'b/{special_branch}')}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art{DIR_PREFIX}/"
                            )
            for _, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    file_root = self._get_file_root(distribute_type, path_root)
                    views.add(file_root)
        log.info("ab views: %s", views)
        self.p4_mgr.add_p4_views(list(views))

    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    view = f"{self._get_file_root(distribute_type, path_root)}{id}"
                    src = f"{self.ab_path.rstrip('/')}/art{DIR_PREFIX}/{id}"
                    _view = self.p4_mgr.parse_p4view_to_localpath(view)
                    if src != _view:
                        path_mgr.xcopy(src, _view, dst_is_file=True)
                    self._submit_ab_views.append(view)
        log.info("dist_ab_file: %s", self._submit_ab_views)
