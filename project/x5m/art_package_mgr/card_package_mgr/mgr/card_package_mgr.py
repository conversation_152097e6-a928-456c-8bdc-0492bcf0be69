import re
from typing import List, Optional

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, ResourceTypeEnum, X5mGitMgr, config, env_mgr, path_mgr, PlatformEnum


class CardPackageMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.CARD, **kwargs)
        self._language = env.get("LANGUAGE", "chin_simp")
        if self._language == "chin_trad":
            self._resource_dir_prefix = "//美术资源/炫舞手游-ui/7-UI常规资源_trad/名片夹切图资源"
            self._card_path = f"{self._workspace}/美术资源/炫舞手游-ui/7-UI常规资源_trad/名片夹切图资源"
        else:
            self._resource_dir_prefix = "//美术资源/炫舞手游-ui/6-UI常规资源/名片夹切图资源"
            self._card_path = f"{self._workspace}/美术资源/炫舞手游-ui/6-UI常规资源/名片夹切图资源"
        self._export_report = f"{self._project_path}/tempFolder/CardArtError.txt"
        self._export_log = os.path.join(self._log_root, f"{self._platform}_{env.pipeline.build_num()}_export_card.log")
        self._sprite_log = os.path.join(self._log_root, f"{self._platform}_{env.pipeline.build_num()}_sprite.log")
        self._suit_log = os.path.join(self._log_root, f"{self._platform}_{env.pipeline.build_num()}_suit.log")
        self._project_art_path = os.path.join(self._project_path, "Assets", "StaticResources", "Art")
        self._suit_resource_path = os.path.join(self._project_path, "Assets", "ab_resources")
        self._sprite_ab_p4_view = f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/uiprefabs/newui/playercard/prefabatlas"
        self._texture_ab_p4_view = f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/texture/card_suit"
        self._const_ab_p4_views = [
            f"{self._sprite_ab_p4_view}/...",
            f"{self._texture_ab_p4_view}/...",
        ]
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s.*?(\d{9,}).*")
        self._fail_pattern = [re.compile(r"^\[Error]\s+\d+\s.*?(\d{9,}).*")]
        self._max_tc_branch = self.p4_mgr.get_last_trad_branch()
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f4454687-a463-4b06-bfb1-94d85987cb29"

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for file in source_paths:
            file: str
            if not file.startswith(self._resource_dir_prefix):
                continue
            if not file.lower().endswith(".png"):
                continue
            file = file.rsplit("/", 1)[0] + "/"
            id = file.rsplit("/", 2)[1]
            if id.isdigit():
                depots.add(file)
        return depots

    def _fill_other_p4_views(self):
        """
        填充其他p4view
        """
        if self._language == "chin_trad":
            source_paths = env_mgr.get_source_paths()
            depots = set()
            # NOTE: 因为繁中仅有字体变动的图片，需要把简中对应目录的内容也要拉取下来，然后合并进行打包
            # //美术资源/炫舞手游-ui/7-UI常规资源_trad/名片夹切图资源/926103566/hjjb_PartnerWordIcon_icon.png
            # 进行繁中打包时，替换//美术资源/炫舞手游-ui/6-UI常规资源/名片夹切图资源/926103566/hjjb_PartnerWordIcon_icon.png
            for file in source_paths:
                file = file.replace("7-UI常规资源_trad", "6-UI常规资源")
                depots.add(file.rsplit("/", 1)[0] + "/")
            self.p4_mgr.add_p4_views(list(depots))

    def _clean_resource(self) -> None:
        """
        清理资源
        """
        self._safe_clean(self._project_art_path, "开始清理原始资源")
        self._safe_clean(self._suit_resource_path, "开始清理suit原始资源")
        self._safe_clean(self._card_path, "开始清理美术资源")
        if self._language == "chin_trad":
            self._safe_clean(self._card_path.replace("7-UI常规资源_trad", "6-UI常规资源"), "开始清理美术资源")
        self._safe_clean(self.ab_path, "开始清理ab资源")
        self._safe_clean(self._report_root, "开始清理打包日志")

    def _update_config_git(self) -> None:
        if self._language == "chin_simp":
            config_git_mgr = X5mGitMgr(self._workspace, "x5mconfig", "cdn")
            config_git_mgr.update(clean=True)
            self._config_git_mgr = config_git_mgr
        mobile_git_mgr = X5mGitMgr(self._workspace, "x5mobile", "master")
        if self._language == "chin_trad":
            mobile_git_mgr = X5mGitMgr(self._workspace, "x5mobile", self._max_tc_branch)
        mobile_git_mgr.update(clean=True)
        self._mobile_git_mgr = mobile_git_mgr

    def __copy_simple_resource(self) -> None:
        """
        拷贝简体资源
        """
        # NOTE: 如果目的文件存在，则不会覆盖
        path_mgr.copy_new_file(self._card_path.replace("7-UI常规资源_trad", "6-UI常规资源"), self._card_path)

    def _package(self):
        """
        打包
        TODO 部分成功处理
        """
        if self._language == "chin_trad":
            self.__copy_simple_resource()
        self.__export_card()
        self.__parse_export_result()
        self.__update_sprite()
        self.__sprite_package()
        self._sprite_result = self.__calculate_package_result()
        self.__update_suit()
        self.__suit_package()
        self._suit_result = self.__calculate_package_result()
        self._set_report_detail()

    def package(self):
        if not env_mgr.get_need_package():
            return
        self._input_check()
        self._package()

    def _set_report_detail(self):
        self._suit_result.update(self._export_result)
        self._suit_result["sprite_success_ids"] = self._sprite_result.get("success_ids", [])
        self._suit_result["sprite_error_ids"] = self._sprite_result.get("error_ids", [])
        self._suit_result["sprite_error_infos"] = self._sprite_result.get("error_infos", [])
        log.info(f"report detail: {self._suit_result}")
        env_mgr.set_report(report=self._suit_result)

    def __calculate_package_result(self):
        """
        计算打包结果
        """
        reports = path_mgr.glob(self._report_path, "*.txt")
        if not reports:
            raise PyframeException(f"获取打包结果失败，请检查查看打包日志")
        reports.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        report = reports[-1]
        with open(report, "r", encoding="utf-8") as f:
            content = f.readlines()
            # 解析打包结果
            report_detail = self._get_report_detail(content)
            # 记录打包结果
            log.debug(f"platform: {self._platform}, report_detail: {report_detail}")
            error_ids = report_detail.get("error_ids", [])
            if len(error_ids) != 0:
                error_infos = report_detail.get("error_infos", [])
                raise PyframeException(f"存在打包失败的名片: {error_ids}, {error_infos}")
            return report_detail

    def __parse_export_result(self):
        if not path_mgr.exists(self._export_report):
            raise PyframeException(f"导出名片结果文件: {self._export_report} 不存在, 无法解析导出结果, 请联系工具组查看日志解决")
        log_text = Path(self._export_report).read_text(encoding="utf-8")
        log.info(f"导出名片结果: {log_text}")

        success_ids = re.findall(re.compile(r"success 成功处理: ([0-9]+)", re.S), log_text)
        if success_ids:
            success_ids = list(set(success_ids))
            log.info(f"导出名片成功success_ids: {success_ids}")
        error_ids = re.findall(re.compile(r"error ([0-9]+).*?", re.S), log_text)

        if error_ids:
            error_ids = list(set(error_ids))
            log.info(f"导出名片失败error_ids: {error_ids}")
            if not success_ids:
                raise PyframeException(f"存在导出失败的名片: {error_ids}, {log_text}")
        self._export_result = {"export_success_ids": success_ids, "export_error_ids": error_ids, "export_error_infos": log_text}

    def __export_card(self):
        cmd_line = f"{self._platform.package_tool} "
        cmd_line += f"-quit -batchmode -projectPath {self._project_path} -logFile {self._export_log} "
        cmd_line += f"-executeMethod TreatCardArtRes.TreatmentForArtRes -buildTarget {self._platform} "
        cmd_line += f"-paths {self._card_path} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._export_log)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def __update_sprite(self):
        """
        更新图集，写resources_file
        """
        newui_path = f"{self._project_art_path}/UIAtlas/NewUI/UIAtlasCardSuit"
        if not path_mgr.exists(newui_path):
            path_mgr.mkdir(newui_path)

        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        lines = []
        for path in env_mgr.get_source_paths():
            id = path.split("/")[-2]
            failed_ids = self._export_result.get("export_error_ids", [])
            if id in failed_ids:
                continue
            sprite_path = f"{self._card_path}/{id}/{id}/sprite"
            path_mgr.xcopy(sprite_path, newui_path, dst_is_file=False)
            prefab_path = f"Assets/StaticResources/art/UIAtlas/NewUI/UIAtlasCardSuit/PrefabAtlas_{id}.prefab"
            lines.append(prefab_path)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            f.write(",".join(lines))

    def __sprite_package(self):
        ab_p4_view = f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles"
        sprite_ab_path = self.p4_mgr.parse_p4view_to_localpath(ab_p4_view)
        cmd_line = f"{self._platform.package_tool} "
        cmd_line += f"-quit -batchmode -projectPath {self._project_path} -logFile {self._sprite_log} "
        cmd_line += f"-executeMethod {self._execute_method} -buildTarget {self._platform} "
        cmd_line += f"out_path={sprite_ab_path} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._sprite_log)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def __update_suit(self):
        suit_path = f"{self._project_path}/Assets/ab_resources/texture/card_suit"
        if not path_mgr.exists(suit_path):
            path_mgr.mkdir(suit_path)
        for path in env_mgr.get_source_paths():
            id = path.split("/")[-2]
            failed_ids = self._export_result.get("export_error_ids", [])
            if id in failed_ids:
                continue
            texture_path = f"{self._card_path}/{id}/{id}/texture"
            path_mgr.xcopy(texture_path, f"{suit_path}/{id}", dst_is_file=False)

    def __suit_package(self):
        ab_p4_view = self._texture_ab_p4_view = f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles"
        suit_ab_path = self.p4_mgr.parse_p4view_to_localpath(ab_p4_view)
        cmd_line = f"{self._platform.package_tool} "
        cmd_line += f"-quit -batchmode -projectPath {self._project_path} -logFile {self._suit_log} "
        cmd_line += f"-executeMethod H3DBuildTools.BuildAB -buildTarget {self._platform} "
        cmd_line += f"outpath={suit_ab_path} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._suit_log)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _copy_product(self):
        self.__copy_sprite()
        if self._language == "chin_simp":
            self.__copy_card_decorate_xml()
        self.__copy_card_suit_ab()

    def _compose_card_suit_path(self):
        """组装需要提交到git的目录"""
        # 最终需要复制到指定的目录后缀
        _copy2path_flag = "client_region/trad" if self._language == "chin_trad" else "client_region/simp"
        return f"x5mobile/{self._mobile_git_mgr.branch}/mobile_dancer/trunk/{_copy2path_flag}/Assets/StaticResources/art/UIAtlas/NewUI/UIAtlasCardSuit"

    def __copy_sprite(self):
        success_ids = env_mgr.get_report().get("success_ids")
        _copy2path = self._compose_card_suit_path()
        for success_id in success_ids:
            src_dir = os.path.join(self._card_path, success_id, success_id, "sprite")
            sprites = path_mgr.glob(src_dir, f"*UIAtlasCardSuit_{success_id}*")
            for sprite in sprites:
                basename = os.path.basename(sprite)
                card_suit_path = os.path.join(self._workspace, _copy2path)
                dst = os.path.join(card_suit_path, basename)
                path_mgr.xcopy(sprite, dst, dst_is_file=True)

    def __copy_card_decorate_xml(self):
        success_ids = env_mgr.get_report().get("success_ids")
        for success_id in success_ids:
            src_dir = os.path.join(self._card_path, success_id, success_id, f"{success_id}.xml")
            dst = os.path.join(self._workspace, f"x5mconfig/{self._config_git_mgr.branch}/config/card_decorate", f"{success_id}.xml")
            path_mgr.xcopy(src_dir, dst, dst_is_file=True)

    def __copy_card_suit_ab(self):
        success_ids = env_mgr.get_report().get("success_ids")
        ab_p4_view = f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles"
        package_sprite_ab_path = self.p4_mgr.parse_p4view_to_localpath(ab_p4_view)
        local_sprite_ab_path = self.p4_mgr.parse_p4view_to_localpath(self._sprite_ab_p4_view)
        for success_id in success_ids:
            src_dir = os.path.join(package_sprite_ab_path, f"assets/staticresources/art/uiatlas/newui/uiatlascardsuit/prefabatlas_{success_id}")
            dst = os.path.join(local_sprite_ab_path, f"prefabatlas_{success_id}")
            path_mgr.xcopy(src_dir, dst, dst_is_file=True)

    def _submit_git(self):
        # git只用提交一次
        if self._platform != PlatformEnum.IOS:
            return
        _copy2path = self._compose_card_suit_path()
        card_suit_path = os.path.join(self._workspace, _copy2path)
        self._mobile_git_mgr.submit_path(self._desc, card_suit_path)
        if self._language == "chin_simp":
            self._config_git_mgr.submit_path(
                self._desc, os.path.join(self._workspace, f"x5mconfig/{self._config_git_mgr.branch}/config/card_decorate")
            )

    def _get_submit_p4_view_ab(self):
        """
        提交p4,主要是ab包，提交改到文件级别
        """
        files = set()
        report_detail = env_mgr.get_report()
        sprite_success_ids = report_detail.get("sprite_success_ids", [])
        suit_success_ids = report_detail.get("success_ids", [])
        for success_id in sprite_success_ids:
            files.add(f"{self._sprite_ab_p4_view}/prefabatlas_{success_id}")
        for success_id in suit_success_ids:
            files.add(f"{self._texture_ab_p4_view}/{success_id}")

        return list(files)

    def get_msg(self, pipeline_status: Optional[str] = None) -> str:
        all_msg = []
        for platform in PlatformEnum:
            msg = ""
            if platform == PlatformEnum.OPENHARMONY and not env_mgr.is_dev():
                continue
            msg += f"# **<font color='orange'>{platform}</font>**: "
            if not env_mgr.get_need_package(platform):
                msg += "不需要打包\n"
                continue
            report_detail = env_mgr.get_report(platform)
            export_success_ids = report_detail.get("export_success_ids", [])
            export_error_ids = report_detail.get("export_error_ids", [])
            sprite_success_ids = report_detail.get("sprite_success_ids", [])
            sprite_error_ids = report_detail.get("sprite_error_ids", [])
            if len(export_success_ids) == 0:
                msg += "打包失败\n"
            elif len(export_error_ids) != 0 and len(export_success_ids) != 0:
                msg += "打包部分成功\n"
            # NOTE: 当流水线失败时，认为本次是失败的，避免误解
            # 但是因为用户可能会通过ID进行搜索，所以仍然返回打包步骤的信息
            elif pipeline_status == PipelineStatus.FAILURE.value:
                msg += "打包失败\n"
            else:
                msg += "打包成功\n"
            msg += f"> **打包机ip**: {env_mgr.get_ip(platform)}\n"
            change_list = env_mgr.get_changelist(platform)
            if change_list != "head":
                msg += f"> **原始资源 changelist**: {change_list}\n"
            submit_change_list = env_mgr.get_submit_changelist(platform)
            if submit_change_list:
                msg += f"> **AB 资源 changelist**: {submit_change_list}\n"
            else:
                error_msg = env.get("PYFRAME_EXCEPTION_MESSAGE")
                if error_msg:
                    msg += ""
                else:
                    msg += f"> **AB 资源**: 与P4服务器文件一致\n"
            # 单个key的长度限制 150 字符, 经验值
            default_key_content_length = 150

            if export_success_ids:
                msg += f"> **导出名片成功ID**: {','.join(export_success_ids)[:default_key_content_length]}\n"
            if export_error_ids:
                msg += f"> **导出名片失败ID**: {','.join(export_error_ids)[:default_key_content_length]}\n"
                msg += f"> **导出名片失败原因**: {report_detail.get('export_error_infos', '').strip()}\n"
            if sprite_success_ids:
                sprite_success_ids = set([_id.split("/")[-1] for _id in sprite_success_ids])
                # 限制一下成功id的长度，消息过长，导致当前平台的消息展示不全
                msg += f"> **图集成功ID**: {','.join(sprite_success_ids)[:default_key_content_length]}\n"
            if sprite_error_ids:
                msg += f"> **图集失败ID**: {','.join(sprite_error_ids)[:default_key_content_length]}\n"
                msg += f"> **图集失败原因**: {report_detail.get('sprite_error_infos',[])[0].strip()}\n"
            if report_detail["success_ids"]:
                # 处理返回，仅展示最后一级内容
                success_ids = set([_id.split("/")[-1] for _id in report_detail["success_ids"]])
                # 限制一下成功id的长度，消息过长，导致当前平台的消息展示不全
                msg += f"> **贴图成功ID**: {','.join(success_ids)[:default_key_content_length]}\n"
            if report_detail["error_ids"]:
                msg += f"> **贴图贴图失败ID**: {','.join(report_detail['error_ids'])[:default_key_content_length]}\n"
                error_infos = report_detail["error_infos"]
                if error_infos:
                    msg += f"> **贴图失败原因**: {error_infos[0].strip()}\n"
            dist_destinations = env_mgr.get_dist_destinations()
            log.info("dist_destinations: %s", dist_destinations)
            if dist_destinations:
                for path, ids in dist_destinations.items():
                    log.info(f"path, ids: {path}, {ids}")
                    msg += f"> **{path}**: {','.join(ids)[:default_key_content_length]}\n"
            report_url = env_mgr.get_package_report(platform)
            if report_url:
                msg += f"> **打包报告**: [打包报告]({report_url})\n"
            log_url = env_mgr.get_log(platform)
            if log_url:
                msg += f"> **中间产物**: [中间产物压缩包]({log_url})\n"
            # NOTE: 限制单平台的消息长度为 1000 字符，避免前面平台展示内容过长，影响后面平台展示
            if len(msg) > 1000:
                msg = msg[:1000] + "..."
            all_msg.append(msg)
        msg = "\n".join(all_msg)
        return msg.replace("\\", "/")
