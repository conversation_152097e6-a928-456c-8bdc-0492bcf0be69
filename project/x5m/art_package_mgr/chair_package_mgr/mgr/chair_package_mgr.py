import re
from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, ResourceTypeEnum, config, env_mgr


class ChairPackageMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(kind=ResourceTypeEnum.Chair, **kwargs)
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/chair/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: list) -> set:
        depots = set()
        path_list = []
        for file in source_paths:
            file: str
            if not file.startswith(self._resource_dir_prefix):
                continue
            takeout_prefix_path = file.replace(self._resource_dir_prefix, "")
            res = takeout_prefix_path.split("/")
            if len(res) > 4:
                continue
            id = re.findall(r"[0-9]{10}", file)
            if not id:
                continue
            file_root_path = f"{file.split(id[0])[0]}{id[0]}"
            if file_root_path in path_list:
                continue
            files_prefab = self.p4.files(f"{file_root_path}/*prefab")
            files_fbx = self.p4.files(f"{file_root_path}/*fbx")
            for search_result in files_prefab + files_fbx:
                depots.add(search_result)
            path_list.append(file_root_path)
        return depots

    def _fill_source_p4_views(self):
        views = []
        for path in env_mgr.get_source_paths():
            base_path, _ = path.rsplit("/", 1)
            new_path = base_path + "/..."
            views.append(new_path)
        self.p4_mgr.add_p4_views(views)

    def _package(self):
        """
        打包
        """
        self._make_resouces_file()
        source_paths = env_mgr.get_source_paths()
        paths = ",".join([path.replace(config.p4_views.get("art_resource_root"), "assets/resources/art").strip("/") for path in source_paths])
        cmd_line = self._get_package_cmd()
        cmd_line += f"path={paths} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _get_submit_p4_view_ab(self):
        files = set()
        with open(self._resources_path, "r", encoding="UTF-8") as f:
            file_paths = f.read()
        resources = file_paths.split(",")
        for submit_file in resources:
            submit_file: str
            if submit_file.lower().find("assets/resources/art/chair") == -1:
                continue

            submit_file = submit_file.lower().replace("assets/resources/art", self._ab_view + "art")
            file, _ = os.path.splitext(submit_file)
            files.add(file)
        return list(files)
