from frame import env, log, PyframeException

from project.x5m.art_package_mgr.base_mgr.enum_mgr import ResourceTypeEnum
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr



class ChnagelistInitializationMgr(PackageMgr):
    def __init__(self, **kwargs):
        super().__init__(kind=ResourceTypeEnum.CHANGELIST_INITIALIZATION, **kwargs)
        
    def prepare(self):
        """准备工作，主要是校验 changelist"""
        changelist = env.get("CHANGELIST")
        # 获取传递的平台及资源类型名称
        platform = env.get("PLATFORM")
        resource_name = env.get("RESOURCE_NAME")
        # 校验存在
        if not (changelist and platform and resource_name):
            raise PyframeException("changelist or platform or resource_name is null")
        
        # 校验 changelist 为数字
        if not str(changelist).isdigit():
            raise PyframeException("changelist is not a number")
        if platform not in ["ios", "android"]:
            raise PyframeException("platform is not ios or android")

        log.info("resource_name: %s, platform: %s, current changelist is %s", resource_name, platform, changelist)

    def check_package(self):
        """检查打包， 用于设置初始changelist"""
        changelist = env.get("CHANGELIST")
        platform = env.get("PLATFORM")
        resource_name = env.get("RESOURCE_NAME")
        # 为 art_package
        function_name = env.pipeline.function_name()

        env.set_global({f"{function_name}-{resource_name}-{platform}": str(changelist)})
