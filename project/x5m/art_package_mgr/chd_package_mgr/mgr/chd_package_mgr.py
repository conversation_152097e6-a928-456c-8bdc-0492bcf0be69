from typing import List

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, ResourceTypeEnum, config, env_mgr, path_mgr


class ChdPackageMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.CHD, **kwargs)
        self._execute_method = "H3DBuildTools.BuildArt"
        self._resource_dir_prefix = f"{config.p4_views.get('art_resources_src')}/chd"
        self._resources_ab_path = f"{self._workspace}/arttrunk/release/mr/Resources/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/"
        self._const_source_p4_views = [
            f"{config.p4_views.get('art_resources_src')}/chd/camera/...",
            f"{config.p4_views.get('art_resources_src')}/chd/actions/...",
            f"{config.p4_views.get('art_resources_src')}/chd/eff/...",
            f"{config.p4_views.get('art_resources_src')}/chd/plainBody/...",
        ]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/chd/...",
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/art/chd/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for file in source_paths:
            if file.startswith(f"{self._resource_dir_prefix}/models"):
                if file.lower().endswith(".fbx") or file.lower().endswith(".prefab"):
                    depots.add(file)
            if file.startswith(f"{self._resource_dir_prefix}/actions"):
                if file.lower().endswith(".fbx"):
                    depots.add(file)
        log.info(f"filter source paths: {depots}")
        return depots

    def _fill_source_p4_views(self):
        views = []
        for path in env_mgr.get_source_paths():
            base_path, _ = path.rsplit("/", 1)
            new_path = base_path + "/..."
            views.append(new_path)
        self.p4_mgr.add_p4_views(views)

    def _make_resouces_file(self):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                asset_path = path.replace(config.p4_views.get("art_resources_src"), "assets/resources/art")
                lines.append(asset_path)
            f.write(",".join(lines))

    def _package(self):
        """
        打包
        """
        self._make_resouces_file()
        source_paths = env_mgr.get_source_paths()
        path = ",".join([path.replace(config.p4_views.get("art_resources_src"), "assets/resources/art") for path in source_paths])
        cmd_line = self._get_package_cmd()
        cmd_line += f"path={path} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def __get_model_list(self):
        model_list = []
        for file in env_mgr.get_source_paths():
            if "530000001" in file or "530100001" in file:
                file = file.replace("assets/resources/art/chd/models/", "")
                f_list = file.split("/")
                model_list.append(f_list[0])
        return model_list

    def _copy_product(self):
        model_list = self.__get_model_list()
        if not model_list:
            return
        ab_root = f"{self.ab_path}/art/chd"
        resources_ab_path = f"{self._resources_ab_path}/art/chd"
        for dir_path, _, filenames in os.walk(ab_root):
            for filename in filenames:
                file_prefix = filename.replace(".h3dmanifest", "") if filename.endswith(".h3dmanifest") else filename
                if file_prefix in model_list:
                    src_path = f"{dir_path}/{filename}"
                    dst_path = f"{resources_ab_path}/{filename}"
                    path_mgr.xcopy(src_path, dst_path, dst_is_file=True, overwrite=True)

    def _get_submit_p4_view_ab(self):
        files = set()
        with open(self._resources_path, "r", encoding="UTF-8") as f:
            file_paths = f.read()
        resources = file_paths.split(",")
        for submit_file in resources:
            submit_file: str
            if submit_file.lower().find("assets/resources/art/chd/models") == -1:
                continue
            if "530000001" in submit_file or "530100001" in submit_file:
                submit_file = submit_file.lower().replace("assets/resources/art", self._resources_ab_path + "art")
            else:
                submit_file = submit_file.lower().replace("assets/resources/art", self._ab_view + "art")
            path, file_name = os.path.split(submit_file)
            file_name_without_suffix, _ = os.path.splitext(file_name)
            if path.find(f"{file_name_without_suffix}/effect") == -1 and path.find(f"{file_name_without_suffix}/prefab") == -1:
                submit_file = f"{submit_file[:submit_file.rfind('/')]}/prefab/{file_name_without_suffix}"
            file, _ = os.path.splitext(submit_file)
            files.add(file)
        return list(files)
