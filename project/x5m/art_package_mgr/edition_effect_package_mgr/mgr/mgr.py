import re
import time
from typing import Dict, List

from fnmatch import fnmatchcase as fnmatch

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, env_mgr, config, DisTributeEnum, global_env_mgr, PlatformEnum
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr

from . import EXECUTE_METHOD, utils


class EditionEffectPackageMgr(DistributePackageMgr):
    def __init__(self, **kwargs):
        super().__init__(ResourceTypeEnum.EDITION_EFFECT, **kwargs)
        self._execute_method = EXECUTE_METHOD
        self._branch_name = env_mgr.get_branch_name()
        # TODO: 暂时以这种硬编码的方式测试分支
        if self._branch_name in ["master", "yzw_edition_effect_perf"]:
            self._resource_branch = "Resources"
        else:
            self._resource_branch = f"b/{self._branch_name}"
        self._resource_dir_prefix = f"{config.p4_views['branch_resource_src'].format(branch=self._resource_branch)}/effect"
        # p4 本地路径的前缀
        self._p4_local_path_prefix = (
            f"//{self._kwargs['client']}/arttrunk/{config.arttrunk_branch}/mobile_dancer/arttrunk/client/Assets/resources/Art"
        )
        # 不可变，用于后续的对比
        self.__resource_dir_prefix = f"{config.p4_views['branch_resource_src'].format(branch=self._resource_branch)}/effect"
        self._const_source_p4_views = utils.render_source_views(
            p4_client=self._kwargs["client"], branch=self._branch_name
        )
        self._const_ab_p4_views = utils.render_ab_views(platform=self._platform, branch=self._branch_name, resource_branch=self._resource_branch)
        # 避免获取语言失败的场景，直接通过分支判断
        if self._branch_name.endswith("_tc"):
            self._mr = "mr_trad"
        else:
            self._mr = "mr"
        self._c_prefix = f"{config.p4_views['branch_resource_src'].format(branch=self._resource_branch)}/c"
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s.*?art/effect/([\w\/]+$)")
        self._fail_pattern = [re.compile(r"\[Error].*?/effect/(.*)"), re.compile(r"\[Error].*?/c/(.*)")]

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        """过滤满足条件的路径"""
        depots = set()
        for fp in source_paths:
            if not fp.startswith(self._c_prefix):
                continue
            if re.search(r"{}/.*?/art/effect/".format(self._c_prefix), fp) or re.search(r"{}/.*?/art/camera/".format(self._c_prefix), fp):
                if fp.endswith(".FBX") or fp.endswith(".prefab"):
                    depots.add(fp)
        for fp in source_paths:
            if not fp.startswith(self.__resource_dir_prefix):
                continue
            if fp.endswith(".FBX") or fp.endswith(".prefab"):
                depots.add(fp)

        return depots

    def check_package(self, stop_current_build: bool = True, force_by_id: bool = True) -> bool:
        """检测是否需要打包"""
        force_id = env_mgr.get_force_ids()
        if force_by_id and force_id:
            self._check_package_by_ids(force_id)
            log.info("%s 平台强制打包i的列表: %s", self._platform, ", ".join(force_id))
            return True
        # 分别按照路径获取对应的changelist
        _c_changelist = self._get_changelist(path=self._c_prefix)
        self._resource_dir_prefix = self._c_prefix
        _c_need_package = super()._check_package_by_changelist(changelist=_c_changelist)
        # 获取到changelist，然后按照特定的key进行存储
        _env_resource_type = self._get_env_resource_type(self._c_prefix)
        if _c_need_package or env_mgr.get_changelist_by_key(_env_resource_type) == "head":
            _set_changelist = env_mgr.get_changelist()
            if _set_changelist == "head":
                _set_changelist = _c_changelist
            env_mgr.set_changelist_by_key(changelist=str(_set_changelist), key=_env_resource_type)
        # 按照非c资源路径判断是否需要打包
        self._resource_dir_prefix = self.__resource_dir_prefix
        _changelist = self._get_changelist(path=self.__resource_dir_prefix)
        _need_package = super()._check_package_by_changelist(changelist=_changelist)
        _env_resource_type = self._get_env_resource_type(self.__resource_dir_prefix)
        if _need_package or env_mgr.get_changelist_by_key(_env_resource_type) == "head":
            _set_changelist = env_mgr.get_changelist()
            if _set_changelist == "head":
                _set_changelist = _changelist
            env_mgr.set_changelist_by_key(changelist=str(_set_changelist), key=_env_resource_type)
        # 如果有资源需要更新
        if _need_package or _c_need_package:
            # 按照平台设置需要打包标识
            env_mgr.set_need_package(True)
            # 如果需要打包，添加通知操作，以便于用户知道流水线已经在执行
            self._send_pipelie_running_msg()
            log.info("%s 平台 %s 资源有更新，需要打包", self._platform, self._kind)
            return True
        # 如果不需要更新，则直接停止
        log.info("%s 平台 %s 资源无更新，无需打包", self._platform, self._kind)
        if stop_current_build:
            self.__unpakaged_save_changelist()
            self.__stop_current_build()
        return False

    def __unpakaged_save_changelist(self):
        """保存未打包的changelist"""
        # 存储c资源的changelist
        self._set_changelist(self._c_prefix)
        # 存储整体资源的changelist
        self._set_changelist(self.__resource_dir_prefix)

    def __stop_current_build(self):
        env_mgr.set_need_package(False)
        for platform in PlatformEnum:
            if platform == PlatformEnum.OPENHARMONY and not self._debug:
                continue
            index = 1
            while env_mgr.get_need_package(platform) is None:
                if index > 60:
                    break
                time.sleep(1)
                index += 1
            if env_mgr.get_need_package(platform):
                return
        pipeline_mgr.stop_current_build()

    def _set_changelist(self, path: str):
        _env_type = self._get_env_resource_type(path)
        s_changelist = env_mgr.get_changelist_by_key(key=_env_type)
        if s_changelist == "head":
            last_changelist = global_env_mgr.get_last_changelist(resource=_env_type)
            if not last_changelist:
                return
            last_changelist = int(last_changelist)
            s_changelist = last_changelist + config.max_changelist_size
        remote_laster_changelist = self.p4.get_latest_changes(f"{path}/...").get("change", "0")
        if int(s_changelist) >= int(remote_laster_changelist):
            return
        log.info("路径: %s, 本地最新changelist: %s, 远程最新changelist: %s", path, s_changelist, remote_laster_changelist)
        global_env_mgr.set_last_changelist(resource=_env_type, changelist=str(s_changelist))

    def _get_env_resource_type(self, path: str) -> str:
        _resource = f"{self._kind}-effect"
        if path == self._c_prefix:
            _resource = f"{self._kind}-c"
        return _resource

    def _get_changelist(self, path: str) -> int:
        # 获取参数中的值
        first_changelist = env_mgr.get_first_changelist()
        if first_changelist != "-1":
            return int(first_changelist) + 1
        # 获取changelist的前缀
        _resource = self._get_env_resource_type(path)
        # 如果没有传递，则获取db中上次的存储
        last_changelist = global_env_mgr.get_last_changelist(resource=_resource)
        if not last_changelist:
            # DB 中没有上次的记录，说明是初始化，则从P4直接获取最新的一条记录
            last_changelist = self._get_lastest_changelist()
            if last_changelist == "-1":
                raise PyframeException("first_changelist为空或默认值-1,且数据库中上次打包版本号也为空,无法打包")
            # 设置 db 记录
            global_env_mgr.set_last_changelist(resource=_resource, changelist=last_changelist)
        return int(last_changelist) + 1

    def _update_p4(self) -> None:
        _resource = self._get_env_resource_type(self._c_prefix)
        max_changelist = env_mgr.get_changelist_by_key(key=_resource)
        _resource = self._get_env_resource_type(self._resource_dir_prefix)
        _changelist = env_mgr.get_changelist_by_key(key=_resource)
        # 拉取最大的，避免另一个路径资源有问题
        if int(max_changelist) < int(_changelist):
            max_changelist = _changelist
        self._fill_const_source_p4_views()
        self.p4_mgr.sync_all(changelist=str(max_changelist), force=True)
        self._fill_const_ab_p4_views()
        self.p4_mgr.sync_all(force=True)

    def _fill_const_source_p4_views(self):
        _p4_views = self._const_source_p4_views
        # 匹配到具体的资源
        paths = env_mgr.get_source_paths()
        _paths, _c_paths = [], []
        for path in paths:
            if path.startswith(self._c_prefix):
                _c_paths.append(path)
                continue
            if path.startswith(self.__resource_dir_prefix):
                _paths.append(path)
        _p4_views.extend(utils.get_resource(paths, self._p4_local_path_prefix, self._resource_branch))
        _p4_views.extend(utils.get_c_resource(_c_paths, self._p4_local_path_prefix, self._resource_branch))
        self.p4_mgr.add_p4_views(_p4_views, format_view=False)

    def _fill_const_ab_p4_views(self):
        self.p4_mgr.set_p4_views(self._const_ab_p4_views)

    @property
    def ab_path(self) -> str:
        self._ab_path = f"{self._kwargs['root']}/arttrunk/{config.arttrunk_branch}/{self._mr}/{self._resource_branch}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles"
        return self._ab_path

    def _make_resouces_file(self):
        # 创建日志报告目录
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                index = path.find("art_src")
                if index == -1:
                    continue
                house_index = path.find("art_src/house")
                # 匹配要打包的文件
                if house_index == -1:
                    _path = "Assets/resources/Art" + path[index + 7 :]
                else:
                    _path = "Assets/staticresources/Art/3d" + path[index + 7 :]
                lines.append(_path)
            log.info("need to package files: %s", lines)
            f.write(",".join(lines))

    def _parse_id_to_filename(self, id: str) -> str:
        return id.split("/")[-1]

    def _get_hotfix_map(self) -> Dict:
        for info in self._distribute_config:
            if info.distribute_type == DisTributeEnum.HOTFIX:
                return info.distribute_rule
        return {}

    def _get_submit_p4_view_ab(self):
        files = set()
        with open(self._resources_path, "r", encoding="UTF-8") as f:
            file_paths = f.read()
        _prefix = f"{config.p4_views.get('branch_cdn_resources').format(branch=self._resource_branch)}/{self._platform}/assetbundles"
        # 获取 hotfix 内容
        hotfix_map = self._get_hotfix_map()
        # 半角逗号分隔
        need_match_hotfix = False
        if self._branch_name in ["master", "yzw_edition_effect_perf"]:
            need_match_hotfix = True
        resources = file_paths.split(",")
        for submit_file in resources:
            file, _ = os.path.splitext(submit_file)
            # 用以目录复制
            _file = file
            if file.find("Assets/resources/Art/effect/") == -1 and file.find("Assets/resources/Art/c/") == -1:
                continue
            if file.find("Assets/resources/Art/effect/") != -1:
                file = file.replace("Assets/resources/Art", f"{_prefix}/art")
            else:
                # copy 文件到指定目录
                # assetbundles/art/c => assetbundles/c
                file = file.replace("Assets/resources/Art", _prefix)
                _file = _file.replace("Assets/resources/Art", f"{_prefix}/art")
                src_path = self.p4_mgr.parse_p4view_to_localpath(_file)
                dst_path = self.p4_mgr.parse_p4view_to_localpath(file)
                log.info("copy %s to %s", src_path, dst_path)
                try:
                    if src_path.rstrip("/") != dst_path.rstrip("/"):
                        path_mgr.copy(src_path, dst_path)
                except Exception as e:
                    log.error("copy file error: %s", e)
                    continue
            files.add(file)
            if not need_match_hotfix:
                continue
            src_path = self.p4_mgr.parse_p4view_to_localpath(file)
            # 针对 master 需要匹配 hotfix 的内容
            for key, val in hotfix_map.items():
                if fnmatch(file.split("/")[-1], key):
                    if not os.path.exists(file):
                        if not file[-2:] == "01":
                            file = file[0 : len(file) - 2] + "01"
                    if file.find("assetbundles/art/effect") != -1:
                        file = f"{val}/client{file.split(config.p4_views.get('branch_cdn_resources').format(branch=self._resource_branch))[-1]}"
                    dst_path = self.p4_mgr.parse_p4view_to_localpath(file)
                    # 拷贝文件
                    if src_path.rstrip("/") != dst_path.rstrip("/"):
                        path_mgr.copy(src_path, dst_path)
                    files.add(file)
        self.p4_mgr.add_p4_views(list(files))
        # 添加对应的view
        log.info("submit p4 view ab: %s", files)
        return list(files)

    def _save_changelist(self):
        """
        记录最后一次打包的changelist
        """
        # 获取失败的资源
        report_detail = env_mgr.get_report()
        # 按照现有流程，只要流水线没有异常，都记录id继续往下走
        # 记录最后一次打包的changelist
        _c_resource = self._get_env_resource_type(self._c_prefix)
        _c_changelist = env_mgr.get_changelist_by_key(key=_c_resource)
        _resource = self._get_env_resource_type(self._resource_dir_prefix)
        _changelist = env_mgr.get_changelist_by_key(key=_resource)
        global_env_mgr.set_last_changelist(resource=_c_resource, changelist=str(_c_changelist))
        global_env_mgr.set_last_changelist(resource=_resource, changelist=str(_changelist))
        if report_detail["succ_count"] == 0:
            raise PyframeException(f"本次打包失败，请修改后重新提交资源")
        if report_detail["failed_count"] != 0 or report_detail["error_count"] != 0:
            raise PyframeException(f"存在打包失败的资源，请修改后重新提交资源")
