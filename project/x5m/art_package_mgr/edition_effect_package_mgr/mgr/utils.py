import os
from typing import List

from project.x5m.art_package_mgr.base_mgr.package_mgr import config

# 进行替换的标识
# 预设标识
PREFAB_FLAG = "/prefab/"
# 资源标识
RESOURCE_FLAG = "/ziyuan/"


def render_source_views(p4_client: str, branch: str) -> List:
    # 主支
    if branch in ["master", "yzw_edition_effect_perf"]:
        return [
            f"{config.p4_views['resmg_root']}/resource_effect/... //{p4_client}/x5mplan/resmg/resource_effect/...",
        ]
    return []


def render_ab_views(platform: str, branch: str, resource_branch: str) -> List:
    base_path_prefix = config.p4_views["branch_cdn_resources"].format(branch=resource_branch)
    if branch in ["master", "yzw_edition_effect_perf"]:
        return [
            f"{base_path_prefix}/{platform}/assetbundles/c/*/art/effect/...",
            f"{base_path_prefix}/{platform}/assetbundles/c/*/art/camera/...",
            f"{base_path_prefix}/{platform}/assetbundles/art/effect/...",
            f"{base_path_prefix}/{platform}/assetbundles/art/camera/...",
            f"{config.p4_views.get('onlineupdate_root')}/*/*/client/{platform}/assetbundles/c/...",
        ]
    elif branch.endswith("_plaza"):
        return [
            f"{base_path_prefix}/{platform}/assetbundles/c/*/art/effect/...",
            f"{base_path_prefix}/{platform}/assetbundles/...",
        ]
    elif not branch.endswith("_tc"):
        return [
            f"{base_path_prefix}/{platform}/assetbundles/c/*/art/effect/...",
            f"{base_path_prefix}/{platform}/assetbundles/...",
            f"{base_path_prefix}/{platform}/assetbundles/art/camera/...",
        ]
    else:
        return [
            f"{base_path_prefix}/{platform}/assetbundles/...",
            f"{base_path_prefix}/{platform}/assetbundles/c/*/art/effect/...",
        ]


class PrefabToResouce:
    @staticmethod
    def get_resource_for_bssc(path: str, p4_local_path_prefix: str, resource_branch: str) -> List:
        """获取变身手持类型的资源

        prefab: //x5_mobile/mr/Resources/art_src/effect/ingame_effect/prefab/bssc/eff_10103031401.prefab
        ziyuan: //x5_mobile/mr/Resources/art_src/effect/ingame_effect/ziyuan/bssc/eff_10103031401/
        """
        _path = os.path.splitext(path)[0]
        p4_path = f"{_path.replace(PREFAB_FLAG, RESOURCE_FLAG)}/..."
        # 拆分路径，组装本地路径
        p4_local_path = PrefabToResouce._convert_p4_to_local_path(p4_path, p4_local_path_prefix, resource_branch)
        return [f"{p4_path} {p4_local_path}"]

    @staticmethod
    def get_resource_for_badge(path: str, p4_local_path_prefix: str, resource_branch: str) -> List:
        """获取 badge 类型的资源, 按照

        # 按照名称或资源文件夹名一致，背景图标识: _ccztpic_eff
        prefab: //x5_mobile/mr/Resources/art_src/effect/ui_effect/prefab/slowtime/badge_effect/1048525_ccztpic_eff.prefab
        ziyuan: //x5_mobile/mr/Resources/art_src/effect/ui_effect/ziyuan/slowtime/badge_effect/1048525_ccztpic_eff/

        # 按照前 8 位保持一致，徽记标识: _icon
        prefab: //x5_mobile/mr/Resources/art_src/effect/ui_effect/prefab/slowtime/badge_effect/946800801_icon.prefab
        prefab: //x5_mobile/mr/Resources/art_src/effect/ui_effect/prefab/slowtime/badge_effect/946800802_icon.prefab
        prefab: //x5_mobile/mr/Resources/art_src/effect/ui_effect/prefab/slowtime/badge_effect/946800803_icon.prefab
        ziyuan: //x5_mobile/mr/Resources/art_src/effect/ui_effect/ziyuan/slowtime/badge_effect/94680080/
        """
        _path = os.path.splitext(path)[0]
        if "_ccztpic_eff" in _path:
            # 背景图标识
            p4_path = f"{_path.replace(PREFAB_FLAG, RESOURCE_FLAG)}/..."
        else:
            # 徽记标识
            _prefix_path, basename = _path.rsplit("/", 1)
            p4_path = f"{_prefix_path.replace(PREFAB_FLAG, RESOURCE_FLAG)}/{basename[:8]}/..."

        p4_local_path = PrefabToResouce._convert_p4_to_local_path(p4_path, p4_local_path_prefix, resource_branch)
        return [f"{p4_path} {p4_local_path}"]

    @staticmethod
    def get_resource_for_nick_baseplate(path: str, p4_local_path_prefix: str, resource_branch: str) -> List:
        """获取社交底板对应的资源路径

        prefab: //x5_mobile/mr/Resources/art_src/effect/ingame_effect/prefab/nick_baseplate/945700007_zy1.prefab
        prefab: //x5_mobile/mr/Resources/art_src/effect/ingame_effect/prefab/nick_baseplate/945700007_zy2.prefab
        ziyuan: //x5_mobile/mr/Resources/art_src/effect/ingame_effect/ziyuan/nick_baseplate/945700007_zy/
        """
        _path = os.path.splitext(path)[0]
        _path = _path.replace(PREFAB_FLAG, RESOURCE_FLAG)
        p4_path = f"{_path[:-1]}/..."
        # 转换为本地路径
        p4_local_path = PrefabToResouce._convert_p4_to_local_path(p4_path, p4_local_path_prefix, resource_branch)
        return [f"{p4_path} {p4_local_path}"]

    @staticmethod
    def get_resource_for_login(path: str, p4_local_path_prefix: str, resource_branch: str) -> List:
        """针对登录，按照 prefab 名称和资源文件夹名一致

        prefab: //x5_mobile/mr/Resources/art_src/effect/ui_effect/prefab/denglu/eff_ui_dl_tanjita1.prefab
        prefab: //x5_mobile/mr/Resources/art_src/effect/ui_effect/prefab/denglu/eff_ui_dl_tanjita2.prefab
        ziyuan: //x5_mobile/mr/Resources/art_src/effect/ui_effect/ziyuan/denglu/eff_ui_dl_tanjita/
        """
        _path = os.path.splitext(path)[0][:-1]
        # 替换 `/prefab/` 为 `/ziyuan/`
        p4_path = f"{_path.replace(PREFAB_FLAG, RESOURCE_FLAG)}/..."
        # 转换为本地路径
        p4_local_path = PrefabToResouce._convert_p4_to_local_path(p4_path, p4_local_path_prefix, resource_branch)
        return [f"{p4_path} {p4_local_path}"]

    @staticmethod
    def _convert_p4_to_local_path(p4_path: str, p4_local_path_prefix: str, resource_branch: str):
        """转换 p4 路径到本地路径

        1. 分拆路径
        2. 拼装后缀
        """
        _suffix_path = p4_path.split(config.p4_views["branch_resource_src"].format(branch=resource_branch))[-1]
        return f"{p4_local_path_prefix.rstrip('/')}/{_suffix_path.lstrip('/')}"

    @staticmethod
    def get_resource_for_huodong(path: str, p4_local_path_prefix: str, resource_branch: str) -> List:
        """获取活动类型的资源

        非固定类型，都按照活动类型匹配
        prefab: //x5_mobile/mr/Resources/art_src/effect/ui_effect/prefab/{huodong|mv|vlog|plaza}/
        ziyuan: //x5_mobile/mr/Resources/art_src/effect/ui_effect/ziyuan/{huodong|mv|vlog|plaza}/
        """
        # 按照prefab进行分割
        prefix_path, suffix_path = path.split(PREFAB_FLAG, 1)
        # 组装资源路径
        p4_path = f"{prefix_path}{RESOURCE_FLAG}{suffix_path.split('/', 1)[0]}/..."
        p4_local_path = PrefabToResouce._convert_p4_to_local_path(p4_path, p4_local_path_prefix, resource_branch)
        return [f"{p4_path} {p4_local_path}"]


def get_resource(paths: List[str], p4_local_path_prefix: str, resource_branch: str) -> List:
    prefab_to_resource_methods = {
        "/bssc/": PrefabToResouce.get_resource_for_bssc,
        "/badge_effect/": PrefabToResouce.get_resource_for_badge,
        "/nick_baseplate/": PrefabToResouce.get_resource_for_nick_baseplate,
        "/denglu/": PrefabToResouce.get_resource_for_login,
    }
    p4_paths = []

    # 按照路径中的标识
    for path in paths:
        _path = path.split(config.p4_views["branch_resource_src"].format(branch=resource_branch))[-1]
        local_path = f"{p4_local_path_prefix.rstrip('/')}/{_path.lstrip('/')}"
        p4_paths.append(f"{path} {local_path}")
        if PREFAB_FLAG not in path:
            continue
        # 标识是否可以查询到
        found = False
        for ptn, method in prefab_to_resource_methods.items():
            if ptn not in path:
                continue
            # 如果匹配上，则获取对应路径，然后跳过剩余的匹配
            p4_paths.extend(method(path, p4_local_path_prefix, resource_branch))
            found = True
            break
        # NOTE: 如果没有匹配上上面的路径，则按照活动类型进行匹配
        if not found:
            p4_paths.extend(PrefabToResouce.get_resource_for_huodong(path, p4_local_path_prefix, resource_branch))

    return list(set(p4_paths))


def get_c_resource(paths: List[str], p4_local_path_prefix: str, resource_branch: str) -> List:
    """获取 C 资源路径"""
    p4_paths = []
    # 组装路径
    for path in paths:
        _path = path.split(config.p4_views["branch_resource_src"].format(branch=resource_branch))[-1]
        local_path = f"{p4_local_path_prefix.rstrip('/')}/{_path.lstrip('/')}"
        p4_paths.append(f"{path} {local_path}")
        if PREFAB_FLAG not in path:
            continue
        # 处理 camera 类型
        camera_flag, effect_flag = "/camera/", "/effect/"
        if camera_flag in path:
            flag = camera_flag
        else:
            flag = effect_flag
        _c_path = path.split(flag, 1)[0]
        _c_local_path = local_path.split(flag, 1)[0]
        p4_paths.append(f"{_c_path}{flag}... {_c_local_path}{flag}...")

    return list(set(p4_paths))
