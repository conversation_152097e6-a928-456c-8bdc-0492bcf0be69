import re
from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, ResourceTypeEnum, config, env_mgr

from project.x5m.art_package_mgr.effect_package_mgr.mgr.utils import get_resource


class EffectPackageMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.Effect, **kwargs)
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s.*?(\d+|\d+_extend\d)")
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/effect/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _filter_source_paths(self, source_paths: list) -> set:
        depots = set()
        for file in source_paths:
            file: str
            if not file.startswith(self._resource_dir_prefix):
                continue
            if file.lower().endswith(".prefab"):
                depots.add(file)
        return depots

    def _fill_source_p4_views(self):
        # 按照匹配规则获取需要拉取的资源路径
        _views = get_resource(env_mgr.get_source_paths())
        log.info("matach p4 views: %s", _views)
        self.p4_mgr.add_p4_views(_views)

    def _package(self):
        """
        打包
        """
        self._make_resouces_file()
        source_paths = env_mgr.get_source_paths()
        paths = ",".join([path.replace(config.p4_views.get("art_resource_root"), "assets/resources/art").strip("/") for path in source_paths])
        cmd_line = self._get_package_cmd()
        cmd_line += f"path={paths} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _get_submit_p4_view_ab(self):
        files = set()
        with open(self._resources_path, "r", encoding="UTF-8") as f:
            file_paths = f.read()
        resources = file_paths.split(",")
        for submit_file in resources:
            submit_file: str
            if submit_file.lower().find("assets/resources/art/") == -1:
                continue

            submit_file = submit_file.lower().replace("assets/resources/art", self._ab_view + "art")
            file, _ = os.path.splitext(submit_file)
            files.add(file)
        return list(files)
