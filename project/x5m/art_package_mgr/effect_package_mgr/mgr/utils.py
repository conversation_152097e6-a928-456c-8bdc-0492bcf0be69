import os
from typing import List

from frame import *

# 进行替换的标识
# 预设标识
PREFAB_FLAG = "/prefab/"
# 资源标识
RESOURCE_FLAG = "/ziyuan/"


class PrefabToResouce:
    @staticmethod
    def get_resource_for_title(path: str) -> List:
        """针对 title，按照 prefab 名称和资源文件夹名一致

        prefab: //x5_mobile/mr/art_release/art_src/effect/ingame_effect/prefab/title/x_eff_s_927102760.prefab
        ziyuan: //x5_mobile/mr/art_release/art_src/effect/ingame_effect/ziyuan/title/x_eff_s_927102760/
        """
        return PrefabToResouce._get_resource_by_same_pattern(path)

    @staticmethod
    def get_resource_for_number(path: str) -> List:
        """针对 number，按照 prefab名称和资源文件夹名一致

        prefab: //x5_mobile/mr/art_release/art_src/effect/ui_effect/prefab/number/1433874701_numeff.prefab
        ziyuan: //x5_mobile/mr/art_release/art_src/effect/ui_effect/ziyuan/1433874701_numeff/
        """
        return PrefabToResouce._get_resource_by_same_pattern(path)

    @staticmethod
    def get_resource_for_portrait(path: str) -> List:
        """针对头像，按照prefab和资源文件夹名一致

        prefab: //x5_mobile/mr/art_release/art_src/effect/ui_effect/prefab/portrait/922000483.prefab
        ziyuan: //x5_mobile/mr/art_release/art_src/effect/ui_effect/ziyuan/922000483/
        """
        return PrefabToResouce._get_resource_by_same_pattern(path)

    @staticmethod
    def get_resource_for_login(path: str) -> List:
        """针对登录，按照 prefab 名称和资源文件夹名一致

        prefab: //x5_mobile/mr/art_release/art_src/effect/ui_effect/prefab/denglu/eff_ui_dl_tanjita1.prefab
        prefab: //x5_mobile/mr/art_release/art_src/effect/ui_effect/prefab/denglu/eff_ui_dl_tanjita2.prefab
        ziyuan: //x5_mobile/mr/art_release/art_src/effect/ui_effect/ziyuan/denglu/eff_ui_dl_tanjita/
        """
        _path = os.path.splitext(path)[0][:-1]
        # 替换 `/prefab/` 为 `/ziyuan/`
        path_prefix = _path.replace(PREFAB_FLAG, RESOURCE_FLAG)
        # 需要拉取资源路径
        return [f"{path_prefix}/..."]

    @staticmethod
    def get_resource_for_garden(path: str) -> List:
        """针对花园皮肤，按照全屏的prefab和资源文件夹名称一致

        prefab: //x5_mobile/mr/art_release/art_src/effect/ui_effect/prefab/garden/946200003_qp_eff.prefab
        prefab: //x5_mobile/mr/art_release/art_src/effect/ui_effect/prefab/garden/946100003_bj_eff.prefab
        ziyuan: //x5_mobile/mr/art_release/art_src/effect/ui_effect/ziyuan/garden/946200003_qp_eff/
        """
        # 如果不是全屏，则转换为全屏的后缀
        _path = os.path.splitext(path)[0]
        _path, _basename = _path.rsplit("/", 1)
        if "_bj_" in _basename:
            _basename = _basename.replace("_bj_", "_qp_")
            _basename = f"{_basename[:3]}2{_basename[4:]}"
        # 组装资源的路径
        return [f"{_path.replace(PREFAB_FLAG, RESOURCE_FLAG)}/{_basename}/..."]

    def get_resource_for_mingpian(path: str) -> List:
        """针对名片，按照高位的和资源文件夹名称一致

        NOTE: 非多人名片是纯数字，其他是纯字母

        # 第5位不一样，包含 0 和 4，都替换为 4
        prefab: //x5_mobile/mr/art_release/art_src/effect/ui_effect/prefab/mingpian/926104419_msyg.prefab
        prefab: //x5_mobile/mr/art_release/art_src/effect/ui_effect/prefab/mingpian/926144419_msyg.prefab
        ziyuan: //x5_mobile/mr/art_release/art_src/effect/ui_effect/ziyuan/mingpian/926144419_msyg/

        prefab: //x5_mobile/mr/art_release/art_src/effect/ui_effect/prefab/mingpian/926104419/
        ziyuan: //x5_mobile/mr/art_release/art_src/effect/ui_effect/ziyuan/mingpian/926104419/
        """
        _path = os.path.splitext(path)[0]
        _path, _basename = _path.rsplit("/", 1)
        # 把第5位设置为4
        if not _basename.isdigit() and _basename[4] != "4":
            _basename = f"{_basename[:4]}4{_basename[5:]}"

        return [f"{_path.replace(PREFAB_FLAG, RESOURCE_FLAG)}/{_basename}/..."]

    @staticmethod
    def get_resource_for_huodong(path: str) -> List:
        """针对活动，按照prefab后的路径进行匹配

        prefab: //x5_mobile/mr/art_release/art_src/effect/ui_effect/prefab/{huodong|mv|vlog|plaza}/
        ziyuan: //x5_mobile/mr/art_release/art_src/effect/ui_effect/ziyuan/{huodong|mv|vlog|plaza}/
        """
        # 按照prefab进行分割
        prefix_path, sufix_path = path.split(PREFAB_FLAG)
        # 组装资源路径
        return [f"{prefix_path}{RESOURCE_FLAG}{sufix_path.split('/', 1)[0]}/..."]

    @staticmethod
    def _get_resource_by_same_pattern(path: str) -> List:
        """相同规则的预设获取到对应的资源"""
        _path = os.path.splitext(path)[0]
        # 替换 `/prefab/` 为 `/ziyuan/`
        path_prefix = _path.replace(PREFAB_FLAG, RESOURCE_FLAG)
        # 需要拉取资源路径
        return [f"{path_prefix}/..."]


def get_resource(paths: List[str]) -> List:
    """获取 prefab 对应的资源"""
    prefab_to_resource_methods = {
        "/title/": PrefabToResouce.get_resource_for_title,
        "/number/": PrefabToResouce.get_resource_for_number,
        "/garden/": PrefabToResouce.get_resource_for_garden,
        "/portrait/": PrefabToResouce.get_resource_for_portrait,
        "/mingpian/": PrefabToResouce.get_resource_for_mingpian,
    }
    p4_paths = []

    # 按照路径中的标识
    for path in paths:
        p4_paths.append(path)
        # 过滤掉包含`ziyuan`关键词的路径
        if RESOURCE_FLAG in path:
            continue
        # 标识是否可以查询到
        found = False
        for ptn, method in prefab_to_resource_methods.items():
            if ptn not in path:
                continue
            # 如果匹配上，则获取对应路径，然后跳过剩余的匹配
            p4_paths.extend(method(path))
            found = True
            break
        # NOTE: 如果没有匹配上上面的路径，则按照活动类型进行匹配
        if not found:
            p4_paths.extend(PrefabToResouce.get_resource_for_huodong(path))

    return list(set(p4_paths))
