node {
    // 使用 BuildUser 包装器设置环境域
    wrap([$class: 'BuildUser']) {
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

pipeline {
    // 设置全局选项
    options {
        timestamps()
        disableConcurrentBuilds()
        durabilityHint('PERFORMANCE_OPTIMIZED')
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder(logRotator(numToKeepStr: env.numsToKeepStr, daysToKeepStr: env.daysToKeepStr))
    }
    // 设置允许输入的参数
    parameters {
        text(name: 'first_changelist', defaultValue: '-1', description: '输入开始打包的changelist')
        booleanParam(name: 'dev', defaultValue: true, description: '是否为开发模式')
        booleanParam(name: 'force', defaultValue: false, description: '是否强制拉取P4资源')
    }
    // 流水线配置中设置对应的执行周期，避免因为P4备份时间变更，影响构建
    triggers {
        cron(env.BUILD_PERIOD)
    }
    // 设置流水线对应的环境变量
    environment {
        PIPELINE_PYTHON = 'pipeline-python'
        RESOURCE = 'game_icon_upload_git'
    }
    agent {
        node {
            label "art_package_game_icon_upload_git" // 使用标签选择节点
            customWorkspace "${env.workspace}/.."
        }
    }
    // 定义流水线步骤    
    stages {
        // 安装python脚本环境依赖
        stage('更新流水线依赖') {
            steps {
                dir('pyframe-pipeline') {
                    script {
                        bat(script: "${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q")
                    }
                }
            }
        }
        // 准备工作，清理目录，检查参数，检查依赖工具
        stage('准备') {
            steps {
                dir('pyframe-pipeline') {
                    script {
                        bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=prepare")
                    }
                }
            }
        }
        // 根据changelist，判断是否需要打包
        stage('检查是否需要打包') {
            steps {
                dir('pyframe-pipeline') {
                    script {
                        bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=check_package")
                    }
                }
            }
        }
        // 更新git和p4资源
        stage('获取p4资源') {
            steps {
                dir('pyframe-pipeline') {
                    script {
                        bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=update_git_and_p4")
                    }
                }
            }
        }
        // 进行打包
        stage('调用打包命令') {
            steps {
                dir('pyframe-pipeline') {
                    script {
                        bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=package")
                    }
                }
            }
        }
        // 提交打包后的产物
        stage('提交产物') {
            steps {
                dir('pyframe-pipeline') {
                    script {
                        bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=submit")
                    }
                }
            }
        }
    }
    // 统一的后置处理
    post {
        always {
            dir('pyframe-pipeline') {
                script {
                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_always")
                }
            }
        }
        unstable {
            dir("pyframe-pipeline") {
                script {
                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_unstable")
                }
            }
        }
        success {
            dir("pyframe-pipeline") {
                script {
                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_success")
                }
            }
        }
        failure {
            dir("${env.workspace}/pyframe-pipeline") {
                script {
                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_failure")
                }
            }
        }
    }
}
