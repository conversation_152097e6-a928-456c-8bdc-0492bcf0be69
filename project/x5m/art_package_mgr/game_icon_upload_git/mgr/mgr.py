from typing import Optional, <PERSON><PERSON>

from frame import *

from project.x5m.art_package_mgr.base_mgr.enum_mgr import ResourceTypeEnum
from project.x5m.art_package_mgr.base_mgr.env_mgr import env_mgr, global_env_mgr
from project.x5m.art_package_mgr.base_mgr.msg_mgr import MsgMgr
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr
from project.x5m.art_package_mgr.base_mgr.git_mgr import X5mGitMgr

from . import utils


class GameIconUploadGitMgr(PackageMgr):
    def __init__(self, **kwargs):
        super().__init__(kind=ResourceTypeEnum.GAME_ICON_UPLOAD_GIT, **kwargs)
        self._resource_dir_prefix = "//美术资源/炫舞手游-ui/游戏图标"
        self._android_flag = "透明"
        self._ios_flag = "白底"
        self._android_platform = "android"
        self._ios_platform = "ios"
        self._git_project_name = "x5mobile"

    def _clean_other(self):
        """清理图标的目录，避免出现不正确的图标"""
        android_path = os.path.join(self._workspace, self._android_platform)
        ios_path = os.path.join(self._workspace, self._ios_platform)
        self._safe_clean(android_path)
        self._safe_clean(ios_path)

    def _update_git(self) -> None:
        """更新 git"""
        # 设置要更新的`git`分支
        branch = utils.get_changed_branch(env_mgr.get_source_paths(), self._resource_dir_prefix)
        utils.set_x5mobile_branch(branch)
        # 拉取对应的分支
        _git_mgr = X5mGitMgr(self._workspace, self._git_project_name, branch)
        _git_mgr.update()

    def _fill_source_p4_views(self) -> None:
        """组装 p4 view"""
        p4_views = []
        for file in env_mgr.get_source_paths():
            file_name = file.split("/")[-1]
            if self._android_flag in file:
                view = f"{file} //{self._kwargs['client']}/{self._android_platform}/{file_name}"
            elif self._ios_flag in file:
                view = f"{file} //{self._kwargs['client']}/{self._ios_platform}/{file_name}"
            else:
                continue
            p4_views.append(view)
        log.info("pull p4 views: %s", p4_views)
        self.p4_mgr.add_p4_views(p4_views, format_view=False)

    def package(self):
        """处理对应的文件"""
        if not env_mgr.get_need_package():
            return
        branch = utils.get_x5mobile_branch()
        utils.check_android_icon(self._workspace, self._android_platform)
        utils.check_ios_icon(self._workspace, self._ios_platform)
        utils.copy_icon(self._workspace, self._android_platform, os.path.join(self._workspace, self._git_project_name, branch))
        utils.copy_icon(self._workspace, self._ios_platform, os.path.join(self._workspace, self._git_project_name, branch))

    def submit(self):
        if not env_mgr.get_need_package():
            return
        # 提交到 git
        self._submit_git()
        s_changelist = env_mgr.get_changelist()
        global_env_mgr.set_last_changelist(resource=self._kind, changelist=s_changelist)

    def _submit_git(self):
        """提交 git"""
        branch = utils.get_x5mobile_branch()
        _git_mgr = X5mGitMgr(self._workspace, self._git_project_name, branch)
        _git_mgr.current_git_mgr.add(".")
        staged_files = _git_mgr.current_git_mgr.get_staged_files()
        log.info("staged files: %s", staged_files)
        # 解析提交的路径
        utils.parse_git_staged_files(staged_files)
        if not staged_files:
            log.info("no files to commit")
            return

        message = f"update game icon upload git {branch}"
        _git_mgr.current_git_mgr.commit(message)
        _git_mgr.current_git_mgr.push(branch)

    def always(self):
        if not env_mgr.get_need_package():
            return
        self._clean()

    def get_msg_user(self, pipeline_status: Optional[str] = None) -> Tuple:
        msg = ""
        branch_name = utils.get_x5mobile_branch()
        if branch_name:
            msg += f"**分支**: {branch_name}\n"

        android_msg = ""
        android_unmatch_icon = utils.get_unmatch_icon(self._android_platform)
        if android_unmatch_icon:
            android_msg += f"**不正确图标尺寸**: {','.join(android_unmatch_icon)}\n"

        no_alpha_icon = utils.get_alpha_icon(self._android_platform)
        if no_alpha_icon:
            android_msg += f"**不存在alpha通道**: {','.join(no_alpha_icon)}\n"

        incorrect_icon = utils.get_incorrect_size(self._android_platform)
        if incorrect_icon:
            android_msg += f"**图标名称和真实名称不匹配**: {','.join(incorrect_icon)}\n"

        android_upload_icons = utils.get_upload_file_path(self._android_platform)
        if android_upload_icons:
            android_msg += "**上传git文件**:\n{}\n".format("\n".join(android_upload_icons))

        if android_msg:
            msg += "**android**\n"
            msg += android_msg

        ios_msg = ""
        ios_unmatch_icon = utils.get_unmatch_icon(self._ios_flag)
        if ios_unmatch_icon:
            ios_msg += f"**不正确图标尺寸**: {','.join(ios_unmatch_icon)}\n"

        alpha_icon = utils.get_alpha_icon(self._ios_flag)
        if alpha_icon:
            ios_msg += f"**存在alpha通道**: {','.join(alpha_icon)}\n"

        incorrect_icon = utils.get_incorrect_size(self._ios_flag)
        if incorrect_icon:
            ios_msg += f"**图标名称和真实名称不匹配**: {','.join(incorrect_icon)}\n"

        ios_upload_icons = utils.get_upload_file_path(self._ios_flag)
        if ios_upload_icons:
            ios_msg += "**上传git文件**:\n{}\n".format("\n".join(ios_upload_icons))

        if ios_msg:
            msg += "**ios**\n"
            msg += ios_msg
        # 组装用户
        user = MsgMgr.get_user()
        user.extend(MsgMgr.get_pm_users())

        return msg, utils.process_user_list(user)
