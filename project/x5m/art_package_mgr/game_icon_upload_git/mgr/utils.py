import re
from typing import Dict, List

from PIL import Image

from frame import *


def get_changed_branch(changed_files: List, resource_dir: str) -> str:
    """获取变更的分支"""
    changed_branches = []
    for file in changed_files:
        if not file.startswith(resource_dir):
            continue
        _branch = file.split(resource_dir)[-1].split("/")[1]
        changed_branches.append(_branch)
    log.info("changed_branches: %s", changed_branches)

    # 处理branch，一次仅处理一个分支
    changed_branches = list(set(changed_branches))
    if not changed_branches:
        raise PyframeException("请确认图标上传路径是否包含分支目录")
    if len(changed_branches) > 1:
        raise PyframeException("当前不支持多个分支图标同时操作,请删除后重新单独上传")
    branch = changed_branches[0]
    log.info("current branch: %s", branch)

    return branch


def set_x5mobile_branch(branch: str) -> None:
    """设置x5mobile分支"""
    return env.set({"x5mobile_branch": branch})


def get_x5mobile_branch() -> str:
    """获取x5mobile分支"""
    return env.get("x5mobile_branch") or ""


class IconCheckMgr:
    def __init__(self, image_path):
        self.all_size = {76, 120, 152, 167, 180, 256, 1024}
        self.platform = os.path.split(image_path)[1]
        self.images_instances = []
        self.file_name_list = []
        for image in os.listdir(image_path):
            self.file_name_list.append(image)
            self.images_instances.append(Image.open(os.path.join(image_path, image)))

    def check_number_of_icon(self):
        number_of_icon = len(self.images_instances)
        log.info("检查图片数量： %s", number_of_icon)
        image_count = 7
        if number_of_icon != image_count:
            msg = "\n".join(self.file_name_list)
            msg = f"{self.platform}图标数量不正确, 数量应该为{image_count}，当前数量为{number_of_icon}\n文件名如下\n{msg}"
            raise PyframeException(msg)

    def check_size_match(self):
        file_size = set()
        log.info("检查图片名称中是否正确")
        for image in self.images_instances:
            filename = image.filename
            size_in_filename = int(re.findall(re.compile(r"_(\d+)\.", re.S), os.path.split(filename)[1])[0])
            file_size.add(size_in_filename)
        return list(self.all_size ^ file_size)

    def check_alpha_exists(self):
        exists_alpha_icon = []
        not_exists_alpha_icon = []
        log.info("检查图片是否存在alpha通道")
        for image in self.images_instances:
            filename = os.path.split(image.filename)[1]
            if image.mode == "RGBA" or image.mode == "LA":
                exists_alpha_icon.append(filename)
                continue

            not_exists_alpha_icon.append(filename)
        log.info("存在alpha通道: %s, 不存在alpha通道: %s", exists_alpha_icon, not_exists_alpha_icon)
        return exists_alpha_icon, not_exists_alpha_icon

    def check_icon_size(self):
        incorrect_icon = []
        for image in self.images_instances:
            filename = os.path.split(image.filename)[1]
            size_in_filename = int(re.findall(re.compile(r"_(\d+)\.", re.S), os.path.split(filename)[1])[0])
            width, height = image.size
            if width != size_in_filename or height != height:
                incorrect_icon.append(filename)
        log.info("检查图标名称和实际尺寸是否匹配, 不匹配图标: %s", incorrect_icon)
        return incorrect_icon


def set_unmatch_icon(platform: str, unmatch_size: List) -> None:
    return env.set({f"{platform}_unmatch_icon": unmatch_size})


def get_unmatch_icon(platform: str) -> List:
    return env.get(f"{platform}_unmatch_icon") or []


def set_alpha_icon(platform: str, not_exists_alpha: List) -> None:
    return env.set({f"{platform}_alpha_icon": not_exists_alpha})


def get_alpha_icon(platform: str) -> List:
    return env.get(f"{platform}_alpha_icon") or []


def set_incorrect_size(platform: str, incorrect_size: List) -> None:
    return env.set({f"{platform}_incorrect_size": incorrect_size})


def get_incorrect_size(platform: str) -> List:
    return env.get(f"{platform}_incorrect_size") or []


def check_android_icon(workspace: str, platform: str):
    android_download_path = os.path.join(workspace, platform)
    icon_check_mgr = IconCheckMgr(android_download_path)
    icon_check_mgr.check_number_of_icon()
    unmatch_size = icon_check_mgr.check_size_match()
    if unmatch_size:
        set_unmatch_icon(platform, unmatch_size)
        raise PyframeException("android图标尺寸不匹配")

    _, not_exists_alpha = icon_check_mgr.check_alpha_exists()
    if not_exists_alpha:
        set_alpha_icon(platform, not_exists_alpha)
        raise PyframeException("android图标有图片没有alpha通道")

    incorrect_icon = icon_check_mgr.check_icon_size()
    if incorrect_icon:
        set_incorrect_size(platform, incorrect_icon)
        raise PyframeException("android图标有图片尺寸和名称不匹配")


def check_ios_icon(workspace: str, platform: str):
    ios_download_path = os.path.join(workspace, platform)
    icon_check_mgr = IconCheckMgr(ios_download_path)
    icon_check_mgr.check_number_of_icon()
    unmatch_size = icon_check_mgr.check_size_match()
    if unmatch_size:
        set_unmatch_icon(platform, unmatch_size)
        raise PyframeException("ios图标尺寸不匹配")

    exists_alpha, _ = icon_check_mgr.check_alpha_exists()
    if exists_alpha:
        set_alpha_icon(platform, exists_alpha)
        raise PyframeException("ios图标有图片存在alpha通道")

    incorrect_icon = icon_check_mgr.check_icon_size()
    if incorrect_icon:
        set_incorrect_size(platform, incorrect_icon)
        raise PyframeException("ios图标有图片尺寸和名称不匹配")


def get_android_path(language: str = "simp") -> Dict:
    return {
        "x5_icon_256.png": [
            f"mobile_dancer/trunk/client_region/{language}/Assets/Plugins/Android/qqx5/res/drawable-hdpi-v4/x5_icon_256.png",
            f"mobile_dancer/trunk/client_region/{language}/Assets/StaticResources/Logo/x5_icon_android.png",
            f"mobile_dancer/trunk/client_region/{language}/experience/formal_root/Assets/StaticResources/Logo/x5_icon_android.png",
            f"mobile_dancer/trunk/client_region/{language}/experience/formal_root/Assets/Plugins/Android/qqx5/res/drawable-hdpi-v4/x5_icon_256.png",
        ],
    }


def get_ios_path(language: str = "simp") -> Dict:
    return {
        "x5_icon_76.png": [f"mobile_dancer/trunk/client_region/{language}/Assets/StaticResources/Logo/x5_icon_76.png"],
        "x5_icon_120.png": [f"mobile_dancer/trunk/client_region/{language}/Assets/StaticResources/Logo/x5_icon_120.png"],
        "x5_icon_152.png": [f"mobile_dancer/trunk/client_region/{language}/Assets/StaticResources/Logo/x5_icon_152.png"],
        "x5_icon_167.png": [f"mobile_dancer/trunk/client_region/{language}/Assets/StaticResources/Logo/x5_icon_167.png"],
        "x5_icon_180.png": [f"mobile_dancer/trunk/client_region/{language}/Assets/StaticResources/Logo/x5_icon_180.png"],
        "x5_icon_256.png": [f"mobile_dancer/trunk/client_region/{language}/Assets/StaticResources/Logo/x5_icon.png"],
    }


def copy_icon(workspace: str, platform: str, git_dir: str) -> None:
    """复制到指定的路径，以便于进行 git 上传"""
    download_path = os.path.join(workspace, platform)
    language = "simp"
    if get_x5mobile_branch().endswith("_tc"):
        language = "trad"
    icons = get_android_path(language) if platform == "android" else get_ios_path(language)
    for image, git_paths in icons.items():
        for git_path in git_paths:
            path_mgr.copy(src=os.path.join(download_path, image), dst=os.path.join(git_dir, git_path))


def set_upload_file_path(platform: str, file_path: List) -> None:
    return env.set({f"{platform}_upload_file_path": file_path})


def get_upload_file_path(platform: str) -> List:
    return env.get(f"{platform}_upload_file_path") or []


def parse_git_staged_files(staged_files: List):
    language = "simp"
    if get_x5mobile_branch().endswith("_tc"):
        language = "trad"
    android_icons = get_android_path(language)
    ios_icons = get_ios_path(language)
    # 需要提交的文件
    upload_android_icons, upload_ios_icons = [], []
    for file in staged_files:
        for _, _path in android_icons.items():
            if file not in _path:
                continue
            upload_android_icons.append(file)
        for _, _path in ios_icons.items():
            if file not in _path:
                continue
            upload_ios_icons.append(file)
    # 设置环境变量
    set_upload_file_path("android", upload_android_icons)
    set_upload_file_path("ios", upload_ios_icons)


def process_user_list(users: List[str]) -> List[str]:
    """用户邮箱特殊处理"""
    ret = []
    for user in users:
        # 特殊处理
        if user == "<EMAIL>":
            ret.append("<EMAIL>")
        else:
            ret.append(user)
    return ret
