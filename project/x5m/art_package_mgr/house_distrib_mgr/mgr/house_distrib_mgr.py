import collections
from typing import List

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config, env_mgr, DisTributeEnum
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr


class HouseDistributeMgr(DistributePackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.HouseDistrib, **kwargs)
        self._execute_method = "H3DBuildTools.BuildArt"
        self._resource_dir_prefix = f"{config.p4_views.get('art_resources_src')}/house"
        self._source_prefix = "Assets/StaticResources/art/3d"
        self._b_path = self.p4_mgr.get_last_branch()
        self._project_client = f"arttrunk/{self._arttrunk_branch}/mobile_dancer/arttrunk/client"
        self._const_source_p4_views = [
            f"{config.p4_views.get('art_resources_src')}/house/common/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/house/common/...",
            f"{config.p4_views.get('art_resources_src')}/house/xuxian/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/house/xuxian/...",
            f"{config.p4_views.get('art_resources_src')}/stage/zaofangzi/LightmapParameters/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/stage/zaofangzi/LightmapParameters/...",
            f"{config.p4_views.get('resmg_root')}/house/... //{self.p4.client}/x5mplan/resmg/house/...",
        ]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/house/",
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/art/house/",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/house/",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"
        self._ab_view = f"{config.p4_views.get('cdn_root')}/cooked_output/{self._platform}/assetbundles/"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for file in source_paths:
            if not file.startswith(self._resource_dir_prefix):
                continue
            if (file.find("jiaju") != -1 and file.find("house_") != -1) or file.find("qiangzhi") != -1:
                if not file.lower().endswith(".prefab"):
                    continue
                file, _ = os.path.split(file)
                depots.add(file)
        log.info(f"filter source paths: {depots}")
        return depots

    def _fill_const_source_p4_views(self):
        """
        填充常量p4view
        """
        self.p4.set_view(self._const_source_p4_views)

    def _fill_source_p4_views(self):
        views = set()
        for path in env_mgr.get_source_paths():
            house_index = path.find("art_src/house")
            if house_index == -1:
                pre_path = "assets/resources/art"
            else:
                pre_path = "assets/staticresources/art/3d"
            house_fangjian_index = path.find("house/fangjian")
            package_type = path[house_fangjian_index + 15 :].split("/")[0]
            views.add(
                f"{path[:house_fangjian_index + 15]}{package_type}/... //{self.p4.client}/{self._project_client}/{pre_path}/house/fangjian/{package_type}/..."
            )
        self._const_source_p4_views.extend(list(views))

    def _make_resouces_file(self):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                house_index = path.find("art_src/house")
                if house_index == -1:
                    pre_path = "assets/resources/art"
                else:
                    pre_path = "assets/staticresources/art/3d"
                asset_path = path.replace(config.p4_views.get("art_resources_src"), pre_path)
                lines.append(asset_path)
            f.write(",".join(lines))

    def _default_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['cdn_root']}",
        ]

    def _trunk_base_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['resources_root'].format(branch='Resources')}",
            f"{config.p4_views['resources_root'].format(branch=f'b/{self._b_path}')}",
        ]

    def _fill_success_ids(self, ids: list):
        success_ids = set()
        for id in ids:
            for path in env_mgr.get_source_paths():
                path: str
                if path.endswith(id) and path.split("/")[-2] == "qiangzhi":
                    success_ids.add("/".join(path.split("/")[-3:]))
                if path.endswith(id) and path.split("/")[-2] != "qiangzhi":
                    success_ids.add(path.split("/")[-1])
        return list(success_ids)

    def _get_distruibute_dict(self):
        success_ids = env_mgr.get_report().get("success_ids")
        success_ids = self._fill_success_ids(success_ids)
        log.info(f"success_ids: {success_ids}")
        dist_destinations = collections.defaultdict(list)
        for id in success_ids:
            for distribute in self._distribute_config:
                ret = distribute.match(self._parse_id_to_filename(id))
                if ret:
                    if distribute.distribute_type == DisTributeEnum.TRUNKBASE:
                        self._distribute_map[distribute.distribute_type].append((id, self._trunk_base_distribute()))
                    else:
                        self._distribute_map[distribute.distribute_type].append((id, [ret]))
                    dist_destinations[str(distribute.distribute_type)].append(id)
                    break
            else:
                self._distribute_map[DisTributeEnum.DEFAULT].append((id, self._default_distribute()))
                dist_destinations[str(DisTributeEnum.DEFAULT)].append(id)
        log.info(f"dist_destinations: {dist_destinations}")
        env_mgr.set_dist_destinations(dist_destinations)
        return self._distribute_map

    def _get_file_root(self, dis_type: DisTributeEnum, root: str):
        if dis_type == DisTributeEnum.DEFAULT:
            return f"{root}/cooked/{self._platform}/assetbundles/art/house/"
        if dis_type == DisTributeEnum.HOTFIX:
            return f"{root}/client/{self._platform}/assetbundles/art/house/"
        if dis_type == DisTributeEnum.TRUNKBASE:
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/house/"
        raise PyframeException(f"Invalid distribute type {dis_type}")

    def _get_path_suffix(self, id: str):
        source_paths = env_mgr.get_source_paths()
        for path in source_paths:
            if path.endswith(id):
                house_index = path.find("house/")
                return path[house_index + 6 :]
        return ""

    def _get_filepath(self, id: str, dis_type: DisTributeEnum, root: str):
        path_suffix = self._get_path_suffix(id)
        return f"{self._get_file_root(dis_type, root)}{path_suffix}"

    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    _base_path = view.rsplit("/", 1)[0]
                    views.add(f"{_base_path}/...")
        self.p4_mgr.add_p4_views(list(views))

    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                path_suffix = self._get_path_suffix(id)
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    dst = self.p4_mgr.parse_p4view_to_localpath(view)
                    src = f"{self.ab_path}art/house/{path_suffix}"
                    if dst.strip("/") != src.strip("/"):
                        path_mgr.copy(src, dst, overwrite=True)
                    self._submit_ab_views.append(view)
