import collections
import re
from typing import List

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config, env_mgr, DisTributeEnum
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr


class HouseSceneMgr(DistributePackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.HouseScene, **kwargs)
        self._execute_method = "H3DBuildTools.BuildArt"
        self._source_prefix = "Assets/StaticResources/art/3d"
        self._b_path = self.p4_mgr.get_last_branch()
        self._project_client = f"arttrunk/{self._arttrunk_branch}/mobile_dancer/arttrunk/client"
        self._resource_dir_prefix = f"{config.p4_views.get('art_resources_src')}/house/scene"
        self._ab_path = os.path.join(self._workspace, f"arttrunk/{self._arttrunk_branch}/mr/Resources/cs/{self._platform}/assetbundles/art/house")
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s+scene/([^/]+)\n")
        self._fail_pattern = [re.compile(r"^\[Error]\s+\d+\s+scene/([^/]+)\n")]
        self._const_source_p4_views = [
            f"{config.p4_views.get('art_resources_src')}/house/scene/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/house/scene/...",
            f"{config.p4_views.get('art_resources_src')}/scene/zaofangzi/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/stage/zaofangzi/...",
            f"{config.p4_views.get('art_resources_src')}/scene/waitingroom_01/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/stage/waitingroom_01/...",
            f"{config.p4_views.get('resmg_root')}/scene/hotfix-house-scene.xlsx //{self.p4.client}/x5mplan/resmg/scene/hotfix-house-scene.xlsx",
        ]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/house/scene/...",
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/art/house/scene/...",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/house/scene/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _filter_source_paths(self, source_paths: List[str]) -> set:

        depots = set()
        for file in source_paths:
            file: str
            if not file.startswith(self._resource_dir_prefix):
                continue
            if file.lower().endswith(".unity"):
                relative_path = file.replace(self._resource_dir_prefix, "assets/staticresources/art/3d/house/scene")
                depots.add(relative_path)
        return depots

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _clean_resource(self) -> None:
        pass

    def _update_git(self) -> None:
        """
        更新git, arttrunk
        """
        self.arttrunk_git_mgr.update_without_delete()

    def _fill_const_source_p4_views(self):
        """
        填充常量p4view
        """
        self.p4.set_view(self._const_source_p4_views)

    def _update_p4(self) -> None:
        """
        更新p4
        """
        # 主要是拉资源路径，资源拉到对应的change
        self._fill_const_source_p4_views()
        self.p4_mgr.sync_all(changelist=env_mgr.get_changelist(), force=env_mgr.get_scene_force())
        self.p4_mgr.clean(d=True)
        # 这个主要是拉ab包路径，ab包每次都拉到head
        self._fill_const_ab_p4_views()
        self.p4_mgr.sync_all(force=env_mgr.get_scene_force())
        self.p4_mgr.clean(d=True)

    def _package(self):
        """
        打包
        """
        self._make_resouces_file()
        source_paths = env_mgr.get_source_paths()
        path = ",".join(source_paths)
        cmd_line = self._get_package_cmd()
        cmd_line += f"path={path} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _default_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['cdn_root']}",
            f"{config.p4_views['cdn_resources']}",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}",
        ]

    def _get_file_root(self, dis_type: DisTributeEnum, root: str):
        if dis_type == DisTributeEnum.DEFAULT:
            if root.startswith("//x5m/res/"):
                return f"{root}/cooked/{self._platform}/assetbundles/art/house/scene/"
            root = root.replace("/ResourcePublish/CDN/SourceFiles", "")
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/house/scene/"
        if dis_type == DisTributeEnum.HOTFIX:
            return f"{root}/client/{self._platform}/assetbundles/art/house/scene/"
        raise PyframeException(f"Invalid distribute type {dis_type}")

    def _get_filepath(self, dis_type: DisTributeEnum, root: str):
        return f"{self._get_file_root(dis_type, root)}"

    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for _, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    views.add(self._get_file_root(distribute_type, path_root))
        self.p4_mgr.add_p4_views(list(views))

    def _get_distruibute_dict(self):
        success_ids = env_mgr.get_report().get("success_ids")
        dist_destinations = collections.defaultdict(list)
        for id in success_ids:
            for distribute in self._distribute_config:
                ret = distribute.match(self._parse_id_to_filename(id))
                if ret:
                    self._distribute_map[distribute.distribute_type].append((id, [ret]))
                    dist_destinations[str(distribute.distribute_type)].append(id)
            else:
                self._distribute_map[DisTributeEnum.DEFAULT].append((id, self._default_distribute()))
                dist_destinations[str(DisTributeEnum.DEFAULT)].append(id)
        log.info(f"dist_destinations: {dist_destinations}")
        env_mgr.set_dist_destinations(dist_destinations)
        return self._distribute_map

    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    view = self._get_filepath(distribute_type, path_root)
                    dst = self.p4_mgr.parse_p4view_to_localpath(view)
                    src = os.path.join(self.ab_path, "scene")
                    path_mgr.copy(src, dst[:-1], overwrite=True)
                    self._submit_ab_views.extend([f"{view}{id}", f"{view}{id}.h3dmanifest"])

    def _dist_ab(self):
        self._get_distruibute_dict()
        self._sync_dist()
        self._dist_ab_file()
