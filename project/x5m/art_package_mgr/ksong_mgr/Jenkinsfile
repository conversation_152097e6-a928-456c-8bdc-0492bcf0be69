@Library('h3d_libs') _
node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
    }
}

pipeline {
    agent {
        node {
            label "art_package_ksong_ios"
            customWorkspace "D:\\k_song\\workspace"
        }
    }
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
    }
    parameters {
        file(name: 'KSongZip', description: 'k歌资源zip包')
    }
    environment {
        PIPELINE_PYTHON = 'python'
        RESOURCE = 'ksong'
    }
    stages {
        stage("音频资源打包") {
            stages {
                stage("解压zip") {
                    steps {
                        script {
                            def kSongZipPath = unstashParam "KSongZip"
                            echo "Upload file path: ${kSongZipPath}"
                            def parts = kSongZipPath.split("\\.")
                            if (parts.length > 0) {
                                kSongDirName = parts[0]
                                echo "kSongDirName: ${kSongDirName}"
                            }
                            def zipDir = "${WORKSPACE}\\zip_dir"
                            if (!fileExists(zipDir)) {
                                bat "mkdir ${zipDir}"
                            }
                            bat """
                                move "${kSongZipPath}" "${zipDir}"
                            """
                            def zipFilePath = "${zipDir}\\${kSongZipPath}"
                            echo "zipFilePath: ${zipFilePath}"
                            def unzipDir = "${WORKSPACE}\\unzip_dir"
                            bat """
                                if exist "${unzipDir}" del /F /S /Q "${unzipDir}"
                            """
                            if (!fileExists(unzipDir)) {
                                bat "mkdir ${unzipDir}"
                            }
                            def kSongUnzipDir = "${unzipDir}\\${kSongDirName}"
                            bat """
                                7z x -o"${kSongUnzipDir}" -aoa -y "${zipFilePath}"
                            """
                            bat(script: "${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r pyframe-pipeline/requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q")
                        }
                    }
                }
                stage("准备") {
                    steps {
                        dir("pyframe-pipeline") {
                            script {
                                bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=prepare")
                            }
                        }
                    }
                }
                stage('校验文件') {
                    steps {
                        dir('pyframe-pipeline') {
                            script {
                                bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=check_package")
                            }
                        }
                    }
                }
                stage('获取p4资源') {
                    steps {
                        dir("pyframe-pipeline") {
                            script {
                                bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=update_git_and_p4")
                            }
                        }
                    }
                }
                stage('调用打包命令') {
                    steps {
                        dir("pyframe-pipeline") {
                            script {
                                bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=package")
                            }
                        }
                    }
                }
//                 stage("提交产物") {
//                     steps {
//                         dir("pyframe-pipeline") {
//                             script {
//                                 bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=submit")
//                             }
//                         }
//                     }
//                 }
            }
            post {
                always {
                    dir("pyframe-pipeline") {
                        script {
                            bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_always")
                        }
                    }
                }
            }

        }
    }
    post {
        unstable {
            dir("${env.workspace}/pyframe-pipeline") {
                script {
                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.workspace}/pyframe-pipeline") {
                script {
                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_success")
                }
            }
        }
        failure {
            dir("${env.workspace}/pyframe-pipeline") {
                script {
                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_failure")
                }
            }
        }
    }
}
