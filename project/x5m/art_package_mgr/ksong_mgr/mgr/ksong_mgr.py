import logging
import zipfile
import subprocess
import shutil
from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, env_mgr, ResourceTypeEnum, X5mGitMgr


class KSongMgr(PackageMgr):
    def __init__(self, **kwargs):
        super().__init__(ResourceTypeEnum.KSong, **kwargs)
        self._execute_method = "PipelineCall.BuildConfig"
        self._arttrunk_branch = "master"
        self._unzip_dir = os.path.join(self._workspace, "unzip_dir")
        self._audio_dir = os.path.join(self._project_path, "Assets", "resources", "ksong", "Audio")
        self._lrc_dir = os.path.join(self._project_path, "Assets", "resources", "ksong", "LRC")
        self._output_path = os.path.join(self._workspace, "arttrunk", "master", "mr", "output")
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _other_check(self):
        # 获取_unzip_dir下的第一层文件夹
        ksong_dir = os.listdir(self._unzip_dir)[0]
        ksong_root_path = os.path.join(self._unzip_dir, ksong_dir, ksong_dir)

        # 遍历该文件夹下的所有子目录 (如 1001, 1002 等)
        for ksong_id_dir in os.listdir(ksong_root_path):
            ksong_id_dir_path = os.path.join(ksong_root_path, ksong_id_dir)

            if not os.path.isdir(ksong_id_dir_path):
                continue  # 跳过非目录项

            expected_ogg_file = f"{ksong_id_dir}.ogg"
            expected_lrc_file = f"{ksong_id_dir}.lrc"

            actual_files = os.listdir(ksong_id_dir_path)

            if expected_ogg_file not in actual_files:
                raise PyframeException(f"歌曲ID目录 {ksong_id_dir} 缺少必要文件: {expected_ogg_file}")

            if expected_lrc_file not in actual_files:
                raise PyframeException(f"歌曲ID目录 {ksong_id_dir} 缺少必要文件: {expected_lrc_file}")

        log.info("所有歌曲ID目录文件校验通过")

    def check_package(self) -> bool:
        env_mgr.set_need_package(True)
        return True

    def _clean_resource(self) -> None:
        """
        清理资源
        """
        self._safe_clean(self._audio_dir, "开始清理音频目录")
        self._safe_clean(self._lrc_dir, "开始清理歌词目录")
        self._safe_clean(self._output_path, "开始清理输出目录")

    def _update_config_git(self) -> None:
        config_git_mgr = X5mGitMgr(self._workspace, "x5mconfig", "master")
        config_git_mgr.update(clean=True)

    def _fill_source_p4_views(self) -> None:
        views = [f"//x5_mobile/mr/dgm_res/ksong/... //{self.p4.client}/arttrunk/master/mr/dgm_res/ksong/..."]
        self.p4.set_view(views)

    def _update_p4(self) -> None:
        self._fill_source_p4_views()
        self.p4.sync_all(force=env_mgr.get_force())

    def _split_vocals(self, ogg_file_path: str, output_dir: str) -> None:
        """
        调用 API 分离人声和伴奏，并将结果保存到指定目录。

        Args:
            ogg_file_path: .ogg 文件的路径。
            output_dir: 输出目录，用于保存解压后的文件。
        """
        # 构造输出 ZIP 文件路径
        logging.info(f"开始分离人声和伴奏，ogg文件路径：{ogg_file_path}")
        zip_file_path = os.path.splitext(ogg_file_path)[0] + ".zip"
        ksong_id_dir = os.path.basename(output_dir)  # 获取当前歌曲目录名作为ID
        p4_target_dir = f"{self._workspace}/arttrunk/master/mr/dgm_res/ksong/"

        # 调用 curl 命令进行 API 请求
        curl_cmd = f"curl -o {zip_file_path} "
        curl_cmd += f"-F file=@{ogg_file_path} "
        curl_cmd += "http://**************:21935/api/ksong/split_vocals"

        try:
            # 执行命令
            log.info(f"执行命令: {curl_cmd}")
            ret, _ = cmd.run_shell(cmds=[curl_cmd], workdir=self._workspace)
            if ret != 0:
                raise PyframeException(f"curl命令执行失败，错误码:{ret}")

            # 解压 ZIP 文件到目标目录
            with zipfile.ZipFile(zip_file_path, "r") as zip_ref:
                zip_ref.extractall(output_dir)

            # 定义原始文件路径
            vocals_path = os.path.join(output_dir, "vocals.ogg")
            others_path = os.path.join(output_dir, "others.ogg")

            # 定义新文件路径
            new_vocals_path = os.path.join(output_dir, f"{ksong_id_dir}_vocals.ogg")
            new_others_path = os.path.join(output_dir, f"{ksong_id_dir}_acc.ogg")

            # 重命名文件
            if os.path.exists(vocals_path):
                os.rename(vocals_path, new_vocals_path)
                log.info(f"人声文件已重命名为: {new_vocals_path}")
            else:
                raise PyframeException("未找到分离后的人声文件: vocals.ogg")

            if os.path.exists(others_path):
                os.rename(others_path, new_others_path)
                log.info(f"伴奏文件已重命名为: {new_others_path}")
                # 把new_others_path文件复制到p4_target_dir对应的id目录下
                p4_target_path = os.path.join(p4_target_dir, ksong_id_dir)
                shutil.copy2(new_others_path, p4_target_path)
                log.info(f"伴奏文件已复制到P4目标目录: {p4_target_path}")
            else:
                raise PyframeException("未找到分离后的伴奏文件: others.ogg")

        except subprocess.CalledProcessError as e:
            log.error(f"调用 API 失败: {e.stderr}")
            raise PyframeException("调用人声分离 API 失败")
        except zipfile.BadZipFile:
            log.error("下载的文件不是有效的 ZIP 文件")
            raise PyframeException("无效的 ZIP 文件")
        finally:
            # 删除 ZIP 文件以清理临时文件
            if os.path.exists(zip_file_path):
                os.remove(zip_file_path)

    def _process_songs(self) -> None:
        """
        遍历 ksong_dir 目录下的所有歌曲目录，处理每个 .ogg 文件。
        """
        # 获取_unzip_dir下的第一层文件夹
        ksong_dir = os.listdir(self._unzip_dir)[0]
        ksong_root_path = os.path.join(self._unzip_dir, ksong_dir, ksong_dir)
        p4_target_dir = f"{self._workspace}/arttrunk/master/mr/dgm_res/ksong"
        path_mgr.xcopy(ksong_root_path, p4_target_dir, dst_is_file=False)

        # 遍历该文件夹下的所有子目录 (如 1001, 1002 等)
        for ksong_id_dir in os.listdir(ksong_root_path):
            ksong_id_dir_path = os.path.join(ksong_root_path, ksong_id_dir)

            if not os.path.isdir(ksong_id_dir_path):
                continue  # 跳过非目录项

            # 查找当前目录下的 .ogg 文件
            for file_name in os.listdir(ksong_id_dir_path):
                if file_name.endswith(".ogg"):
                    ogg_file_path = os.path.join(ksong_id_dir_path, file_name)
                    self._split_vocals(ogg_file_path, ksong_id_dir_path)

        log.info("所有人声和伴奏处理完成")

    def _move_resource(self) -> None:
        """
        遍历 ksong 目录下的所有歌曲 ID 目录，将人声文件和歌词文件分别复制到指定路径，
        并将整个资源上传至 P4 指定路径。
        """
        # 获取_unzip_dir下的第一层文件夹
        ksong_dir = os.listdir(self._unzip_dir)[0]
        ksong_root_path = os.path.join(self._unzip_dir, ksong_dir, ksong_dir)

        p4_target_dir = f"{self._workspace}/arttrunk/master/mr/dgm_res/ksong/"

        log.info(f"开始移动资源到: {self._audio_dir}, {self._lrc_dir} 和 P4 路径: {p4_target_dir}")

        # 遍历歌曲 ID 目录
        for ksong_id_dir in os.listdir(ksong_root_path):
            ksong_id_dir_path = os.path.join(ksong_root_path, ksong_id_dir)

            if not os.path.isdir(ksong_id_dir_path):
                continue  # 跳过非目录项

            # 构建源文件路径
            vocals_ogg_file = f"{ksong_id_dir}_vocals.ogg"
            lrc_file = f"{ksong_id_dir}.lrc"

            src_vocals_path = os.path.join(ksong_id_dir_path, vocals_ogg_file)
            src_lrc_path = os.path.join(ksong_id_dir_path, lrc_file)

            # 构建目标路径
            dst_audio_path = os.path.join(self._audio_dir, vocals_ogg_file)
            dst_lrc_path = os.path.join(self._lrc_dir, lrc_file)

            # 复制人声文件
            if os.path.exists(src_vocals_path):
                shutil.copy2(src_vocals_path, dst_audio_path)
                log.info(f"已复制人声文件: {src_vocals_path} → {dst_audio_path}")
            else:
                raise PyframeException(f"未找到人声文件: {src_vocals_path}")

            # 复制歌词文件
            if os.path.exists(src_lrc_path):
                shutil.copy2(src_lrc_path, dst_lrc_path)
                log.info(f"已复制歌词文件: {src_lrc_path} → {dst_lrc_path}")
            else:
                raise PyframeException(f"未找到歌词文件: {src_lrc_path}")

    def update_git_and_p4(self) -> None:
        """
        更新git和p4
        """
        if not env_mgr.get_need_package():
            return
        self._clean_resource()
        self._update_git()
        self._update_p4()
        self._process_songs()

    def _get_package_cmd(self) -> str:
        cmd_line = f"{self._platform.package_tool} "
        cmd_line += f"-quit -batchmode -projectPath {self._project_path} -logFile {self._log_file} "
        cmd_line += f"-executeMethod {self._execute_method} "
        cmd_line += f"-audioDir {self._audio_dir} -lyricDir {self._lrc_dir} "
        cmd_line += f"outputPath={self._output_path} "
        return cmd_line.replace("/", "\\")

    def _package(self):
        """
        打包
        """
        cmd_line = self._get_package_cmd()
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _calculate_package_result(self):
        """
        计算打包结果
        """
        ksong_dir = os.listdir(self._unzip_dir)[0]
        upload_ids = os.listdir(os.path.join(self._unzip_dir, ksong_dir, ksong_dir))
        package_files = os.listdir(self._output_path)
        package_ids = [file.split(".")[0] for file in package_files if file.endswith(".xml")]
        success_ids = list(set(upload_ids) & set(package_ids))
        fail_ids = list(set(upload_ids) - set(success_ids))
        report_detail = {
            "total_count": len(upload_ids),
            "succ_count": len(success_ids),
            "failed_count": len(fail_ids),
            "error_count": len(fail_ids),
            "success_ids": success_ids,
            "fail_ids": fail_ids,
            "error_infos": "",
        }
        log.info(f"report_detail: {report_detail}")
        env_mgr.set_report(report=report_detail)

    def package(self):
        if not env_mgr.get_need_package():
            return
        self._move_resource()
        self._package()
        self._calculate_package_result()

    def _copy_product(self):
        # 将output目录下的.xml文件复制到x5mconfig\master\config\shared\voice_room_ksong_pitch\目录下
        x5mconfig_path = os.path.join(self._workspace, "x5mconfig/master/config/shared/voice_room_ksong_pitch")
        for file in os.listdir(self._output_path):
            if file.endswith(".xml"):
                src = os.path.join(self._output_path, file)
                dst = os.path.join(x5mconfig_path, file)
                path_mgr.xcopy(src, dst, dst_is_file=True)

    def _submit_git(self):
        config_git_mgr = X5mGitMgr(self._workspace, "x5mconfig", "master")
        config_git_mgr.submit_path(self._desc, os.path.join(self._workspace, "x5mconfig/master/config/shared/voice_room_ksong_pitch"))

    def _submit_p4(self):
        p4_view = f"//{self.p4.client}/arttrunk/master/mr/dgm_res/ksong/..."
        self.p4.reconcile(p4_view, add=True, edit=True)
        self.p4.submit(self._desc)

    def submit(self):
        if not env_mgr.get_need_package():
            return
        self.__get_desc()
        self._update_config_git()
        self._copy_product()
        self._submit_git()
        self._submit_p4()
