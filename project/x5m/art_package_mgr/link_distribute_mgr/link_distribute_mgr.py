import logging
import os.path
import re
import shutil
import typing
from fnmatch import fnmatchcase as match
from typing import List, Iterable

from frame import *
from project.x5m.base_mgr import BaseWorkflow
from project.x5m.base_mgr.enum_mgr import PlatformEnum
from project.x5m.base_mgr.git_mgr import X5mGitMgr


class LinkDistributeMgr(BaseWorkflow):
    kind = "link_distribute"
    resource_path = "x5_mobile/mr/art_release/art_src/role/link"
    execute_method = "H3DBuildTools.BuildBodyPartOrLink"
    default_changelist = 460436

    class P4Setting(BaseWorkflow.P4Setting):
        hotfix_rule_depot = "x5mplan/resmg/link/hotfix-link-rule.xlsx"
        special_rule_depot = "x5mplan/resmg/link/special-branch-link-rule.xlsx"
        trunk_rule_depot = "x5mplan/resmg/link/trunk-link-base.xlsx"

    def workflow(self):
        return (
            ("展示系统信息", self.prepare),
            ("获取打包工具repo", self.update_or_clone_repo),
            ("判断是否需要打包", self.check_package),
            ("清理上次打包资源", self.clean_resource),
            ("获取P4资源", self.get_p4_resource),
            ("准备打包资源", self.prepare_resource),
            ("打包", self.package),
            ("计算打包结果", self.calculate_package_result),
            ("配置需要提交的文件", self.get_need_submit_files),
            ("提交Git", self.submit_files_to_git),
            ("复制包到P4路径", self.copy_package_to_p4_path),
            ("提交P4", self.submit_files_to_p4),
            ("持久化配置", self.save_result),
            ("备份+上传", self.always),
        )

    def get_p4_base_map(self):
        return []

    def transform_p4_to_art_trunk_path(self, local_path):
        _, target = local_path.split("art_src/")
        return f"Assets/resources/Art/{target}"

    def prepare_base_resource(self, depot_files, **kwargs):
        # TODO: 增加复制资源路径是因为衍生物存在资源依赖， 单独文件无法打包， 后续如果可以提供资源引用， 可以移除此粗糙方法
        return

    def _make_resouces_file(self, source_paths: typing.List[str] = None):
        """
        输入路径有两种方式，现在采用第二种方式
        1. 传path=xxxx
        2. 把相关路径写在AssetBundleTool/resources.txt文件中
        """
        # 创建日志报告目录
        if not os.path.exists(self._report_path):
            os.makedirs(self._report_root, exist_ok=True)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in source_paths:
                path = os.path.basename(path)
                lines.append(path)
            f.write(",".join(lines))

    def get_p4_target(self, ff) -> typing.List[typing.Tuple[str, str]]:
        custom_fname_func = getattr(self, "custom_fname", lambda ff: ff.split("/")[-1])
        file_name = custom_fname_func(ff)

        # 规则值判断文件名， 不判断文件后缀
        data_list_special = self.get_p4_rule_special()
        for rule_item, _target in data_list_special:
            if match(file_name, rule_item):
                return [("special", _target)]

        data_list_hotfix = self.get_p4_rule_hotfix()
        for rule_item, _target in data_list_hotfix:
            if match(file_name, rule_item):
                return [("hotfix", _target)]

        data_list_trunk = self.get_p4_rule_trunk()
        for rule_item, _target in data_list_trunk:
            if match(file_name, rule_item):
                return [("master", _target), ("branch", self.get_p4_latest_branch())]

        target_list = []
        if self.P4Setting.cdn in self.distribute_branch:
            target_list.append(("cdn", ""))
        return target_list

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        source_paths = filter(
            lambda x: x.startswith(f"//{self.resource_path}"), source_paths
        )
        for file in source_paths:
            dir_name = os.path.dirname(file)
            idx = re.findall(r"[0-9]{10}", dir_name)
            log.info(f"id: {idx}")
            if not idx:
                continue
            id_index = dir_name.find(idx[0])
            if id_index == -1:
                continue
            dir_name = dir_name[0:id_index] + idx[0]
            depots.add(dir_name)
        return depots

    def get_depot_bind_resources(self, depot_files: Iterable[str]):
        p4_views_map = set()
        depot_bind_files = set()
        for depot in depot_files:
            for d in self._get_bind_resource(depot):
                v = self.format_p4_depot(d)
                p4_views_map.add(f"//{v}/... //{self.p4_mgr.client}/{v}/...")
                depot_bind_files.add(f"//{v}")

        return list(p4_views_map), list(depot_bind_files)

    def _get_bind_resource(self, depot_path: str):
        basename = os.path.basename(depot_path)
        role = os.path.basename(os.path.dirname(depot_path))
        if not role in ("male", "female"):
            return []
        if not re.match(r"\d{10}", basename):
            return []
        common_name = f"{basename[:-2]}*"
        common_depot = os.path.dirname(os.path.dirname(depot_path))
        dirs = self.p4_mgr.p4_cli.raw_p4.run_dirs(
            [
                f"{common_depot}/male/{common_name}",
                f"{common_depot}/female/{common_name}",
            ]
        )
        return [i.get("dir") for i in dirs if i.get("dir")]

    def _fill_source_p4_views(self, source_paths):
        views = set()
        for path in source_paths:
            paths = path.split("/")
            gender = paths[-3]
            file_dir = paths[-2]
            path = path.replace(gender, "*").replace(file_dir, f"*{file_dir[2:-2]}*")
            views.add(path)
        self.p4_mgr.add_p4_views(list(views))

    def update_or_clone_repo(self, *args, **kwargs) -> None:
        grm_arttrunk = X5mGitMgr(self.workspace, "arttrunk", "release")
        grm_arttrunk.update(clean=False, commit_id="origin/release", depth=1)
        self.attrunk_client_path = os.path.join(
            grm_arttrunk.workspace, "mobile_dancer", "arttrunk", "client"
        )
        x5mconfig = X5mGitMgr(self.workspace, "x5mconfig", "cdn")
        x5mconfig.update(clean=False, commit_id="origin/cdn")

    def submit_files_to_git(self, relative_result: Iterable[str] = None, **kwargs):
        if not relative_result:
            relative_result = self.runtime_conf["get_need_submit_files"]["output"].get(
                "relative_result", []
            )

        x5mconfig = X5mGitMgr(self._workspace, "x5mconfig", "cdn")
        x5mconfig.update(clean=False, commit_id="origin/cdn")
        # git只用提交一次
        if PlatformEnum(self.platform) != PlatformEnum.IOS:
            return
        git_project = "x5mconfig"
        branch = "cdn"
        git_root = x5mconfig.workspace
        if not os.path.exists(os.path.join(git_root, "config/custom_accouterment")):
            os.makedirs(
                os.path.join(git_root, "config/custom_accouterment"), exist_ok=True
            )
        need_submit = []
        # art/role/link/male/0022893101
        for path in relative_result:
            id = path.split("/")[-1]
            file_name = f"custom_{id}.xml"
            p4_path = f"x5_mobile/mr/art_release/art_src/{path[4:]}/{file_name}"
            full_path = self.p4_mgr.get_files_local_path(p4_path)
            logging.info(f"full_path:{full_path}")
            if not os.path.exists(full_path):
                continue
            need_submit.append(full_path)
            shutil.move(
                full_path,
                os.path.join(git_root, f"config/custom_accouterment/{file_name}"),
            )

        logging.info(f"need_submit: {need_submit}")
        if need_submit:
            os.chdir(os.path.join(git_root, "config/custom_accouterment"))
            os.system("git add .")
            os.system(f'git commit -m "{self.get_desc()}"')
            ret = os.system(f"git push origin {branch}")
            if ret != 0:
                raise Exception(f"上传git失败, 错误码: {ret}")
            commit_id = os.popen("git rev-parse HEAD").read()
            log.info(
                f"https://x5mobile-gitlab.h3d.com.cn/dgm/{git_project}/-/commit/{commit_id}"
            )
        return {
            "relative_result": relative_result,
            **kwargs,
        }
