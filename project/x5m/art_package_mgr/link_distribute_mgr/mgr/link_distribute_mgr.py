import collections
import re
from frame import *
from typing import List
from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config, env_mgr, X5mGitMgr, PlatformEnum, path_mgr, DisTributeEnum
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr


class LinkDistributeMgr(DistributePackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(kind=ResourceTypeEnum.LinkDistrib, **kwargs)
        self._execute_method = "H3DBuildTools.BuildBodyPartOrLink"
        self._resource_dir_prefix = f"{config.p4_views.get('art_resource_root')}/role/link"
        self._b_path = self.p4_mgr.get_last_branch()
        self._large_ab = []
        self._ab_path = os.path.join(self._workspace, f"arttrunk/release/mr/Resources/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/")
        self._const_source_p4_views = [
            f"{config.p4_views.get('art_resource_root')}/role/cloth_show/...",
            f"{config.p4_views.get('resmg_root')}/link/...",
        ]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/role/link/...",
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/art/role/link/...",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/role/link/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for file in source_paths:
            file: str
            if not file.startswith(self._resource_dir_prefix):
                continue
            if (
                file.find("bodypart/female/face/") != -1
                or file.find("bodypart/female/plainBody/") != -1
                or file.find("bodypart/male/face/") != -1
                or file.find("bodypart/male/plainBody/") != -1
                or file.find("/unencrypt/") != -1
            ):
                continue
            dir_name = os.path.dirname(file)
            id = re.findall(r"[0-9]{10}", dir_name)
            log.info(f"id: {id}")
            if not id:
                continue
            id_index = dir_name.find(id[0])
            if id_index == -1:
                continue
            dir_name = dir_name[0:id_index] + id[0] + "/"
            depots.add(dir_name)
        return depots

    def _fill_source_p4_views(self):
        views = set()
        for path in env_mgr.get_source_paths():
            paths = path.split("/")
            gender = paths[-3]
            file_dir = paths[-2]
            path = path.replace(gender, "*").replace(file_dir, f"*{file_dir[2:-2]}*")
            views.add(path)
        self.p4_mgr.add_p4_views(list(views))

    def _make_resouces_file(self):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                link_id = path.split("/")[-2]
                lines.append(link_id)
            f.write(",".join(lines))

    def _update_config_git(self) -> None:
        self._cdn_git = X5mGitMgr(self._workspace, "x5mconfig", "cdn")
        self._cdn_git.update(clean=True)

    def _copy_product(self):
        submit_file_path = os.path.join(env_mgr.get_workspace(), "x5mconfig/cdn/config/custom_accouterment")
        for file in env_mgr.get_source_paths():
            log.info(f"file: {file}")
            path = self.p4_mgr.parse_p4view_to_localpath(file)
            log.info(f"path: {path}")
            if not os.path.exists(path):
                continue
            id = path.split("/")[-2]
            for filename in os.listdir(path):
                if filename.endswith(f"custom_{id}.xml"):
                    log.info(f"need submit related config file `{filename}`, full path: `{path}`")
                    src = os.path.join(path, filename)
                    path_mgr.xcopy(src, submit_file_path, dst_is_file=False)

    def _submit_git(self):
        # git只用提交一次
        if self._platform != PlatformEnum.IOS:
            return
        self._cdn_git.submit_path(self._desc, os.path.join(self._workspace, "x5mconfig/cdn/config/custom_accouterment"))

    def _trunk_base_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['resources_root'].format(branch='Resources')}",
            f"{config.p4_views['resources_root'].format(branch=f'b/{self._b_path}')}",
        ]

    def _get_distruibute_dict(self):
        success_ids = env_mgr.get_report().get("success_ids")
        dist_destinations = collections.defaultdict(list)
        for id in success_ids:
            for distribute in self._distribute_config:
                ret = distribute.match(self._parse_id_to_filename(id))
                if ret:
                    if distribute.distribute_type == DisTributeEnum.TRUNKBASE:
                        self._distribute_map[distribute.distribute_type].append((id, self._trunk_base_distribute()))
                    else:
                        self._distribute_map[distribute.distribute_type].append((id, [ret]))
                    dist_destinations[str(distribute.distribute_type)].append(id)
                    break
            else:
                self._distribute_map[DisTributeEnum.DEFAULT].append((id, self._default_distribute()))
                dist_destinations[str(DisTributeEnum.DEFAULT)].append(id)
        log.info(f"dist_destinations: {dist_destinations}")
        env_mgr.set_dist_destinations(dist_destinations)
        return self._distribute_map

    def _get_file_root(self, dis_type: DisTributeEnum, root: str):
        if dis_type == DisTributeEnum.DEFAULT:
            return f"{root}/cooked/{self._platform}/assetbundles/art/role/link/"
        if dis_type == DisTributeEnum.HOTFIX:
            return f"{root}/client/{self._platform}/assetbundles/art/role/link/"
        if dis_type == DisTributeEnum.SPECIAL:
            return f"{config.p4_views.get('resources_root').format(branch='b')}/{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/role/link/"
        if dis_type == DisTributeEnum.TRUNKBASE:
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/role/link/"
        raise PyframeException(f"Invalid distribute type {dis_type}")

    def _get_filepath(self, id: str, dis_type: DisTributeEnum, root: str):
        gender = "male" if id[0] == "0" else "female"
        return f"{self._get_file_root(dis_type, root)}{gender}/{id}"

    def _default_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['cdn_root']}",
        ]

    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            if distribute_type == DisTributeEnum.SPECIAL:
                for distribute in self._distribute_config:
                    if distribute.distribute_type == DisTributeEnum.SPECIAL:
                        special_branch = self._get_special_branch(distribute.distribute_rule.items())
                        if special_branch:
                            views.add(
                                f"{config.p4_views['resources_root']}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/role/link/".format(
                                    branch=f"b/{special_branch}"
                                )
                            )
            for _, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    file_root = self._get_file_root(distribute_type, path_root)
                    log.info(f"dist p4 view file root: {file_root}")
                    views.add(file_root)
        self.p4_mgr.add_p4_views(list(views))

    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    if not view[-2:] == "01":
                        view = view[0 : len(view) - 2] + "01"
                    log.info(f"distribute ab file view: {view}")
                    gender = "male" if id[0] == "0" else "female"
                    dst = self.p4_mgr.parse_p4view_to_localpath(view)
                    src = os.path.join(self.ab_path, f"art/role/link/{gender}/{id}")
                    large_abs = self._get_large_ab([src])
                    if large_abs:
                        self._large_ab += large_abs
                        continue
                    # NOTE: 只有文件路径不一致时，才进行文件的复制操作
                    if src.replace("\\", "/") != dst.replace("\\", "/"):
                        path_mgr.copy(src, dst, overwrite=True)
                    self._submit_ab_views.append(view)

    def _get_special_branch(self, data_list_special) -> str:
        max_branch = self._b_path
        special_data_branches = []
        for special_data in data_list_special:
            for special_data_name in special_data:
                special_data_branch = special_data[special_data_name]
                if special_data_branch not in special_data_branches and special_data_branch.startswith(max_branch):
                    special_data_branches.append(special_data_branch)
        if special_data_branches:
            return special_data_branches[0]
        return ""
