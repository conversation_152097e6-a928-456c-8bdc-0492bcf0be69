import re

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, ResourceTypeEnum, config, env_mgr


class LinkUnencryptMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.LINK_UNENCRYPT, **kwargs)
        self._resource_dir_prefix = f"{config.p4_views.get('art_resource_root')}/role/link/unencrypt"
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/role/link/unencrypt/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: list) -> set:
        depots = set()
        id_list = []
        for file in source_paths:
            file: str
            if (
                file.find("bodypart/female/face/") != -1
                or file.find("bodypart/female/plainBody/") != -1
                or file.find("bodypart/male/face/") != -1
                or file.find("bodypart/male/plainBody/") != -1
                or file.find(self._resource_dir_prefix) == -1
            ):
                continue
            dir_name = os.path.dirname(file)
            id = re.findall(r"[0-9]{10}", dir_name)
            if not id:
                continue
            if id[0] not in id_list:
                id_index = dir_name.find(id[0])
                if id_index == -1:
                    continue
                id_list.append(id[0])
                id_path = dir_name[0:id_index] + id[0] + "/"
                depots.add(id_path)
        return depots

    def _fill_source_p4_views(self):
        views = []
        for path in env_mgr.get_source_paths():
            # id后两位改成*
            views.append(path[:-3] + "*/")
        self.p4_mgr.add_p4_views(views)

    def _make_resouces_file(self):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                link_id = path.split("/")[-2]
                lines.append(link_id)
            f.write(",".join(lines))

    def _package(self):
        """
        打包
        """
        self._make_resouces_file()
        source_paths = env_mgr.get_source_paths()
        paths = ",".join([path[:-1].replace(config.p4_views.get("art_resource_root"), "assets/resources/art").strip() for path in source_paths])
        cmd_line = self._get_package_cmd()
        cmd_line += f"path={paths} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _get_submit_p4_view_ab(self):
        files = set()
        for submit_file in env_mgr.get_source_paths():
            submit_file: str

            submit_file = submit_file.replace(self._resource_dir_prefix, self._ab_view + "art/role/link/unencrypt")
            file, _ = os.path.splitext(submit_file)
            if file[-3:] == "01/":
                files.add(file)
                file = file[:-1]
                for i in range(10):
                    extend_path = f"{file}_extend{i + 1}/"
                    files.add(extend_path)
            else:
                file = file[0:-3] + "01/"
                files.add(file)
        return list(files)
