import re
from typing import List

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config, env_mgr, DisTributeEnum
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr


class MakeupDistribMgr(DistributePackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.MAKEUP_DISTRIB, **kwargs)
        self._execute_method = "H3DBuildTools.BuildMakeup"
        self._b_path = self.p4_mgr.get_last_branch()
        self._success_pattern = re.compile(r"^【Info】.*$|[/]([^/]+)\n$")
        self._resource_dir_prefix = f"{config.p4_views.get('art_resources_src')}/role/makeup"
        self._const_ab_p4_views = [
            f"{config.p4_views.get('resmg_root')}/makeup/",
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/art/role/makeup/",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/role/makeup/",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        exist_list = list()
        for file in source_paths:
            if not file.startswith(self._resource_dir_prefix):
                continue
            release_path = file.replace(self._resource_dir_prefix, "").lstrip("/")
            release_list = release_path.split("/")
            valid_path = "/".join(release_list[:3])
            if valid_path not in exist_list:
                exist_list.append(valid_path)
                p4_valid_path = f"{self._resource_dir_prefix}/{valid_path}"
                depots.add(p4_valid_path)
        return depots

    def _fill_source_p4_views(self):
        views = set()
        for path in env_mgr.get_source_paths():
            views.add(f"{path}/...")
        self.p4_mgr.add_p4_views(list(views))

    def _make_resources_file(self):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                if path.find("/makeup/") == -1:
                    continue
                asset_path = path.replace(config.p4_views.get("art_resources_src"), "assets/resources/art")
                lines.append(asset_path)
            f.write(",".join(lines))

    def _package(self):
        """
        打包
        """
        self._make_resources_file()
        source_paths = env_mgr.get_source_paths()
        path = ",".join([path.rsplit("/", 1)[1] for path in source_paths])
        cmd_line = self._get_package_cmd()
        cmd_line += f"path={path} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _calculate_package_result(self):
        """
        计算打包结果
        """
        reports = path_mgr.glob(self._report_path, "*.txt")
        if not reports:
            raise PyframeException(f"获取打包结果失败，请检查查看打包日志")
        report = reports[-1]
        with open(report, "r", encoding="utf-8") as f:
            content = f.readlines()
            # 解析打包结果
            report_detail = self._get_report_detail(content)
            success_ids = report_detail.get("success_ids")
            report_detail["success_ids"] = [id for id in success_ids if id != "shaders"]

            # 记录打包结果
            log.debug(f"platform: {self._platform}, report_detail: {report_detail}")
            env_mgr.set_report(report=report_detail)

    def _parse_id_to_filename(self, id: str) -> str:
        source_paths = env_mgr.get_source_paths()
        for path in source_paths:
            if path.endswith(id):
                return path.rsplit("/", 1)[1]
        return id

    def _default_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['resources_root'].format(branch='Resources')}",
            f"{config.p4_views['resources_root'].format(branch=f'b/{self._b_path}')}",
        ]

    def _get_file_root(self, dis_type: DisTributeEnum, root: str):
        if dis_type == DisTributeEnum.DEFAULT:
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/role/makeup/"
        if dis_type == DisTributeEnum.HOTFIX:
            return f"{root}/client/{self._platform}/assetbundles/art/role/makeup/"
        if dis_type == DisTributeEnum.CDNRULE:
            return f"{root}/cooked/{self._platform}/assetbundles/art/role/makeup/"
        raise PyframeException(f"Invalid distribute type {dis_type}")

    def _get_path_suffix(self, id: str):
        source_paths = env_mgr.get_source_paths()
        for path in source_paths:
            if path.endswith(id):
                makeup_index = path.find("/makeup/")
                id_index = path.find(id)
                return path[makeup_index + 8 : id_index]
        return ""

    def _get_filepath(self, id: str, dis_type: DisTributeEnum, root: str):
        path_suffix = self._get_path_suffix(id)
        return f"{self._get_file_root(dis_type, root)}{path_suffix}{id}"

    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    views.add(view)
        self.p4_mgr.add_p4_views(list(views))

    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                path_suffix = self._get_path_suffix(id)
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    if distribute_type == DisTributeEnum.CDNRULE:
                        self._submit_ab_views.append(f"{view}")
                        continue
                    dst = self.p4_mgr.parse_p4view_to_localpath(view)
                    src = f"{self.ab_path}art/role/makeup/{path_suffix}{id}"
                    path_mgr.copy(src, dst, overwrite=True)
                    self._submit_ab_views.append(f"{view}")
