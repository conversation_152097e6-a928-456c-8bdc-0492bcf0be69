from typing import List

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config, env_mgr, DisTributeEnum
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr


class ModelPackageMgr(DistributePackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.Model, **kwargs)
        self._execute_method = "H3DBuildTools.BuildArt"
        self._b_path = self.p4_mgr.get_last_branch()
        self._resource_dir_prefix = f"{config.p4_views.get('art_resources_src')}/model"
        self._const_source_p4_views = []
        self._const_ab_p4_views = [
            f"{config.p4_views.get('resmg_root')}/model/",
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/art/model/",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/model/",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for file in source_paths:
            if not file.startswith(self._resource_dir_prefix):
                continue

            if file.lower().endswith(".prefab"):
                depots.add(file)
        log.info(f"filter source paths: {depots}")
        return depots

    def _fill_source_p4_views(self):
        views = set()
        for path in env_mgr.get_source_paths():
            base_path = path.rsplit("/", 2)[0]
            new_path = base_path + "/..."
            views.add(new_path)
        self.p4_mgr.add_p4_views(list(views))

    def _make_resouces_file(self):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                if path.find("art_src/model/") == -1:
                    continue
                asset_path = path.replace(config.p4_views.get("art_resources_src"), "assets/resources/art")
                lines.append(asset_path)
            f.write(",".join(lines))

    def _package(self):
        """
        打包
        """
        self._make_resouces_file()
        source_paths = env_mgr.get_source_paths()
        path = ",".join([path.replace(config.p4_views.get("art_resources_src"), "assets/resources/art") for path in source_paths])
        cmd_line = self._get_package_cmd()
        cmd_line += f"path={path} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _default_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['resources_root'].format(branch='Resources')}",
            f"{config.p4_views['resources_root'].format(branch=f'b/{self._b_path}')}",
        ]

    def _get_file_root(self, dis_type: DisTributeEnum, root: str):
        if dis_type == DisTributeEnum.DEFAULT:
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/model/"
        if dis_type == DisTributeEnum.HOTFIX:
            return f"{root}/client/{self._platform}/assetbundles/art/model/"
        if dis_type == DisTributeEnum.CDNBASE or dis_type == DisTributeEnum.CDNRULE:
            return f"{root}/cooked/{self._platform}/assetbundles/art/model/"
        raise PyframeException(f"Invalid distribute type {dis_type}")

    def _get_path_suffix(self, id: str):
        source_paths = env_mgr.get_source_paths()
        for path in source_paths:
            if id in path:
                model_index = path.find("/model/")
                id_index = path.find(id)
                return path[model_index + 7 : id_index]
        return ""

    def _get_filepath(self, id: str, dis_type: DisTributeEnum, root: str):
        path_suffix = self._get_path_suffix(id)
        return f"{self._get_file_root(dis_type, root)}{path_suffix}"

    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    views.add(view.rsplit("/", 2)[0])
        self.p4_mgr.add_p4_views(list(views))

    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                path_suffix = self._get_path_suffix(id)
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    if distribute_type == DisTributeEnum.CDNBASE or distribute_type == DisTributeEnum.CDNRULE:
                        self._submit_ab_views.extend([f"{view}{id}", f"{view}{id}.h3dmanifest"])
                        continue
                    dst = self.p4_mgr.parse_p4view_to_localpath(view)
                    src = f"{self.ab_path}art/model/{path_suffix}"
                    path_mgr.copy(src, dst, overwrite=True)
                    self._submit_ab_views.extend([f"{view}{id}", f"{view}{id}.h3dmanifest"])
