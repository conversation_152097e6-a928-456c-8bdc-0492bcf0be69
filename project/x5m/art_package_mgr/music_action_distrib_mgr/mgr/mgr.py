import collections
from typing import List

from frame import *

from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr, env_mgr
from project.x5m.art_package_mgr.base_mgr.enum_mgr import DisTributeEnum
from project.x5m.art_package_mgr.music_action_distrib_mgr.mgr import *
from project.x5m.art_package_mgr.music_action_distrib_mgr.mgr.utils import render_source_p4_view_list, render_ab_p4_view_list


class MusicActionDistribMgr(DistributePackageMgr):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(kind=ResourceTypeEnum.MUSIC_ACTION_DISTRIB, **kwargs)
        # 资源拉去到的路径前缀
        self._resource_dir_prefix = f"{config.p4_views.get('art_resources_src')}{DIR_PREFIX}"
        # 打包命令执行的静态方法
        self._execute_method = EXECUTE_METHOD
        # 打包拉取的资源
        self._b_path = self.p4_mgr.get_last_branch()
        self._const_source_p4_views = render_source_p4_view_list()
        self._const_ab_p4_views = render_ab_p4_view_list(self._platform, self._b_path)

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        """
        过滤满足条件的资源路径

        Args:
            source_paths: 资源路径列表
        Returns:
            满足条件的资源路径
        """
        depots = set()
        paths = self._render_p4_view_paths()
        for fp in source_paths:
            # 首先校验是否是对应的资源前缀，如果不满足，则直接跳过
            if not fp.startswith(self._resource_dir_prefix):
                continue
            # 获取路径，并按照规则匹配路径是否满足条件，如果满足条件追加到记录中
            if fp.startswith((paths["avatar_weapon_actions_anim"], paths["avatar_weapon_actions_anim_expression"], paths["weapon_actions_anim"])):
                depots.add(f"{os.path.dirname()}/...")

        return depots

    def _render_p4_view_paths(self):
        """渲染路径"""
        # 渲染路径
        def _render_p4_view_path(subfix_name: str) -> str:
            return f"{self._resource_dir_prefix.rstrip('/')}/{subfix_name}".replace("\\", "/")

        return {
            "avatar_weapon_actions_anim": _render_p4_view_path("avatar_weapon_actions_anim"),
            "avatar_weapon_actions_anim_expression": _render_p4_view_path("avatar_weapon_actions_anim_expression"),
            "weapon_actions_anim": _render_p4_view_path("weapon_actions_anim"),
        }

    def _default_distribute(self) -> List[str]:
        return [config.p4_views["cdn_root"]]

    def _make_resouces_file(self):
        """组装打包命令需要的文件路径"""
        super()._make_resouces_file(p4_view_type="art_resources_src")

    def _get_distruibute_dict(self):
        """获取分发路径"""
        success_ids = env_mgr.get_report().get("success_ids")
        dist_destinations = collections.defaultdict(list)
        for id in success_ids:
            for distribute in self._distribute_config:
                ret = distribute.match(self._parse_id_to_filename(id))
                if ret:
                    # 根据不同分发类型进行判断路径
                    if distribute.distribute_type in [DisTributeEnum.CDNBASE, DisTributeEnum.CDNRULE, DisTributeEnum.HOTFIX]:
                        self._distribute_map[distribute.distribute_type].append((id, [ret]))
                    else:
                        other_base_distribute = [
                            f"{config.p4_views['resources_root'].format(branch='Resources')}",
                            f"{config.p4_views['resources_root'].format(branch=f'b/{self._b_path}')}",
                        ]
                        self._distribute_map[distribute.distribute_type].append((id, other_base_distribute))
                    # special 处理
                    if distribute.distribute_type == DisTributeEnum.SPECIAL:
                        dist_destinations[f"{str(distribute.distribute_type)}({ret})"].append(id)
                        self._is_special_branch = True
                    else:
                        dist_destinations[str(distribute.distribute_type)].append(id)
                    break
            else:
                self._distribute_map[DisTributeEnum.DEFAULT].append((id, self._default_distribute()))
                dist_destinations[str(DisTributeEnum.DEFAULT)].append(id)
        log.info(f"dist_destinations: {dist_destinations}")
        env_mgr.set_dist_destinations(dist_destinations)
        return self._distribute_map
