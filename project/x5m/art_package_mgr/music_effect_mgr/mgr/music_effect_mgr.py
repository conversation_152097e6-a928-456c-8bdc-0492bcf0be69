import base64
import re
import requests
from pathlib import Path
from typing import Optional, Tuple

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import (
    PackageMgr,
    ResourceTypeEnum,
    env_mgr,
    config,
    path_mgr,
    PipelineUpdateTypeEum,
    PlatformEnum,
)


class MusicEffectMgr(PackageMgr):
    def __init__(self, **kwargs):
        super().__init__(ResourceTypeEnum.MUSIC_EFFECT, **kwargs)
        self._execute_method = "H3DBuildTools.BuildAudio"
        self._git_config = config.GITLAB_MAINTAINER
        self._git_headers = {"PRIVATE-TOKEN": self._git_config.get("token")}
        self._success_pattern = re.compile(r"^【Info】.*$|[/]([^/]+)\n$")
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f4454687-a463-4b06-bfb1-94d85987cb29"

    def check_package(self, stop_current_build: bool = True, force_by_id: bool = True) -> bool:
        self._get_commit_id()
        self._download_commit_file()

    def _get_commit_id(self):
        """
        根据pipeline_id获取commit_id信息
        """
        pipeline_id = env_mgr.get_pipeline_id()
        if not pipeline_id:
            raise PyframeException("请检查参数PIPELINE_ID填写是否正确")

        # 通过gitlab API 获取pipelines信息，从返回结果中获取commit_id
        url = self._git_config.get("url") + "/api/v4/projects/52/pipelines/{}".format(pipeline_id)
        try:
            resp = requests.get(url=url, headers=self._git_headers)
            pipeline_info = resp.json()
        except Exception as e:
            log.error(e)
            raise PyframeException("获取音效提交信息失败，请检查gitlab代码服务是否正常后，再重试")
        commit_id = pipeline_info.get("sha", "")
        log.info("commit_id: {}".format(commit_id))
        if not commit_id:
            raise PyframeException("获取commit_id信息失败，请联系技术")
        env_mgr.set_commit_id(commit_id)

    def __is_effect_commit_file(self, file_path: str) -> bool:
        """
        判断提交的文件是否是音效文件
        Args:
            file_path: 文件路径
        Returns:
            True: 是音效文件
            False: 不是音效文件
        """
        # 判断文件是否是音效文件
        if file_path.startswith("ui_sound_effect/") and (file_path.endswith(".wav") or file_path.endswith(".mp3")):
            return True
        else:
            return False

    def __get_commit_file(self) -> list:
        """
        根据commit_id信息获取提交的文件路径
        """
        commit_id = env_mgr.get_commit_id()
        if not commit_id:
            raise PyframeException("请检查环境变量COMMIT_ID是否正常保存")
        url = self._git_config.get("url") + "/api/v4/projects/52/repository/commits/{}/diff?per_page=200".format(commit_id)
        try:
            resp = requests.get(url=url, headers=self._git_headers)
            diff_info = resp.json()
        except Exception as e:
            log.error(e)
            raise PyframeException("获取音效提交信息失败，请检查gitlab代码服务是否正常后，再重试")
        if not diff_info:
            raise PyframeException("获取此次:【{}】提交的文件内容有误，请检查参数PIPELINE_ID填写是否正确".format(commit_id))
        return diff_info

    def __save_commit_file(self, filename: str, content_str: str) -> bool:
        """
        保存二进制内容为文件
        Args:
            filename: 文件名
            content_str: 二进制内容
        """
        try:
            # 判断文件filename 父目录是否存在，不存在则创建
            dirname = os.path.dirname(filename)
            if not path_mgr.exists(dirname):
                path_mgr.mkdir(dirname)
            log.info("正在解码...")
            content_bytes = base64.b64decode(content_str)
            log.info("解码成功...")
            log.info("正在写入文件: {}...".format(filename))
            with open(filename, "wb") as f:
                f.write(content_bytes)
            return True
        except Exception as e:
            log.error("保存二进制内容为文件失败", e)
            return False

    def _download_commit_file(self):
        # 获取所有需要下载的文件
        file_paths = self.__get_commit_file()
        if not file_paths:
            raise PyframeException("获取的文件列表为空，请检查参数PIPELINE_ID填写是否正确")

        # 下载前，如果目录不存在，先进行创建，如果存在，先删除原来旧的文件
        dirname = os.path.join(self._workspace, "ui_sound_effect")
        if not Path(dirname).exists():
            os.mkdir(dirname)
        else:
            self._safe_clean(dirname, "清理旧音频文件")
            os.mkdir(dirname)

        # 记录下的载文件
        download_commit_files = []
        # 下载到指定目录
        for file_path in file_paths:
            new_path = file_path.get("new_path")
            # 判断文件是否是音效文件 如果不是，跳过
            if not self.__is_effect_commit_file(new_path):
                continue
            new_path = new_path.replace("/", "%2F")
            url = self._git_config.get("url") + "/api/v4/projects/52/repository/files/{}?ref=audio".format(new_path)
            log.info("url: {}".format(url))
            try:
                resp = requests.get(url=url, headers=self._git_headers)
                effect_file = resp.json()
            except Exception as e:
                log.error(e)
                raise PyframeException("下载音效文件失败，请检查gitlab代码服务是否正常后，再重试")

            # 组织文件保存路径
            file_name = effect_file.get("file_name")
            if not file_name:
                raise PyframeException(f"获取文件失败，请确认文件{new_path}是否存在，或者稍后重试")
            file_path = effect_file.get("file_path")
            filename = os.path.join(self._workspace, file_path)

            # 获取文件base64 encode之后的字符串并保存成文件
            content = effect_file.get("content")
            if not self.__save_commit_file(filename, content):
                raise PyframeException("文件: {} 下载失败，请稍后重试".format(file_name))
            download_commit_files.append(filename)

        if not download_commit_files:
            raise PyframeException("没有音效文件需要下载，请检查参数PIPELINE_ID填写是否正确")

        # 记录下载的文件名称
        download_commit_files = [os.path.basename(f) for f in download_commit_files]
        env_mgr.set_download_files(download_commit_files)
        env_mgr.set_need_package(True)

    def update_git_and_p4(self) -> None:
        """
        更新git和p4
        """
        if not env_mgr.get_need_package():
            return
        self._clean_resource()
        self._update_git()
        self._copy_commit_file()
        self._update_p4()

    def _copy_commit_file(self):
        """
        将下载好的音效资源拷贝到客户端（arttrunk项目）目录
        """
        src_dir = os.path.join(self._workspace, "ui_sound_effect")

        # 复制前先检查目标目录，如果不存在先创建，如果存在，删除旧内容
        dst_dir = os.path.join(self._workspace, f"arttrunk/{self._arttrunk_branch}/mobile_dancer/arttrunk/client/Assets/resources/audio")
        self._safe_clean(dst_dir, "删除旧音频文件")

        dst_dir = os.path.join(dst_dir, "ui_sound_effect")
        log.info("src_dir: {} dst_dir: {}".format(src_dir, dst_dir))
        path_mgr.xcopy(src_dir, dst_dir, dst_is_file=False)

    def __get_p4_view(self):
        """
        P4的ab view
        """
        update_type = env_mgr.get_trunk_branch_weekupdate()
        log.info("update_type: {}".format(update_type))

        if not update_type:
            raise PyframeException("请检查更新方式参数TRUNK_BRANCH_WEEKUPDATE填写是否正确")
        # 如果是debug模式的话直接将所有内容指定到一个具体的测试目录
        # if env_mgr.is_dev():
        #     return f"//x5_mobile/mr/art_release_test/{self._platform}/assetbundles/audio/..."

        if update_type == PipelineUpdateTypeEum.TRUNK.value:
            ab_view = f"//x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/audio/..."
        elif update_type == PipelineUpdateTypeEum.BRANCH.value:
            branch = env_mgr.get_sound_bound_ver()
            if not branch:
                raise PyframeException("请检查参数SOUND_BOUND_VER是否填写正确")
            ab_view = f"//x5_mobile/mr/b/{branch}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/audio/..."
        elif update_type == PipelineUpdateTypeEum.WEEK_UPDATE.value:
            branch = env_mgr.get_sound_bound_ver()
            if not branch:
                raise PyframeException("请检查参数SOUND_BOUND_VER是否填写正确")
            week_update = env_mgr.get_sound_res_ver()
            if not week_update:
                raise PyframeException("请检查参数SOUND_RES_VER是否填写正确")
            ab_view = f"//x5_mobile/mr/onlineupdate/{branch}/{week_update}/client/{self._platform}/assetbundles/audio/..."
        else:
            raise PyframeException("请检查更新方式参数TRUNK_BRANCH_WEEKUPDATE填写是否正确")
        return ab_view

    def _update_p4(self) -> None:
        """
        更新p4
        """
        self._fill_const_ab_p4_views()
        self.p4_mgr.sync_all(force=env_mgr.get_force())

    def _fill_const_ab_p4_views(self):
        """
        填充常量p4view
        """
        ab_view = self.__get_p4_view()
        self.p4_mgr.set_p4_views([ab_view])

    def package(self):
        if not env_mgr.get_need_package():
            return
        self._input_check()
        self._package()
        self._check_package_result()
        self._calculate_package_result()

    def __get_package_result(self):
        """
        通过读取日志获取打包结果
        """
        # 读取日志，从日志中匹配Bundle Name关键字
        with open(self._log_file, "r", encoding="utf-8") as f:
            log_content = f.read()
        # 匹配关键字
        pattern = re.compile(r"Bundle Name: (.*)")
        result = pattern.findall(log_content)
        return result

    def _check_package_result(self):
        """
        检查打包结果
        """
        # 需要打包的资源
        # AB资源
        # 对比结果

        download_files = env_mgr.get_download_files()
        package_result = self.__get_package_result()
        for download_file in download_files:
            download_file = download_file.split(".")[0]
            log.info(f"download_file: {download_file}")
            if download_file.lower() in package_result:
                log.info("文件:{}打包成功".format(download_file))
            else:
                raise PyframeException("未找到文件:{}对应的ab资源，请检查打包日志查看详情".format(download_file))

    def _get_submit_p4_view_ab(self):
        """
        提交p4,主要是ab包，提交改到文件级别
        """
        files = set()
        ab_view = self.__get_p4_view()
        local_ab_path = self.p4_mgr.parse_p4view_to_localpath(ab_view)
        path_mgr.xcopy(self.ab_path, local_ab_path, dst_is_file=False)
        for file_path in Path(self.ab_path).rglob("*"):
            if file_path.is_file():
                log.info(f"ab file: {file_path}")
                audio_index = str(file_path).find("audio")
                asset_file = str(file_path)[audio_index + 6 :]
                log.info(f"asset_file: {asset_file}")
                file = f"{ab_view.replace('...', '')}{asset_file}".replace("\\", "/")
                files.add(file)

        return list(files)

    def get_msg(self, pipeline_status: Optional[str] = None) -> Tuple:
        msg = ""
        report_detail = env_mgr.get_report(PlatformEnum.IOS.value)
        if report_detail:
            msg += f"**输出版本**: {env_mgr.get_trunk_branch_weekupdate()}\n"
            msg += f"**音效git版本**: {env_mgr.get_pipeline_id()}\n"
            msg += f"**Android音效P4提交号**: {env_mgr.get_submit_changelist(PlatformEnum.ANDROID.value)}\n"
            msg += f"**Android音效P4提交号**: {env_mgr.get_submit_changelist(PlatformEnum.IOS.value)}\n"
        submitters = env_mgr.get_users()
        if submitters:
            submitters = list(set(submitters))
            submitters_str = ",".join(submitters)
            msg += f"**提交人:** {submitters_str}\n"
        return msg
