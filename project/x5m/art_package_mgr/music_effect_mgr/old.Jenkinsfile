import groovy.json.JsonSlurper

node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = "音效打包-美术"
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

def getParams() {
    node {
        def resp = powershell(returnStdout: true, script: '''
            $response = Invoke-WebRequest -Uri "http://**************:21935/api/apigw-user/v3/projects/dgm/pipelines/p-1c49b124300c4313ae47ea39646ffb4d" -Headers @{"X-DEVOPS-UID"="<EMAIL>"; "Content-Type"="application/json"} -UseBasicParsing
            $utf8Encoding = [System.Text.Encoding]::UTF8
            $bytes = [System.Text.Encoding]::Default.GetBytes($response.Content)
            $utf8String = $utf8Encoding.GetString($bytes)
            $utf8String
        ''').trim()
        def jsonSlurper = new JsonSlurper()
        try {
            def respJson = jsonSlurper.parseText(resp)

            // 检查解析后的JSON结构
            if (respJson?.data) {
                def params = respJson.data.stages[0].containers[0].params
                echo "respJson params: ${params}"
                def soundResVerOptions = params.findAll { it.id == "SOUND_RES_VER" }.collect { it.options }.flatten().collect { it.key }
                def soundBoundVerOptions = params.findAll { it.id == "SOUND_BOUND_VER" }.collect { it.options }.flatten().collect { it.key }
                // 默认为空
                soundResVerOptions = [''] + soundResVerOptions
                return [soundResVerOptions, soundBoundVerOptions]
            } else {
                echo "respJson does not contain 'data' property"
                return [[], []]
            }
        } catch (Exception e) {
            echo "Failed to parse JSON: ${e.getMessage()}"
            return [[], []]
        }
    }
}

pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder(logRotator(numToKeepStr: env.numsToKeepStr, daysToKeepStr: env.daysToKeepStr))
    }
    parameters {
        choice(name: 'trunk_branch_weekupdate', choices: ['主支', '分支', '周更'], description: '主支或分支或周更，选择其一（默认主支）')
        string(name: 'pipeline_id', defaultValue: '', description: 'git提交时Pipline的值(必填，eg:8098)')
        choice(name: 'sound_bound_ver', choices: getParams()[1], description: '分支版本(eg: 4.06.0,trunk,默认trunk)')
        choice(name: 'sound_res_ver', choices: getParams()[0], description: '周更版本(eg:4062.401000)（周更必填）')
        booleanParam(name: 'dev', defaultValue: true, description: '是否为开发模式')
    }
    environment {
        PIPELINE_PYTHON = 'python3'
        RESOURCE = 'music_effect'
    }

    stages {
        stage('美术资源打包'){
            matrix {
                agent {
                    node {
                        label "art_package_music_${platform}"
                        customWorkspace "${env.workspace}/.."
                    }
                }
                axes {
                    axis {
                        name "PLATFORM"
                        values "android","ios"
                    }
                }
                stages {
                    stage("更新流水线依赖") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q")
                                }
                            }
                        }
                    }
                    stage("准备") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=prepare")
                                }
                            }
                        }
                    }
                    stage("检查是否需要打包") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=check_package")
                                }
                            }
                        }
                    }
                    stage("获取P4资源") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=update_git_and_p4")
                                }
                            }
                        }
                    }
                    stage("调用打包命令") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=package")
                                }
                            }
                        }
                    }
                    stage("提交产物") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=submit")
                                }
                            }
                        }
                    }
                }
                post {
                    always {
                        dir("pyframe-pipeline"){
                            script {
                                bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_always")
                            }
                        }
                    }
                }
            }
        }
    }
    post {
        unstable {
            node ("art_package_music_ios"){
                // 默认路径为 {slave workspace}/workspace/job_name  所以需要加两个 /..
                withEnv(["WORKSPACE=${env.WORKSPACE}/../.."]){
                    dir("${env.workspace}/pyframe-pipeline"){
                        script {
                            bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_unstable")
                        }
                    }
                }
            }
        }
        success {
            node ("art_package_music_ios"){
                withEnv(["WORKSPACE=${env.WORKSPACE}/../.."]){
                    dir("${env.workspace}/pyframe-pipeline"){
                        script {
                            bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_success")
                        }
                    }
                }
            }
        }
        failure {
            node ("art_package_music_ios"){
                withEnv(["WORKSPACE=${env.WORKSPACE}/../.."]){
                    dir("${env.workspace}/pyframe-pipeline"){
                        script {
                            bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_failure")
                        }
                    }
                }
            }
        }
    }
}