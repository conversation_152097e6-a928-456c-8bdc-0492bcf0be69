import base64
import logging
import os
import shutil
import typing
from itertools import chain
from urllib.parse import urljoin

import requests

from project.x5m.base_mgr.base_mgr import BaseWorkflow
from project.x5m.base_mgr.env_mgr import env_mgr
from project.x5m.base_mgr.git_mgr import X5mGitMgr

DIR = os.path.dirname(__file__)
REPO = os.path.dirname(DIR)


class GitlabUtil:
    GITLAB_HOST = "https://x5mobile-gitlab.h3d.com.cn/"
    HEADERS = {"PRIVATE-TOKEN": "o-yNgwVADiQ8CicYdt_4"}

    @classmethod
    def get_diff_info(cls, commit_id: str) -> typing.List:
        path = cls.GITLAB_HOST + "api/v4/projects/52/repository/commits/{}/diff".format(commit_id)
        # 最大页只能是100个，故需要循环获取.
        result = []
        page = '1'
        while page:
            resp = cls.request('get', path, params={'per_page': 100, 'page': page})
            res_headers = resp.headers
            diff_info = resp.json()
            result.extend([i['new_path'] for i in diff_info if (i and i.get('new_path') and i.get('deleted_file') != True)])
            page = res_headers['X-Next-Page']
        return result

    @classmethod
    def request(cls, method, path, **kwargs):
        if not 'headers' in kwargs:
            kwargs['headers'] = cls.HEADERS
        else:
            kwargs.update({'headers': cls.HEADERS})
        url = urljoin(cls.GITLAB_HOST, path)
        return requests.request(method, url, **kwargs)

    @classmethod
    def download_commit_file(cls, file_paths, workspace, ref: str = 'audio'):
        # 下载前，如果目录不存在，先进行创建，如果存在，先删除原来旧的文件
        dirname = os.path.join(workspace, "ui_sound_effect")
        if not os.path.exists(dirname):
            os.makedirs(dirname, exist_ok=True)
        else:
            shutil.rmtree(dirname)
            os.makedirs(dirname, exist_ok=True)
        # 记录下的载文件
        download_commit_files = []
        # 下载到指定目录
        for new_path in file_paths:
            # 判断文件是否是音效文件 如果不是，跳过
            if not new_path:
                continue
            new_path = new_path.replace("/", "%2F")
            path = f"api/v4/projects/52/repository/files/{new_path}?ref={ref}"
            resp = cls.request('get', path, params={'ref': ref})
            effect_file = resp.json()
            # 组织文件保存路径
            file_name = effect_file.get("file_name")
            if not file_name:
                raise ValueError(f"获取文件失败，请确认文件{new_path}是否存在，或者稍后重试")
            file_path = effect_file.get("file_path")
            filename = os.path.join(workspace, file_path)

            # 获取文件base64 encode之后的字符串并保存成文件
            content = effect_file.get("content")
            if not cls.save_commit_file(filename, content):
                raise ValueError("文件: {} 下载失败，请稍后重试".format(file_name))
            download_commit_files.append(filename)

        if not download_commit_files:
            raise ValueError("没有音效文件需要下载，请检查参数PIPELINE_ID填写是否正确")

        # 记录下载的文件名称
        download_commit_base_files = [os.path.basename(f) for f in download_commit_files]
        env_mgr.set_download_files(download_commit_base_files)
        return download_commit_files

    @classmethod
    def get_commit_id(cls, pipeline_id):
        """根据pipeline_id获取commit_id信息.
        """
        # 通过gitlab API 获取pipelines信息，从返回结果中获取commit_id
        path = "api/v4/projects/52/pipelines/{}".format(pipeline_id)
        resp = cls.request('get', path)
        pipeline_info = resp.json()
        commit_id = pipeline_info.get("sha", "")
        return commit_id, pipeline_info.get('user', {}).get('username', "")

    @classmethod
    def save_commit_file(cls, filename: str, content_str: str) -> bool:
        """
        保存二进制内容为文件
        """
        try:
            # 判断文件filename 父目录是否存在，不存在则创建
            dirname = os.path.dirname(filename)
            if not os.path.exists(dirname):
                os.makedirs(dirname, exist_ok=True)
            content_bytes = base64.b64decode(content_str)
            with open(filename, "wb") as f:
                f.write(content_bytes)
            return True
        except Exception as e:
            logging.error(e)
            return False


class MusicEffect(BaseWorkflow):
    kind = "music_effect"
    name = "音乐效果"
    resource_path = ""
    # 打包大群
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"
    default_changelist = 413872
    execute_method = "H3DBuildTools.BuildAudio"
    gitlab_util = GitlabUtil
    git_branch = "audio"

    def workflow(self):
        return (
            ("展示系统信息", self.prepare),
            ("获取打包工具repo", self.update_or_clone_repo),
            ("判断是否需要打包", self.check_package),
            ("清理上次打包资源", self.clean_resource),
            ("获取P4资源", self.get_p4_resource),
            ("准备打包资源", self.prepare_resource),
            ("打包", self.package),
            ("计算打包结果", self.calculate_package_result),
            ("配置需要提交的文件", self.get_need_submit_files),
            ("复制包到P4路径", self.copy_package_to_p4_path),
            ("提交文件", self.submit_files_to_p4),
            ("持久化配置", self.save_result),
            ("备份+上传", self.always),
        )

    class P4Setting(BaseWorkflow.P4Setting):

        @classmethod
        def copy_split(cls):
            return 'resources'

    @property
    def resource_dst_path(self):
        return f"audio/ui_sound_effect"

    def update_or_clone_repo(self, *args, **kwargs) -> dict:
        grm_arttrunk = X5mGitMgr(self.workspace, "arttrunk", "release")
        grm_arttrunk.update(clean=True, commit_id="origin/release", depth=1)

    def check_package(self, changelist: str = "", force_ids=(), *args, **kwargs) -> dict:
        """检查是否需要打包"""
        changelist = changelist or self.changelist
        if not changelist:
            result = {"package": False, "changelist": '', "files": []}
            gitlabActionType = os.environ.get('GITLABACTIONTYPE')
            if not gitlabActionType:
                return result
            gitlabBranch = os.environ.get('GITLABBRANCH')
            if gitlabBranch not in ["audio", "audio_tc"]:
                return result
            commit_id = os.environ.get('SHA') or os.environ.get('GITLABAFTER')
            username = os.environ.get('GITLABUSERNAME')
        else:
            commit_id, username = self.gitlab_util.get_commit_id(changelist)
        if not commit_id:
            raise ValueError("为获取到commit_id")

        files = self.gitlab_util.get_diff_info(commit_id)
        logging.info("get files: {} from commit_id: {} by{}".format(files, commit_id, username))
        # 判断指定路径下的资源是否有更新
        files = self._filter_source_paths(files)
        if not files:
            info = f"{self.platform}平台{self.kind}资源无更新，无需打包"
            logging.info(info)
            return {"package": False, "changelist": changelist, "files": []}
        files = list(files)
        logging.info(
            f"{self._platform}平台{self.kind}资源有更新，需要打包; changelist:{changelist}"
        )
        if not username.endswith('@h3d.com.cn'):
            username = F"{username}@h3d.com.cn"
        env_mgr.set_source_paths(source_paths=files)
        env_mgr.update_users(users=list([username]))
        env_mgr.set_changelist(changelist=commit_id)
        env_mgr.set_need_package(True, self.platform)
        return {"package": True, "changelist": commit_id, "files": files}

    def _filter_source_paths(self, depot_files: typing.List[str]) -> set:
        paths = set()
        for file in depot_files:
            if not file.startswith('ui_sound_effect'):
                continue
            _, ext = os.path.splitext(file)
            if not ext in ['.wav', '.mp3']:
                continue
            paths.add(file)
        return paths

    def transform_p4_to_art_trunk_path(self, local_path: str, auto_full_path=False):
        _, target = local_path.split(self.workspace)
        if target.endswith("/"):
            target = target[:-1]
        if target.startswith('\\') or target.startswith('/'):
            target = target[1:]
        dst_part = f"Assets/resources/audio/{target}"
        if auto_full_path:
            client_path = self._project_path.lower().replace("\\", "/")
            return f"{client_path}/{dst_part}"
        return dst_part

    def prepare_resource(
        self,
        depot_files: typing.Iterable[str] = (),
        depot_bind_files: typing.Iterable[str] = (),
        *args,
        **kwargs,
    ):
        if not depot_files and self.ins_id:
            depot_files = self.runtime_conf["get_p4_resource"]["output"]["depot_files"]
        if not depot_bind_files and self.ins_id:
            depot_bind_files = self.runtime_conf["get_p4_resource"]["output"]["depot_bind_files"]

        depot_dirs = self.gitlab_util.download_commit_file(
            chain(depot_files, depot_bind_files),
            workspace=self.workspace, ref=self.git_branch
        )
        dst_paths = set()
        for local_path in depot_dirs:
            client_path = self._project_path.lower().replace("\\", "/")
            dst_part = self.transform_p4_to_art_trunk_path(local_path)
            dst = f"{client_path}/{dst_part}"
            dst = self.copy_local_p4_to_dst(local_path, dst)
            dst and dst_paths.add(dst)

        return {"depot_files": depot_files, "dst_paths": list(dst_paths)}

    def _make_resouces_file(self, source_paths: typing.List[str] = None):
        return ""
