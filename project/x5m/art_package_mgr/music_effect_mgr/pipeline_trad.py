import os
import re
from .pipeline import MusicEffect

DIR = os.path.dirname(__file__)
REPO = os.path.dirname(DIR)


class MusicEffectTrad(MusicEffect):
    kind = "music_effect_trad"
    name = "音乐效果-繁体"
    resource_path = ""
    branch_version_ptn = re.compile("(\d+)\.(\d+).(\d+)_tc$")


    default_changelist = 281461
    git_branch = 'audio_tc'

    class P4Setting(MusicEffect.P4Setting):
        master_prefix_base = "x5_mobile/mr_trad/Resources/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles"
        branch_prefix_base = "x5_mobile/mr_trad/b/{branch}/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles"
        online_prefix_base = "x5_mobile/mr_trad/onlineupdate/{version}/{sub_version}/client/{platform}/assetbundles"
        cdn_prefix_base = "x5m/res_trad/cdn/cooked/{platform}/assetbundles"

        @classmethod
        def copy_split(cls):
            return 'resources'

    def workflow(self):
        return (
            ("展示系统信息", self.prepare),
            ("获取打包工具repo", self.update_or_clone_repo),
            ("判断是否需要打包", self.check_package),
            ("清理上次打包资源", self.clean_resource),
            ("获取P4资源", self.get_p4_resource),
            ("准备打包资源", self.prepare_resource),
            ("打包", self.package),
            ("计算打包结果", self.calculate_package_result),
            ("配置需要提交的文件", self.get_need_submit_files),
            ("复制包到P4路径", self.copy_package_to_p4_path),
            ("提交文件", self.submit_files_to_p4),
            ("持久化配置", self.save_result),
            ("备份+上传", self.always),
        )
