import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.json.JsonSlurperClassic

node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = "${BUILD_USER}"
        env.BUILD_ID = "${BUILD_ID}"
        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
        try {
            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
        } catch (exc) {
            echo '获取不到构建的用户邮箱'
            env.BUILD_USER_EMAIL = ''
        }
    }
}


def json_load(raw_out) {
    println raw_out
    def out = raw_out.trim().split('\n')
    out = out.last()
    return new JsonSlurperClassic().parseText(out)
}

// 用于存储platforminfo
PLATFORM_INFO = [:]

pipeline {
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr)
        // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
        string(name: 'changelist', defaultValue: '', description: 'git提交时Pipline的值(必填，eg:8098)')
    }
    environment {
        PYTHONIOENCODING = 'utf-8'
        KIND="music_effect_trad"
        PYTHON = "pipeline-python"
    }

//     因为五点到六点半p4需要备份数据，流水线会执行失败
//     triggers {
//         cron('H/15 0-4,8-23 * * *')
//     }
    stages {
        stage('音效打包') {
            matrix {
                agent {
                    node {
                        label "music_effect_trad_${PLATFORM}"
                        customWorkspace "music_effect_trad/${PLATFORM}"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'ios', 'android'
                    }
                }
                environment {
                    WS = "${env.WORKSPACE.replace("\\", "/").toLowerCase()}"
                }
                stages {
                    stage('更新流水线依赖') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    PLATFORM_INFO[PLATFORM] = [:]
                                    PLATFORM_INFO[PLATFORM].WORKSPACE = env.WORKSPACE
                                    PLATFORM_INFO[PLATFORM].NODE_NAME = env.NODE_NAME
                                    bat(
                                            script: "\"$PYTHON\" -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn",
                                            returnStdout: true
                                    )
                                    bat(
                                            script: "\"$PYTHON\" -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn",
                                            returnStdout: true
                                    )
                                }
                            }
                        }
                    }
                    stage("获取处理任务ID") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    def data = [
                                            workspace: env.WORKSPACE,
                                            platform: PLATFORM
                                    ]
                                    def out = powershell(script: "cmd /c 'echo ${JsonOutput.toJson(data)} |\"${PYTHON}\" x5m_cli.py --kind=$KIND --method=get_ins_id'",
                                                         encoding:'utf8',
                                                         returnStdout: true)
                                    out = out.trim().split('\n')
                                    PLATFORM_INFO[PLATFORM].INS_ID = new JsonSlurper().parseText(out.last())
                                }
                            }
                        }
                    }

                    stage('展示系统信息') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID
                                    ]
                                    powershell(
                                        script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=prepare'",
                                        encoding: 'utf8'
                                    )                                }
                            }
                        }
                    }
                    stage('获取打包工具repo') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=update_or_clone_repo'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                }
                            }
                        }
                    }
                    stage('判断是否需要打包') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                            changelist: params.changelist
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=check_package'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    def package_info = json_load(output)
                                    if (package_info['package'] in ['false', false]){
                                        currentBuild.result = 'ABORTED'
                                        throw new org.jenkinsci.plugins.workflow.steps.FlowInterruptedException(hudson.model.Result.ABORTED)
                                    }
                                    PLATFORM_INFO[PLATFORM].PACKAGE_INFO = package_info

                                }
                            }
                        }
                    }
                    stage("清理上次打包资源") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=clean_resource'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                }
                            }
                        }
                    }
                    stage("获取P4资源") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    data << PLATFORM_INFO[PLATFORM].PACKAGE_INFO
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=get_p4_resource'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].P4_RESOURCE = json_load(output)
                                }
                            }
                        }
                    }
                    stage("准备打包资源") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    data << PLATFORM_INFO[PLATFORM].P4_RESOURCE
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=prepare_resource'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].PACKAGE_RESOURCE = json_load(output)
                                }
                            }
                        }
                    }
                    stage("打包") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=package'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].PACKAGE_RESULT = json_load(output)
                                }
                            }
                        }
                    }
                    stage("计算打包结果") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    data << PLATFORM_INFO[PLATFORM].PACKAGE_RESULT
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=calculate_package_result'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].PACKAGE_DETAIL = json_load(output)
                                }
                            }
                        }
                    }
                    stage("配置需要提交的文件") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=get_need_submit_files'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].NEED_SUBMIT_FILES = json_load(output)
                                }
                            }
                        }
                    }
                    stage("复制包到P4路径") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=copy_package_to_p4_path'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].NEED_SUBMIT_FILES = json_load(output)
                                }
                            }
                        }
                    }
                    stage("提交文件") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=submit_files_to_p4'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].SUBMIT_INFO = json_load(output)
                                }
                            }
                        }
                    }
                    stage("持久化配置") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=save_result'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].PERSISTENCE_INFO = json_load(output)
                                }
                            }
                        }
                    }

                }
                post {
                    always {
                        dir("pyframe-pipeline") {
                            script {
                                def data = [
                                        ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                ]
                                def output = powershell(
                                        script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=always'",
                                        encoding: 'utf8',
                                        returnStdout: true
                                )
                            }
                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            node(PLATFORM_INFO['ios'].NODE_NAME) {
                dir("${PLATFORM_INFO['ios'].WORKSPACE}\\pyframe-pipeline") {
                     script {
                        bat(script: "\"$PYTHON\" x5m_cli.py --kind=$KIND --method=on_failure ",
                            encoding: 'utf8'
                            )
                    }
                }
            }

        }
        success {
            node(PLATFORM_INFO['ios'].NODE_NAME) {
                dir("${PLATFORM_INFO['ios'].WORKSPACE}\\pyframe-pipeline") {
                     script {
                        bat(script: "\"$PYTHON\" x5m_cli.py --kind=$KIND --method=on_success ",
                            encoding: 'utf8'
                            )
                    }
                }
            }
        }
        unstable {
            node(PLATFORM_INFO['ios'].NODE_NAME) {
                dir("${PLATFORM_INFO['ios'].WORKSPACE}\\pyframe-pipeline") {
                     script {
                        bat(script: "\"$PYTHON\" x5m_cli.py --kind=$KIND --method=on_unstable ",
                            encoding: 'utf8'
                            )
                    }
                }
            }
        }
    }
}
