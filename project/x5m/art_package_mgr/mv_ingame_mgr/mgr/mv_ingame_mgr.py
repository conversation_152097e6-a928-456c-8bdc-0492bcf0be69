import re
import collections
from typing import List

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config, env_mgr, DisTributeEnum
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr


class MvIngameMgr(DistributePackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.INGAME_PREFABS, **kwargs)
        self._resource_dir_prefix = f"{config.p4_views.get('art_resource_root')}/ingame_prefabs"
        self._b_path = self.p4_mgr.get_last_branch()
        self._const_source_p4_views = [
            f"{config.p4_views.get('art_resource_root')}/ingame_prefabs/",
            f"{config.p4_views.get('resmg_root')}/ingame_prefabs/",
        ]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/art/ingame_prefabs/",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/ingame_prefabs/",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s.*?ingame_prefabs.*?(\d+$|\d+_extend\d$|\w+$)")

    def _get_distruibute_dict(self):
        success_ids = env_mgr.get_report().get("success_ids")
        # NOTE: 去除掉 `shaders`
        success_ids = [id for id in success_ids if "shaders" != id]
        dist_destinations = collections.defaultdict(list)
        for id in success_ids:
            if len(id) <= 1:
                continue
            for distribute in self._distribute_config:
                ret = distribute.match(self._parse_id_to_filename(id))
                if ret:
                    self._distribute_map[distribute.distribute_type].append((id, [ret]))
                    if distribute.distribute_type == DisTributeEnum.SPECIAL:
                        dist_destinations[f"{str(distribute.distribute_type)}({ret})"].append(id)
                        break
                    else:
                        dist_destinations[str(distribute.distribute_type)].append(id)
            else:
                self._distribute_map[DisTributeEnum.DEFAULT].append((id, self._default_distribute()))
                dist_destinations[str(DisTributeEnum.DEFAULT)].append(id)
        env_mgr.set_dist_destinations(dist_destinations)
        return self._distribute_map

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for file in source_paths:
            if not file.startswith(self._resource_dir_prefix):
                continue
            if file.lower().endswith(".anim") or file.lower().endswith(".prefab"):
                depots.add(file)
        return depots

    def _fill_source_p4_views(self):
        pass

    def _make_resouces_file(self):
        pass

    def _package(self):
        """
        打包
        """
        self._make_resouces_file()
        source_paths = env_mgr.get_source_paths()
        path = ",".join([path.replace(config.p4_views.get("art_resource_root"), "assets/resources/art") for path in source_paths])
        cmd_line = self._get_package_cmd()
        cmd_line += f"path={path} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _default_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['resources_root'].format(branch='Resources')}",
            f"{config.p4_views['resources_root'].format(branch=f'b/{self._b_path}')}",
        ]

    def _get_file_root(self, dis_type: DisTributeEnum, root: str):
        if dis_type == DisTributeEnum.DEFAULT:
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/ingame_prefabs/"
        if dis_type == DisTributeEnum.SPECIAL:
            return f"{config.p4_views.get('resources_root').format(branch='b')}/{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/ingame_prefabs/"
        raise PyframeException(f"Invalid distribute type {dis_type}")

    def _get_path_suffix(self, id: str):
        source_paths = env_mgr.get_source_paths()
        for path in source_paths:
            if id in path:
                ingame_index = path.find("/ingame_prefabs/")
                id_index = path.find(id)
                return path[ingame_index + 16 : id_index]
        return ""

    def _get_filepath(self, id: str, dis_type: DisTributeEnum, root: str):
        path_suffix = self._get_path_suffix(id)
        return f"{self._get_file_root(dis_type, root)}{path_suffix}{id}"

    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            if distribute_type == DisTributeEnum.SPECIAL:
                for distribute in self._distribute_config:
                    if distribute.distribute_type == DisTributeEnum.SPECIAL:
                        special_branch = self._get_special_branch(distribute.distribute_rule.items())
                        if special_branch:
                            views.add(
                                f"{config.p4_views['resources_root'].format(branch=f'b/{special_branch}')}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/ingame_prefabs/"
                            )
                            self.p4_mgr.add_p4_views(list(views))
                            return
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    views.add(view)
                    # NOTE: 以目录的形式多加一层
                    views.add(f"{view}/")
        self.p4_mgr.add_p4_views(list(views))

    def _get_special_branch(self, data_list_special) -> str:
        max_branch = self._b_path
        special_data_branches = []
        for _, branch in data_list_special:
            if branch not in special_data_branches and branch.startswith(max_branch):
                special_data_branches.append(branch)
        if special_data_branches:
            return special_data_branches[0]
        return ""

    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                path_suffix = self._get_path_suffix(id)
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    dst = self.p4_mgr.parse_p4view_to_localpath(view)
                    src = f"{self.ab_path}art/ingame_prefabs/{path_suffix}{id}"
                    path_mgr.copy(src, dst, overwrite=True)
                    self._submit_ab_views.append(view)
