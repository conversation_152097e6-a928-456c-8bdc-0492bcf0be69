import os
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, config, ResourceTypeEnum, env_mgr


class NpcPackageMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(kind=ResourceTypeEnum.NPC, **kwargs)
        self._const_source_p4_views = [
            f"{config.p4_views.get('art_resource_root')}/role/cloth_show/...",
        ]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/npc/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _filter_source_paths(self, source_paths: list) -> set:
        depots = set()
        for file in source_paths:
            file: str
            if file.startswith(f"{self._resource_dir_prefix}/actions"):
                if file.endswith(".FBX") or file.endswith(".anim"):
                    depots.add(file)
            elif file.startswith(f"{self._resource_dir_prefix}/models"):
                search_path = "/".join(file.split("/")[:9])

                files_prefab = self.p4.files(f"{search_path}/*prefab")
                files_fbx = []
                if not files_prefab:
                    files_fbx = self.p4.files(f"{search_path}/*fbx")
                for search_result in files_prefab + files_fbx:
                    depots.add(search_result)
        return depots

    def _fill_source_p4_views(self):
        views = []
        for path in env_mgr.get_source_paths():
            base_path, _ = path.rsplit("/", 1)
            new_path = base_path + "/..."
            views.append(new_path)
        self.p4_mgr.add_p4_views(views)

    def _get_submit_p4_view_ab(self):
        files = set()
        with open(self._resources_path, "r", encoding="UTF-8") as f:
            file_paths = f.read()
        resources = file_paths.split(",")
        for submit_file in resources:
            submit_file: str
            if submit_file.lower().find("assets/resources/art/npc") == -1:
                continue

            submit_file = submit_file.lower().replace("assets/resources/art", self._ab_view + "art")
            file, _ = os.path.splitext(submit_file)
            files.add(os.path.dirname(file))
        return list(files)
