node {
    wrap([$class: 'BuildUser']){
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline{
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder(logRotator(numToKeepStr: env.numsToKeepStr, daysToKeepStr: env.daysToKeepStr))
    }
    parameters {
        text(name: 'first_changelist', defaultValue: '-1', description: '输入开始打包的changelist')
        booleanParam(name: 'dev', defaultValue: true, description: '是否为开发模式')
        text(name: 'force_ids', defaultValue: '', description: '强制打包id列表，多个id使用英文半角逗号隔开')
    }
    triggers {
        cron(env.BUILD_PERIOD)
    }
    environment {
        PIPELINE_PYTHON = 'pipeline-python'
        RESOURCE = 'online_update_texture'
    }
    stages {
        stage("美术资源打包"){
            matrix {
                agent {
                    node{
                        label "art_package_${PLATFORM}"
                        customWorkspace "${env.workspace}/.."
                    }
                }
                axes {
                    axis {
                        name "PLATFORM"
                        values "android","ios"
                    }
                }
                stages{
                    stage("更新流水线依赖") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "${PIPELINE_PYTHON} -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && ${PIPELINE_PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q")
                                }
                            }
                        }
                    }
                    stage("准备") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=prepare")
                                }
                            }
                        }
                    }
                    stage('检查是否需要打包'){
                        steps {
                            dir('pyframe-pipeline'){
                                script{
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=check_package")
                                }
                            }
                        }
                    }
                    stage('获取p4资源'){
                        steps{
                            dir("pyframe-pipeline"){
                                script{
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=update_git_and_p4")
                                }
                            }
                        }
                    }
                    stage('调用打包命令'){
                        steps{
                            dir("pyframe-pipeline"){
                                script{
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=package")
                                }
                            }
                        }
                    }
                    stage("提交产物"){
                        steps{
                            dir("pyframe-pipeline"){
                                script{
                                    bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=submit")
                                }
                            }
                        }
                    }
                }
                post{
                    always{
                        dir("pyframe-pipeline"){
                            script{
                                bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_always")
                            }
                        }
                    }
                }
            }
        }
    }
    post {
        unstable {
            node('art_package_ios') {
                // 默认路径为 {slave workspace}/workspace/job_name  所以需要加两个 /..
                withEnv(["WORKSPACE=${env.WORKSPACE}/../.."]){
                    dir("${env.workspace}/pyframe-pipeline"){
                        script{
                            bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_unstable")
                        }
                    }
                }
            }
        }
        success{
            node('art_package_ios') {
                withEnv(["WORKSPACE=${env.WORKSPACE}/../.."]){
                    dir("${env.workspace}/pyframe-pipeline"){
                        script{
                            bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_success")
                        }
                    }
                }
            }
        }
        failure {
            node('art_package_ios') {
                withEnv(["WORKSPACE=${env.WORKSPACE}/../.."]){
                    dir("${env.workspace}/pyframe-pipeline"){
                        script{
                            bat(script: "${PIPELINE_PYTHON} x5m.py art_package --resource=${RESOURCE} --job=on_failure")
                        }
                    }
                }
            }
        }
    }
}
