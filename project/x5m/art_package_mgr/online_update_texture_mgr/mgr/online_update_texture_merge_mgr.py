import re

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import (
    PackageMgr,
    config,
    ResourceTypeEnum,
    env_mgr,
)


class OnlineUpdateTextureMergeMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(kind=ResourceTypeEnum.ONLINE_UPDATE_TEXTURE, **kwargs)
        self._execute_method = "H3DBuildTools.BuildAB"
        self._resource_dir_prefix = f"{config.p4_views.get('onlineupdate_root')}"
        self._ab_resource = os.path.join(self._project_path, "Assets", "ab_resources")
        self._version_pattern = re.compile(r"/onlineupdate/([^/]+)/([^/]+)/")
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: list) -> set:
        depots = set()
        for file in source_paths:
            if not file.startswith(self._resource_dir_prefix):
                continue
            if "/art_src/texture/" in file or "/art_src/c/" in file:
                if file.lower().endswith(".png"):
                    depots.add(file)
        return depots

    def _set_common_texture_paths(self) -> list:
        texture_common_path = set()
        for path in env_mgr.get_source_paths():
            end_index = path.find("/art_src/")
            if end_index != -1:
                prefix = path[:end_index]
                texture_common_path.add(prefix)
        env_mgr.set_common_texture_paths(list(texture_common_path))

    def _get_path_version(self, path: str) -> str:
        match = self._version_pattern.search(path)
        if match:
            return f"{match.group(1)}/{match.group(2)}"
        return ""

    def _clean_resource(self) -> None:
        """
        清理资源
        """
        self._set_common_texture_paths()
        self._common_paths = env_mgr.get_common_texture_paths()
        for path in self._common_paths:
            ab_view = f"{path}/client/{self._platform}/assetbundles"
            ab_path = self.p4_mgr.parse_p4view_to_localpath(ab_view)
            self._safe_clean(ab_path, "开始清理ab资源")
        self._safe_clean(self._project_art_path, "开始清理原始资源")
        self._safe_clean(self._ab_resource, "开始清理ab_resource")
        self._safe_clean(self._report_root, "开始清理打包日志")

    def _fill_source_p4_views(self):
        source_paths = env_mgr.get_source_paths()
        views = []
        for path in source_paths:
            art_index = path.find("art_src")

            resource_path = path.replace(
                path[: art_index + 7],
                f"arttrunk/{config.arttrunk_branch}/mobile_dancer/arttrunk/client/Assets/ab_resources",
            )
            path_version = self._get_path_version(path)
            if "/art_src/texture/" in resource_path:
                resource_path = resource_path.replace("/texture/", f"/texture/{path_version}/")
            # if "/art_src/c/" in resource_path:
            #     resource_path = resource_path.replace("/c/", f"/c/{path_version}/")
            view = f"{path} //{self.p4.client}/{resource_path}"
            views.append(view)
        self.p4.set_view(views)

    def _fill_const_ab_p4_views(self):
        """
        填充常量p4view
        """
        const_ab_p4_views = []
        for path in self._common_paths:
            const_ab_p4_views.extend(
                [
                    f"{path}/client/{self._platform}/assetbundles/texture/",
                    f"{path}/client/{self._platform}/assetbundles/c/",
                ]
            )
        self.p4_mgr.set_p4_views(const_ab_p4_views)

    def _update_p4(self) -> None:
        """
        更新p4
        """
        # 主要是拉资源路径，资源拉到对应的change
        self._fill_source_p4_views()
        self.p4_mgr.sync_all(changelist=env_mgr.get_changelist(), force=env_mgr.get_force())
        # 这个主要是拉ab包路径，ab包每次都拉到head
        self._fill_const_ab_p4_views()
        self.p4_mgr.sync_all(force=env_mgr.get_force())

    def _make_resouces_file(self, p4_dir_name: str):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                if not path.startswith(f"{p4_dir_name}/art_src"):
                    continue
                log.info(f"old path: {path}")
                path_version = self._get_path_version(path)
                path = path.replace(f"{p4_dir_name}/art_src", f"Assets/ab_resources")
                if "/art_src/texture/" in path:
                    path = path.replace("/texture/", f"/texture/{path_version}/")
                # if "/art_src/c/" in path:
                #     path = path.replace("/c/", f"/c/{path_version}/")
                log.info(f"new path: {path}")
                lines.append(path)
            f.write(",".join(lines))

    def _get_package_cmd(self, ab_path: str) -> str:
        """
        获取打包命令
        """
        cmd_line = f"{self._platform.package_tool} "
        cmd_line += f"-quit -batchmode -projectPath {self._project_path} -logFile {self._log_file} "
        cmd_line += f"-executeMethod {self._execute_method} -buildTarget {self._platform} "
        cmd_line += f"outpath={ab_path} "
        return cmd_line.replace("/", "\\")

    def _package(self):
        """
        打包
        """
        self._common_paths = env_mgr.get_common_texture_paths()
        for path in self._common_paths:
            self._make_resouces_file(path)
            ab_view = f"{path}/client/{self._platform}/assetbundles"
            ab_path = self.p4_mgr.parse_p4view_to_localpath(ab_view)
            cmd_line = self._get_package_cmd(ab_path)
            ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
            if ret != 0:
                advance.raise_unity_log_exception(log_path=self._log_file)
                raise PyframeException(f"打包命令执行失败，错误码:{ret}")
            path_version = self._get_path_version(ab_path)
            major_version, build_number = path_version.split("/")
            if path_mgr.exists(f"{ab_path}/texture/{path_version}/"):
                # 移动f"{ab_path}/texture/{path_version}"下的资源到f"{ab_path}/texture/"下
                src = os.path.join(ab_path, "texture", major_version, build_number)
                dst = os.path.join(ab_path, "texture")
                path_mgr.xcopy(src, dst, dst_is_file=False, quiet=True)
            if path_mgr.exists(f"{ab_path}/c/{path_version}/"):
                # 移动f"{ab_path}/c/{path_version}"下的资源到f"{ab_path}/c/"下
                src = os.path.join(ab_path, "c", major_version, build_number)
                dst = os.path.join(ab_path, "c")
                path_mgr.xcopy(src, dst, dst_is_file=False, quiet=True)

    def _calculate_package_result(self):
        """
        计算打包结果
        """
        reports = path_mgr.glob(self._report_path, "*.txt")
        if not reports:
            raise PyframeException(f"获取打包结果失败，请检查查看打包日志")
        report = reports[-1]
        with open(report, "r", encoding="utf-8") as f:
            content = f.readlines()
            # 解析打包结果
            report_detail = self._get_report_detail(content)
            success_ids = report_detail.get("success_ids")
            report_detail["success_ids"] = [id for id in success_ids if id != "shaders"]

            # 记录打包结果
            log.debug(f"platform: {self._platform}, report_detail: {report_detail}")
            env_mgr.set_report(report=report_detail)

    def _get_submit_p4_view_ab(self):
        files = set()
        resources = env_mgr.get_source_paths()
        for submit_file in resources:
            submit_file: str
            submit_file = submit_file.lower().replace("art_src", f"client/{self._platform}/assetbundles")
            file, _ = os.path.splitext(submit_file)
            file = f"{file}_mat"
            log.info(f"submit_file: {submit_file}")
            files.add(file)
        return list(files)
