import configparser

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import (
    PackageMgr,
    X5mGitMgr,
    env_mgr,
    config,
)
from project.x5m.art_package_mgr.base_mgr.week_update_mgr import (
    WeekUpdateMgr,
    ONLINE_UPDATE_PATH,
)


class OnlineUpdateTextureMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(env_mgr.get_resource(), **kwargs)
        self._namespace = env_mgr.get_namespace()
        self._config_git_mgr = X5mGitMgr(
            self._workspace,
            "x5m_config_persistent",
            f"texture-{self._namespace}-{self._platform}",
            "auto",
        )
        self._ab_resource = os.path.join(self._project_path, "Assets", "ab_resources")
        self._execute_method = "H3DBuildTools.BuildAB"
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb"

    def _check_package_by_changelist(self, changelist: int) -> bool:
        """
        检查指定路径下资源是否有更新,子类有特殊逻辑可以重写这部分
        """
        self._init_params()
        changes = []
        for path in self._latest_paths:
            resource_dir_prefix = f"{path}/art_src/texture"
            c_resource_dir_prefix = f"{path}/art_src/c"
            # 暂时不考虑harmony
            _changes = self.p4.get_changes(
                f"{resource_dir_prefix}/...@{changelist},now",
                max=config.max_changelist_size,
            )
            if _changes:
                changes.extend(_changes)
            c_changes = self.p4.get_changes(
                f"{c_resource_dir_prefix}/...@{changelist},now",
                max=config.max_changelist_size,
            )
            if c_changes:
                changes.extend(c_changes)
        log.info(f"changes: {changes}")
        if len(changes) == 0:
            return False
        depot_files = set()
        users = set()
        # 每个souces_path对应的最新action,解决先add后delete的问题
        source_path_acition_dict = {}
        # 记录每个资源对应本地打包的change,用于记录历史资源打包的资源以及对应change
        source_path_change_dct = {}
        # changes format: [{'change': 'xxx', 'time': '1728528241', 'user': 'xxx', 'client': 'xxx', 'status': 'submitted', 'changeType': 'public', 'path': 'xxx', 'desc': 'xxx'}]
        for change in changes:
            changeid: str = change.get("change")
            changed_files: dict = self.p4.get_files_by_changelist(
                changeid, depotfile_only=False
            )
            source_paths: list = changed_files.get("depotFile", [])
            action = changed_files.get("action", [])
            for i in range(len(source_paths) - 1, -1, -1):
                if source_paths[i] not in source_path_acition_dict:
                    source_path_acition_dict[source_paths[i]] = action[i]
                # 如果操作满足条件，则标识路径已经删除
                if action[i] in self._p4_ingore_action_list:
                    source_paths.pop(i)
            log.info(f"source_paths: {source_paths}")
            source_path = self._filter_source_paths(source_paths)
            log.info(f"source_path: {source_path}")
            if not source_path:
                continue
            # tmp
            changeid = int(changeid)
            for source in source_path:
                if source in source_path_change_dct:
                    if changeid > source_path_change_dct[source]:
                        source_path_change_dct[source] = changeid
                else:
                    source_path_change_dct[source] = changeid
            depot_files.update(source_path)

            user: str = change.get("user")
            # TODO: 固定后缀，可以设置为常量
            if not user.endswith("h3d.com.cn"):
                user = f"{user}@h3d.com.cn"
            users.add(user)
        # if self.__history_source:
        #     env.set({"source_path_change_dct": source_path_change_dct})
        log.info(f"depot_files: {depot_files}")
        if not depot_files:
            return False
        env_mgr.update_source_paths(source_paths=list(depot_files))
        env_mgr.update_users(users=list(users))
        env_mgr.set_changelist(changelist=changes[0].get("change"))
        return True

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _init_params(self):
        self._config_git_mgr.update(clean=True)
        configs = configparser.ConfigParser(allow_no_value=True)
        configs.read(
            f"{self._workspace}/x5m_config_persistent/texture-{self._namespace}-{self._platform}/config.ini"
        )
        hotfix_config = configs["HOTFIX"]
        log.info(f"before config hotfix: {hotfix_config}")
        week_update_mgr = WeekUpdateMgr(
            kind=self._namespace, hotfix_config=hotfix_config
        )
        week_update_mgr.switch_version()
        week_update_mgr.to_config(configs)
        configs.write(
            open(
                f"{self._workspace}/x5m_config_persistent/texture-{self._namespace}-{self._platform}/config.ini",
                "w",
            )
        )
        log.info(f"after config hotfix: {configs['HOTFIX']}")
        weekly_resource_dir_name, _ = week_update_mgr.calculate_weekly_resource_dir()
        log.info(f"周更资源目录: {weekly_resource_dir_name}")
        # 获取周更资源目录
        current_major_version_p4_path = os.path.join(
            ONLINE_UPDATE_PATH, week_update_mgr._major_version
        ).replace("\\", "/")
        log.info(f"当前主版本p4目录: {current_major_version_p4_path}")
        current_source_p4_dir_name = os.path.join(
            current_major_version_p4_path, weekly_resource_dir_name
        ).replace("\\", "/")
        log.info(f"当前周更资源目录: {current_source_p4_dir_name}")
        self._latest_paths = []
        if self._namespace == "resources":
            self._latest_paths = week_update_mgr.find_latest_online_update_dir(
                current_major_version_p4_path, weekly_resource_dir_name
            )
        else:
            self._latest_paths.append(current_source_p4_dir_name)
        log.info(f"latest_paths: {self._latest_paths}")
        env_mgr.set_latest_paths(self._namespace, self._latest_paths)

    def _filter_source_paths(self, source_paths: list) -> set:
        depots = set()
        prefix_paths = []
        for path in self._latest_paths:
            resource_dir_prefix = f"{path}/art_src/texture"
            c_resource_dir_prefix = f"{path}/art_src/c"
            prefix_paths.append(resource_dir_prefix)
            prefix_paths.append(c_resource_dir_prefix)
        log.info(f"prefix_paths: {prefix_paths}")
        for file in source_paths:
            file: str
            if not file.lower().endswith(".png"):
                continue
            if not self._check_start_with(file, prefix_paths):
                continue
            depots.add(file)
        return depots

    def _check_start_with(self, file: str, prefixes: list):
        for prefix in prefixes:
            if file.startswith(prefix):
                return True
        return False

    def _clean_resource(self) -> None:
        """
        清理资源
        """
        self._latest_paths = env_mgr.get_latest_paths(self._namespace)
        for path in self._latest_paths:
            ab_view = f"{path}/client/{self._platform}/assetbundles"
            ab_path = self.p4_mgr.parse_p4view_to_localpath(ab_view)
            self._safe_clean(ab_path, "开始清理ab资源")
        self._safe_clean(self._project_art_path, "开始清理原始资源")
        self._safe_clean(self._ab_resource, "开始清理ab_resource")
        self._safe_clean(self._report_root, "开始清理打包日志")

    def _fill_source_p4_views(self):
        source_paths = env_mgr.get_source_paths()
        views = []
        for path in source_paths:
            art_index = path.find("art_src")
            resource_path = path.replace(
                path[: art_index + 7],
                f"arttrunk/{config.arttrunk_branch}/mobile_dancer/arttrunk/client/Assets/ab_resources",
            )
            view = f"{path} //{self.p4.client}/{resource_path}"
            views.append(view)
        self.p4.set_view(views)

    def _fill_const_ab_p4_views(self):
        """
        填充常量p4view
        """
        const_ab_p4_views = []
        for path in self._latest_paths:
            const_ab_p4_views.extend(
                [
                    f"{path}/client/{self._platform}/assetbundles/texture/",
                    f"{path}/client/{self._platform}/assetbundles/c/",
                ]
            )
        self.p4_mgr.set_p4_views(const_ab_p4_views)

    def _update_p4(self) -> None:
        """
        更新p4
        """
        # 主要是拉资源路径，资源拉到对应的change
        self._fill_source_p4_views()
        self.p4_mgr.sync_all(
            changelist=env_mgr.get_changelist(), force=env_mgr.get_force()
        )
        # 这个主要是拉ab包路径，ab包每次都拉到head
        self._fill_const_ab_p4_views()
        self.p4_mgr.sync_all(force=env_mgr.get_force())

    def _package(self):
        """
        打包
        """
        self._latest_paths = env_mgr.get_latest_paths(self._namespace)
        for path in self._latest_paths:
            if not self._check_source_path_prefix(path):
                log.info(f"{path} 不是资源目录，跳过打包")
                continue
            self._make_resouces_file(path)
            ab_view = f"{path}/client/{self._platform}/assetbundles"
            ab_path = self.p4_mgr.parse_p4view_to_localpath(ab_view)
            cmd_line = self._get_package_cmd(ab_path)
            ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
            if ret != 0:
                advance.raise_unity_log_exception(log_path=self._log_file)
                raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _check_source_path_prefix(self, prefix: str) -> bool:
        for path in env_mgr.get_source_paths():
            if path.startswith(prefix):
                return True
        return False

    def _make_resouces_file(self, p4_dir_name: str):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                if not path.startswith(f"{p4_dir_name}/art_src"):
                    continue
                log.info(f"old path: {path}")
                path = path.replace(f"{p4_dir_name}/art_src", "Assets/ab_resources")
                log.info(f"new path: {path}")
                lines.append(path)
            f.write(",".join(lines))

    def _get_package_cmd(self, ab_path: str) -> str:
        """
        获取打包命令
        """
        cmd_line = f"{self._platform.package_tool} "
        cmd_line += f"-quit -batchmode -projectPath {self._project_path} -logFile {self._log_file} "
        cmd_line += (
            f"-executeMethod {self._execute_method} -buildTarget {self._platform} "
        )
        cmd_line += f"outpath={ab_path} "
        return cmd_line.replace("/", "\\")

    def _calculate_package_result(self):
        """
        计算打包结果
        """
        reports = path_mgr.glob(self._report_path, "*.txt")
        if not reports:
            raise PyframeException(f"获取打包结果失败，请检查查看打包日志")
        report = reports[-1]
        with open(report, "r", encoding="utf-8") as f:
            content = f.readlines()
            # 解析打包结果
            report_detail = self._get_report_detail(content)
            success_ids = report_detail.get("success_ids")
            report_detail["success_ids"] = [id for id in success_ids if id != "shaders"]

            # 记录打包结果
            log.debug(f"platform: {self._platform}, report_detail: {report_detail}")
            env_mgr.set_report(report=report_detail)

    def _get_submit_p4_view_ab(self):
        files = set()
        resources = env_mgr.get_source_paths()
        for submit_file in resources:
            submit_file: str
            submit_file = submit_file.replace(
                "art_src", f"client/{self._platform}/assetbundles"
            )
            file, _ = os.path.splitext(submit_file)
            file = f"{file}_mat"
            log.info(f"submit_file: {submit_file}")
            files.add(file)
        return list(files)

    def always(self):
        build_number = os.getenv("BUILD_NUMBER")
        self._desc = f"jenkins auto commit {build_number}"
        if not env_mgr.get_need_package():
            self._config_git_mgr.submit_path(
                self._desc,
                f"{self._workspace}/x5m_config_persistent/texture-{self._namespace}-{self._platform}/config.ini",
            )
            return
        self._config_git_mgr.submit_path(
            self._desc,
            f"{self._workspace}/x5m_config_persistent/texture-{self._namespace}-{self._platform}/config.ini",
        )
        self._backup()
        self._upload()
        self._clean()
