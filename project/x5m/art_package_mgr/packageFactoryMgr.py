from frame import PyframeException, env
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr
from project.x5m.art_package_mgr.base_mgr.enum_mgr import ResourceTypeEnum
from project.x5m.art_package_mgr.timeline_package_mgr.mgr.timeline_package_mgr import TimelinePackageMgr
from project.x5m.art_package_mgr.npc_package_mgr.mgr.npc_package_mgr import NpcPackageMgr
from project.x5m.art_package_mgr.bodypart_package_mgr.mgr.bodypart_package_mgr import BodypartPackageMgr
from project.x5m.art_package_mgr.theme_package_mgr.mgr.theme_package_mgr import ThemePackageMgr
from project.x5m.art_package_mgr.link_distribute_mgr.mgr.link_distribute_mgr import LinkDistributeMgr
from project.x5m.art_package_mgr.small_island_mgr.mgr.small_island_mgr import SmallIslandMgr
from project.x5m.art_package_mgr.scene_package_mgr.mgr.scene_package_mgr import ScenePackageMgr
from project.x5m.art_package_mgr.house_scene_mgr.mgr.house_scene_mgr import HouseSceneMgr
from project.x5m.art_package_mgr.action_distrib_mgr.mgr.action_distrib_mgr import ActionDistribMgr
from project.x5m.art_package_mgr.stream_scene_mgr.mgr.stream_scene_mgr import StreamSceneMgr
from project.x5m.art_package_mgr.chd_package_mgr.mgr.chd_package_mgr import ChdPackageMgr
from project.x5m.art_package_mgr.house_distrib_mgr.mgr.house_distrib_mgr import HouseDistributeMgr
from project.x5m.art_package_mgr.model_package_mgr.mgr.model_package_mgr import ModelPackageMgr
from project.x5m.art_package_mgr.pet_package_mgr.mgr.pet_package_mgr import PetPackageMgr
from project.x5m.art_package_mgr.chair_package_mgr.mgr.chair_package_mgr import ChairPackageMgr
from project.x5m.art_package_mgr.effect_package_mgr.mgr.effect_package_mgr import EffectPackageMgr
from project.x5m.art_package_mgr.beast_package_mgr.mgr.beast_package_mgr import BeastPackageMgr
from project.x5m.art_package_mgr.music_action_distrib_mgr.mgr.mgr import MusicActionDistribMgr
from project.x5m.art_package_mgr.camera_distrib_mgr.mgr.mgr import CameraDistribMgr
from project.x5m.art_package_mgr.camera_action_mgr.mgr.mgr import CameraActionMgr
from project.x5m.art_package_mgr.camera_effect_mgr.mgr.mgr import CameraEffectMgr
from project.x5m.art_package_mgr.link_unencrypt_mgr.mgr.link_unencrypt_mgr import LinkUnencryptMgr
from project.x5m.art_package_mgr.arthub_upload_p4_mgr.mgr.mgr import ArthubUploadP4Mgr
from project.x5m.art_package_mgr.card_package_mgr.mgr.card_package_mgr import CardPackageMgr
from project.x5m.art_package_mgr.makeup_distrib_mgr.mgr.makeup_distrib_mgr import MakeupDistribMgr
from project.x5m.art_package_mgr.game_icon_upload_git.mgr.mgr import GameIconUploadGitMgr
from project.x5m.art_package_mgr.mv_ingame_mgr.mgr.mv_ingame_mgr import MvIngameMgr
from project.x5m.art_package_mgr.texture_package_mgr.mgr.texture_package_mgr import TexturePackageMgr
from project.x5m.art_package_mgr.edition_effect_package_mgr.mgr.mgr import EditionEffectPackageMgr
from project.x5m.art_package_mgr.art_cdn_package.mgr.mgr import ArtCdnPackageMgr
from project.x5m.art_package_mgr.online_update_texture_mgr.mgr.online_update_texture_mgr import OnlineUpdateTextureMgr
from project.x5m.art_package_mgr.music_effect_mgr.mgr.music_effect_mgr import MusicEffectMgr
from project.x5m.art_package_mgr.online_update_texture_mgr.mgr.online_update_texture_merge_mgr import OnlineUpdateTextureMergeMgr
from project.x5m.art_package_mgr.changelist_initialization.mgr.mgr import ChnagelistInitializationMgr


class PackageFactoryMgr:
    ResourcesRigister = {
        ResourceTypeEnum.TIMELINE: TimelinePackageMgr,
        ResourceTypeEnum.NPC: NpcPackageMgr,
        ResourceTypeEnum.BODYPART: BodypartPackageMgr,
        ResourceTypeEnum.THEME: ThemePackageMgr,
        ResourceTypeEnum.LinkDistrib: LinkDistributeMgr,
        ResourceTypeEnum.IslandDistrib: SmallIslandMgr,
        ResourceTypeEnum.Scene: ScenePackageMgr,
        ResourceTypeEnum.HouseScene: HouseSceneMgr,
        ResourceTypeEnum.ACTION_DISTRIB: ActionDistribMgr,
        ResourceTypeEnum.StreamScene: StreamSceneMgr,
        ResourceTypeEnum.CHD: ChdPackageMgr,
        ResourceTypeEnum.HouseDistrib: HouseDistributeMgr,
        ResourceTypeEnum.Model: ModelPackageMgr,
        ResourceTypeEnum.Pet: PetPackageMgr,
        ResourceTypeEnum.Chair: ChairPackageMgr,
        ResourceTypeEnum.Effect: EffectPackageMgr,
        ResourceTypeEnum.Beast: BeastPackageMgr,
        ResourceTypeEnum.MUSIC_ACTION_DISTRIB: MusicActionDistribMgr,
        ResourceTypeEnum.CAMERA_DISTRIB: CameraDistribMgr,
        ResourceTypeEnum.CAMERA_ACTION: CameraActionMgr,
        ResourceTypeEnum.CAMERA_EFFECT: CameraEffectMgr,
        ResourceTypeEnum.LINK_UNENCRYPT: LinkUnencryptMgr,
        ResourceTypeEnum.ARTHUB_UPLOAD_P4: ArthubUploadP4Mgr,
        ResourceTypeEnum.CARD: CardPackageMgr,
        ResourceTypeEnum.MAKEUP_DISTRIB: MakeupDistribMgr,
        ResourceTypeEnum.GAME_ICON_UPLOAD_GIT: GameIconUploadGitMgr,
        ResourceTypeEnum.INGAME_PREFABS: MvIngameMgr,
        ResourceTypeEnum.TEXTURE: TexturePackageMgr,
        ResourceTypeEnum.EDITION_EFFECT: EditionEffectPackageMgr,
        ResourceTypeEnum.ART_CDN: ArtCdnPackageMgr,
        ResourceTypeEnum.ONLINE_UPDATE_ONLINE_TEXTURE: OnlineUpdateTextureMgr,
        ResourceTypeEnum.ONLINE_UPDATE_RELEASE_TEXTURE: OnlineUpdateTextureMgr,
        ResourceTypeEnum.ONLINE_UPDATE_RESOURCES_TEXTURE: OnlineUpdateTextureMgr,
        ResourceTypeEnum.MUSIC_EFFECT: MusicEffectMgr,
        ResourceTypeEnum.ONLINE_UPDATE_TEXTURE: OnlineUpdateTextureMergeMgr,
        ResourceTypeEnum.CHANGELIST_INITIALIZATION: ChnagelistInitializationMgr,
    }

    @staticmethod
    def get_package_mgr(kind: ResourceTypeEnum) -> PackageMgr:
        if kind not in PackageFactoryMgr.ResourcesRigister:
            raise PyframeException("不支持的资源类型")
        return PackageFactoryMgr.ResourcesRigister[kind]


resource: ResourceTypeEnum = ResourceTypeEnum.get_resource_type(env.get("RESOURCE", ""))
package_mgr: PackageMgr = PackageFactoryMgr().get_package_mgr(resource)()
# package_mgr: PackageMgr = PackageFactoryMgr.get_package_mgr(resource)(client="chenjinlei_test", root="E:\chenjinlei_test", workspace="E:\chenjinlei_test")
