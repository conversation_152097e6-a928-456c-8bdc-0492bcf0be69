from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import (
    PackageMgr,
    ResourceTypeEnum,
    config,
    env_mgr,
)


class PetPackageMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(kind=ResourceTypeEnum.Pet, **kwargs)
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/art/pet/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: list) -> set:
        depots = set()
        for file in source_paths:
            file: str
            lower_file = file.lower()
            if not lower_file.startswith(self._resource_dir_prefix):
                continue
            if lower_file.startswith(
                f"{self._resource_dir_prefix}/actions"
            ) and lower_file.endswith(".fbx"):
                depots.add(file)
            if lower_file.startswith(f"{self._resource_dir_prefix}/models"):
                if lower_file.find("/effect/") != -1:
                    continue
                if lower_file.endswith(".fbx") or lower_file.endswith(".prefab"):
                    depots.add(file)
        return depots

    def _fill_source_p4_views(self):
        views = set()
        for path in env_mgr.get_source_paths():
            base_path = path.rsplit("/", 2)[0]
            new_path = base_path + "/..."
            views.add(new_path)
        self.p4_mgr.add_p4_views(list(views))

    def _get_submit_p4_view_ab(self):
        files = set()
        with open(self._resources_path, "r", encoding="UTF-8") as f:
            file_paths = f.read()
        resources = file_paths.split(",")
        # [
        #   'Assets/resources/Art/pet/models/pet_00380088/00380088_s/Prefab/0038008804_EF.prefab',
        #   'Assets/resources/Art/pet/models/pet_00380088/00380088_s/0038008804.FBX'
        # ]
        for submit_file in resources:
            submit_file: str
            if submit_file.lower().find("assets/resources/art/pet") == -1:
                continue

            submit_file = submit_file.lower().replace(
                "assets/resources/art", self._ab_view + "art"
            )
            file, _ = os.path.splitext(submit_file)
            if file.find("/pet/models") != -1:
                path, file_name = os.path.split(file)
                if not path.endswith("prefab"):
                    file = f"{path}/prefab/{file_name}"
                else:
                    file = f"{path}/{file_name}"
            files.add(file)
        return list(files)
