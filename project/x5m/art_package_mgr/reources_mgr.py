from frame import *
from project.x5m.art_package_mgr.env_mgr import env_mgr
from project.x5m import config
from project.x5m.base import ResourceManager


class ReourcesMgr(ResourceManager):
    def __init__(self, tp, *args, **kwargs) -> None:
        """
        Args:
            tp (str): 美术资源打包类型
            p4_root: p4根目录
            client: p4 client
            workspace: jenkins workspace
        """
        self.tp = tp
        self.debug = env_mgr.get_debug()
        self.platform = env_mgr.get_platform()
        self.p4_root = kwargs.get("p4_root", env_mgr.get_workspace())
        self.client = kwargs.get("client", f"jenkins-{env.pipeline.function_name()}-{env_mgr.get_platform()}-{common.get_host_ip()}")
        self.workspace = kwargs.get("worksapce", env_mgr.get_workspace())
        self.__init_p4()
        super().__init__(*args, **kwargs)

    def __init_p4(self):
        self.p4 = P4Client(
            host=config.P4_CONFIG_JENKINS.get("host"),
            username=config.P4_CONFIG_JENKINS.get("username"),
            password=config.P4_CONFIG_JENKINS.get("password"),
            client=self.client,
            charset=P4Client.Charset.CP936,
        )
