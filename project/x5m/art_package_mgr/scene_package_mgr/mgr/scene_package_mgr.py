import collections
import re
from typing import List

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config, X5mGitMgr, DisTributeEnum, env_mgr, PlatformEnum
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr


class ScenePackageMgr(DistributePackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.Scene, **kwargs)
        self._execute_method = "H3DBuildTools.BuildArt"
        self._export_method = "NavMeshExport.OpenSceneAndExport"
        self._resource_dir_prefix = f"{config.p4_views.get('art_resources_src')}/scene"
        self._source_prefix = "Assets/StaticResources/art/3d"
        self._project_art_path = os.path.join(self._project_path, self._source_prefix)
        self._b_path = self.p4_mgr.get_last_branch()
        self._ab_path = os.path.join(self._workspace, f"arttrunk/{self._arttrunk_branch}/mr/common/cs/{self._platform}/assetbundles")
        self._special_scene_dir_name = ["3d", "shequ_01", "sta", "streaming", "xuejingshequ", "zaofangzi"]
        self._c_ab_prefix = "assets/staticresources/art/3d/stage/c/"
        self._project_client = f"arttrunk/{self._arttrunk_branch}/mobile_dancer/arttrunk/client"
        self._cs_path = f"cs/{self._platform}/assetbundles"
        self._project_mr = f"arttrunk/{self._arttrunk_branch}/mr"
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s+scene/([^/]+)\n")
        self._fail_pattern = [re.compile(r"^\[Error]\s+\d+\s+scene/([^/]+)\n")]
        self._is_special_branch = False
        self._x5config_path = os.path.join(self._workspace, "x5mconfig", "cdn", "config", "server", "community", "extend_game")
        self._export_log = os.path.join(self._log_root, f"export_{self._platform}_{env.pipeline.build_num()}.log")
        self._const_source_p4_views = [
            f"{config.p4_views.get('art_resource_root')}/island/building/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/island/building/...",
            f"{config.p4_views.get('art_resource_root')}/role/link/unencrypt/1017026801/... //{self.p4.client}/{self._project_client}/Assets/resources/Art/role/link/unencrypt/1017026801/...",
            f"{config.p4_views.get('art_resources_src')}/scene/3d/fitment/... //{self.p4.client}/{self._project_client}/Assets/resources/Art/3d/fitment/...",
            f"{config.p4_views.get('art_resources_src')}/scene/sta/3d/fitment/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/fitment/...",
            f"{config.p4_views.get('art_resources_src')}/scene/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/stage/...",
            f"{config.p4_views.get('art_resources_src')}/model/cwkft_paizi/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/model/cwkft_paizi/...",
            f"{config.p4_views.get('art_resources_src')}/model/cwkft_zhuangshi/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/model/cwkft_zhuangshi/...",
            f"{config.p4_views.get('resmg_root')}/scene/... //{self.p4.client}/x5mplan/resmg/scene/...",
        ]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/scene/",
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/c/*/scene/",
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/scene/",
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/c/*/scene/",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/scene/",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/c/*/scene/",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _get_distruibute_dict(self):
        success_ids = env_mgr.get_report().get("success_ids")
        dist_destinations = collections.defaultdict(list)
        for id in success_ids:
            for distribute in self._distribute_config:
                ret = distribute.match(self._parse_id_to_filename(id))
                if ret:
                    self._distribute_map[distribute.distribute_type].append((id, [ret]))
                    if distribute.distribute_type == DisTributeEnum.SPECIAL:
                        dist_destinations[f"{str(distribute.distribute_type)}({ret})"].append(id)
                        self._is_special_branch = True
                    else:
                        dist_destinations[str(distribute.distribute_type)].append(id)
            else:
                # 如果是特殊分支，不分发主支、分支和CDN
                if not self._is_special_branch:
                    self._distribute_map[DisTributeEnum.DEFAULT].append((id, self._default_distribute()))
                    dist_destinations[str(DisTributeEnum.DEFAULT)].append(id)
        log.info(f"dist_destinations: {dist_destinations}")
        env_mgr.set_dist_destinations(dist_destinations)
        return self._distribute_map

    def __check_special_dir(self, file: str) -> bool:
        """
        是否是特殊目录
        """
        for i in self._special_scene_dir_name:
            path = "{}/{}/".format(self._resource_dir_prefix, i)
            if file.startswith(path):
                return True

    def _filter_source_paths(self, source_paths: List[str]) -> set:

        depots = set()
        for file in source_paths:
            file: str
            if not file.startswith(self._resource_dir_prefix):
                continue
            if self.__check_special_dir(file):
                continue
            if file.lower().endswith(".unity"):
                relative_path = file.replace(self._resource_dir_prefix, "assets/staticresources/art/3d/stage")
                depots.add(relative_path)
        return depots

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _clean_resource(self) -> None:
        pass

    def _update_git(self) -> None:
        """
        更新git, arttrunk
        """
        self.arttrunk_git_mgr.update_without_delete()

    def _fill_const_source_p4_views(self):
        """
        填充常量p4view
        """
        self.p4.set_view(self._const_source_p4_views)

    def _update_p4(self) -> None:
        """
        更新p4
        """
        # 主要是拉资源路径，资源拉到对应的change
        self._fill_const_source_p4_views()
        self.p4_mgr.sync_all(changelist=env_mgr.get_changelist(), force=env_mgr.get_scene_force())
        self.p4_mgr.clean(d=True)
        # 这个主要是拉ab包路径，ab包每次都拉到head
        self._fill_const_ab_p4_views()
        self.p4_mgr.sync_all(force=env_mgr.get_scene_force())
        self.p4_mgr.clean(d=True)

    def _get_export_cmd(self):
        cmd_line = f"{self._platform.package_tool} "
        cmd_line += f"-quit -batchmode -projectPath {self._project_path} -logFile {self._export_log} "
        cmd_line += f"-executeMethod {self._export_method} -buildTarget {self._platform} "
        cmd_line += f"output_path={self._x5config_path} "
        return cmd_line.replace("/", "\\")

    def _package(self):
        """
        打包
        """
        self._make_resouces_file()
        source_paths = env_mgr.get_source_paths()
        path = ",".join(source_paths)
        cmd_line = self._get_package_cmd()
        cmd_line += f"path={path} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def _update_config_git(self) -> None:
        self._cdn_git = X5mGitMgr(self._workspace, "x5mconfig", "cdn")
        self._cdn_git.update(clean=True)

    def _copy_product(self):
        """
        生成导航网格配置
        """
        self._get_distruibute_dict()
        if self._platform != PlatformEnum.IOS or self._is_special_branch:
            return
        source_paths = env_mgr.get_source_paths()
        for path in source_paths:
            export_cmd = self._get_export_cmd()
            export_cmd += f"scene_path={path} "
            ret, _ = cmd.run_shell(cmds=[export_cmd], workdir=self._workspace)
            if ret != 0:
                advance.raise_unity_log_exception(log_path=self._export_log)
                raise PyframeException(f"生成导航网格配置命令执行失败，错误码:{ret}")

    def _submit_git(self):
        # git只用提交一次
        if self._platform != PlatformEnum.IOS or self._is_special_branch:
            return
        self._cdn_git.submit_path(self._desc, os.path.join(self._workspace, "x5mconfig/cdn/config/server/community/extend_game/"))

    def _get_file_root(self, dis_type: DisTributeEnum, root: str, is_c_ab: bool):
        if dis_type == DisTributeEnum.DEFAULT:
            if root.startswith("//x5m/res/"):
                if is_c_ab:
                    return f"{root}/cooked/{self._platform}/assetbundles/c/*/scene/"
                return f"{root}/cooked/{self._platform}/assetbundles/scene/"
            root = root.replace("/ResourcePublish/CDN/SourceFiles", "")
            if is_c_ab:
                return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/c/*/scene/"
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/scene/"
        if dis_type == DisTributeEnum.HOTFIX:
            if is_c_ab:
                return f"{root}/client/{self._platform}/assetbundles/c/*/scene/"
            return f"{root}/client/{self._platform}/assetbundles/scene/"
        if dis_type == DisTributeEnum.SPECIAL:
            if is_c_ab:
                return f"{config.p4_views.get('resources_root').format(branch='b')}/{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/c/*/scene/"
            return f"{config.p4_views.get('resources_root').format(branch='b')}/{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/scene/"
        raise PyframeException(f"Invalid distribute type {dis_type}")

    def _get_filepath(self, dis_type: DisTributeEnum, root: str, is_c_ab: bool):
        return f"{self._get_file_root(dis_type, root, is_c_ab)}"

    def __check_c_version(self, id: str) -> bool:
        source_paths = env_mgr.get_source_paths()
        for path in source_paths:
            if id in path:
                if path.startswith(self._c_ab_prefix):
                    return True
        return False

    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                is_c_ab = self.__check_c_version(id)
                for path_root in path_roots:
                    views.add(self._get_file_root(distribute_type, path_root, is_c_ab))
        self.p4_mgr.add_p4_views(list(views))

    def _default_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['cdn_root']}",
            f"{config.p4_views['cdn_resources']}",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}",
        ]

    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                is_c_ab = self.__check_c_version(id)
                for path_root in path_roots:
                    view = self._get_filepath(distribute_type, path_root, is_c_ab)
                    dst = self.p4_mgr.parse_p4view_to_localpath(view)
                    src = os.path.join(self.ab_path, "scene")
                    path_mgr.copy(src, dst[:-1], overwrite=True)
                    self._submit_ab_views.extend([f"{view}{id}", f"{view}{id}.h3dmanifest"])

    def _dist_ab(self):
        self._sync_dist()
        self._dist_ab_file()
