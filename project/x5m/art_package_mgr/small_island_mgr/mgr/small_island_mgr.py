import re
from typing import List

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import ResourceTypeEnum, config, env_mgr, DisTributeEnum
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr


class SmallIslandMgr(DistributePackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.IslandDistrib, **kwargs)
        self._execute_method = "H3DBuildTools.BuildArt"
        self._resource_dir_prefix = f"{config.p4_views.get('art_resource_root')}/island"
        # self._ab_path = os.path.join(self._workspace, f"arttrunk/release/mr/Resources/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/")
        self._b_path = self.p4_mgr.get_last_branch()
        self.source_prefix = "Assets/StaticResources/art/3d"
        self._relative_source = ""
        self._const_source_p4_views = [
            f"{config.p4_views.get('art_resource_root')}/island/...",
            f"{config.p4_views.get('resmg_root')}/island/...",
        ]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/art/island/...",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/island/...",
            f"{config.p4_views.get('onlineupdate_root')}/*/*/client/{self._platform}/assetbundles/art/island/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _filter_source_paths(self, source_paths: list) -> set:
        depots = set()
        path_pattern = re.compile(r"^(//.*?\d{9,10}|//.*?island/common/island_\w+.?|//.*?island/videotemplate/vt\d{4,})/.*?$")
        paths = []
        for file in source_paths:
            file: str
            if file.startswith(f"{self._resource_dir_prefix}"):
                ret = re.findall(path_pattern, file)
                if ret:
                    if ret[0] not in paths:
                        paths.append(ret[0])
        for source_path in paths:
            # 如果是目录，直接添加
            if self.p4.dirs(source_path):
                depots.add(source_path)
        return depots

    def _fill_source_p4_views(self):
        views = []
        for path in env_mgr.get_source_paths():
            log.info(f"source path: {path}")
            new_path = path + "/..."
            views.append(new_path)
        views.append("//x5_mobile/mr/art_release/art_src/island/videotemplate/common/...")
        self.p4_mgr.add_p4_views(views)

    def _clean_other(self) -> None:
        log.info("清理StaticResources")
        project_art_path = os.path.join(self._project_path, self.source_prefix)
        if path_mgr.exists(project_art_path):
            path_mgr.rm(project_art_path)

    def _package(self):
        """
        打包
        """
        self._make_resouces_file()
        self.__move_resource_to_project()
        cmd_line = self._get_package_cmd()
        cmd_line += f"path={self._relative_source} "
        ret, _ = cmd.run_shell(cmds=[cmd_line], workdir=self._workspace)
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self._log_file)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    def __move_resource_to_project(self):
        source_paths = env_mgr.get_source_paths()
        project_art_path = os.path.join(self._project_path, self.source_prefix)
        relative_sources = []
        relative_pattern = re.compile(r"^//(.*?island/.*?\d{9,10}|.*?island/common/island_\w+.?|.*?island/videotemplate/vt\d{4,}).*?$")
        for source_path in source_paths:
            ret = re.findall(relative_pattern, source_path)
            if not ret:
                continue
            relative_path = ret[0]
            temp = relative_path.replace("x5_mobile/mr/art_release/art_src/", "")
            src = os.path.join(self._project_path, "Assets/resources/art/", temp)
            dst = os.path.join(project_art_path, temp)
            relative_source = f"{self.source_prefix}/" + temp
            if relative_source not in relative_sources:
                path_mgr.move(src, dst)
                relative_sources.append(relative_source)
        self._relative_source = ",".join(relative_sources)
        src = os.path.join(self._project_path, "Assets/resources/art/island/videotemplate/common")
        dst = os.path.join(project_art_path, "island/videotemplate/common")
        path_mgr.move(src, dst)

    def _default_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['resources_root'].format(branch='Resources')}",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}",
        ]

    def _get_file_root(self, dis_type: DisTributeEnum, root: str):
        if dis_type == DisTributeEnum.DEFAULT:
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/island/"
        if dis_type == DisTributeEnum.HOTFIX:
            return f"{root}/client/{self._platform}/assetbundles/art/island/"
        if dis_type == DisTributeEnum.SPECIAL:
            return f"{config.p4_views.get('resources_root').format(branch='b')}/{root}/resourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/island/"
        raise PyframeException(f"Invalid distribute type {dis_type}")

    def _get_path_suffix(self, id: str):
        source_paths = env_mgr.get_source_paths()
        for path in source_paths:
            if path.endswith(id):
                island_index = path.find("/island/")
                id_index = path.find(id)
                return path[island_index + 8 : id_index]
        return ""

    def _get_filepath(self, id: str, dis_type: DisTributeEnum, root: str):
        path_suffix = self._get_path_suffix(id)
        return f"{self._get_file_root(dis_type, root)}{path_suffix}{id}"

    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    views.add(view + "/...")
        log.info("fill_dist_p4_views: %s", views)
        self.p4_mgr.add_p4_views(list(views))

    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            log.info(f"distribute_id_path_roots: {distribute_id_path_roots}")
            for id, path_roots in distribute_id_path_roots:
                path_suffix = self._get_path_suffix(id)
                log.info(f"path_suffix: {path_suffix}")
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    dir_path = self.p4_mgr.parse_p4view_to_localpath(view)
                    # 当dir_path 不存在，或者不为文件夹时，创建文件夹
                    if path_mgr.exists(dir_path):
                        if path_mgr.is_file(dir_path):
                            log.warning(f"目标路径已存在且为文件，正在删除: {dir_path}")
                            path_mgr.rm(dir_path)
                            path_mgr.mkdir(dir_path)
                    if not path_mgr.exists(dir_path):
                        path_mgr.mkdir(dir_path)
                    view = view + "/" + id
                    log.info(f"dist_ab_file view: {view}")
                    dst = self.p4_mgr.parse_p4view_to_localpath(view)
                    src = os.path.join(self.ab_path, f"art/island/{path_suffix}{id}")
                    path_mgr.copy(src, dst, overwrite=True)
                    self._submit_ab_views.append(view)
