import re
from typing import List

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, ResourceTypeEnum, config, env_mgr, path_mgr


class StreamSceneMgr(PackageMgr):
    def __init__(self, **kwargs) -> None:
        super().__init__(ResourceTypeEnum.StreamScene, **kwargs)
        self._execute_method = "H3DBuildTools.BuildStreamingScene"
        self._shequ_dir_name = "shequ_01"
        self._xuejingshequ_dir_name = "xuejingshequ"
        self._source_prefix = "Assets/StaticResources/art/3d"
        self._b_path = self.p4_mgr.get_last_branch()
        self._project_client = f"arttrunk/{self._arttrunk_branch}/mobile_dancer/arttrunk/client"
        self._project_mr = f"arttrunk/{self._arttrunk_branch}/mr"
        self._resource_dir_prefix = f"{config.p4_views.get('art_resources_src')}/scene/{self._shequ_dir_name}"
        self._xuejing_prefix = f"{config.p4_views.get('art_resources_src')}/scene/{self._xuejingshequ_dir_name}"
        self._success_pattern = re.compile(r"^\[info]\s+\d+\s+scene/([^/]+)\n")
        self._fail_pattern = [re.compile(r"^\[Error]\s+\d+\s+scene/([^/]+)\n")]
        self._ab_path = os.path.join(self._workspace, f"arttrunk/{self._arttrunk_branch}/mr/Resources/cs/{self._platform}/assetbundles")
        self._const_source_p4_views = [
            f"{config.p4_views.get('art_resources_src')}/scene/streaming/... //{self.p4.client}/{self._project_client}/Assets/ab_resources/streaming/...",
        ]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/streaming/...",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/streaming/...",
        ]
        self._other_p4_views = [
            f"{config.p4_views.get('art_resources_src')}/scene/3d/fitment/... //{self.p4.client}/{self._project_client}/Assets/resources/Art/3d/fitment/...",
            f"{config.p4_views.get('art_resources_src')}/scene/sta/3d/fitment/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/fitment/...",
            f"{config.p4_views.get('art_resources_src')}/role/actions/actions_anim/... //{self.p4.client}/{self._project_client}/Assets/resources/Art/role/actions/actions_anim/...",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def _filter_source_paths(self, source_paths: List[str]) -> set:

        depots = set()
        for file in source_paths:
            file: str
            if not file.startswith(self._resource_dir_prefix) and not file.startswith(self._xuejing_prefix):
                continue

            if file.endswith(".unity"):
                relative_path = file.replace("//x5_mobile/mr/art_resource/art_src/scene", "")
                log.info(f"relative_path: {relative_path}")
                if relative_path.startswith("/{}/".format(self._shequ_dir_name)):
                    env_mgr.set_package_type(self._shequ_dir_name)
                    depots.add(f"{self._source_prefix}{relative_path}")
                    break
                else:
                    depots.add(f"{self._source_prefix}{relative_path}")
                    env_mgr.set_package_type(self._xuejingshequ_dir_name)
        return depots

    def _clean_resource(self) -> None:
        """
        清理资源
        """
        package_type = env_mgr.get_package_type()
        self._safe_clean(os.path.join(self._project_path, "Assets", "ab_resources", "streaming"), "clean ab resource")
        self._safe_clean(os.path.join(self._project_path, "Assets", "StaticResources", "Art", "3d", "stage", "shequ_01"), "clean shequ01 resource")
        self._safe_clean(
            os.path.join(self._project_mr, "art_release", "cs", f"{self._platform}", "assetbundles", f"{package_type}"), "clean streaming_stage"
        )
        self._safe_clean(os.path.join(self._project_mr, "Resources", "cs", f"{self._platform}", "assetbundles", "streaming"), "clean mr resources")
        self._safe_clean(os.path.join(self._project_mr, "b", f"{self._b_path}", "cs", f"{self._platform}", "assetbundles", "streaming"), "clean mr b")

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _update_git(self) -> None:
        """
        更新git, arttrunk
        """
        self.arttrunk_git_mgr.update_without_delete()

    def _fill_const_source_p4_views(self):
        """
        填充常量p4view
        """
        package_type = env_mgr.get_package_type()
        if package_type == self._shequ_dir_name:
            self._const_source_p4_views.append(
                f"{config.p4_views.get('art_resources_src')}/scene/{self._shequ_dir_name}/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/stage/{self._shequ_dir_name}/..."
            )
        else:
            self._const_source_p4_views.append(
                f"{config.p4_views.get('art_resources_src')}/scene/{self._xuejingshequ_dir_name}/... //{self.p4.client}/{self._project_client}/{self._source_prefix}/stage/{self._shequ_dir_name}/..."
            )
        self.p4.set_view(self._const_source_p4_views)

    def _fill_const_ab_p4_views(self):
        """
        填充常量p4view
        """
        package_type = env_mgr.get_package_type()
        if package_type == self._shequ_dir_name:
            self._const_ab_p4_views.append(f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/streaming_stage/shequ/...")
        else:
            self._const_ab_p4_views.append(f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/streaming_stage/xuejingshequ/...")
        self.p4_mgr.set_p4_views(self._const_ab_p4_views)

    def _fill_other_p4_views(self):
        """
        填充其他p4view
        """
        self.p4.set_view(self._other_p4_views)

    def _update_p4(self) -> None:
        """
        更新p4
        """
        # 主要是拉资源路径，资源拉到对应的change
        self._fill_const_source_p4_views()
        self.p4_mgr.sync_all(changelist=env_mgr.get_changelist(), force=env_mgr.get_force())
        self.p4_mgr.clean(d=True)
        # 这个主要是拉ab包路径，ab包每次都拉到head
        self._fill_const_ab_p4_views()
        self.p4_mgr.sync_all(force=env_mgr.get_force())
        self.p4_mgr.clean(d=True)
        # 其他资源路径，不强更
        self._fill_other_p4_views()
        self.p4_mgr.sync_all(force=False)

    def _get_submit_p4_view_ab(self):
        package_type = env_mgr.get_package_type()
        files = [f"{config.p4_views.get('cdn_resources')}/{self._platform}/assetbundles/streaming"]
        ab_p4_views = [
            f"{config.p4_views.get('cdn_root')}/cooked/{self._platform}/assetbundles/streaming_stage/{package_type}",
            f"{config.p4_views.get('resources_root').format(branch='b')}/{self._b_path}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/streaming/",
        ]
        for view in ab_p4_views:
            dst = self.p4_mgr.parse_p4view_to_localpath(view)
            src = os.path.join(self.ab_path, "streaming")
            path_mgr.copy(src, dst[:-1], overwrite=True)
            files.append(view[:-1])
