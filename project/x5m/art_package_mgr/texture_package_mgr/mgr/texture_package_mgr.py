import re
import typing

from frame import *
from project.x5m.art_package_mgr.base_mgr.package_mgr import PackageMgr, ResourceTypeEnum, config, path_mgr, env_mgr


class TexturePackageMgr(PackageMgr):
    def __init__(self, **kwargs):
        super().__init__(ResourceTypeEnum.TEXTURE, **kwargs)
        self._execute_method = "H3DBuildTools.BuildAB"
        self._branch_name = env_mgr.get_branch_name()
        if self._branch_name == "master":
            self._resource_branch = "resources"
        else:
            self._resource_branch = f"b/{self._branch_name}"
        self._project_art_path = os.path.join(self._project_path, "Assets", "ab_resources")
        self._success_pattern = re.compile(r"^【Info】.*$|[/]([^/]+)\n$")
        self._resource_dir_prefix = f"{config.p4_views.get('branch_resource_src').format(branch=self._resource_branch)}/texture"
        self._c_resource_dir_prefix = f"{config.p4_views.get('branch_resource_src').format(branch=self._resource_branch)}/c"
        self._ab_view = f"{config.p4_views.get('branch_cdn_resources').format(branch=self._resource_branch)}/{self._platform}/assetbundles"
        self._const_ab_p4_views = [
            f"{config.p4_views.get('branch_cdn_resources').format(branch=self._resource_branch)}/{self._platform}/assetbundles/texture/",
            f"{config.p4_views.get('branch_cdn_resources').format(branch=self._resource_branch)}/{self._platform}/assetbundles/c/",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb"

    def _get_source_paths_by_id(self, id: str) -> str:
        pass

    def _filter_source_paths(self, source_paths: typing.List[str]) -> set:
        source_paths = [i.strip().lower() for i in source_paths if i.strip()]
        depots = set()
        for file in source_paths:
            if not file.lower().endswith(".png"):
                continue
            if not file.startswith(self._resource_dir_prefix) and not file.startswith(self._c_resource_dir_prefix):
                continue
            # 例如: //x5_mobile/mr/Resources/art_src/c/2205ipjzds/texture/transparent/pixel_1024/HYZH_bg11.png
            # 正则表达式匹配结果: c/2205ipjzds/texture
            # 意义: [^/]+表示匹配除了`/`以外的其他字符, 且该字符至少出现一次
            if file.startswith(self._c_resource_dir_prefix) and len(re.findall("c/[^/]+/texture", file)) == 0:
                continue
            depots.add(file)
        return depots

    def _fill_source_p4_views(self):
        source_paths = env_mgr.get_source_paths()
        source_paths = [i.strip().lower() for i in source_paths if i.strip()]
        views = []
        for path in source_paths:
            resource_path = path.replace(
                config.p4_views.get("branch_resource_src").format(branch=self._resource_branch),
                f"arttrunk/{config.arttrunk_branch}/mobile_dancer/arttrunk/client/Assets/ab_resources",
            )
            view = f"{path} //{self.p4.client}/{resource_path}"
            views.append(view)
        self.p4.set_view(views)

    def _update_p4(self) -> None:
        """
        更新p4
        """
        # 主要是拉资源路径，资源拉到对应的change
        self._fill_source_p4_views()
        self.p4_mgr.sync_all(changelist=env_mgr.get_changelist(), force=env_mgr.get_force())
        # 如果 ab 包的路径为空，则不需要处理，直接跳过
        if not self._const_ab_p4_views:
            return
        # 这个主要是拉ab包路径，ab包每次都拉到head
        self._fill_const_ab_p4_views()
        self.p4_mgr.sync_all(force=env_mgr.get_force())

    def _make_resouces_file(self):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            lines = []
            for path in env_mgr.get_source_paths():
                path: str
                path = path.replace(f"{config.p4_views.get('branch_resource_src').format(branch=self._resource_branch)}", "Assets/ab_resources")
                lines.append(path)
            f.write(",".join(lines))

    def _get_package_cmd(self) -> str:
        """
        获取打包命令
        """
        cmd_line = f"{self._platform.package_tool} "
        cmd_line += f"-quit -batchmode -projectPath {self._project_path} -logFile {self._log_file} "
        cmd_line += f"-executeMethod {self._execute_method} -buildTarget {self._platform} "
        cmd_line += f"outpath={self.ab_path} "
        return cmd_line.replace("/", "\\")

    def _calculate_package_result(self):
        """
        计算打包结果
        """
        reports = path_mgr.glob(self._report_path, "*.txt")
        if not reports:
            raise PyframeException(f"获取打包结果失败，请检查查看打包日志")
        # 做判断，只有一个
        report = reports[-1]
        with open(report, "r", encoding="utf-8") as f:
            content = f.readlines()
            # 解析打包结果
            report_detail = self._get_report_detail(content)
            success_ids = report_detail.get("success_ids")
            report_detail["success_ids"] = [id for id in success_ids if id != "shaders"]

            # 记录打包结果
            log.debug(f"platform: {self._platform}, report_detail: {report_detail}")
            env_mgr.set_report(report=report_detail)

    def _get_submit_p4_view_ab(self):
        files = set()
        # 可以直接读取成功的资源
        with open(self._resources_path, "r", encoding="UTF-8") as f:
            file_paths = f.read()
        resources = file_paths.split(",")
        for submit_file in resources:
            submit_file: str
            submit_file = submit_file.lower().replace("assets/ab_resources", self._ab_view)
            file, _ = os.path.splitext(submit_file)
            file = f"{file}_mat"
            files.add(file)
        return list(files)
