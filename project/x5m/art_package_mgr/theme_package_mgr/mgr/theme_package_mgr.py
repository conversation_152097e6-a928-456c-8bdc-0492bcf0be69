import re
from frame import *

from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import *


class ThemePackageMgr(DistributePackageMgr):
    
    def __init__(self, **kwargs) -> None:
        super().__init__(kind=ResourceTypeEnum.THEME, **kwargs)
        self._resource_dir_prefix = "//美术资源/炫舞手游-ui/6-UI常规资源/新常规资源/主题表情"
        self._success_pattern = re.compile(r"\[info\]\[UIBuild\]\s+.*?uiatlasexprssion(theme\d+).bundle")
        self._execute_method = "UIBuild.JenkinsBuildUI"
        # UIBuild是从客户端移植过来的接口，目前不支持传参指定输出路径，输出路径是写死在代码中的
        self._ab_path = os.path.join(self._workspace, f"arttrunk/x5mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/uiatlas/uiatlasexprssion")
        self._const_source_p4_views = [
        ]
        self._const_ab_p4_views = [
            f"{config.p4_views.get('resources_root')}/uislice/UIAtlasExprssion/",
        ]
        self._webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"
        self.__sources_paths: List[str] = []
        self.__last_branch = None

    def _clean_other(self):
        self._safe_clean(os.path.join(self._project_path, "Assets/Res"), "开始清理图集")

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        depots = set()
        for file in source_paths:
            file: str
            if not file.startswith(self._resource_dir_prefix):
                continue
            file_relative_path = file.replace(self._resource_dir_prefix, "")
            package_list = file_relative_path.split("/")
            if len(package_list) >= 3:
                source_path = f"{self._resource_dir_prefix}/{package_list[1]}/"
                depots.add(source_path)
        return depots
    
    def _fill_const_ab_p4_views(self):
        views = []
        for view in self._const_ab_p4_views:
            views.append(view.format(branch='Resources'))
            views.append(view.format(branch=f'b/{self.last_branch}'))
        self.p4_mgr.set_p4_views(views)
    
    def _update_config_git(self) -> None:
        master_git_mgr = X5mGitMgr(self._workspace, "x5mobile", "master")
        master_git_mgr.update(clean=True)
        last_git_mgr = X5mGitMgr(self._workspace, "x5mobile", self.last_branch)
        last_git_mgr.update(clean=True)
        self._git_mgrs = [master_git_mgr, last_git_mgr]
    
    @property
    def last_branch(self):
        if self.__last_branch:
            return self.__last_branch
        last_branch = env_mgr.get_last_branch()
        if last_branch:
            self.__last_branch = last_branch
            return last_branch
        master_git_mgr = X5mGitMgr(self._workspace, "x5mobile", "master")
        git_last_branch = master_git_mgr.get_last_branch()
        if not git_last_branch:
            raise PyframeException("Get git last branch error")
        p4_last_branch = self.p4_mgr.get_last_branch()
        if not p4_last_branch:
            raise PyframeException("Get p4 last branch error")
        if git_last_branch != p4_last_branch:
            raise PyframeException(f"Git last branch {git_last_branch} not equal to p4 last branch {p4_last_branch}")
        self.__last_branch = git_last_branch
        env_mgr.set_last_branch(git_last_branch)
        return git_last_branch
    
    def _package(self):
        # 构建图集工程和图集
        self.__package_atlas()
        # 生成budle
        super()._package()
    
    def __package_atlas(self):
        for path in env_mgr.get_source_paths():
            theme_id = path.split('/')[-2]
            self.__package_atlas_by_id(theme_id)
    
    def __package_atlas_by_id(self, theme_id: str):
        log_file = os.path.join(self._log_root, f"atlas_{self._platform}_{env.pipeline.build_num()}_{theme_id}.log")
        cmd_line = self.__get_package_atlas_cmd(theme_id, log_file)
        ret, _ = cmd.run_shell(
            cmds=[cmd_line],
            workdir=self._workspace
        )
        if ret != 0:
            advance.raise_unity_log_exception(log_path=log_file)
            raise PyframeException(f"生成图集命令执行失败，错误码:{ret}")
    
    def __get_package_atlas_cmd(self, theme_id: str, log_file: str) -> str:
        """
        获取打包命令
        """
        atlas_execute_method = "PackAtlasTool.CreateAtlas"
        atlas_imgScale = "0.4"
        imgFolder_src = self.p4_mgr.parse_p4view_to_localpath(f"{self._resource_dir_prefix}/{theme_id}")
        exportAtlasPath = f"Assets/Res/Editor/resources/Art/UIAtlas/UIAtlasExprssion/UIAtlasExprssion{theme_id}"
        self.__sources_paths.append(exportAtlasPath)
        imgFolder = os.path.join(self._project_path, exportAtlasPath)
        path_mgr.xcopy(imgFolder_src, imgFolder, dst_is_file=False)
        atlasprojPath = os.path.join(self._project_path, f"{exportAtlasPath}/UIAtlasExprssion{theme_id}.atlasproj")
        
        cmd_line = f'{self._platform.package_tool} '
        cmd_line += f'-quit -batchmode -projectPath {self._project_path} -logFile {log_file} '
        cmd_line += f'-buildTarget {self._platform} -executeMethod {atlas_execute_method} -imgFolder {imgFolder} '
        cmd_line += f'-atlasprojPath {atlasprojPath} -imgScale {atlas_imgScale} -exportAtlasPath {exportAtlasPath} '
        return cmd_line.replace("/", "\\")

    def _make_resouces_file(self):
        if not path_mgr.exists(self._report_root):
            path_mgr.mkdir(self._report_root)
        with open(self._resources_path, "w", encoding="utf-8") as f:
            f.write(','.join(self.__sources_paths))
    
    def _get_report_detail(self, read_lines: List[str]) -> dict:
        """
        获取打包结果
        """
        success_pattern = self._success_pattern
        fail_patterns = self._fail_pattern
        total_count = 0
        succ_count = 0
        failed_count = 0
        error_count = 0
        success_ids = set()
        error_ids = set()
        error_info = set()
        index = 0
        while index < len(read_lines):
            if read_lines[index].startswith("[info][UIBuild]"):
                if success_pattern:
                    suc = re.findall(success_pattern, read_lines[index].strip())
                    print(read_lines[index].strip(), success_pattern, suc)
                    if suc:
                        succ_count += 1
                        total_count += 1
                        success_ids.update(suc)
            elif read_lines[index].startswith("[Info]Build Failed Count"):
                first_index = read_lines[index].find("==", 6)
                second_index = read_lines[index].find("]", 6)
                failed_num = int(read_lines[index][first_index + 2 : second_index])
                failed_count += failed_num
            elif read_lines[index].startswith("[Error]"):
                error_count += 1
                for fail_pattern in fail_patterns:
                    fail = re.findall(fail_pattern, read_lines[index])
                    if fail:
                        error_ids.update(fail)
                        error_info.add(read_lines[index][7:])
            index += 1
        # 将失败的从成功的列表中去除
        temp_list = set()
        for success_id in success_ids:
            if success_id not in error_ids:
                temp_list.add(success_id)

        # 将只有extend成功的id过滤掉
        final_success_ids = self._filter_extend(temp_list, error_ids)
        report_detail = {
            "total_count": total_count,
            "succ_count": succ_count,
            "failed_count": failed_count,
            "error_count": error_count,
            "success_ids": list(final_success_ids),
            "error_ids": list(error_ids),
            "error_infos": list(error_info)
        }
        return report_detail
    
    def _fill_dist_p4_views(self):
        views = set()
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    views.add(self._get_file_root(distribute_type, path_root))
        self.p4_mgr.add_p4_views(list(views))
    
    def _dist_ab_file(self):
        for distribute_type, distribute_id_path_roots in self._distribute_map.items():
            for id, path_roots in distribute_id_path_roots:
                for path_root in path_roots:
                    view = self._get_filepath(id, distribute_type, path_root)
                    dst = self.p4_mgr.parse_p4view_to_localpath(view)
                    src = os.path.join(self.ab_path, f"uiatlasexprssion{id}.bundle")
                    path_mgr.copy(src, dst, overwrite=True)
                    self._submit_ab_views.append(view)
    
    def _copy_product(self):
        for id in env_mgr.get_report()["success_ids"]:
            id: str = id.title()
            src = os.path.join(self._project_path, f"Assets/Res/Editor/resources/Art/UIAtlas/UIAtlasExprssion/UIAtlasExprssion{id}")
            if not path_mgr.exists(src):
                raise PyframeException(f"生成图集工程失败,失败图集:{id}")
            atlasproj_view = [
                f"{config.p4_views['resources_root']}/uislice/UIAtlasExprssion/UIAtlasExprssion{id}/".format(branch='Resources'),
                f"{config.p4_views['resources_root']}/uislice/UIAtlasExprssion/UIAtlasExprssion{id}/".format(branch=f'b/{self.last_branch}'),
            ]
            p4_dst = self.p4_mgr.parse_p4view_list_to_localpath(atlasproj_view)
            git_dst = [
                f"{self._workspace}/x5mobile/master/mobile_dancer/trunk/client/Assets/Res/Editor/resources/Art/UIAtlas/UIAtlasExprssion/UIAtlasExprssion{id}",
                f"{self._workspace}/x5mobile/{self.last_branch}/mobile_dancer/trunk/client/Assets/Res/Editor/resources/Art/UIAtlas/UIAtlasExprssion/UIAtlasExprssion{id}",
            ]
            for filename in os.listdir(src):
                if filename.startswith("Theme") and not filename.endswith(".meta") or filename == f"UIAtlasExprssion{id}.atlasproj":
                    for dst in p4_dst:
                        path_mgr.copy(os.path.join(src, filename), os.path.join(dst, filename), True)
                elif filename.endswith(".meta") and not filename.startswith(f"UIAtlasExprssion{id}.atlasproj"):
                    for dst in git_dst:
                        path_mgr.copy(os.path.join(src, filename), os.path.join(dst, filename), True)
                        path_mgr.copy(os.path.join(src, filename[:-5]), os.path.join(dst, filename[:-5]), True)
            for dst in git_dst:
                path_mgr.copy(f"{src}.meta", f"{dst}.meta", False)

    def _get_file_root(self, dis_type: DisTributeEnum, root: str):
        if dis_type == DisTributeEnum.DEFAULT:
            return f"{root}/ResourcePublish/CDN/SourceFiles/{self._platform}/assetbundles/art/uiatlas/uiatlasexprssion/"
        if dis_type == DisTributeEnum.HOTFIX:
            return f"{root}/client/{self._platform}/assetbundles/art/uiatlas/uiatlasexprssion/"
        raise PyframeException(f"Invalid distribute type {dis_type}, theme only supported distribute to hotfix")
    
    def _get_filepath(self, id: str, dis_type: DisTributeEnum, root: str):
        return f"{self._get_file_root(dis_type, root)}uiatlasexprssion{id}.bundle"
    
    def _default_distribute(self) -> List[str]:
        return [
            f"{config.p4_views['resources_root'].format(branch='Resources')}",
            f"{config.p4_views['resources_root'].format(branch=f'b/{self.last_branch}')}",
        ]

    def _submit_git(self):
        # git只用提交一次
        if self._platform != PlatformEnum.IOS:
            return
        for git_mgr in self._git_mgrs:
            git_mgr.submit_path(self._desc, os.path.join(git_mgr.workspace, "mobile_dancer/trunk/client/Assets/Res/Editor/resources/Art/UIAtlas/UIAtlasExprssion"))

    def _get_submit_p4_view_other(self):
        if self._platform != PlatformEnum.IOS:
            return []
        files = []
        for id in env_mgr.get_report()["success_ids"]:
            id: str = id.title()
            atlasproj_view = [
                f"{config.p4_views['resources_root']}/uislice/UIAtlasExprssion/UIAtlasExprssion{id}/".format(branch='Resources'),
                f"{config.p4_views['resources_root']}/uislice/UIAtlasExprssion/UIAtlasExprssion{id}/".format(branch=f'b/{self.last_branch}'),
            ]
            self.p4_mgr.add_p4_views(atlasproj_view)
            files.extend(atlasproj_view)
        return files