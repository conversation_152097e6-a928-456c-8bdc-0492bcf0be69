import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.json.JsonSlurperClassic

def json_load_output(raw_out) {
    println raw_out
    def out = raw_out.trim().split('\n')
    out = out.last()
    return new JsonSlurperClassic().parseText(out)
}

PLATFORM_INFO = [:]
PYTHON = 'pipeline-python'
RETURN_CODE = 0

node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent none
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder(logRotator(numToKeepStr: env.numsToKeepStr, daysToKeepStr: env.daysToKeepStr))
    }
    parameters {
        text(name: 'changelist', defaultValue: '0', description: '输入开始打包的changelist')
        booleanParam(name: 'p4_force', defaultValue: false, description: '是否强更P4')

//         booleanParam(name: 'dev', defaultValue: true, description: '是否为开发模式')
//         text(name: 'force_ids', defaultValue: '', description: '强制打包id列表，多个id使用英文半角逗号隔开')
    }
    // 因为四点到五点p4需要备份数据，流水线会执行失败
    triggers {
        cron(env.BUILD_PERIOD)
    }
    environment {
        KIND = 'timeline_normal'
        PYTHONIOENCODING = 'utf-8'
    }
    stages {
        stage("美术资源打包") {
            matrix {
                agent {
                    node {
                        label "timeline_${PLATFORM}"
                        customWorkspace "timeline_normal_${PLATFORM}"
                    }
                }
                axes {
                    axis {
                        name "PLATFORM"
                        values "android", "ios"
                    }
                }
                stages {
                    stage('更新流水线依赖') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    PLATFORM_INFO[PLATFORM] = [:]
                                    PLATFORM_INFO[PLATFORM].WORKSPACE = env.WORKSPACE
                                    PLATFORM_INFO[PLATFORM].NODE_NAME = env.NODE_NAME
                                    println(env.NODE_NAME)
                                    bat(
                                            script: "\"$PYTHON\" -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn",
                                            returnStdout: true
                                    )
                                    bat(
                                            script: "\"$PYTHON\" -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn",
                                            returnStdout: true
                                    )
                                }
                            }
                        }
                    }
                    stage("获取处理任务ID") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            workspace: env.WORKSPACE,
                                            platform : PLATFORM,
                                            p4_force : params.p4_force
                                    ]
                                    def out = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} |\"${PYTHON}\" x5m_cli.py --kind=$KIND --method=get_ins_id'",
                                            encoding: 'utf8',
                                            returnStdout: true)
                                    out = out.trim().split('\n')
                                    PLATFORM_INFO[PLATFORM].INS_ID = new JsonSlurper().parseText(out.last())
                                }
                            }
                        }
                    }

                    stage('展示系统信息') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID
                                    ]
                                    powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=prepare'",
                                            encoding: 'utf8'
                                    )
                                }
                            }
                        }
                    }
                    stage('获取打包工具repo') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=update_or_clone_repo'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                }
                            }
                        }
                    }
                    stage('判断是否需要打包') {
                        steps {
                            dir('pyframe-pipeline') {
                                script {
                                    def data = [
                                            ins_id    : PLATFORM_INFO[PLATFORM].INS_ID,
                                            changelist: params.changelist
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=check_package'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    def package_info = json_load_output(output)
                                    if (package_info['package'] in ['false', false]) {
                                        currentBuild.result = 'ABORTED'
                                        throw new org.jenkinsci.plugins.workflow.steps.FlowInterruptedException(hudson.model.Result.ABORTED)
                                    }
                                    PLATFORM_INFO[PLATFORM].PACKAGE_INFO = package_info

                                }
                            }
                        }
                    }
                    stage("清理上次打包资源") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=clean_resource'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                }
                            }
                        }
                    }
                    stage("获取P4资源") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    data << PLATFORM_INFO[PLATFORM].PACKAGE_INFO
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=get_p4_resource'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].P4_RESOURCE = json_load_output(output)
                                }
                            }
                        }
                    }
                    stage("准备打包资源") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    // 数据太长, 使用文件传输
                                    // data << PLATFORM_INFO[PLATFORM].P4_RESOURCE
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=prepare_resource'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].PACKAGE_RESOURCE = json_load_output(output)
                                }
                            }
                        }
                    }
                    stage("打包") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
//                                    data << PLATFORM_INFO[PLATFORM].PACKAGE_RESOURCE
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=package'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].PACKAGE_RESULT = json_load_output(output)
                                }
                            }
                        }
                    }
                    stage("计算打包结果") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    data << PLATFORM_INFO[PLATFORM].PACKAGE_RESULT
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=calculate_package_result'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    def PACKAGE_DETAIL = json_load_output(output)
                                    PLATFORM_INFO[PLATFORM].PACKAGE_DETAIL = PACKAGE_DETAIL
                                    if (PACKAGE_DETAIL['report_detail']['succ_count'] == PACKAGE_DETAIL['report_detail']['total_count'] && !(PACKAGE_DETAIL['report_detail']['succ_count'] in [0, '0'])) {
                                        RETURN_CODE += 1
                                    }
                                }
                            }
                        }
                    }
                    stage("配置需要提交的文件") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=get_need_submit_files'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].NEED_SUBMIT_FILES = json_load_output(output)
                                }
                            }
                        }
                    }
                    stage("复制包到P4路径") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
//                                    data << PLATFORM_INFO[PLATFORM].NEED_SUBMIT_FILES
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=copy_package_to_p4_path'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].NEED_SUBMIT_FILES = json_load_output(output)
                                }
                            }
                        }
                    }
                    stage("提交文件") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=submit_files_to_p4'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].SUBMIT_INFO = json_load_output(output)
                                }
                            }
                        }
                    }
                    stage("持久化配置") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=save_result'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                    PLATFORM_INFO[PLATFORM].PERSISTENCE_INFO = json_load_output(output)
                                }
                            }
                        }
                    }
                    stage("提交GIt") {
                        steps {
                            dir("pyframe-pipeline") {
                                script {
                                    def data = [
                                            ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                    ]
                                    def output = powershell(
                                            script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=submit_git'",
                                            encoding: 'utf8',
                                            returnStdout: true
                                    )
                                }
                            }
                        }
                    }

                }
                post {
                    always {
                        dir("pyframe-pipeline") {
                            script {
                                def data = [
                                        ins_id: PLATFORM_INFO[PLATFORM].INS_ID,
                                ]
                                powershell(
                                        script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=always'",
                                        encoding: 'utf8',
                                        returnStdout: true
                                )
                            }
                        }
                    }
                }
            }
            post {
                always {
                    script {
                        if (currentBuild.result != 'ABORTED') {
                            if (RETURN_CODE == 0) {
                                currentBuild.result = 'FAILURE'
                            } else if (RETURN_CODE == 1) {
                                currentBuild.result = 'UNSTABLE'
                            } else if (RETURN_CODE == 2) {
                                currentBuild.result = 'SUCCESS'
                            }
                        }
                    }
                }
            }
        }
    }
    post {
        unstable {
            // 默认路径为 {slave workspace}/workspace/job_name  所以需要加两个 /..
            node(PLATFORM_INFO['ios'].NODE_NAME) {
                dir("${PLATFORM_INFO['ios'].WORKSPACE}\\pyframe-pipeline") {
                    script {
                        bat(script: "${PYTHON}  x5m_cli.py --kind=$KIND --method=on_unstable", encoding: "utf8")
                    }
                }
            }
        }
        success {
            node(PLATFORM_INFO['ios'].NODE_NAME) {
                dir("${PLATFORM_INFO['ios'].WORKSPACE}\\pyframe-pipeline") {
                    script {
                        bat(script: "${PYTHON}  x5m_cli.py --kind=$KIND --method=on_success", encoding: "utf8")
                    }
                }
            }
        }
        failure {
            node(PLATFORM_INFO['ios'].NODE_NAME) {
                dir("${PLATFORM_INFO['ios'].WORKSPACE}\\pyframe-pipeline") {
                    script {
                        bat(script: "${PYTHON}  x5m_cli.py --kind=$KIND --method=on_failure", encoding: "utf8")
                    }
                }
            }
        }
    }
}
