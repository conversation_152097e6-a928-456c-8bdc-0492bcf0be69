import logging
import os
import subprocess
from typing import Iterable

from frame.path_mgr.path_mgr import path_mgr
from project.x5m.art_package_mgr.npc_package_mgr.mgr.npc_package_mgr import (
    NpcPackageMgr,
)
from project.x5m.base_mgr import BaseWorkflow
from project.x5m.base_mgr.package_mgr import (
    env_mgr,
    X5mGitMgr,
    PlatformEnum,
)


class TimelineBase(BaseWorkflow):
    append_p4_base_view_map = False

    class P4Setting(BaseWorkflow.P4Setting):
        customize_rules = {
            "newmulti": "//x5mplan/resmg/timeline/newmulti_dependence.xlsx",
            "huodong": "//x5mplan/resmg/timeline/huodong_dependence.xlsx",
        }

    def clean_resource(self, **kwargs):
        self._safe_clean(self._report_root, "开始清理打包日志")
        os.makedirs(self._report_root, exist_ok=True)
        art_resource_path = os.path.join(self._project_art_path, "timeline")
        self._safe_clean(art_resource_path, f"清理{art_resource_path}")
        if not os.path.exists(art_resource_path):
            os.makedirs(art_resource_path, exist_ok=True)
        self.p4_mgr.set_p4_views([], format_view=False)
        return kwargs

    def get_p4_rule_cdn(self):
        return [("*", "")]

    def update_or_clone_repo(self, *args, **kwargs) -> None:
        grm_arttrunk = X5mGitMgr(self.workspace, "arttrunk", "release")
        grm_arttrunk.update(clean=False, commit_id="origin/release", depth=1)
        self.attrunk_client_path = os.path.join(
            grm_arttrunk.workspace, "mobile_dancer", "arttrunk", "client"
        )
        cdn_git = X5mGitMgr(self._workspace, "x5mconfig", "cdn")
        cdn_git.update(clean=True)

    def check_package(self, changelist: int = 0, force_ids=(), *args, **kwargs) -> dict:
        """
        检查是否需要打包
        Args:
            stop_current_build: 是否停止当前构建
            force_by_id: 是否通过id强制打包
        """
        if force_ids:
            files = self._check_package_by_ids(force_ids)
            logging.info(f"{self._platform}平台强制打包id列表: {', '.join(force_ids)}")
            return {"package": True, "changelist": changelist, "files": files}
        changelist = int(changelist) or self.changelist
        if changelist:
            changelist = int(changelist) - 1
        else:
            last_changelist = (
                self._database_.get_last_changelist(platform=self.platform)
                or self.default_changelist
            )
            if not last_changelist:
                raise ValueError("第一次打包， 请指定first_changelist/changelist")
            changelist = int(last_changelist) + 1
        # 判断指定路径下的资源是否有更新
        files, latest_changelist, users = self._check_package_by_changelist(changelist)
        files = self._filter_source_paths(files)
        if not files:
            info = f"{self.platform}平台{self.kind}资源无更新，无需打包"
            logging.info(info)
            return {"package": False, "changelist": changelist, "files": []}
        files = list(files)
        logging.info(
            f"{self._platform}平台{self.kind}资源有更新，需要打包; changelist:{changelist}"
        )
        env_mgr.set_source_paths(source_paths=files)
        env_mgr.update_users(users=list(users))
        env_mgr.set_changelist(changelist=latest_changelist)
        env_mgr.set_need_package(True, self.platform)
        return {"package": True, "changelist": latest_changelist, "files": files}

    def get_customized_rule_local_paths(self):
        views = self.p4_mgr.get_p4_view()
        p4_depots = list(self.P4Setting.customize_rules.values())
        p4_view_map = self.generate_p4_view_map(p4_depots, view_type="file")

        self.p4_mgr.set_p4_views(p4_view_map, format_view=False)
        self.p4_mgr.raw_p4.run_sync(p4_depots)
        # 还原views
        self.p4_mgr.set_p4_views(views, format_view=False)
        local_paths = {}
        for key, depot in self.P4Setting.customize_rules.items():
            local_path = self.p4_mgr.get_files_local_path(depot)
            local_paths[key] = local_path
        return local_paths

    def get_huodong_p4_views(self, dependence_local_path):
        p4_view_set = []
        excel_data = self._excel_to_dict(dependence_local_path)
        # 原fill_npc_p4_views函数的实现
        paths = self._parse_huo_dong_path(excel_data)
        # 原fill_p4_view函数的实现
        p4_view_set.extend(paths)
        return p4_view_set

    def get_newmulti_p4_views(self, dependence_local_path):
        excel_data = self._excel_to_dict(dependence_local_path)
        # 不处理zhouye_nptao
        excel_data.pop("zhouye_nptao")
        p4_view_set = []
        path_mgr.xcopy(
            src=dependence_local_path,
            dst=os.path.join(self._project_path, "Assets/resources/Art/timeline"),
            dst_is_file=False,
            overwrite=True,
        )
        paths = self._parse_path(excel_data)
        p4_view_set.extend(paths)

        bind_timeline_paths = self._parse_bind_timeline_path(dependence_local_path)
        p4_view_set.extend(bind_timeline_paths)
        return p4_view_set

    def _excel_to_dict(self, file_path: str) -> dict:
        """
        {
            "huodong": [
                {
                    "名称": "活动9330002",
                    "路径1": "huodong/9330002",
                    "路径2": "newmulti/1048577",
                    "路径3": "qinglvtao/1048575",
                    "路径4": "qinglvtao/1048576",
                    "路径5": "npc/models/npc_0408857701",
                    "路径6": "npc/models/npc_1408857701",
                    "路径7": "npc/models/npc_1408857601",
                    "路径8": "npc/models/npc_1408857501",
                    "路径9": "npc/models/npc_0408857601",
                    "路径10": "npc/models/npc_0408857501",
                }
            ]
        }
        """
        import pandas as pd

        xls = pd.ExcelFile(file_path)
        sheet_names = xls.sheet_names
        excel_data = {}
        for sheet in sheet_names:
            if sheet == "bindTimelines":
                continue
            df = pd.read_excel(file_path, sheet_name=sheet, header=0)
            excel_data[sheet.strip()] = df.to_dict(orient="records")
        return excel_data

    def _parse_bind_timeline_path(self, file_path) -> list:
        import pandas as pd

        df = pd.read_excel(file_path, sheet_name="bindTimelines")
        timeid_data = df["TimelineID"].apply(
            lambda x: [tuple(i.strip().split("/")) for i in x.split(",")]
        )
        return timeid_data.tolist()

    def _parse_huo_dong_path(self, path_data: dict) -> list:
        path = []
        for _, data in path_data.items():
            if not data:
                continue
            full_path = []
            for index in range(len(data)):
                full_path.extend(list(data[index].values())[1:])
            path.extend(full_path)
        return path

    def _parse_path(self, path_data: dict) -> list:
        path = []
        for _, data in path_data.items():
            if not data:
                continue
            full_path = []
            path_prefix = list(data[0].values())[1:]
            for item in data[1:]:
                # 重新组织路径后, [["newmulti/123"], ["qinglvtao/456"], ["qinglvtao/789"]]
                full_path.append(
                    set(
                        zip(path_prefix, [str(int(x)) for x in list(item.values())[1:]])
                    )
                )
            path.extend(full_path)
        return path

    def get_p4_rule_detail(self):
        local_path = self.get_customized_rule_local_paths()
        p4_view_set_huodong = self.get_huodong_p4_views(local_path["huodong"])
        p4_view_set_huodong = filter(
            lambda x: len(x.split("/")) in [2, 3], p4_view_set_huodong
        )
        p4_view_set_huodong = set(tuple(x.split("/")) for x in p4_view_set_huodong)
        p4_view_set_newmulti = self.get_newmulti_p4_views(local_path["newmulti"])
        p4_view_set_newmulti.append(p4_view_set_huodong)
        return p4_view_set_newmulti

    def copy_file_to_cdn(self, ff, _target, **kwargs):
        _, target = ff.split(f"{self.P4Setting.copy_split()}/")
        if target.startswith("art/timeline/actions_anim_expression"):
            target = target.replace(
                "art/timeline/actions_anim_expression",
                "art/role/actions/actions_anim_expression",
            )
        elif target.startswith("art/timeline/actions_anim"):
            target = target.replace(
                "art/timeline/actions_anim", "art/role/actions/actions_anim"
            )
        elif target.startswith("art/timeline/link"):
            target = target.replace("art/timeline/link", "art/role/link")

        cdn_path = self.P4Setting.cdn_prefix_base.format(platform=self.platform)
        p4_path = f"{self.root}/{cdn_path}/{target}"
        real_path = self.copy_file_to_p4_target(p4_path, ff)
        return real_path

    def copy_files_to_custom(self, submit_files):
        cdn_path = self.P4Setting.cdn_prefix_base.format(platform=self.platform)
        depot_mp4_timeline = f"{cdn_path}/art/mp4/timeline"
        local_mp4_timeline = self.p4_mgr.get_files_local_path(depot_mp4_timeline)
        unity_video_path = os.path.join(self.unity_out, "art/timeline/video")
        need_submit_files = set()
        for file in submit_files:
            timeline_id = file.split("/")[-1]
            mp4_src = os.path.join(unity_video_path, timeline_id)
            if not path_mgr.exists(mp4_src):
                continue
            ab_video_root = os.path.join(local_mp4_timeline, timeline_id, "video")
            path_mgr.xcopy(mp4_src, ab_video_root, dst_is_file=False)
            need_submit_files.add(ab_video_root)
            xml_src = os.path.join(
                ab_video_root, f"{timeline_id}/{timeline_id}_video.xml"
            )
            if not path_mgr.exists(xml_src):
                continue
            xml_dst = os.path.join(self._workspace, "x5mconfig/cdn/config/shared/video")
            path_mgr.xcopy(xml_src, xml_dst, dst_is_file=False)
        return list(need_submit_files)

    def get_custom_p4_views(self, depot_files: Iterable[str], *args, **kwargs):
        """

        :param depot_files: ['//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1046591/', ]
        :return:
        """
        # {("qinglvtao", "1046591"): "//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1046591/"), }
        relpath_map = dict(
            (tuple(os.path.relpath(p, f"//{self.resource_path}").split(os.sep)), p)
            for p in depot_files
        )
        p4_rule_detail = self.get_p4_rule_detail()
        rule_result = {}
        for depot_item in relpath_map.keys():
            for p4_rule in p4_rule_detail:
                if depot_item in p4_rule and depot_item not in relpath_map:
                    rule_result[depot_item] = p4_rule

        # 如果有活动或者newmulti, 则补充相关的p4_views
        p4_views = []
        if rule_result:
            for resource_list in rule_result.values():
                for item in resource_list:
                    _depot_path = "/".join(item)
                    if _depot_path.startswith("npc"):
                        p4_view = (
                            f"//x5_mobile/mr/art_release/art_src/npc/{_depot_path}"
                        )
                    else:
                        p4_view = f"{self._resource_dir_prefix}/{_depot_path}"
                    v = self.format_p4_depot(p4_view).format(platform=self.platform)
                    p4_views.append(f"//{v}/... //{self.p4_mgr.client}/{v}/...")

        # 补充special的p4_views
        for v in relpath_map.values():
            v = self.format_p4_depot(v)
            p4_views.append(f"//{v}/... //{self.p4_mgr.client}/{v}/...")

        p4_depots = (
            "//x5m/res/cdn/cooked/{platform}/assetbundles/art/timeline/...",
            "//x5m/res/cdn/cooked/{platform}/assetbundles/art/mp4/timeline/...",
            "//x5m/res/cdn/cooked/{platform}/assetbundles/art/role/actions/actions_anim/...",
            "//x5m/res/cdn/cooked/{platform}/assetbundles/art/role/actions/actions_anim_expression/...",
            "//x5m/res/cdn/cooked/{platform}/assetbundles/art/role/link/...",
        )
        for depot in p4_depots:
            v = self.format_p4_depot(depot).format(platform=self.platform)
            p4_views.append(f"//{v}/... //{self.p4_mgr.client}/{v}/...")
            p4_views.append(f"//{v} //{self.p4_mgr.client}/{v}")
        return p4_views

    def _check_package_by_changelist(self, changelist) -> bool:
        _ = NpcPackageMgr().check_package(
            stop_current_build=False, force_by_id=False, changelist=changelist
        )
        # npc_package = NpcPackageMgr(client="cuizemin_test", root="F:/cuizemin_test", workspace="F:/cuizemin_test").check_package(stop_current_build=False, force_by_id=False)
        return super()._check_package_by_changelist(changelist)

    def get_depot_bind_resources(self, depot_files: Iterable[str]):
        p4_views_map = set()
        depot_bind_files = set()
        for depot in depot_files:
            if "timeline/shenqi/" in depot:
                v = "//x5_mobile/mr/art_release/art_src/timeline/shenqi/shenqi_common_action/"
                v = self.format_p4_depot(v)
                p4_views_map.add(f"//{v}/... //{self.p4_mgr.client}/{v}/...")
                depot_bind_files.add(f"//{v}")

            elif "timeline/dongzuoshouchi" in depot:
                v = "//x5_mobile/mr/art_release/art_src/timeline/dongzuoshouchi/shouchi_common_action/"
                v = self.format_p4_depot(v)
                p4_views_map.add(f"//{v}/... //{self.p4_mgr.client}/{v}/...")
                depot_bind_files.add(f"//{v}")

        return list(p4_views_map), list(depot_bind_files)

    def _get_source_paths_by_id(self, id: str) -> str:
        data_id = id.split("/")[-1]
        if "mingpian" in id:
            tail = "11"
        else:
            tail = "01"
        return f"{self._resource_dir_prefix}/{id}/{data_id}{tail}.prefab"

    def submit_git(self):
        # git只用提交一次
        if PlatformEnum(self.platform) != PlatformEnum.IOS:
            return
        cdn_git = X5mGitMgr(self._workspace, "x5mconfig", "cdn")
        files = cdn_git.submit_path(
            self.get_desc(),
            [
                os.path.join(self._workspace, "x5mconfig/cdn/config/shared/video"),
                os.path.join(self._workspace, "x5mconfig/cdn/config/shared/double_fly_actions"),
                os.path.join(self._workspace, "x5mconfig/cdn/config/shared/float_cloud_actions"),
             ]
        )
        if files:
            p = subprocess.Popen('git rev-parse --short HEAD', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding='utf-8')
            commit_id, _ = p.communicate()
            if p.returncode != 0:
                logging.warning("get commit failed")
            commit_info = {
                "files": list(files),
                "commit_id": commit_id.strip()
            }
            env_mgr.set_git_commit_info(commit_info, self.platform)

            return commit_info
        return {}

    @classmethod
    def get_extend_msg(cls, platform)->str:
        if not platform:
            return ""
        if str(platform).lower() != 'ios':
            return ""
        commit_info = env_mgr.get_git_commit_info(platform)
        if commit_info:
            return f"\n**Git**: commit_id \t {commit_info['commit_id']} files\t: {commit_info['files']}\n"
        return ""

