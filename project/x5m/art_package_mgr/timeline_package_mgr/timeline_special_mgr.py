from functools import reduce
from typing import List

from frame import *
from .timeline_base import TimelineBase


class TimelineSpecialMgr(TimelineBase):
    kind = "timeline_special"
    resource_path = "x5_mobile/mr/art_release/art_src/timeline"
    p4_base_views = (
        # "//x5_mobile/mr/art_release/art_src/timeline/common/",
        "//x5_mobile/mr/art_release/art_src/role/cloth_show/",
    )

    default_changelist = 462963
    execute_method = "H3DBuildTools.BuildTimeline"
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"

    def workflow(self):
        return (
            ("展示系统信息", self.prepare),
            ("获取打包工具repo", self.update_or_clone_repo),
            ("判断是否需要打包", self.check_package),
            ("清理上次打包资源", self.clean_resource),
            ("获取P4资源", self.get_p4_resource),
            ("准备打包资源", self.prepare_resource),
            ("打包", self.package),
            ("计算打包结果", self.calculate_package_result),
            ("配置需要提交的文件", self.get_need_submit_files),
            ("复制包到P4路径", self.copy_package_to_p4_path),
            ("提交文件", self.submit_files_to_p4),
            ("持久化配置", self.save_result),
            ("提交GIt", self.submit_git),
            ("备份+上传", self.always),
        )

    def _filter_source_paths(self, source_paths: List[str]) -> set:
        source_paths = list(
            filter(lambda f: f.startswith(f"//{self.resource_path}"), source_paths)
        )
        depots = set()
        p4_rule = self.get_p4_rule_detail()
        p4_timeline_rule = reduce(lambda x, y: set(x).union(set(y)), p4_rule, set())
        # 活动资源前缀
        p4_timeline_rule_prefix = set(r[0] for r in p4_timeline_rule)

        for file in source_paths:
            relpath = os.path.relpath(file, f"//{self.resource_path}")
            package_list = relpath.split(os.sep)
            if package_list[0] not in p4_timeline_rule_prefix:
                continue
            if len(package_list) < 2:
                continue
            # 增加双飞类型8
            if len(package_list[1]) in [6, 7, 8] and package_list[1].isdigit():
                daifei_common_action = "shuangfei/daifei_common_action/"
                if package_list[0] == "shuangfei":
                    depots.add(f"//{self.resource_path}/{daifei_common_action}")

                shouchi_common_action = "dongzuoshouchi/shouchi_common_action/"
                if package_list[0] == "dongzuoshouchi":
                    depots.add(f"//{self.resource_path}/{shouchi_common_action}")

                source_path = (
                    f"//{self.resource_path}/{package_list[0]}/{package_list[1]}/"
                )
                depots.add(source_path)
        return depots
