import os
import re
from typing import List

from project.x5m.base_mgr.base_mgr import BaseWorkflow

DIR = os.path.dirname(__file__)
REPO = os.path.dirname(DIR)


class VoiceroomBuildingMgr(BaseWorkflow):
    kind = "voiceroom_building"
    name = "语音房-动态建筑"
    resource_path = "x5_mobile/mr/art_release/art_src/voiceroom/building"
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"
    execute_method = "H3DBuildTools.BuildArt"
    default_changelist = 502123
    distribute_branch = (BaseWorkflow.P4Setting.master, BaseWorkflow.P4Setting.branch, BaseWorkflow.P4Setting.special)

    def _filter_source_paths(self, depot_files: List[str]) -> set:
        paths = set()
        id_pattern = re.compile(r"^(//.*?/voiceroom/building/(\d{7,10}))/.*?$")
        for file in depot_files:
            rid = re.findall(id_pattern, file)
            if len(rid) == 0:
                continue
            paths.add(rid[0][0])
        return paths

    def workflow(self):
        return (
            ("展示系统信息", self.prepare),
            ("获取打包工具repo", self.update_or_clone_repo),
            ("判断是否需要打包", self.check_package),
            ("清理上次打包资源", self.clean_resource),
            ("获取P4资源", self.get_p4_resource),
            ("准备打包资源", self.prepare_resource),
            ("打包", self.package),
            ("计算打包结果", self.calculate_package_result),
            ("配置需要提交的文件", self.get_need_submit_files),
            ("复制包到P4路径", self.copy_package_to_p4_path),
            ("提交文件", self.submit_files_to_p4),
            ("持久化配置", self.save_result),
            ("备份+上传", self.always),
        )

    def transform_p4_to_art_trunk_path(self, local_path):
        _, target = local_path.split("art_src/")
        return f"Assets/StaticResources/art/3d/{target}"

    def copy_file_to_master(self, ff, _target, **kwargs):
        _, target = ff.split("art/")
        p4_path = f"{self.root}/{self.P4Setting.master_prefix_base.format(platform=self.platform)}/art/{target}"
        basename = os.path.basename(ff)
        dir_path = os.path.dirname(p4_path)
        re_result = re.search(r"^\d+", basename)
        if not re_result:
            return ""
        base_dir = re_result.group()
        dir_path = f"{dir_path}/{base_dir}"
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
        p4_path = f"{dir_path}/{basename}"
        return self.copy_file_to_p4_target(p4_path, ff)

    def copy_file_to_branch(self, ff, _target, **kwargs):
        _, target = ff.split("art/")
        p4_path = f"{self.root}/{self.P4Setting.branch_prefix_base.format(branch=_target, platform=self.platform)}/art/{target}"
        basename = os.path.basename(ff)
        dir_path = os.path.dirname(p4_path)
        re_result = re.search(r"^\d+", basename)
        if not re_result:
            return ""
        base_dir = re_result.group()
        dir_path = f"{dir_path}/{base_dir}"
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
        p4_path = f"{dir_path}/{basename}"
        return self.copy_file_to_p4_target(p4_path, ff)
