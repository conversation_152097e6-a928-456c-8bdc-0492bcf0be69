node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node{
            label "dgm_pub_arthub_texture_win_192.168.14.107"
            customWorkspace "e:\\arthub_submit_p4_trad"
        }
    }
    options {
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
        string name: 'deploy_env', defaultValue: 'prod', description: '部署环境' // TODO 测试使用
        text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
        // string name: 'input_start_time', defaultValue: '', description: '需要下载arthub资源的开始时间'
        // string name: 'input_end_time', defaultValue: '', description: '需要下载arthub资源的结束时间'
        string name: 'exclude_file', defaultValue: '', description: '需要跳过的文件，例如0407837401_icon.png,1407837401_icon.png 只需要填写文件名，多个就用英文逗号隔开'
        booleanParam(name: 'p4_undefined', defaultValue: false, description: '是否重新分发p4图片下undefined目录')
        booleanParam(name: 'p4_force', defaultValue: false, description: '是否强更p4')
    }
    // 因为5点到6:30点p4需要备份数据，流水线会执行失败
    triggers {
        cron('*/30 0-4,8-23 * * *')
    }
    stages {
        stage("检查是否需要分发") {
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        python x5m.py arthub_upload_p4_trad --job=check_need_distribute
                        """
                    )
                }
            }
        }
        stage("检查资源有效性") {
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        python x5m.py arthub_upload_p4_trad --job=check_p4_file
                        """
                    )
                }
            }
        }
        stage("下载需要分发的资源") {
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        python x5m.py arthub_upload_p4_trad --job=download_need_distribute
                        """
                    )
                }
            }
        }
        // stage("检查资源有效性") {
        //     steps {
        //         dir("pyframe-pipeline") {
        //             bat(
        //                 script: """
        //                 python x5m.py arthub_upload_p4_trad --job=check_file
        //                 """
        //             )
        //         }
        //     }
        // }
        // stage('下载arthub资源'){
        //     steps {
        //         dir("pyframe-pipeline") {
        //             bat(
        //                 script: """
        //                 python x5m.py arthub_upload_p4_trad --job=download_arthub
        //                 """
        //             )
        //         }
        //     }
        // }
        stage('更新p4 xlsx'){
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        python x5m.py arthub_upload_p4_trad --job=p4_sync_xlsx
                        """
                    )
                }
            }
        }
        stage('更新表单'){
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        python x5m.py arthub_upload_p4_trad --job=update_xlsx
                        """
                    )
                }
            }
        }
        stage('更新p4资源'){
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        python x5m.py arthub_upload_p4_trad --job=update_p4
                        """
                    )
                }
            }
        }
        stage('本地分发'){
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        python x5m.py arthub_upload_p4_trad --job=texture_distribute
                        """
                    )
                }
            }
        }
        // TODO 测试使用
        stage('提交p4'){
            steps {
                dir("pyframe-pipeline") {
                    bat(
                        script: """
                        python x5m.py arthub_upload_p4_trad --job=submit_p4
                        """
                    )
                }
            }
        }
    }
    post {
        always{
            dir("pyframe-pipeline") {
                bat label: '',
                script: """
                python x5m.py arthub_upload_p4_trad --job=on_always
                """
            }
        }
        unstable{
            dir("pyframe-pipeline") {
                bat label: '',
                script: """
                python x5m.py arthub_upload_p4_trad --job=on_canceled
                """
            }
        }
        success{
            dir("pyframe-pipeline") {
                bat label: '',
                script: """
                python x5m.py arthub_upload_p4_trad --job=on_success
                """
            }
        }
        // TODO 测试使用
        failure{
            dir("pyframe-pipeline") {
                bat label: '',
                script: """
                python x5m.py arthub_upload_p4_trad --job=on_failure
                """
            }
        }
    }
}
