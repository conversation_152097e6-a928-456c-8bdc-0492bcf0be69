# coding=utf-8
from datetime import datetime, timedelta
from frame import *
from project.x5m.arthub_upload_p4.mgr.arthub_mgr import arthub_mgr
from project.x5m.arthub_upload_p4.mgr.p4_mgr import ArthubP4TradMgr as ArthubP4Mgr
from project.x5m.arthub_upload_p4.mgr.env_mgr import env_mgr, jenkins_env_mgr
from project.x5m.arthub_upload_p4.mgr.distribute_mgr import distribute_trad_mgr as distribute_mgr
from project.x5m.arthub_upload_p4.mgr.msg_mgr import msg_mgr


@advance.stage(stage="下载arthub资源")
def download_arthub(**kwargs):
    start_time = jenkins_env_mgr.input_start_time
    end_time = jenkins_env_mgr.input_end_time
    # 如果只指定了开始时间或者结束时间，报错
    if len(start_time) ^ len(end_time):
        raise PyframeException("必须同时指定开始时间和结束时间")

    # 如果没有指定开始时间和结束时间，使用上次的结束时间作为开始时间
    if len(start_time) == 0 and len(end_time) == 0:
        start_time = env_mgr.global_last_time
        # 数据库中没有记录
        if not start_time:
            env_mgr.global_last_time = arthub_mgr.get_end_time()
            pipeline_mgr.stop_current_build()
        end_time = arthub_mgr.get_end_time()
    log.info(f"start_time={start_time}, end_time={end_time}")
    env_mgr.end_time = end_time

    ten_minutes_before = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S") - timedelta(minutes=10)
    arthub_mgr.download(start_time=ten_minutes_before.strftime("%Y-%m-%d %H:%M:%S"), end_time=end_time)


@advance.stage(stage="检查是否需要分发")
def check_need_distribute(**kwargs):
    distribute_mgr.check_need_distribute()


@advance.stage(stage="下载需要分发的资源")
def download_need_distribute(**kwargs):
    distribute_mgr.download_need_distribute()


@advance.stage(stage="检查文件是否合规")
def check_file(**kwargs):
    distribute_mgr.check_file()


@advance.stage(stage="检查文件是否合规")
def check_p4_file(**kwargs):
    distribute_mgr.check_p4_file()


@advance.stage(stage="更新p4xlsx")
def p4_sync_xlsx(**kwargs):
    p4_mgr = ArthubP4Mgr()
    p4_mgr.sync_xlsx()


@advance.stage(stage="解析分发规则")
def update_xlsx(**kwargs):
    distribute_mgr.update_xlsx()


@advance.stage(stage="更新p4资源")
def update_p4(**kwargs):
    p4_mgr = ArthubP4Mgr()
    p4_mgr.sync(force=jenkins_env_mgr.p4_force)


@advance.stage(stage="本地分发")
def texture_distribute(**kwargs):
    distribute_mgr.texture_distribute()


@advance.stage(stage="提交p4")
def submit_p4(**kwargs):
    p4_mgr = ArthubP4Mgr()
    p4_mgr.submit()


def on_always(**kwargs):
    if not env_mgr.arthub_no_updated:
        distribute_mgr.upload_log()


def on_success(**kwargs):
    if not jenkins_env_mgr.input_end_time:
        env_mgr.global_last_time = env_mgr.end_time
    msg_mgr.on_success()
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    msg_mgr.on_failure()
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    msg_mgr.on_canceled()
    advance.insert_pipeline_history_on_canceled()
