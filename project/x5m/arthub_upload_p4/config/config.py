# coding=utf-8

from frame import *
from project.x5m.arthub_upload_p4.mgr.env_mgr import jenkins_env_mgr

WORKDIR = env.pipeline.workspace()

# redis数据库
REDIS_CONFIG = {
    "host": "*************",
    "port": 6379,
    "db": 0,
    "password": "",
    "socket_connect_timeout": 7,
    "socket_keepalive": True,
    "decode_responses": True,
}

# jenkins p4账号
P4_CONFIG_JENKINS = {
    "host": "x5_mobile.p4.com:1666",
    "username": "dgm_jenkins",
    "password": "x5m12345",
}

# gitlab
GITLAB_MAINTAINER = {
    "url": "https://x5mobile-gitlab.h3d.com.cn/",
    "username": "<EMAIL>",
    "password": "maintainer123",
    "token": "o-yNgwVADiQ8CicYdt_4",
}


class P4:
    def __init__(self, chl):
        self.port = chl.p4_port
        self.username = chl.p4_username
        self.password = chl.p4_password
        self.deploy_env = jenkins_env_mgr.deploy_env
        self.__p4_workspace = f"arthub_upload_p4_{common.get_host_ip()}_{self.deploy_env}"
        self.root = WORKDIR
        self.client = self.__p4_workspace
        self.views = [
            f"//x5_mobile/mr/art_release/art_src/texture/... //{self.client}/x5_mobile/mr/art_release/art_src/texture/...",
            f"//x5_mobile/mr/onlineupdate/... //{self.client}/x5_mobile/mr/onlineupdate/...",
            f"//x5_mobile/mr/Resources/art_src/texture/... //{self.client}/x5_mobile/mr/Resources/art_src/texture/...",
            f"//x5_mobile/mr/b/... //{self.client}/x5_mobile/mr/b/...",
            f"//x5_mobile/mr/art_release/undefined/... //{self.client}/x5_mobile/mr/art_release/undefined/...",
            f"//x5mplan/resmg/ui/... //{self.client}/table_rule/...",
        ]


class SrcP4:
    def __init__(self, chl):
        self.port = chl.p4_port
        self.username = chl.p4_username
        self.password = chl.p4_password
        self.deploy_env = jenkins_env_mgr.deploy_env
        self.__p4_workspace = f"p4_upload_p4_{common.get_host_ip()}_{self.deploy_env}"
        self.root = WORKDIR
        self.client = self.__p4_workspace
        self.need_distribute_dir = "//美术资源/炫舞手游-ui/6-UI常规资源/新常规资源" if self.deploy_env == "prod" else "//美术资源/炫舞手游-ui/6-UI常规资源/新常规资源"
        self.exclude_dir = "//美术资源/炫舞手游-ui/6-UI常规资源/新常规资源/大背景相关/章节手册"
        self.views = [
            f"{self.need_distribute_dir}/... //{self.client}/{self.need_distribute_dir.replace('//', '')}/...",
        ]


class ArthubConfig:
    def __init__(self):
        self.p4_port = P4_CONFIG_JENKINS.get("host")
        self.p4_username = P4_CONFIG_JENKINS.get("username")
        self.p4_password = P4_CONFIG_JENKINS.get("password")

    @property
    def p4(self):
        return P4(self)

    @property
    def src_p4(self):
        return SrcP4(self)


class ArthubConfigProd(ArthubConfig):
    def __init__(self):
        super(ArthubConfigProd, self).__init__()


class ArthubConfigDev(ArthubConfig):
    def __init__(self):
        super(ArthubConfigDev, self).__init__()


# def init_config():
#     if jenkins_env_mgr.deploy_env == "dev":
#         return ArthubConfigDev
#     return ArthubConfigProd

config = ArthubConfigDev() if jenkins_env_mgr.deploy_env == "dev" else ArthubConfigProd()
