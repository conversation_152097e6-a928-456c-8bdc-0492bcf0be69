# coding=utf-8
from fnmatch import fnmatchcase as match
import re
import time

import xlrd

from frame import *
from project.x5m.arthub_upload_p4.config.config import WORKDIR
from project.x5m.arthub_upload_p4.mgr.env_mgr import env_mgr, jenkins_env_mgr, global_env_mgr
from project.x5m.arthub_upload_p4.mgr.gitlab_mgr import ArthubGitlabMgr
from project.x5m.arthub_upload_p4.mgr.p4_mgr import ArthubP4Mgr, SrcP4Mgr


class DistributeMgr:
    def __init__(self):
        self.__workspace = WORKDIR
        self._table_rule_path = os.path.join(self.__workspace, "table_rule")
        self.src_p4_mgr = SrcP4Mgr()

    def check_need_distribute(self):
        """
        检查是否需要分发
        """
        end_time = time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime())
        file_info = {
            "time": end_time,
            "all_count": "0",
            "p4_count": 0,
            "repeat_count": "0",
            "changelist": "",
            "sign": "0",
        }
        first_changelist = jenkins_env_mgr.first_changelist
        last_changelist = global_env_mgr.last_changelist
        log.info(f"first_changelist: {first_changelist} last_changelist: {last_changelist}")
        if not first_changelist or first_changelist == "0":
            if not last_changelist:
                raise PyframeException("流水线第一次运行, 请指定first_changelist")
            changlist = int(last_changelist) + 1
        else:
            changlist = first_changelist
        changes = self.src_p4_mgr.get_changes(changlist)
        changed_files = self.__get_changed_files(changes)
        log.info(f"changed_files: {changed_files}")
        if not changed_files:
            file_info["sign"] = "1"
            pipeline_mgr.stop_current_build()
        else:
            env_mgr.changes = changes
            env_mgr.changed_files = changed_files
        env_mgr.fileinfo = file_info

    def __get_changed_files(self, changes: list) -> list:
        users = []
        files = []
        pattern = re.compile(r"^.*<(.*@h3d.com.cn)>$")
        changes = sorted(changes, key=lambda x: x["change"])
        for change in changes:
            # 获取提交记录中的提交人
            desc = change.get("desc")
            ret = pattern.findall(desc)
            if ret:
                users.append(ret[0])
            user = change.get("user")
            if user not in users:
                if not user.endswith("@h3d.com.cn"):
                    user = user + "@h3d.com.cn"
                users.append(user)

            # 获取提交记录中的文件, 并过滤掉删除的文件
            changelist = change.get("change")
            changed_files = self.src_p4_mgr.get_changed_files(changelist)
            for file, action in changed_files.items():
                if (
                    action != "delete"
                    # 去掉章节手册目录
                    and not file.startswith(self.src_p4_mgr.p4_config.exclude_dir)
                    and file.startswith(self.src_p4_mgr.p4_config.need_distribute_dir)
                ):
                    files.append(file)
                else:
                    if file in files:
                        files.remove(file)
        env_mgr.submitters = users
        return files

    def check_p4_file(self):
        """
        检查需要下载分发的文件是否合规
        """
        space_files = []
        not_png_files = []
        files = env_mgr.changed_files
        for file in files:
            file_name = file.split("/")[-1]
            # 检查文件是否包含空格
            if " " in file:
                space_files.append(file_name)
            # 检查文件是否是png
            if not file_name.endswith(".png") and not file_name.endswith(".PNG"):
                not_png_files.append(file_name)

        if len(space_files) > 0:
            log.error(f"文件名包含空格: {space_files}")
            msg = "下面文件名包含空格, 请相关人员修改后重新上传:\n"
            for space_file in space_files:
                msg += f"{space_file}\n"
            raise PyframeException(message=msg, mentioned_list=env_mgr.submitters)

        if len(not_png_files) > 0:
            log.error(f"文件不是png: {not_png_files}")
            msg = "下面文件不是png, 请相关人员修改后重新上传:\n"
            for not_png_file in not_png_files:
                msg += f"{not_png_file}\n"
            raise PyframeException(message=msg, mentioned_list=env_mgr.submitters)

    def download_need_distribute(self):
        """
        下载需要分发的资源
        """
        # 获取指定的文件信息到指定目录
        files = env_mgr.changed_files
        p4_views = self.src_p4_mgr.get_views(files)
        self.src_p4_mgr.p4.set_view(views=p4_views)
        self.src_p4_mgr.p4.sync_all(force=True)

    def check_file(self):
        """
        检查文件是否合规
        """
        space_files = []
        not_png_files = []
        p = Path(env_mgr.resource_dir)
        for file in p.iterdir():
            # 检查文件是否包含空格
            if " " in file.name:
                space_files.append(file.name)
            # 检查文件是否是png
            if not file.name.endswith(".png") and not file.name.endswith(".PNG"):
                not_png_files.append(file.name)

        if len(space_files) > 0:
            log.error(f"文件名包含空格: {space_files}")
            msg = "下面文件名包含空格, 请相关人员修改后重新上传:\n"
            for space_file in space_files:
                msg += f"{space_file}\n"
            raise PyframeException(message=msg, mentioned_list=env_mgr.submitters)

        if len(not_png_files) > 0:
            log.error(f"文件不是png: {not_png_files}")
            msg = "下面文件不是png, 请相关人员修改后重新上传:\n"
            for not_png_file in not_png_files:
                msg += f"{not_png_file}\n"
            raise PyframeException(message=msg, mentioned_list=env_mgr.submitters)

    def distribute_precheck(self):
        """
        分发热更资源前预检查
        """
        hotfix_distrib_file_path = os.path.join(self._table_rule_path, "hotfix-texture-rule.xlsx")
        log.info(f"hotfix_distrib_file_path: {hotfix_distrib_file_path}")
        data = xlrd.open_workbook(hotfix_distrib_file_path)
        table = data.sheets()[0]
        err = []
        for i in range(1, table.nrows):
            config_data = table.row_values(i, 1, 2)
            distrib_file_path = str(config_data[0])
            big_edit = distrib_file_path[0] + "." + distrib_file_path[1:3] + ".0"
            small_edit = distrib_file_path + "000"
            # local_path = os.path.join(self.p4_root, "x5_mobile/mr/onlineupdate", big_edit, small_edit)
            p4_path = f"//x5_mobile/mr/onlineupdate/{big_edit}/{small_edit}/*"
            if not self.src_p4_mgr.p4.dirs(p4_path):
                err.append(f"p4 path {p4_path} not exists or empty")
        if err:
            err_msg = "\n".join(err)
            raise PyframeException(f"分发资源路径不存在: {err_msg}")

    def update_xlsx(self):
        """
        更新本地的xlsx文件
        """
        self._update_cdn_texture_base()
        self._update_cdn_texture_rule()
        self._update_trunk_texture_base()
        self._update_trunk_texture_rule()
        self._update_hotfix_texture_rule()

    def _update_cdn_texture_base(self):
        log.info("更新cdn-texture-base.xlsx")
        cdn_texture_base_list = self.__read_table(os.path.join(self._table_rule_path, "cdn-texture-base.xlsx"))
        env_mgr.cdn_texture_base_list = cdn_texture_base_list

    def _update_cdn_texture_rule(self):
        log.info("更新cdn-texture-rule.xlsx")
        cdn_texture_rule_list = self.__read_table(os.path.join(self._table_rule_path, "cdn-texture-rule.xlsx"))
        env_mgr.cdn_texture_rule_list = cdn_texture_rule_list

    def _update_trunk_texture_base(self):
        log.info("更新trunk-texture-base.xlsx")
        trunk_texture_base_list = self.__read_table(os.path.join(self._table_rule_path, "trunk-texture-base.xlsx"))
        env_mgr.trunk_texture_base_list = trunk_texture_base_list

    def _update_hotfix_texture_rule(self):
        log.info("更新hotfix-texture-rule.xlsx")
        excel = xlrd.open_workbook(os.path.join(self._table_rule_path, "hotfix-texture-rule.xlsx"))
        hotfix_data_list = []
        sheet = excel.sheets()[0]
        hotfix_dir_list = []
        hotfix_version_list = []
        for i in range(1, sheet.nrows):
            temp = {}
            config_data = sheet.row_values(i, 0, 3)
            config_data[0] = self.__float_to_str(config_data[0])
            temp.update({"name": config_data[0].lower().replace("#", "[0-9]") + ".png"})
            temp.update({"version": config_data[1]})
            temp.update({"path": config_data[2]})
            hotfix_data_list.append(temp)
        log.info("开始组织表单数据 热更2")
        for data in hotfix_data_list:
            temp = str(data["version"])
            if temp not in hotfix_version_list:
                hotfix_version_list.append(temp)
                version = str(data["version"]) + "000"
                pre, next = version[0:1], version[1:3]
                version_main = pre + "." + next + "." + "0"
                if data.get("path"):
                    hotfix_dir = f"//x5_mobile/mr/onlineupdate/{version_main}/{version}/art_src/texture/{data['path']}/..."
                else:
                    hotfix_dir = f"//x5_mobile/mr/onlineupdate/{version_main}/{version}/art_src/texture/..."
                hotfix_dir_list.append(hotfix_dir)
        env_mgr.hotfix_dir_list = hotfix_dir_list
        env_mgr.hotfix_data_list = hotfix_data_list

    def _update_trunk_texture_rule(self):
        excel = xlrd.open_workbook(os.path.join(self._table_rule_path, "trunk-texture-rule.xlsx"))
        trunk_texture_rule_list = []
        sheet = excel.sheets()[0]
        for i in range(1, sheet.nrows):
            temp = {}
            config_data = sheet.row_values(i, 0, 3)
            config_data[0] = self.__float_to_str(config_data[0])
            temp.update({"name": config_data[0].lower().replace("#", "[0-9]") + ".png"})
            temp.update({"path": config_data[2]})
            trunk_texture_rule_list.append(temp)

        env_mgr.trunk_texture_rule_list = trunk_texture_rule_list

    def __read_table(self, table_path: str) -> list:
        excel = xlrd.open_workbook(table_path)
        data_list = []
        sheet = excel.sheets()[0]

        for i in range(1, sheet.nrows):
            temp = {}
            config_data = sheet.row_values(i, 0, 3)
            config_data[0] = self.__float_to_str(config_data[0]).strip()
            temp.update({"name": config_data[0].lower().replace("#", "[0-9]") + ".png"})
            temp.update({"path": config_data[1]})
            data_list.append(temp)
        return data_list

    def __float_to_str(self, data):
        """
        将传入的float对象转换为整数字符串
        """
        if type(data) == float:
            return str(int(data))
        return str(data)

    # 匹配白名单
    @staticmethod
    def _match_white_list(filename) -> bool:
        """
        匹配白名单，如果匹配成功，返回True，否则返回False
        """
        white_list = jenkins_env_mgr.exclude_file

        for i in white_list:
            if match(filename, i):
                return True
        return False

    def texture_distribute(self):
        """
        本地分发
        """
        resource_dir = env_mgr.resource_dir
        cdn_texture_rule_list = env_mgr.cdn_texture_rule_list
        cdn_texture_base_list = env_mgr.cdn_texture_base_list
        hotfix_data_list = env_mgr.hotfix_data_list
        trunk_texture_rule_list = env_mgr.trunk_texture_rule_list
        trunk_texture_base_list = env_mgr.trunk_texture_base_list
        all_resource = {}  # 所有资源
        cdn_resource = {}  # cdn内资源
        hotfix_resource = {}  # 热更内资源
        edition_resource = {}  # 版本内资源
        undefined_resource = {}  # 无规则资源
        repeat_resource = {}  # 重名资源
        delete_resource = {}  # 删除资源
        max_branch = ArthubGitlabMgr.get_latest_branch()
        cdn_path = os.path.join(self.__workspace, r"x5_mobile\mr\art_release\art_src\texture")
        master_path = os.path.join(self.__workspace, r"x5_mobile\mr\Resources\art_src\texture")
        branch_path = os.path.join(self.__workspace, r"x5_mobile\mr\b\{}\art_src\texture".format(max_branch))
        log.info(f"本次分发本地资源路径为:{resource_dir}")
        file_info = env_mgr.fileinfo
        for filepath, _, filenames in os.walk(resource_dir):
            for filename in filenames:
                filename = filename.lower()
                # 重名资源进提示名单
                if filename in all_resource.keys():
                    # os.chmod(all_resource[filename], stat.S_IWRITE)
                    # os.remove(all_resource[filename])
                    path_mgr.rm(path=all_resource[filename])
                    repeat_resource[filename] = all_resource[filename]
                    del all_resource[filename]

                # 临时解决问题
                elif self._match_white_list(filename):
                    log.info(f"匹配白名单: {filename}")
                    continue

                # 匹配CDN表单规则
                elif self._match_cdn_texture_rule(
                    data_list=cdn_texture_rule_list,
                    p4_dir=cdn_path,
                    all_resource=all_resource,
                    cdn_resource=cdn_resource,
                    filename=filename,
                    filepath=filepath,
                ):
                    log.info(f"匹配CDN表单规则: {filename}")
                    log.debug(f"all_resource: {all_resource}")
                    continue

                # 匹配CDN基础表资源
                elif self._match_cdn_texture_base(
                    data_list=cdn_texture_base_list,
                    p4_dir=cdn_path,
                    all_resource=all_resource,
                    type_resource=cdn_resource,
                    filename=filename,
                    filepath=filepath,
                ):
                    log.info(f"匹配CDN基础表资源: {filename}")
                    log.debug(f"all_resource: {all_resource}")
                    continue

                # 匹配hotfix表单规则
                elif self._match_hotfix_data(
                    data_list=hotfix_data_list,
                    data_list1=trunk_texture_rule_list,
                    data_list2=trunk_texture_base_list,
                    all_resource=all_resource,
                    hotfix_resource=hotfix_resource,
                    filename=filename,
                    filepath=filepath,
                ):
                    log.info(f"匹配hotfix表单规则: {filename}")
                    log.debug(f"all_resource: {all_resource}")
                    continue

                # 匹配版本内表单规则
                elif self._match_trunk_texture_rule(
                    trunk_texture_rule_list, master_path, branch_path, all_resource, edition_resource, filename, filepath
                ):
                    log.info(f"匹配版本内表单规则: {filename}")
                    log.debug(f"all_resource: {all_resource}")
                    continue

                # 匹配版本内基础资源。重复，这里可能有问题
                elif self._match_cdn_texture_base(trunk_texture_base_list, master_path, all_resource, edition_resource, filename, filepath):
                    self._match_cdn_texture_base(trunk_texture_base_list, branch_path, all_resource, edition_resource, filename, filepath)
                    log.info(f"匹配版本内基础资源: {filename}")
                    log.debug(f"all_resource: {all_resource}")
                    continue

                else:
                    version = "x5_mobile\\mr\\art_release\\undefined"
                    if not jenkins_env_mgr.p4_undefined:
                        self.__copy_resource(self.__workspace, version, all_resource, undefined_resource, filepath, filename)
                    else:
                        all_resource[filename] = os.path.join(self.__workspace, version, filename.lower())
                        undefined_resource[filename] = os.path.join(self.__workspace, version, filename.lower())

        repeat_count = len(repeat_resource)
        self.__texture_check(all_resource, undefined_resource, delete_resource)
        file_info["all_count"] = str(len(all_resource))
        file_info["repeat_count"] = str(repeat_count)
        self.__log_generation(cdn_resource, hotfix_resource, edition_resource, undefined_resource, repeat_resource, delete_resource, file_info)
        env_mgr.fileinfo = file_info
        if len(undefined_resource) > 0:
            undefines = []
            for _, path in undefined_resource.items():
                undefines.append(path)
            msg = f"存在未定义文件:\n"
            msg += "\n".join(undefines)
            mentioned_list = env_mgr.submitters
            mentioned_list.append("<EMAIL>")
            mentioned_list.append("<EMAIL>")
            raise PyframeException(msg, mentioned_list=mentioned_list)

    def __log_generation(
        self,
        cdn_resource: dict,
        hotfix_resource: dict,
        edition_resource: dict,
        undefined_resource: dict,
        repeat_resource: dict,
        delete_resource: dict,
        file_info: dict,
    ):
        """
        本地生成日志
        """
        now_time = file_info["time"]
        log_dir = os.path.join(self.__workspace, "log")
        path_mgr.mkdir(log_dir)

        error_resource = {}
        for unkey in undefined_resource.keys():
            if not unkey.endswith(".png"):
                error_resource[unkey] = undefined_resource[unkey]

        log_file = os.path.join(log_dir, f"{now_time}_record.txt")
        file = open(log_file, "w+")
        file.write("本地资源的分发情况如下：\n\n")
        file.write("1.未定义规则的资源分发情况：\n")
        self.__write_txt(undefined_resource, file)
        file.write("2.重名资源的分发情况：\n")
        self.__write_txt(repeat_resource, file)
        file.write("3.新符合规则资源的分发情况：\n")
        self.__write_txt(delete_resource, file)
        file.write("4.cdn内资源的分发情况：\n")
        self.__write_txt(cdn_resource, file)
        file.write("5.热更内资源的分发情况：\n")
        self.__write_txt(hotfix_resource, file)
        file.write("6.版本内资源的分发情况：\n")
        self.__write_txt(edition_resource, file)
        file.close()
        env_mgr.log = log_file

    def __write_txt(self, dic: dict, file):
        """
        将文件名和文件路径写入txt文件

        Args：
           dic：包含文件名和文件路径的字典
           file：写入的文件
        """
        file.write(f"资源数量：{len(dic)}\n")
        if len(dic) != 0:
            for key, value in dic.items():
                file.write(f"文件名：{key}  文件路径：{value}\n")
        file.write("\n")

    def __texture_check(self, all_resource: dict, undefined_resource: dict, delete_resource: dict):
        """
        检查图片

        Args：
            all_resource：上传的资源字典
            undefined_resource：未定义的资源字典
            delete_resource：删除的资源字典
        """
        undefined_dir = r"//x5_mobile/mr/art_release/undefined"
        for filename in all_resource:
            if filename not in undefined_resource:
                match_rule = os.path.join(self.__workspace, "x5_mobile\\mr\\art_release\\undefined", filename)
                if os.path.exists(match_rule):
                    p4_mgr = ArthubP4Mgr()
                    p4_mgr.p4.delete(path=os.path.join(undefined_dir, filename))
                    log.info(f"本次从p4 undefined目录删除的资源为:{os.path.join(undefined_dir, filename)}")
                    delete_resource[filename] = match_rule

    def _match_cdn_texture_rule(self, data_list: list, p4_dir: str, all_resource: dict, cdn_resource: dict, filename: str, filepath: str):
        """
        是否匹配cdn表的分发规则

        Args：
            data_list: 不同资源类型的数据列表
            p4_dir: 不同资源类型的p4路径
            all_resource: 上传资源
            cdn_resource: cdn资源
            filename: 文件名
            filepath: 文件路径
        """
        # log.debug(f"data_list: {data_list}")
        log.debug(f"p4_dir: {p4_dir}")
        log.debug(f"cdn_resource: {cdn_resource}")
        log.debug(f"filename: {filename}")
        log.debug(f"filepath: {filepath}")
        dir_name = ""
        for data in data_list:
            if match(filename, data["name"]):
                if filename.startswith("zs_9261"):
                    dir_name = filename[3:12]

                path = data["path"]
                if path.startswith("../"):
                    path = path.replace("../", "")

                version = os.path.join(path, dir_name)
                self.__copy_resource(p4_dir, version, all_resource, cdn_resource, filepath, filename)
                return True
        return False

    def _match_cdn_texture_base(self, data_list: list, p4_dir: str, all_resource: dict, type_resource: dict, filename: str, filepath: str):
        """
        是否匹配通用表的分发规则

        Args：
            data_list: 不同资源类型的数据列表
            p4_dir: 不同资源类型的p4路径
            all_resource: 上传资源
            type_resource: 不同资源类型的资源
            filename: 文件名
            filepath: 文件路径
        """
        # log.debug(f"data_list: {data_list}")
        log.debug(f"p4_dir: {p4_dir}")
        log.debug(f"type_resource: {type_resource}")
        log.debug(f"filename: {filename}")
        log.debug(f"filepath: {filepath}")

        for data in data_list:
            if filename == data["name"]:
                version = data["path"]
                self.__copy_resource(p4_dir, version, all_resource, type_resource, filepath, filename)
                return True
        return False

    def _match_hotfix_data(
        self, data_list: list, data_list1: list, data_list2: list, all_resource: dict, hotfix_resource: dict, filename: str, filepath: str
    ):
        """
        是否匹配热更表的分发规则

        Args：
            data_list: 不同资源类型的数据列表
            data_list1: 不同资源类型的数据列表
            data_list2: 不同资源类型的数据列表
            all_resource: 上传资源
            hotfix_resource: 热更资源
            filename: 文件名
            filepath: 文件路径
        """
        # log.debug(f"data_list: {data_list}")
        # log.debug(f"data_list1: {data_list1}")
        # log.debug(f"data_list2: {data_list2}")
        log.debug(f"hotfix_resource: {hotfix_resource}")
        log.debug(f"filename: {filename}")
        log.debug(f"filepath: {filepath}")

        for data in data_list:
            if filename == data["name"]:
                version = str(data["version"]) + "000"
                pre = version[0:1]
                next = version[1:3]
                version_main = pre + "." + next + "." + "0"
                hotfix_path = os.path.join(self.__workspace, f"x5_mobile\\mr\\onlineupdate\\{version_main}\\{version}\\art_src\\texture")
                path = ""
                for data1 in data_list1:
                    if match(filename, data1["name"]):
                        path = data1["path"]
                for data2 in data_list2:
                    if filename == data2["name"]:
                        path = data2["path"]
                ver_path = path
                self.__copy_resource(hotfix_path, ver_path, all_resource, hotfix_resource, filepath, filename)
                return True
        return False

    def _match_trunk_texture_rule(
        self, data_list: list, master_dir: str, branch_dir: str, all_resource: dict, edition_resource: dict, filename: str, filepath: str
    ):
        """
        是否匹配版本内表的分发规则

        Args：
            data_list: 不同资源类型的数据列表
            master_dir: 不同资源类型的p4主支路径
            branch_dir: 不同资源类型的p4分支路径
            all_resource: 上传资源
            edition_resource: 版本内资源
            filename: 文件名
            filepath: 文件路径
        """
        # log.debug(f"data_list: {data_list}")
        log.debug(f"master_dir: {master_dir}")
        log.debug(f"branch_dir: {branch_dir}")
        log.debug(f"edition_resource: {edition_resource}")
        log.debug(f"filename: {filename}")
        log.debug(f"filepath: {filepath}")

        max_branch = ArthubGitlabMgr.get_latest_branch()
        max_branch = max_branch.replace(".", "")
        for data in data_list:
            if match(filename, data["name"]):
                if filename[0:4].isdigit() and filename[4:5] == "_":
                    version = data["name"]
                    version_main = version[0:4]
                    version = data["path"]
                    if int(version_main) > int(max_branch):
                        self.__copy_resource(master_dir, version, all_resource, edition_resource, filepath, filename)
                    else:
                        self.__copy_resource(branch_dir, version, all_resource, edition_resource, filepath, filename)
                    return True
                else:
                    version = data["path"]
                    self.__copy_resource(master_dir, version, all_resource, edition_resource, filepath, filename)
                    self.__copy_resource(branch_dir, version, all_resource, edition_resource, filepath, filename)
                    return True
        return False

    def __copy_resource(self, p4_dir: str, version: str, all_resource: dict, type_resource: dict, filepath: str, filename: str):
        """
        拷贝资源到p4路径
        Args：
           p4_dir: p4路径
           version：版本号
           all_resource: 上传资源
           type_resource: 不同类型的资源
           filename: 文件名
           filepath: 文件路径
        """
        # log.info(f"copy_resource params, p4_dir: {p4_dir}, version: {version}, all_resource: {all_resource}, type_resource: {type_resource}, filepath: {filepath}, filename: {filename}")
        # if not os.path.exists(os.path.join(p4_dir, version)):
        #     os.makedirs(os.path.join(p4_dir, version))
        path_mgr.mkdir(os.path.join(p4_dir, version))
        path_mgr.copy(src=os.path.join(filepath, filename), dst=os.path.join(p4_dir, version, filename.lower()))
        all_resource[filename] = os.path.join(p4_dir, version, filename.lower())
        type_resource[filename] = os.path.join(p4_dir, version, filename.lower())

    def upload_log(self):
        """
        上传日志
        """
        log_path = env_mgr.log
        if log_path:
            log_url = advance.upload_pipeline_log(path=log_path)
            env_mgr.log_url = log_url


distribute_mgr = DistributeMgr()
