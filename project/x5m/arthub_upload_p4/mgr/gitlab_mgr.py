# coding=utf-8
import re

from frame import *
from project.x5m.arthub_upload_p4.config.config import GITLAB_MAINTAINER


class ArthubGitlabMgr:
    @staticmethod
    def get_latest_branch() -> str:
        git_x5moblie_mgr = GitlabMgr(*********************["url"], token=GITLAB_MAINTAINER["token"], project="dgm/x5mobile")
        branches = git_x5moblie_mgr.get_all_branches()
        target_branches = []
        for branch in branches:
            reg = r"^\d.\d+.\d$"
            if re.match(reg, branch) is not None:
                target_branches.append(branch)
        target_branches.sort(reverse=True)
        return target_branches[0]
