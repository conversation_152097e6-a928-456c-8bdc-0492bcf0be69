from frame import wechat
from project.x5m.arthub_upload_p4.mgr.env_mgr import env_mgr


class MsgMgr:
    def __init__(self) -> None:
        self._webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb"

    def _get_msg(self) -> str:
        """
        获取消息
        Returns:
            str: 消息
        """
        msg = ""
        file_info = env_mgr.fileinfo
        if file_info:
            msg += f"**arthub得到的图片数量**: {file_info['all_count']}\n"
            msg += f"**实际上传p4的图片数量**: {file_info['p4_count']}\n"
            if file_info["repeat_count"] != "0":
                msg += f"**异常图片数量**: {file_info['repeat_count']}\n"
        log_url = env_mgr.log_url
        if log_url:
            log_name = log_url.split("/")[-1]
            msg += f"**统计日志:** [{log_name}]({log_url})\n"
        submitters = env_mgr.submitters
        if submitters:
            submitters = list(set(submitters))  # 去重
            submitters_str = ",".join(submitters)
            msg += f"**提交人:** {submitters_str}\n"

        return msg

    def _get_users(self) -> list:
        """
        获取通知人列表
        Returns:
            list: 用户列表
        """
        submitters = env_mgr.submitters
        if not submitters:
            submitters = []
        submitters.append("<EMAIL>")
        submitters.append("<EMAIL>")
        return submitters

    def on_success(self) -> None:
        msg = self._get_msg()
        wechat.send_unicast_post_success(user_list=self._get_users(), content=msg)
        # wechat.send_multicast_post_success(webhook=self._webhook_url, content=msg)

    def on_failure(self) -> None:
        msg = self._get_msg()
        # wechat.send_unicast_post_failure(content=msg, rescue=False, to_admin=False)
        wechat.send_multicast_post_failure(webhook=self._webhook_url, content=msg)

    def on_canceled(self) -> None:
        msg = self._get_msg()
        wechat.send_unicast_post_canceled(user_list=self._get_users(), content=msg)
        wechat.send_multicast_post_canceled(webhook=self._webhook_url, content=msg)


msg_mgr = MsgMgr()
