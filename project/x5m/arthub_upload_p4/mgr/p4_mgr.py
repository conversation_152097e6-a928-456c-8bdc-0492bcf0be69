# coding=utf-8

import time
from frame import *
from project.x5m.arthub_upload_p4.mgr.env_mgr import env_mgr, jenkins_env_mgr, global_env_mgr
from project.x5m.arthub_upload_p4.mgr.gitlab_mgr import ArthubGitlabMgr
from project.x5m.arthub_upload_p4.config.config import config, WORKDIR


class ArthubP4Mgr:
    def __init__(self):
        self.p4_config = config.p4
        self.p4 = P4Client(
            host=self.p4_config.port,
            username=self.p4_config.username,
            password=self.p4_config.password,
            client=self.p4_config.client,
        )
        self.p4.set_view(views=self.p4_config.views)
        self.p4.set_root(path=self.p4_config.root)
        self.p4.set_options(allwrite=True, clobber=True, rmdir=True)
        # self.p4.set_encoding("gbk")

        max_branch = ArthubGitlabMgr.get_latest_branch()
        self.__hotfix_dir = env_mgr.hotfix_dir_list
        self.__cdn_dir = "//x5_mobile/mr/art_release/art_src/texture/..."
        self.__master_dir = "//x5_mobile/mr/Resources/art_src/texture/..."
        self.__branch_dir = f"//x5_mobile/mr/b/{max_branch}/art_src/texture/..."
        self.__undefined_dir = "//x5_mobile/mr/art_release/undefined/..."

    def sync_xlsx(self):
        xlsx_list = [
            "//x5mplan/resmg/ui/cdn-texture-base.xlsx",
            "//x5mplan/resmg/ui/cdn-texture-rule.xlsx",
            "//x5mplan/resmg/ui/hotfix-texture-rule.xlsx",
            "//x5mplan/resmg/ui/trunk-texture-base.xlsx",
            "//x5mplan/resmg/ui/trunk-texture-rule.xlsx",
        ]
        self.p4.sync_list(path_list=xlsx_list, force=True)

    def sync(self, force: bool):
        log.debug(f"env_mgr.cdn_texture_rule_list: {env_mgr.cdn_texture_rule_list}")
        texture_p4_paths = self._modify_p4_view(cdn_data_list=env_mgr.cdn_texture_rule_list)
        texture_p4_paths.append(self.__cdn_dir)
        self.p4.sync_list(path_list=texture_p4_paths, force=force)
        self.p4.sync_list(path_list=self.__hotfix_dir, force=force)
        self.p4.sync(path=self.__master_dir, force=force)
        self.p4.sync(path=self.__branch_dir, force=force)
        self.p4.clean(path=self.__undefined_dir, a=True)
        self.p4.sync(path=self.__undefined_dir, force=force)

    def _modify_p4_view(self, cdn_data_list: list) -> list:
        texture_p4_path_set = set()

        dot_dot_list = []
        for data in cdn_data_list:
            path = data.get("path")
            if path.startswith(".."):
                dot_dot_list.append(data)
        log.debug(f"dot_dot_list: {dot_dot_list}")
        # [{"name": "*_n_bg.png", "path": "../fetter_cg"}, {"name": "*_r_bg.png", "path": "../fetter_cg"}, {"name": "*_sr_bg.png", "path": "../fetter_cg"}, {"name": "*_ssr_bg.png", "path": "../fetter_cg"}]
        new_view = []
        for data in dot_dot_list:
            path = data.get("path")
            useful_path = path.replace("../", "")
            texture_p4_path_set.add(f"//x5_mobile/mr/art_release/art_src/{useful_path}/...")
            new_view.append(
                f"//x5_mobile/mr/art_release/art_src/{useful_path}/... //{self.p4_config.client}/x5_mobile/mr/art_release/art_src/{useful_path}/..."
            )
        new_view = list(set(new_view))
        log.debug(f"new_view: {new_view}")
        p4_view = self.p4.get_view()
        p4_view.extend(new_view)
        log.debug(f"p4_view: {p4_view}")

        self.p4.set_view(views=p4_view)
        # [
        #     "//x5_mobile/mr/b/... //arthub_upload_p4_192.168.12.155_dev/x5_mobile/mr/b/...",
        #     "//x5_mobile/mr/art_release/art_src/texture/... //arthub_upload_p4_192.168.12.155_dev/x5_mobile/mr/art_release/art_src/texture/...",
        #     "//x5_mobile/mr/art_release/art_src/fetter_cg/... //arthub_upload_p4_192.168.12.155_dev/x5_mobile/mr/art_release/art_src/fetter_cg/...",
        #     "//x5_mobile/mr/art_release/undefined/... //arthub_upload_p4_192.168.12.155_dev/x5_mobile/mr/art_release/undefined/...",
        #     "//x5mplan/resmg/ui/... //arthub_upload_p4_192.168.12.155_dev/table_rule/...",
        #     "//x5_mobile/mr/Resources/art_src/texture/... //arthub_upload_p4_192.168.12.155_dev/x5_mobile/mr/Resources/art_src/texture/...",
        #     "//x5_mobile/mr/onlineupdate/... //arthub_upload_p4_192.168.12.155_dev/x5_mobile/mr/onlineupdate/..."
        # ]
        return list(texture_p4_path_set)

    def submit(self):
        file_info = env_mgr.fileinfo

        # p4 reconcile -f {path}
        reconciles = []
        ret_cdn = self.p4.reconcile(path=self.__cdn_dir)
        if ret_cdn:
            reconciles.extend(ret_cdn)
        for h_dir in self.__hotfix_dir:
            ret_hotfix = self.p4.reconcile(path=h_dir)
            if ret_hotfix:
                reconciles.extend(ret_hotfix)
        ret_master = self.p4.reconcile(path=self.__master_dir)
        if ret_master:
            reconciles.extend(ret_master)
        ret_branch = self.p4.reconcile(path=self.__branch_dir)
        if ret_branch:
            reconciles.extend(ret_branch)
        ret_undefined = self.p4.reconcile(path=self.__undefined_dir)
        if ret_undefined:
            reconciles.extend(ret_undefined)

        submitters = env_mgr.submitters
        submitters_str = ",".join(submitters)
        log.debug(f"submitters_str: {submitters_str}")
        sub_info = f"uploader:{submitters_str}\n url:{env.pipeline.build_url()}"
        p4_undefined = jenkins_env_mgr.p4_undefined
        if reconciles:
            try:
                ret_submit = self.p4.submit(sub_info, revert_if_failed=True)
            except Exception as e:
                if "该文件已经被锁定，无法进行修改或者操作，请联系PM进行解锁" in str(e):
                    mentioned_list = env_mgr.submitters
                    mentioned_list.append("<EMAIL>")
                    mentioned_list.append("<EMAIL>")
                else:
                    mentioned_list = MAINTAINER_LIST
                raise PyframeException(message=str(e), mentioned_list=mentioned_list)

            log.info(f"ret_submit: {ret_submit}")
            file_info["changelist"] = str(ret_submit[0]["change"])
            file_info["p4_count"] = str(len(reconciles))
            self.__log_supplement("实际上传p4资源情况如下:", ret_submit)
            if not p4_undefined:
                if not jenkins_env_mgr.input_end_time:
                    end_time = env_mgr.end_time
                    env_mgr.global_last_time = end_time
                    log.info(f"end_time: {end_time}")
        else:
            result_str = "没有改动的文件, 不需要提交"
            self.__log_supplement(result_str)
            if not p4_undefined:
                if not jenkins_env_mgr.input_end_time:
                    env_mgr.global_last_time = env_mgr.end_time
            log.info(f"result_str:{result_str}")
            # 郝永辉修改代码开始
            # arthub图片有更新，但是没有改动，不需要提交，也要走完流程发送通知,注释掉stop_current_build, 敏哥需求
            file_info["p4_count"] = "0"  # 增加这一条防止发送信息拼接字符串错位，因为初始化类型石int
            # pipeline_mgr.stop_current_build()
            # 郝永辉修改代码结束
        env_mgr.fileinfo = file_info

        # 记录本次原始资源的changelist
        changelist = env_mgr.changes[0].get("change")
        global_env_mgr.last_changelist = changelist

    def __log_supplement(self, title: str, content=None):
        f = open(env_mgr.log, "a")
        f.write(f"\n{title}\n\n")
        if content:
            for data in content:
                f.write(f"{data}\n")
        f.close()


class SrcP4Mgr:
    def __init__(self):
        self.workspace = WORKDIR
        self.p4_config = config.src_p4
        self.p4 = P4Client(
            host=self.p4_config.port,
            username=self.p4_config.username,
            password=self.p4_config.password,
            client=self.p4_config.client,
            charset=P4Client.Charset.CP936,
        )
        self.p4.set_view(views=self.p4_config.views)
        self.p4.set_root(path=self.p4_config.root)
        self.p4.set_options(allwrite=True, clobber=True, rmdir=True)
        self.p4.set_line_end("WIN")
        # self.p4.set_encoding("GBK")

    def get_changes(self, changlist: str) -> list:
        changes = self.p4.get_changes(f"{self.p4_config.need_distribute_dir}/...@{changlist},now", max=1000)
        log.info(f"changes: {changes}")
        return changes

    def get_changed_files(self, changlist: str) -> list:
        changed_files = self.p4.get_files_by_changelist(changlist, depotfile_only=False)

        depots_files = changed_files.get("depotFile", [])
        action = changed_files.get("action", [])
        changed_files = dict(zip(depots_files, action))
        log.info(f"changed_files: {changed_files}")

        return changed_files

    def get_views(self, changed_files: list) -> list:
        p4_views = []
        # 生成一个时间字符串
        time_str = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
        for file in changed_files:
            file_name = file.split("/")[-1]
            view = f"{file} //{self.p4_config.client}/arthub/{time_str}/{file_name}"
            # log.info(f"view: {view}")
            p4_views.append(view)
        env_mgr.resource_dir = os.path.join(self.workspace, "arthub", time_str)
        return p4_views
