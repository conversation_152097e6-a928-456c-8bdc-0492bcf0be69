node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

def clone_pyframe_pipeline(branch) {
    if (!fileExists("pyframe-pipeline/.git")) {
        bat """
        git clone -b $branch https://gitlab.h3d.com.cn/dev-productivity/pyframe-pipeline.git
        git config --local user.name "<EMAIL>"
        git config --local user.email "<EMAIL>"
        """
    }else{
        dir('pyframe-pipeline') {
            bat """
            git config pull.rebase false
            git clean -xdf -e logs
            git reset --hard HEAD
            git fetch --all
            git checkout $branch
            git pull origin $branch --quiet
            """
        }
    }
    dir('pyframe-pipeline') {
        bat """
        python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
        python -m pip install -r requirements.txt -i  http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
        """
    }
}

pipeline {
    agent {
            node{
                label "cdn-git-auto-merge"
            }
        }
    options {
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    stages {
        stage("下载pyframe脚本") {
            steps {
                script {
                    branch = "master"
                    clone_pyframe_pipeline(branch)
                }
            }
        }
        stage('获取待合并分支'){
            steps {
                dir('pyframe-pipeline'){
                    script {
                        bat(script: "python x5m.py auto_merge_x5mconfig_cdn --job=get_branch")
                    }
                }
            }
        }
        stage('合并'){
            steps {
                dir('pyframe-pipeline'){
                    script {
                        bat(script: "python x5m.py auto_merge_x5mconfig_cdn --job=merge")
                    }
                }
            }
        }
        stage('是否继续'){
            steps {
                dir('pyframe-pipeline'){
                    script {
                        bat(script: "python x5m.py auto_merge_x5mconfig_cdn --job=whether_continue")
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("pyframe-pipeline"){
                script {
                    bat(script: "python x5m.py auto_merge_x5mconfig_cdn --job=on_unstable")
                }
            }
        }
        success {
            dir("pyframe-pipeline"){
                script {
                    bat(script: "python x5m.py auto_merge_x5mconfig_cdn --job=on_success")
                }
            }
        }
        failure {
            dir("pyframe-pipeline"){
                script {
                    bat(script: "python x5m.py auto_merge_x5mconfig_cdn --job=on_failure")
                }
            }
        }
    }
}