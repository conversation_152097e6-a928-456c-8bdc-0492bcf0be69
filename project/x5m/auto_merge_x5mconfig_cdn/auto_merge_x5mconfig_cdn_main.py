# coding=utf-8
from frame import *
from project.x5m.auto_merge_x5mconfig_cdn.mgr.env_mgr import env_mgr
from project.x5m.auto_merge_x5mconfig_cdn.mgr.x5mconfig_gitlab_mgr import x5mconfig_gitlab_mgr


# 1. x5mconfig cdn分支的钩子触发
# x 2. 获取最新的commit id, commit msg
# √ 3. 获取最新的两个分支以及拓展分支
# √ 4. 依次创建merge request
# √ 5. 通知
# √ 成功分支、失败分支、失败原因
# √ merge的文件
# 提交人
# √ merge失败iid


# 一个分支merge失败，其他分支还要继续merge
@advance.stage(stage="获取需要合并的分支")
def get_branch(**kwargs):
    merge_branches = x5mconfig_gitlab_mgr.get_merge_branches()
    log.info(f"need merge branches:{merge_branches}")
    env_mgr.set_merge_branches(merge_branches=merge_branches)


@advance.stage(stage="合并")
def merge(**kwargs):
    merge_branches = env_mgr.get_merge_branches()
    x5mconfig_gitlab_mgr.merge_from_cdn(target_branches=merge_branches)

@advance.stage(stage="合并(git)")
def merge_by_git(cwd, **kwargs):
    logging.info(f"call merge_by_git with args: {cwd} {kwargs}")
    merge_branches = env_mgr.get_merge_branches()
    work_dir = os.path.dirname(cwd)
    local_repo_path = os.path.join(work_dir, 'x5mconfig_in_merge')
    x5mconfig_gitlab_mgr.merge_by_git(local_repo_path, target_branches=merge_branches)


@advance.stage(stage="是否继续")
def whether_continue(**kwargs):
    success_branches = env_mgr.get_success_branches()
    failed_branches = env_mgr.get_failed_branches()
    changed_files = env_mgr.get_change_files()
    if len(failed_branches) == 0 and len(success_branches) == 0 or len(changed_files) == 0:
        pipeline_mgr.stop_current_build()


def __get_msg() -> str:
    success_branches = ", ".join(env_mgr.get_success_branches())
    failed_branches = ", ".join(env_mgr.get_failed_branches())
    success_iids = ", ".join(env_mgr.get_success_iids())
    failed_iids = ", ".join(env_mgr.get_failed_iids())
    changed_files = "\n".join(env_mgr.get_change_files())
    submitters = ",".join(env_mgr.get_submitters())
    msg = f"**成功分支**: {success_branches}\n" if success_branches else ""
    msg += f"**成功iid**: {success_iids}\n" if success_iids else ""
    msg += f"**失败分支**: {failed_branches}\n" if failed_branches else ""
    msg += f"**失败iid**: {failed_iids}\n" if failed_iids else ""
    msg += f"**提交人**: {submitters}\n" if submitters else ""
    msg += f"**文件**:\n{changed_files}" if changed_files else ""
    return msg


def on_success(**kwargs):
    wechat.send_unicast_post_success(content=__get_msg())
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=14457620-a0c0-45ce-9220-584c19b1dfa7",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(content=__get_msg())
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=14457620-a0c0-45ce-9220-584c19b1dfa7",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_failure()


def on_unstable(**kwargs):
    wechat.send_unicast_post_unstable(content=__get_msg())
    wechat.send_multicast_post_unstable(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=14457620-a0c0-45ce-9220-584c19b1dfa7",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_unstable()
