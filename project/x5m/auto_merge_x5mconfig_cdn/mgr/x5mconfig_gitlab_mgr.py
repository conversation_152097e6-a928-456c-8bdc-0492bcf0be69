# coding=utf-8
from frame import *
from project.x5m import config
import gitlab

from project.x5m.auto_merge_x5mconfig_cdn.mgr.env_mgr import env_mgr


class X5mconfigGitlabMgr:
    def __init__(self):
        self.url = config.GITLAB_MAINTAINER["url"]
        self.token = config.GITLAB_MAINTAINER["token"]
        self.gitlab_mgr = GitlabMgr(url=self.url, token=self.token, project="dgm/x5mconfig")
        self.gl = gitlab.Gitlab(self.url, self.token)
        self.project = self.gl.projects.get(52)

    def get_merge_branches(self):
        """
        获取最新的三个分支、对应拓展分支和master
        """
        all_branches = self.gitlab_mgr.get_all_branches()
        log.info(f"all branch:{all_branches}")
        branches = []
        for branch in all_branches:
            if branch.count(".") == 2:
                branches.append(branch.replace(".", "").split("_")[0])
        x = list(set(branches))
        x.sort(reverse=True)
        merge_branches = []
        for i in all_branches:
            # 临时屏蔽  7.02.0_tc, 7.02.0_top, 7.02.0_vedio, 7.03.0_wetest 这四个分支
            if i in {"7.02.0_tc", "7.02.0_top", "7.02.0_vedio", "7.03.0_wetest"}:
                continue
            if i.replace(".", "").split("_")[0] in x[0:3] and i.count(".") == 2:
                merge_branches.append(i)
        merge_branches.append("master")
        return merge_branches

    def merge_from_cdn(self, target_branches: list):
        """
        从cdn分支合并到目标分支
        Args:
            target_branches: 目标分支
        """
        source_branch = "cdn"
        log.info(f"target_branches: {target_branches}")
        failed_branches, success_branches, failed_iids, success_iids, changed_files, submitters = [], [], [], [], [], []
        for target_branch in target_branches:
            try:
                mr, result = x5mconfig_gitlab_mgr.gitlab_mgr.merge(
                    source_branch=source_branch,
                    target_branch=target_branch,
                    merge_when_pipeline_succeeds=False,
                    title=f"##skipcodecheck## cdn config auto merge to {target_branch}",
                )
                if result:
                    success_branches.append(target_branch)
                    success_iids.append(str(mr.iid))
                else:
                    failed_branches.append(target_branch)
                    failed_iids.append(str(mr.iid))
                changes = mr.changes()
                for change in changes["changes"]:
                    changed_files.append(change["new_path"])
                # 提交人
                commits = mr.commits()
                for commit in commits:
                    log.info(f"commit: {commit}")
                    submitters.append(commit.author_name)
            except Exception as e:
                log.warn(e)
                failed_branches.append(target_branch)

        # 变更的文件
        env.set({"failed_branches": failed_branches, "success_branches": success_branches})
        log.info(f"success_iids: {success_iids}")
        log.info(f"success_branches: {success_branches}")
        log.info(f"failed_iids: {failed_iids}")
        log.info(f"failed_branches: {failed_branches}")
        env_mgr.set_failed_branches(failed_branches=failed_branches)
        env_mgr.set_success_branches(success_branches=success_branches)
        env_mgr.set_success_iids(success_iids=success_iids)
        env_mgr.set_failed_iids(failed_iids=failed_iids)
        env_mgr.set_change_files(change_files=list(set(changed_files)))
        env_mgr.set_submitters(submitters=list(set(submitters)))

        if len(failed_branches) > 0:
            raise PyframeException(f"{source_branch}分支merge到{', '.join(failed_branches)}出错")


x5mconfig_gitlab_mgr = X5mconfigGitlabMgr()
