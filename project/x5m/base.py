import logging
import typing

class CachedMeta(type):
    def __init__(cls, *args, **kwargs):
        super().__init__(*args, **kwargs)
        cls._cache_ = {}


class ResourceManager(metaclass=CachedMeta):
    kind = 'base'

    class P4Depot:
        CDN_RULE = "//x5mplan/resmg/{kind}/cdn-rule.xlsx"
        HOTFIX_RULE = "//x5mplan/resmg/{kind}/hotfix-rule.xlsx"
        SPECIAL_RULE = "//x5mplan/resmg/{kind}/special-rule.xlsx"


    def __init__(self, *args, **kwargs):
        self.args = args
        self.kwargs = kwargs

    @staticmethod
    def get_user_from_changes(changes: typing.List[dict]) -> typing.List[str]:
        send_users = set()
        for change in changes:
            user = change.get('user') or ""
            if not user:
                continue
            if not user.endswith("@h3d.com.cn"):
                user = "{}@h3d.com.cn".format(user)
            send_users.add(user)
        return list(send_users)

    @staticmethod
    def win_path_to_linux(path: str) -> str:
        path = path.lower().replace("\\", "/")
        path = path.replace('//', '/')
        return path


    @classmethod
    def echo(cls, *args, **kwargs):
        logging.info(f"********   class: {cls.__name__}, kind:{cls.kind}, args:{args}, kwargs:{kwargs}   ********")
        return 0
