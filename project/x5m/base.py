import logging
import os
import socket
import typing
import zlib

import yaml

from frame.env.env import env


class P4PersistentHelper:
    namespace = "x5m"

    def __init__(self, mgr: typing.Type["ResourceManager"]):
        self.mgr = mgr

    def set_last_changelist(self, changelist: int, platform):
        if not changelist:
            return
        changelist = int(changelist)
        last_changelist = int(self.get_last_changelist(platform) or 0)
        if not last_changelist or last_changelist < changelist:
            env.set_global(
                {f"{self.mgr.kind}-{platform}": str(changelist)},
                doc_id=f"{self.namespace}_{self.mgr.__class__.__name__}",
            )
            return

    def get_last_changelist(self, platform):
        doc_id = f"{self.namespace}_{self.mgr.__class__.__name__}"
        return env.get_global(f"{self.mgr.kind}-{platform}", doc_id=doc_id)


class CachedMetaV2(type):
    def __init__(cls, name, bases=None, props=None):
        super().__init__(name, bases, props)
        cls._cache_ = {"__name__": name}


class DBMeta(type):
    def __init__(cls, name, bases=None, props=None):
        super().__init__(name, bases, props)
        cls._database_ = P4PersistentHelper(cls)


class DBAndCacheMeta(type):
    def __init__(cls, name, bases=None, props=None):
        super().__init__(name, bases, props)
        cls._cache_ = {"__name__": name}
        cls._database_ = P4PersistentHelper(cls)


class CachedMeta(type):
    def __init__(cls, what, bases=None, dict=None):
        cls._cache_ = {}
        cls._database_ = P4PersistentHelper(cls)


class ResourceManager(metaclass=CachedMeta):
    kind = "base"

    def __init__(self, *, workspace, **kwargs):
        logging.info(f"{self.__class__.__name__} init with: workspace:{workspace}, {kwargs}")
        self.workspace = workspace

    @staticmethod
    def get_lan_ip():
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # 不需要真正发送数据，所以使用一个不存在的地址
            s.connect(("**************", 1))
            # 获取本地地址
            return s.getsockname()[0]

    @staticmethod
    def get_user_from_changes(changes: typing.List[dict]) -> typing.List[str]:
        send_users = set()
        for change in changes:
            user = change.get("user") or ""
            if not user:
                continue
            if not user.endswith("@h3d.com.cn"):
                user = "{}@h3d.com.cn".format(user)
            send_users.add(user)
        return list(send_users)

    @staticmethod
    def win_path_to_linux(path: str) -> str:
        path = path.lower().replace("\\", "/")
        path = path.replace("//", "/")
        return path

    @classmethod
    def echo(cls, *args, **kwargs):
        logging.info(f"********   class: {cls.__name__}, kind:{cls.kind}, args:{args}, kwargs:{kwargs}   ********")
        return 0


class Pipeline(ResourceManager):
    kind = "pipeline"
    context = {
        "workflow": [],
        "detail": {},
        "preceding_return": {},
    }

    @classmethod
    def get_ins_id(cls, workspace: str, platform: str = "", **kwargs):
        return cls.create_ins(workspace, platform, **kwargs)

    @classmethod
    def create_ins(cls, workspace: str, platform: str = "", **kwargs):
        os.environ["PLATFORM"] = platform
        root = workspace.lower().replace("\\", "/")
        ins = cls(workspace=workspace, root=root, platform=platform, **kwargs)
        if "changelist" in kwargs:
            ins.changelist = kwargs["changelist"]
        u4 = zlib.adler32(f"{workspace}_{platform}_{cls.get_lan_ip()}".encode())
        ins.ins_id = f"{cls.kind}_{u4}_{platform}"
        __init__ = dict(workspace=workspace, root=root, platform=platform, **kwargs)
        context = cls.context
        context["__init__"] = __init__
        path = os.path.join(os.path.expanduser("~"), f"{ins.ins_id}.yaml")
        with open(path, "w", encoding="utf8") as f:
            yaml.dump(
                context,
                f,
                default_flow_style=False,
                allow_unicode=True,
                sort_keys=False,
            )
        return ins.ins_id

    @classmethod
    def load_ins(cls, context_path: str = None):
        if not os.path.exists(context_path):
            context_path = os.path.join(os.path.expanduser("~"), f"{context_path}.yaml")
        if not os.path.exists(context_path):
            raise ValueError(f"Path not exists: {context_path}")
        with open(context_path, "r", encoding="utf8") as steam:
            context = yaml.load(steam, yaml.loader.FullLoader)
        __init__ = context["__init__"]
        return cls(**__init__)


if __name__ == "__main__":
    workspace = os.getcwd()
    root = workspace.lower().replace("\\", "/")
    ins_id = Pipeline.create_ins(workspace=workspace)
    print(ins_id)
