import importlib
import os
import inspect
import logging
import typing
import pkgu<PERSON>


def get_submodules_path(path):
    """
    获取当前目录下的所有Python子模块。
    :return: 子模块的列表
    """
    # 获取当前脚本的目录
    # 使用pkgutil.iter_modules遍历当前目录下的所有模块
    submodules = {name: {} for finder, name, ispkg in pkgutil.iter_modules([path])}
    for k in submodules:
        k_path = os.path.join(path, k)
        if os.path.isdir(k_path):
            submodules[k] = get_submodules_path(k_path)
    return submodules


def format_submodules(modules: dict):
    result = []
    for k, v in modules.items():
        if not v:
            result.append(k)
        else:
            for sub in format_submodules(v):
                result.append(f"{k}.{sub}")
    return result


def get_kind_cls(
    kind,
    src_path: str,
    predicate: typing.Callable[[typing.Any, str], bool],
    ignore_dirs=('auto_merge_witebox', 'language', 'ui_scripts', 'tests', 'pyframe_test')
) -> typing.Tuple[str, typing.Type[typing.Any]]:
    """获取名为kind的第一个类

    :param kind:
    :param src_path:
    :param predicate:
    :param ignore_dirs:
    :return:
    """
    modules = get_submodules_path(path=src_path)

    result = format_submodules(modules)

    def _predicate(obj):
        return predicate(obj, kind)

    cls_list = None
    for m in result:
        try:
            # art_package.scripts.resource_manager.misc
            if any(char in m for char in ignore_dirs):
                continue
            logging.info(f"current m: {m}")
            m = importlib.import_module(m)
            cls_list = inspect.getmembers(m, _predicate)
            if cls_list:
                break
        except Exception as e:
            logging.warning(f"{m} get error of {e}")
            continue
    if not cls_list:
        raise ValueError(f"Cant found class with kind == {kind}")
    return cls_list[0]


if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    DIR = os.path.dirname(os.path.abspath(__file__))

    name, cls = get_kind_cls("plaza_scene_effect", DIR, predicate=lambda obj, kind: inspect.isclass(obj) and getattr(obj, 'kind', None) == kind)
    print(cls.unity_src())
