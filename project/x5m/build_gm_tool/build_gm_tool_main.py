# coding=utf-8
import datetime
import re

from frame import *
from project.x5m import config
from project.x5m.build_gm_tool.mgr.build_gm_mgr import BuildGmMgr
from project.x5m.build_gm_tool.mgr.gm_git_mgr import GmGitMgr
from project.x5m.build_gm_tool.mgr.link_mgr import LinkMgr
from project.x5m.build_gm_tool.mgr.p4_com_1666 import P4COM1666
from project.x5m.build_gm_tool.mgr.zip_gm_mgr import ZipGmMgr
from project.x5m.build_gm_tool.mgr.env_mgr import *


@advance.stage(stage="更新gitlab代码")
def prepare(**kwargs):
    branch = bk_env_mgr.get_git_branch_name()
    lastest_branch = get_latest_branch()
    now_hour = datetime.datetime.now().hour
    deploy_env = bk_env_mgr.get_deploy_environment()

    if now_hour == 8:
        branch = "master"
    elif now_hour == 9:
        branch = lastest_branch
        deploy_env = "branch"
    bk_env_mgr.set_git_branch_name(branch_name=branch)
    bk_env_mgr.set_deploy_environment(deploy_environment=deploy_env)


# python x5m.py build_gm_tool --job=update_gitlab
@advance.stage(stage="更新gitlab代码")
def update_gitlab(**kwargs):
    gm_git_mgr = GmGitMgr()
    gm_git_mgr.update_x5mobile()
    gm_git_mgr.update_x5mconfig()
    gm_git_mgr.update_x5mweek_resources()
    gm_git_mgr.update_x5mweek_release()
    gm_git_mgr.update_x5mweek_online()


@advance.stage(stage="更新p4资源")
def update_p4(**kwargs):
    p4_com_1666 = P4COM1666()
    p4_com_1666.sync_all(force=bk_env_mgr.get_force_update_p4())

    link_mgr = LinkMgr()
    link_mgr.link_mr()


@advance.stage(stage="创建链接")
def make_link(**kwargs):
    link_mgr = LinkMgr()
    link_mgr.make_link()


@advance.stage(stage="编译gm工具")
def build_gm(**kwargs):
    build_gm_mgr = BuildGmMgr()
    build_gm_mgr.build_gm_client()
    build_gm_mgr.build_press_test_main()
    build_gm_mgr.choice()
    build_gm_mgr.build_IDIP_client_console()


@advance.stage(stage="压缩gm工具包")
def make_gm_zip(**kwargs):
    zip_gm_mgr = ZipGmMgr()
    zip_gm_mgr.copy()
    zip_gm_mgr.tar()


@advance.stage(stage="上传FTP")
def upload_ftp(**kwargs):
    branch = bk_env_mgr.get_git_branch_name()
    deploy_env = bk_env_mgr.get_deploy_environment()
    ftp = FtpMgr(**config.FTP_CONFIG_150_30)
    ftp.upload_file(
        src=os.path.join(env.pipeline.workspace(), f"{deploy_env}/x5mobile/{branch}/mobile_dancer/trunk/exe/GMClient.zip"),
        dst=f"/version_test/server_pack/{deploy_env}/",
    )
    dst = common.join_url("https://dgmdd.h3d.com.cn/dgm", f"/version_test/server_pack/{deploy_env}/GMClient.zip")
    env.set({"FTP_DST": dst})


def __get_committer():
    errors = env.get("errors")
    gm_git_mgr = GmGitMgr()

    errors_with_committer = []
    committers = []
    if isinstance(errors, list):
        for err in errors:
            err = err.strip("\n")
            try:
                file_rel_path = err.split("(")[0].strip()
                line_num = re.findall(re.compile(r"[(](.*?)[,]", re.S), err)[0].strip()
                p = os.path.join("mobile_dancer/trunk/client", file_rel_path)
                if p.startswith("mobile_dancer/trunk/client"):
                    committer = gm_git_mgr.get_x5mobile_committer(
                        path=os.path.join("mobile_dancer/trunk/client", file_rel_path),
                        line=line_num,
                    )
                    errors_with_committer.append(f"{err} <@{committer}>")
                    committers.append(committer)
                else:
                    errors_with_committer.append(err)
            except Exception as e:
                log.error(e)
                continue
    else:
        log.error("errors is not list")
    env.set(
        {
            "COMMITTERS": committers,
            "ERRORS_WITH_COMMITTER": errors_with_committer,
        }
    )


def __get_msg():
    branch = bk_env_mgr.get_git_branch_name()
    deploy_env = bk_env_mgr.get_deploy_environment()
    upload_dst = env.get("FTP_DST")

    msg = f"**git分支**: {branch}\n"
    msg += f"**部署环境**: {deploy_env}\n"
    if upload_dst:
        gm_client = upload_dst.split("/")[-1]
        msg += f"**GM包:** [{gm_client}]({upload_dst})\n"

    errors_with_committer = env.get("ERRORS_WITH_COMMITTER")
    if errors_with_committer is not None:
        if len(errors_with_committer) > 0:
            msg += "**错误定位**: \n"
            msg += "\n".join(errors_with_committer)

    return msg


def on_success(**kwargs):
    commiters = env.get("COMMITTERS")
    user_list = list(set(commiters)) if commiters is not None else []
    wechat.send_unicast_post_success(user_list=user_list, content=__get_msg())
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=80ba345f-35ef-4346-9a76-616a5b171b77",
        mentioned_list=[],
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    try:
        __get_committer()
    except Exception as e:
        log.error(e)
    commiters = env.get("COMMITTERS")
    user_list = list(set(commiters)) if commiters is not None else []
    wechat.send_unicast_post_failure(user_list=user_list, content=__get_msg())
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=80ba345f-35ef-4346-9a76-616a5b171b77",
        mentioned_list=[],
        content=__get_msg(),
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    commiters = env.get("COMMITTERS")
    user_list = list(set(commiters)) if commiters is not None else []
    wechat.send_unicast_post_canceled(user_list=user_list, content=__get_msg())
    wechat.send_multicast_post_canceled(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=80ba345f-35ef-4346-9a76-616a5b171b77",
        mentioned_list=[],
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_canceled()


def compare_version(v1: str, v2: str) -> int:
    for _v1, _v2 in zip(v1.split("."), v2.split(".")):
        _v1, _v2 = int(_v1), int(_v2)
        if v1 > v2:
            return 1
        elif v1 < v2:
            return -1
    return 0


def get_latest_branch() -> str:
    git_x5moblie_mgr = GitlabMgr(
        url=config.GITLAB_MAINTAINER["url"],
        token=config.GITLAB_MAINTAINER["token"],
        project="dgm/x5mobile",
    )
    branches = git_x5moblie_mgr.get_all_branches()
    branch_num = []
    for branch in branches:
        num = re.findall(r"^\d.\d+.\d$", branch)
        if num:
            branch_num.append(num[0])
    for branch_one in range(len(branch_num)):
        for branch_two in range(0, len(branch_num) - 1):
            if compare_version(branch_num[branch_one], branch_num[branch_two]) == -1:
                branch_num[branch_one], branch_num[branch_two] = branch_num[branch_two], branch_num[branch_one]
    return branch_num[-1]
