PYTHON = 'C:\\Python\\Python36\\python.exe'

pipeline {
    // *************
    agent {
        label 'gm_util_trad'
    }

    options {
        timestamps()
        disableConcurrentBuilds()
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
        choice(name: 'DEPLOY_ENVIRONMENT', choices: ['branch','trunk', 'release', 'resources', 'online'], description: '需要部署的环境')
        string(name: 'GIT_BRANCH_NAME', defaultValue: '7.12.0_tc', description: '分支名，默认主支master，也可以其他分支, 例如 5.12.0')
        booleanParam(name: 'FORCE_UPDATE_P4', defaultValue: false, description: '是否强更P4')
    }
    environment {
        DEPLOY_ENVIRONMENT = "${params.DEPLOY_ENVIRONMENT}"
        GIT_BRANCH_NAME = "${params.GIT_BRANCH_NAME}"
        FORCE_UPDATE_P4 = "${params.FORCE_UPDATE_P4}"
        LANGUAGE = 'chin_trad'
    }

     triggers {
         // 周一至周五的8, 9, 13:30运行
         cron('0 8,9 * * 1-5\n 30 13 * * 1-5')
     }
    stages {
        stage('创建或者更新虚拟环境') {
            steps {
                script {
                    def python_venv_path = 'venv_pyframe'
                    if (!fileExists(python_venv_path)) {
                        bat script: """python3 -m venv $python_venv_path"""
                    }
                    PYTHON = "${WORKSPACE}\\${python_venv_path}\\Scripts\\python3"
                    def python_version = bat script: "${PYTHON} --version", returnStdout: true
                    echo "当前python版本: ${python_version}"
                }
            }
        }
        stage('更新pip version到21.3.1') {
            steps {
                script {
                    bat script:"""${PYTHON} -m pip install -U "pip==21.3.1" -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn"""

                }
            }
        }
        stage('安装requirements.txt') {
            steps {
                script {
                    dir('pyframe-pipeline') {
                        bat script: """${PYTHON} -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn"""
                    }
                }
            }
        }
        stage('prepare') {
            steps {
                script {
                    dir('pyframe-pipeline') {
                        bat script: "${PYTHON} x5m.py build_gm_tool --job=prepare --branch=${GIT_BRANCH_NAME}"
                    }
                }
            }
        }
        stage('更新gitlab代码') {
            steps {
                script {
                    dir('pyframe-pipeline') {
                        bat script: "${PYTHON} x5m.py build_gm_tool --job=update_gitlab"
                    }
                }
            }
        }
        stage('更新P4资源') {
            steps {
                script {
                    dir('pyframe-pipeline') {
                        bat script: "${PYTHON} x5m.py build_gm_tool --job=update_p4"
                    }
                }
            }
        }
        stage('链接和拷贝资源') {
            steps {
                script {
                    dir('pyframe-pipeline') {
                        bat script: "${PYTHON} x5m.py build_gm_tool --job=make_link"
                    }
                }
            }
        }
        stage('创建region链接') {
            steps {
                script {
                    dir('pyframe-pipeline') {
                        bat script: "${PYTHON} x5m.py build_gm_tool --job=make_region_link"
                    }
                }
            }
        }
        stage('编译GMclient.exe') {
            steps {
                script {
                    dir('pyframe-pipeline') {
                        bat script: "${PYTHON} x5m.py build_gm_tool --job=build_gm"
                    }
                }
            }
        }
        stage('制作GM工具包') {
            steps {
                script {
                    dir('pyframe-pipeline') {
                        bat script: "${PYTHON} x5m.py build_gm_tool --job=make_gm_zip"
                    }
                }
            }
        }
        stage('上传ftp') {
            steps {
                script {
                    dir('pyframe-pipeline') {
                        bat script: "${PYTHON} x5m.py build_gm_tool --job=upload_ftp"
                    }
                }
            }
        }
    }

    post {
        always {
            script {
                dir('pyframe-pipeline') {
                    // 使用build-user-vars-plugin插件提供的步骤来获取构建用户
                    wrap([$class: 'BuildUser']) {
                        env.BUILD_USER = "${BUILD_USER}"
                        env.BUILD_ID = "${BUILD_ID}"
                        env.DURATION_STRING = currentBuild.durationString
                        env.CURRENT_RESULT = currentBuild.currentResult
                        env.PIPELINE_NAME = currentBuild.fullDisplayName
                        // 定时触发时，获取不到构建用户的邮箱, 远程构建触发时, 也获取不到构建邮箱
                        try {
                            env.BUILD_USER_EMAIL = "${BUILD_USER_EMAIL}"
                        } catch (exc) {
                            echo "获取不到构建的用户邮箱"
                            env.BUILD_USER_EMAIL = ""
                        }
                    }
                    bat script: "${PYTHON} x5m.py build_gm_tool --job=on_always"
                }
            }
        }
    }
}
