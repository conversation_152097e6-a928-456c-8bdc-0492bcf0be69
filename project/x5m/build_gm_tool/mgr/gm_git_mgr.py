# coding=utf-8
from pathlib import Path

from frame import *
from project.x5m.build_gm_tool.mgr.env_mgr import *


class GmGitMgr:
    def __init__(self) -> None:
        self._workdir = env.pipeline.workspace()
        self._deploy_env = bk_env_mgr.get_deploy_environment()
        self._branch = bk_env_mgr.get_git_branch_name()
        self.x5mobile_path = os.path.join(self._deploy_env, "x5mobile", self._branch)
        self.x5mconfig_path = os.path.join(self._deploy_env, "x5mconfig", self._branch)
        self.x5mweek_resources_path = os.path.join(self._deploy_env, "x5mweek", "resources")
        self.x5mweek_release_path = os.path.join(self._deploy_env, "x5mweek", "release")
        self.x5mweek_online_path = os.path.join(self._deploy_env, "x5mweek", "online")

        self.x5mobile_git = GitMgr(workdir=self._workdir, project_name=self.x5mobile_path)
        self.x5mconfig_git = GitMgr(workdir=self._workdir, project_name=self.x5mconfig_path)
        self.x5mweek_resources_git = GitMgr(workdir=self._workdir, project_name=self.x5mweek_resources_path)
        self.x5mweek_release_git = GitMgr(workdir=self._workdir, project_name=self.x5mweek_release_path)
        self.x5mweek_online_git = GitMgr(workdir=self._workdir, project_name=self.x5mweek_online_path)

    def update_x5mobile(self) -> None:
        if not Path(os.path.join(self._workdir, self.x5mobile_path)).exists():
            self.x5mobile_git.clone(
                url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git",
                branch=self._branch,
            )
        else:
            if Path(os.path.join(self._workdir, self.x5mobile_path, ".gitattributes")).exists():
                self.x5mobile_git.exec("git rm .gitattributes")
            self.x5mobile_git.reset(commit_id="HEAD")
            self.x5mobile_git.exec("git pull")
            self.x5mobile_git.clean()
            self.x5mobile_git.advance_pull(branch=self._branch)

    def update_x5mconfig(self) -> None:
        if not Path(os.path.join(self._workdir, self.x5mconfig_path)).exists():
            self.x5mconfig_git.clone(
                url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig.git",
                branch=self._branch,
            )
        else:
            self.x5mconfig_git.reset()
            self.x5mconfig_git.clean()
            self.x5mconfig_git.advance_pull(branch=self._branch)

    def update_x5mweek_resources(self) -> None:
        if not Path(os.path.join(self._workdir, self.x5mweek_resources_path)).exists():
            self.x5mweek_resources_git.clone(
                url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mweek.git",
                branch="resources",
            )
        else:
            self.x5mweek_resources_git.reset()
            self.x5mweek_resources_git.clean()
            self.x5mweek_resources_git.advance_pull(branch="resources")

    def update_x5mweek_release(self) -> None:
        if not Path(os.path.join(self._workdir, self.x5mweek_release_path)).exists():
            self.x5mweek_release_git.clone(
                url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mweek.git",
                branch="release",
            )
        else:
            self.x5mweek_release_git.reset()
            self.x5mweek_release_git.clean()
            self.x5mweek_release_git.advance_pull(branch="release")

    def update_x5mweek_online(self) -> None:
        if not Path(os.path.join(self._workdir, self.x5mweek_online_path)).exists():
            self.x5mweek_online_git.clone(
                url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mweek.git",
                branch="online",
            )
        else:
            self.x5mweek_online_git.reset()
            self.x5mweek_online_git.clean()
            self.x5mweek_online_git.advance_pull(branch="online")

    def get_x5mobile_committer(self, path: str, line: int) -> str:
        return self.x5mobile_git.blame(path=path, line=line).committer_mail
