# coding=utf-8
from frame import *
from project.x5m.build_gm_tool.mgr.env_mgr import *


class LinkMgr:
    def __init__(self):
        self._workdir = env.pipeline.workspace()
        self._branch = bk_env_mgr.get_git_branch_name()
        self._deploy_env = bk_env_mgr.get_deploy_environment()
        self._x5mobile_path = os.path.join(self._workdir, f"{self._deploy_env}/x5mobile/{self._branch}")  # xm5mobile仓库路径
        self._x5mconfig_path = os.path.join(self._workdir, f"{self._deploy_env}/x5mconfig/{self._branch}")  # xm5config仓库路径
        if self._branch == "master":
            self._x5mobile_mr_path = os.path.join(self._workdir, f"{self._deploy_env}/x5mobile/{self._branch}/mr/Resources")
        else:
            self._x5mobile_mr_path = os.path.join(self._workdir, f"{self._deploy_env}/x5mobile/{self._branch}/mr/b/{self._branch}")

    def link_mr(self):
        path_mgr.soft_link(
            src=os.path.join(env.pipeline.workspace(), f"{self._deploy_env}/p4/x5_mobile/mr"),
            dst=os.path.join(env.pipeline.workspace(), f"{self._deploy_env}/x5mobile/{self._branch}/mr"),
        )

    def make_link(self):
        """
        创建链接
        """
        path_mgr.mkdir(path=os.path.join(self._x5mobile_path, "mobile_dancer\\trunk\\exe\\resources\\config"))
        # 服务器配置映射
        path_mgr.soft_link(
            src=os.path.join(self._x5mconfig_path, "config\\server"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer\\trunk\\exe\\resources\\config\\server"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mconfig_path, "config/shared"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/exe/resources/config/shared"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client_build_config/config/shared/tencent_xg"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/exe/resources/config/shared/tencent_xg"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mconfig_path, "level"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/exe/resources/level"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mconfig_path, "experience"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/exe/resources/experience"),
        )

        # 客户端配置映射
        path_mgr.soft_link(
            src=os.path.join(self._x5mconfig_path, "config"),
            dst=os.path.join(self._x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/xml_sources/config"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mconfig_path, "level/musicact"),
            dst=os.path.join(self._x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/xml_sources/level/musicact"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mconfig_path, "level/wedding"),
            dst=os.path.join(self._x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/xml_sources/level/wedding"),
        )

        # 客户端资源映射
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/windows/assetbundles"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client/assetbundles/editorCdn/assetbundles"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/android/assetbundles"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/crossplatform/config"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/config"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/crossplatform/level"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/level"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/crossplatform/luascript"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/luascript"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/crossplatform/luascript"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client/GameLuaProject/luascript"),
        )
        # path_mgr.soft_link(
        #     src=os.path.join(self.__x5mconfig_path, "audio/bgm"),
        #     dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/audio/bgm")
        # )
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/crossplatform/audio/sound_effect"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/audio/sound_effect"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_mr_path, "art_src/sta/3d/fitment"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client/Assets/StaticResources/art/3d/fitment"),
        )
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_mr_path, "art_src/sta/3d/fitment"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client/Assets/resources/Art/3d/fitment"),
        )
        # path_mgr.soft_link(
        #     src=os.path.join(self.__x5mobile_mr_path, "Editor/config"),
        #     dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/Assets/engine/Editor/PlotEditor/Plot/config")
        # )

        # TSS脏词本地检测用
        path_mgr.mkdir(path="C:/temp/tsssdk_cfg")
        path_mgr.copy(src=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/tools/tss_local_cfg/tss_sdk_conf.xml"), dst="C:/temp/tsssdk_cfg/")
        path_mgr.copy(src=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/tools/tss_local_cfg/vocabulary.txt"), dst="C:/temp/tsssdk_cfg/")

    def make_region_link(self) -> None:
        """创建region相关链接

        因为目录变动，需要添加对应的软链
        """
        lang = "simp"
        _src_base_dir_path = f"mobile_dancer/trunk/client_region/{lang}"
        _dst_base_dir_path = "mobile_dancer/trunk/client/Assets/"
        # 判断如果client_region目录不存在，则跳过
        if not path_mgr.exists(os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client_region")):
            return

        # 创建 experience 软链
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_path, f"{_src_base_dir_path}/experience"),
            dst=os.path.join(self._x5mobile_path, "mobile_dancer/trunk/client/experience"),
        )

        # 创建 GcloudSDK 软链
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_path, f"{_src_base_dir_path}/Assets/GCloudSDK"),
            dst=os.path.join(self._x5mobile_path, f"{_dst_base_dir_path}/GCloudSDK"),
        )

        # 创建 Plugin 软链
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_path, f"{_src_base_dir_path}/Assets/Plugins"),
            dst=os.path.join(self._x5mobile_path, f"{_dst_base_dir_path}/Plugins"),
        )

        # 创建 proxy 和 GcloudVoice 脚本软链
        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_path, f"{_src_base_dir_path}/Assets/Scripts/Proxy"),
            dst=os.path.join(self._x5mobile_path, f"{_dst_base_dir_path}/Scripts/Proxy"),
        )

        path_mgr.soft_link(
            src=os.path.join(self._x5mobile_path, f"{_src_base_dir_path}/Assets/Scripts/GCloudVoice"),
            dst=os.path.join(self._x5mobile_path, f"{_dst_base_dir_path}/Scripts/GCloudVoice"),
        )
