import stat

from frame import *
from project.x5m.build_gm_tool.mgr.env_mgr import *


class ZipGmMgr:
    def __init__(self):
        self._workdir = env.pipeline.workspace()
        self._deploy_env = bk_env_mgr.get_deploy_environment()
        self._branch = bk_env_mgr.get_git_branch_name()
        self._exe_path = os.path.join(self._workdir, f"{self._deploy_env}\\x5mobile\\{self._branch}\\mobile_dancer\\trunk\\exe")

    def copy(self):
        ab_path = os.path.join(
            self._workdir, f"{self._deploy_env}\\x5mobile\\{self._branch}\\mobile_dancer\\trunk\\client\\assetbundles\\cdn\\assetbundles"
        )
        tmp_path = os.path.join(self._exe_path, "tmp")
        path_mgr.rm(path=tmp_path)
        path_mgr.mkdir(path=tmp_path)
        # os.chmod()更改文件或权限的目录
        # stat.S_IRWXU: 拥有者有全部权限
        # stat.S_IRWXG: 组用户有全部权限
        # stat.S_IRWXO: 其他用户有全部权限
        os.chmod(path=tmp_path, mode=stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)
        zip_root_path = os.path.join(tmp_path, "exe")
        path_mgr.mkdir(path=zip_root_path)
        path_mgr.mkdir(path=os.path.join(zip_root_path, "bin"))
        path_mgr.copy(
            src=os.path.join(self._exe_path, "bin\\GMClient.exe"),
            dst=os.path.join(zip_root_path, "bin\\GMClient.exe"),
        )
        resource_dst_path = os.path.join(zip_root_path, "resources")
        path_mgr.mkdir(path=resource_dst_path)
        path_mgr.copy(
            src=os.path.join(self._exe_path, "resources"),
            dst=resource_dst_path,
        )
        path_mgr.copy(
            src=os.path.join(self._exe_path, "ip_config.csv"),
            dst=zip_root_path,
        )
        path_mgr.copy(
            src=os.path.join(ab_path, "config\\shared"),
            dst=os.path.join(resource_dst_path, "config\\shared"),
        )
        path_mgr.copy(
            src=os.path.join(ab_path, "level"),
            dst=os.path.join(resource_dst_path, "level"),
        )
        if path_mgr.exists(path=os.path.join(self._exe_path, "idip_pipeline.bat")):
            path_mgr.copy(
                src=os.path.join(self._exe_path, "bin\\IDIP_Client_Console_d.exe"),
                dst=os.path.join(zip_root_path, "bin"),
            )
            path_mgr.copy(
                src=os.path.join(self._exe_path, "idip_pipeline.bat"),
                dst=zip_root_path,
            )
            for src in [
                "C:\\Windows\\SysWOW64\\ucrtbase.dll",
                "C:\\Windows\\SysWOW64\\ucrtbased.dll",
                "C:\\Windows\\SysWOW64\\vcruntime140d.dll",
                "C:\\Windows\\SysWOW64\\msvcp140d.dll",
            ]:
                path_mgr.copy(
                    src=src,
                    dst=os.path.join(zip_root_path, "bin"),
                )

    def tar(self):
        tar.compress(
            src=os.path.join(self._exe_path, "tmp"),
            dst=os.path.join(self._exe_path, "GMClient.zip"),
        )
