node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}


pipeline {
    agent {
        node{
            label "*************"
            customWorkspace "/data/workspace/build_server_ci"
        }
    }
    options {
        disableConcurrentBuilds()
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
        choice(choices: ['false', 'true'], description: '是否为rebuild', name: 'is_rebuild')
        booleanParam(defaultValue: false, description: '强制编译，没有服务器代码变更也编译', name: 'FORCE_BUILD')
    }
    stages {
        stage("安装依赖") {
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                        python3 -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        python3 -m pip install -r requirements.txt -i  http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                        """
                    )
                }
             }
        }
        stage('准备'){
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                        python3 x5m.py build_server --job=prepare
                        """
                    )
                }
            }
        }
        stage('克隆x5mobile'){
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                        python3 x5m.py build_server --job=clone_x5mobile
                        """
                    )
                }
            }
        }
        stage('编译服务器'){
            steps {
                dir("pyframe-pipeline") {
                    sh(
                        script: """
                        python3 x5m.py build_server --job=build_server
                        """
                    )
                }
            }
        }
    }
    post {
        unstable{
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                python3 x5m.py build_server --job=post_canceled
                """
            }
        }
        success{
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                python3 x5m.py build_server --job=post_success
                """
            }
        }
        failure{
            dir("pyframe-pipeline") {
                sh label: '',
                script: """
                python3 x5m.py build_server --job=post_failure
                """
            }
        }
    }
}
