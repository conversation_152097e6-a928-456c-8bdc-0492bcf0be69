import re
import time

from pathlib import Path

from frame import advance, env, log, wechat
from project.x5m.test.deploy_public_server.mgr.server_mgr import server_mgr
from project.x5m.build_server.mgr.new_gitlab_mgr import new_gitlab_mgr
from project.x5m.build_server.mgr.env_mgr import EnvMgr


@advance.stage(stage="准备")
def prepare():
    advance.check_disk_size(threshold=5)


@advance.stage("获取最新分支")
def get_latest_branch():
    branches = new_gitlab_mgr.get_all_branches()
    log.info(f"branches: {branches}")

    target_branches = []
    for branch in branches:
        reg = r"^\d.\d+.\d$"
        if re.match(reg, branch) is not None:
            target_branches.append(branch)
    target_branches.sort(reverse=True)
    log.info(f"target_branches is {target_branches}")

    latest_branch = target_branches[0]
    log.info(f"latest branch is {latest_branch}")
    EnvMgr.set_latest_branch(branch=latest_branch)


@advance.stage(stage="克隆x5mobile")
def clone_x5mobile():
    branch = EnvMgr.get_build_branch()
    new_gitlab_mgr.get_max_pipeline_id(branch=branch)
    new_gitlab_mgr.get_commit_id(branch=branch)
    new_gitlab_mgr.clone_x5mobile(branch=branch)
    # 测试编译错误
    # file_mgr.replace_file_string(
    #     file="/data/workspace/x5_mobile/mobile_dancer/trunk/server/products/Project_DGM/mobile/h3d_mobile/core/h3d_mobile_manager_global_lobby.cpp",
    #     old="dynamic_cast",
    #     new="dynamic_cast1111"
    # )


@advance.stage(stage="编译服务器")
def build_server():
    server_mgr.build()


def __get_committer():
    errors = env.get("errors")
    errors_with_committer = []
    committers = []
    if errors:
        for error in errors:
            try:
                error = error.replace("\n", "")
                error_path = error.split(":")[0]
                glob_path = "**/" + error_path.lstrip("../").strip()
                line_num = error.split(":")[1]
                glob_paths = Path("/data/workspace/x5_mobile/mobile_dancer/trunk/server").glob(glob_path)
                for path in glob_paths:
                    committer, commit_time = new_gitlab_mgr.get_x5mobile_committer(path=str(path), line=int(line_num))
                    now = time.time()
                    error = error.replace(error_path, str(path).replace("/data/workspace/x5_mobile/mobile_dancer/trunk/", ""))
                    # 3天内的错误才会通知
                    if int(now - float(commit_time)) < 60 * 60 * 24 * 3:
                        committers.append(committer)
                        errors_with_committer.append(f"{error} <@{committer}>")
            except Exception as e:
                log.error(e)
                continue
    env.set({"COMMITTERS": committers, "ERRORS_WITH_COMMITTER": errors_with_committer})


def __get_msg():
    max_pipeline_id = env.get("max_pipeline_id")
    branch = EnvMgr.get_build_branch()
    msg = ""
    msg += f"**编译分支**: {branch}\n"
    msg += f"**max_pipeline_id**: {max_pipeline_id}\n"
    errors_with_committer = env.get("ERRORS_WITH_COMMITTER")
    if errors_with_committer is not None:
        if len(errors_with_committer) > 0:
            msg += "**错误定位**: \n"
            msg += "\n".join(errors_with_committer)
    return msg


def post_success():
    commiters = env.get("COMMITTERS")
    user_list = list(set(commiters)) if commiters is not None else []
    wechat.send_unicast_post_success(user_list=user_list, content=__get_msg())
    # 成功暂时不发群通知
    # wechat.send_multicast_post_success(
    #     webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ba068c4a-97d2-447a-906f-33728913eb33",
    #     content=__get_msg()
    # )
    advance.insert_pipeline_history_on_success()


def post_failure():
    wechat.send_unicast_post_failure(
        user_list=[],
        content=__get_msg(),
        rescue=True,
        to_admin=True,
    )
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ba068c4a-97d2-447a-906f-33728913eb33",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_failure()


def post_canceled():
    wechat.send_unicast_post_canceled(user_list=[], content=__get_msg())
    advance.insert_pipeline_history_on_canceled()
