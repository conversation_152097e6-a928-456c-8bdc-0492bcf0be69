from frame import env, common


class EnvMgr:
    @classmethod
    def set_latest_branch(cls, branch: str):
        env.set({"branch": branch})

    @classmethod
    def get_latest_branch(cls):
        return env.get("branch")

    @classmethod
    def get_build_branch(cls):
        return cls.get_latest_branch() or "master"


class JenkinsEnvMgr:
    def get_force_build(self):
        return common.str2bool(env.get("FORCE_BUILD"))

    def get_is_rebuild(self):
        return env.get("IS_REBUILD")


jenkins_env_mgr = JenkinsEnvMgr()
