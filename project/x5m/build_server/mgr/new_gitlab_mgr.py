# coding=utf-8
from typing import Tu<PERSON>
from frame import *
from project.x5m import config
from project.x5m.build_server.mgr.env_mgr import jenkins_env_mgr


class NewGitlabMgr:
    def __init__(self):
        self.x5mobile_gitlab_mgr = GitlabMgr(url=config.GITLAB_MAINTAINER["url"], token=config.GITLAB_MAINTAINER["token"], project="dgm/x5mobile")
        self.x5mconfig_gitlab_mgr = GitlabMgr(url=config.GITLAB_MAINTAINER["url"], token=config.GITLAB_MAINTAINER["token"], project="dgm/x5mconfig")
        self.x5mweek_gitlab_mgr = GitlabMgr(url=config.GITLAB_MAINTAINER["url"], token=config.GITLAB_MAINTAINER["token"], project="dgm/x5mweek")
        self.x5mobile_git_mgr = GitMgr(workdir=env.pipeline.workspace(), project_name="x5_mobile")
        # self.x5mobile_local_git_mgr = GitMgr(workdir="e:\\git_code", project_name="x5mobile")

    def get_max_pipeline_id(self, branch: str):
        """
        获取三个项目中最大的 max_pipeline_id
        """
        x5mobile_pipeline_id = self.x5mobile_gitlab_mgr.get_newest_pipeline_id(branch=branch)
        x5mconfig_pipeline_id = self.x5mconfig_gitlab_mgr.get_newest_pipeline_id(branch=branch)
        x5mweek_resources_pipeline_id = self.x5mweek_gitlab_mgr.get_newest_pipeline_id(branch="resources")
        x5mweek_release_pipeline_id = self.x5mweek_gitlab_mgr.get_newest_pipeline_id(branch="release")
        x5mweek_online_pipeline_id = self.x5mweek_gitlab_mgr.get_newest_pipeline_id(branch="online")
        max_pipeline_id = max(
            x5mobile_pipeline_id, x5mconfig_pipeline_id, x5mweek_online_pipeline_id, x5mweek_resources_pipeline_id, x5mweek_release_pipeline_id
        )
        log.info("x5mobile_pipeline_id: {}".format(x5mobile_pipeline_id))
        log.info("x5mconfig_pipeline_id: {}".format(x5mconfig_pipeline_id))
        log.info("x5mweek_resources_pipeline_id: {}".format(x5mweek_resources_pipeline_id))
        log.info("x5mweek_release_pipeline_id: {}".format(x5mweek_release_pipeline_id))
        log.info("x5mweek_online_pipeline_id: {}".format(x5mweek_online_pipeline_id))
        log.info("max_pipeline_id: {}".format(max_pipeline_id))
        env.set({"max_pipeline_id": max_pipeline_id})

    def get_commit_id(self, branch: str):
        """
        获取三个项目中的最近 commit_id
        """
        x5mobile_commit_id = self.x5mobile_gitlab_mgr.get_newest_commit_id(branch=branch)
        x5mconfig_commit_id = self.x5mconfig_gitlab_mgr.get_newest_commit_id(branch=branch)
        x5mweek_resources_commit_id = self.x5mweek_gitlab_mgr.get_newest_commit_id(branch="resources")
        x5mweek_release_commit_id = self.x5mweek_gitlab_mgr.get_newest_commit_id(branch="release")
        x5mweek_online_commit_id = self.x5mweek_gitlab_mgr.get_newest_commit_id(branch="online")
        env.set(
            {
                "x5mobile_commit_id": x5mobile_commit_id,
                "x5mconfig_commit_id": x5mconfig_commit_id,
                "x5mweek_resources_commit_id": x5mweek_resources_commit_id,
                "x5mweek_release_commit_id": x5mweek_release_commit_id,
                "x5mweek_online_commit_id": x5mweek_online_commit_id,
            }
        )
        log.info("x5mobile_commit_id: {}".format(x5mobile_commit_id))
        log.info("x5mconfig_commit_id: {}".format(x5mconfig_commit_id))
        log.info("x5mweek_resources_commit_id: {}".format(x5mweek_resources_commit_id))
        log.info("x5mweek_release_commit_id: {}".format(x5mweek_release_commit_id))
        log.info("x5mweek_online_commit_id: {}".format(x5mweek_online_commit_id))

    def clone_x5mobile(self, branch: str):
        if not self.x5mobile_git_mgr.exist():
            self.x5mobile_git_mgr.clone(
                url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git",
                branch=branch,
            )
        else:
            self.x5mobile_git_mgr.fetch(all=True)
            self.x5mobile_git_mgr.checkout(branch_or_tag=branch)
            if path_mgr.exists(os.path.join(self.x5mobile_git_mgr.project_dir(), ".gitattributes")):
                self.x5mobile_git_mgr.exec("git rm .gitattributes")
            self.x5mobile_git_mgr.exec(command="git status")
            self.x5mobile_git_mgr.reset(commit_id="HEAD")
            old_commit_id = self.x5mobile_git_mgr.get_local_latest_commit_id()
            self.x5mobile_git_mgr.clean(exclude="mobile_dancer/trunk/")
            self.x5mobile_git_mgr.advance_pull(branch=branch)
            lines = self.x5mobile_git_mgr.exec(f"git diff --name-only HEAD {old_commit_id}")
            for line in lines:
                if "server" in line:
                    log.info(f"server changed: {line}")
                    return
            if not jenkins_env_mgr.get_force_build():
                pipeline_mgr.stop_current_build()

    def get_all_branches(self):
        """
        获取所有分支
        """
        return self.x5mobile_gitlab_mgr.get_all_branches()

    def get_x5mobile_committer(self, path: str, line: int) -> Tuple[str, str]:
        info = self.x5mobile_git_mgr.blame(path=path, line=line)
        return info.committer_mail, info.committer_time


new_gitlab_mgr = NewGitlabMgr()
