from frame import *
from project.x5m.card_package.mgr.build_mgr import build_mgr, build_sprite_mgr, build_texture_mgr
from project.x5m.card_package.mgr.p4_mgr import CardP4Mgr, CardPackageP4Mgr
from project.x5m.card_package.mgr.git_mgr import gitlab_mgr as git_mgr
from project.x5m.card_package.mgr.msg_mgr import msg_mgr


# 检查是否需要打包
@advance.stage("检查是否需要打包")
def check_package(**kwargs):
    build_mgr.check_package()


# 同步P4资源
@advance.stage("同步P4资源")
def sync_resource(**kwargs):
    p4_mgr = CardP4Mgr()
    p4_mgr.sync_resource()


# 更新打包工程
@advance.stage("更新打包工程")
def update_project(**kwargs):
    git_mgr.update_project()


# 导出名片
@advance.stage("导出名片")
def export_card(**kwargs):
    build_mgr.export_card()


# 上传贴图
@advance.stage("上传贴图")
def upload_texture(**kwargs):
    p4_mgr = CardP4Mgr()
    p4_mgr.upload_texture()


# 上传图集
@advance.stage("上传图集")
def upload_sprite(**kwargs):
    git_mgr.upload_sprite()


# 上传xml配置
@advance.stage("上传xml配置")
def upload_xml(**kwargs):
    git_mgr.upload_config()


# 更新arttrunk
@advance.stage("更新arttrunk")
def update_arttrunk(**kwargs):
    git_mgr.update_project(only_arttrunk=True)


# 更新图集
@advance.stage("更新图集")
def update_sprite(**kwargs):
    build_sprite_mgr.update_sprite()


# 打包图集
@advance.stage("打包图集")
def package_sprite(**kwargs):
    build_sprite_mgr.package_sprite()


# 检查打包结果
@advance.stage("检查打包结果")
def check_sprite(**kwargs):
    build_sprite_mgr.check_sprite()


# 上传图集AB
@advance.stage("上传图集AB")
def upload_sprite_ab(**kwargs):
    p4_mgr = CardPackageP4Mgr()
    p4_mgr.upload_sprite()


# 更新贴图
@advance.stage("更新贴图")
def update_texture(**kwargs):
    build_texture_mgr.update_texture()


# 打包贴图
@advance.stage("打包贴图")
def package_texture(**kwargs):
    build_texture_mgr.package_texture()


# 检查打包结果
@advance.stage("检查打包结果")
def check_texture(**kwargs):
    build_texture_mgr.check_texture()


# 上传贴图AB
@advance.stage("上传贴图AB")
def upload_texture_ab(**kwargs):
    p4_mgr = CardPackageP4Mgr()
    p4_mgr.upload_texture()


WEBHOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f4454687-a463-4b06-bfb1-94d85987cb29"


def on_success(**kwargs):
    content = msg_mgr.get_card_msg()
    users = msg_mgr.get_users()
    wechat.send_unicast_post_success(user_list=users, content=content)
    wechat.send_multicast_post_success(webhook=WEBHOOK, content=content)
    build_mgr.record_changelist()


def on_failure(**kwargs):
    content = msg_mgr.get_card_msg()
    users = msg_mgr.get_users()
    wechat.send_unicast_post_failure(user_list=users, content=content)
    wechat.send_multicast_post_failure(webhook=WEBHOOK, content=content, mentioned_list=users, rescue=False)


def on_card_always(**kwargs):
    build_mgr.upload_export_log_and_report()


def on_package_always(**kwargs):
    build_sprite_mgr.upload_sprite_log_and_report()
    build_texture_mgr.upload_texture_log_and_report()
