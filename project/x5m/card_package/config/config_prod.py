from project.x5m import config


class Git:
    url = "https://x5mobile-gitlab.h3d.com.cn/dgm/{project_name}.git"
    username = config.GITLAB_MAINTAINER["username"]
    password = config.GITLAB_MAINTAINER["password"]


class P4:
    card_client = "card_package_{host_ip}_prod"
    card_src_p4 = "//美术资源/炫舞手游-ui/6-UI常规资源/名片夹切图资源"
    texture_src_p4 = "//x5_mobile/mr/art_release/art_src/texture/card_suit"
    host = config.P4_CONFIG_JENKINS["host"]
    username = config.P4_CONFIG_JENKINS["username"]
    password = config.P4_CONFIG_JENKINS["password"]
    card_package_client = "card_package_{host_ip}_{platform}_prod"
    sprite_ab_p4 = "//x5m/res/cdn/cooked/{platform}/assetbundles/art/uiprefabs/newui/playercard/prefabatlas"
    texuture_ab_p4 = "//x5m/res/cdn/cooked/{platform}/assetbundles/texture/card_suit"
