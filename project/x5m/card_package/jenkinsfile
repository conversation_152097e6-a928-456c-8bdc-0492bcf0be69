node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}
PYTHON = 'C:\\Python\\Python36\\python.exe'

pipeline{
    agent {
        node {
            label "card-package"
            customWorkspace "D:\\card_package"
        }
    }
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '', description: '输入开始打包的changelist')
         booleanParam(name: 'debug', defaultValue: false, description: '是否为测试模式')
    }
    //     因为五点到六点半p4需要备份数据，流水线会执行失败
    triggers {
        cron('H/10 0-4,8-23 * * *')
    }
    environment {
        GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage('美术资源获取'){
            stages{
                stage("更新流水线依赖") {
                    steps {
                        dir("${GIT_NAME}"){
                            script {
                                bat(script: "$PYTHON -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && $PYTHON -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q")
                            }
                        }
                    }
                }
                // 检查是否需要打包
                stage("检查是否需要打包") {
                    steps {
                        dir("${GIT_NAME}"){
                            script {
                                bat(script: "$PYTHON x5m.py card_packages --job=check_package")
                            }
                        }
                    }
                }
                // 获取P4资源
                stage("获取P4资源") {
                    steps {
                        dir("${GIT_NAME}"){
                            script {
                                bat(script: "$PYTHON x5m.py card_packages --job=sync_resource")
                            }
                        }
                    }
                }
                // 更新打包工程
                stage("更新打包工程") {
                    steps {
                        dir("${GIT_NAME}"){
                            script {
                                bat(script: "$PYTHON x5m.py card_packages --job=update_project")
                            }
                        }
                    }
                }
                // 导出名片
                stage("导出名片"){
                    steps{
                        dir("${GIT_NAME}"){
                            script {
                                bat(script: "$PYTHON x5m.py card_packages --job=export_card")
                            }
                        }
                    }
                }
                // 上传贴图
                stage("上传贴图"){
                    steps{
                        dir("${GIT_NAME}"){
                            script {
                                bat(script: "$PYTHON x5m.py card_packages --job=upload_texture")
                            }
                        }
                    }
                }
                // 上传图集
                stage("上传图集"){
                    steps{
                        dir("${GIT_NAME}"){
                            script {
                                bat(script: "$PYTHON x5m.py card_packages --job=upload_sprite")
                            }
                        }
                    }
                }
                // 上传xml配置
                stage("上传xml配置"){
                    steps{
                        dir("${GIT_NAME}"){
                            script {
                                bat(script: "$PYTHON x5m.py card_packages --job=upload_xml")
                            }
                        }
                    }
                }

            }
            post {
                always {
                    dir("${GIT_NAME}"){
                        script {
                            bat(script: "$PYTHON x5m.py card_packages --job=on_card_always")
                        }
                    }
                }
            }
        }
        stage('美术资源打包'){
            matrix {
                agent {
                    node {
                        label "card-package-${PLATFORM}"
                        customWorkspace "D:\\card_package\\${PLATFORM}"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'android','ios'
                    }
                }
                stages {
                    stage("更新打包工程") {
                        steps {
                            dir("${GIT_NAME}"){
                                script {
                                    bat(script: "$PYTHON x5m.py card_packages --job=update_arttrunk")
                                }
                            }
                        }
                    }
                    // 更新图集
                    stage("更新图集") {
                        steps {
                            dir("${GIT_NAME}"){
                                script {
                                    bat(script: "$PYTHON x5m.py card_packages --job=update_sprite")
                                }
                            }
                        }
                    }
                    // 图集打包
                    stage("图集打包") {
                        steps {
                            dir("${GIT_NAME}"){
                                script {
                                    bat(script: "$PYTHON x5m.py card_packages --job=package_sprite")
                                }
                            }
                        }
                    }
                    // 查看图集打包结果
                    stage("检查图集结果") {
                        steps {
                            dir("${GIT_NAME}"){
                                script {
                                    bat(script: "$PYTHON x5m.py card_packages --job=check_sprite")
                                }
                            }
                        }
                    }
                    // 上传图集ab
                    stage("上传图集ab") {
                        steps {
                            dir("${GIT_NAME}"){
                                script {
                                    bat(script: "$PYTHON x5m.py card_packages --job=upload_sprite_ab")
                                }
                            }
                        }
                    }
                    // 更新贴图
                    stage("更新贴图") {
                        steps {
                            dir("${GIT_NAME}"){
                                script {
                                    bat(script: "$PYTHON x5m.py card_packages --job=update_texture")
                                }
                            }
                        }
                    }
                    // 贴图打包
                    stage("贴图打包") {
                        steps {
                            dir("${GIT_NAME}"){
                                script {
                                    bat(script: "$PYTHON x5m.py card_packages --job=package_texture")
                                }
                            }
                        }
                    }
                    // 查看贴图打包结果
                    stage("检查贴图结果") {
                        steps {
                            dir("${GIT_NAME}"){
                                script {
                                    bat(script: "$PYTHON x5m.py card_packages --job=check_texture")
                                }
                            }
                        }
                    }
                    // 上传贴图ab
                    stage("上传贴图ab") {
                        steps {
                            dir("${GIT_NAME}"){
                                script {
                                    bat(script: "$PYTHON x5m.py card_packages --job=upload_texture_ab")
                                }
                            }
                        }
                    }
                }
                post {
                    always {
                        dir("${GIT_NAME}"){
                            script {
                                bat(script: "$PYTHON x5m.py card_packages --job=on_package_always")
                            }
                        }
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("${GIT_NAME}"){
                script {
                    bat(script: "$PYTHON x5m.py card_packages --job=on_unstable")
                }
            }
        }
        success {
            dir("${GIT_NAME}"){
                script {
                    bat(script: "$PYTHON x5m.py card_packages --job=on_success")
                }
            }
        }
        failure {
            dir("${GIT_NAME}"){
                script {
                    bat(script: "$PYTHON x5m.py card_packages --job=on_failure")
                }
            }
        }
    }
}
