import re
from frame import *
from project.x5m.card_package.mgr.env_mgr import jenkins_env_mgr, global_env_mgr, env_mgr
from project.x5m.card_package.config import config


class BuildMgr:
    def __init__(self) -> None:
        self.workspace = env.pipeline.workspace()
        self.debug = jenkins_env_mgr.get_debug()
        self.log_dir = os.path.join(self.workspace, "logs")
        if not path_mgr.exists(self.log_dir):
            path_mgr.mkdir(self.log_dir)
        self.export_log = os.path.join(self.log_dir, "export_card.log")
        self.project_dir = os.path.join(self.workspace, "arttrunk\\mobile_dancer\\arttrunk\\client")
        self.p4_config = config.P4
        self.card_dir = os.path.join(self.workspace, self.p4_config.card_src_p4.replace("//", "")).replace("/", "\\")
        self.export_report = os.path.join(self.project_dir, "tempFolder\\CardArtError.txt")
        self.cardsuit_abs = "Assets\\StaticResources\\art\\UIAtlas\\NewUI\\UIAtlasCardSuit"
        self.cardsuit_dir = os.path.join(self.project_dir, self.cardsuit_abs)
        self.texture_dir = os.path.join(self.workspace, "texture_src")

    # 检查是否需要打包
    def check_package(self) -> bool:
        first_changelist = jenkins_env_mgr.get_first_changelist()
        last_changelist = global_env_mgr.get_last_changelist()
        log.info(f"first_changelist: {first_changelist}, last_changelist: {last_changelist}")
        if not first_changelist or first_changelist == "0":
            if not last_changelist:
                raise PyframeException("流水线第一次运行, 请指定first_changelist")
            changelist = int(last_changelist) + 1
        else:
            changelist = first_changelist
        from project.x5m.card_package.mgr.p4_mgr import CardP4Mgr

        p4 = CardP4Mgr()
        changes = p4.get_changes(changelist)
        sorted_changes = sorted(changes, key=lambda x: int(x["change"]))
        log.info(f"changes: {sorted_changes}")
        if not sorted_changes:
            log.info("没有需要打包的资源")
            pipeline_mgr.stop_current_build()
        else:
            env_mgr.set_changes(sorted_changes)
            pattern = re.compile(r"^.*<(.*@h3d.com.cn)>$")
            files = []
            # 记录提交人信息
            users = []
            for change in changes:
                log.info(f"change: {change}")
                description = change["desc"]
                result = pattern.findall(description)
                if result:
                    user = result[0]
                    log.info(f"user: {user}")
                    if user not in users:
                        users.append(user)
                user = change.get("user")
                if user not in users:
                    if not user.endswith("h3d.com.cn"):
                        user = user + "@h3d.com.cn"
                    users.append(user)

                # 获取提交记录中的文件, 并过滤掉删除的文件以及非名片资源
                changelist = change.get("change")
                changed_file = p4.get_changed_files(changelist)
                for file, action in changed_file.items():
                    if file.startswith(p4.card_src_p4) and action != "delete":
                        if file not in files:
                            files.append(file)
                    else:
                        if file in files:
                            files.remove(file)
            env_mgr.set_users(users)
            if not files:
                log.info("no files changed, stop current build")
                pipeline_mgr.stop_current_build()
            else:
                env_mgr.set_files(files)

    # 解析导出结果
    def __parse_export_result(self) -> bool:
        if not path_mgr.exists(self.export_report):
            raise PyframeException(f"导出名片结果文件: {self.export_report} 不存在, 无法解析导出结果, 请联系工具组查看日志解决")
        log_text = Path(self.export_report).read_text(encoding="utf-8")
        log.info(f"导出名片结果: {log_text}")

        success_ids = re.findall(re.compile(r"success 成功处理: ([0-9]+)", re.S), log_text)
        if success_ids:
            success_ids = list(set(success_ids))
            env_mgr.set_success_ids(success_ids)
        error_ids = re.findall(re.compile(r"error ([0-9]+).*?", re.S), log_text)

        if error_ids:
            error_ids = list(set(error_ids))
            env_mgr.set_error_ids(error_ids)
            # TODO 临时跳过
            raise PyframeException(f"存在导出失败的名片: {error_ids}, {log_text}")

    # 导出名片
    def export_card(self):
        # 导出名片
        cmd_lines = f"unity -quit -batchmode -projectPath {self.project_dir} -executeMethod TreatCardArtRes.TreatmentForArtRes "
        cmd_lines += f"-logFile {self.export_log} -buildTarget android -paths {self.card_dir} "
        ret, output = cmd.run_shell(
            cmds=[cmd_lines],
            workdir=self.workspace,
        )
        if ret != 0:
            raise PyframeException(f"导出名片失败: {output}")
        log.info(f"导出名片命令行执行成功: {output}")

        # 解析导出结果
        self.__parse_export_result()

        # 记录名片输出目录
        env_mgr.set_card_dir(self.card_dir)

    # 上传导出日志
    def upload_export_log_and_report(self):
        if path_mgr.exists(self.export_log):
            export_log = advance.upload_pipeline_log(self.export_log)
            env_mgr.set_export_log(export_log)

        if path_mgr.exists(self.export_report):
            export_report = advance.upload_pipeline_log(self.export_report)
            env_mgr.set_export_report(export_report)

    def record_changelist(self):
        # 记录最后一次提交的changelist
        changeslist = env_mgr.get_changes()[-1].get("change")
        global_env_mgr.set_last_changelist(changeslist)


class BuildSpriteMgr(BuildMgr):
    def __init__(self) -> None:
        super().__init__()
        self.platform = jenkins_env_mgr.get_platform()
        self.sprite_log = os.path.join(self.log_dir, f"package_sprite_{self.platform}.log")
        self.sprite_ab = os.path.join(self.workspace, "sprite_ab")
        self.report_dir = os.path.join(self.project_dir, "AssetBundleTool\\AssetBundleToolLogs")
        self.sprite_report = ""

    # 清理上次构建
    def __clean_last_build(self):
        if path_mgr.exists(self.cardsuit_dir):
            path_mgr.rm(self.cardsuit_dir)
        path_mgr.mkdir(self.cardsuit_dir)

        if path_mgr.exists(self.sprite_ab):
            path_mgr.rm(self.sprite_ab)
        path_mgr.mkdir(self.sprite_ab)

    def update_sprite(self):
        # 如果目录存在 先清理再复制
        self.__clean_last_build()

        success_ids = env_mgr.get_success_ids()
        card_dir = env_mgr.get_card_dir()
        success_prefabs = []
        for success_id in success_ids:
            # 记录需要打包的prefab资源
            cardsuit_abs = self.cardsuit_abs.replace("\\", "/")
            success_prefab = cardsuit_abs + f"/PrefabAtlas_{success_id}.prefab"
            if success_prefab not in success_prefabs:
                success_prefabs.append(success_prefab)

            # 复制prefab相关资源
            src_dir = os.path.join(card_dir, success_id, success_id, "sprite")
            path_mgr.xcopy(src_dir, self.cardsuit_dir, dst_is_file=False)

        env_mgr.set_success_prefab(success_prefabs)

    # 将需要打包的prefab写入到mobile_dancer\trunk\client\AssetBundleTool\resources.txt
    def __write_resources_txt(self):
        success_prefabs = env_mgr.get_success_prefab()
        if not success_prefabs:
            raise PyframeException("没有需要打包的prefab资源")
        resources_txt = os.path.join(self.project_dir, "AssetBundleTool\\resources.txt")
        dir_path = os.path.dirname(resources_txt)
        # 如果目录不存在，则创建目录
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        success_prefabs = ",".join(success_prefabs)
        with open(resources_txt, "w") as f:
            f.write(f"{success_prefabs}")

    # 图集打包
    def package_sprite(self):
        # 写入resources.txt
        self.__write_resources_txt()

        # 图集打包
        cmd_lines = f"unity -quit -batchmode -projectPath {self.project_dir} -executeMethod H3DBuildTools.BuildArt "
        cmd_lines += f"-buildTarget {self.platform} -logFile {self.sprite_log} out_path={self.sprite_ab} "
        ret, output = cmd.run_shell(
            cmds=[cmd_lines],
            workdir=self.workspace,
        )
        if ret != 0:
            advance.raise_unity_log_exception(self.sprite_log)
            raise PyframeException(f"图集打包失败: {output}")

    def __calculate_result(self) -> dict:
        reports = path_mgr.glob(self.report_dir, "*.txt")
        if not reports:
            raise PyframeException(f"图集打包结果文件不存在, 无法解析导出结果, 请联系工具组查看日志解决")
        # 报告根据修改时间倒序排列
        reports.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        self.sprite_report = report = reports[0]
        report_detail = {}
        with open(report, "r", encoding="utf-8") as f:
            content = f.readlines()
            report_detail = self.__parse_export_result(content)
            log.debug(f"platform: {self.platform}, report_detail: {report_detail}")
        return report_detail

    # 计算打包结果
    def check_sprite(self):
        report_detail = self.__calculate_result()
        env_mgr.set_sprite_report_detail(self.platform, report_detail)
        # TODO 临时跳过
        if report_detail.get("fail"):
            raise PyframeException(f"存在导出失败的图集: {report_detail.get('fail')}, 请查看导出日志解决")

    # 分析打包结果
    def __parse_export_result(self, read_lines: list) -> dict:
        success_pattern = re.compile(r"^\[info]\s+\d+\s.*?(\d{9,}$)")
        fail_pattern = re.compile(r"^\[Error]\s+\d+\s.*?(\d{9,}$)")
        success_ids = []
        fail_ids = []
        report_detail = {}
        for read_line in read_lines:
            suc = re.findall(success_pattern, read_line)
            if suc:
                success_ids.extend(suc)

            fail = re.findall(fail_pattern, read_line)
            if fail:
                fail_ids.extend(fail)
        success_ids = list(set(success_ids))
        fail_ids = list(set(fail_ids))
        report_detail.update({"success": success_ids, "fail": fail_ids})
        return report_detail

    def upload_sprite_log_and_report(self):
        if path_mgr.exists(self.sprite_log):
            sprite_log = advance.upload_pipeline_log(self.sprite_log)
            env_mgr.set_sprite_log(self.platform, sprite_log)
        # TODO 临时跳过
        # if path_mgr.exists(self.sprite_report):
        #     sprite_report = advance.upload_pipeline_log(self.sprite_report)
        #     env_mgr.set_sprite_report(self.platform, sprite_report)


class BuildTextureMgr(BuildMgr):
    def __init__(self) -> None:
        super().__init__()
        self.platform = jenkins_env_mgr.get_platform()
        self.texture_log = os.path.join(self.log_dir, f"package_texture_{self.platform}.log")
        self.texture_ab = os.path.join(self.workspace, "texture_ab")
        self.report_dir = os.path.join(self.project_dir, "AssetBundleTool\\AssetBundleToolLogs")
        self.texture_dir = os.path.join(self.project_dir, "Assets\\ab_resources\\texture\\card_suit")
        self.texture_report = ""

    def __clean_last_build(self):
        if path_mgr.exists(self.texture_dir):
            path_mgr.rm(self.texture_dir)
        path_mgr.mkdir(self.texture_dir)

        if path_mgr.exists(self.texture_ab):
            path_mgr.rm(self.texture_ab)
        path_mgr.mkdir(self.texture_ab)

    def update_texture(self):
        # 如果目录存在 先清理再复制
        self.__clean_last_build()

        success_ids = env_mgr.get_success_ids()
        card_dir = env_mgr.get_card_dir()
        for success_id in success_ids:
            # 复制texture相关资源
            src_dir = os.path.join(card_dir, success_id, success_id, "texture")
            dst_dir = os.path.join(self.texture_dir, success_id)
            path_mgr.xcopy(src_dir, dst_dir, dst_is_file=False)

    def package_texture(self):
        # 图集打包
        cmd_lines = f"unity -quit -batchmode -projectPath {self.project_dir} -executeMethod H3DBuildTools.BuildAB "
        cmd_lines += f"-buildTarget {self.platform} -logFile {self.texture_log} outpath={self.texture_ab} "
        ret, output = cmd.run_shell(
            cmds=[cmd_lines],
            workdir=self.workspace,
        )
        if ret != 0:
            advance.raise_unity_log_exception(self.texture_log)
            raise PyframeException(f"图集打包失败: {output}")

    def check_texture(self):
        report_detail = self.__calculate_result()
        env_mgr.set_texture_report_detail(self.platform, report_detail)
        # TODO 临时跳过
        if report_detail.get("fail"):
            raise PyframeException(f"存在导出失败的贴图: {report_detail.get('fail')}, 请查看导出日志解决")

    def __calculate_result(self) -> dict:
        reports = path_mgr.glob(self.report_dir, "*.txt")
        if not reports:
            raise PyframeException(f"图集打包结果文件不存在, 无法解析导出结果, 请联系工具组查看日志解决")
        # 报告根据修改时间倒序排列
        reports.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        self.texture_report = report = reports[0]
        report_detail = {}
        with open(report, "r", encoding="utf-8") as f:
            content = f.readlines()
            report_detail = self.__parse_export_result(content)
            log.debug(f"platform: {self.platform}, report_detail: {report_detail}")
        return report_detail

    def __parse_export_result(self, read_lines: list) -> dict:
        success_pattern = re.compile(r"^\[info]\s+\d+\s.*?(\d{9,}).*")
        fail_pattern = re.compile(r"^\[Error]\s+\d+\s.*?(\d{9,}).*")
        success_ids = []
        fail_ids = []
        report_detail = {}
        for read_line in read_lines:
            suc = re.findall(success_pattern, read_line)
            if suc:
                success_ids.extend(suc)

            fail = re.findall(fail_pattern, read_line)
            if fail:
                fail_ids.extend(fail)
        success_ids = list(set(success_ids))
        fail_ids = list(set(fail_ids))
        report_detail.update({"success": success_ids, "fail": fail_ids})
        return report_detail

    def upload_texture_log_and_report(self):
        if path_mgr.exists(self.texture_log):
            texture_log = advance.upload_pipeline_log(self.texture_log)
            env_mgr.set_texture_log(self.platform, texture_log)

        # TODO 临时跳过
        # if path_mgr.exists(self.texture_report):
        #     texture_report = advance.upload_pipeline_log(self.texture_report)
        #     env_mgr.set_texture_report(self.platform, texture_report)


build_texture_mgr = BuildTextureMgr()
build_sprite_mgr = BuildSpriteMgr()
build_mgr = BuildMgr()
