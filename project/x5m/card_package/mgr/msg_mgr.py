from frame import *
from project.x5m.card_package.mgr.env_mgr import env_mgr, jenkins_env_mgr


class MsgMgr:
    def get_card_msg(self):
        msg = ""
        card_ids = []
        succcess_ids = ""
        error_ids = ""
        if env_mgr.get_success_ids():
            card_ids += env_mgr.get_success_ids()
            succcess_ids = ",".join(env_mgr.get_success_ids())
        if env_mgr.get_error_ids():
            card_ids += env_mgr.get_error_ids()
            error_ids = ",".join(env_mgr.get_error_ids())
        card_ids = ",".join(card_ids)
        export_log = env_mgr.get_export_log()
        export_log_basename = os.path.basename(export_log)
        export_report = env_mgr.get_export_report()
        export_report_basename = os.path.basename(export_report)
        debug_mode = jenkins_env_mgr.get_debug()
        if debug_mode:
            msg += "**执行环境:** 测试\n"
        else:
            msg += "**执行环境:** 正式\n"
        if card_ids:
            msg += f"**导出名片:** {card_ids}\n"
        if succcess_ids:
            msg += f"**导出成功:** {succcess_ids}\n"
        if error_ids:
            msg += f"**导出失败:** {error_ids}\n"
        if export_log:
            msg += f"**导出日志:** [{export_log_basename}]({export_log})\n"
        if export_report:
            msg += f"**导出报告:** [{export_report_basename}]({export_report})\n"

        msg += "*" * 50 + "\n"
        msg += "**Android Texture:**\n"
        texture_android_changelist = env_mgr.get_submit_texture_changelist("android")
        if texture_android_changelist:
            msg += f"**AB 资源:** {texture_android_changelist}\n"
        else:
            msg += "**AB 资源:** \n"
        texture_android_log = env_mgr.get_texture_log("android")
        if texture_android_log:
            msg += f"**打包日志:** [{os.path.basename(texture_android_log)}]({texture_android_log})\n"
        texture_android_report = env_mgr.get_texture_report("android")
        if texture_android_report:
            msg += f"**打包报告:** [{os.path.basename(texture_android_report)}]({texture_android_report})\n"

        msg += "**Android Sprite:**\n"
        sprite_android_changelist = env_mgr.get_submit_sprite_changelist("android")
        if sprite_android_changelist:
            msg += f"**AB 资源:** {sprite_android_changelist}\n"
        else:
            msg += "**AB 资源:** \n"
        sprite_android_log = env_mgr.get_sprite_log("android")
        if sprite_android_log:
            msg += f"**打包日志:** [{os.path.basename(sprite_android_log)}]({sprite_android_log})\n"
        sprite_android_report = env_mgr.get_sprite_report("android")
        if sprite_android_report:
            msg += f"**打包报告:** [{os.path.basename(sprite_android_report)}]({sprite_android_report})\n"

        msg += "**iOS Texture:**\n"
        texture_ios_changelist = env_mgr.get_submit_texture_changelist("ios")
        if texture_ios_changelist:
            msg += f"**AB 资源:** {texture_ios_changelist}\n"
        else:
            msg += "**AB 资源:** \n"
        texture_ios_log = env_mgr.get_texture_log("ios")
        if texture_ios_log:
            msg += f"**打包日志:** [{os.path.basename(texture_ios_log)}]({texture_ios_log})\n"
        texture_ios_report = env_mgr.get_texture_report("ios")
        if texture_ios_report:
            msg += f"**打包报告:** [{os.path.basename(texture_ios_report)}]({texture_ios_report})\n"

        msg += "**iOS Sprite:**\n"
        sprite_ios_changelist = env_mgr.get_submit_sprite_changelist("ios")
        if sprite_ios_changelist:
            msg += f"**AB 资源:** {sprite_ios_changelist}\n"
        else:
            msg += "**AB 资源:** \n"
        sprite_ios_log = env_mgr.get_sprite_log("ios")
        if sprite_ios_log:
            msg += f"**打包日志:** [{os.path.basename(sprite_ios_log)}]({sprite_ios_log})\n"
        sprite_ios_report = env_mgr.get_sprite_report("ios")
        if sprite_ios_report:
            msg += f"**打包报告:** [{os.path.basename(sprite_ios_report)}]({sprite_ios_report})\n"

        return msg

    def get_users(self):
        return env_mgr.get_users()


msg_mgr = MsgMgr()
