import re
from frame import *
from project.x5m.card_package.config import config
from project.x5m.card_package.mgr.env_mgr import jenkins_env_mgr, env_mgr


class CardP4Mgr:
    def __init__(self) -> None:
        self.workspace = env.pipeline.workspace()
        self.project_dir = os.path.join(self.workspace, "arttrunk\\mobile_dancer\\arttrunk\\client")
        self.cardsuit_dir = os.path.join(self.project_dir, "Assets\\StaticResources\\art\\UIAtlas\\NewUI\\UIAtlasCardSuit")
        self.p4_config = config.P4
        self.client = self.p4_config.card_client.format(host_ip=common.get_host_ip())
        self.card_src_p4 = self.p4_config.card_src_p4
        self.card_dir = os.path.join(self.workspace, self.card_src_p4.replace("//", ""))
        self.texture_src_p4 = self.p4_config.texture_src_p4
        self.texture_dir = os.path.join(self.workspace, self.texture_src_p4.replace("//", ""))
        self.views = [
            f"{self.card_src_p4}/... //{self.client}/{self.card_src_p4.replace('//', '')}/...",
            f"{self.texture_src_p4}/... //{self.client}/{self.texture_src_p4.replace('//', '')}/...",
        ]
        self.root = self.workspace
        self.p4 = P4Client(
            host=self.p4_config.host,
            username=self.p4_config.username,
            password=self.p4_config.password,
            client=self.client,
            charset=P4Client.Charset.CP936,
        )
        self.p4.set_view(views=self.views)
        self.p4.set_root(path=self.root)
        self.p4.set_options(allwrite=True, clobber=True, rmdir=True)
        self.p4.set_line_end("WIN")

    def get_changes(self, changlist: str) -> list:
        changes = self.p4.get_changes(f"{self.card_src_p4}/...@{changlist},now", max=1000)
        log.info(f"changes: {changes}")
        return changes

    def get_changed_files(self, changlist: str) -> dict:
        changed_files = self.p4.get_files_by_changelist(changlist, depotfile_only=False)
        depot_files = changed_files.get("depotFile", [])
        action = changed_files.get("action", [])
        changed_files = dict(zip(depot_files, action))
        return changed_files

    def get_views(self, changed_files: list) -> list:
        card_ids = []
        # //美术资源/炫舞手游-ui/6-UI常规资源/名片夹切图资源/926105006/zs_AchvGetIcon_bg.png
        pattern = re.compile(rf"{self.card_src_p4}/(\d{{9,}})/.*")
        for file in changed_files:
            result = pattern.findall(file)
            if result and result[0] not in card_ids:
                card_ids.append(result[0])
        views = []
        for card_id in card_ids:
            views.append(f"{self.card_src_p4}/{card_id}/... //{self.client}/{self.card_src_p4.replace('//', '')}/{card_id}/...")
        return views

    def sync_resource(self):
        files = env_mgr.get_files()
        log.info(f"files: {files}")
        # 下载资源前删除本地目录
        path_mgr.rm(self.card_dir)

        # 下载资源
        p4_views = self.get_views(files)
        self.p4.set_view(views=p4_views)
        self.p4.sync_all(force=True)
        if path_mgr.exists(self.card_dir):
            cmd.run_shell([f"attrib -r {self.card_dir}"])

    def upload_texture(self):
        self.p4.set_view(views=self.views)

        # 复制贴图到P4本地目录, 并提交
        depots_files = []
        success_ids = env_mgr.get_success_ids()
        for success_id in success_ids:
            src_dir = os.path.join(self.card_dir, success_id, success_id, "texture")
            dst_dir = os.path.join(self.texture_dir, success_id)
            path_mgr.xcopy(src_dir, dst_dir, dst_is_file=False)

            depots_files.extend(self.p4.reconcile(dst_dir, delete=True))

        users = env_mgr.get_users()
        desc = f"uploader:{''.join(users)}\n"
        desc += f"Identity:#Jenkins#{env.pipeline.function_name()}#{env.pipeline.build_num()}\n"
        desc += f"link:{env.pipeline.build_url()}"
        if depots_files:
            self.p4.submit(description=desc, revert_if_failed=True)
        else:
            log.info("没有需要提交的文件")


class CardPackageP4Mgr:
    def __init__(self) -> None:
        self.workspace = env.pipeline.workspace()
        self.platform = jenkins_env_mgr.get_platform()
        self.p4_config = config.P4
        self.client = self.p4_config.card_package_client.format(host_ip=common.get_host_ip(), platform=self.platform)
        self.sprite_ab_p4 = self.p4_config.sprite_ab_p4.format(platform=self.platform)
        self.sprite_ab_p4_local = os.path.join(self.workspace, self.sprite_ab_p4.replace("//", "")).replace("/", "\\")  # Sprite AB资源P4本地目录
        self.sprite_ab = os.path.join(
            self.workspace, "sprite_ab\\assets\\staticresources\\art\\uiatlas\\newui\\uiatlascardsuit"
        )  # 打包工具输出的Sprite AB资源目录
        self.texuture_ab_p4 = self.p4_config.texuture_ab_p4.format(platform=self.platform)  # Texture AB资源P4目录
        self.texuture_ab_p4_local = os.path.join(self.workspace, self.texuture_ab_p4.replace("//", "")).replace("/", "\\")  # Texture AB资源P4本地目录
        self.texuture_ab = os.path.join(self.workspace, "texture_ab\\texture\\card_suit")  # 打包工具输出的Texture AB资源目录
        self.views = [
            f"{self.sprite_ab_p4}/... //{self.client}/{self.sprite_ab_p4.replace('//', '')}/...",
            f"{self.texuture_ab_p4}/... //{self.client}/{self.texuture_ab_p4.replace('//', '')}/...",
        ]
        self.p4 = P4Client(
            host=self.p4_config.host,
            username=self.p4_config.username,
            password=self.p4_config.password,
            client=self.client,
            charset=P4Client.Charset.CP936,
        )
        self.root = self.workspace
        self.p4.set_view(views=self.views)
        self.p4.set_root(path=self.root)
        self.p4.set_options(allwrite=True, clobber=True, rmdir=True)
        self.p4.set_line_end("WIN")
        self.p4.sync_all()

    def __copy_sprite(self):
        log.info(f"__copy_sprite: {self.client}")
        success_ids = env_mgr.get_success_ids()
        for success_id in success_ids:
            src_dir = os.path.join(self.sprite_ab, f"prefabatlas_{success_id}")
            dst_dir = os.path.join(self.sprite_ab_p4_local, f"prefabatlas_{success_id}")
            path_mgr.xcopy(src_dir, dst_dir, dst_is_file=True)

    def upload_sprite(self):
        # 复制图集到P4本地目录, 并提交
        self.__copy_sprite()
        log.info(f"upload_sprite: {self.client}")
        depot_files = self.p4.reconcile(f"{self.sprite_ab_p4_local}\\...", delete=True)
        if depot_files:
            uploader = ",".join(env_mgr.get_users())
            submit_desc = f"uploader:{uploader}\n"
            submit_desc += f"Identity:#Jenkins#{env.pipeline.function_name()}#{env.pipeline.build_num()}\n"
            submit_desc += f"link:{env.pipeline.build_url()}"
            submit_ret = self.p4.submit(desc=submit_desc, revert_if_failed=True)
            log.info(f"submit_ret: {submit_ret}")
            if submit_ret:
                submit_changelist = submit_ret[0].get("change")
                log.info(f"submit_changelist: {submit_changelist}")
                env_mgr.set_submit_sprite_changelist(self.platform, submit_changelist)
        else:
            log.warning("没有需要提交的文件")

    def __copy_texture(self):
        log.info(f"__copy_texture: {self.client}")
        success_ids = env_mgr.get_success_ids()
        for success_id in success_ids:
            src_dir = os.path.join(self.texuture_ab, success_id)
            dst_dir = os.path.join(self.texuture_ab_p4_local, success_id)
            path_mgr.xcopy(src_dir, dst_dir, dst_is_file=False)

    def upload_texture(self):
        # 复制贴图到P4本地目录, 并提交
        self.__copy_texture()
        log.info(f"upload_texture: {self.client}")
        depot_files = self.p4.reconcile(f"{self.texuture_ab_p4_local}\\...", delete=True)
        if depot_files:
            uploader = ",".join(env_mgr.get_users())
            submit_desc = f"uploader:{uploader}\n"
            submit_desc += f"Identity:#Jenkins#{env.pipeline.function_name()}#{env.pipeline.build_num()}\n"
            submit_desc += f"link:{env.pipeline.build_url()}"
            submit_ret = self.p4.submit(desc=submit_desc, revert_if_failed=True)
            log.info(f"submit_ret: {submit_ret}")
            if submit_ret:
                submit_changelist = submit_ret[0].get("change")
                log.info(f"submit_changelist: {submit_changelist}")
                env_mgr.set_submit_texture_changelist(self.platform, submit_changelist)
        else:
            log.warning("没有需要提交的文件")
