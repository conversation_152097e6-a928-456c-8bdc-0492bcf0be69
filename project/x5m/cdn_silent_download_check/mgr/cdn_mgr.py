import csv

from frame import *
from project.x5m import config
from project.x5m.cdn_silent_download_check.mgr.cal_mgr import crc32

# from pathos.multiprocessing import ProcessingPool as Pool

WORKDIR = env.pipeline.workspace()
CHECK_PATH = os.path.join(WORKDIR, "check/")


class CdnMgr:
    def __init__(self, cdn_version: str):
        self.cdn = cdn_version
        self.cdn_version = "v" + cdn_version.split("v")[1] + "_v" + cdn_version.split("v")[2]
        self.cdn_name = "res_list_v" + cdn_version.split("v")[1] + "_v" + cdn_version.split("v")[2]
        self.big_edit = "v" + cdn_version.split("v")[1]
        self.cdn_index = "res_list_v" + cdn_version.split("v")[1] + "_v" + cdn_version.split("v")[2] + ".zip"

        self.ftp_dynamic_android = "/version_test/dynamic/android/"
        self.ftp_dynamic_ios = "/version_test/dynamic/ios/"
        self.ftp_cdn_android_index_zip = "/version_test/dynamic/android/" + self.cdn_index
        self.ftp_cdn_android_index_zip_md5 = self.ftp_cdn_android_index_zip + ".md5"
        self.ftp_cdn_ios_index_zip = "/version_test/dynamic/ios/" + self.cdn_index
        self.ftp_cdn_ios_index_zip_md5 = self.ftp_cdn_ios_index_zip + ".md5"

        self.local_cdn_android_index_zip = CHECK_PATH + "android/" + self.cdn_index
        self.local_cdn_android_index_zip_md5 = self.local_cdn_android_index_zip + ".md5"
        self.local_new_cdn_android_index_zip_md5 = CHECK_PATH + "android/" + "new_" + self.cdn_index + ".md5"

        self.local_cdn_ios_index_zip = CHECK_PATH + "ios/" + self.cdn_index
        self.local_cdn_ios_index_zip_md5 = self.local_cdn_ios_index_zip + ".md5"
        self.local_new_cdn_ios_index_zip_md5 = CHECK_PATH + "ios/" + "new_" + self.cdn_index + ".md5"

        self.local_android = CHECK_PATH + "android/"
        self.local_ios = CHECK_PATH + "ios/"
        self.ftp_mgr = FtpMgr(
            ip=config.FTP_CONFIG_150_30["ip"],
            port=config.FTP_CONFIG_150_30["port"],
            username=config.FTP_CONFIG_150_30["username"],
            password=config.FTP_CONFIG_150_30["password"],
        )

    def download_cdn_index(self):
        """
        下载cdn索引
        """
        if not self.__ftp_exist_cdn_version():
            log.warn(f"cdn_version: {self.cdn}不存在")
            raise PyframeException(f"cdn_version: {self.cdn}不存在")
        self.__download_cdn_index_and_md5()
        self.__compare_cdn_index_md5()
        self.__decompress_cdn_index()

    def __download_cdn_index_and_md5(self):
        """
        下载cdn_index和md5文件
        """
        try:
            self.ftp_mgr.download_file(src=self.ftp_cdn_android_index_zip, dst=self.local_cdn_android_index_zip)
            self.ftp_mgr.download_file(src=self.ftp_cdn_android_index_zip_md5, dst=self.local_cdn_android_index_zip_md5)
            self.ftp_mgr.download_file(src=self.ftp_cdn_ios_index_zip, dst=self.local_cdn_ios_index_zip)
            self.ftp_mgr.download_file(src=self.ftp_cdn_ios_index_zip_md5, dst=self.local_cdn_ios_index_zip_md5)
            log.info("下载索引和md5文件成功")
        except Exception as e:
            raise PyframeException(str(e))

    def __compare_cdn_index_md5(self) -> bool:
        """
        对比cdn索引文件和md5文件
        """
        cal_crc32_android = crc32.get_crc32(self.local_cdn_android_index_zip)
        with open(self.local_cdn_android_index_zip_md5, "r") as f:
            md5_crc32_android = f.read()

        cal_crc32_ios = crc32.get_crc32(self.local_cdn_ios_index_zip)
        with open(self.local_cdn_ios_index_zip_md5, "r") as f:
            md5_crc32_ios = f.read()

        if cal_crc32_android == int(md5_crc32_android) and cal_crc32_ios == int(md5_crc32_ios):
            return True
        else:
            log.info(
                f"CDN索引表crc32与计算不符：安卓索引表MD5的crc32值为：{md5_crc32_android},"
                f"安卓计算出来的crc32值为：{cal_crc32_android}, "
                f"ios索引表MD5的crc32值为：{md5_crc32_ios}, "
                f"ios计算出来的crc32值为：{cal_crc32_ios}"
            )
            return False

    def __ftp_exist_cdn_version(self) -> bool:
        """
        检查ftp上cdn版本是否存在
        """
        if (
            self.ftp_mgr.exists_file(path=self.ftp_cdn_android_index_zip)
            and self.ftp_mgr.exists_file(path=self.ftp_cdn_ios_index_zip)
            and self.ftp_mgr.exists_file(path=self.ftp_cdn_android_index_zip_md5)
            and self.ftp_mgr.exists_file(path=self.ftp_cdn_ios_index_zip_md5)
        ):
            log.info(f"ftp上cdn版本{self.cdn}存在")
            return True
        else:
            log.info(f"ftp上CDN版本{self.cdn}不存在")
            return False

    def __decompress_cdn_index(self):
        """
        解压cdn索引表
        """
        tar.decompress(src=self.local_cdn_android_index_zip, dst=os.path.join(self.local_android, self.cdn_name))
        tar.decompress(src=self.local_cdn_ios_index_zip, dst=os.path.join(self.local_ios, self.cdn_name))

    def load_index(self, path: str) -> list:
        cdn_path_list = []
        csv_path = path + "/" + self.cdn_index.replace(".zip", ".csv")
        log.info(f"csv文件:{csv_path}")
        csv_file = open(csv_path, "r")
        reader = csv.reader(csv_file)
        for item in reader:
            # 忽略第一行
            if reader.line_num == 1:
                continue
            cdn_path_list.append(item[0] + "/" + item[1])
        return cdn_path_list

    def __download_android(self, files: list):
        try:
            for file in files:
                self.ftp_mgr.download_file(src=self.ftp_dynamic_android + file, dst=self.local_android + file)
        except Exception as e:
            raise PyframeException(str(e))

    def __download_android_multiprocess(self, files: list):
        try:
            p = Pool(processes=8)
            for file in files:
                p.apply_async(self.ftp_mgr.download_file(src=self.ftp_dynamic_android + file, dst=self.local_android + file), (file,))
            p.close()
            p.join()
        except Exception as e:
            raise PyframeException(str(e))

    def __download_ios(self, files: list):
        try:
            for file in files:
                self.ftp_mgr.download_file(src=self.ftp_dynamic_ios + file, dst=self.local_ios + file)
        except Exception as e:
            raise PyframeException(str(e))

    def __download_ios_multiprocess(self, files: list):
        try:
            p = Pool(processes=8)
            for file in files:
                p.apply_async(self.ftp_mgr.download_file(src=self.ftp_dynamic_ios + file, dst=self.local_ios + file), (file,))
            p.close()
            p.join()
        except Exception as e:
            raise PyframeException(str(e))
