node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "check_p4_opened_files"
            customWorkspace "D:/check_p4_opened_files"
        }
    }
    options {
        disableConcurrentBuilds()
    }
    triggers {
        cron('30 13 */1 * *')
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python36 -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python36 -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        stage("检查锁定文件") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python36 x5m.py check_p4_opened_files --job=check_opened_files")
                    }
                }
            }
        }
        stage("上传检查结果") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python36 x5m.py check_p4_opened_files --job=upload_check_result")
                    }
                }
            }
        }
    }
    post {
        unstable{
            dir("pyframe-pipeline") {
                bat label: '',
                script: """
                python36 x5m.py check_p4_opened_files --job=on_canceled
                """
            }
        }
        success{
            dir("pyframe-pipeline") {
                bat label: '',
                script: """
                python36 x5m.py check_p4_opened_files --job=on_success
                """
            }
        }
        failure{
            dir("pyframe-pipeline") {
                bat label: '',
                script: """
                python36 x5m.py check_p4_opened_files --job=on_failure
                """
            }
        }
    }
}