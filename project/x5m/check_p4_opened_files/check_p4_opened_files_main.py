from frame import advance, wechat

from project.x5m.check_p4_opened_files.mgr.check_opend_file_mgr import CheckOpenedFile
from project.x5m.check_p4_opened_files.mgr.env_mgr import EnvMgr


@advance.stage(stage="检查锁定文件")
def check_opened_files():
    check_opened_files_mgr = CheckOpenedFile()
    check_opened_files_mgr.check_opened_files()


@advance.stage(stage="上传检查结果")
def upload_check_result():
    opened_files = EnvMgr.get_opened_files()
    for e in opened_files:
        upload_path = advance.upload_pipeline_log(e.get("saved_file"))
        e["upload_path"] = upload_path

    EnvMgr.set_opened_files(opened_files)


def on_success():
    message = ["**检查到以下被用户锁定的文件超过1天**\n"]
    opened_files = EnvMgr.get_opened_files()
    for e in opened_files:
        user = e.get("user")
        client = e.get("client")
        change = e.get("change")
        upload_path = e.get("upload_path")
        msg = "**检查到p4以下文件被用户锁定超过1天**\n"
        msg += f"**用户**: {user}\n"
        msg += f"**p4 workspace**: {client}\n"
        msg += f"**pending list**: {change}\n"
        msg += f"[点击查看文件信息]({upload_path})\n"
        message.append(msg)
        wechat.send_abnormal_unicast_msg(
            user_list=[f"{user}@h3d.com.cn"],
            content=msg,
        )
    advance.insert_pipeline_history_on_success()


def on_failure():
    wechat.send_unicast_post_failure()
    advance.insert_pipeline_history_on_failure()


def on_canceled():
    wechat.send_unicast_post_canceled()
    advance.insert_pipeline_history_on_canceled()


if __name__ == "__main__":
    check_opened_files()
    upload_check_result()
    # on_success()
