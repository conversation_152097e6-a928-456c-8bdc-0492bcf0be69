import time

from frame import path_mgr
from project.x5m.check_p4_opened_files.mgr.p4_mgr import P4Mgr
from project.x5m.check_p4_opened_files.mgr.redis_mgr import RedisMgr
from project.x5m.check_p4_opened_files.mgr.env_mgr import EnvMgr


class CheckOpenedFile:
    def __init__(self):
        self.redis_mgr = RedisMgr()
        self.p4_mgr = P4Mgr()
        self.now = int(time.time())
        if not path_mgr.exists(str(self.now)):
            path_mgr.mkdir(str(self.now))

        self.check_time = 86400
        self.expire_time = 2592000

    def get_opened_files(self) -> dict:
        result = {}
        opened_files = self.p4_mgr.check_opened_files()
        for file in opened_files:
            user = file.get("user")
            client = file.get("client")
            change = file.get("change")
            file_name = file.get("depotFile")

            clients = result.get(user)
            if not clients:
                result[user] = {
                    client: {
                        change: [self.now, file_name],
                    }
                }
                continue

            changes = clients.get(client)
            if not changes:
                result[user][client] = {change: [self.now, file_name]}
                continue

            file_names = changes.get(change)
            if not file_names:
                result[user][client][change] = [self.now, file_name]
                continue

            if file_name not in file_names:
                file_names += [file_name]
                result[user][client][change] = file_names
                continue
        return result

    def check_opened_files(self):
        opened_files = []
        for user, clients in self.get_opened_files().items():
            for client, changes in clients.items():
                for change, files in changes.items():
                    redis_data = self.redis_mgr.get_value(user, f"{client}|||{change}")
                    if redis_data:
                        redis_time = redis_data[0]
                        redis_files = redis_data[1:]

                        if files[1:] == redis_files:
                            if self.now - redis_time > self.check_time:
                                saved_file = f"{self.now}/{user}.txt"
                                with open(f"./{saved_file}", "a", encoding="gbk") as f:
                                    f.write("用户: " + user + "\n")
                                    f.write("p4 workspace: " + client + "\n")
                                    f.write("pending list: " + change + "\n")
                                    f.write("\n".join(files[1:]))
                                    f.write(f"\n{'*' * 100}\n")

                                opened_files.append(
                                    {
                                        "saved_file": saved_file,
                                        "user": user,
                                        "client": client,
                                        "change": change,
                                    }
                                )
                                continue

                            files[0] = redis_time

                    self.redis_mgr.set_value(user, f"{client}|||{change}", files, self.expire_time)

        EnvMgr.set_opened_files(opened_files)
