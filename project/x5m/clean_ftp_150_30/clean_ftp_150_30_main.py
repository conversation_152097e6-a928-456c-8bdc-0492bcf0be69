# coding=utf-8
from collections import defaultdict
import datetime as dt
import os.path
import re
import time
from datetime import timedelta

from frame import *
from project.x5m.clean_ftp_150_30.mgr.p4_x5mobile import P4X5mobile
from project.x5m import config


@advance.stage(stage="准备")
def prepare(**kwargs):
    advance.check_disk_size(threshold=5)

    paths = [
        "E:/will_delete_files/dynamic/android/v28",
        "E:/will_delete_files/dynamic/ios",
        "E:/will_delete_files/dynamic/android",
        "E:/will_delete_files/dynamic/ios/v28",
        "E:/will_delete_files/silent_res/ios",
        "E:/will_delete_files/silent_res/android",
        "E:/will_delete_files/dress",
        "E:/will_delete_files/server_resources",
        "E:/will_delete_files/update_config/source",
        "E:/will_delete_files/update_config/release",
        "E:/will_delete_files/update_config/online",
        "E:/will_delete_files/server_pack/release",
        "E:/will_delete_files/server_pack/online",
        "E:/will_delete_files/server_pack/resources",
        "E:/will_delete_files/server_pack/branch",
        "E:/will_delete_files/server_pack/trunk",
    ]
    for path in paths:
        path_mgr.mkdir(path=path)


def __get_local_files(path: str) -> list:
    """
    获取指定路径下的文件列表
    Args:
        path: 指定路径

    Returns:
        filelist:获取到的文件列表
    """
    filelist = os.listdir(path=path)
    filelist.sort()
    return filelist


def __get_p4_files(path: str) -> list:
    """
    获取p4上指定目录下的目录
    Args:
        path: p4指定路径
    Returns:

    """
    p4x5m = P4X5mobile()
    files = p4x5m.get_p4files(path=path)
    p4_files = __get_need_p4_files(path=path, files=files)
    return p4_files


def __get_need_p4_files(path: str, files: list):
    """
    从p4上获取到的文件属于路径形式：'//x5m/res/lite/android/4.10.115'
    这里需要把这些p4文件处理成为我们所需的文件名形式：  4.10.115
    Args:
        path: p4指定路径
        files: 获取到的原始的p4文件
    Returns:
        p4_files:处理后所需的p4文件

    """
    p4_files = []
    # lite和silent路径中目录结构相同，都存在android和ios
    if "/android/" in path:
        for s in files:
            n = s.rfind("id/")  # 找到"id/"出现的位置
            p4_files.append(s[n + 3 :])
        # p4_files.sort(key=lambda ss: [int(u) for u in s.split('.')])
        return p4_files
    elif "/ios/" in path:
        for s in files:
            n = s.rfind("os/")  # 找到"os/"出现的位置
            p4_files.append(s[n + 3 :])
        # p4_files.sort(key=lambda ss: [int(u) for u in s.split('.')])
        return p4_files
    # dynamic目录结构不存在android和ios
    elif "/pack/" in path:
        # num = 0
        for s in files:
            n = s.rfind("ck/")  # 找到"ck/"出现的位置
            p4_files.append(s[n + 3 :])
        # p4_files.sort(key=lambda ss: [int(u) for u in s.split('.')])
        return p4_files


# 逻辑过于复杂
def __clean_lite_silent(path, p4_files, local_versions, move_path: str):
    """
    进行删除lite和silent中满足要求的文件（lite和silent的清理规则）
    Args:
        path: 本地指定路径
        p4_files: p4上的版本目录
        local_versions: 本地的版本目录
        move_path: 要移动到的路径
    Returns:
    """
    # count = len(p4_files)
    # 获取最大的p4中的版本号
    for p4_file in p4_files:
        p4_versions = p4_file.split(".")
        max_p4_file = "0.0.0"
        max_p4_versions = max_p4_file.split(".")
        if p4_versions[0] > max_p4_versions[0]:
            max_p4_file = p4_file
        elif p4_versions[0] == max_p4_versions[0]:
            if p4_versions[1] > max_p4_versions[1]:
                max_p4_file = p4_file
            elif p4_versions[1] == max_p4_versions[1]:
                if p4_versions[2] > max_p4_versions[2]:
                    max_p4_file = p4_file
                else:
                    pass
    max_p4_version = max_p4_file.split(".")
    second_version = int(max_p4_version[0]) - 1
    sub_second_version = 0
    # 当出现临界时，例如5.01.* 根据规则应当保留5.01.*和4._.*，_处是4中最大的版本，此处计算应该4中最大的那个_处版本
    for local_version in local_versions:
        version = local_version.split(".")
        if str(version[0]) == str(second_version):
            if int(version[1]) > sub_second_version:
                sub_second_version = int(version[1])
            else:
                pass
    # 进行本地目录遍历判断是否删除
    for local_version in local_versions:
        version = local_version.split(".")
        # major_version, minor_version, micro_version = version[0], version[1], version[2]
        if version[0] > max_p4_version[0]:
            p4_files.append(local_version)
        else:
            if (int(max_p4_version[1]) - 1) >= 0:
                if int(version[1]) > int(max_p4_version[1]) - 1:
                    p4_files.append(local_version)
                else:
                    pass
            else:
                if version[0] == max_p4_version[0]:
                    p4_files.append(local_version)
                else:
                    if int(version[0]) == second_version:
                        if int(version[1]) == sub_second_version:
                            p4_files.append(local_version)
    # 进行遍历删除
    for local_version in local_versions:
        if local_version not in p4_files:
            files_path = os.path.join(path, local_version)
            log.info("delete {}".format(files_path))
            path_mgr.move(files_path, move_path)


def __clean_files(path: str, p4_path: str, move_path: str):
    """
    判断哪些文件属于删除文件进行删除
    Args:
        path: 本地指定路径
        p4_path: p4指定目录
        move_path: 要移动到的路径
    Returns:
    """
    local_versions = __get_local_files(path=path)
    p4_files = __get_p4_files(path=p4_path)
    __clean_lite_silent(path=path, p4_files=p4_files, local_versions=local_versions, move_path=move_path)


def __clean_dynamic_other(path, local_files, p4_folders, move_path: str):
    """
    清理dynamic中除v28文件夹之外的剩余文件的规则
    Args:
        path: 本地指定路径
        p4_folders: p4目录
        local_files: 本地的dynamic中的目录
        move_path: 要移动到的路径
    Returns:

    """
    # 获取p4 v28v1000中的1000
    reserve_versions = []
    for folder in p4_folders:
        if folder.startswith("v27"):
            continue
        version = folder.split("v")
        reserve_versions.append(int(version[-1]))
    reserve_versions.sort()
    # 添加本地待保留的版本
    max_sub_version = max(reserve_versions)

    pattern = re.compile(r"res_list_v28_v(\d+)\.zip")

    for file in local_files:
        version = re.findall(pattern, file)
        if version:
            version = int(version[0])
            if version > max_sub_version - 100:
                reserve_versions.append(version)
        # if file.endddswith(".zip"):
        #     version = int(file.strip("res_list_v28_v").strip(".zip"))
        #     if version > max_sub_version - 100:
        #         reserve_versions.append(version)
    # 除去目录中的‘v28’目录
    local_files.remove("v28")
    # 合理化文件名称
    res_list_v28_zip = ["res_list_v28_v{}.zip".format(i) for i in reserve_versions]
    res_list_v28_zip_md5 = ["res_list_v28_v{}.zip.md5".format(i) for i in reserve_versions]
    all_reserve_files = res_list_v28_zip + res_list_v28_zip_md5
    for folder in local_files:
        if folder not in all_reserve_files:
            files_path = os.path.join(path, folder)
            log.info("delete {}".format(files_path))
            path_mgr.move(files_path, move_path)


def __clean_files_dynamic(path: str, p4_path: str, move_path: str):
    """
    判断dynamic哪些文件属于删除文件进行删除
    Args:
        path: 本地指定路径
        p4_path: p4指定目录
        move_path: 要移动到的路径
    Returns:
    """
    local_files = __get_local_files(path=path)
    p4_folders = __get_p4_files(path=p4_path)
    __clean_dynamic_other(path=path, local_files=local_files, p4_folders=p4_folders, move_path=move_path)


def __clean_dress(path, local_files, p4_folders, move_path: str):
    """
    清理dress中文件的规则
    Args:
        path: 本地指定路径
        p4_folders: p4目录
        local_files: 本地的目录
        move_path: 要移动到的路径
    Returns:

    """
    # 获取p4 v28v1000中的1000
    reserve_versions = []
    for folder in p4_folders:
        if folder.startswith("v27"):
            continue
        version = folder.split("v")
        reserve_versions.append(int(version[-1]))
    reserve_versions.sort()
    # 添加本地待保留的版本
    max_sub_version = max(reserve_versions)
    for file in local_files:
        if file.endswith("@tmp"):
            # 去掉前四个v28v 和后四个@tmp
            version = int(file[4:-4])
            if version > max_sub_version - 100:
                reserve_versions.append(version)

    # 合理化文件名称
    v28v = ["v28v{}".format(i) for i in reserve_versions]
    v28v_tmp = ["v28v{}@tmp".format(i) for i in reserve_versions]
    all_reserve_files = v28v + v28v_tmp
    for folder in local_files:
        if folder not in all_reserve_files:
            files_path = os.path.join(path, folder)
            move_folder = os.path.join(move_path, folder)
            log.info("delete {}".format(files_path))
            if os.path.exists(path=move_folder):
                path_mgr.rm(path=move_folder)
            path_mgr.move(files_path, move_path)


def __clean_files_dress(path: str, p4_path: str, move_path: str):
    """
    判断dress哪些文件属于删除文件进行删除
    Args:
        path: 本地指定路径
        p4_path: p4指定目录
        move_path: 要移动到的路径
    Returns:
    """
    local_files = __get_local_files(path=path)
    p4_folders = __get_p4_files(path=p4_path)
    __clean_dress(path=path, local_files=local_files, p4_folders=p4_folders, move_path=move_path)


def __clean_dynamic_v28(path, local_v28_folders, p4_folders, move_path: str):
    """
    清理dynamic中的v28文件夹规则
    Args:
        path: 本地指定路径
        p4_folders: p4目录
        local_v28_folders: 本地的dynamic中的v28目录
        move_path: 要移动到的路径
    Returns:

    """
    # 获取p4 v28v1000中的1000
    reserve_versions = []
    for folder in p4_folders:
        if folder.startswith("v27"):
            continue
        version = folder.split("v")
        reserve_versions.append(int(version[-1]))
    reserve_versions.sort()
    # 添加本地待保留的版本
    max_sub_version = max(reserve_versions)
    for folder in local_v28_folders:
        version = int(folder.split("v")[-1])
        if version > max_sub_version - 100:
            reserve_versions.append(version)

    v_reserve_versions = ["v" + str(i) for i in reserve_versions]
    for folder in local_v28_folders:
        if folder not in v_reserve_versions:
            files_path = os.path.join(path, folder)
            log.info("delete {}".format(files_path))
            # 如果要移动的目标文件或者目录已经存在，先删除旧的
            base_name = os.path.basename(files_path)
            dst_file_or_dir = os.path.join(move_path, base_name)
            dst = Path(dst_file_or_dir)
            if dst.exists():
                path_mgr.rm(dst)
            # 再移动
            path_mgr.move(files_path, move_path)


def is_beyond_st(folder_or_file: str, days: int = 7) -> bool:
    """
    判断目录或者文件最后修改时间是否已经超过某个时间，默认30天
    """
    today = dt.date.today()
    beyond_date = today + timedelta(days=-days)
    p_obj = Path(folder_or_file)
    modified_time = dt.date.fromtimestamp(p_obj.stat().st_mtime)
    log.info(f"folder_or_file: {folder_or_file}: modified_time: {modified_time} beyond_date: {beyond_date}")
    if modified_time <= beyond_date:
        return True
    return False


def __clean_files_dynamic_v28(path: str, p4_path: str, move_path: str):
    """
    判断dynamic哪些文件属于删除文件进行删除
    Args:
        path: 本地指定路径
        p4_path: p4指定目录
        move_path: 要移动到的路径
    Returns:

    """
    local_v28_folders = __get_local_files(path=path)
    p4_folders = __get_p4_files(path=p4_path)
    __clean_dynamic_v28(path=path, local_v28_folders=local_v28_folders, p4_folders=p4_folders, move_path=move_path)


def __find_old_folder(root_dir: str) -> list:
    # 获取今天的日期
    today = dt.date.today()
    old_folders = []
    root_path = Path(root_dir)
    for folder_path in root_path.iterdir():
        if folder_path.is_dir():
            modified_time = dt.date.fromtimestamp(folder_path.stat().st_mtime)
            if modified_time < today:
                log.info(f"Old folder: {folder_path} (Last modified: {modified_time})")
                old_folders.append(folder_path)
    return old_folders


def __clean_hotfix_folder(platform: str):
    hotfix_package = rf"D:\cdn_hotfix_zip\workspace\ResourcePublish\3.12.0\SourceFiles\{platform}"
    old_folders = __find_old_folder(hotfix_package)
    for folder in old_folders:
        src = str(folder)
        basename = os.path.basename(src)
        dst = rf"F:\bak\cdn_hotfix_zip_bak\workspace\ResourcePublish\3.12.0\SourceFiles\{platform}"
        dst_name = os.path.join(dst, basename)
        if path_mgr.exists(dst_name):
            log.warn(f"Folder {dst_name} already exists, skip moving")
            continue
        log.info(f"delete {src}")
        path_mgr.move(src, dst)


def __get_current_branch() -> list:
    """
    获取当前分支信息
    """
    x5mobile_gitlab_mgr = GitlabMgr(config.GITLAB_MAINTAINER.get("url"), config.GITLAB_MAINTAINER.get("token"), "18")
    branches = x5mobile_gitlab_mgr.get_all_branches()
    pattern = re.compile(r"\d+\.\d+\.\d+")
    filtered_branches = []
    for branch in branches:
        if re.fullmatch(pattern, branch):
            filtered_branches.append(branch)
    filtered_branches.sort(reverse=True)
    filtered_branches = filtered_branches[:3]
    final_branches = []
    for filtered_branch in filtered_branches:
        temp = "".join(filtered_branch.split(".")[:2]) + "2"
        final_branches.append(temp)
    return final_branches


def __clean_server_resources(path: str, move_path: str):
    server_resource_zips = Path(path).glob("resources_*.zip")
    pattern = re.compile(r"resources_(\d+)\.\d+\.zip")
    branches_info = __get_current_branch()
    for server_resource_zip in server_resource_zips:
        ret = re.findall(pattern, str(server_resource_zip))
        if ret:
            branch_info = ret[0]
        else:
            log.warn(f"未能正确找到 {server_resource_zip} 的版本信息")
            continue
        if branch_info not in branches_info:
            log.info(f"branch_info: {branch_info} branches_info: {branches_info}")
            dst = os.path.join(move_path, os.path.basename(server_resource_zip))
            log.info("delete {}".format(server_resource_zip))
            path_mgr.move(server_resource_zip, dst)


def __clean_update_config(path: str, move_path: str, days: int = 7):
    server_resource_zips = Path(path).glob("resources_*.zip")
    for server_resource_zip in server_resource_zips:
        if is_beyond_st(str(server_resource_zip), days=days):
            dst = os.path.join(move_path, os.path.basename(server_resource_zip))
            log.info("delete {}".format(server_resource_zip))
            path_mgr.move(server_resource_zip, dst)


def __clean_server_pack(path: str, move_path: str, days: int = 7):
    server_pack_zips = Path(path).glob("dgm_*.zip")
    for server_pack_zip in server_pack_zips:
        if is_beyond_st(str(server_pack_zip), days=days):
            dst = os.path.join(move_path, os.path.basename(server_pack_zip))
            log.info("delete {}".format(server_pack_zip))
            path_mgr.move(server_pack_zip, dst)


@advance.stage(stage="清理lite")
def clean_lite(**kwargs):
    lite_android_p4 = "//x5m/res/lite/android/*"
    lite_android_local = "D:/file_services/version_test/lite_res/android"
    move_path = "E:/will_delete_files/lite_res/android"
    __clean_files(path=lite_android_local, p4_path=lite_android_p4, move_path=move_path)

    lite_ios_p4 = "//x5m/res/lite/ios/*"
    lite_ios_local = "D:/file_services/version_test/lite_res/ios"
    move_path = "E:/will_delete_files/lite_res/ios"
    __clean_files(path=lite_ios_local, p4_path=lite_ios_p4, move_path=move_path)


@advance.stage(stage="清理silent")
def clean_silent(**kwargs):
    silent_android_local = "D:/file_services/version_test/silent_res/android"
    silent_android_p4 = "//x5m/res/silent/android/*"
    move_path = "E:/will_delete_files/silent_res/android"
    __clean_files(path=silent_android_local, p4_path=silent_android_p4, move_path=move_path)

    silent_ios_local = "D:/file_services/version_test/silent_res/ios"
    silent_ios_p4 = "//x5m/res/silent/ios/*"
    move_path = "E:/will_delete_files/silent_res/ios"
    __clean_files(path=silent_ios_local, p4_path=silent_ios_p4, move_path=move_path)


@advance.stage(stage="清理dynamic")
def clean_dynamic(**kwargs):
    dynamic_android_local = "D:/file_services/version_test/dynamic/android/v28"
    dynamic_p4 = "//x5m/res/cdn/pack/*"
    move_path = "E:/will_delete_files/dynamic/android/v28"
    __clean_files_dynamic_v28(dynamic_android_local, dynamic_p4, move_path)

    dynamic_ios_local = "D:/file_services/version_test/dynamic/ios/v28"
    move_path = "E:/will_delete_files/dynamic/ios/v28"
    __clean_files_dynamic_v28(dynamic_ios_local, dynamic_p4, move_path)

    dynamic_android_local = "D:/file_services/version_test/dynamic/android"
    move_path = "E:/will_delete_files/dynamic/android"
    __clean_files_dynamic(dynamic_android_local, dynamic_p4, move_path)

    dynamic_android_local = "D:/file_services/version_test/dynamic/ios"
    move_path = "E:/will_delete_files/dynamic/ios"
    __clean_files_dynamic(dynamic_android_local, dynamic_p4, move_path)


@advance.stage(stage="清理dress")
def clean_dress(**kwargs):
    dress_android_local = "D:/cdn_hotfix_zip/workspace/cdn_hotfix_zip/x5_mobile/mr/art_release/pack/dress"
    dress_p4 = "//x5m/res/cdn/pack/*"
    move_path = "E:/will_delete_files/dress"
    __clean_files_dress(dress_android_local, dress_p4, move_path)


@advance.stage(stage="清理cdn热更流水线目录")
def clean_cdn_hotfix_zip(**kwargs):
    for platform in ["android", "ios"]:
        __clean_hotfix_folder(platform)


@advance.stage(stage="清理热更服务器包目录")
def clean_ftp_server_resources(**kwargs):
    """
    清理策略：当前分支和上一个分支的服务器包保留，其余可以清理
    """
    server_resources_local = "D:/file_services/version_test/server_resources"
    move_path = "E:/will_delete_files/server_resources"
    __clean_server_resources(server_resources_local, move_path)


@advance.stage(stage="清理update_config下的热更服务器包")
def clean_ftp_update_config(**kwargs):
    """
    清理策略：三个环境source、release、online只保留一周的内容
    """
    update_config_dct = {
        "source": 7,
        "release": 7,
        "online": 30,
    }
    for branch, days in update_config_dct.items():
        local = f"D:/file_services/version_test/update_config/{branch}"
        move_path = f"E:/will_delete_files/update_config/{branch}"
        __clean_update_config(local, move_path, days=days)
    # source_local = "D:/file_services/version_test/update_config/source"
    # move_path = "E:/will_delete_files/update_config/source"
    # __clean_update_config(source_local, move_path)
    # release_local = "D:/file_services/version_test/update_config/release"
    # move_path = "E:/will_delete_files/update_config/release"
    # __clean_update_config(release_local, move_path)
    # online_local = "D:/file_services/version_test/update_config/online"
    # move_path = "E:/will_delete_files/update_config/online"
    # __clean_update_config(online_local, move_path, days=30)


@advance.stage(stage="清理服务器二进制包")
def clean_ftp_server_pack(**kwargs):
    """
    清理策略：五个环境release(三个月)、online(三个月)、resources(一个月)、branch(一个月)、trunk(一个月)
    """
    dct = {
        "release": 90,
        "online": 90,
        "resources": 30,
        "branch": 30,
        "trunk": 30,
    }
    for branch, days in dct.items():
        local = f"D:/file_services/version_test/server_pack/{branch}"
        move_path = f"E:/will_delete_files/server_pack/{branch}"
        __clean_server_pack(local, move_path, days=days)
    # release_local = "D:/file_services/version_test/server_pack/release"
    # move_path = "E:/will_delete_files/server_pack/release"
    # __clean_server_pack(release_local, move_path, days=90)
    # online_local = "D:/file_services/version_test/server_pack/online"
    # move_path = "E:/will_delete_files/server_pack/online"
    # __clean_server_pack(online_local, move_path, days=90)
    # resources_local = "D:/file_services/version_test/server_pack/resources"
    # move_path = "E:/will_delete_files/server_pack/resources"
    # __clean_server_pack(resources_local, move_path, days=30)
    # branch_local = "D:/file_services/version_test/server_pack/branch"
    # move_path = "E:/will_delete_files/server_pack/branch"
    # __clean_server_pack(branch_local, move_path, days=30)
    # trunk_local = "D:/file_services/version_test/server_pack/trunk"
    # move_path = "E:/will_delete_files/server_pack/trunk"
    # __clean_server_pack(trunk_local, move_path, days=30)


def delete_f(left_space=500):
    # f盘中碎文件太多，改为按照文件夹时间删除
    files_time = defaultdict(list)

    def get_file_dict(base_dir: str) -> dict:
        for subdir in os.listdir(base_dir):
            subdir_path = os.path.join(base_dir, subdir)
            for folder in os.listdir(subdir_path):
                filepath = os.path.join(subdir_path, folder)
                time_str = os.path.getmtime(filepath)
                time_local = time.localtime(time_str)
                datetime = time.strftime("%Y-%m-%d", time_local)
                files_time[datetime].append(filepath)

    # get_file_dict("f:\\bak\\server_pack")
    get_file_dict("f:\\bak\\cdn_hotfix_zip_bak\\workspace\\ResourcePublish\\3.12.0\\SourceFiles")
    files_time = sorted(files_time.items(), key=lambda x: x[0])
    for datetime, file_list in files_time:
        if common.get_disk_free_size(disk_path="f:\\bak") >= left_space:
            break
        log.info(f"开始删除 {datetime} 对应的文件")
        for file in file_list:
            path_mgr.rm(file)


def delete_by_dir(base_dir: str, left_space: int = 500):
    log.info(f"开始清理{base_dir}目录, 保留磁盘空间：{left_space}G")
    files_time = defaultdict(list)

    for root, dirs, file_list in os.walk(base_dir):
        for dir in dirs:
            folder = os.path.join(root, dir)
            # 删除路径下的所有空文件夹
            if not os.listdir(folder):
                path_mgr.rm(folder)
        for file in file_list:
            filepath = os.path.join(root, file)
            time_str = os.path.getmtime(filepath)
            time_local = time.localtime(time_str)
            datetime = time.strftime("%Y-%m-%d", time_local)
            files_time[datetime].append(filepath)

    files_time = sorted(files_time.items(), key=lambda x: x[0])
    for datetime, file_list in files_time:
        if common.get_disk_free_size(disk_path=base_dir) >= left_space:
            break
        log.info(f"开始删除 {datetime} 对应的文件")
        for file in file_list:
            path_mgr.rm(file)


@advance.stage(stage="删除")
def delete(**kwargs):
    # e盘按照文件时间排序删除
    delete_by_dir("e:\\will_delete_files")
    # f盘中碎文件太多，改为按照文件夹时间删除
    delete_f()


def post_success(**kwargs: dict):
    wechat.send_unicast_post_success(user_list=["<EMAIL>"])
    advance.insert_pipeline_history_on_success()


def post_failure(**kwargs: dict):
    wechat.send_unicast_post_failure()
    advance.insert_pipeline_history_on_failure()


def post_canceled(**kwargs: dict):
    wechat.send_unicast_post_canceled()
    advance.insert_pipeline_history_on_canceled()
