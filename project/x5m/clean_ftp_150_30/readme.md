# 1. 需求分析

因为172.17.150.30机器磁盘空间出现危机，其中有大量的文件资源占用空间，这些文件中存在许多的无效文件或垃圾文件，所以需要对这些没有用的文件进行清理，释放磁盘空间资源，主要清理路径为：

```
D:\file_services\version_test\silent_res
D:\file_services\version_test\lite_res
D:\file_services\version_test\dynamic
```

p4路径：

```
//x5m/res/lite/android/*
//x5m/res/lite/ios/*
//x5m/res/silent/android/*
//x5m/res/silent/ios/*
//x5m/res/cdn/pack/
```

清理规则：从p4中获取对应的有效文件夹目录，结合上面文件夹中的目录进行对比，保留最新版本文件和p4文件目录中存在的文件，将其他文件进行删除。


# 2. 详细设计

## 2.1. lite
1. 先从p4端获取对应的文件版本目录

```
def __get_p4_files(path: str):
```

2. 获取本地对应路径下的目录列表

```
__get_local_files(path: str)
```

3.  结合p4中的目录进行对比并找出本地目录中比p4中最新版本更大的版本进行保留，删除其他目录

```
__clean_files(path: str, p4_path: str)
```

4. 调用入口函数进行执行

```
clean_lite(**kwargs)
```

## 2.2. silent

1. 先从p4端获取对应的文件版本目录

```
__get_p4_files(path: str)
```

2. 获取本地对应路径下的目录列表

```
__get_local_files(path: str)
```

3. 结合p4中的目录进行对比并找出本地目录中比p4中最新版本更大的版本进行保留，删除其他目录

```
__clean_files(path: str, p4_path: str)
```

4. 调用入口函数进行执行

```
clean_silent(**kwargs)
```

## 2.3. dynamic

1. 获取本地对应路径下的目录列表

```
__get_local_files(path: str)
```

2. 从p4端获取对应的文件版本目录

```
__get_p4_files(path: str)
```

3. 结合p4中的目录进行对比并找出本地目录中比p4中最新版本更大的版本进行保留，删除其他目录

```
__clean_files_dynamic
```

4. 调用入口函数进行执行

```
clean_dynamic(**kwargs)
```

max_sub_version = 最大版本号，-3代表保留最近三次版本号
