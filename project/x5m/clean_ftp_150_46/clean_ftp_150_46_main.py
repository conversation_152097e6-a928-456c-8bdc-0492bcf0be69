# coding=utf-8
from copy import deepcopy
import datetime as dt
import os.path
import re
from datetime import timedelta

from frame import *
from project.x5m.clean_ftp_150_30.mgr.p4_x5mobile import P4X5mobile


def __get_local_files(path: str) -> list:
    """
    获取指定路径下的文件列表
    Args:
        path: 指定路径

    Returns:
        filelist:获取到的文件列表
    """
    filelist = os.listdir(path=path)
    filelist.sort()
    filelist = [file for file in filelist if "v27" not in file]
    return filelist


def __get_p4_files(path: str) -> list:
    """
    获取p4上指定目录下的目录
    Args:
        path: p4指定路径
    Returns:

    """
    p4x5m = P4X5mobile()
    files = p4x5m.get_p4files(path=path)
    p4_files = __get_need_p4_files(path=path, files=files)
    return p4_files


def __get_need_p4_files(path: str, files: list):
    """
    从p4上获取到的文件属于路径形式：'//x5m/res/lite/android/4.10.115'
    这里需要把这些p4文件处理成为我们所需的文件名形式：  4.10.115
    Args:
        path: p4指定路径
        files: 获取到的原始的p4文件
    Returns:
        p4_files:处理后所需的p4文件

    """
    p4_files = []
    # lite和silent路径中目录结构相同，都存在android和ios
    if "/android/" in path:
        for s in files:
            n = s.rfind("id/")  # 找到"id/"出现的位置
            p4_files.append(s[n + 3 :])
        # p4_files.sort(key=lambda ss: [int(u) for u in s.split('.')])
        return p4_files
    elif "/ios/" in path:
        for s in files:
            n = s.rfind("os/")  # 找到"os/"出现的位置
            p4_files.append(s[n + 3 :])
        # p4_files.sort(key=lambda ss: [int(u) for u in s.split('.')])
        return p4_files
    # dynamic目录结构不存在android和ios
    elif "/pack/" in path:
        # num = 0
        for s in files:
            n = s.rfind("ck/")  # 找到"ck/"出现的位置
            p4_files.append(s[n + 3 :])
        # p4_files.sort(key=lambda ss: [int(u) for u in s.split('.')])
        return p4_files


def __clean_dynamic_other(path, local_files, p4_folders, move_path: str):
    """
    清理dynamic中除v28文件夹之外的剩余文件的规则
    Args:
        path: 本地指定路径
        p4_folders: p4目录
        local_files: 本地的dynamic中的目录
        move_path: 要移动到的路径
    Returns:

    """
    # 获取p4 v28v1000中的1000
    reserve_versions = []
    for folder in p4_folders:
        version = folder.split("v")
        reserve_versions.append(int(version[-1]))
    reserve_versions.sort()
    # 添加本地待保留的版本
    max_sub_version = max(reserve_versions)

    pattern = re.compile(r"res_list_v\d+_v(\d+)\.zip")

    for file in local_files:
        version = re.findall(pattern, file)
        if version:
            version = int(version[0])
            if version > max_sub_version - 100:
                reserve_versions.append(version)

    # 除去目录中的‘v28’目录
    local_files.remove("v28")
    # 合理化文件名称
    res_list_v28_zip = ["res_list_v28_v{}.zip".format(i) for i in reserve_versions]
    res_list_v28_zip_md5 = ["res_list_v28_v{}.zip.md5".format(i) for i in reserve_versions]
    all_reserve_files = res_list_v28_zip + res_list_v28_zip_md5
    for folder in local_files:
        if folder not in all_reserve_files:
            files_path = os.path.join(path, folder)
            # path_mgr.rm(files_path)
            log.info("delete {}".format(files_path))
            # shutil.move(files_path, move_path)


def __clean_files_dynamic(path: str, p4_path: str, move_path: str):
    """
    判断dynamic哪些文件属于删除文件进行删除
    Args:
        path: 本地指定路径
        p4_path: p4指定目录
        move_path: 要移动到的路径
    Returns:
    """
    local_files = __get_local_files(path=path)
    p4_folders = __get_p4_files(path=p4_path)
    __clean_dynamic_other(path=path, local_files=local_files, p4_folders=p4_folders, move_path=move_path)


def __clean_dynamic_v28(path, local_v28_folders, p4_folders, move_path: str):
    """
    清理dynamic中的v28文件夹规则
    Args:
        path: 本地指定路径
        p4_folders: p4目录
        local_v28_folders: 本地的dynamic中的v28目录
        move_path: 要移动到的路径
    Returns:

    """
    # 获取p4 v28v1000中的1000
    reserve_versions = []
    for folder in p4_folders:
        version = folder.split("v")
        reserve_versions.append(int(version[-1]))
    reserve_versions.sort()
    p4_reserve_versions = deepcopy(reserve_versions)
    p4_reserve_versions = ["v" + str(i) for i in p4_reserve_versions]
    # 添加本地待保留的版本
    max_sub_version = max(reserve_versions)
    for folder in local_v28_folders:
        version = int(folder.split("v")[-1])
        if version > max_sub_version - 100:
            reserve_versions.append(version)
    v_reserve_versions = ["v" + str(i) for i in reserve_versions]
    for folder in local_v28_folders:
        if folder not in v_reserve_versions:
            files_path = os.path.join(path, folder)
            path_mgr.rm(files_path)
            log.info("delete {}".format(files_path))
            # shutil.move(files_path, move_path)
        if folder not in p4_reserve_versions:
            # 判断v28下的文件夹是否超过7天
            files_path = os.path.join(path, folder)
            if is_beyond_st(files_path, 5):
                path_mgr.rm(files_path)
                log.info("delete {}".format(files_path))
                # shutil.move(files_path, move_path)


def is_beyond_st(folder_or_file: str, days: int = 7) -> bool:
    """
    判断目录或者文件最后修改时间是否已经超过某个时间，默认30天
    """
    today = dt.date.today()
    beyond_date = today + timedelta(days=-days)
    p_obj = Path(folder_or_file)
    modified_time = dt.date.fromtimestamp(p_obj.stat().st_mtime)
    log.info(f"folder_or_file: {folder_or_file}: modified_time: {modified_time} beyond_date: {beyond_date}")
    if modified_time <= beyond_date:
        return True
    return False


def __clean_files_dynamic_v28(path: str, p4_path: str, move_path: str):
    """
    判断dynamic哪些文件属于删除文件进行删除
    Args:
        path: 本地指定路径
        p4_path: p4指定目录
        move_path: 要移动到的路径
    Returns:

    """
    local_v28_folders = __get_local_files(path=path)
    p4_folders = __get_p4_files(path=p4_path)
    log.info(f"local_v28_folders: {local_v28_folders}")
    __clean_dynamic_v28(path=path, local_v28_folders=local_v28_folders, p4_folders=p4_folders, move_path=move_path)


@advance.stage(stage="清理dynamic")
def clean_dynamic(**kwargs):
    dynamic_android_local = "D:/file_services/version_test/dynamic/android/v28"
    dynamic_p4 = "//x5m/res/cdn_unity2022/pack/*"
    move_path = "E:/will_delete_files/dynamic/android/v28"
    __clean_files_dynamic_v28(dynamic_android_local, dynamic_p4, move_path)

    dynamic_ios_local = "D:/file_services/version_test/dynamic/ios/v28"
    move_path = "E:/will_delete_files/dynamic/ios/v28"
    __clean_files_dynamic_v28(dynamic_ios_local, dynamic_p4, move_path)

    # dynamic_android_local = "D:/file_services/version_test/dynamic/android"
    # move_path = "E:/will_delete_files/dynamic/android"
    # __clean_files_dynamic(dynamic_android_local, dynamic_p4, move_path)

    # dynamic_android_local = "D:/file_services/version_test/dynamic/ios"
    # move_path = "E:/will_delete_files/dynamic/ios"
    # __clean_files_dynamic(dynamic_android_local, dynamic_p4, move_path)


def post_success(**kwargs: dict):
    wechat.send_unicast_post_success()
    advance.insert_pipeline_history_on_success()


def post_failure(**kwargs: dict):
    wechat.send_unicast_post_failure()
    advance.insert_pipeline_history_on_failure()


def post_canceled(**kwargs: dict):
    wechat.send_unicast_post_canceled()
    advance.insert_pipeline_history_on_canceled()
