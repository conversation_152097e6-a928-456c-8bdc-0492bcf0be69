# coding=utf-8

from frame import *
from project.x5m import config


class P4X5mobile:
    def __init__(self):
        self.__client = "clean_ftp_".format(common.get_host_ip())
        self.p4 = P4Client(
            host=config.P4_CONFIG_JENKINS.get("host"),
            username=config.P4_CONFIG_JENKINS.get("username"),
            password=config.P4_CONFIG_JENKINS.get("password"),
            client=self.__client,
        )

        views = [
            "//x5m/...        //{}/x5m/...".format(self.__client),
        ]
        self.p4.set_view(views=views)

    def get_p4files(self, path: str):
        files = self.p4.dirs(path)
        return files
