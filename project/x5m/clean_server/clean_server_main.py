# coding=utf8
import os
import shutil
import time
from pathlib import Path

from frame import log, wechat, advance

# core文件的路径
CORE_PATH = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe/bin/"
# dgm_server*.zip文件的路径
DGM_SERVER_ZIP_PATH = "/data/depot"
# log_back的路径
LOG_BACK_PATH = "/data/depot/log_back"
# log的路径
LOG_PATH = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe/logs/"


@advance.stage(stage="清理core")
def clean_core(**kwargs: dict):
    """
    清理过期的core，默认保留七天

    Args:
        **kwargs:
            days: 保留的天数
    """
    days = int(kwargs.get("days", 7))
    days = 3
    p = Path(CORE_PATH)
    if not p.exists():
        log.warn("{}不存在".format(p.absolute()))
        return
    cores = p.glob("core.*")
    current_time = time.time()
    for c in cores:
        if current_time - c.stat().st_ctime > 60 * 60 * 24 * days:
            log.info("remove {}, create time: {}".format(c.name, time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(c.stat().st_ctime))))
            c.unlink()
    log.info("清理core结束")


@advance.stage(stage="清理dgm_server*.zip")
def clean_dgm_server(**kwargs: dict):
    """
    清理过期dgm_server*.zip，默认保留7天，最少保留3个

    Args:
        **kwargs:
            days: 保留的天数
    """
    days = int(kwargs.get("days", 7))
    p = Path(DGM_SERVER_ZIP_PATH)
    if not p.exists():
        log.warn("{}不存在".format(p.absolute()))
        return
    zips = [z for z in p.glob("dgm_server_*.zip")]
    zips = sorted(zips, key=lambda x: x.name)
    log.info("{}".format(zips))
    current_time = time.time()
    count = 0
    for z in zips:
        if len(zips) - count <= 3:
            log.info("保留3个")
            break
        if current_time - z.stat().st_ctime > 60 * 60 * 24 * days:
            log.info("remove {}, create time: {}".format(z.name, time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(z.stat().st_ctime))))
            z.unlink()
            count += 1
    log.info("清理dgm_server结束")


@advance.stage(stage="清理log_back")
def clean_log_back(**kwargs: dict):
    """
    清理日志备份，默认保留7天，最少保留3个
    Args:
        **kwargs:
            days: 保留的天数
    """
    days = int(kwargs.get("days", 7))
    p = Path(LOG_BACK_PATH)
    if not p.exists():
        log.warn("{}不存在".format(p.absolute()))
        return
    logs = [z for z in p.glob("*")]
    logs = sorted(logs, key=lambda x: x.name)
    log.info("{}".format(logs))
    current_time = time.time()
    count = 0
    for z in logs:
        if len(logs) - count <= 3:
            log.info("保留3个")
            break
        if current_time - z.stat().st_ctime > 60 * 60 * 24 * days:
            log.info("remove {}, create time: {}".format(z.absolute(), time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(z.stat().st_ctime))))
            if z.is_dir():
                shutil.rmtree(z.absolute())
            elif z.is_file():
                os.remove(z.absolute())
            count += 1
    log.info("清理log_back结束")


@advance.stage(stage="清理log")
def clean_log(**kwargs: dict):
    """
    清理日志，默认保留7天
    Args:
        **kwargs:
            days: 保留的天数
    """
    days = int(kwargs.get("days", 7))
    p = Path(LOG_PATH)
    if not p.exists():
        log.warn("{}不存在".format(p.absolute()))
        return
    logs = p.glob("*.log")
    current_time = time.time()
    for g in logs:
        if current_time - g.stat().st_mtime > 60 * 60 * 24 * days:
            log.info("remove {}, last modify time: {}".format(g.name, time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(g.stat().st_mtime))))
            g.unlink()
    log.info("清理log结束")


def post_success(**kwargs: dict):
    wechat.send_unicast_post_success()
    advance.insert_pipeline_history_on_success()


def post_failure(**kwargs: dict):
    wechat.send_unicast_post_failure()
    advance.insert_pipeline_history_on_failure()


def post_canceled(**kwargs: dict):
    wechat.send_unicast_post_canceled()
    advance.insert_pipeline_history_on_canceled()
