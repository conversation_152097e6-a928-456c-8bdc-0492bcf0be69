# coding=utf8
import os
import shutil
import time
from pathlib import Path

from frame import log, wechat, advance

# core文件的路径
CORE_PATH = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe/bin/"
# dgm_server*.zip文件的路径
DGM_SERVER_ZIP_PATH = "/data/depot"
# log_back的路径
LOG_BACK_PATH = "/data/depot/log_back"
# log的路径
LOG_PATH = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe/logs/"


@advance.stage(stage="清理core")
def clean_core(**kwargs: dict):
    """
    清理过期的core，默认保留七天

    Args:
        **kwargs:
            days: 保留的天数
    """
    days = int(kwargs.get("days", 7))
    days = 3
    p = Path(CORE_PATH)
    if not p.exists():
        log.warn("{}不存在".format(p.absolute()))
        return
    cores = p.glob("core.*")
    current_time = time.time()
    for c in cores:
        if current_time - c.stat().st_ctime > 60 * 60 * 24 * days:
            log.info("remove {}, create time: {}".format(c.name, time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(c.stat().st_ctime))))
            c.unlink()
    log.info("清理core结束")


@advance.stage(stage="清理dgm_server*.zip")
def clean_dgm_server(**kwargs: dict):
    """
    清理过期dgm_server*.zip，默认保留7天，最少保留3个

    Args:
        **kwargs:
            days: 保留的天数
    """
    days = int(kwargs.get("days", 7))
    p = Path(DGM_SERVER_ZIP_PATH)
    if not p.exists():
        log.warn("{}不存在".format(p.absolute()))
        return
    zips = [z for z in p.glob("dgm_server_*.zip")]
    zips = sorted(zips, key=lambda x: x.name)
    log.info("{}".format(zips))
    current_time = time.time()
    count = 0
    for z in zips:
        if len(zips) - count <= 3:
            log.info("保留3个")
            break
        if current_time - z.stat().st_ctime > 60 * 60 * 24 * days:
            log.info("remove {}, create time: {}".format(z.name, time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(z.stat().st_ctime))))
            z.unlink()
            count += 1
    log.info("清理dgm_server结束")


@advance.stage(stage="清理log_back")
def clean_log_back(**kwargs: dict):
    """
    清理日志备份，默认保留7天，最少保留3个
    Args:
        **kwargs:
            days: 保留的天数
    """
    days = int(kwargs.get("days", 7))
    p = Path(LOG_BACK_PATH)
    if not p.exists():
        log.warn("{}不存在".format(p.absolute()))
        return
    logs = [z for z in p.glob("*")]
    logs = sorted(logs, key=lambda x: x.name)
    log.info("{}".format(logs))
    current_time = time.time()
    count = 0
    for z in logs:
        if len(logs) - count <= 3:
            log.info("保留3个")
            break
        if current_time - z.stat().st_ctime > 60 * 60 * 24 * days:
            log.info("remove {}, create time: {}".format(z.absolute(), time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(z.stat().st_ctime))))
            if z.is_dir():
                shutil.rmtree(z.absolute())
            elif z.is_file():
                os.remove(z.absolute())
            count += 1
    log.info("清理log_back结束")


@advance.stage(stage="清理log")
def clean_log(**kwargs: dict):
    """
    清理日志，默认保留7天
    Args:
        **kwargs:
            days: 保留的天数
    """
    days = int(kwargs.get("days", 7))
    p = Path(LOG_PATH)
    if not p.exists():
        log.warn("{}不存在".format(p.absolute()))
        return
    logs = p.glob("*.log")
    current_time = time.time()
    for g in logs:
        if current_time - g.stat().st_mtime > 60 * 60 * 24 * days:
            log.info("remove {}, last modify time: {}".format(g.name, time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(g.stat().st_mtime))))
            g.unlink()
    log.info("清理log结束")


def post_success(**kwargs: dict):
    wechat.send_unicast_post_success()
    advance.insert_pipeline_history_on_success()


def post_failure(**kwargs: dict):
    wechat.send_unicast_post_failure()
    advance.insert_pipeline_history_on_failure()


def post_canceled(**kwargs: dict):
    wechat.send_unicast_post_canceled()
    advance.insert_pipeline_history_on_canceled()


@advance.stage(stage="按照保留数量和修改时间清理文件")
def cleanup_dgm_server_folders(**kwargs: dict) -> bool:
    """清理指定的文件夹, 数量优先于时间

    Args:
        **kwargs:
            folder_path: 需要清理的文件夹路径
            count: 保留的文件数量, 默认为3个
            days: 保留的天数, 默认为7天
            is_force: 是否强制执行，默认不强制执行，如果开启强制执行，则会仅保留满足数量的文件夹
    """

    folder_path = kwargs.get("folder_path", "")
    keep_days = int(kwargs.get("days") or 7)
    keep_count = int(kwargs.get("count") or 3)
    # 兼容模式
    is_force = kwargs.get("is_force") == "true" or kwargs.get("is_force") == "True" or kwargs.get("is_force")
    # 将字符串路径转换为 Path 对象
    if not folder_path:
        log.error("the folder_path is empty")
        return False

    pf = Path(folder_path)
    # 确保文件夹存在
    if not pf.exists() or not pf.is_dir():
        log.error("the path %s does not exist or is not a directory", folder_path)
        return False

    sub_folders = sorted([item for item in pf.iterdir() if item.is_dir()], key=lambda x: x.stat().st_ctime)
    # 如果文件夹的数量小于需要保留的数量，则直接返回
    if len(sub_folders) <= keep_count:
        log.info("the number of keep folders is less than the keep count, skip")
        return True

    # 获取需要保留的文件夹
    keep_folders = sub_folders[-keep_count:]
    log.info("keep folders: %s", keep_folders)

    now_time = time.time()
    # 删除超出保留时间的文件夹
    for folder in sub_folders[:-keep_count]:
        # 计算文件夹创建时间与当前时间的差值，当开启强制删除时，仅按照保留数量清理
        if (not is_force) and (now_time - folder.stat().st_ctime < 60 * 60 * 24 * keep_days):
            log.info("folder %s is not out of date, skip", folder)
            continue
        # 递归删除文件夹，包含文件夹下的所有内容
        log.info("deleting folder %s", folder)
        shutil.rmtree(folder)

    log.info("cleanup server folders end")
    return True
