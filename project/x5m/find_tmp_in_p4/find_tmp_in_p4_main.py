# coding=utf-8
from frame import *
from project.x5m import config


def find_tmp(**kwargs):
    """
    查找临时文件
    """

    def __find_tmp_files(path: str):
        if path.endswith("..."):
            path = path.replace("...", "*")
        try:
            files = p4.files(path=path)
            tmp_files = []
            for file in files:
                if file.endswith(".tmp"):
                    tmp_files.append(file)
            if len(tmp_files) > 0:
                f = open("p4_tmp.txt", "a")
                for tmp_file in tmp_files:
                    f.write(tmp_file + "\n")
                f.close()
        except Exception as e:
            log.error(e)
        dirs = p4.dirs(path=path)
        for dir in dirs:
            __find_tmp_files(path=dir + "/...")

    p4 = P4Client(
        host=config.P4_CONFIG_JENKINS.get("host"),
        username=config.P4_CONFIG_JENKINS.get("username"),
        password=config.P4_CONFIG_JENKINS.get("password"),
        client="x5m-find-tmp-in-p4",
    )
    p4.set_view(views=["//x5_mobile/... //x5m-find-tmp-in-p4/x5_mobile/..."])
    p4.set_root("D:\\tmp")
    p4.set_options(allwrite=True)
    files = p4.files(path="//x5_mobile/.../*.tmp")
    if len(files) != 0:
        f = open("p4_tmp_x5_mobile.txt", "a")
        for file in files:
            f.write(file + "\n")
        f.close()
    files = p4.files(path="//x5m/.../*.tmp")
    if len(files) != 0:
        f = open("p4_tmp_x5m.txt", "a")
        for file in files:
            f.write(file + "\n")
        f.close()
