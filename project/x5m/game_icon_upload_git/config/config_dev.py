class ArtHub:
    domain = "http://arthub-api.h3d.com.cn"
    token = "154071"
    depot = "trial"
    root_node_id = 1332145


class Git:
    project_name = "x5mobile"
    url = "https://x5mobile-gitlab.h3d.com.cn/niushuaibing/x5mobile.git"
    token = "********************"
    android_icon = {
        "x5_icon_256.png": [
            "mobile_dancer/trunk/client/Assets/Plugins/Android/qqx5/res/drawable-hdpi-v4/x5_icon_256.png",
            "mobile_dancer/trunk/client/Assets/StaticResources/Logo/x5_icon_android.png",
            "mobile_dancer/trunk/client/experience/formal_root/Assets/StaticResources/Logo/x5_icon_android.png",
            "mobile_dancer/trunk/client/experience/formal_root/Assets/Plugins/Android/qqx5/res/drawable-hdpi-v4/x5_icon_256.png",
        ],
    }
    ios_icon = {
        "x5_icon_76.png": ["mobile_dancer/trunk/client/Assets/StaticResources/Logo/x5_icon_76.png"],
        "x5_icon_120.png": ["mobile_dancer/trunk/client/Assets/StaticResources/Logo/x5_icon_120.png"],
        "x5_icon_152.png": ["mobile_dancer/trunk/client/Assets/StaticResources/Logo/x5_icon_152.png"],
        "x5_icon_167.png": ["mobile_dancer/trunk/client/Assets/StaticResources/Logo/x5_icon_167.png"],
        "x5_icon_180.png": ["mobile_dancer/trunk/client/Assets/StaticResources/Logo/x5_icon_180.png"],
        "x5_icon_256.png": ["mobile_dancer/trunk/client/Assets/StaticResources/Logo/x5_icon.png"],
    }


class Gitlab:
    url = "https://x5mobile-gitlab.h3d.com.cn"
    token = "********************"
    repo = "niushuaibing/x5mobile"


class Notification:
    user_list = []
    bot = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1d143382-fafd-40fd-8599-262860acbb36"


class P4:
    port = "x5_mobile.p4.com:1666"
    username = "dgm_jenkins"
    password = "x5m12345"
    client = "game_icon_upload_git_{host_ip}_dev"
    need_upload_dir = "//美术资源/炫舞手游-ui/游戏图标"  # TODO 测试使用
