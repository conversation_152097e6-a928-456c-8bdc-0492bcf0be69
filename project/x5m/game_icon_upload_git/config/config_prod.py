class ArtHub:
    domain = "http://arthub-api.h3d.com.cn"
    token = "4b477"
    depot = "x5mobile"
    root_node_id = 188626


class Git:
    project_name = "x5mobile"
    url = "https://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git"
    token = "********************"
    android_icon = {
        "x5_icon_256.png": [
            "mobile_dancer/trunk/client_region/simp/Assets/Plugins/Android/qqx5/res/drawable-hdpi-v4/x5_icon_256.png",
            "mobile_dancer/trunk/client_region/simp/Assets/StaticResources/Logo/x5_icon_android.png",
            "mobile_dancer/trunk/client_region/simp/experience/formal_root/Assets/StaticResources/Logo/x5_icon_android.png",
            "mobile_dancer/trunk/client_region/simp/experience/formal_root/Assets/Plugins/Android/qqx5/res/drawable-hdpi-v4/x5_icon_256.png",
        ],
    }
    ios_icon = {
        "x5_icon_76.png": ["mobile_dancer/trunk/client_region/simp/Assets/StaticResources/Logo/x5_icon_76.png"],
        "x5_icon_120.png": ["mobile_dancer/trunk/client_region/simp/Assets/StaticResources/Logo/x5_icon_120.png"],
        "x5_icon_152.png": ["mobile_dancer/trunk/client_region/simp/Assets/StaticResources/Logo/x5_icon_152.png"],
        "x5_icon_167.png": ["mobile_dancer/trunk/client_region/simp/Assets/StaticResources/Logo/x5_icon_167.png"],
        "x5_icon_180.png": ["mobile_dancer/trunk/client_region/simp/Assets/StaticResources/Logo/x5_icon_180.png"],
        "x5_icon_256.png": ["mobile_dancer/trunk/client_region/simp/Assets/StaticResources/Logo/x5_icon.png"],
    }


class Gitlab:
    url = "https://x5mobile-gitlab.h3d.com.cn"
    token = "********************"
    repo = "dgm/x5mobile"


class Notification:
    user_list = ["<EMAIL>"]
    bot = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3526eae1-7971-4fb9-a16d-c07fd526c7eb"


class P4:
    port = "x5_mobile.p4.com:1666"
    username = "dgm_jenkins"
    password = "x5m12345"
    client = "game_icon_upload_git_{host_ip}_prod"
    need_upload_dir = "//美术资源/炫舞手游-ui/游戏图标"  # TODO 正式使用
