import os

from frame import advance, env, PyframeException, path_mgr, wechat

from project.x5m.game_icon_upload_git.mgr.game_icon_mgr import GameIconMgr
from project.x5m.game_icon_upload_git.mgr.icon_check_mgr import IconCheckMgr
from project.x5m.game_icon_upload_git.mgr.env_mgr import EnvMgr
from project.x5m.game_icon_upload_git.mgr.git_mgr import GitMgr
from project.x5m.game_icon_upload_git.config import config


@advance.stage(stage="从arthub下载图标资源")
def download_icon_from_arthub():
    game_icon_mgr = GameIconMgr()
    game_icon_mgr.download(save_path=env.pipeline.workspace())


@advance.stage(stage="检查是否需要上传")
def check_need_upload():
    game_icon_mgr = GameIconMgr()
    game_icon_mgr.check_need_upload()


@advance.stage(stage="从p4下载图标资源")
def download_icon_from_p4():
    game_icon_mgr = GameIconMgr()
    game_icon_mgr.download_from_p4(save_path=env.pipeline.workspace())


@advance.stage(stage="检查android图标")
def check_android_icon():
    android_download_path = os.path.join(env.pipeline.workspace(), "android")
    icon_check_mgr = IconCheckMgr(android_download_path)
    icon_check_mgr.check_number_of_icon()
    unmatch_size = icon_check_mgr.check_size_match()
    if unmatch_size:
        EnvMgr.set_android_unmatch_icon(unmatch_size)
        raise PyframeException("android图标尺寸不匹配")

    _, not_exists_alpha = icon_check_mgr.check_alpha_exists()
    if not_exists_alpha:
        EnvMgr.set_android_no_alpha_icon(not_exists_alpha)
        raise PyframeException("android图标有图片没有alpha通道")

    incorrect_icon = icon_check_mgr.check_icon_size()
    if incorrect_icon:
        EnvMgr.set_android_incorrect_size(incorrect_icon)
        raise PyframeException("android图标有图片尺寸和名称不匹配")


@advance.stage(stage="检查ios图标")
def check_ios_icon():
    ios_download_path = os.path.join(env.pipeline.workspace(), "ios")
    icon_check_mgr = IconCheckMgr(ios_download_path)
    icon_check_mgr.check_number_of_icon()
    unmatch_size = icon_check_mgr.check_size_match()
    if unmatch_size:
        EnvMgr.set_ios_unmatch_icon(unmatch_size)
        raise PyframeException("ios图标尺寸不匹配")

    exists_alpha, _ = icon_check_mgr.check_alpha_exists()
    if exists_alpha:
        EnvMgr.set_ios_alpha_icon(exists_alpha)
        raise PyframeException("ios图标有图片存在alpha通道")

    incorrect_icon = icon_check_mgr.check_icon_size()
    if incorrect_icon:
        EnvMgr.set_ios_incorrect_size(incorrect_icon)
        raise PyframeException("ios图标有图片尺寸和名称不匹配")


@advance.stage(stage="更新x5mobile代码")
def get_x5mobile():
    git_mgr = GitMgr()
    git_mgr.pull()


@advance.stage(stage="复制android图标")
def copy_android_icon():
    android_download_path = os.path.join(env.pipeline.workspace(), "android")
    git_mgr = GitMgr()
    for image, git_paths in git_mgr.git_config.android_icon.items():
        for git_path in git_paths:
            path_mgr.copy(src=os.path.join(android_download_path, image), dst=os.path.join(git_mgr.project_dir, git_path))


@advance.stage(stage="复制ios图标")
def copy_ios_icon():
    ios_download_path = os.path.join(env.pipeline.workspace(), "ios")
    git_mgr = GitMgr()
    for image, git_paths in git_mgr.git_config.ios_icon.items():
        for git_path in git_paths:
            path_mgr.copy(src=os.path.join(ios_download_path, image), dst=os.path.join(git_mgr.project_dir, git_path))


@advance.stage(stage="将图标提交到git仓库")
def submit_to_git():
    git_mgr = GitMgr()
    git_mgr.commit()


def __get_msg():
    msg = ""
    _, pipeline_id = EnvMgr.get_pipeline_status_and_id()
    msg += f"**pipeline id**: {pipeline_id}\n"

    branch_name = EnvMgr.get_x5mobile_branch()
    if branch_name:
        msg += f"**分支**: {branch_name}\n"

    android_msg = ""
    android_unmatch_icon = EnvMgr.get_android_unmatch_icon()
    if android_unmatch_icon:
        android_msg += f"**不正确图标尺寸**: {','.join(android_unmatch_icon)}\n"

    no_alpha_icon = EnvMgr.get_android_no_alpha_icon()
    if no_alpha_icon:
        android_msg += f"**不存在alpha通道**: {','.join(no_alpha_icon)}\n"

    incorrect_icon = EnvMgr.get_android_incorrect_size()
    if incorrect_icon:
        android_msg += f"**图标名称和真实名称不匹配**: {','.join(incorrect_icon)}\n"

    android_upload_icons = EnvMgr.get_android_upload_icon()
    if android_upload_icons:
        android_msg += "**上传git文件**:\n{}\n".format("\n".join(android_upload_icons))

    if android_msg:
        msg += "**android**\n"
        msg += android_msg

    ios_msg = ""
    ios_unmatch_icon = EnvMgr.get_ios_unmatch_icon()
    if ios_unmatch_icon:
        ios_msg += f"**不正确图标尺寸**: {','.join(ios_unmatch_icon)}\n"

    alpha_icon = EnvMgr.get_ios_alpha_icon()
    if alpha_icon:
        ios_msg += f"**存在alpha通道**: {','.join(alpha_icon)}\n"

    incorrect_icon = EnvMgr.get_ios_incorrect_size()
    if incorrect_icon:
        ios_msg += f"**图标名称和真实名称不匹配**: {','.join(incorrect_icon)}\n"

    ios_upload_icons = EnvMgr.get_ios_upload_icon()
    if ios_upload_icons:
        ios_msg += "**上传git文件**:\n{}\n".format("\n".join(ios_upload_icons))

    if ios_msg:
        msg += "**ios**\n"
        msg += ios_msg

    gitlab_url = EnvMgr.get_gitlab_url()
    if gitlab_url:
        msg += f"**gitlab提交地址**: {gitlab_url}"

    return msg


def __get_users():
    users = config.Notification.user_list
    submitter = EnvMgr.get_icon_submitter()
    return users + submitter


def on_success():
    wechat.send_unicast_post_success(user_list=__get_users(), content=__get_msg())
    wechat.send_multicast_post_success(
        webhook=config.Notification.bot,
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_success()


def on_failure():
    wechat.send_unicast_post_failure(content=__get_msg())
    wechat.send_multicast_post_failure(
        webhook=config.Notification.bot,
        content=__get_msg(),
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_unstable():
    wechat.send_unicast_post_unstable(content=__get_msg())
    wechat.send_multicast_post_unstable(
        webhook=config.Notification.bot,
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_unstable()


if __name__ == "__main__":
    download_icon_from_arthub()
    check_android_icon()
    check_ios_icon()
    get_x5mobile()
    copy_android_icon()
    copy_ios_icon()
    submit_to_git()
