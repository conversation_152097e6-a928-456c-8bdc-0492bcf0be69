node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "win10_192.168.14.107"
            customWorkspace "D:\\game_icon_upload_git"
        }
    }
    options {
        disableConcurrentBuilds()
    }
    parameters {
        text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
    }
    // 因为四点到五点p4需要备份数据，流水线会执行失败
    triggers {
      cron('H/5 0-3,5-23 * * *')
    }
    environment {
       GIT_NAME = 'pyframe-pipeline'
    }

    stages {
        stage("安装python依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                                python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
                            """
                        )
                    }
                }
            }
        }
        // stage("从arthub下载图标资源") {
        //     steps {
        //         dir("${env.GIT_NAME}") {
        //             script {
        //                 bat(script: "python x5m.py game_icon_upload_git --job=download_icon_from_arthub")
        //             }
        //         }
        //     }
        // }
        stage("检查是否需要上传") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python x5m.py game_icon_upload_git --job=check_need_upload")
                    }
                }
            }
        }
        stage("从P4下载图标资源") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python x5m.py game_icon_upload_git --job=download_icon_from_p4")
                    }
                }
            }
        }
        stage("检查android图标") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python x5m.py game_icon_upload_git --job=check_android_icon")
                    }
                }
            }
        }
        stage("检查ios图标") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python x5m.py game_icon_upload_git --job=check_ios_icon")
                    }
                }
            }
        }
        stage("更新x5mobile代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python x5m.py game_icon_upload_git --job=get_x5mobile")
                    }
                }
            }
        }        
        stage("复制android图标") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python x5m.py game_icon_upload_git --job=copy_android_icon")
                    }
                }
            }
        }        
        stage("复制ios图标") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python x5m.py game_icon_upload_git --job=copy_ios_icon")
                    }
                }
            }
        }
        // TODO 测试使用
        stage("将图标提交到git仓库") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "python x5m.py game_icon_upload_git --job=submit_to_git")
                    }
                }
            }
        }
    }
    post {
        success {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python x5m.py game_icon_upload_git --job=on_success")
                }
            }
        }
        // TODO 测试使用
        failure {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python x5m.py game_icon_upload_git --job=on_failure")
                }
            }
        }
        unstable {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "python x5m.py game_icon_upload_git --job=on_unstable")
                }
            }
        }
    }
}