import re
import requests

from urllib.parse import urljoin
from frame import PyframeException, log
from project.x5m.game_icon_upload_git.mgr.redis_mgr import RedisMgr


class ArtHubApi:
    def __init__(self, token: str, domain: str, depot: str):
        redis_mgr = RedisMgr()
        self.headers = {
            "Content-Type": "application/json",
            "publictoken": token,
            "Accept": "application/json",
            "arthubtoken": redis_mgr.get_arthub_token(),
        }
        self.domain = domain
        self.depot_v2_url_prefix = f"{domain}/{depot}/data/openapi/v2/core/"

    @staticmethod
    def post(url, data=None, json=None, **kwargs) -> dict:
        try:
            resp = requests.post(url, data, json, **kwargs)
            resp_data = resp.json()
        except Exception as e:
            log.error(f"request {url} error, {e}")
            return {}

        return resp_data

    @staticmethod
    def get(url, params=None, **kwargs) -> dict:
        try:
            resp = requests.get(url, params, **kwargs)
            resp_data = resp.json()
        except Exception as e:
            log.error(f"request {url} error, {e}")
            return {}

        return resp_data

    def get_account_detail_by_account_name(self, account_names: list):
        body = {
            "account_name": account_names,
            "department_array": [],
        }
        url = f"{self.domain}/account/account/openapi/v3/core/get-account-detail-by-account-name"
        return self.post(url, headers=self.headers, json=body)

    def get_child_node_id_in_range(self, folder_id: int) -> dict:
        body = {
            "parent_id": folder_id,
            "offset": 0,
            "count": -1,
            "filter": [],
            "order": {"meta": "order_weight", "type": "descend"},
            "is_recursive": False,
        }
        return self.post(url=urljoin(self.depot_v2_url_prefix, "get-child-node-id-in-range"), headers=self.headers, json=body)

    def get_node_brief_by_id(self, ids: list) -> dict:
        body = {"ids": ids, "meta": ["id", "name", "name_pinyin", "parent_id", "node_type", "created_date", "updated_date", "last_modifier"]}
        return self.post(url=urljoin(self.depot_v2_url_prefix, "get-node-brief-by-id"), headers=self.headers, json=body)

    def get_download_signature(self, ids: list) -> dict:
        body = [{"object_id": object_id, "object_meta": "origin_url", "content_type": "application%2Foctet-stream"} for object_id in ids]
        return self.post(url=urljoin(self.depot_v2_url_prefix, "get-download-signature"), headers=self.headers, json=body)


class ArtHubMgr:
    def __init__(self, token, domain, depot):
        self.arthub_api = ArtHubApi(token, domain, depot)

    @staticmethod
    def get_resp_result(resp: dict):
        code = resp.get("code")
        if code is None or code != 0:
            raise PyframeException(f"error http response, code: {code}")

        return resp.get("result")

    def get_child_node_detail_by_folder_id(self, folder_id: int) -> list:
        child_nodes_resp = self.arthub_api.get_child_node_id_in_range(folder_id)
        child_nodes_result = self.get_resp_result(child_nodes_resp)

        nodes = child_nodes_result.get("nodes")
        nodes_detail_resp = self.arthub_api.get_node_brief_by_id(nodes)
        nodes_detail_result = self.get_resp_result(nodes_detail_resp)
        return nodes_detail_result.get("items", [])

    def get_submitter_by_folder_id(self, folder_id: int):
        nodes_detail_resp = self.arthub_api.get_node_brief_by_id([folder_id])
        nodes_detail_result = self.get_resp_result(nodes_detail_resp)
        modifiers = []
        for item in nodes_detail_result.get("items", []):
            last_modifier = item.get("last_modifier")
            modifiers.append(last_modifier)

        account_detail_resp = self.arthub_api.get_account_detail_by_account_name(modifiers)
        account_detail = self.get_resp_result(account_detail_resp)
        return [(x.get("nick_name"), x.get("ldap_name")) for x in account_detail if x.get("ldap_name")]

    def find_nodes_by_re(self, folder_id: int, pattern: str, depth=-1) -> list:
        """
        根据folder_id循环查找下面的每个目录和文件，找到所有节点名字满足匹配规则的节点信息
        Args:
            folder_id: 文件夹id
            pattern: 匹配规则, 使用正则表达式
            depth: 循环深度, 默认-1代表无限深, 0代表不进行查找
        Returns:
            list: 返回所有节点名字满足匹配规则的节点信息列表
        """
        nodes = []
        if depth == 0:
            return nodes

        loop_count = 0
        items = self.get_child_node_detail_by_folder_id(folder_id)
        while items:
            if loop_count == depth:
                break

            find_items = []
            for item in items:
                find_folder_id = item.get("id")
                find_name = item.get("name")
                if re.match(re.compile(pattern, re.S), find_name):
                    nodes.append(item)

                if loop_count + 1 == depth:
                    continue

                if item.get("node_type") == "directory":
                    find_items += self.get_child_node_detail_by_folder_id(find_folder_id)

            items = find_items
            loop_count += 1

        return nodes

    def get_download_signature(self, ids: list) -> list:
        signature_resp = self.arthub_api.get_download_signature(ids)
        signature_result = self.get_resp_result(signature_resp)
        return signature_result.get("items", [])


if __name__ == "__main__":
    arthub_api = ArtHubMgr(
        token="4b477",
        domain="http://arthub-api.h3d.com.cn",
        depot="x5mobile",
    )
    res = arthub_api.get_submitter_by_folder_id(1392670)
    # res = arthub_api.find_nodes_by_re(1332145, r"^\d+\.\d+.\d+$", depth=1)
    print(res)
