import os
import re

import requests

from urllib.parse import urljoin

from frame import PyframeException, env, log, path_mgr, pipeline_mgr
from project.x5m.game_icon_upload_git.config import config
from project.x5m.game_icon_upload_git.mgr.arthub_mgr import ArtHubMgr
from project.x5m.game_icon_upload_git.mgr.env_mgr import EnvMgr, jenkins_mgr, global_env_mgr
from project.x5m.game_icon_upload_git.mgr.p4_mgr import P4Mgr


class GameIconMgr:
    def __init__(self):
        self.arthub_config = config.ArtHub
        self.arthub_api = ArtHubMgr(
            token=self.arthub_config.token,
            domain=self.arthub_config.domain,
            depot=self.arthub_config.depot,
        )
        self.p4_mgr = P4Mgr()

    def _get_game_icon_child_node(self) -> list:
        """
        获取游戏图标目录下的所有子目录
        """
        return sorted(
            filter(
                # 过滤id为188655的说明文档
                # http://arthub.h3d.com.cn/x5mobile/detail/comment?node=188655
                lambda x: not x.get("id") == 188655,
                # 游戏图标目录id为188626
                # http://arthub.h3d.com.cn/x5mobile/pan?node=188626
                self.arthub_api.find_nodes_by_re(self.arthub_config.root_node_id, r"^\d+\.\d+\.\d+$", depth=1)
                # self.arthub_api.find_nodes_by_re(188626, r"^\d+$", depth=1)
            ),
            key=lambda x: x.get("name"),
        )

    def _get_android_download_url(self, folder_id: int) -> list:
        """
        获取android图标下载地址
        """
        android_node = self.arthub_api.find_nodes_by_re(folder_id, "^透明$")
        if not android_node:
            log.warn("没有找到`透明`目录")

            return pipeline_mgr.stop_current_build()
        return self._get_icon_download_url(android_node[0].get("id"))

    def get_ios_download_url(self, folder_id: int) -> list:
        """
        获取ios图标下载地址
        """
        ios_node = self.arthub_api.find_nodes_by_re(folder_id, "^白底$")
        if not ios_node:
            log.warn("没有找到`白底`目录")
            return pipeline_mgr.stop_current_build()

        return self._get_icon_download_url(ios_node[0].get("id"))

    def _get_icon_download_url(self, platform_node_id: int) -> list:
        """
        获取图标下载地址
        """
        assets_nodes = self.arthub_api.get_child_node_detail_by_folder_id(platform_node_id)
        assets_ids = [x.get("id") for x in assets_nodes]
        return [
            (
                x.get("signed_url"),
                os.path.split(x.get("origin_url"))[1],
            )
            for x in self.arthub_api.get_download_signature(assets_ids)
        ]

    @staticmethod
    def _download_from_url(url_list: list, save_path: str):
        """
        从url下载文件
        """
        path_mgr.rm(save_path)
        path_mgr.mkdir(save_path)

        for url, file_name in url_list:
            response = requests.get(urljoin("http://", url))
            with open(os.path.join(save_path, file_name), "wb") as f:
                f.write(response.content)

    def download(self, save_path=env.pipeline.workspace()):
        """
        下载游戏图标
        """
        child_nodes = self._get_game_icon_child_node()
        if not child_nodes:
            raise PyframeException("没有找到匹配的版本号")

        child_node = child_nodes[-1]
        submitters_data = self.arthub_api.get_submitter_by_folder_id(child_node.get("id"))
        submitters = [x[1] for x in submitters_data]
        EnvMgr.set_icon_submitter(submitters)
        EnvMgr.set_x5mobile_branch(child_node.get("name"))
        log.info(f"查找到节点{child_node}")

        self._download_from_url(
            url_list=self._get_android_download_url(child_node.get("id")),
            save_path=os.path.join(save_path, "android"),
        )
        self._download_from_url(
            url_list=self.get_ios_download_url(child_node.get("id")),
            save_path=os.path.join(save_path, "ios"),
        )

    def check_need_upload(self):
        """
        检查是否需要上传
        """
        first_changelist = jenkins_mgr.first_changelist
        last_changelist = global_env_mgr.last_changelist

        if not first_changelist or first_changelist == "0":
            if not last_changelist:
                raise PyframeException("流水线第一次运行, 请指定first_changelist")
            changelist = int(last_changelist) + 1
        else:
            changelist = first_changelist

        changes = self.p4_mgr.get_changes(changelist)
        log.info(f"检查changelist: {changelist}, {changes}")
        if not changes:
            pipeline_mgr.stop_current_build()
        else:
            EnvMgr.set_changes(changes)

    def download_from_p4(self, save_path=env.pipeline.workspace()):
        """
        从P4下载游戏图标
        """
        changes = EnvMgr.get_changes()
        users = []
        files = []
        pattern = re.compile(r"^.*<(.*@h3d.com.cn)>$")
        for change in changes:
            # 获取提交记录中的提交人
            desc = change.get("desc")
            ret = pattern.findall(desc)
            if ret:
                users.append(ret[0])
            user = change.get("user")
            if user not in users:
                if not user.endswith("@h3d.com.cn"):
                    user = user + "@h3d.com.cn"
                users.append(user)
            changelist = change.get("change")
            changed_files = self.p4_mgr.get_changed_files(changelist)
            files.extend(changed_files)
        EnvMgr.set_icon_submitter(users)

        p4_views = self.p4_mgr.get_views(files)
        self.p4_mgr.p4.set_view(p4_views)
        self.p4_mgr.p4.sync_all(force=True)

    def get_submitter(self, folder_id: int):
        #
        # self.arthub_api.get_submitter_by_folder_id()
        return


if __name__ == "__main__":
    game_icon = GameIconMgr()
    game_icon.download(".")
    # game_icon.get_android_download_url()
