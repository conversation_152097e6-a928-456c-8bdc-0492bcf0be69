import os

from frame import GitMgr as _GitMgr, env, path_mgr, pipeline_mgr

from project.x5m.game_icon_upload_git.config import config
from project.x5m.game_icon_upload_git.mgr.env_mgr import EnvMgr, global_env_mgr
from project.x5m.game_icon_upload_git.mgr.gitlab_mgr import X5MobileGitlabMgr


class GitMgr:
    def __init__(self):
        self.git_config = config.Git
        self.git_mgr = _GitMgr(workdir=env.pipeline.workspace(), project_name=self.git_config.project_name)
        self.project_dir = self.git_mgr.project_dir()

    def pull(self):
        branch = EnvMgr.get_x5mobile_branch()
        if not self.git_mgr.exist():
            self.git_mgr.clone(url=self.git_config.url)
        self.git_mgr.fetch(all=True)
        self.git_mgr.reset(commit_id=f"origin/{branch}")
        self.git_mgr.clean()
        self.git_mgr.checkout(branch)

    def commit(self):
        path_mgr.rm(os.path.join(self.project_dir, "mobile_dancer", "trunk", "client", ".gitignore"))

        for _, git_paths in self.git_config.android_icon.items():
            for git_path in git_paths:
                self.git_mgr.add(git_path)

        android_upload_icons = self.git_mgr.get_staged_files()

        for _, git_paths in self.git_config.ios_icon.items():
            for git_path in git_paths:
                self.git_mgr.add(git_path)

        all_upload_files = self.git_mgr.get_staged_files()
        if not all_upload_files:
            pipeline_mgr.stop_current_build()
            return

        ios_upload_icons = [x for x in all_upload_files if x not in android_upload_icons]

        self.git_mgr.commit(f"[jenkins]自动提交图标资源, {env.pipeline.build_num()}")
        commit_id = self.git_mgr.get_local_latest_commit_id()

        self.git_mgr.pull(branch=EnvMgr.get_x5mobile_branch())
        self.git_mgr.push(branch=EnvMgr.get_x5mobile_branch())

        x5mobile_gitlab = X5MobileGitlabMgr()
        pipeline_status, pipeline_id = x5mobile_gitlab.get_pipeline_status_and_id_by_commit_id(commit_id)

        gitlab_url = f"{os.path.splitext(self.git_config.url)[0]}/-/commit/{commit_id}"
        EnvMgr.set_gitlab_url(gitlab_url)
        EnvMgr.set_android_upload_icon(android_upload_icons)
        EnvMgr.set_ios_upload_icon(ios_upload_icons)
        EnvMgr.set_piepline_status_and_id(pipeline_status, pipeline_id)

        # 记录本次的changelist
        changelist = EnvMgr.get_changes()[0].get("change")
        global_env_mgr.last_changelist = changelist
