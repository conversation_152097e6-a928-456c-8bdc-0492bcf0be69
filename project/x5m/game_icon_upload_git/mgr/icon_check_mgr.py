import os
import re

from typing import List
from PIL import Image

from frame import PyframeException, log


class IconCheckMgr:
    def __init__(self, image_path):
        self.all_size = {76, 120, 152, 167, 180, 256, 1024}
        self.platform = os.path.split(image_path)[1]
        self.images_instances: List[Image] = []
        for image in os.listdir(image_path):
            self.images_instances.append(Image.open(os.path.join(image_path, image)))

    def check_number_of_icon(self):
        number_of_icon = len(self.images_instances)
        log.info(f"检查图片数量, {number_of_icon}")
        if number_of_icon != 7:
            raise PyframeException(f"{self.platform}图标数量不正确, {number_of_icon}")

    def check_size_match(self):
        file_size = set()
        log.info("检查图片名称中是否正确")
        for image in self.images_instances:
            filename = image.filename
            size_in_filename = int(re.findall(re.compile(r"_(\d+)\.", re.S), os.path.split(filename)[1])[0])
            file_size.add(size_in_filename)
        return list(self.all_size ^ file_size)

    def check_alpha_exists(self):
        exists_alpha_icon = []
        not_exists_alpha_icon = []
        log.info("检查图片是否存在alpha通道")
        for image in self.images_instances:
            filename = os.path.split(image.filename)[1]
            if image.mode == "RGBA" or image.mode == "LA":
                exists_alpha_icon.append(filename)
                continue

            not_exists_alpha_icon.append(filename)
        log.info(f"存在alpha通道: {exists_alpha_icon}, 不存在alpha通道: {not_exists_alpha_icon}")
        return exists_alpha_icon, not_exists_alpha_icon

    def check_icon_size(self):
        incorrect_icon = []
        for image in self.images_instances:
            filename = os.path.split(image.filename)[1]
            size_in_filename = int(re.findall(re.compile(r"_(\d+)\.", re.S), os.path.split(filename)[1])[0])
            width, height = image.size
            if width != size_in_filename or height != height:
                incorrect_icon.append(filename)
        log.info(f"检查图标名称和实际尺寸是否匹配, 不匹配图标: {incorrect_icon}")
        return incorrect_icon


if __name__ == "__main__":
    icon_check = IconCheckMgr(r"E:\python\test\arthub_search\android")
    res = icon_check.check_icon_size()
    print(res)
