import re
from frame import *
from project.x5m.game_icon_upload_git.config import config
from project.x5m.game_icon_upload_git.mgr.env_mgr import EnvMgr


class P4Mgr:
    def __init__(self) -> None:
        self.workspace = env.pipeline.workspace()
        self.p4_config = config.P4
        self.client = self.p4_config.client.format(host_ip=common.get_host_ip())
        self.p4 = P4Client(
            host=self.p4_config.port,
            username=self.p4_config.username,
            password=self.p4_config.password,
            client=self.client,
            charset=P4Client.Charset.CP936,
        )
        views = [
            f"{self.p4_config.need_upload_dir}/... //{self.client}/{self.p4_config.need_upload_dir.replace('//', '')}/...",
        ]
        self.p4.set_view(views)
        self.p4.set_root(path=self.workspace)
        self.p4.set_options(allwrite=True, clobber=True, rmdir=True)

    def get_views(self, changed_files: list) -> list:
        p4_views = []
        for file in changed_files:
            # 拼接出p4的view
            file_name = file.split("/")[-1]
            if "透明" in file:
                p4_view = f"{file} //{self.client}/android/{file_name}"
            elif "白底" in file:
                p4_view = f"{file} //{self.client}/ios/{file_name}"
            else:
                continue
            p4_views.append(p4_view)
        log.info(f"p4_views: {p4_views}")
        return p4_views

    def get_changes(self, changlist: str) -> list:
        changes = self.p4.get_changes(f"{self.p4_config.need_upload_dir}/...@{changlist},now", max=1000)
        log.info(f"changes: {changes}")
        return changes

    def __get_changed_branch(self, changed_files: list) -> str:
        changed_branches = []
        pattern = re.compile(rf"{self.p4_config.need_upload_dir}/(\d+\.\d+\.\d+).*")
        for file in changed_files:
            branches = pattern.findall(file)
            changed_branches.extend(branches)
        log.info(f"changed_branches: {changed_branches}")

        changed_branches = list(set(changed_branches))
        if not changed_branches:
            raise PyframeException("请确认图标上传路径是否包含分支目录")
        if len(changed_branches) > 1:
            raise PyframeException("当前不支持多个分支图标同时操作,请删除后重新单独上传")
        branch = changed_branches[0]
        log.info(f"branch: {branch}")
        EnvMgr.set_x5mobile_branch(branch)
        return branch

    def get_changed_files(self, changlist: str) -> list:
        changed_files = self.p4.get_files_by_changelist(changlist)
        log.info(f"changed_files: {changed_files}")
        self.__get_changed_branch(changed_files)
        return changed_files
