from frame import advance, wechat
from project.x5m.h5_package.mgr import config
from project.x5m.h5_package.mgr.h5_mgr import h5_mgr, env_mgr


@advance.stage("清理工作目录")
def clean():
    h5_mgr.clean()


@advance.stage("获取h5version")
def get_last_version():
    h5_mgr.get_last_version()


@advance.stage("更新p4")
def update_p4():
    h5_mgr.update_p4()


@advance.stage("打包")
def package():
    h5_mgr.package()


@advance.stage("上传h5到ftp")
def upload_h5_to_ftp():
    h5_mgr.upload_h5_to_ftp()


def on_success(**kwargs):
    content = __get_msg()
    wechat.send_unicast_post_success(content=content)
    wechat.send_multicast_post_success(webhook=config.webhook, content=content)
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    content = __get_msg()
    wechat.send_unicast_post_failure(content=content, rescue=False, to_admin=True)
    wechat.send_multicast_post_failure(webhook=config.webhook, content=content, rescue=False)
    advance.insert_pipeline_history_on_failure()


def __get_msg():
    msg = ""
    msg += f"**版本号**: {env_mgr.last_ftp_version}\n"
    msg += f"**小图高度**: {env_mgr.resized_height}"
    return msg
