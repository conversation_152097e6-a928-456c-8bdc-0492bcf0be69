from frame import env, common, PyframeException


class EnvMgr:
    @property
    def p4_force(self):
        return common.str2bool(env.get("P4_FORCE", "False"))

    @property
    def resized_height(self) -> int:
        resized_height = env.get("RESIZED_HEIGHT")
        if not resized_height:
            raise PyframeException("没有指定小图resize高度")
        return int(resized_height)

    @property
    def force_tiny(self) -> bool:
        return common.str2bool(env.get("FORCE_TINY", "False"))

    @property
    def workspace(self) -> str:
        return env.get("WORKSPACE")

    @property
    def last_p4_version(self) -> str:
        return env.get("last_p4_version", "")

    def set_last_p4_version(self, last_p4_version):
        env.set({"last_p4_version": last_p4_version})

    @property
    def last_ftp_version(self) -> str:
        return env.get("last_ftp_version", "")

    def set_last_ftp_version(self, last_ftp_version):
        env.set({"last_ftp_version": last_ftp_version})


env_mgr = EnvMgr()
