from frame import FtpMgr, PyframeException, log, os
from project.x5m.h5_package.mgr import config
from project.x5m.h5_package.mgr.env_mgr import env_mgr


class H5FtpMgr:
    def __init__(self) -> None:
        self.__ftp = FtpMgr(**config.FTP_CONFIG_150_30)

    @property
    def ftp(self) -> FtpMgr:
        return self.__ftp

    def set_last_version(self):
        first_package_list = self.__ftp.dirs(config.H5_Ftp_Path)
        if not first_package_list:
            raise PyframeException("No h5 package found in ftp server.")
        latest_first_version = sorted(first_package_list)[-1]
        send_package_list = self.__ftp.dirs(f"{config.H5_Ftp_Path}/{latest_first_version}")
        if send_package_list:
            second_version = sorted(send_package_list)[-1]
        else:
            second_version = "v0"
        latest_send_version = "v{}".format(int(second_version.split("v")[-1]) + 1)
        version = "{}{}".format(latest_first_version, latest_send_version)
        env_mgr.set_last_ftp_version(version)

    def upload_h5_to_ftp(self, workdir):
        new_version = env_mgr.last_ftp_version
        package_root = os.path.join(workdir, new_version)
        log.info("上传H5增量原资源")
        source_src = os.path.join(package_root, "source")
        self.ftp.upload_folder(source_src, config.H5_Ftp_Root)
        log.info("上传H5增量zip包")
        zip_src = os.path.join(package_root, f"{new_version}.zip")
        self.ftp.upload_file(zip_src, f"{config.H5_Ftp_Root}/h5_server_resources/{new_version}")
        log.info("上传H5资源索引配置文件")
        config_src = os.path.join(package_root, "config/h5_res_list.csv")
        self.ftp.upload_file(config_src, f"{config.H5_Ftp_Root}/update_config/h5_config")
        self.ftp.upload_file(config_src, f"{config.H5_Ftp_Root}/h5_server_resources/{new_version}")
