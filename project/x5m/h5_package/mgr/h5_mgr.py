import binascii
import csv
import os
import json
from PIL import Image
from frame import path_mgr, log, tar, PyframeException
from project.x5m.h5_package.mgr.env_mgr import env_mgr
from project.x5m.h5_package.mgr.p4_mgr import P4Mgr
from project.x5m.h5_package.mgr.ftp_mgr import H5FtpMgr


class H5Mgr:
    def __init__(self):
        self.__workspace = env_mgr.workspace
        self.__p4_mgr: P4Mgr = None
        self.__ftp_mgr: H5FtpMgr = None

        self.__new_version = ""
        self.__resize_height = None
        # 资源路径
        self.__source_root = os.path.join(self.__workspace, "x5_mobile/mr/art_release/art_src/texture")
        # h5路径
        self.__h5_root = os.path.join(self.__workspace, "x5m/res/h5")
        # 打包路径
        self.__package_root = os.path.join(self.__workspace, "packaged")

    @property
    def p4_mgr(self) -> P4Mgr:
        if not self.__p4_mgr:
            self.__p4_mgr = P4Mgr()
        return self.__p4_mgr

    @property
    def ftp_mgr(self) -> H5FtpMgr:
        if not self.__ftp_mgr:
            self.__ftp_mgr = H5FtpMgr()
        return self.__ftp_mgr

    @property
    def new_version(self) -> str:
        if not self.__new_version:
            self.__new_version = env_mgr.last_ftp_version
        return self.__new_version

    @property
    def resize_height(self) -> int:
        if not self.__resize_height:
            self.__resize_height = env_mgr.resized_height
        return self.__resize_height

    def clean(self):
        self.__clean()

    def get_last_version(self):
        self.p4_mgr.set_last_version()
        self.ftp_mgr.set_last_version()

    def update_p4(self):
        self.p4_mgr.update()

    def __clean(self):
        if env_mgr.p4_force:
            self.__safe_clean(self.__source_root)
            self.__safe_clean(self.__h5_root)
        self.__safe_clean(self.__package_root)

    def __safe_clean(self, path):
        if path_mgr.exists(path):
            path_mgr.rm(path)
        path_mgr.mkdir(path)

    def package(self):
        _, first_version, second_version = self.new_version.split("v")
        package_root = f"{self.__package_root}/{self.new_version}"
        if not path_mgr.exists(package_root):
            path_mgr.mkdir(package_root)
        new_data = self.__get_new_data()
        old_data = self.__read_csv_file()
        # 目前不考虑delete  delete_list是空的
        insert_list, delete_list, update_list = self.__get_change(old_data, new_data)
        if not insert_list and not update_list:
            return
        package_path = os.path.join(package_root, f"source/h5/v{first_version}/v{second_version}")
        self.__store_inc_source_package(package_path, insert_list, update_list)
        if env_mgr.force_tiny:
            self.__force_tiny_source_package(package_path, insert_list)
        else:
            self.__tiny_source_package(package_path, update_list, insert_list)
        tar.compress(os.path.join(package_root, f"source"), os.path.join(package_root, f"v{first_version}v{second_version}.zip"))
        self.__concat_new_config_file(package_root, insert_list, update_list, delete_list)

    def __get_new_data(self):
        log.info("开始获取新的资源信息")
        data_dict = {}
        for dir_path, _, filenames in os.walk(self.__source_root):
            for filename in filenames:
                file_path = os.path.join(dir_path, filename)
                relative_path = "texture{}".format(file_path.replace(self.__source_root, "")).replace(os.path.sep, "/")
                file_crc32_value = self.__get_file_crc32_value(file_path)
                data_dict[relative_path] = {"res_version": self.new_version, "res_path": relative_path, "crc32": str(file_crc32_value)}
        return data_dict

    def __read_csv_file(self):
        """
        读取 csv文件
        """
        log.info("开始读取旧的资源配置表")
        data_dict = {}
        if not env_mgr.last_p4_version:
            log.info("p4上没有已发布的h5资源，返回空数据，后续进行全量打包逻辑")
            return data_dict
        path = os.path.join(self.__h5_root, f"{env_mgr.last_p4_version}/h5_res_list.csv")
        with open(path, "r") as f:
            csv_dict_obj = csv.DictReader(f)
            for item in csv_dict_obj:
                # 小图的增删改跟大图走，不考虑本身的变动
                if item["res_path"].startswith("texture/card_icon_tiny"):
                    continue
                data_dict[item.get("res_path")] = item
        return data_dict

    def __get_change(self, old_data, new_data):
        ins_list, del_list, upd_list = [], [], []
        for key, value in new_data.items():
            if not old_data.get(key):
                ins_list.append(dict(value))
            else:
                if value.get("crc32") != old_data[key].get("crc32"):
                    log.info("new: {} old: {}".format(value.get("crc32"), old_data[key].get("crc32")))
                    upd_list.append(dict(value))
        log.info("新增的文件信息列表:\n{}".format("\n".join(map(json.dumps, ins_list))))
        log.info("删除的文件信息列表:\n{}".format("\n".join(map(json.dumps, del_list))))
        log.info("更新的文件信息列表:\n{}".format("\n".join(map(json.dumps, upd_list))))
        return ins_list, del_list, upd_list

    def __get_file_crc32_value(self, file_path):
        """
        获取文件的crc32值
        """
        with open(file_path, "rb") as fp:
            temp = binascii.crc32(fp.read())
            if temp > 0x7FFFFFFF:  # 大于 0xFFFFFFFF的一半：0x7fffffff：即为相对的负数，需要把uint转为int类型
                return -((temp - 1) ^ 0xFFFFFFFF)
            else:
                return temp

    def __store_inc_source_package(self, package_path: str, insert_list: list, update_list: list):
        """
        存放增量包资源
        """
        source_dir_path = os.path.dirname(self.__source_root)
        self.__copy_source_file(update_list, source_dir_path, package_path)
        self.__copy_source_file(insert_list, source_dir_path, package_path)

    def __force_tiny_source_package(self, package_path: str, insert_list: list) -> dict:
        log.info("强制生成小图标全量包")
        source_dir_path = os.path.dirname(self.__source_root)
        root = os.path.join(source_dir_path, "texture/card_icon")
        tiny_root = os.path.join(package_path, "texture/card_icon_tiny")
        if not path_mgr.exists(tiny_root):
            path_mgr.mkdir(tiny_root)
        err_list = []
        for filename in os.listdir(root):
            src = os.path.join(root, filename)
            tiny_rel_path = f"texture/card_icon_tiny/{filename}"
            dst = os.path.join(package_path, tiny_rel_path)
            try:
                self.__resize(src, dst, self.resize_height)
                insert_list.append({"res_version": self.new_version, "res_path": tiny_rel_path, "crc32": self.__get_file_crc32_value(dst)})
            except Exception as e:
                log.error(e)
                err_list.append(f"texture/card_icon/{filename}")
        if not os.listdir(tiny_root):
            path_mgr.rm(tiny_root)
        if err_list:
            raise PyframeException("生成小图标全量包失败，错误文件列表：\n{}".format(", ".join(err_list)))

    def __tiny_source_package(self, package_path: str, update_list: list, insert_list: list):
        log.info("生成小图标全量包")
        source_dir_path = os.path.dirname(self.__source_root)
        tiny_root = os.path.join(package_path, "texture/card_icon_tiny")
        if not path_mgr.exists(tiny_root):
            path_mgr.mkdir(tiny_root)
        err_list = []
        err_list.extend(self.__tiny_source_package_by_filelist(source_dir_path, package_path, update_list))
        err_list.extend(self.__tiny_source_package_by_filelist(source_dir_path, package_path, insert_list))
        if not os.listdir(tiny_root):
            path_mgr.rm(tiny_root)
        if err_list:
            raise PyframeException("生成小图标全量包失败，错误文件列表：\n{}".format(", ".join(err_list)))

    def __tiny_source_package_by_filelist(self, source_dir_path: str, package_path: str, file_list: list):
        err_list = []
        for i in range(len(file_list) - 1, -1, -1):
            file = file_list[i]
            rel_path: str = file["res_path"]
            if rel_path.startswith("texture/card_icon/"):
                tiny_rel_path = rel_path.replace("texture/card_icon", "texture/card_icon_tiny")
                path = os.path.join(package_path, tiny_rel_path)
                try:
                    self.__resize(os.path.join(source_dir_path, rel_path), path, self.resize_height)
                    file_list.insert(i, {"res_version": file["res_version"], "res_path": tiny_rel_path, "crc32": self.__get_file_crc32_value(path)})
                except Exception as e:
                    log.error(e)
                    err_list.append(rel_path)
        return err_list

    def __concat_new_config_file(self, package_root, insert_list, update_list, delete_list):
        """
        生成新的配置文件
        """
        # 打全量包
        if not env_mgr.last_p4_version:
            with open(new_config_path, "w", newline="") as new_fp:
                writer = csv.DictWriter(new_fp, fieldnames=fieldnames)
                writer.writeheader()
                # 写入新增的
                for i in insert_list:
                    writer.writerow(i)
            return
        config_path = os.path.join(self.__h5_root, f"{env_mgr.last_p4_version}/h5_res_list.csv")
        new_config_path = os.path.join(package_root, "config/h5_res_list.csv")
        new_config_dir_name = os.path.dirname(new_config_path)
        if not os.path.exists(new_config_dir_name):
            os.makedirs(new_config_dir_name)
        delete_res_path_list = [i["res_path"] for i in delete_list]
        update_res_path_dict = {i["res_path"]: i for i in update_list}
        fieldnames = ["res_version", "res_path", "crc32"]
        # 强打全量小图
        if env_mgr.force_tiny:
            with open(config_path, "r") as fp, open(new_config_path, "w", newline="") as new_fp:
                csv_dict_obj = csv.DictReader(fp)
                writer = csv.DictWriter(new_fp, fieldnames=fieldnames)
                writer.writeheader()
                for item in csv_dict_obj:
                    # 删除的跳过
                    if item["res_path"] in delete_res_path_list:
                        continue
                    if item["res_path"].startswith("texture/card_icon_tiny"):
                        continue
                    # 修改的替换
                    if item["res_path"] in update_res_path_dict:
                        item = update_res_path_dict[item["res_path"]]
                    writer.writerow(item)
                # 写入新增的
                for i in insert_list:
                    writer.writerow(i)
            return
        # 正常打增量包
        with open(config_path, "r") as fp, open(new_config_path, "w", newline="") as new_fp:
            csv_dict_obj = csv.DictReader(fp)
            writer = csv.DictWriter(new_fp, fieldnames=fieldnames)
            writer.writeheader()
            for item in csv_dict_obj:
                # 删除的跳过
                if item["res_path"] in delete_res_path_list:
                    continue
                # 修改的替换
                if item["res_path"] in update_res_path_dict:
                    item = update_res_path_dict[item["res_path"]]
                writer.writerow(item)
            # 写入新增的
            for i in insert_list:
                writer.writerow(i)

    def __resize(self, input_image_path: str, output_image_path: str, new_height: int):
        with Image.open(input_image_path) as original_image:
            width, height = original_image.size
            new_width = int(new_height / height * width)
            resized_image = original_image.resize((new_width, new_height))
            resized_image.save(output_image_path)

    def __copy_source_file(self, file_list, source_dir_path, store_path):
        """
        copy文件
        """
        for file in file_list:
            rel_path = file["res_path"]
            file_path = os.path.join(source_dir_path, rel_path)
            dst_path = os.path.join(store_path, rel_path)
            path_mgr.copy(file_path, dst_path)

    def upload_h5_to_ftp(self):
        self.ftp_mgr.upload_h5_to_ftp(self.__package_root)


h5_mgr = H5Mgr()
