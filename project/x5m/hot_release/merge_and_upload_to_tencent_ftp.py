import os
import sys
import subprocess
import hashlib
from P4 import P4, P4Exception
import zipfile, shutil, stat, hashlib, time


class TerminalDeal:
    def __init__(self):
        pass

    def terminal_get(self, cmd1, cmd2, sleep=None):
        try:
            obj = subprocess.Popen(cmd1, shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                                   stderr=subprocess.PIPE)
            byt = bytes(cmd2, 'utf-8')
            back = obj.communicate(input=byt, timeout=sleep)
            cmd_out = str(back[0], 'utf-8')
            cmd_err = str(back[1], 'utf-8')
            return cmd_out, cmd_err
        except:
            print('命令执行错误')

    def terminal_notget(self, cmd_input):
        os.system(cmd_input)


class P4Service(object):
    def __init__(self, user: str, port: str, passwd: str, workspace: str, root: str):
        self.root = root
        self.p4 = P4()
        self.p4.port = port
        self.p4.user = user
        self.p4.password = passwd
        self.p4.client = workspace

    def init_p4_view(self, views: []):
        p4_View = []
        for i in range(len(views)):
            v = self.format_view(views[i])
            if v != "":
                p4_View.append(v)
        if len(p4_View) == 0:
            return -1
        try:
            self.p4.connect()
            self.p4.run_login()
            p4_client = self.p4.fetch_client(self.p4.client)
            p4_client['Root'] = self.root
            p4_client['View'] = p4_View
            self.p4.save_client(p4_client)
        except P4Exception:
            for e in self.p4.errors:
                print(e)
                return -1
        finally:
            self.p4.disconnect()
        return 0

    def format_view(self, view: str):
        if not view.startswith("//"):
            return ""
        p4_view = str.format("{} //{}/{}", view, self.p4.client, view[2:])
        return p4_view

    def sync(self):
        try:
            self.p4.connect()
            self.p4.run_sync()
        except P4Exception:
            for e in self.p4.errors:
                print(e)
            return -1
        finally:
            self.p4.disconnect()
        return 0

    def sync_file(self, file: str):
        try:
            self.p4.connect()
            self.p4.run_sync('-f', file)
        except P4Exception:
            for e in self.p4.errors:
                print(e)
            return -1
        finally:
            self.p4.disconnect()
        return 0

    def reconcile(self, *args):
        try:
            self.p4.connect()
            self.p4.run('reconcile', args)
        except P4Exception:
            for e in self.p4.errors:
                print(e)
            return -1
        finally:
            self.p4.disconnect()
        return 0

    def submit(self, desc: str, *args, **kwargs):
        try:
            self.p4.connect()
            # change = self.p4.fetch_change()
            # change["Description"] = desc
            kwargs['encoding'] = 'gbk'
            result = self.p4.run_submit('-d', desc, *args, **kwargs)
            return result
        except P4Exception:
            for e in self.p4.errors:
                print(e)
        finally:
            self.p4.disconnect()

    def add(self, file: str):
        try:
            self.p4.connect()
            self.p4.run_add(file)
        except P4Exception:
            for e in self.p4.errors:
                print(e)
        finally:
            self.p4.disconnect()

    def edit(self, file: str):
        try:
            self.p4.connect()
            self.p4.run_edit(file)
        except P4Exception:
            for e in self.p4.errors:
                print(e)
        finally:
            self.p4.disconnect()

    def files(self, file: str):
        """
        返回目录下所有文件记录
        :param file: 路径
        :return:    文件记录
        """
        try:
            self.p4.connect()
            file_map = self.p4.run_files(file)
            return file_map
        except P4Exception:
            for e in self.p4.errors:
                print(e)
        finally:
            self.p4.disconnect()

    def get_dir_names(self, p4_path) -> []:
        dir_names = []
        try:
            results = self.dirs(p4_path)
            for res in results:
                dir_names.append(res['dir'].split("/")[-1])
        except Exception as e:
            print(e)
            return []
        return dir_names

    def dirs(self, p4_path: str):
        try:
            self.p4.connect()
            result = self.p4.run('dirs', p4_path)
        except P4Exception as e:
            raise e
        finally:
            self.p4.disconnect()
        return result

    def get_file_names_without_deleted(self, p4_path: str):
        file_names = []
        try:
            results = self.get_files_without_deleted(p4_path)
            for res in results:
                file_names.append(os.path.splitext(res['depotFile'].split("/")[-1])[0])
        except Exception:
            # logging.error(e)
            return []
        return file_names

    def get_files_without_deleted(self, p4_path: str):
        try:
            self.p4.connect()
            result = self.p4.run('files', '-e', p4_path)
        except P4Exception as e:
            raise e
        finally:
            self.p4.disconnect()
        return result

    def get_subdirectory_name(self, p4_path: str):
        try:
            temp_dict = []
            get_index = len(p4_path.split("/")) - 1
            results = self.get_files_without_deleted(p4_path)
            for i in results:
                temp_dict.append(i['depotFile'].split("/")[get_index])
            return list(set(temp_dict))
        except:
            return -1


class ZipUnzipService:
    def __init__(self):
        pass

    def zipDir(self, dir_path):
        """
        压缩指定文件夹，每回都会覆盖
        :param dir_path: 目标文件夹路径
        :param outFullName: 压缩文件保存路径+xxxx.zip
        :return: 无
        """
        try:
            outFullName = dir_path + '.zip'
            zip = zipfile.ZipFile(outFullName, "w", zipfile.ZIP_DEFLATED)
            print("正在压缩文件...")
            time_start = time.time()
            for path, dirnames, filenames in os.walk(dir_path):
                # 去掉目标跟路径，只对目标文件夹下边的文件及文件夹进行压缩
                fpath = path.replace(dir_path, '')
                for filename in filenames:
                    zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
            zip.close()
            print("压缩完成")
            time_end = time.time()
            print('压缩文件消耗时间为：' + str(time_end - time_start))
            return 0
        except:
            print("本地已有压缩文件")
            return -1

    def un_zip(self, file_name):
        """unzip zip file"""
        zip_file = zipfile.ZipFile(file_name)
        if os.path.isdir(file_name.replace(".zip", '')):
            pass
        else:
            os.mkdir(file_name.replace(".zip", ''))
        for names in zip_file.namelist():
            zip_file.extract(names, file_name.replace(".zip", ''))
        print("解压文件%s完成" % file_name)
        zip_file.close()
        return 0, file_name.replace(".zip", '')

    def delete_file(self, path):
        try:
            if os.path.exists(path):
                shutil.rmtree(path, onerror=self.readonly_handler)
                print("删除 %s 成功" % path)
            else:
                print("文件 %s 不存在" % path)
            return 0
        except:
            print("文件不存在或删除错误")
            return -1

    def readonly_handler(self, func, path, execinfo):
        os.chmod(path, stat.S_IWRITE)
        func(path)

    def delete_chmod(self, file):
        if os.path.exists(file):
            try:
                os.remove(file)
                print("删除 %s 成功" % file)
            except:
                os.chmod(file, stat.S_IWRITE)
                os.remove(file)
                print("删除 %s 成功" % file)
        else:
            print("文件 %s 不存在" % file)
            return -1
        return 0

    def replaceDirName(self, old_rootDir, new_rootDir):
        try:
            if old_rootDir != new_rootDir:
                os.rename(old_rootDir, new_rootDir)  # 替换
            return 0
        except:
            print("重命名失败，请检查文件是否存在")

    def move_file(self, path, obj_file):
        obj_may_file = obj_file + '\\' + path.split('\\')[-1]
        if not os.path.exists(obj_may_file):
            print('正在拷贝: %s' % path)
            shutil.copy2(path, obj_file)
            return 0
        else:
            if self.isModify(path, obj_file):
                os.chmod(obj_may_file, stat.S_IWRITE)
                shutil.copy2(path, obj_file)
                return 0
            else:
                print('目标文件夹下已有同名文件，目标文件：%s' % obj_may_file)
                return -1

    def move_files(self, path, obj_file):
        """
        :param path: 文件夹名字，不可用文件结尾
        :param obj_file:文件夹名字，不可用文件结尾
        """
        for files in os.listdir(path):
            name = os.path.join(path, files)
            back_name = os.path.join(obj_file, files)
            if os.path.isfile(name):
                if os.path.isfile(back_name):
                    if self.isModify(name, back_name):
                        shutil.copy2(name, back_name)
                    else:
                        shutil.copy2(name, back_name)
                        print('目标文件夹下已有同名文件，已完成覆盖，目标文件：%s' % back_name)
                else:
                    shutil.copy2(name, back_name)
            else:
                if not os.path.isdir(back_name):
                    os.makedirs(back_name)
                self.move_files(name, back_name)
        return 0

    def Merge(self, A_path, B_path):  # 合并两个目录
        B_paths = os.listdir(B_path)  # 获取当前B中的目录结构
        for fp in os.listdir(A_path):  # 遍历当前A目录中的文件或文件夹
            A_new_path = os.path.join(A_path, fp)  # A中的文件或目录
            B_new_path = os.path.join(B_path, fp)  # B中对应的文件或路径，不一定存在

            if os.path.isdir(A_new_path):  # A中的目录
                if os.path.exists(B_new_path):  # 如果在B中存在
                    self.Merge(A_new_path, B_new_path)  # 继续合并下一级目录
                else:  # 如果在B中不存在
                    print('[目录]\t%s ===> %s' % (A_new_path, B_new_path))
                    shutil.copytree(A_new_path, B_new_path)  # 完全复制目录到B
            elif os.path.isfile(A_new_path):  # A中的文件
                if os.path.exists(B_new_path):  # 如果在B中存在
                    s = os.stat(B_new_path)
                    if self.isModify(A_new_path, B_new_path):  # 如果该文件修改过
                        # 创建副本
                        suffix = B_new_path.split('.')[-1]  # 得到文件的后缀名
                        # 将B中原文件创建副本
                        B_copy_path = B_new_path[:-len(suffix) - 1] + "(%s)." % (self.Stamp2Time(s.st_mtime)) + suffix
                        print('[副本]\t%s ===> %s' % (A_new_path, B_copy_path))
                        shutil.copy2(B_new_path, B_copy_path)
                        # 将A中修改后文件复制过来
                        print('[文件]\t%s ===> %s' % (A_new_path, B_new_path))
                        shutil.copy2(A_new_path, B_new_path)
                    else:  # 如果该文件没有修改过
                        pass  # 不复制
                else:  # 如果在B中不存在
                    # 将该文件复制过去
                    print('[文件]\t%s ===> %s' % (A_new_path, B_new_path))
                    shutil.copy2(A_new_path, B_new_path)

    def isModify(self, A_file, B_file):  # 判断两个文件是否相同，如果不同，表示修改过
        # 参数需是绝对路径
        return self.GetFileMd5(A_file) != self.GetFileMd5(B_file)

    def GetFileMd5(self, filename):  # 计算文件的md5值
        if not os.path.isfile(filename):
            return
        myhash = hashlib.md5()
        f = open(filename, 'rb')
        while True:
            b = f.read(8096)
            if not b:
                break
        myhash.update(b)
        f.close()
        return myhash.hexdigest()

    def Stamp2Time(self, Stamp):  # 将时间戳转换成时间显示格式
        timeArray = time.localtime(Stamp)
        Time = time.strftime("%Y年%m月%d日 %H时%M分%S秒 旧文件副本", timeArray)
        return Time


class CalMd5Service:
    def __init__(self):
        pass

    def create_md5_file(self, file):
        """
        在当前目录下，生成md5文件：xxx.md5
        :param file:
        :return:
        """
        if not os.path.exists(file):
            print('%s is not exists!' % file)
            return 1
        try:
            myhash = hashlib.md5()
            with open(file, 'rb') as f:
                while True:
                    b = f.read(8096)
                    if not b:
                        break
                    myhash.update(b)
            with open('%s.md5' % file, "wb+") as w:
                s = myhash.hexdigest() + ' ' + './' + file.split('\\')[-1]
                s.format('%s' % s)
                w.write(s.encode())
                f.close()
                w.close()
            print("create %s.md5 success" % file)
            return 0
        except Exception:
            print("create %s.md5 error!" % file)
            return -1

    def create_config_file(self, path, createName):
        try:
            files = os.listdir(path)
            for file in files:
                file_path = os.path.join(path, file)
                if os.path.isfile(file_path):
                    self.write_config_file(file_path, createName)
                elif os.path.isdir(file_path):
                    self.create_config_file(file_path, createName)
            return 0
        except:
            return -1

    def write_config_file(self, path, createName):
        normpath = path.split('resources')[0] + 'resources'
        up_path = os.path.abspath(os.path.dirname(normpath) + os.path.sep + ".")
        md5_path = up_path + '\\' + createName
        with open(md5_path, "a") as w:
            b = os.path.basename(normpath)
            s = self.get_md5_of_file(path) + "  " + path.replace(normpath, './' + b).replace('\\', '/')
            s.format('%s' % s)
            w.write(str(s))
            w.write(str('\n'))
            w.close()

    def get_md5_of_file(self, filename):
        """
        get md5 of a file
        :param filename:
        :return:
        """
        if not os.path.isfile(filename):
            return None

        myhash = hashlib.md5()
        with open(filename, 'rb') as f:
            while True:
                b = f.read(8096)
                if not b:
                    break
                myhash.update(b)
        return myhash.hexdigest()

    def create_md5_file_only(self, file):
        """
        在当前目录下，生成md5文件：xxx.md5
        :param file:
        :return:
        """
        if not os.path.exists(file):
            print('%s is not exists!' % file)
            return 1
        try:
            myhash = hashlib.md5()
            with open(file, 'rb') as f:
                while True:
                    b = f.read(8096)
                    if not b:
                        break
                    myhash.update(b)
            with open('%s.md5' % file, "wb+") as w:
                s = myhash.hexdigest() + ' ' + './' + file.split('/')[-1]
                s.format('%s' % s)
                w.write(s.encode())
                f.close()
                w.close()
            print("create %s md5 success" % file)
            return 0
        except Exception:
            print("create %s md5 error!" % file)
            return -1

    def create_server_file(self, path, endName, createName):
        try:
            files = os.listdir(path)
            for file in files:
                file_path = os.path.join(path, file)
                if os.path.isfile(file_path):
                    self.write_server_file(file_path, endName, createName)
                elif os.path.isdir(file_path):
                    self.create_server_file(file_path, endName, createName)
            return 0
        except:
            return -1

    def write_server_file(self, path, endName, createName):
        normpath = path.split(endName)[1]
        md5_path = path.split(endName)[0] + endName + '\\' + createName
        with open(md5_path, "a") as w:
            s = self.get_md5_of_file(path) + "  ." + normpath.replace('\\', '/')
            s.format('%s' % s)
            w.write(str(s))
            w.write(str('\n'))
            w.close()


class UploadFile:
    def __init__(self, cdn: str, hot: str, h5: str):
        self.ter = TerminalDeal()
        self.cal = CalMd5Service()
        self.zip = ZipUnzipService()
        self.edit = create_num
        self.release_cdn = cdn.replace("'", '')
        self.release_hot = hot.replace("'", '')
        self.release_h5 = h5.strip("'")
        self.hot_path = hot_p4_path
        self.cdn_path = cdn_p4_path
        self.h5_path = h5_p4_path
        self.h5_config_path = h5_config_p4_path

    def upload_h5(self):
        """
        上传h5到指定目录
        :return:
        """
        tx_path = tx_release_path + '{edit}/h5/'.format(edit=self.edit)
        try:
            service_cmd = 'put -neweronly ' + self.h5_path + ' ' + tx_path
            self.ter.terminal_get(cmd_winscp, login_cmd + service_cmd)
            print('腾讯:上传H5文件成功，目标路径%s<br>' % tx_path)
        except:
            print('腾讯:上传H5文件失败，目标路径%s<br>' % tx_path)
            raise Exception('腾讯:上传H5文件失败，目标路径%s<br>' % tx_path)

    def upload_cdn(self):
        tx_path = tx_release_path + '{edit}/dress/'.format(edit=self.edit)
        try:
            service_cmd = 'put -neweronly ' + self.cdn_path + ' ' + tx_path
            self.ter.terminal_get(cmd_winscp, login_cmd + service_cmd)
            print('腾讯:上传CDN文件成功，目标路径%s<br>' % tx_path)
        except:
            print('腾讯:上传CDN文件失败，目标路径%s<br>' % tx_path)
            raise Exception('腾讯:上传CDN文件失败，目标路径%s<br>' % tx_path)

    def cal_cdn_md5(self):
        if self.cal.create_md5_file(self.cdn_path) == 0:
            print('计算CDN.MD5文件成功')
            return 0
        else:
            print('计算CDN.MD5文件失败')
            raise Exception('计算CDN.MD5文件失败')
            return -1

    def upload_cdn_md5(self):
        cdn_md5_path = self.cdn_path + '.md5'
        tx_path = tx_release_path + '{edit}/dress/'.format(edit=self.edit)
        if self.cal_cdn_md5() == 0:
            try:
                service_cmd = 'put -neweronly ' + cdn_md5_path + ' ' + tx_path
                self.ter.terminal_get(cmd_winscp, login_cmd + service_cmd)
                print('腾讯:上传CDN.MD5文件成功，目标路径%s<br>' % tx_path)
            except:
                print('腾讯:上传CDN.MD5文件失败，目标路径%s<br>' % tx_path)
                raise Exception('腾讯:上传CDN.MD5文件失败，目标路径%s<br>' % tx_path)

    def upload_hot(self):
        hot_path = self.hot_path + r'\{hot}.zip'.format(hot=self.release_hot)
        tx_path = tx_release_path + '{edit}/resources/'.format(edit=self.edit)
        try:
            service_cmd = 'put -neweronly ' + hot_path + ' ' + tx_path
            self.ter.terminal_get(cmd_winscp, login_cmd + service_cmd)
            print('腾讯:上传周更包文件成功，目标路径%s<br>' % tx_path)
        except:
            print('腾讯:上传周更包文件失败，目标路径%s<br>' % tx_path)
            raise Exception('腾讯:上传周更包文件失败，目标路径%s<br>' % tx_path)

    def cal_hot_md5(self):
        hot_path = self.hot_path + r'\{hot}.zip'.format(hot=self.release_hot)
        if self.cal.create_md5_file(hot_path) == 0:
            print('计算周更包.MD5文件成功')
            return 0
        else:
            print('计算周更包.MD5文件失败')
            raise Exception('计算周更包.MD5文件失败')
            return -1

    def upload_hot_md5(self):
        hot_md5_path = self.hot_path + r'\{hot}.zip.md5'.format(hot=self.release_hot)
        tx_path = tx_release_path + '{edit}/resources/'.format(edit=self.edit)
        if self.cal_hot_md5() == 0:
            try:
                service_cmd = 'put -neweronly ' + hot_md5_path + ' ' + tx_path
                self.ter.terminal_get(cmd_winscp, login_cmd + service_cmd)
                print('腾讯:上传周更包.MD5文件成功，目标路径%s<br>' % tx_path)
            except:
                print('腾讯:上传周更包.MD5文件失败，目标路径%s<br>' % tx_path)
                raise Exception('腾讯:上传周更包.MD5文件失败，目标路径%s<br>' % tx_path)

    def move_cdn_server(self):
        move_file = move_server_resources + '\\cdn_' + self.release_cdn + '.zip'
        try:
            self.zip.move_file(move_file, self.hot_path)
            self.zip.un_zip(self.hot_path + '\\cdn_' + self.release_cdn + '.zip')
            print('移动CDN服务器配置文件成功:' + move_file)
            return 0
        except:
            print('移动CDN服务器配置文件失败:' + move_file)
            raise Exception('移动CDN服务器配置文件失败:' + move_file)

    def merge_cdn_hot_server(self):
        if self.move_cdn_server() == 0:
            cdn_merge_file = self.hot_path + '\\cdn_' + self.release_cdn + '\\publish'
            hot_merge_file = self.hot_path + '\\resources_' + self.release_hot + '\\publish\\resources'
            try:
                self.zip.Merge(cdn_merge_file, hot_merge_file)
                print('合并cdn、周更包配置成功')
                return 0
            except:
                print('合并cdn、周更包配置错误')
                raise Exception('合并cdn、周更包配置错误')
                return -1

    def merge_h5_to_hot(self):
        """
        merge h5资源配置表到 热更配置
        :return:
        """
        # 移动 h5配置到 热更配置
        self.copy_h5_to_work_path(h5_release)
        # 合并 h5、周更包配置
        hot_config_path = self.hot_path + '\\resources_' + self.release_hot + '\\publish\\resources\\resources\\config\\server\\h3d'
        try:
            os.chmod(self.h5_config_path, stat.S_IWRITE)
            filename = os.path.basename(self.h5_config_path)
            shutil.copyfile(self.h5_config_path, os.path.join(hot_config_path, filename))
            print('合并H5、周更包配置成功')
            return 0
        except:
            print('合并H5、周更包配置错误')
            raise Exception('合并H5、周更包配置错误')
            return -1

    def copy_h5_to_work_path(self, h5_version: str):
        src_dir = rf'D:\h5_public\x5m\res\h5\{h5_version}'
        dst_dir = os.path.dirname(self.h5_config_path)
        if os.path.exists(dst_dir):
            shutil.rmtree(dst_dir)
        os.makedirs(os.path.dirname(dst_dir), exist_ok=True)
        shutil.copytree(src_dir, dst_dir)

    def create_config(self):
        config_path = self.hot_path + '\\config' + time.strftime('%Y%m%d', time.localtime())
        if not os.path.isdir(config_path):
            os.makedirs(config_path)
        return config_path

    def move_merge_file(self):
        back_path = self.create_config()
        move_file = self.hot_path + '\\resources_' + self.release_hot + '\\publish\\resources'
        try:
            self.zip.move_files(move_file, back_path)
            print('移动cdn、周更包配置成功')
            return 0, back_path
        except:
            print('移动cdn、周更包配置失败')
            raise Exception('移动cdn、周更包配置失败')
            return -1

    def cal_new_md5(self, path: str):
        try:
            ret = self.zip.delete_chmod(path + r'\config.md5')
            if ret == 0:
                print('删除原有config.MD5成功')
                ret = self.cal.create_config_file(path, 'config.md5')
                if ret == 0:
                    print('重新计算config.MD5成功')
                    return 0
        except:
            return -1

    def zip_cal_md5(self):
        back = self.move_merge_file()

        if back[0] == 0:
            ret = self.cal_new_md5(back[1])
            if ret != 0:
                print('重新计算config.MD5失败')
            try:
                self.zip.zipDir(back[1])
                self.cal.create_md5_file(back[1] + '.zip')
                print('压缩、计算config.zip成功')
                return 0, back[1] + '.zip'
            except:
                print('压缩、计算config.zip失败')
                return -1
        else:
            return back[0]

    def zip_cal_md5_only_resources(self):
        back = self.move_merge_file()
        if back[0] == 0:
            try:
                self.zip.zipDir(back[1])
                self.cal.create_md5_file(back[1] + '.zip')
                print('压缩、计算config.zip成功')
                return 0, back[1] + '.zip'
            except:
                print('压缩、计算config.zip失败')
                return -1

    def upload_config(self):
        # if self.merge_cdn_hot_server() == 0:
        back = self.zip_cal_md5()
        if back[0] == 0:
            config_path = back[1]
            tx_path = tx_release_path + '{edit}/'.format(edit=self.edit)
            try:
                service_cmd = 'put -neweronly ' + config_path + ' ' + tx_path
                service_md5_cmd = 'put -neweronly ' + config_path + '.md5' + ' ' + tx_path
                self.ter.terminal_get(cmd_winscp, login_cmd + service_cmd)
                self.ter.terminal_get(cmd_winscp, login_cmd + service_md5_cmd)
                print('腾讯:上传config、config.MD5文件成功，目标路径%s<br>' % tx_path)
            except:
                print('腾讯:上传config、config.MD5文件失败，目标路径%s<br>' % tx_path)

    def upload_config_only_resources(self):
        back = self.zip_cal_md5_only_resources()
        if back[0] == 0:
            config_path = back[1]
            tx_path = tx_release_path + '{edit}/'.format(edit=self.edit)
            try:
                service_cmd = 'put -neweronly ' + config_path + ' ' + tx_path
                service_md5_cmd = 'put -neweronly ' + config_path + '.md5' + ' ' + tx_path
                self.ter.terminal_get(cmd_winscp, login_cmd + service_cmd)
                self.ter.terminal_get(cmd_winscp, login_cmd + service_md5_cmd)
                print('腾讯:上传config、config.MD5文件成功，目标路径%s<br>' % tx_path)
            except:
                print('腾讯:上传config、config.MD5文件失败，目标路径%s<br>' % tx_path)

    def delete_local_config(self, path):
        """
        删除本地config文件，防止一天内多次发布，导致文件错误
        :param path:
        """
        try:
            self.zip.delete_file(path)
            self.zip.delete_file(path + '.md5')
            print('删除本地config.zip、md5文件成功')
        except:
            print('删除本地config.zip、md5文件失败')

    def create_readme(self):
        try:
            if not os.path.exists(work_path + r'\readme.txt'):
                open(work_path + r'\readme.txt', 'w', encoding='utf-8')
        except:
            print('本地创建readme.txt失败')
        return work_path + r'\readme.txt'

    def upload_readme(self):
        local_path = self.create_readme()
        tx_path = tx_release_path + '{edit}/'.format(edit=self.edit)
        try:
            service_cmd = 'put -neweronly ' + local_path + ' ' + tx_path
            self.ter.terminal_get(cmd_winscp, login_cmd + service_cmd)
            print('腾讯:上传readme.txt成功，目标路径{obj}<br>'.format(obj=tx_path))
        except:
            print('腾讯:上传readme.txt失败，目标路径{obj}<br>'.format(obj=tx_path))


class DealP4:
    def __init__(self, cdn: str, hot: str, h5: str):
        self.p4 = P4Service(P4User, P4PORT, P4PWD, 'release', P4Root)
        self.p4.init_p4_view(P4View)
        self.create_p4_root()
        self.ter = TerminalDeal()
        self.zip = ZipUnzipService()
        self.cal = CalMd5Service()
        self.up = UploadFile(cdn, hot, h5)
        self.edit = self.up.edit
        self.release_cdn = self.up.release_cdn
        self.release_hot = self.up.release_hot
        self.hot_path = self.up.hot_path
        self.server_path = onlineupdate_path

    def create_p4_root(self):
        if not os.path.isdir(P4Root):
            os.makedirs(P4Root)

    def check_release_server(self):
        temp_dic = {}
        big_edit = ''.join(self.release_hot[0:3][0] + '.' + self.release_hot[0:3][1:3] + '.' + '0')
        specific_path = self.server_path + big_edit + '/' + self.release_hot[0:9] + '000/...'
        p4_data = self.p4.files(specific_path)
        for i in p4_data:
            dic = {i['depotFile'].split('/')[server_release_index]: '/'.join(i['depotFile'].split('/')[0:9])}
            temp_dic.update(dic)
        if 'server_release' in temp_dic:
            print('腾讯:服务器代码/数据库文件有变更，不上传config目录，将文件部署到腾讯ftp<br>')
            return 0
        else:
            print('腾讯:服务器代码/数据库文件无变更,上传config目录<br>')
            return -1

    def deal_server_file(self, server_path: str):
        local_server_path = work_path + server_path.replace('//', '/').replace('...', '')
        if self.p4.sync_file(server_path) == 0:
            path_dir = os.listdir(local_server_path)
            for i in path_dir:
                if 'dgm_server' in i and i[-3:] == 'zip':
                    if self.deal_dgm_server_file(local_server_path + i) == 0:
                        self.cal_md5_server_file(local_server_path + i)
                        print('计算服务器代码zip包的MD5成功')
            self.upload_server_file(local_server_path)
        else:
            print('p4得服务器变更文件失败')

    def deal_dgm_server_file(self, file):
        self.zip.un_zip(file)
        if len(self.release_cdn) != 0 and len(self.release_hot) != 0:
            back = self.move_server_config(file)
            if back[0] == 0:
                ret = self.cal_server_md5(back[1])
                if ret != 0:
                    print('计算生成md5.md5失败')
                    raise Exception('计算生成md5.md5失败')
                if self.zip.replaceDirName(file.replace('.zip', '') + '/config.md5',
                                           file.replace('.zip', '') + '/md5.md5') == 0:
                    print('修改MD5文件名成功')
        else:
            back1 = self.move_server_config_only_resources(file)
            if back1[0] == 0:
                ret = self.cal_server_md5(back1[1])
                if ret != 0:
                    print('计算生成md5.md5失败')
                    raise Exception('计算生成md5.md5失败')
                if self.zip.replaceDirName(file.replace('.zip', '') + '/config.md5',
                                           file.replace('.zip', '') + '/md5.md5') == 0:
                    print('修改MD5文件名成功')
        return 0

    def move_server_config(self, file):
        # 将cdn、服务配置包合并移动过来
        if self.up.merge_cdn_hot_server() == 0:
            obj_path = file.replace('.zip', '')
            move_file = self.hot_path + '\\resources_' + self.release_hot + '\\publish\\resources'
            try:
                self.zip.move_files(move_file, obj_path)
                print('移动cdn、周更包配置成功')
                return 0, obj_path
            except:
                print('移动cdn、周更包配置失败')
                raise Exception('移动cdn、周更包配置失败')
                return -1
        else:
            print('mergeCDN、服务端配置包失败')
            raise Exception('mergeCDN、服务端配置包失败')

    def cal_server_md5(self, file):
        try:
            ret = self.zip.delete_chmod(file + '/config.md5')
            if ret != 0:
                print('删除原有的md5.md5失败')
                raise Exception('删除原有的md5.md5失败')
            ret = self.cal.create_server_file(file, os.path.basename(file), 'md5.md5')
            if ret == 0:
                print('计算生成md5.md5成功')
                return 0
        except:
            return -1

    def move_server_config_only_resources(self, file):
        # 将服务器配置包移动过来
        obj_path = file.replace('.zip', '')
        move_file = self.hot_path + '\\resources_' + self.release_hot + '\\publish\\resources'
        try:
            if self.zip.move_files(move_file, obj_path) == 0:
                print('移动cdn、周更包配置成功')
                return 0, obj_path
        except:
            print('移动cdn、周更包配置失败')
            return -1

    def cal_md5_server_file(self, path):
        ret = self.zip.delete_chmod(path)
        if ret == 0:
            try:
                if self.zip.zipDir(path.replace('.zip', '')) == 0:
                    ret = self.zip.delete_file(path.replace('.zip', ''))
                    if ret == 0:
                        self.cal.create_md5_file_only(path)
                print('计算服务器变更文件md5成功:' + path)
                return 0
            except:
                print('计算服务器变更文件md5失败:' + path)
                raise Exception('计算服务器变更文件md5失败:' + path)
                return -1

    def upload_server_file(self, path):
        tx_path = tx_release_path + '{edit}/'.format(edit=self.edit)
        for i in os.listdir(path):
            try:
                service_cmd = 'put -neweronly ' + path.replace('/', '\\') + i + ' ' + tx_path
                self.ter.terminal_get(cmd_winscp, login_cmd + service_cmd)
                print('腾讯:上传服务器文件{local}成功，目标路径{obj}<br>'.format(local=i, obj=tx_path))
            except:
                print('腾讯:上传服务器文件{local}失败，目标路径{obj}<br>'.format(local=i, obj=tx_path))
                raise Exception('腾讯:上传服务器文件{local}失败，目标路径{obj}<br>'.format(local=i, obj=tx_path))


if __name__ == '__main__':
    print(sys.version)
    os.system("chdir")
    work_path = "D:\\White_box_tools"

    cdn_release = sys.argv[1]
    hot_release = sys.argv[2]
    h5_release = sys.argv[3]

    move_server_resources = work_path + r'\file_services\version_test\server_resources'

    # p4用户信息
    P4User = "dgm_bksaas"
    P4PWD = "x5m12345"
    P4PORT = "x5_mobile.p4.com:1666"
    P4Root = work_path
    P4View = ["//x5_mobile/..."]

    debug = 0
    try:
        debug = int(sys.argv[4])
    except:
        pass
    if debug:
        # test
        tx_release_path = '/server/x5m_server/test/'
        hot_p4_path = work_path + r'\x5_mobile\mr\art_release_test\pack\hotfix'
        cdn_p4_path = work_path + r'\x5_mobile\mr\art_release_test\x5m\res\cdn\pack\{cdn}\{cdn}.zip'.format(cdn=cdn_release)
        h5_p4_path = work_path + r"\x5_mobile\mr\art_release_test\x5m\res\h5\{h5}\{h5}.zip".format(h5=h5_release)
        h5_config_p4_path = work_path + r"\x5_mobile\mr\art_release_test\x5m\res\h5\{}\h5_res_list.csv".format(h5_release)
        onlineupdate_path = "//x5_mobile/mr/art_release_test/onlineupdate/"
        server_release_index = 8
    else:
        tx_release_path = '/server/x5m_server/'
        hot_p4_path = work_path + r'\x5_mobile\mr\art_release\pack\hotfix'
        cdn_p4_path = work_path + r'\x5m\res\cdn\pack\{cdn}\{cdn}.zip'.format(cdn=cdn_release)
        h5_p4_path = work_path + r"\x5m\res\h5\{h5}\{h5}.zip".format(h5=h5_release)
        h5_config_p4_path = work_path + r"\x5m\res\h5\{h5}\h5_res_list.csv".format(h5=h5_release)
        onlineupdate_path = "//x5_mobile/mr/onlineupdate/"
        server_release_index = 7


    # winscp命令
    cmd_winscp = 'winscp'
    # login_cmd = 'open ftp://ftp.cloudstone.qq.com:9055 -explicit\nfworks_1019\nIeg#xH]xy[WRqEIVv4+6\n'
    login_cmd = 'open ftp://ftp-sh1.cloudstone.qq.com:9054 -explicit\nfworks_1019\nIeg#xH]xy[WRqEIVv4+6\n'
    if len(hot_release) <= 2:
        print('未填写文件名，退出...')
        raise Exception('未填写文件名')

    split_hot = hot_release.split('.')
    # 如果周更大版本号第二位是0：去掉0作为大版本号；
    # 不为0：取23位为大版本号的23位
    #if split_hot[0][1] == "0":
    #    create_num = split_hot[0][0] + '.' + split_hot[0][2] + '.' + \
    #                 split_hot[0][-1] + '.' + split_hot[1][:3]
    #else:
    #    create_num = split_hot[0][0] + '.' + split_hot[0][1:3] + '.' + \
    #                 split_hot[0][-1] + '.' + split_hot[1][:3]

    #create_num = split_hot[0][0] + '.' + split_hot[0][1:3] + '.' + \
    #                 split_hot[0][-1] + '.' + split_hot[1][:3]

    if len(hot_release) == 11:
        create_num = split_hot[0][0] + '.' + split_hot[0][1:3] + '.' + split_hot[0][-1] + '.' + split_hot[1][:3]
    elif len(hot_release) == 12:
        create_num = split_hot[0][0] + '.' + split_hot[0][1:3] + '.' + split_hot[0][-1] + '.' + split_hot[1][:4]

    print_info = ""
    if len(cdn_release) > 2:
        print_info += "**CDN包发布，CDN版本号:{}<br>".format(cdn_release)
    if len(hot_release) > 2:
        print_info += "{}周更包发布，周更包版本号:{}<br>".format("" if print_info else "**", hot_release)
    if len(h5_release) > 2:
        print_info += "{}H5包发布，H5版本号:{}, P4版本分支:{}<br>".format("" if print_info else "**", h5_release, create_num)
    print(print_info)

    mf = UploadFile(cdn_release, hot_release, h5_release)
    dp4 = DealP4(cdn_release, hot_release, h5_release)

    ret = dp4.check_release_server()
    # 如果没有服务器包
    if ret != 0:
        if len(cdn_release) <= 2 and len(h5_release) <= 2:
            print('只发布周更包，周更包版本号:' + hot_release)
            mf.upload_config_only_resources()
        else:
            # 先进行合并配置操作
            if len(cdn_release) > 2:
                mf.merge_cdn_hot_server()
            if len(h5_release) > 2:
                mf.merge_h5_to_hot()
            mf.upload_config()
    else:
        big_edit = ''.join(dp4.release_hot[0:3][0] + '.' + dp4.release_hot[0:3][1:3] + '.' + '0')
        specific_path = dp4.server_path + big_edit + '/' + dp4.release_hot[0:9] + '000/...'
        if len(h5_release) > 2:
            mf.merge_h5_to_hot()
        dp4.deal_server_file(specific_path.replace('...', 'server_release/...'))
    mf.upload_readme()
