import os
import re
import shutil
from pathlib import Path

from frame import Nexus
from frame import advance
from frame import common
from frame import env
from frame import log
from frame import wechat, PyframeException
from project.x5m import config

# 不同平台下不同发版类型与安装包类型的对应关系
package_relations = {
    "ANDROID_TXTEST": "Android-txtest",
    "ANDROID_TY": "Android-ty",
    "ANDROID_APPSTORE": "Android-AppStore",
    "IOS_TXTEST": "iOS-txtest",
    "IOS_APPSTORE": "iOS-AppStore",
}

# 安装包重命名时与安装包类型的对应关系
name_relations = {"Android-txtest": "204", "Android-ty": "221", "Android-AppStore": "", "iOS-txtest": "204", "iOS-AppStore": ""}


def __validate_package(package_list: list) -> bool:
    """
    进行安装包的验证，不能存在两个类型相同的且位数相同的安装包
    Args:
        package_list:

    Returns:
        bool:
    """
    temp_list = []
    for package in package_list:
        group_info = package.get("group")
        name_info = package.get("name")
        info = f"{group_info}/{name_info}"
        if info not in temp_list:
            temp_list.append(info)
        else:
            return False
    return True


def search_package(**kwargs: dict):
    """
    根据关键信息从制品库搜索安装包
    """
    # 检查磁盘空间是否满足执行流水线
    advance.check_disk_size(threshold=5)

    # 获取入参以及环境变量信息
    platform = kwargs.get("platform", "")
    log.info("platform: {}".format(platform))
    if not platform:
        raise PyframeException("请检查平台参数platform是否正确")

    version_type = env.get("VERSION_TYPE")
    log.info("version_type: {}".format(version_type))
    if not version_type:
        raise PyframeException("请检查发版类型VERSION_TYPE环境变量是否正确")

    version_number = env.get("VERSION_NUMBER")
    log.info("version_number: {}".format(version_number))
    if not version_number:
        raise PyframeException("请检查版本号VERSION_NUMBER是否正确")

    build_number = env.get("INSTALLATION_{}".format(platform.upper()))
    log.info("build_number: {}".format(build_number))
    if not build_number:
        raise PyframeException("请检查构建号INSTALLATION_{}是否正确".format(platform.upper()))
    pattern = re.compile(r"(?!=#)\d+")
    build_number_list = re.findall(pattern, build_number)
    log.info("build_number_list: {}".format(build_number_list))
    if not build_number_list:
        raise PyframeException("获取构建号信息失败，请检查构建号INSTALLATION_{}是否正确".format(platform.upper()))

    # 组织查询参数 name、repository和group
    # 从2023年4月25日开始，Ptr发版也使用分支目录
    # ptr、rc和final在分支进行IPA和apk打包。
    if version_type in ["rc", "final", "ptr"]:
        jenkins_name = __get_jenkins_names(version_number)
    else:
        jenkins_name = "master"

    # 仅当版本为final时，且平台为安卓才会发布32位版本
    if version_type == "final" and platform == "android":
        bit_info = "*"  # 包含（32位或者64位）
    else:
        bit_info = 64

    log.info("jenkins_name: {}".format(jenkins_name))
    if not jenkins_name:
        raise PyframeException("请检查分支名称BRANCH_NAME是否正确")

    silent_version = env.get("SILENT_{}".format(platform.upper()))
    if not silent_version:
        raise PyframeException("请检查静默版本SILENT_VERSION是否正确")

    repository = "mobile"
    half_group = "/{}/{}".format(platform, jenkins_name)
    log.info("jenkins_name: {} silent_version: {} repository: {} half_group: {}".format(jenkins_name, silent_version, repository, half_group))
    # 根据关键词信息依次查询相关资源 并设置环境变量
    package_list = []
    nexus = Nexus(**config.NEXUS_CONFIG)
    for build_number in build_number_list:
        log.info("build_number: {}".format(build_number))
        name = "{platform}/{jenkins_name}*/qqx5_{bit_info}_silent_{silent_version}_*_{build_number}_git_*".format(
            platform=platform, jenkins_name=jenkins_name, silent_version=silent_version, build_number=build_number, bit_info=bit_info
        )
        log.info("name: {}".format(name))
        items = nexus.search(name=name, repository=repository, group="{}*".format(half_group))
        if not items:
            raise PyframeException("所查找的制品不存在，请确认查询参数信息或制品是否真实存在")

        for item in items:
            name = item.get("name")
            if name.endswith("libil2cpp.zip"):
                continue

            package_list.append(item)

    # 进行安装包类型校验
    if not __validate_package(package_list):
        raise PyframeException("请检查构建号信息是否正确，已经有相同类型的制品")
    env.set({"{}_package_list".format(platform): package_list})


def __handle_package_name(name: str) -> str:
    """
    根据上传规则将安装包重命名
    Args:
        name: 原始名字，例：
            qqx5_64_silent_5.05.62_Android-txtest_5.5.2_2060645_78_git_d8003b0ac4_38915_res.apk
            qqx5_lite_5.05.61_iOS-txtest_5.5.2_2061035_84_git_67faa7b_39145.ipa
    """
    try:
        log.info("name={}".format(name))
        name = name.replace("_res", "")  # 将安卓里面的特殊字符"_res"先去掉
        temp_list = name.split("/")
        if not temp_list:
            raise PyframeException("请检查安装包名字是否正确")

        old_name = temp_list[-1]
        log.info("old_name: {}".format(old_name))
        temp_list = old_name.split("_")

        # 从原始名字中提取部分信息重新拼接
        new_list = []
        new_list.extend(temp_list[0:3])
        new_list.extend(temp_list[5:7])
        new_list.append(temp_list[8])
        unknown = temp_list[-1].split(".")
        new_list.append(unknown[0])
        log.info("new_list: {}".format(new_list))
        server_info = name_relations[temp_list[4]]
        log.info("server_info: {}".format(server_info))
        if server_info:
            new_list.insert(len(new_list), server_info)
        new_name = "_".join(new_list) + ".{}".format(unknown[-1])
        log.info("new_name: {}".format(new_name))

        # 将安装包的新旧文件名对应关系记录到文件中
        name_file = os.path.join(r"D:\White_box_tools\pyframe-pipeline\logs", "package_info.txt")
        __record_name_relations(name_file, old_name, new_name)
    except Exception as e:
        raise PyframeException("安装包重命名错误，详情：{}".format(e))
    return new_name


def __record_name_relations(file: str, old_name: str, new_name: str):
    """
    将新旧文件名对应关系记录到文件中
    Args:
        file:
        old_name:
        new_name:
    """
    with open(file, "a+", encoding="utf-8") as f:
        f.write("原文件名：{}\n发布文件名：{}\n\n".format(old_name, new_name))


def __handle_version_number(version_number: str) -> str:
    """
    对版本号进行特殊处理满足客户端目录要求
    Args:
        version_number:

    Returns:

    """
    try:
        version_number_list = version_number.split(".")
        temp_list = version_number_list[0:-1]
        int_list = [str(int(temp)) for temp in temp_list]
        int_list.append(version_number_list[-1])
        new_version_number = ".".join(int_list)
        log.info("new_version_number: {}".format(new_version_number))
    except Exception as e:
        raise PyframeException("请检查版本号是否正确：version_number: {} 错误信息：{}".format(version_number, e))
    return new_version_number


def download_package(**kwargs: dict):
    """
    下载安装包资源到指定目录
    """
    # 获取平台信息和版本号信息
    platform = kwargs.get("platform")
    log.info("platform: {}".format(platform))
    if not platform:
        raise PyframeException("请检查平台参数platform是否正确")

    version_number = env.get("VERSION_NUMBER")
    log.info("version_number: {}".format(version_number))
    if not version_number:
        raise PyframeException("请检查版本号VERSION_NUMBER是否正确")

    # 区分测试环境和正式环境安装包的保存地址
    DST_PATH = r"D:\final_version_tool\installation_package\package"
    DST_PATH_DEBUG = r"D:\final_version_tool\installation_package\package_test"
    debug = env.get("DEBUG")
    dst_path = DST_PATH_DEBUG if int(debug) else DST_PATH

    # 根据版本号和平台信息创建具体目录，如果之前已经存在此目录，先删除，再重新创建
    version_number = __handle_version_number(version_number)
    final_path = os.path.join(dst_path, str(version_number), str(platform))
    log.info("final_path: {}".format(final_path))
    p = Path(final_path)
    if p.exists():
        shutil.rmtree(p)
    os.makedirs(final_path)

    # 初始化nexus对象并下载指定资源
    nexus = Nexus(**config.NEXUS_CONFIG)
    package_list = env.get("{}_package_list".format(platform))
    log.info("package list: {}".format(package_list))
    for package in package_list:
        assets = package.get("assets")
        download_url = assets[0].get("downloadUrl") if assets else None
        if not download_url:
            raise PyframeException("获取制品下载链接失败，请确认制品是否真实存在")
        # 获取原始名字后，根据上传规则进行重命名
        origin_name = package.get("name")
        handled_name = __handle_package_name(origin_name)
        dst = os.path.join(final_path, handled_name)
        nexus.download(src=download_url, dst=dst)
        # 下载完成后 计算MD5并写入文件
        __generate_md5(dst)


def __get_jenkins_names(version_number: str) -> str:
    """
    根据版本信息获取Jenkins流水线名称
    Args:
        version_number:

    Returns:

    """
    pattern = re.compile(r"(?!^\d+\.\d+\.)(\d+)$")
    return re.sub(pattern, "0", version_number)


def __generate_md5(file: str):
    """
    生成MD5值并保存到文件中
    Args:
        file:
    """
    try:
        md5_str = common.compute_md5(file)
        p = Path(file)
        file_name = os.path.join(p.parent, "{}{}.md5".format(p.stem, p.suffix))
        log.info("file_name: {}".format(file_name))
        with open(file_name, "w") as f:
            f.write("{}  {}".format(p.stem + p.suffix, md5_str))
    except Exception as e:
        raise PyframeException("MD5生成失败，请联系相关人员：{}".format(e))


def do_installation_success(**kwargs):
    """
    成功状态要做的事情
    """
    wechat.send_unicast_post_success(user_list=["<EMAIL>", "<EMAIL>"])
    advance.insert_pipeline_history_on_success()


def do_installation_failure(**kwargs):
    """
    失败状态要做的事情
    """
    wechat.send_unicast_post_failure(user_list=["<EMAIL>", "<EMAIL>"])
    advance.insert_pipeline_history_on_failure()


def do_installation_canceled(**kwargs):
    """
    取消状态要做的事情
    """
    wechat.send_unicast_post_canceled(user_list=["<EMAIL>", "<EMAIL>"])
    advance.insert_pipeline_history_on_canceled()
