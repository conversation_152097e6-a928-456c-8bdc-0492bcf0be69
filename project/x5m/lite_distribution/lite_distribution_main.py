# coding=utf-8
import os
import shutil
import stat
from pathlib import Path

import P4

from frame import Ftp
from frame import Perforce
from frame import advance
from frame import env
from frame import log
from frame import wechat, PyframeException
from project.x5m import config

# 调试模式下使用不同目录
DST_PATH = r"D:\White_box_tools\file_services\version_test\lite_res"
DST_PATH_DEBUG = r"D:\White_box_tools\file_services\version_test\lite_test"


def download_lite_file(**kwargs: dict):
    """
    从远程FTP下载LITE资源
    """
    # 检查磁盘空间是否满足执行流水线
    advance.check_disk_size(threshold=5)

    log.info("准备开始下载LITE资源")

    # 获取远程资源平台及版本信息
    platform = kwargs.get("platform")
    debug = kwargs.get("debug", 0)

    dst_path = DST_PATH_DEBUG if int(debug) else DST_PATH

    lite_android = env.get("LITE_ANDROID")
    lite_ios = env.get("LITE_IOS")

    # 初始化FTP对象
    ftp = Ftp(**config.FTP_CONFIG_150_30)

    # 分别下载两个平台的LITE资源
    if str(platform).lower() == "android":
        remote_path = "/version_test/lite_res/android/{}".format(lite_android)
        ftp.download(remote_path, dst_path)
    elif str(platform).lower() == "ios":
        remote_path = "/version_test/lite_res/ios/{}".format(lite_ios)
        ftp.download(remote_path, dst_path)
    else:
        raise PyframeException("平台参数错误，请确认参数是否为Android或者iOS")


def submit_lite_file(**kwargs):
    """
    上传P4目录下的LITE资源
    """
    log.info("开始准备上传LITE资源到P4")
    platform = kwargs.get("platform")
    debug = kwargs.get("debug", 0)

    # 获取平台参数信息
    if platform:
        platform = str(platform).lower()
    else:
        raise PyframeException("缺少平台参数，请确认参数是否为Android或者iOS")

    # 复制到P4目录并上传
    if platform == "android" or platform == "ios":
        __submit_lite_file(platform, debug)
    else:
        raise PyframeException("平台参数错误，请确认参数是否为Android或者iOS")


def __readonly_handler(func, path, exc_info):
    """
    修改文件夹属性
    Args:
        func:
        path:
        exc_info:
    """
    os.chmod(path, stat.S_IWRITE)
    func(path)


def copy_lite_file(platform: str, p4_path: str, debug: int = 0) -> list:
    """
    将下载好的lite资源拷贝到P4目录
    Args:
        platform:
        p4_path:
        debug:

    Returns:

    """
    log.info("platform: {}， p4_path: {}".format(platform, p4_path))

    # 获取LITE资源下载路径
    lite_version = env.get("LITE_{}".format(platform.upper()))
    remote_path = "/version_test/lite_res/{}/{}".format(platform.lower(), lite_version)

    dst_path = DST_PATH_DEBUG if int(debug) else DST_PATH

    # 原目录下面如果已经包含其他版本的LITE资源，先删除
    p = Path(p4_path)
    if p.exists() and os.listdir(p):
        shutil.rmtree(p, onerror=__readonly_handler)
        # path_mgr.rm(p)

    # 创建此次需要的LITE资源目录
    p4_path = os.path.join(p4_path, lite_version)
    # if not Path(p4_path).exists():
    #     os.makedirs(p4_path)

    # 复制所有文件，并返回复制列表
    need_submit = []
    dst = (dst_path + remote_path).replace("/", "\\")
    copy_result = shutil.copytree(dst, p4_path)
    if copy_result == p4_path:
        log.info("目录:{}已复制到:{}".format(dst, p4_path))
    else:
        raise PyframeException("目录{}复制过程中可能出现意外，请确认结果:{}".format(dst, copy_result))
    for file in Path(dst).rglob("*.*"):
        need_submit.append(os.path.join(p4_path, file.name))
    return need_submit


def init_p4(platform: str, debug: int = 0):
    """
    初始化P4客户端
    Args:
        platform:
        debug:

    Returns:

    """
    workspace = env.get("WORKSPACE")
    server_ip = env.get("SERVER_IP")
    p4_root = "lite_workspace"
    p4_client = "bk-devops-lite-distribution-{}-{}".format(server_ip, platform.upper())

    # 调试模式下使用不同的目录映射
    view_dict = {
        "P4_VIEW_ANDROID": "//x5m/res/lite/android/...",
        "P4_VIEW_ANDROID_DEBUG": "//x5_mobile/mr/art_release_test/x5m/res/lite/android/...",
        "P4_VIEW_IOS": "//x5m/res/lite/ios/...",
        "P4_VIEW_IOS_DEBUG": "//x5_mobile/mr/art_release_test/x5m/res/lite/ios/...",
    }
    p4_view = view_dict.get("P4_VIEW_{}_DEBUG".format(platform.upper())) if int(debug) else view_dict.get("P4_VIEW_{}".format(platform.upper()))
    p4_abs = os.path.join(workspace, p4_root, p4_view.replace("//", "").replace("...", "").replace("/", "\\"))

    config.P4_CONFIG_BKCI.update({"client": p4_client, "p4_view": [p4_view]})
    log.info("p4_config: {}".format(config.P4_CONFIG_BKCI))
    p4 = Perforce(**config.P4_CONFIG_BKCI)
    p4.create_workspace(client=p4_client, view=[p4_view], root=os.path.join(workspace, p4_root), options=["clobber"])
    return p4, p4_abs, p4_view


def __submit_lite_file(platform: str, debug: int = 0):
    """
    上传P4目录下的LITE资源
    Args:
        platform:

    Returns:

    """
    p4, p4_abs, p4_view = init_p4(platform, debug)
    p4.update_all()

    # 复制LITE资源到P4路径
    need_submit = copy_lite_file(platform, p4_abs, debug)

    version_type = env.get("VERSION_TYPE")
    if str(version_type).lower() != "final":
        log.info("发版类型为: {} 不需要提交P4".format(version_type))
        return

    log.info("需要被提交的文件: {}".format(need_submit))
    if need_submit:
        p4.reconcile(file=p4_view, params=["-a", "-e"])
        try:
            msg = " Release-Tool lite: {}".format(platform)
            p4.submit(msg)
        except P4.P4Exception as e:
            if "No files to submit from the default changelist." in e.errors:
                log.error("没有需要提交的文件，此内容请忽略。")
            else:
                raise e
    else:
        log.info("需要提交的内容列表为空，无需提交。")


def do_lite_success(**kwargs):
    """
    成功状态要做的事情
    """
    wechat.send_unicast_post_success(user_list=["<EMAIL>", "<EMAIL>"])


def do_lite_failure(**kwargs):
    """
    失败状态要做的事情
    """
    wechat.send_unicast_post_failure(user_list=["<EMAIL>", "<EMAIL>"])


def do_lite_canceled(**kwargs):
    """
    取消状态要做的事情
    """
    wechat.send_unicast_post_canceled(user_list=["<EMAIL>", "<EMAIL>"])
