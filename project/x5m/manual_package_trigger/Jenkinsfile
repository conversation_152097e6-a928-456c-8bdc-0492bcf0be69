node {
    label "manual_package_trigger"
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline{
    agent {
        node {
            label "manual_package_trigger"
        }
    }
//     triggers {
//         GenericTrigger(
//             genericVariables: [
//                 [key: 'resources', value: '$.resources'],
//                 [key: 'user', value: '$.user'],
//             ],
//             causeString: 'Triggered on $ref',
//             token: 'x5m_manual_package_trigger',
//             printContributedVariables: true,
//             printPostContent: true,
//             silentResponse: false,
//         )
//     }
    // Valid parameter types:
    // [booleanParam, choice, credentials, extendedChoice, file, gitParameter, text, separator, password,
    // persistentBoolean, persistentChoice, persistentString, persistentText, run, string, validatingString]
    parameters {
        text(name: 'resources', defaultValue: '//x5_mobile/mr/art_release/art_src/timeline/zhouyetao/1048450/daoju/bluefox/a/gdt_bluefox_stand_shape4_01_anim.anim', description: '强制打包的资源')
//         persistentString(name: 'user', defaultValue: '<EMAIL>', description: '用户')
    }
    options {
        // timestamps() // 展示时间戳
        disableConcurrentBuilds()
        timeout(time: 60, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: '60')
    }

    stages {
        stage("拉取脚本") {
            steps {
                bat label: '拉取脚本', script:
                '''
set branch="master"

if exist pyframe-pipeline (
    pushd pyframe-pipeline
    git config pull.rebase false
    git reset --hard
    git clean -xdf -e logs
    for /f "delims=" %%i in ('git branch --show-current') do set local_branch=%%i
	echo "%local_branch%"
    if %branch%=="%local_branch%" (
        git pull
    ) else (
        git checkout %branch%
		git pull
    )
) else (
	git clone -b %branch% https://gitlab.h3d.com.cn/dev-productivity/pyframe-pipeline.git
    pushd pyframe-pipeline
)
python3 -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
python3 -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q
'''
            }
        }
        stage("下载上传") {
            steps {
                dir("pyframe-pipeline") {
                    bat label: 'sync_and_submit', script:
                    '''
                    python3 x5m.py manual_package_trigger --job=sync_and_submit
                    '''
                }
            }
        }
    }
    post {
        success {
            dir("pyframe-pipeline") {
                bat label: '成功后通知', script:
                '''
                python3 x5m.py manual_package_trigger --job=on_success
                '''
            }
        }
        failure {
            dir("pyframe-pipeline") {
                bat label: '失败后通知', script:
                '''
                python3 x5m.py manual_package_trigger --job=on_failure
                '''
            }
        }
        unstable {
            dir("pyframe-pipeline") {
                bat label: '不稳定通知', script:
                '''
                python3 x5m.py manual_package_trigger --job=on_unstable
                '''
            }
        }
    }
}
