import base64
import json
import re
import shutil
from enum import Enum

import P4
import requests

from frame import *
from frame import cmd, env, log, path_mgr, Perforce, wechat, advance, PyframeException
from project.x5m import config
from project.x5m.music_effect_package.mgr.env_mgr import env_mgr

gitlab_host = "https://x5mobile-gitlab.h3d.com.cn/"

headers = {"PRIVATE-TOKEN": "o-yNgwVADiQ8CicYdt_4"}

bk_host = "http://**************:21935/"
# TODO 个人账号密码
bk_headers = {"X-DEVOPS-UID": "<EMAIL>", "content-type": "application/json"}
p4_root = "arttrunk"


class UpdateType(Enum):
    TRUNK = "主支"
    BRANCH = "分支"
    WEEK_UPDATE = "周更"


def __get_week_version() -> str:
    """
    获取周更版本
    """
    p4_config = config.P4_CONFIG_BKCI
    p4 = Perforce(**p4_config)
    p4.login()
    abc = p4.p4.run("dirs", "//x5_mobile/mr/onlineupdate/*/*")
    new_abc = abc[::-1]
    temp = [i.get("dir").split("/")[-1] for i in new_abc]
    new_abc = [{"key": i, "value": i} for i in temp]
    return new_abc[:10]


def __get_branches_info():
    """
    获取分支版本信息
    """
    url = gitlab_host + "api/v4/projects/52/repository/branches?page=1&per_page=1000&sort=updated_desc"
    resp = requests.get(url=url, headers=headers)

    pattern = re.compile(r"^\d+\.\d+.\d+$")
    branches = [{"key": i.get("name"), "value": i.get("name")} for i in resp.json() if re.match(pattern, i.get("name"))]

    def get_value(elem):
        return elem.get("value")

    branches.sort(key=get_value, reverse=True)
    branches.insert(0, {"key": "trunk", "value": "trunk"})
    return branches


def __get_pipeline_params():
    """
    获取流水线参数信息
    """
    url = bk_host + "api/apigw-user/v3/projects/dgm/pipelines/p-1c49b124300c4313ae47ea39646ffb4d"
    resp = requests.get(url=url, headers=bk_headers)
    return resp.json()


def __update_pipeline_params(data: dict):
    """
    更新流水线参数信息
    Args:
        data:
    """
    url = bk_host + "api/apigw-user/v3/projects/dgm/pipelines/p-1c49b124300c4313ae47ea39646ffb4d"
    resp = requests.put(url=url, headers=bk_headers, data=json.dumps(data.get("data")))
    ret = resp.json()
    if ret.get("status") == 0 and ret.get("data") is True:
        return True
    else:
        return False


def __delete_pipeline():
    """
    取消流水线构建
    """
    log.info("准备取消构建...")
    build_id = env.get("BK_CI_BUILD_ID")
    url = bk_host + "api/apigw-user/v3/projects/dgm/pipelines/p-1c49b124300c4313ae47ea39646ffb4d/builds/{}/stop".format(build_id)
    log.info("url: {}".format(url))
    resp = requests.post(url, headers=bk_headers)
    ret = resp.json()
    log.info("ret: {}".format(ret))
    if ret.get("status") == 0 and ret.get("data") is True:
        log.info("取消构建:{} 成功".format(build_id))
        return True
    else:
        return False


def update_pipeline_params(**kwargs):
    """
    更新参数信息
    """
    # 检查磁盘空间是否满足执行流水线
    advance.check_disk_size(threshold=5)

    ret = __get_pipeline_params()
    status = ret.get("status")
    if status == 0:
        params = ret.get("data").get("stages")[0].get("containers")[0].get("params")
        for param in params:
            if param.get("id") == "SOUND_BOUND_VER":
                branches = __get_branches_info()
                param.update({"options": branches, "defaultValue": branches[0].get("value")})
            if param.get("id") == "SOUND_RES_VER":
                week = __get_week_version()
                param.update({"options": week, "defaultValue": ""})
    if __update_pipeline_params(ret):
        # __delete_pipeline()
        exit(0)
    else:
        exit(-1)


def get_commit_id(**kwargs):
    """
    根据pipeline_id获取commit_id信息
    """
    pipeline_id = env.get("PIPELINE_ID")
    if not pipeline_id:
        raise PyframeException("请检查参数PIPELINE_ID填写是否正确")

    # 通过gitlab API 获取pipelines信息，从返回结果中获取commit_id
    url = gitlab_host + "api/v4/projects/52/pipelines/{}".format(pipeline_id)
    try:
        resp = requests.get(url=url, headers=headers)
        pipeline_info = resp.json()
    except Exception as e:
        log.error(e)
        raise PyframeException("获取音效提交信息失败，请检查gitlab代码服务是否正常后，再重试")
    commit_id = pipeline_info.get("sha", "")
    log.info("commit_id: {}".format(commit_id))
    if not commit_id:
        raise PyframeException("获取commit_id信息失败，请联系技术")

    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")

    # 将commit_id 保存到环境变量，方便后续使用
    env.set({"COMMIT_ID": commit_id})


def __is_effect_commit_file(file_path: str) -> bool:
    """
    判断提交的文件是否是音效文件
    Args:
        file_path: 文件路径
    Returns:
        True: 是音效文件
        False: 不是音效文件
    """
    # 判断文件是否是音效文件
    if file_path.startswith("ui_sound_effect/") and (file_path.endswith(".wav") or file_path.endswith(".mp3")):
        return True
    else:
        return False


def __get_commit_file() -> list:
    """
    根据commit_id信息获取提交的文件路径
    Args:

    Returns:

    """
    commit_id = env.get("COMMIT_ID")
    if not commit_id:
        raise PyframeException("请检查环境变量COMMIT_ID是否正常保存")
    url = gitlab_host + "api/v4/projects/52/repository/commits/{}/diff?per_page=200".format(commit_id)
    try:
        resp = requests.get(url=url, headers=headers)
        diff_info = resp.json()
    except Exception as e:
        log.error(e)
        raise PyframeException("获取音效提交信息失败，请检查gitlab代码服务是否正常后，再重试")
    if not diff_info:
        raise PyframeException("获取此次:【{}】提交的文件内容有误，请检查参数PIPELINE_ID填写是否正确".format(commit_id))
    return diff_info


def __save_commit_file(filename: str, content_str: str) -> bool:
    """
    保存二进制内容为文件
    Args:
        filename:
        content_str:

    Returns:

    """
    try:
        # 判断文件filename 父目录是否存在，不存在则创建
        dirname = os.path.dirname(filename)
        if not path_mgr.exists(dirname):
            path_mgr.mkdir(dirname)
        log.info("正在解码...")
        content_bytes = base64.b64decode(content_str)
        log.info("解码成功...")
        log.info("正在写入文件: {}...".format(filename))
        with open(filename, "wb") as f:
            f.write(content_bytes)
        return True
    except Exception as e:
        log.error(e)
        return False


def __clear_old_dir(dirname: str) -> bool:
    """
    下载新的音效文件前，清理旧文件：
        预防文件过多后占用空间
    Args:
        dirname:

    Returns:

    """
    try:
        p = Path(dirname)
        if not p.exists():
            return True
        else:
            shutil.rmtree(p)
            return True
    except Exception as e:
        log.error(e)
        return False


def download_commit_file(**kwargs):
    """

    Args:
        **kwargs:

    Returns:

    """
    # 获取所有需要下载的文件
    file_paths = __get_commit_file()
    if not file_paths:
        raise PyframeException("获取的文件列表为空，请检查参数PIPELINE_ID填写是否正确")

    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")

    # 下载前，如果目录不存在，先进行创建，如果存在，先删除原来旧的文件
    dirname = os.path.join(workspace, "ui_sound_effect")
    if not Path(dirname).exists():
        os.mkdir(dirname)
    else:
        if not __clear_old_dir(dirname):
            raise PyframeException("删除旧音频文件失败，请联系技术")
        os.mkdir(dirname)

    # 记录下的载文件
    download_commit_files = []
    # 下载到指定目录
    for file_path in file_paths:
        new_path = file_path.get("new_path")
        # 判断文件是否是音效文件 如果不是，跳过
        if not __is_effect_commit_file(new_path):
            continue
        new_path = new_path.replace("/", "%2F")
        url = gitlab_host + "api/v4/projects/52/repository/files/{}?ref=audio".format(new_path)
        log.info("url: {}".format(url))
        try:
            resp = requests.get(url=url, headers=headers)
            effect_file = resp.json()
        except Exception as e:
            log.error(e)
            raise PyframeException("下载音效文件失败，请检查gitlab代码服务是否正常后，再重试")

        # 组织文件保存路径
        file_name = effect_file.get("file_name")
        workspace = env.get("WORKSPACE")
        if not workspace:
            raise PyframeException("请检查参数WORKSPACE填写是否正确")
        if not file_name:
            raise PyframeException(f"获取文件失败，请确认文件{new_path}是否存在，或者稍后重试")
        file_path = effect_file.get("file_path")
        filename = os.path.join(workspace, file_path)

        # 获取文件base64 encode之后的字符串并保存成文件
        content = effect_file.get("content")
        if not __save_commit_file(filename, content):
            raise PyframeException("文件: {} 下载失败，请稍后重试".format(file_name))
        download_commit_files.append(filename)

    if not download_commit_files:
        raise PyframeException("没有音效文件需要下载，请检查参数PIPELINE_ID填写是否正确")

    # 将所有下载好的文件保存到环境变量
    env.set({"DOWNLOADED": ",".join(download_commit_files)})

    # 记录下载的文件名称
    download_commit_files = [os.path.basename(f) for f in download_commit_files]
    env_mgr.set_download_files(download_commit_files)


def copy_commit_file(**kwargs):
    """
    将下载好的音效资源拷贝到客户端（arttrunk项目）目录
    """
    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")

    platform = kwargs.get("platform")
    if not platform:
        raise PyframeException("请检查参数platform是否填写正确")

    src_dir = os.path.join(workspace, "ui_sound_effect")

    # 复制前先检查目标目录，如果不存在先创建，如果存在，删除旧内容
    dst_dir = os.path.join(workspace, r"{}\arttrunk\mobile_dancer\arttrunk\client\Assets\resources\audio".format(str(platform).lower()))
    if not __clear_old_dir(dst_dir):
        raise PyframeException("删除旧音频文件失败，请联系技术")

    dst_dir = os.path.join(dst_dir, "ui_sound_effect")
    log.info("src_dir: {} dst_dir: {}".format(src_dir, dst_dir))
    cmd_str = r"xcopy {}\ {}\  /s /e /c /y /h /r /f".format(src_dir, dst_dir)
    ret, output = cmd.run_shell(cmds=[cmd_str], workdir=workspace)
    log.info("ret: {} output: {}".format(ret, output))
    if int(ret) != 0:
        raise PyframeException("复制音频源文件失败")


def __get_p4_view(debug: int = 0) -> dict:
    """
    组织P4的view
    Returns:

    """
    update_type = env.get("TRUNK_BRANCH_WEEKUPDATE")
    log.info("update_type: {}".format(update_type))

    if not update_type:
        raise PyframeException("请检查更新方式参数TRUNK_BRANCH_WEEKUPDATE填写是否正确")
    # 如果是debug模式的话直接将所有内容指定到一个具体的测试目录
    if debug:
        if update_type == UpdateType.TRUNK.value:
            pass
        elif update_type == UpdateType.BRANCH.value:
            branch = env.get("SOUND_BOUND_VER")
            if not branch:
                raise PyframeException("请检查参数SOUND_BOUND_VER是否填写正确")
        elif update_type == UpdateType.WEEK_UPDATE.value:
            branch = env.get("SOUND_BOUND_VER")
            if not branch:
                raise PyframeException("请检查参数SOUND_BOUND_VER是否填写正确")
            week_update = env.get("SOUND_RES_VER")
            if not week_update:
                raise PyframeException("请检查参数SOUND_RES_VER是否填写正确")
        else:
            pass
        view_dict = {
            "android": "//x5_mobile/mr/art_release_test/android/assetbundles/audio/...",
            "ios": "//x5_mobile/mr/art_release_test/ios/assetbundles/audio/...",
        }
        return view_dict

    if update_type == UpdateType.TRUNK.value:
        view_dict = {
            "android": "//x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/android/assetbundles/audio/...",
            "ios": "//x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/ios/assetbundles/audio/...",
        }
    elif update_type == UpdateType.BRANCH.value:
        branch = env.get("SOUND_BOUND_VER")
        if not branch:
            raise PyframeException("请检查参数SOUND_BOUND_VER是否填写正确")
        view_dict = {
            "android": "//x5_mobile/mr/b/{branch}/ResourcePublish/CDN/SourceFiles/android/assetbundles/audio/...".format(branch=branch),
            "ios": "//x5_mobile/mr/b/{branch}/ResourcePublish/CDN/SourceFiles/ios/assetbundles/audio/...".format(branch=branch),
        }
    elif update_type == UpdateType.WEEK_UPDATE.value:
        branch = env.get("SOUND_BOUND_VER")
        if not branch:
            raise PyframeException("请检查参数SOUND_BOUND_VER是否填写正确")
        week_update = env.get("SOUND_RES_VER")
        if not week_update:
            raise PyframeException("请检查参数SOUND_RES_VER是否填写正确")
        view_dict = {
            "android": "//x5_mobile/mr/onlineupdate/{branch}/{week_update}/client/android/assetbundles/audio/...".format(
                branch=branch, week_update=week_update
            ),
            "ios": "//x5_mobile/mr/onlineupdate/{branch}/{week_update}/client/ios/assetbundles/audio/...".format(
                branch=branch, week_update=week_update
            ),
        }
    else:
        raise PyframeException("请检查更新方式参数TRUNK_BRANCH_WEEKUPDATE填写是否正确")
    return view_dict


def update_ab_src(**kwargs):
    """
    从P4上的获取最新的AB资源
    """
    # 拼接P4 CLIENT
    platform = kwargs.get("platform")
    if not platform:
        raise PyframeException("请检查参数platform是否填写正确")

    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")

    server_ip = env.get("SERVER_IP")
    if not server_ip:
        raise PyframeException("请检查参数SERVER_IP填写是否正确")
    p4_client = "bk-devops-music-effect-package-{}-{}".format(server_ip, str(platform).upper())

    # 获取P4 view
    debug = env.get("DEBUG")
    if not debug:
        raise PyframeException("请检查参数DEBUG是否填写正确")
    view_dict = __get_p4_view(int(debug))
    p4_view = view_dict.get(str(platform).lower())

    # 初始化P4对象
    p4_config = config.P4_CONFIG_BKCI
    p4_config.update({"client": p4_client, "p4_view": [p4_view]})
    log.info("p4_config: {}".format(p4_config))
    p4 = Perforce(**p4_config)

    # 创建P4 WORKSPACE并更新
    p4.create_workspace(client=p4_client, view=[p4_view], root=os.path.join(workspace, p4_root), options=["clobber"])
    p4.update_all()


def clean_last_package(**kwargs):
    """
    清理上次打包后的ab包
    """
    platform = kwargs.get("platform")
    if not platform:
        raise PyframeException("请检查参数platform填写是否正确")

    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")
    out_path = os.path.join(workspace, r"{platform}\arttrunk\out\cs\{platform}\assetbundles".format(platform=str(platform).lower()))
    if Path(out_path).exists():
        path_mgr.rm(out_path)


def package_effect_music(**kwargs):
    """
    执行打包动作
    """
    platform = kwargs.get("platform")
    if not platform:
        raise PyframeException("请检查参数platform填写是否正确")

    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")

    unity = "Unity.exe"
    project_path = os.path.join(workspace, r"{}\arttrunk\mobile_dancer\arttrunk\client".format(str(platform).lower()))
    out_path = os.path.join(workspace, r"{platform}\arttrunk\out\cs\{platform}\assetbundles".format(platform=str(platform).lower()))

    # 对平台信息进行特定转换
    platform = str(platform).title() if str(platform).lower() == "android" else "iOS"
    cmd_str = """
"{unity}" -quit -batchmode -logFile package_{platform}.log -projectPath {project_path} -executeMethod H3DBuildTools.BuildAudio \
-buildTarget {platform} out_path={out_path}
    """.format(
        unity=unity, project_path=project_path, out_path=out_path, platform=platform
    )

    # 执行打包命令并查看结果
    ret, output = cmd.run_shell(cmds=[cmd_str], workdir=workspace, return_output=True)
    log.info("ret: {} output: {}".format(ret, output))

    if int(ret) != 0:
        raise PyframeException("打包执行失败:{}，详情请查看日志".format(output))


# 通过读取日志获取打包结果
def __get_package_result(package_log: str):
    """
    通过读取日志获取打包结果
    """
    # 读取日志，从日志中匹配Bundle Name关键字
    with open(package_log, "rb") as f:
        log_content = f.read()
    # 匹配关键字
    pattern = re.compile(rb"Bundle Name: (.*)")
    result = pattern.findall(log_content)
    if not result:
        return []
    result = [l.strip().decode("utf8") for l in result]
    return result


# 检查打包结果
def check_package_result(**kwargs):
    """
    检查打包结果
    """
    # 需要打包的资源
    # AB资源
    # 对比结果
    platform = kwargs.get("platform")
    if not platform:
        raise PyframeException("请检查参数platform填写是否正确")
    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")

    download_files = env_mgr.get_download_files()
    package_log = os.path.join(workspace, r"package_{}.log".format(str(platform).title()))
    if not path_mgr.exists(package_log):
        raise PyframeException("请检查日志文件是否存在:{}".format(package_log))
    package_result = __get_package_result(str(package_log))
    for download_file in download_files:
        download_file = download_file.split(".")[0]
        log.info(f"download_file: {download_file}")
        if download_file.lower() in package_result:
            log.info("文件:{}打包成功".format(download_file))
        else:
            raise PyframeException("未找到文件:{}对应的ab资源，请检查打包日志查看详情".format(download_file))


def upload_package_log(**kwargs):
    """
    # 上传打包日志
    """
    platform = kwargs.get("platform")
    if not platform:
        raise PyframeException("请检查参数platform填写是否正确")
    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")
    package_log = os.path.join(workspace, r"package_{}.log".format(str(platform).title()))
    if platform == "android":
        log_dst = advance.upload_pipeline_log(package_log)
        log.info("上传日志成功:{}".format(log_dst))
        env_mgr.set_android_package_log(log_dst)
    elif platform == "ios":
        log_dst = advance.upload_pipeline_log(package_log)
        log.info("上传日志成功:{}".format(log_dst))
        env_mgr.set_ios_package_log(log_dst)
    else:
        ...


def __copy_effect_music(local_path: str, p4_path: str, debug: int = 0):
    """
    将打包好的ab包拷贝到P4目录
    Args:
        local_path:
        p4_path:
        debug:
    """
    log.info("local_path: {} p4_path: {} debug: {}".format(local_path, p4_path, debug))
    src_path = os.path.join(local_path, r"assets\resources\audio")
    cmd_str = "xcopy {} {}  /s /e /c /y /h /r /f".format(src_path, p4_path)
    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")
    ret, output = cmd.run_shell(cmds=[cmd_str], workdir=workspace)
    if int(ret) != 0:
        raise PyframeException("音效AB资源复制失败:{}，请检查路径及相关文件之后再重试".format(output))


def submit_effect_music(**kwargs):
    """
    提交ab资源到P4目录
    """
    platform = kwargs.get("platform")
    if not platform:
        raise PyframeException("请检查参数platform填写是否正确")

    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")
    out_path = os.path.join(workspace, r"{platform}\arttrunk\out\cs\{platform}\assetbundles".format(platform=str(platform).lower()))

    # 获取P4 view，组织P4本地绝对路径
    debug = env.get("DEBUG")
    if not debug:
        raise PyframeException("请检查参数DEBUG是否填写正确")
    view_dict = __get_p4_view(int(debug))
    p4_view = view_dict.get(str(platform).lower())
    p4_abs = os.path.join(workspace, p4_root, p4_view.replace("//", "").replace("...", "").replace("/", "\\"))
    __copy_effect_music(out_path, p4_abs)

    server_ip = env.get("SERVER_IP")
    if not server_ip:
        raise PyframeException("请检查参数SERVER_IP填写是否正确")
    p4_client = "bk-devops-music-effect-package-{}-{}".format(server_ip, str(platform).upper())

    p4_config = config.P4_CONFIG_BKCI
    p4_config.update({"client": p4_client, "p4_view": [p4_view]})
    log.info("p4_config: {}".format(p4_config))
    p4 = Perforce(**p4_config)
    log.info("p4_view: {}".format(p4_view))
    p4.reconcile(file=p4_view, params=["-a", "-e"])
    try:
        bk_user_name = env.get("BK_CI_START_USER_NAME")
        submit_msg = "蓝盾发起人:{},音效版本:{}".format(bk_user_name, pipeline_id).encode("gbk")
        log.info("submit_msg: {}".format(submit_msg))
        ret = p4.submit(submit_msg)
        log.info("ret: {}".format(ret))
        changelist = __get_changelist(ret)
        env.set({"CHANGELIST_{}".format(platform.upper()): changelist})
    except P4.P4Exception as e:
        if "No files to submit from the default changelist." in e.errors:
            log.info("No files to submit from the default changelist.")
        else:
            raise e


def __get_changelist(changes: list) -> str:
    """
    从提交结果中获取changelist
    Args:
        changes:

    Returns:

    """
    changelist = None
    try:
        for change in changes:
            if not isinstance(change, dict):
                continue
            changelist = change.get("submittedChange")
            if not changelist:
                continue
            else:
                break
    except Exception as e:
        raise PyframeException("获取changelist失败: {}".format(e))
    return changelist


# 消息变量
branch_name = env.get("TRUNK_BRANCH_WEEKUPDATE")
pipeline_id = env.get("PIPELINE_ID")
changelist_android = env.get("CHANGELIST_ANDROID")
changelist_ios = env.get("CHANGELIST_IOS")
android_log = env_mgr.get_android_package_log()
ios_log = env_mgr.get_ios_package_log()

# 消息内容
# content = "**输出版本:** {}\n **音效git版本:** {}\n **Android音效P4提交号:** {}\n **iOS音效P4提交号:** {}\n".format(
#     branch_name, pipeline_id,
#     changelist_android,
#     changelist_ios
# )
content = ""
content += "**输出版本:** {}\n".format(branch_name)
content += "**音效git版本:** {}\n".format(pipeline_id)
content += "**Android音效P4提交号:** {}\n".format(changelist_android)
content += "**iOS音效P4提交号:** {}\n".format(changelist_ios)
if android_log:
    android_log_name = android_log.split("/")[-1]
    content += f"**Android打包日志:** [{android_log_name}]({android_log})\n"
if ios_log:
    ios_log_name = ios_log.split("/")[-1]
    content += f"**iOS打包日志:** [{ios_log_name}]({ios_log})\n"


def do_music_success(**kwargs):
    """
    成功状态要做的事情
    """
    wechat.send_unicast_post_success(content=content)
    advance.insert_pipeline_history_on_success()


def do_music_failure(**kwargs):
    """
    失败状态要做的事情
    """
    wechat.send_unicast_post_failure(content=content)
    advance.insert_pipeline_history_on_failure()


def do_music_canceled(**kwargs):
    """
    取消状态要做的事情
    """
    wechat.send_unicast_post_canceled(content=content)
    advance.insert_pipeline_history_on_canceled()
