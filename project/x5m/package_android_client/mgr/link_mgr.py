# coding=utf-8
import os.path
from frame import *
from project.x5m.package_android_client.config.config import WOR<PERSON><PERSON><PERSON>
from project.x5m.package_android_client.mgr.env_mgr import jenkins_env_mgr


class PackageLinkMgr:
    def __init__(self):
        self.__workdir = WORKDIR
        self.__branch = jenkins_env_mgr.get_branch_name()
        # self.__res_branch = res_branch
        self.__x5mobile_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}")
        self.__x5mconfig_path = os.path.join(self.__workdir, f"x5mconfig/{self.__branch}")
        self.__cur_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk")
        if self.__branch == "master":
            self.__x5mobile_mr_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mr/Resources")
        else:
            self.__x5mobile_mr_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mr/b/{self.__branch}")

    def unlink_all(self):
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, f"mr"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, f"mobile_dancer/trunk/exe/resources/config/server"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, f"mobile_dancer/trunk/exe/resources/config/shared"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, f"mobile_dancer/trunk/exe/resources/config/shared/tencent_xg"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, f"mobile_dancer/trunk/exe/resources/level"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, f"mobile_dancer/trunk/exe/resources/experience"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_mr_path, f"ResourcePublish/CDN/SourceFiles/xml_sources/config"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_mr_path, f"ResourcePublish/CDN/SourceFiles/xml_sources/level/musicact"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_mr_path, f"ResourcePublish/CDN/SourceFiles/xml_sources/level/wedding"))
        path_mgr.rm(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/config"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/level"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/luascript"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/GameLuaProject/luascript"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/audio/bgm"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/audio/sound_effect"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/Assets/StaticResources/art/3d/fitment"))
        path_mgr.unlink(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/Assets/resources/Art/3d/fitment"))

    def make_link_mr(self):
        # path_mgr.rm(path=os.path.join(self.__x5mobile_path, f"mr"))
        path_mgr.soft_link(src=os.path.join(self.__workdir, "p4/x5_mobile/mr"), dst=os.path.join(self.__x5mobile_path, f"mr"))

    def make_link(self):
        path_mgr.mkdir(path=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/exe/resources/config"))
        # 服务器配置映射
        path_mgr.soft_link(
            src=os.path.join(self.__x5mconfig_path, "config/server"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/exe/resources/config/server"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mconfig_path, "config/shared"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/exe/resources/config/shared"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client_build_config/config/shared/tencent_xg"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/exe/resources/config/shared/tencent_xg"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mconfig_path, "level"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/exe/resources/level"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mconfig_path, "experience"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/exe/resources/experience"),
        )

        # 客户端配置映射
        path_mgr.soft_link(
            src=os.path.join(self.__x5mconfig_path, "config"),
            dst=os.path.join(self.__x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/xml_sources/config"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mconfig_path, "level/musicact"),
            dst=os.path.join(self.__x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/xml_sources/level/musicact"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mconfig_path, "level/wedding"),
            dst=os.path.join(self.__x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/xml_sources/level/wedding"),
        )

        # 客户端资源映射
        # path_mgr.soft_link(
        #     src=os.path.join(self.__x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/windows/assetbundles"),
        #     dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/editorCdn/assetbundles")
        # )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/android/assetbundles"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/crossplatform/config"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/config"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/crossplatform/level"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/level"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/crossplatform/luascript"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/luascript"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/crossplatform/luascript"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/GameLuaProject/luascript"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__workdir, f"x5mconfig/audio/bgm"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/audio/bgm"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mobile_mr_path, "ResourcePublish/CDN/SourceFiles/crossplatform/audio/sound_effect"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/assetbundles/cdn/assetbundles/audio/sound_effect"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mobile_mr_path, "art_src/sta/3d/fitment"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/Assets/StaticResources/art/3d/fitment"),
        )
        path_mgr.soft_link(
            src=os.path.join(self.__x5mobile_mr_path, "art_src/3d/fitment"),
            dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/Assets/resources/Art/3d/fitment"),
        )
        # path_mgr.soft_link(
        #     src=os.path.join(self.__x5mobile_mr_path, "Editor/config"),
        #     dst=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/client/Assets/engine/Editor/PlotEditor/Plot/config")
        # )

        # TSS脏词本地检测用
        path_mgr.mkdir(path="C:/temp/tsssdk_cfg")
        path_mgr.copy(src=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/tools/tss_local_cfg/tss_sdk_conf.xml"), dst="C:/temp/tsssdk_cfg/")
        path_mgr.copy(src=os.path.join(self.__x5mobile_path, "mobile_dancer/trunk/tools/tss_local_cfg/vocabulary.txt"), dst="C:/temp/tsssdk_cfg/")

    def campaign_link(self):
        # 清理目录
        path_mgr.rm(path=f"{self.__cur_path}/client/Assets/StaticResources/art/UIAtlas/NewUI/activity")
        path_mgr.mkdir(path=f"{self.__cur_path}/client/Assets/StaticResources/art/UIAtlas/NewUI/activity")

        path_mgr.rm(path=f"{self.__cur_path}/client/Assets/resources/Art/UIPrefabs/NewUI/Activity/New")
        path_mgr.mkdir(path=f"{self.__cur_path}/client/Assets/resources/Art/UIPrefabs/NewUI/Activity/New")

        path_mgr.rm(path=f"{self.__cur_path}/client/Assets/Scripts/UI/NewUI/Activity")
        path_mgr.mkdir(path=f"{self.__cur_path}/client/Assets/Scripts/UI/NewUI/Activity")

        # 软链接
        with open(f"{self.__cur_path}/client/assetbundles/cdn/assetbundles/config/campaigninpack.xml", "r", encoding="UTF-8") as f:
            all_lines_in_file = f.readlines()
            for index in all_lines_in_file:
                # log.info(index)
                if os.path.exists(path=f"{self.__cur_path}/client_campaign/Assets/StaticResources/art/UIAtlas/NewUI/activity/{index}".strip()):
                    path_mgr.soft_link(
                        src=f"{self.__cur_path}/client_campaign/Assets/StaticResources/art/UIAtlas/NewUI/activity/{index}".strip(),
                        dst=f"{self.__cur_path}/client/Assets/StaticResources/art/UIAtlas/NewUI/activity/{index}".strip(),
                    )
                if os.path.exists(path=f"{self.__cur_path}/client_campaign/Assets/resources/Art/UIPrefabs/NewUI/Activity/New/{index}".strip()):
                    path_mgr.soft_link(
                        src=f"{self.__cur_path}/client_campaign/Assets/resources/Art/UIPrefabs/NewUI/Activity/New/{index}".strip(),
                        dst=f"{self.__cur_path}/client/Assets/resources/Art/UIPrefabs/NewUI/Activity/New/{index}".strip(),
                    )

                if os.path.exists(path=f"{self.__cur_path}/client_campaign/Assets/Scripts/UI/NewUI/Activity/{index}".strip()):
                    path_mgr.soft_link(
                        src=f"{self.__cur_path}/client_campaign/Assets/Scripts/UI/NewUI/Activity/{index}".strip(),
                        dst=f"{self.__cur_path}/client/Assets/Scripts/UI/NewUI/Activity/{index}".strip(),
                    )
