# coding=utf-8

from frame import *
from project.x5m import config
from project.x5m.package_android_client.config.config import WORKDIR
from project.x5m.package_android_client.mgr.env_mgr import env_mgr, jenkins_env_mgr


class NexusMgr:
    def __init__(self):
        self.__workdir = WORKDIR
        self.__branch = jenkins_env_mgr.get_branch_name()
        self.__bit = "32"
        self.__package_type = jenkins_env_mgr.get_pack_type()
        self.__fake_p4_changelist = env_mgr.get_fake_p4_changelist()
        self.__app_version = jenkins_env_mgr.get_app_version()
        self.__build_num = env.pipeline.build_num()
        self.__commit_id = env_mgr.get_commit_id()
        self.__pipeline_id = env_mgr.get_pipeline_id()
        self.__silent_version = env_mgr.get_silent_version()
        self.__p4_changelist = env_mgr.get_p4_changelist()
        # 测试url
        self.__url = "http://nexus.h3d.com.cn/repository/dev-productivity/package_android_client"
        # 正式url
        # self.__url = "http://nexus.h3d.com.cn/repository/mobile/android"
        self.__nexus = Nexus(**config.NEXUS_CONFIG_DEVPRO)

    # def upload_artifact(self, bit: str):
    #     self.__bit = bit
    #     artifacts_path = os.path.join(self.__workdir, f"x5mobile/{self.__branch}/mobile_dancer/trunk/client/h3dbuild")
    #     artifact_name = f"qqx5_silent_{self.__silent_version}_{self.__package_type}_{self.__app_version}_{self.__fake_p4_changelist}_res.apk"
    #     artifact_name_new = f"qqx5_{self.__bit}_silent_{self.__silent_version}_{self.__package_type}_{self.__app_version}_{self.__fake_p4_changelist}_{self.__build_num}_git_{self.__commit_id}_{self.__pipeline_id}_res.apk"
    #     artifact_url = f"{self.__url}/{self.__branch}/{self.__package_type}/{artifact_name_new}"
    #
    #     log.info(f"upload_url: {artifact_url}")
    #
    #     # 改名
    #     path_mgr.copy(
    #         src=os.path.join(artifacts_path, artifact_name),
    #         dst=os.path.join(artifacts_path, artifact_name_new)
    #     )
    #
    #     # 计算包体大小
    #     artifacts_size = self.__cal_file_size(file_path=os.path.join(artifacts_path, artifact_name_new))
    #     env_mgr.set_apk_size(apk_size=artifacts_size, bit=self.__bit)
    #
    #     # 上传apk文件
    #     self.__nexus.upload(
    #         src=os.path.join(artifacts_path, artifact_name_new),
    #         dst=artifact_url
    #     )
    #
    #     env.set({f"{self.__bit}bit_artifact_url": artifact_url})
    #
    #     # 上传libil2cpp.so文件
    #     self.__upload_libil2cpp(url=artifact_url, library_path=f"{self.__workdir}/x5mobile/{self.__branch}/mobile_dancer/trunk/client/Library")
    #
    # def __cal_file_size(self, file_path) -> str:
    #     return str(round(os.path.getsize(file_path) / 1024 / 1024, 2))
    #
    # def __upload_libil2cpp(self, url: str, library_path: str):
    #     il2cpp_android_path = self.__search_path(path=library_path, pattern="il2cpp_android*")
    #     libcpp_find_path = self.__search_path(path=f"{il2cpp_android_path}/il2cpp_cache", pattern="linkresult*")
    #     libcpp_file_name = "libil2cpp.so"
    #     libcpp_paths = self.__get_file_paths(path=libcpp_find_path, file_name=libcpp_file_name)
    #     log.info(libcpp_paths)
    #     libcpp_path = libcpp_paths[0]
    #     compress_file_path = os.path.splitext(libcpp_path)[0] + ".zip"
    #     tar.compress(
    #         src=libcpp_path,
    #         dst=compress_file_path
    #     )
    #     upload_url = os.path.splitext(url)[0] + "/" + os.path.split(compress_file_path)[-1]
    #     self.__nexus.upload(
    #         src=compress_file_path,
    #         dst=upload_url
    #     )
    #
    #     env.set({f"{self.__bit}bit_libil2cpp_url": upload_url})
    #
    # def __search_path(self, path: str, pattern: str) -> str:
    #     for x in os.listdir(path):
    #         if fnmatch(x, pattern) and path_mgr.is_dir(f"{path}/{x}"):
    #             return f"{path}/{x}"
    #
    # def __get_file_paths(self, path: str, file_name: str) -> list:
    #     paths = []
    #     for root, _, files in os.walk(path):
    #         for file in files:
    #             if fnmatch(file, file_name):
    #                 paths.append(root + "\\" + file)
    #     return paths
