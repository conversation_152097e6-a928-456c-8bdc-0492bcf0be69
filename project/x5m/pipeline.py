import click
import logging
import importlib
import json
from operator import methodcaller

# 兼容frame， 将repo路径加到PYTHONPATH中
import os
import sys
from uuid import uuid4

logging.basicConfig(level=logging.INFO)


X5M_PATH = os.path.dirname(os.path.abspath(__file__))
REPO_PATH = os.path.dirname(os.path.dirname(X5M_PATH))
if REPO_PATH not in sys.path:
    logging.info(f"repo_path: {REPO_PATH}")
    sys.path.append(REPO_PATH)


class JsonType(click.ParamType):
    name = "json"

    def convert(self, value, param, ctx):
        try:
            # 尝试解析 JSON 字符串
            return json.loads(value)
        except json.JSONDecodeError:
            # 如果解析失败，提供错误信息
            self.fail(f"invalid JSON: {value}", param, ctx)


@click.command()
@click.option("--job", default="", help="步骤")
@click.option("--kwargs", type=JsonType(), default="{}", help="额外的参数， json string")
def pipeline(job: str, kwargs: dict = None):
    """
    x5m jenkins pipeline 入口函数
    流水线地址：http://jenkins-x5mobile.h3d.com.cn/job/git_auto_merge/

    示例：
        python3 pipeline.py --job=i18n_checker.determine_data_source --extra='{"repo_name": "x5mweek"}'
    """
    try:
        logging.info(f"pipeline start with argv: {job}, {kwargs}")
        if ":" in job:
            _module, func_name = job.rsplit(":", maxsplit=1)
        else:
            _module, func_name = job.rsplit(".", maxsplit=1)
        module = importlib.import_module(_module)
        if "." in func_name:
            cls_name, func_name = func_name.split(".", maxsplit=1)
            CLS = getattr(module, cls_name)
            result = methodcaller(func_name, **kwargs)(CLS())
        else:
            result = methodcaller(func_name, **kwargs)(module)
        print(json.dumps([0, result], ensure_ascii=False))
    except Exception as e:
        logging.error(str(e), exc_info=True)
        print(json.dumps([-1, str(e)], ensure_ascii=False))


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    pipeline()
