import os

from project.x5m.test.deploy_private_server.mgr.env_mgr import bk_env_mgr

class Config:
    envs = dict(os.environ)
    # 获取当前语言
    # 如果为繁中，则添加后缀
    is_trad = bk_env_mgr.is_trad()
    _p4_client_suffix = "_trad" if is_trad else ""
    P4_CONFIG = {
        "P4PORT": "x5_mobile.p4.com:1666",
        "P4USER": "dgm_bkci",
        "P4CLIENT": f"bk_final_release{_p4_client_suffix}",
        "P4PASSWD": "x5m12345"
    }
    
    GIT_URL = {
        "x5mobile": "http://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git",
        "x5mconfig": "http://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig.git",
        "x5mweek": "http://x5mobile-gitlab.h3d.com.cn/dgm/x5mweek.git",
    }
    
    GITLAB_MAINTAINER = {
        "url": "http://x5mobile-gitlab.h3d.com.cn/",
        "username": "<EMAIL>",
        "password": "maintainer123",
        "token": "o-yNgwVADiQ8CicYdt_4",
    }
    
    _git_branch_suffix = "_trad" if is_trad else ""
    # 需要备份的配置仓库和分支
    BACKUP_CONFIG_BRANCH = [
        ("x5mconfig", f"cdn{'-trad' if is_trad else ''}"), 
        ("x5mweek", f"resources{_git_branch_suffix}"), 
        ("x5mweek", f"online{_git_branch_suffix}"), 
        ("x5mweek", f"release{_git_branch_suffix}"),
    ]

config = Config()