class Config:
    P4_CONFIG = {
        "P4PORT": "x5_mobile.p4.com:1666",
        "P4USER": "dgm_bkci",
        "P4CLIENT": "bk_final_release",
        "P4PASSWD": "x5m12345"
    }
    
    GIT_URL = {
        "x5mobile": "http://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git",
        "x5mconfig": "http://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig.git",
        "x5mweek": "http://x5mobile-gitlab.h3d.com.cn/dgm/x5mweek.git",
    }
    
    GITLAB_MAINTAINER = {
        "url": "http://x5mobile-gitlab.h3d.com.cn/",
        "username": "<EMAIL>",
        "password": "maintainer123",
        "token": "o-yNgwVADiQ8CicYdt_4",
    }
    
    # 需要备份的配置仓库和分支
    BACKUP_CONFIG_BRANCH = [
        ("x5mconfig", "cdn"), 
        ("x5mweek", "resources"), 
        ("x5mweek", "online"), 
        ("x5mweek", "release"),
    ]

config = Config()