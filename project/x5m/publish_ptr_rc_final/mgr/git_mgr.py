# coding=utf-8

from frame import *
from project.x5m.publish_ptr_rc_final import config


class X5mGitMgr:
    def __init__(self, workdir: str) -> None:
        self.__workdir = os.path.join(workdir, "git_prj") 

    def __update_git_by_branch(self, group_name: str, branch_name: str):
        project_name=f"{group_name}-{branch_name}"
        git_mgr = GitMgr(workdir=self.__workdir, project_name=project_name)
        try:
            if not Path(os.path.join(self.__workdir, project_name)).exists():
                git_mgr.clone_with_oauth2(
                    url=config.GIT_URL.get(group_name),
                    branch=branch_name,
                    oauth2=config.GITLAB_MAINTAINER.get("token"),
                )
            else:
                git_mgr.reset()
                git_mgr.clean()
                git_mgr.advance_pull(branch=branch_name)
        except Exception as e:
            log.info(f"Download or Update '{group_name}/{branch_name}' error, Clear the folder and try again, error msg: {e}")
            path_mgr.rm(os.path.join(self.__workdir, project_name))
            git_mgr.clone_with_oauth2(
                url=config.GIT_URL.get(group_name),
                branch=branch_name,
                oauth2=config.GITLAB_MAINTAINER.get("token"),
            )
    
    def update_config(self) -> None:
        for group, branch in config.BACKUP_CONFIG_BRANCH:
            self.__update_git_by_branch(group, branch)