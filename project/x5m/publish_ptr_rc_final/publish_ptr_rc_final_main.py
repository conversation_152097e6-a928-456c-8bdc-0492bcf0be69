from frame import *
from project.x5m import config

from project.x5m.publish_ptr_rc_final.mgr.git_mgr import X5mGitMgr
from project.x5m.publish_ptr_rc_final.mgr.p4_mgr import P4COM1666
from project.x5m.publish_ptr_rc_final.mgr.config_backup_mgr import ConfigBackupMgr

def upload_app(**kwargs: dict):
    """
    上传app到制品库
    """
    version_num = env.get("VERSION_NUM")
    if not version_num:
        raise PyframeException("请检查参数VERSION_NUM填写是否正确")
    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")

    SRC_PATH_DEBUG = os.path.join(workspace, "installation_package", "package_test", version_num)
    SRC_PATH = os.path.join(workspace, "installation_package", "package", version_num)
    debug = env.get("DEBUG")
    app_path = SRC_PATH_DEBUG if int(debug) else SRC_PATH
    nexus = Nexus(**config.NEXUS_CONFIG_DATA)
    files = Path(app_path).rglob("*.*")
    base_path = "http://nexus.h3d.com.cn/repository/mobile-date/x5m/app"
    dst_path = base_path + "/test" if int(debug) else base_path
    for file in files:
        file_name = "{}/{}{}".format(dst_path, version_num, str(file.absolute()).replace(app_path, "").replace("\\", "/"))
        log.info(file_name.lower())
        nexus.upload(str(file.absolute()), file_name.lower())


def download_dgm_server(dgm_server: str, workspace: str):
    """
    下载并解压dgm_server压缩包
    Args:
        dgm_server:
        workspace:

    Returns:

    """
    # 从FTP下载dgm_server压缩包
    ftp = FtpMgr(**config.FTP_CONFIG_150_30)
    src = os.path.join("/version_test/server_pack/branch/", dgm_server)
    ptr_rc_final = os.path.join(workspace, "ptr_rc_final")
    # 下载前先删除本地的dgm_server压缩包
    if path_mgr.exists(ptr_rc_final):
        path_mgr.rm(ptr_rc_final)

    # 下载dgm_server压缩包
    dst = os.path.join(ptr_rc_final, dgm_server)
    ftp.download_file(src, dst)

    # 解压dgm_server压缩包
    tar.decompress(dst, ptr_rc_final)


# 更新服务器配置文件
def update_server_config(**kwargs: dict):
    """

    Args:
        **kwargs:

    Returns:

    """
    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")
    max_pipeline_id = env.get("MAX_PIPELINE_ID")
    if not max_pipeline_id:
        raise PyframeException("请检查参数max_pipeline_id填写是否正确")
    dgm_server = f"dgm_server_{max_pipeline_id}.zip"
    # 下载并解压dgm_server压缩包
    download_dgm_server(dgm_server, workspace)
    git_prj = os.path.join(workspace, "git_prj", "x5mconfig", "config", "server", "h3d")
    # 更新配置文件
    configs = [
        "version_control_ios_app.xml",
        "version_control_ios_lite.xml",
        "version_control_ios_res.xml",
        "version_control_ios_silent.xml",
        "cdn_config.csv",
    ]

    server_config_path = os.path.join(workspace, "ptr_rc_final/resources/config/server/h3d")
    log.info(f"server_config_path: {server_config_path}")
    for c in configs:
        src = os.path.join(server_config_path, c)
        dst = os.path.join(git_prj, c)
        path_mgr.copy(src, dst, overwrite=True)


def update_config(**kwargs: dict):
    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")
    x5m_git_mgr = X5mGitMgr(workspace)
    x5m_git_mgr.update_config()
    
    
def update_p4(**kwargs: dict):
    workspace = env.get("WORKSPACE")
    branch = env.get("BRANCH_NAME")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")
    if not branch:
        raise PyframeException("请检查参数BRANCHNAME填写是否正确")
    p4_mgr = P4COM1666(workspace, branch)
    p4_mgr.revert()
    p4_mgr.sync_all(True)


def backup_config(**kwargs: dict):
    workspace = env.get("WORKSPACE")
    if not workspace:
        raise PyframeException("请检查参数WORKSPACE填写是否正确")
    branch = env.get("BRANCH_NAME")
    if not branch:
        raise PyframeException("请检查参数BRANCHNAME填写是否正确")
    config_backup_mgr = ConfigBackupMgr(workspace, branch)
    config_backup_mgr.backup_config()
    p4_mgr = P4COM1666(workspace, branch)
    p4_mgr.submit_backup_config()


def __make_msg(msg_type: str) -> str:
    """
    组织不同阶段的消息内容
    Args:
        msg_type:
    """
    version_type = env.get("VERSION_TYPE")
    version_num = env.get("VERSION_NUM")
    silent_android = env.get("SILENT_ANDROID")
    silent_ios = env.get("SILENT_IOS")
    compile_server_ip = env.get("COMPILE_SERVER_IP")
    deploy_server_ip = env.get("DEPLOY_SERVER_IP")
    base_msg = f"**当前版本号:** {version_num}\n" f"**silent_android:** {silent_android}\n" f"**silent_ios:** {silent_ios}"
    if msg_type == "msg":
        return "\n" f"**{version_type}版本发布，所有部分均已发布完毕**\n" f"{base_msg}\n" f"**编译机IP:** {compile_server_ip}\n" f"**部署机IP:** {deploy_server_ip}"
    elif msg_type == "pre_msg":
        return (
            "\n"
            f"**{version_type}版本发布，除silent外文件已发布完毕，silent任务开始执行**\n"
            f"{base_msg}\n"
            f"**编译机IP:** {compile_server_ip}\n"
            f"**部署机IP:** {deploy_server_ip}"
        )
    elif msg_type == "begin":
        return "\n" f"**{version_type}版本发布开始执行**\n" f"{base_msg}"
    else:
        return ""


def on_begin_success(**kwargs):
    """
    开始时通知
    """
    wechat.send_unicast_on_start(user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>"], content=__make_msg("begin"))


def on_pre_success(**kwargs):
    """
    部分成功时通知
    """
    wechat.send_unicast_post_success(user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>"], content=__make_msg("pre_msg"))


def on_success(**kwargs):
    """
    全部成功时通知
    """
    wechat.send_unicast_post_success(user_list=["<EMAIL>", "<EMAIL>", "<EMAIL>"], content=__make_msg("msg"))
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    """
    失败时通知
    """
    wechat.send_unicast_post_failure()
    advance.insert_pipeline_history_on_failure()


def on_unstable(**kwargs):
    """
    取消时通知
    """
    wechat.send_unicast_post_unstable()
    advance.insert_pipeline_history_on_unstable()
