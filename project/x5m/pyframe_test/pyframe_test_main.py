# coding=utf-8
from frame import log
from frame import wechat, env
from frame import advance

webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3d8aa5e5-3de3-4ff4-b205-13810641bd99"


def do_first_job(**kwargs):
    log.info("do_first_job start")
    # 使用之前先提取所需参数
    param1, param2 = kwargs["param1"], kwargs["param1"]
    log.info("param1: {}, param2: {}".format(param1, param2))
    envs = env.get("BK_CI_PIPELINE_NAME")
    log.info("BK_CI_PIPELINE_NAME is {}".format(envs))


def do_second_job(**kwargs):
    log.info("do_second_job start")
    # 使用之前先提取所需参数
    param1, param2 = kwargs["param1"], kwargs["param1"]
    log.info(kwargs)
    log.info("param1: {}, param2: {}".format(param1, param2))
    envs = env.get_all()
    log.info("get_all() is {}".format(envs))
    env.set({"HHH": "hhhhhhh"})
    envs = env.get("HHH")
    log.info(envs)
    env.set({"HHH": "HHHHHHHH"})
    envs = env.get("HHH")
    log.info(envs)


def post_success(**kwargs):
    log.info(kwargs)
    wechat.send_unicast_post_success(user_list=[])
    advance.insert_pipeline_history_on_success()


def post_failure(**kwargs):
    log.info(kwargs)
    wechat.send_multicast_post_failure(webhook=webhook)
    advance.insert_pipeline_history_on_failure()


def post_canceled(**kwargs):
    log.info(kwargs)
    wechat.send_multicast_post_canceled(webhook=webhook)
    advance.insert_pipeline_history_on_canceled()
