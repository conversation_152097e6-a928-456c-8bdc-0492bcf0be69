import os
import shutil
import stat
from pathlib import Path

import P4

from frame import FtpMgr
from frame import Perforce
from frame import advance
from frame import env
from frame import log
from frame import wechat, PyframeException
from project.x5m import config

# 调试模式下使用不同目录
DST_PATH = r"D:\White_box_tools\file_services\version_test\silent_res"
DST_PATH_DEBUG = r"D:\White_box_tools\file_services\version_test\silent_test"


def download_silent_file(**kwargs: dict):
    """
    从远程FTP下载静默资源
    Args:
        **kwargs:
    """
    # 检查磁盘空间是否满足执行流水线
    advance.check_disk_size(threshold=5)

    log.info("准备开始下载静默资源")

    # 获取远程资源平台及版本信息
    platform = kwargs.get("platform")
    debug = kwargs.get("debug", 0)

    # 调试模式下使用不同目录
    dst_path = DST_PATH_DEBUG if int(debug) else DST_PATH

    silent_android = env.get("SILENT_ANDROID")
    silent_ios = env.get("SILENT_IOS")
    try:
        platform = str(platform).lower()
    except Exception as e:
        raise PyframeException(f"平台参数有误，请检查。错误信息: {e}")

    ftp = FtpMgr(**config.FTP_CONFIG_150_30)

    # 分别下载两个平台的静默资源
    if platform == "android":
        remote_path = "/version_test/silent_res/android/{}/".format(silent_android)
        dst_path = os.path.join(dst_path, "version_test", "silent_res", platform, silent_android)
        ftp.download_folder(remote_path, dst_path)
    elif platform == "ios":
        remote_path = "/version_test/silent_res/ios/{}/".format(silent_ios)
        dst_path = os.path.join(dst_path, "version_test", "silent_res", platform, silent_ios)
        ftp.download_folder(remote_path, dst_path)
    else:
        raise PyframeException("平台参数错误，请确认参数是否为Android或者iOS")


def init_p4(platform: str, debug: int = 0):
    """
    初始化P4客户端
    Args:
        platform:
        debug:
    """
    workspace = env.get("WORKSPACE")
    server_ip = env.get("SERVER_IP")
    p4_root = "silent_workspace"
    p4_client = "bk-devops-silent-distribution-{}-{}".format(server_ip, platform.upper())

    # 调试模式下使用不同的目录映射
    view_dict = {
        "P4_VIEW_ANDROID": "//x5m/res/silent/android/...",
        "P4_VIEW_ANDROID_DEBUG": "//x5_mobile/mr/art_release_test/x5m/res/silent/android/...",
        "P4_VIEW_IOS": "//x5m/res/silent/ios/...",
        "P4_VIEW_IOS_DEBUG": "//x5_mobile/mr/art_release_test/x5m/res/silent/ios/...",
    }
    p4_view = view_dict.get("P4_VIEW_{}_DEBUG".format(platform.upper())) if int(debug) else view_dict.get("P4_VIEW_{}".format(platform.upper()))
    p4_abs = os.path.join(workspace, p4_root, p4_view.replace("//", "").replace("...", "").replace("/", "\\"))

    config.P4_CONFIG_BKCI.update({"client": p4_client, "p4_view": [p4_view]})
    log.info("p4_config: {}".format(config.P4_CONFIG_BKCI))
    p4 = Perforce(**config.P4_CONFIG_BKCI)
    p4.create_workspace(client=p4_client, view=[p4_view], root=os.path.join(workspace, p4_root), options=["clobber"])
    return p4, p4_abs, p4_view


def __submit_silent_file(platform: str, debug: int = 0):
    """
    上传P4目录下的静默资源
    Args:
        platform:
    """
    p4, p4_abs, p4_view = init_p4(platform, debug)
    p4.update_all()

    # 复制静默资源到P4路径
    need_submit = copy_silent_file(platform, p4_abs, debug)

    version_type = env.get("VERSION_TYPE")
    if str(version_type).lower() != "final":
        log.info("发版类型为: {} 不需要提交P4".format(version_type))
        return

    log.info("需要被提交的文件: {}".format(need_submit))
    if need_submit:
        reconcile_file = p4.reconcile(file=p4_view, params=["-a", "-e"])
        log.info("reconcile_file: {}".format(reconcile_file))
        try:
            msg = " Release-Tool silent: {}".format(platform)
            p4.submit(msg)
        except P4.P4Exception as e:
            if "No files to submit from the default changelist." in e.errors:
                log.error("没有需要提交的文件，此内容请忽略。")
            else:
                raise e
    else:
        log.info("需要提交的内容列表为空，无需提交。")


def submit_silent_file(**kwargs):
    """
    上传P4目录下的静默资源
    """
    log.info("开始准备上传静默资源到P4")
    platform = kwargs.get("platform")
    debug = kwargs.get("debug", 0)

    # 获取平台参数信息
    if platform:
        platform = str(platform).lower()
    else:
        raise PyframeException("缺少平台参数，请确认参数是否为Android或者iOS")

    # 复制到P4目录并上传
    if platform == "android" or platform == "ios":
        __submit_silent_file(platform, debug)
    else:
        raise PyframeException("平台参数错误，请确认参数是否为Android或者iOS")


def copy_silent_file(platform: str, p4_path: str, debug: int = 0) -> list:
    """
    将下载好的静默资源拷贝到P4目录
    Args:
        platform:
        p4_path:
        debug:
    """
    log.info("platform: {}， p4_path: {}".format(platform, p4_path))

    # 获取静默资源下载路径
    silent_version = env.get("SILENT_{}".format(platform.upper()))
    remote_path = "/version_test/silent_res/{}/{}".format(platform.lower(), silent_version)

    dst_path = DST_PATH_DEBUG if int(debug) else DST_PATH

    # 原目录下面如果已经包含其他版本的静默资源，先删除
    p = Path(p4_path)
    if p.exists() and os.listdir(p):
        shutil.rmtree(p, onerror=__readonly_handler)
        # path_mgr.rm(p)

    # 创建此次需要的静默资源目录
    p4_path = os.path.join(p4_path, silent_version)
    if not Path(p4_path).exists():
        os.makedirs(p4_path)

    # 复制所有文件，并返回复制列表
    need_submit = []
    dst = dst_path + remote_path
    for file in Path(dst).glob("*.*"):
        try:
            shutil.copy(file, p4_path)
        except PermissionError:
            os.chmod(os.path.join(p4_path, file.name), stat.S_IWRITE)
            shutil.copy(file, p4_path)
        log.info("文件: {} 已经复制到: {}".format(file, p4_path))
        need_submit.append(os.path.join(p4_path, file.name))
    return need_submit


def __readonly_handler(func, path, exc_info):
    """
    修改文件夹属性
    Args:
        func:
        path:
        exc_info:
    """
    os.chmod(path, stat.S_IWRITE)
    func(path)


def do_silent_success(**kwargs):
    """
    成功状态要做的事情
    """
    wechat.send_unicast_post_success(user_list=["<EMAIL>", "<EMAIL>"])
    advance.insert_pipeline_history_on_success()


def do_silent_failure(**kwargs):
    """
    失败状态要做的事情
    """
    wechat.send_unicast_post_failure(user_list=["<EMAIL>", "<EMAIL>"])
    advance.insert_pipeline_history_on_failure()


def do_silent_canceled(**kwargs):
    """
    取消状态要做的事情
    """
    wechat.send_unicast_post_failure(user_list=["<EMAIL>", "<EMAIL>"])
    advance.insert_pipeline_history_on_canceled()
