from frame import FtpMgr, common, env, log, os
from project.x5m import config
from project.x5m import LanguageEnum
from project.x5m.silent_package.mgr.env_mgr import env_mgr, jenkins_env_mgr


class SilentFTPMgr:
    def __init__(self):
        self.__workdir = env.pipeline.workspace()
        self.__res_branch = env_mgr.get_res_branch()
        self.__platform = env_mgr.get_platform()
        self.__release_version = env_mgr.get_release_version()
        lang = jenkins_env_mgr.get_language()
        if lang == LanguageEnum.chin_simp.value:
            self.ftp = FtpMgr(
                ip=config.FTP_CONFIG_150_30["ip"],
                port=config.FTP_CONFIG_150_30["port"],
                username=config.FTP_CONFIG_150_30["username"],
                password=config.FTP_CONFIG_150_30["password"],
            )
        elif lang == LanguageEnum.chin_trad.value:
            # TODO: 确认 ftp config
            pass

    def upload(self):
        remote_path = os.path.join(f"/version_test/silent_res/{self.__platform}", self.__release_version).replace("\\", "/")
        # remote_path = os.path.join(f'/version_test/silent_test/{self.__platform}', self.__release_version).replace("\\", "/")
        local_path = os.path.join(
            self.__workdir,
            f"x5mobile/mr/{self.__res_branch}/ResourcePublish/CDN/SourceFiles/{self.__platform}/{self.__release_version}/silent",
        )
        local_files = os.listdir(local_path)
        for file in local_files:
            log.info(file)
            self.ftp.upload_file(
                src=os.path.join(local_path, file).replace("\\", "/"),
                dst=f"{remote_path}",
            )
        env_mgr.set_upload_ftp_url(remote_path="https://dgmdd.h3d.com.cn/dgm" + remote_path)

        config_path = os.path.join(
            self.__workdir,
            f"x5mobile/mobile_dancer/tools/LitePackToolEx/exe/silent/{self.__release_version}.zip",
        )
        config_remote_path = f"/version_test/silent_config/{self.__platform}"
        # config_remote_path = f'/version_test/silent_test/{self.__platform}'
        self.ftp.upload_file(src=config_path, dst=config_remote_path)
        config_url = common.join_url(
            "https://dgmdd.h3d.com.cn/dgm",
            config_remote_path,
            f"{self.__release_version}.zip",
        )
        env_mgr.set_upload_config_url(url=config_url)
