# coding=utf-8
from typing import Tuple
import re
from fnmatch import fnmatch
from frame import *
from project.x5m import config
from project.x5m.silent_package.mgr.env_mgr import env_mgr, jenkins_env_mgr


class SilentP4Mgr:
    def __init__(self):
        branch = jenkins_env_mgr.get_branch()
        self.__platform = env_mgr.get_platform()
        self.__p4_workspace = f"jenkins-silent-{self.__platform}-{branch}-{common.get_host_ip()}"
        log.info(self.__p4_workspace)
        self.__p4_res_branch = env_mgr.get_res_branch()
        self.__root = env.pipeline.workspace()
        self.__client = self.__p4_workspace
        self.p4 = P4Client(
            host=config.P4_CONFIG_JENKINS.get("host"),
            username=config.P4_CONFIG_JENKINS.get("username"),
            password=config.P4_CONFIG_JENKINS.get("password"),
            client=self.__client,
            charset=P4Client.Charset.CP936
        )
        self.ftp = FtpMgr(
            ip=config.FTP_CONFIG_150_30["ip"],
            port=config.FTP_CONFIG_150_30["port"],
            username=config.FTP_CONFIG_150_30["username"],
            password=config.FTP_CONFIG_150_30["password"],
        )
        self.__base_version, _ = self.__get_base_and_release_version()
        env_mgr.set_base_version(base_version=self.__base_version)
        # env_mgr.set_release_version(release_version=self.__release_version)

        views = [
            f"//x5mplan/dev_bak/LitePackToolEx/exe/... //{self.__client}/x5mobile/mobile_dancer/tools/LitePackToolEx/exe/...",
            f"//x5_mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/pack/lite/Lite_update_white_list.csv //{self.__client}/x5mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/pack/lite/Lite_update_white_list.csv",
            f"//x5_mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/pack/lite/Lite_white_list.csv //{self.__client}/x5mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/pack/lite/Lite_white_list.csv",
            f"//x5_mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/pack/silent/... //{self.__client}/x5mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/pack/silent/...",
            f"//x5_mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/{self.__platform}/... //{self.__client}/x5mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/{self.__platform}/...",
            f"//x5_mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/crossplatform/... //{self.__client}/x5mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/crossplatform/...",
            f"//x5m/res/silent/{self.__platform}/{self.__base_version}/... //{self.__client}/x5mobile/mr/{self.__p4_res_branch}/ResourcePublish/CDN/SourceFiles/{self.__platform}/silent/{self.__base_version}/...",
        ]
        self.p4.set_view(views=views)
        self.p4.set_root(path=self.__root)
        self.p4.set_options(allwrite=True, clobber=True, rmdir=True)

    def get_latest_changelist(self) -> str:
        max_changelist = self.p4.get_current_workspace_latest_changes()
        return max_changelist.get("change")

    def sync_all(self, force: bool):
        self.p4.sync_all(force=force)

    def __get_base_and_release_version(self) -> Tuple[str, str]:
        silent_path = f"//x5m/res/silent/{self.__platform}/*"
        subdirs = self.p4.dirs(path=silent_path)
        base_version = self.__get_largest_version(paths=subdirs)
        top_twice_base_version = base_version.split(".")[:-2]
        if int(base_version.split(".")[-2]) < 9:
            top_twice_base_version.append("0" + str(int(base_version.split(".")[-2]) + 1))
        else:
            top_twice_base_version.append(str(int(base_version.split(".")[-2]) + 1))

        # 跳版本临时处理
        log.info(f"top_twice_base_version: {top_twice_base_version}")
        if top_twice_base_version[0] == "5" and top_twice_base_version[1] == "14":
            top_twice_base_version[0] = "6"
            top_twice_base_version[1] = "01"

        http_list = self.ftp.dirs(path=f"version_test/silent_res/{self.__platform}")
        version_list = []
        for version in http_list:
            if version.split(".")[:-1] == top_twice_base_version:
                version_list.append(version)
        log.info(version_list)
        if len(version_list) == 0:
            top_twice_base_version.append("0")
            release_version = ".".join(top_twice_base_version)
        else:
            top_twice_base_version.append(str(self.__get_max_version(version_list=version_list) + 1))
            release_version = ".".join(top_twice_base_version)
        return base_version, release_version

    def __get_largest_version(self, paths: list) -> str:
        dirs = []
        for path in paths:
            path = path.split("/")[-1]
            dirs.append(path)
        versions = []
        for version in dirs:
            if re.match(r"^\d.\d+.\d+$", version):
                versions.append(re.match(r"^\d.\d+.\d+$", version).group())
        versions.sort()
        branch_num = versions[-1]
        return branch_num

    def __get_max_version(self, version_list: list) -> int:
        version_nums = []
        for num in version_list:
            num = num.split(".")[-1]
            version_nums.append(int(num))

        return max(version_nums)

    def clean_tmp(self):
        for root, _, files in os.walk(self.__root):
            for file in files:
                if fnmatch(file, "p4j*.tmp"):
                    path_mgr.rm(os.path.join(root, file))
