# coding=utf-8
import re

from frame import *
from project.x5m.silent_package.mgr.env_mgr import env_mgr, jenkins_env_mgr, global_env_mgr
from project.x5m.silent_package.mgr.git_mgr import SilentGitMgr


class SilentPackageMgr:
    def __init__(self):
        self.__workdir = env.pipeline.workspace()
        self.__branch = jenkins_env_mgr.get_branch()
        self.__build_num = env.pipeline.build_num()
        self.__res_branch = env_mgr.get_res_branch()
        self.__base_version = env_mgr.get_base_version()
        self.__release_version = self.__get_release_version()
        self.__auto_redundant_silent = jenkins_env_mgr.get_auto_redundant_silent()
        self.__platform = env_mgr.get_platform()
        # self.__silent_git_mgr = SilentGitMgr()

    def package(self):
        exe_path = os.path.join(self.__workdir, f"x5mobile\\mobile_dancer\\tools\\LitePackToolEx\\exe")
        ab_path = os.path.join(self.__workdir, f"x5mobile\\mr\\{self.__res_branch}\\ResourcePublish\\CDN\\SourceFiles")
        white_path = os.path.join(
            self.__workdir, f"x5mobile\\mr\\{self.__res_branch}\\ResourcePublish\\CDN\\SourceFiles\\pack\\lite\\Lite_white_list.csv"
        )
        screen_video_config = os.path.join(self.__workdir, f"x5mconfig\\config\\shared\\islet\\ball_config\\ball_screen_video.csv")
        gamein_video_config = os.path.join(self.__workdir, f"x5mconfig\\config\\shared\\gamein_video\\gamein_video.csv")

        cmds = f"start /wait LitePackTool ResToolPackType:Silent AbPath:{ab_path} LiteWhiteCSV:{white_path} platform:{self.__platform} baseLiteVersion:{self.__base_version} releaseVersion:{self.__release_version} AutoRedundantSilent:{self.__auto_redundant_silent}"
        cmds += f" SlientVedioConfig_1:{screen_video_config} SlientVedioConfig_2:{gamein_video_config} "
        ret = cmd.run_shell(
            cmds=[cmds],
            workdir=exe_path,
        )
        if ret[0] != 0:
            log.error(f"silent package return {ret[0]}")
            raise PyframeException(f"silent打包失败, 返回值: {ret[0]}")

        # 保存当前版本号
        self.__set_release_version()

        # 记录本次版本号用于通知
        env_mgr.set_release_version(release_version=self.__release_version)

    def upload_log(self):
        logs_path = os.path.join(self.__workdir, "x5mobile\\mobile_dancer\\tools\\LitePackToolEx\\exe\\Logs")
        log_list = os.listdir(path=logs_path)
        log_versions = []
        for log_file in log_list:
            if re.match(r"\d+_\d+", log_file):
                log_versions.append(re.match(r"\d+_\d+", log_file).group())
        local_log_name = max(log_versions) + ".txt"
        nexus_log_name = max(log_versions) + f"_{self.__build_num}" + ".txt"
        path_mgr.copy(src=os.path.join(logs_path, local_log_name), dst=os.path.join(logs_path, nexus_log_name))
        nexus_url = advance.upload_pipeline_log(path=os.path.join(logs_path, nexus_log_name))
        env_mgr.set_log_url(nexus_url=nexus_url)

    def __is_special_branch(self) -> bool:
        """
        判断当前分支是否为特殊分支
        """
        pattern = re.compile(r"\d+\.\d+\.\d+|master")
        if re.fullmatch(pattern, self.__branch):
            return False
        return True

    def __read_special_release_version_from_local_file(self, platform: str) -> str:
        """
        从文本文件中读取特殊分支的上次的版本号
        """
        special_release_version_file_name = f"{platform}_special_release_version.txt"
        work_parent_dir = "\\".join(self.__workdir.split("\\")[:-1])
        special_release_version_file = os.path.join(work_parent_dir, special_release_version_file_name)
        if not path_mgr.exists(special_release_version_file):
            self.__write_special_release_version_to_local_file(platform, "")
        with open(special_release_version_file, "r") as f:
            content = f.read().strip()
        log.info(f"特殊分支版本号更: {content}")
        return content

    def __write_special_release_version_to_local_file(self, platform: str, release_version: str):
        """
        将特殊分支版本号更新到本地文件
        """
        special_release_version_file_name = f"{platform}_special_release_version.txt"
        work_parent_dir = "\\".join(self.__workdir.split("\\")[:-1])
        special_release_version_file = os.path.join(work_parent_dir, special_release_version_file_name)
        with open(special_release_version_file, "w") as f:
            f.write(release_version)
            log.info(f"特殊分支版本号更新为: {release_version}")

    def __get_release_version(self) -> str:
        """
        组织版本号
        """
        # 如果是特殊分支，使用6.99.XXX
        platform = env_mgr.get_platform()
        if self.__is_special_branch():
            special_release_version = self.__read_special_release_version_from_local_file(platform)
            if not special_release_version:
                release_version = "9.99.0"
            else:
                branch_prefix, branch_middle, branch_suffix = special_release_version.split(".")
                release_version = ".".join([branch_prefix, branch_middle, str(int(branch_suffix) + 1)])
        else:
            # 如果是主支，以当前git最大分支为准+1
            max_branch = SilentGitMgr().get_max_branch()
            if self.__branch == "master":
                branch_prefix, branch_middle, branch_suffix = max_branch.split(".")
                max_branch = "_".join([branch_prefix, str(int(branch_middle) + 1), branch_suffix])
                master_release_version = global_env_mgr.get_master_release_version(platform, max_branch)
                if not master_release_version:
                    # branch_prefix, branch_middle, branch_suffix = max_branch.split(".")
                    release_version = ".".join([branch_prefix, str(int(branch_middle) + 1), branch_suffix])
                else:
                    branch_prefix, branch_middle, branch_suffix = master_release_version.split(".")
                    release_version = ".".join([branch_prefix, branch_middle, str(int(branch_suffix) + 1)])
            # 分支的话，就是分支号
            else:
                branch_release_version = global_env_mgr.get_branch_release_version(platform, self.__branch)
                if not branch_release_version:
                    branch_prefix, branch_middle, branch_suffix = max_branch.split(".")
                    max_branch = "_".join([branch_prefix, str(int(branch_middle)), branch_suffix])
                    master_release_version = global_env_mgr.get_master_release_version(platform, max_branch)
                    branch_prefix, branch_middle, branch_suffix = master_release_version.split(".")
                else:
                    branch_prefix, branch_middle, branch_suffix = branch_release_version.split(".")
                release_version = ".".join([branch_prefix, branch_middle, str(int(branch_suffix) + 1)])
        return release_version

    def __set_release_version(self):
        """
        保存版本号，方便下次打包使用
        """
        release_version = self.__get_release_version()
        if self.__is_special_branch():
            self.__write_special_release_version_to_local_file(self.__platform, release_version)

        else:
            if self.__branch == "master":
                max_branch = SilentGitMgr().get_max_branch()
                branch_prefix, branch_middle, branch_suffix = max_branch.split(".")
                max_branch = "_".join([branch_prefix, str(int(branch_middle) + 1), branch_suffix])
                global_env_mgr.set_master_release_version(self.__platform, max_branch, release_version)
            else:
                global_env_mgr.set_branch_release_version(self.__platform, self.__branch, release_version)
