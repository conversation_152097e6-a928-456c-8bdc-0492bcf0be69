# coding=utf-8

from frame import advance, wechat
from frame.exception.exception import PyframeException
from project.x5m import LanguageEnum
from project.x5m.silent_package.mgr.env_mgr import env_mgr, jenkins_env_mgr
from project.x5m.silent_package.mgr.ftp_mgr import SilentFTPMgr
from project.x5m.silent_package.mgr.git_mgr import SilentGitMgr
from project.x5m.silent_package.mgr.link_mgr import SilentLinkMgr
from project.x5m.silent_package.mgr.p4_mgr import SilentP4Mgr
from project.x5m.silent_package.mgr.package_mgr import SilentPackageMgr

WEBHOOK = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5c09712d-12c7-44d3-9ba0-80e7903d6614"


@advance.stage(stage="准备")
def prepare(**kwargs):
    lang = jenkins_env_mgr.get_language()
    if lang not in LanguageEnum.__members__:
        raise PyframeException(f"未知语言类型: {lang}")
    # 设置资源分支
    branch = jenkins_env_mgr.get_branch()
    if branch == "master":
        resource_branch = "Resources"
    else:
        resource_branch = f"b/{branch}"
    env_mgr.set_res_branch(res_branch=resource_branch)
    # 设置平台
    env_mgr.set_platform_android()


@advance.stage(stage="更新x5mobile")
def update_x5mobile(**kwargs):
    package_git_mgr = SilentGitMgr()
    package_git_mgr.update_x5mobile()


@advance.stage(stage="更新x5mconfig")
def update_x5mconfig(**kwargs):
    package_git_mgr = SilentGitMgr()
    package_git_mgr.update_x5mconfig()


@advance.stage(stage="更新p4")
def update_p4(**kwargs):
    silent_p4_mgr = SilentP4Mgr()
    changelist = silent_p4_mgr.get_latest_changelist()
    env_mgr.set_p4_changelist(changelist=changelist)

    silent_p4_mgr.sync_all(force=jenkins_env_mgr.get_force_sync_p4())
    silent_p4_mgr.clean_tmp()


@advance.stage(stage="软连接")
def soft_link(**kwargs):
    package_link_mgr = SilentLinkMgr()
    package_link_mgr.copy_config()
    package_link_mgr.make_link()


@advance.stage(stage="打包")
def package(**kwargs):
    package_mgr = SilentPackageMgr()
    package_mgr.package()


@advance.stage(stage="上传ftp")
def upload_ftp(**kwargs):
    silent_ftp_mgr = SilentFTPMgr()
    silent_ftp_mgr.upload()


@advance.stage(stage="上传版本控制")
def upload_version_control(**kwargs):
    # 上传配置
    git_mgr = SilentGitMgr()
    git_mgr.upload_version_control()


def __get_msg():
    msg = f"**git分支**: {jenkins_env_mgr.get_branch()}\n"
    msg += f"**base版本号**: {env_mgr.get_base_version()}\n"
    msg += f"**release版本号**: {env_mgr.get_release_version()}\n"
    msg += f"**强更p4**: {jenkins_env_mgr.get_force_sync_p4()}\n"
    msg += f"**p4提交号**: {env_mgr.get_p4_changelist()}\n"

    has_upload_version_control = env_mgr.get_upload_version_control()
    if has_upload_version_control:
        msg += f"**上传version_control**: {has_upload_version_control}\n"

    upload_ftp_url = env_mgr.get_upload_ftp_url()
    if upload_ftp_url:
        msg += f"**silent包:** [silent目录]({upload_ftp_url})\n"

    upload_config_url = env_mgr.get_upload_config_url()
    if upload_config_url:
        upload_config_name = upload_config_url.split("/")[-1]
        msg += f"**配置文件:** [{upload_config_name}]({upload_config_url})\n"

    log_url = env_mgr.get_log_url()
    if log_url:
        log_name = log_url.split("/")[-1]
        msg += f"**打包日志:** [{log_name}]({log_url})"

    return msg


def on_always(**kwargs):
    package_mgr = SilentPackageMgr()
    package_mgr.upload_log()


def on_success(**kwargs):
    wechat.send_unicast_post_success(user_list=[], content=__get_msg())
    wechat.send_multicast_post_success(webhook=WEBHOOK, content=__get_msg())
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(user_list=[], content=__get_msg())
    wechat.send_multicast_post_failure(
        webhook=WEBHOOK,
        content=__get_msg(),
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(user_list=[], content=__get_msg())
    wechat.send_multicast_post_canceled(
        webhook=WEBHOOK,
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_canceled()
