node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline{
    agent {
        node {
            label "small-island-package"
        }
    }
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder logRotator(daysToKeepStr: env.daysToKeepStr) // daysToKeepStr、numToKeepStr等为全局环境变量，可在【系统管理>系统配置>全局属性>环境变量】中进行修改
    }
    parameters {
         text(name: 'first_changelist', defaultValue: '0', description: '输入开始打包的changelist')
         booleanParam(name: 'debug', defaultValue: false, description: '是否为测试模式')
    }
    //     因为四点到五点p4需要备份数据，流水线会执行失败
    triggers {
        cron('H/15 0-3,5-23 * * *')
    }
    // environment {
    //    GIT_NAME = 'pyframe-pipeline'
    //    PIPELINE_NAME = '美术资源定时打包 » 小岛打包-分发'
    // }

    stages {
        stage('美术资源打包'){
            matrix {
                agent {
                    node {
                        label "small-island-${PLATFORM}-package"
                        customWorkspace "D:\\small_island\\${PLATFORM}"
                    }
                }
                axes {
                    axis {
                        name 'PLATFORM'
                        values 'android','ios'
                    }
                }
                stages {
                    stage("更新流水线依赖") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "python -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q && python -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn -q")
                                }
                            }
                        }
                    }
                    stage("更新打包工程") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "python x5m.py small_island_package --job=update_project")
                                }
                            }
                        }
                    }
                    stage("检查是否需要打包") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "python x5m.py small_island_package --job=check_package")
                                }
                            }
                        }
                    }
                    stage("清理资源") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "python x5m.py small_island_package --job=clean_resource")
                                }
                            }
                        }
                    }
                    stage("获取P4资源") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "python x5m.py small_island_package --job=sync_resource")
                                }
                            }
                        }
                    }
                    stage("调用打包命令") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "python x5m.py small_island_package --job=start_package")
                                }
                            }
                        }
                    }
                    stage("计算打包结果") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "python x5m.py small_island_package --job=calculate_result")
                                }
                            }
                        }
                    }
                    stage("分发AB") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "python x5m.py small_island_package --job=dist_ab")
                                }
                            }
                        }
                    }
                    stage("提交P4") {
                        steps {
                            dir("pyframe-pipeline"){
                                script {
                                    bat(script: "python x5m.py small_island_package --job=submit_p4")
                                }
                            }
                        }
                    }
                }
                post {
                    always {
                        dir("pyframe-pipeline"){
                            script {
                                bat(script: "python x5m.py small_island_package --job=on_always")
                            }
                        }
                    }
                }
            }
        }
    }
    post {
        unstable {
            dir("D:\\small_island\\${PLATFORM}\\pyframe-pipeline"){
                script {
                    bat(script: "python x5m.py small_island_package --job=on_unstable")
                }
            }
        }
        success {
            dir("D:\\small_island\\${PLATFORM}\\pyframe-pipeline"){
                script {
                    bat(script: "python x5m.py small_island_package --job=on_success")
                }
            }
        }
        failure {
            dir("D:\\small_island\\${PLATFORM}\\pyframe-pipeline"){
                script {
                    bat(script: "python x5m.py small_island_package --job=on_failure")
                }
            }
        }
    }
}
