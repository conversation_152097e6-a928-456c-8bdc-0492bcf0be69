import re
from typing import List, <PERSON><PERSON>

import xlrd

from frame import *
from project.x5m.small_island_package.mgr.env_mgr import env_mgr, global_env_mgr
from project.x5m.art_package_mgr.base_mgr.distribute_package_mgr import DistributePackageMgr


class BuildMgr(DistributePackageMgr):
    def __init__(self):
        super().__init__("island_distrib")
        self.resource_dir_prefix = f"//x5_mobile/mr/art_release_test/art_src/island" if self._debug else f"//x5_mobile/mr/art_release/art_src/island"
        self.table_rule_prefix = f"//x5_mobile/mr/art_release_test/table_rule" if self._debug else f"//x5mplan/resmg/island"
        if self._debug:
            self.ab_hotfix_dir_prefix = "//x5_mobile/mr/art_release_test/onlineupdate/{branch}/{hotfix_version}/client/{platform}/assetbundles/art"
            self.ab_trunk_dir_prefix = "//x5_mobile/mr/art_release_test/Resources/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art"
            self.ab_branch_dir_prefix = "//x5_mobile/mr/art_release_test/b/{branch}/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art"
            self.ab_cdn_dir_prefix = "//x5_mobile/mr/art_release_test/{platform}/assetbundles"
        else:
            self.ab_hotfix_dir_prefix = "//x5_mobile/mr/onlineupdate/{branch}/{hotfix_version}/client/{platform}/assetbundles/art"
            self.ab_trunk_dir_prefix = "//x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art"
            self.ab_branch_dir_prefix = "//x5_mobile/mr/b/{branch}/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art"
            self.ab_cdn_dir_prefix = "//x5m/res/cdn/cooked/{platform}/assetbundles"
        self.p4.set_options(clobber=True)
        self.git_mgr = GitMgr(
            workdir=self._workspace,
            project_name="x5_mobile",
        )
        self.package_project_url = "http://x5mobile-gitlab.h3d.com.cn/dgm/arttrunk.git"
        self.unity = "Unity.exe"
        self.log_path = os.path.join(self._workspace, "jenkins_log")
        self.package_log = os.path.join(self.log_path, f"{self._platform}_{env.pipeline.build_num()}.log")
        self.project_path = os.path.join(self._workspace, "x5_mobile", "mobile_dancer", "arttrunk", "client")
        self.source_prefix = "Assets/StaticResources/art/3d"
        self.project_art_path = os.path.join(self.project_path, self.source_prefix)
        self.ab_path = os.path.join(self._workspace, "ab")
        self.report_path = os.path.join(self.project_path, "AssetBundleTool", "AssetBundleToolLogs")
        self.resources_txt = os.path.join(self.project_path, "AssetBundleTool", "resources.txt")

    def check_package(self):
        """
        检查是否需要打包
        """
        first_changelist = env_mgr.get_first_changelist()
        last_changelist = global_env_mgr.get_last_changelist(platform=self._platform)
        if first_changelist is None or first_changelist == "0" or first_changelist == "":
            if last_changelist == "0":
                raise PyframeException("first_changelist为空，且数据库中上次打包版本号也为空，无法打包")
            changelist = int(last_changelist) + 1
        else:
            changelist = first_changelist

        changes = self.p4.get_changes(f"{self.resource_dir_prefix}/...@{changelist},now", max=1000)
        log.info(f"changes: {changes}")
        if len(changes) == 0:
            pipeline_mgr.stop_current_build()
        else:
            env_mgr.set_changes(platform=self._platform, changes=changes)
    
    # 暂时这么调用吧，后续在重构
    def distribute_check(self):
        super()._other_check()

    def update_project(self):
        """
        获取打包工程
        """
        if not self.git_mgr.exist():
            self.git_mgr.clone_with_password(
                url=self.package_project_url, branch="master", username="<EMAIL>", password="reporter123"
            )
        else:
            try:
                self.git_mgr.pull()
            except Exception as e:
                log.error(e)
                error_code, error_msg = cmd.run_shell(
                    cmds=[f"git clean mobile_dancer/arttrunk/client -xdf && git checkout . && git pull origin master"],
                    workdir=os.path.join(self._workspace, "x5_mobile"),
                )
                if error_code != 0:
                    raise PyframeException(f"更新arttrunk工程失败: {error_msg}")

    def clean_resource(self):
        """
        清理资源
        """
        log.info("开始清理原始资源")
        if path_mgr.exists(self.project_art_path):
            path_mgr.rm(self.project_art_path)

        log.info("开始清理ab资源")
        if path_mgr.exists(self.ab_path):
            path_mgr.rm(self.ab_path)

        log.info("开始清理打包报告")
        if path_mgr.exists(self.report_path):
            path_mgr.rm(self.report_path)

        log.info("开始清理打包日志")
        if path_mgr.exists(self.log_path):
            path_mgr.rm(self.log_path)
        # 创建打包日志目录
        path_mgr.mkdir(self.log_path)

    def __get_resource_id_and_path_and_suffix(self, changelist: str) -> Tuple[List[str], List[str], List[str]]:
        """
        根据changelist获取资源ID
        Args:
            changelist:

        Returns:

        """
        files = self.p4.get_files_by_changelist(changelist)
        # 通过正则表达式获取资源ID
        # id_pattern = re.compile(r"^//.*?(\d{9,10}|//.*?island/common/island_\w+.?)/.*?$")
        id_pattern = re.compile(r"^//.*?(\d{9,10}|island_\w+.?|vt\d{4,})/.*?$")
        source_ids = []
        # path_pattern = re.compile(r"^(//.*?\d{9,10}|//.*?island/common/island_\w+.?)/.*?$")
        path_pattern = re.compile(r"^(//.*?\d{9,10}|//.*?island/common/island_\w+.?|//.*?island/videotemplate/vt\d{4,})/.*?$")
        source_paths = []
        # suffix_pattern = re.compile(r"^//.*?(island/.*?/.*?/\d{9,10}|island/common/island_\w+.?)/.*?$")
        suffix_pattern = re.compile(r"^//.*?(island/.*?/.*?/\d{9,10}|island/common/island_\w+.?|island/videotemplate/vt\d{4,})/.*?$")
        source_suffixes = []
        for file in files:
            ret = re.findall(id_pattern, file)
            if ret:
                if ret[0] not in source_ids:
                    source_ids.append(ret[0])
            ret = re.findall(path_pattern, file)
            if ret:
                if ret[0] not in source_paths:
                    source_paths.append(ret[0])
                    log.info(f"ret {ret[0]}")
            ret = re.findall(suffix_pattern, file)
            if ret:
                if ret[0] not in source_suffixes:
                    source_suffixes.append(ret[0])
        log.info(f"source_paths: {source_paths}")
        # 过滤掉已经存在的路径
        exist_source_paths = []
        for source_path in source_paths:
            # 如果是目录，直接添加
            if self.p4.dirs(source_path):
                exist_source_paths.append(source_path)

        return source_ids, exist_source_paths, source_suffixes

    def __get_resource_views(self, source_paths: list) -> List[str]:
        """
        获取原始资源映射
        Args:
            source_paths: 资源路径
        Returns:
            list: 映射列表
        """
        views = []
        for source_path in source_paths:
            view = f"{source_path}/... //{self.p4.client}/{source_path.replace('//', '')}/..."
            if view not in views:
                views.append(view)
        vt_common = "//x5_mobile/mr/art_release/art_src/island/videotemplate/common"
        views.append(f"{vt_common}/... //{self.p4.client}/{vt_common.replace('//', '')}/...")
        return views

    def __get_dist_views(self) -> list:
        """
        获取分发表映射
        Returns:
            list: 映射列表
        """
        view = f"{self.table_rule_prefix}/... //{self.p4.client}/{self.table_rule_prefix.replace('//', '')}/..."
        return [view]

    def sync_resource(self):
        """
        更新P4资源
        """
        all_changes = env_mgr.get_changes(platform=self._platform)
        users = []
        source_paths = []
        pattern = re.compile(r"^.*<(.*@h3d.com.cn)>$")
        for change in all_changes:
            desc = change.get("desc")
            ret = re.findall(pattern, desc)
            if ret:
                user = ret[0]
                users.append(user)
            user = change.get("user")
            if not user.endswith("h3d.com.cn"):
                user = f"{user}@h3d.com.cn"
            if user not in users:
                users.append(user)
            changelist = change.get("change")
            _, source_path, _ = self.__get_resource_id_and_path_and_suffix(changelist)
            source_paths += source_path

        env_mgr.set_source_paths(source_paths=source_paths)
        env_mgr.set_users(platform=self._platform, users=users)
        # 记录最新changelist
        env_mgr.set_changelist(platform=self._platform, changelist=all_changes[0].get("change"))
        views = []

        # 原始资源映射
        resource_views = self.__get_resource_views(source_paths)
        views.extend(resource_views)

        # 分发表映射
        dist_views = self.__get_dist_views()
        views.extend(dist_views)

        # 设置P4 view 和 root，同步资源
        self.p4.set_root(self._workspace)
        self.p4.set_view(views=views)
        self.p4.sync_all(force=True)

        self.__copy_resource_to_project()

    def __copy_resource_to_project(self):
        """
        复制原始资源到打包工程
        """
        # 复制需要打包的原始资源到打包工程,并记录资源路径
        if not path_mgr.exists(self.project_art_path):
            path_mgr.mkdir(self.project_art_path)
        source_paths = env_mgr.get_source_paths()
        relative_sources = []
        relative_pattern = re.compile(r"^//(.*?island/.*?\d{9,10}|.*?island/common/island_\w+.?|.*?island/videotemplate/vt\d{4,}).*?$")
        for source_path in source_paths:
            ret = re.findall(relative_pattern, source_path)
            if not ret:
                continue
            relative_path = ret[0]
            log.info(f"相对路径: {relative_path}")
            src = os.path.join(self._workspace, relative_path)
            if not path_mgr.exists(src):
                log.warn(f"资源不存在: {src}")
                raise PyframeException(f"资源不存在: {src}, 请检查P4是否同步成功")
            temp = relative_path.replace("x5_mobile/mr/art_release/art_src/", "")
            dst = os.path.join(self.project_art_path, temp)

            # 资源路径如果有重复，只复制一次
            relative_source = f"{self.source_prefix}/" + temp
            if relative_source not in relative_sources:
                path_mgr.xcopy(src, dst, dst_is_file=False)
                relative_sources.append(relative_source)
        env_mgr.set_relative_source(",".join(relative_sources))
        # 特殊处理，vt common资源每次都会更新
        src = os.path.join(self._workspace, "x5_mobile/mr/art_release/art_src/island/videotemplate/common")
        dst = os.path.join(self.project_art_path, "island/videotemplate/common")
        path_mgr.xcopy(src, dst, dst_is_file=False)

    def start_package(self):
        """
        调用打包命令
        """
        path = env_mgr.get_relative_source()
        if not path:
            log.info("没有需要打包的资源，直接取消流水线运行")
            s_changelist = env_mgr.get_changelist(platform=self._platform)
            global_env_mgr.set_last_changelist(platform=self._platform, changelist=s_changelist)
            pipeline_mgr.stop_current_build()

        cmd_line = f'"{self.unity}" '
        cmd_line += f"-quit -batchmode -projectPath {self.project_path} -logFile {self.package_log} "
        cmd_line += f"-executeMethod H3DBuildTools.BuildArt -buildTarget {self._platform} path={path} "
        cmd_line += f"out_path={self.ab_path} "
        cmd_line += f"forbid_open_log=true "
        ret, _ = cmd.run_shell(
            cmds=[cmd_line],
            workdir=self._workspace,
        )
        if ret != 0:
            advance.raise_unity_log_exception(log_path=self.package_log)
            raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    @staticmethod
    def __get_report_detail(read_lines: list) -> dict:
        """
        获取打包结果
        Returns:
        """
        # success_pattern = re.compile(r"^\[info]\s+\d+\s.*?(\d{10,}$|\d+_extend\d$|island_\w+_\w+$|island_.*?$)")
        # fail_pattern = re.compile(r"^\[Error]\s+\d+\s.*?(\d{10,}$|\d+_extend\d$|island_\w+_\w+$|island_.*?$)")
        success_pattern = re.compile(r"^\[info]\s+\d+\s.*?(\d{10,}$|island_\w+_\w+$|island_.*?$|vt\d{4,})")
        fail_pattern = re.compile(r"^\[Error]\s+\d+\s.*?(\d{10,}$|island_\w+_\w+$|island_.*?$|vt\d{4,})")
        success_ids = []
        fail_ids = []
        for read_line in read_lines:
            suc = re.findall(success_pattern, read_line)
            if suc:
                success_ids.extend(suc)

            fail = re.findall(fail_pattern, read_line)
            if fail:
                fail_ids.extend(fail)
        success_ids = list(set(success_ids))
        fail_ids = list(set(fail_ids))
        report_detail = {"success_ids": success_ids, "fail_ids": fail_ids}
        return report_detail

    def calculate_package_result(self):
        """
        计算打包结果
        """
        reports = path_mgr.glob(self.report_path, "*.txt")
        if not reports:
            raise PyframeException(f"获取打包结果失败，请检查查看打包日志")
        report = reports[0]
        with open(report, "r", encoding="utf-8") as f:
            content = f.readlines()
            # 解析打包结果
            report_detail = self.__get_report_detail(content)
            # 记录打包结果
            log.debug(f"platform: {self._platform}, report_detail: {report_detail}")
            env_mgr.set_report(report=report_detail)

    def __get_dist_table(self) -> str:
        """
        获取分发表本地路径
        Returns:
            str: 分发表本地路径
        """
        return os.path.join(self._workspace, f"{self.table_rule_prefix.replace('//', '')}")

    @staticmethod
    def __get_island_type_by_id(resource_id: str) -> str or None:
        """
        根据ID获取island类型
        Args:
            resource_id:

        Returns:

        """
        source_paths = env_mgr.get_source_paths()
        pattern = re.compile(rf"^//.*?(island.*?{resource_id}).*$")
        for source_path in source_paths:
            ret = re.findall(pattern, source_path)
            if ret:
                return ret[0]

    @staticmethod
    def __get_version_and_hotfix(hotfix_version: str) -> Tuple[str, str]:
        """
        拆分热更版本号
        Args:
            hotfix_version:

        Returns:

        """
        # 正则校验热更版本号，如果校验失败，返回空
        if not hotfix_version:
            return "", ""
        pattern = re.compile(r"^(\d{4}).\d{7}")
        ret = re.findall(pattern, hotfix_version)
        if ret:
            major_version = ret[0][0] + "." + ret[0][1:3] + ".0"
            return major_version, hotfix_version
        raise PyframeException(f"请检查热更分发表中的path路径是否正确: {hotfix_version}")

    def __match_special_branch_dist_path(self, resource_id: str) -> str or None:
        """
        匹配特殊分支分发规则
        Args:
            reource_id: 资源ID
        Returns:
            str: 匹配到的特殊分支
        """
        dist_table_path = self.__get_dist_table()
        # 获取特殊分支分发表
        special_branch_table = os.path.join(dist_table_path, "special-branch-small-island-rule.xlsx")

        if path_mgr.exists(special_branch_table):
            dist_table = xlrd.open_workbook(special_branch_table)
            sheet = dist_table.sheets()[0]  # 获取第一个sheet
            rows = sheet.nrows  # 行数
            row_values = sheet.row_values(0)  # 获取第一行的数据

            if row_values and row_values[0] != "name" and row_values[1] != "path":
                log.warning(f"热更分发表格式不正确，第一行第一列的值不等于name，且第二列的值不等于path")
                return None

            for i in range(1, rows):
                row = sheet.row_values(i, 0, 2)
                # 如果第一列的值以.0结尾的小数，则去掉.0
                if isinstance(row[0], float):
                    found_row = str(row[0])[:-2]
                else:
                    found_row = str(row[0]).strip()
                if str(resource_id).strip() == found_row:
                    return str(row[1]).strip()
        else:
            log.info("未找到特殊分支分发表，不进行规则匹配")
            return None

    def __match_hotfix_dist_path(self, resource_id: str) -> str or None:
        """
        匹配热更分发规则
        Args:
            resource_id: 资源ID
        Returns:
            str: 匹配到的热更
        """
        dist_table_path = self.__get_dist_table()
        # 获取热更分发表
        hotfix_table = os.path.join(dist_table_path, "hotfix-small-island-rule.xlsx")
        if path_mgr.exists(hotfix_table):
            dist_table = xlrd.open_workbook(hotfix_table)
            sheet = dist_table.sheets()[0]  # 获取第一个sheet
            rows = sheet.nrows  # 行数
            row_values = sheet.row_values(0)  # 获取第一行的数据
            # 如果第一行第一列的值不等于name，且第二列的值不等于path，则返回
            if row_values and row_values[0] != "name" and row_values[1] != "path":
                log.warning(f"热更分发表格式不正确，第一行第一列的值不等于name，且第二列的值不等于path")
                return None
            # 从第二行数据开始，如果第一列的值等于资源id，则返回第二列的值
            for i in range(1, rows):
                row = sheet.row_values(i, 0, 2)
                # 如果第一列的值以.0结尾的小数，则去掉.0
                if isinstance(row[0], float):
                    found_row = str(row[0])[:-2]
                else:
                    found_row = str(row[0]).strip()
                if str(resource_id).strip() == found_row:
                    return str(row[1]).strip() + "000"
        else:
            log.info("未找到热更分发表，")
            return None

    def get_current_branch(self) -> str:
        """
        获取当前分支
        Returns:
            str: 当前分支
        """
        dirs = self.p4.dirs("//x5_mobile/mr/b/*")
        pattern = re.compile(r"^//x5_mobile/mr/b/(\d+.\d+.\d+$)")
        new = []
        for d in dirs:
            ret = re.findall(pattern, d)
            if ret:
                new.append(ret[0])
        if not new:
            raise PyframeException("获取当前分支失败")
        # new = ['6.05.0_i']
        new = sorted(new, reverse=True)
        return new[0]

    def __find_eff_files(self, success_id: str) -> list:
        """
        获取eff ab 包
        """
        log.info(f"success_id: {success_id}")
        eff_files = []
        ab_path = os.path.join(self.ab_path, "art", "island")
        p = Path(ab_path).rglob(f"{success_id}*eff*")
        for i in p:
            if i:
                log.info(f"{i.name}")
                eff_files.append(i)
        log.info(f"eff_files: {eff_files}")
        return eff_files

    # 判断当前分支是否是特殊分支
    def __is_special_branch(self) -> bool:
        """
        判断当前分支是否是特殊分支
        Returns:
            bool: True 是特殊分支，False 不是特殊分支
        """
        branch = self.get_current_branch()
        pattern = re.compile(r"^\d+.\d+.\d+$")
        ret = re.findall(pattern, branch)
        if ret:
            return False
        return True

    def dist_ab(self):
        """
        分发AB资源
        """
        report_detail = env_mgr.get_report(platform=self._platform)
        success_ids = report_detail.get("success_ids", [])
        current_branch = self.get_current_branch()
        # is_special_branch = self.__is_special_branch()
        dist_channels = []
        if success_ids:
            # 设置映射更新AB
            views = []
            for success_id in success_ids:
                hotfix_version = self.__match_hotfix_dist_path(success_id)

                if hotfix_version:  # 热更
                    major_version, hotfix_version = self.__get_version_and_hotfix(hotfix_version)
                    # 映射热更分发路径
                    hotfix_dir = self.ab_hotfix_dir_prefix.format(branch=major_version, hotfix_version=hotfix_version, platform=self._platform)
                    view = f"{hotfix_dir}/... //{self.p4.client}/{hotfix_dir.replace('//', '')}/..."
                    if view not in views:
                        views.append(view)

                    # 记录分发路径
                    if "热更" not in dist_channels:
                        dist_channels.append("热更")
                else:  # 默认版内
                    # 映射特殊分支路径
                    special_branch = self.__match_special_branch_dist_path(success_id)
                    if special_branch:
                        special_branch_dir = self.ab_branch_dir_prefix.format(branch=special_branch, platform=self._platform)
                        view = f"{special_branch_dir}/... //{self.p4.client}/{special_branch_dir.replace('//', '')}/..."
                        if view not in views:
                            views.append(view)

                        if "特殊分支" not in dist_channels:
                            dist_channels.append("特殊分支")

                    # 映射主支分发路径
                    trunk_dir = self.ab_trunk_dir_prefix.format(platform=self._platform)
                    view = f"{trunk_dir}/... //{self.p4.client}/{trunk_dir.replace('//', '')}/..."
                    if view not in views:
                        views.append(view)
                    # 记录分发路径
                    if "主支" not in dist_channels:
                        dist_channels.append("主支")

                    # 映射分支分发路径
                    branch_dir = self.ab_branch_dir_prefix.format(branch=current_branch, platform=self._platform)
                    view = f"{branch_dir}/... //{self.p4.client}/{branch_dir.replace('//', '')}/..."
                    if view not in views:
                        views.append(view)

                    # 记录分发路径
                    if "分支" not in dist_channels:
                        dist_channels.append("分支")
            log.info(f"设置映射: {views}")
            self.p4.set_view(views)
            self.p4.sync_all()

            # 进行分发
            dist_destinations = []
            dist_info = {}.fromkeys(dist_channels, [])
            for success_id in success_ids:
                island_info = self.__get_island_type_by_id(success_id)
                if not island_info:
                    raise PyframeException(f"未解析到资源{success_id}的ID信息, ")

                hotfix_version = self.__match_hotfix_dist_path(success_id)

                ab_src = os.path.join(self.ab_path, "art", island_info)
                ab_manifest_src = os.path.join(self.ab_path, "art", f"{island_info}.h3dmanifest")
                ab_eff_files = self.__find_eff_files(success_id)

                if hotfix_version:  # 热更
                    major_version, hotfix_version = self.__get_version_and_hotfix(hotfix_version)
                    dst = os.path.join(
                        self._workspace,
                        self.ab_hotfix_dir_prefix.format(branch=major_version, hotfix_version=hotfix_version, platform=self._platform).replace(
                            "//", ""
                        ),
                        island_info,
                    )
                    path_mgr.mkdir(dst)
                    path_mgr.xcopy(ab_src, dst, dst_is_file=False)
                    path_mgr.xcopy(ab_manifest_src, dst, dst_is_file=False)
                    if ab_eff_files:
                        for ab_eff_file in ab_eff_files:
                            path_mgr.xcopy(ab_eff_file, dst, dst_is_file=False)
                    dist_destinations.append(dst)
                    # 记录对应分发渠道的ID信息
                    dist_info["热更"].append(success_id)
                else:  # 默认版内
                    special_branch = self.__match_special_branch_dist_path(success_id)
                    # 分发到特殊分支
                    if special_branch:
                        dst = os.path.join(
                            self._workspace,
                            self.ab_branch_dir_prefix.format(branch=special_branch, platform=self._platform).replace("//", ""),
                            island_info,
                        )
                        path_mgr.mkdir(dst)
                        path_mgr.xcopy(ab_src, dst, dst_is_file=False)
                        path_mgr.xcopy(ab_manifest_src, dst, dst_is_file=False)
                        if ab_eff_files:
                            for ab_eff_file in ab_eff_files:
                                path_mgr.xcopy(ab_eff_file, dst, dst_is_file=False)
                        dist_destinations.append(dst)
                        # 记录对应分发渠道的ID信息
                        dist_info["特殊分支"].append(success_id)

                    # 分发到分支
                    dst = os.path.join(
                        self._workspace, self.ab_branch_dir_prefix.format(branch=current_branch, platform=self._platform).replace("//", ""), island_info
                    )
                    path_mgr.mkdir(dst)
                    path_mgr.xcopy(ab_src, dst, dst_is_file=False)
                    path_mgr.xcopy(ab_manifest_src, dst, dst_is_file=False)
                    if ab_eff_files:
                        for ab_eff_file in ab_eff_files:
                            path_mgr.xcopy(ab_eff_file, dst, dst_is_file=False)
                    dist_destinations.append(dst)
                    # 记录对应分发渠道的ID信息
                    dist_info["分支"].append(success_id)

                    # 分发到主支
                    dst = os.path.join(self._workspace, self.ab_trunk_dir_prefix.format(platform=self._platform).replace("//", ""), island_info)
                    path_mgr.mkdir(dst)
                    path_mgr.xcopy(ab_src, dst, dst_is_file=False)
                    path_mgr.xcopy(ab_manifest_src, dst, dst_is_file=False)
                    if ab_eff_files:
                        for ab_eff_file in ab_eff_files:
                            path_mgr.xcopy(ab_eff_file, dst, dst_is_file=False)
                    dist_destinations.append(dst)
                    # 记录对应分发渠道的ID信息
                    dist_info["主支"].append(success_id)
            env_mgr.set_dist_destinations(platform=self._platform, destinations=dist_destinations)
            env_mgr.set_distribution_channel(platform=self._platform, dist_info=dist_info)

        else:
            log.warning("没有打包成功的资源，无需分发提交")

    def submit_p4(self):
        """
        提交P4资源
        """
        # 获取分发路径
        dist_destinations = env_mgr.get_dist_destinations(platform=self._platform)
        reconciles = []
        for dist_destination in dist_destinations:
            reconciles += self.p4.reconcile_without_tmp(f"{dist_destination}/...")
        # 如果有资源需要提交，则提交
        if reconciles:
            # 原始资源提交人、流水线名称、流水线编号、流水线链接
            users = env_mgr.get_users(platform=self._platform)
            desc = f"uploader:{''.join(users)}\n"
            desc += f"Identity:#Jenkins#{env.pipeline.function_name()}#{env.pipeline.build_num()}"
            desc += f"link:{env.pipeline.build_url()}"

            submit_rets = self.p4.submit(desc=desc, revert_if_failed=True)
            if submit_rets:
                submitted_change = submit_rets[0].get("change")
                env_mgr.set_submit_changelist(platform=self._platform, changelist=submitted_change)

        # 获取失败的资源
        report = env_mgr.get_report(platform=self._platform)
        fail_ids = report.get("fail_ids")
        if not fail_ids:
            # 记录最后一次打包的changelist
            s_changelist = env_mgr.get_changelist(platform=self._platform)
            global_env_mgr.set_last_changelist(resource=self._kind, changelist=s_changelist)
        else:
            raise PyframeException(f"存在打包失败的资源，请修改后重新提交资源")

    def upload_report(self):
        """
        上传打包报告
        """
        reports = path_mgr.glob(self.report_path, "*.html")
        if not reports:
            return log.warning("无打包报告生成")
        report = reports[0]
        renamed_report = rf"{self.report_path}\{self._platform}_report_{env.pipeline.build_num()}.html"
        log.info(f"重命名打包报告为: {renamed_report}")
        path_mgr.move(report, renamed_report)
        dst = advance.upload_pipeline_log(renamed_report)
        if dst:
            env_mgr.set_package_report(platform=self._platform, package_report=dst)

    def upload_log(self):
        """
        上传日志
        """
        if not path_mgr.exists(self.package_log):
            return log.warning("无打包日志生成")
        dst = advance.upload_pipeline_log(self.package_log)
        if dst:
            env_mgr.set_log(platform=self._platform, log_path=dst)

    @staticmethod
    def get_notify_info() -> str:
        """
        组织通知信息
        Returns:
            str: 通知信息
        """
        msg = ""
        # Android和iOS的分发渠道一致，所以只需要获取一次
        dist_info = env_mgr.get_distribution_channel(platform="ios")
        if dist_info:
            for key, value in dist_info.items():
                if value:
                    # 去重后的分发渠道
                    value = list(set(value))
                    if len(value) >= 5:
                        value = value[:5]
                        value.append("...")
                    ids = ", ".join(value)
                    msg += f"**{key}:** {ids}\n"

        for platform in ["android", "ios"]:
            msg += f"**{platform}:** \n"
            package_report = env_mgr.get_package_report(platform=platform)
            msg += f"**打包报告:** [打包报告]({package_report})\n" if package_report else ""
            logs = env_mgr.get_log(platform=platform)
            msg += f"**打包日志:** [打包日志]({logs})\n" if logs else ""
            source_changelist = env_mgr.get_changelist(platform=platform)
            msg += f"**原始资源:** {source_changelist}\n"
            submitted_change = env_mgr.get_submit_changelist(platform=platform)
            if submitted_change:
                msg += f"**AB 资源:** {submitted_change}\n"
            else:
                error_msg = env.get("PYFRAME_EXCEPTION_MESSAGE")
                if error_msg:
                    msg += ""
                else:
                    msg += f"**AB 资源:** 与P4服务器文件一致\n"

            report = env_mgr.get_report(platform=platform)
            success_ids = report.get("success_ids")
            fail_ids = report.get("fail_ids")
            msg += f"**打包数量:** {len(success_ids + fail_ids)}\n" if success_ids is not None and fail_ids is not None else ""

            success_id_str = ""
            if success_ids:
                msg += f"**成功数量:** {len(success_ids)}\n"
                if len(success_ids) >= 5:
                    success_ids = success_ids[:5]
                    success_ids.append("...")
                success_id_str = ", ".join(success_ids)
            msg += f"**成功 ID:** {success_id_str}\n" if success_id_str else ""

            fail_id_str = ""
            if fail_ids:
                fail_id_str = ", ".join(fail_ids)
            msg += f"**失败 ID:** {fail_id_str}\n" if fail_id_str else ""

        return msg

    @staticmethod
    def get_android_ios_users() -> list:
        """
        获取通知用户
        Returns:
            list: 通知用户
        """
        android_users = env_mgr.get_users(platform="android")
        ios_users = env_mgr.get_users(platform="ios")
        users = []
        if android_users:
            users += android_users
        if ios_users:
            users += ios_users
        return users
