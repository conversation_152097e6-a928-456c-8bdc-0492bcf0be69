
from frame import env
from project.x5m.art_package_mgr.base_mgr.env_mgr import EnvMgr, global_env_mgr

class IslandEnvMgr(EnvMgr):
    
    @staticmethod
    def set_relative_source(relative_source: str):
        env.set({"relative_source": relative_source})

    @staticmethod
    def get_relative_source():
        return env.get("relative_source", "")
    
    @staticmethod
    def set_resource_suffix(resource_id: str, suffix: str):
        return env.set({resource_id: suffix})

    @staticmethod
    def get_resource_suffix(resource_id: str):
        return env.get(resource_id, "")

    @staticmethod
    def set_dist_destinations(platform: str, destinations: list):
        return env.set({f"{platform}_dist_destinations": destinations})

    @staticmethod
    def get_dist_destinations(platform: str):
        return env.get(f"{platform}_dist_destinations", [])

    @staticmethod
    def set_source_uploader(uploader: str):
        return env.set({"source_uploader": uploader})

    @staticmethod
    def get_source_uploader():
        return env.get("source_uploader", "")

    @staticmethod
    def set_log(platform: str, log_path: str):
        return env.set({f"{platform}_log": log_path})

    @staticmethod
    def get_log(platform: str):
        return env.get(f"{platform}_log")

    @staticmethod
    def set_package_report(platform: str, package_report: str):
        return env.set({f"{platform}_package_report": package_report})

    @staticmethod
    def get_package_report(platform: str):
        return env.get(f"{platform}_package_report")

    @staticmethod
    def set_changes(platform: str, changes: list):
        return env.set({f"{platform}_changes": changes})

    @staticmethod
    def get_changes(platform: str):
        return env.get(f"{platform}_changes")

    @staticmethod
    def set_submit_changelist(platform: str, changelist: list):
        return env.set({f"{platform}_submit_changelist": changelist})

    @staticmethod
    def get_submit_changelist(platform: str):
        return env.get(f"{platform}_submit_changelist")

    @staticmethod
    def set_distribution_channel(platform: str, dist_info: dict):
        return env.set({f"{platform}_dist_channel": dist_info})

    @staticmethod
    def get_distribution_channel(platform: str):
        return env.get(f"{platform}_dist_channel", {})

env_mgr = IslandEnvMgr()