from frame import *
from project.x5m.small_island_package.mgr.build_mgr import BuildMgr

build_mgr = BuildMgr()


@advance.stage("检查是否需要打包")
def check_package(**kwargs):
    build_mgr.check_package()


@advance.stage("获取打包工程")
def update_project(**kwargs):
    build_mgr.update_project()


@advance.stage("清理资源")
def clean_resource(**kwargs):
    build_mgr.clean_resource()


@advance.stage("获取P4资源")
def sync_resource(**kwargs):
    build_mgr.distribute_check()
    build_mgr.sync_resource()

@advance.stage("调用打包命令")
def start_package(**kwargs):
    build_mgr.start_package()
    build_mgr.upload_report()


@advance.stage("计算打包结果")
def calculate_result(**kwargs):
    build_mgr.calculate_package_result()


@advance.stage("分发AB")
def dist_ab(**kwargs):
    build_mgr.dist_ab()


@advance.stage("提交P4")
def submit_p4(**kwargs):
    build_mgr.submit_p4()


def on_always(**kwargs):
    build_mgr.upload_log()
    build_mgr.upload_report()


def on_unstable(**kwargs):
    content = build_mgr.get_notify_info()
    users = build_mgr.get_android_ios_users()
    wechat.send_unicast_post_unstable(user_list=users, content=content)
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"
    wechat.send_multicast_post_unstable(webhook=webhook, content=content)
    advance.insert_pipeline_history_on_unstable()


def on_success(**kwargs):
    content = build_mgr.get_notify_info()
    users = build_mgr.get_android_ios_users()
    wechat.send_unicast_post_success(user_list=users, content=content)
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"
    wechat.send_multicast_post_success(webhook=webhook, content=content)
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    content = build_mgr.get_notify_info()
    users = build_mgr.get_android_ios_users()
    wechat.send_unicast_post_failure(user_list=users, content=content, rescue=False, to_admin=True)
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b3e622-f2be-4203-b7fa-5703fc668033"
    wechat.send_multicast_post_failure(webhook=webhook, content=content)
    advance.insert_pipeline_history_on_failure()
