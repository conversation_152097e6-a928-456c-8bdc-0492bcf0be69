# coding=utf-8
"""
私服部署
"""
import time
import re

from frame import *
from project.x5m.test.deploy_private_server.mgr.cdn_mgr import cdn_mgr
from project.x5m.test.deploy_private_server.mgr.config_mgr import config_mgr
from project.x5m.test.deploy_private_server.mgr.core_mgr import CoreMgr
from project.x5m.test.deploy_private_server.mgr.deploy_install_mgr import deploy_install_mgr
from project.x5m.test.deploy_private_server.mgr.env_mgr import env_mgr, bk_env_mgr
from project.x5m.test.deploy_private_server.mgr.hotfix_mgr import hot_fix_mgr
from project.x5m.test.deploy_private_server.mgr.prepare_environment_mgr import prepare_environment_mgr
from project.x5m.test.deploy_private_server.mgr.server_package_mgr import server_package_mgr

WORKDIR = r"/data/workspace/x5_mobile/mobile_dancer/trunk/exe"


@advance.stage(stage="关服")
def stop_all_server(**kwargs):
    ret = cmd.run_shell(cmds=["bash ./stop_allserver.sh"], workdir=WORKDIR)
    if ret[0] != 0:
        log.error("stop all server failed!")
        raise PyframeException("关服失败")


@advance.stage(stage="解析参数")
def analytic_parameter(**kwargs):
    db_name = bk_env_mgr.get_db_name()
    sub_db_name = bk_env_mgr.get_sub_db_name()
    prepare_environment_mgr.analytic_parameter(db_name=db_name, sub_db_name=sub_db_name)
    server_package = bk_env_mgr.get_server_package()
    if not server_package.startswith("dgm_server") and server_package:
        env_mgr.set_is_special_branch(is_special_branch=True)
    else:
        env_mgr.set_is_special_branch(is_special_branch=False)


@advance.stage(stage="下载并解压服务器包和脚本包")
def download_server_package_from_nexus(**kwargs):
    artifacts_branch = bk_env_mgr.get_artifacts_branch()
    server_package = bk_env_mgr.get_server_package()
    if "dgm_server" in server_package:
        max_pipeline_id = server_package.split("_")[-1].split(".")[0]
        special_branch = server_package.split("_dgm")[0]
    else:
        max_pipeline_id = server_package_mgr.get_max_pipeline_id(artifacts_branch=artifacts_branch)
        special_branch = ""
    log.info(max_pipeline_id)
    env_mgr.set_max_pipeline_id(max_pipeline_id=max_pipeline_id)
    env_mgr.set_special_branch(special_branch=special_branch)
    # 下载服务器包和脚本包
    server_package_mgr.download_server_package(max_pipeline_id=max_pipeline_id, artifacts_branch=artifacts_branch)
    server_package_mgr.download_script_package(max_pipeline_id=max_pipeline_id, artifacts_branch=artifacts_branch)
    # 解压脚本文件
    server_package_mgr.unzip_script_package(max_pipeline_id=max_pipeline_id)


@advance.stage(stage="修改数据库")
def change_db(**kwargs):
    global_name = env_mgr.get_global_db_name()
    main_name = env_mgr.get_main_db_name()
    sub_name = bk_env_mgr.get_sub_db_name()
    private_ip = bk_env_mgr.get_private_server_ip()
    # 修改数据库
    config_mgr.update_db_config(global_name=global_name, main_name=main_name, sub_name=sub_name, private_ip=private_ip)


@advance.stage(stage="构造环境")
def construct_environment(**kwargs):
    prepare_environment_mgr.construct_environment()


def __move_config():
    """
    覆盖ip_config.csv和region_list_config.xml文件
    """
    # 删除原文件
    path_mgr.rm(path="/data/depot/make_dev_files/ip_config.csv")
    path_mgr.rm(path="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/ip_config.csv")
    path_mgr.rm(path="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/resources/config/server/op/region_list_config.xml")
    # 复制新文件到所需位置
    path_mgr.copy(src="/data/workspace/ip_config.csv", dst="/data/depot/make_dev_files/ip_config.csv")
    path_mgr.copy(src="/data/workspace/ip_config.csv", dst="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/ip_config.csv")
    path_mgr.copy(
        src="/data/workspace/region_list_config.xml",
        dst="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/resources/config/server/op/region_list_config.xml",
    )


@advance.stage(stage="部署安装")
def deploy_install(**kwargs):
    # 替换配置文件
    if common.get_host_ip() == "*************" or common.get_host_ip() == "*************":
        __move_config()

    max_pipeline_id = env_mgr.get_max_pipeline_id()
    week_config = bk_env_mgr.get_week_config()
    deploy_install_mgr.deploy_install(max_pipeline_id=max_pipeline_id, week_config=week_config)


@advance.stage(stage="分发热更文件")
def download_ftp_hotfix(**kwargs):
    config_path = bk_env_mgr.get_config_path()
    hot_fix_mgr.download_ftp_hotfix(config_path=config_path)


@advance.stage(stage="现网版本刷热更")
def current_version_brush_hotfix(**kwargs):
    config_path = bk_env_mgr.get_config_path()
    # config_path = "release"
    hot_fix_mgr.current_version_brush_hotfix(config_path=config_path)


@advance.stage(stage="刷cdn配置")
def download_ftp_cdn_config(**kwargs):
    # 下载CDN CONFIG
    server_package = bk_env_mgr.get_server_package()
    pattern = re.compile(r"^\d+\.\d+\.\d+_unity_dgm_server_\d+\.zip$")
    re.findall(pattern, server_package)
    cdn_mgr.download_cdn_config()
    # 如果是unity相关的特殊分支，则使用unity2022的CDN配置
    # if ret:
    #     cdn_mgr.download_cdn_config_from_150_46()
    # elif not bk_env_mgr.get_artifacts_branch() in {"release", "online"}:
    #     cdn_mgr.download_cdn_config_from_150_46()
    # else:
    #     cdn_mgr.download_cdn_config()
    # 解压 更新CDN_CONFIG
    cdn_mgr.update_cdn_config()


@advance.stage(stage="修改gcloud配置")
def update_gcloud_config(**kwargs):
    config_mgr.update_gcloud_config()


@advance.stage(stage="关闭支付开关")
def close_pay(**kwargs):
    config_mgr.close_pay()


@advance.stage(stage="同步数据库")
def sync_db(**kwargs):
    # 替换配置文件
    if common.get_host_ip() == "*************" or common.get_host_ip() == "*************":
        __move_config()

    script_path = ""
    exe_path = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe/"
    main_deploy_ip = bk_env_mgr.get_private_server_ip()
    sub_deploy_ip = ""
    config_mgr.sync_db_config(
        script_path=script_path, exe_path=exe_path, main_deploy_ip=main_deploy_ip, sub_deploy_ip=sub_deploy_ip, branch_name="empty"
    )


@advance.stage(stage="开启server all")
def start_server_all(**kwargs):
    server_slave = bk_env_mgr.get_server_slave()
    ret = cmd.run_shell(
        cmds=["bash ./start_allserver.sh"],
        workdir=WORKDIR,
        dot_kill_me=True,
    )
    if ret[0] != 0:
        log.error("start_allserver.sh failed")
        raise PyframeException("start_allserver.sh执行失败")

    ret = cmd.run_shell(
        cmds=["bash ./start_global_server.sh"],
        workdir=WORKDIR,
        dot_kill_me=True,
    )
    if ret[0] != 0:
        log.error("start_global_server.sh failed")
        raise PyframeException("start_global_server.sh执行失败")

    if server_slave == "联服":
        ret = cmd.run_shell(cmds=["bash ./start_allserver_slave.sh"], workdir=WORKDIR)
        if ret[0] != 0:
            log.error("start_allserver_slave.sh failed")
            raise PyframeException("start_allserver_slave.sh执行失败")


@advance.stage(stage="检查进行运行")
def check_process(**kwargs):
    if proc_mgr.exists_proc_by_param("lobby"):
        log.info("lobby is running")
    else:
        log.error("lobby is not running")
        # raise PyframeException("lobby is not running")


def check_core(**kwargs):
    core_mgr = CoreMgr()
    core_mgr.monitor()


def __get_msg():
    max_pipeline_id = env_mgr.get_max_pipeline_id()
    diff_cores = env_mgr.get_diff_cores()
    gdb_url = env_mgr.get_gdb_url()
    msg = "**服务器包**: dgm_server_{}.zip".format(max_pipeline_id)
    msg += "\n**脚本包**: dgm_sh_{}.zip".format(max_pipeline_id)
    if diff_cores:
        diff_data = []
        for d in diff_cores:
            create_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime(Path(d).stat().st_ctime))
            d = "{}-{}".format(d, create_time)
            diff_data.append(d)
        msg += "\n**本次部署有core文件产生** \n"
        msg += "**core文件解析:** \n"
        for d in range(len(diff_cores)):
            msg += "[{}]({})\n".format(diff_data[d], gdb_url[d])
    return msg


def post_success(**kwargs: dict):
    wechat.send_unicast_post_success(user_list=[], content=__get_msg())
    advance.insert_pipeline_history_on_success()


def post_failure(**kwargs: dict):
    wechat.send_unicast_post_failure(
        user_list=[],
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_failure()


def post_canceled(**kwargs: dict):
    wechat.send_unicast_post_canceled(user_list=[], content=__get_msg())
    advance.insert_pipeline_history_on_canceled()
