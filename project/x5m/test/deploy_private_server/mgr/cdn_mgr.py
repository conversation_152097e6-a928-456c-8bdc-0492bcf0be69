# coding=utf-8
from frame import path_mgr, FtpMgr, tar
from project.x5m import config


class CdnMgr:
    def __init__(self):
        self.__WORKDIR = r"/data/workspace/x5_mobile/mobile_dancer/trunk/exe"

    def download_cdn_config(self):
        """
        下载CDN CONFIG
        """
        ftp = FtpMgr(
            ip=config.FTP_CONFIG_150_30["ip"],
            port=config.FTP_CONFIG_150_30["port"],
            username=config.FTP_CONFIG_150_30["username"],
            password=config.FTP_CONFIG_150_30["password"],
        )
        ftp.download_file(
            src="/version_test/update_config/fix_cdn_config/cdn_config.zip", dst="/data/depot/update_config/fix_cdn_config/cdn_config.zip"
        )

    def download_cdn_config_from_150_46(self):
        """
        从*************下载CDN CONFIG
        """
        ftp = FtpMgr(
            ip=config.FTP_CONFIG_150_46["ip"],
            port=config.FTP_CONFIG_150_46["port"],
            username=config.FTP_CONFIG_150_46["username"],
            password=config.FTP_CONFIG_150_46["password"],
        )
        ftp.download_file(
            src="/version_test/update_config/fix_cdn_config/cdn_config.zip", dst="/data/depot/update_config/fix_cdn_config/cdn_config.zip"
        )

    def update_cdn_config(self):
        """
        更新CDN_CONFIG
        """
        tar.decompress(src="/data/depot/update_config/fix_cdn_config/cdn_config.zip", dst="/data/depot/update_config/fix_cdn_config/")
        path_mgr.copy(src="/data/depot/update_config/fix_cdn_config/test", dst=f"/data/workspace/x5_mobile/mobile_dancer/trunk/exe")
        path_mgr.rm("/data/depot/update_config/fix_cdn_config/cdn_config.zip")


cdn_mgr = CdnMgr()
