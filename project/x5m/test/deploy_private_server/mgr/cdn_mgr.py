# coding=utf-8
from frame import path_mgr, FtpMgr, tar
from project.x5m import config
from project.x5m.test.deploy_private_server.mgr.env_mgr import bk_env_mgr


class CdnMgr:
    def __init__(self):
        pass

    def download_ftp_cdn_config(self):
        """
        分发CDN CONFIG
        """

        src = "/version_test/update_config/fix_cdn_config/cdn_config.zip"
        dst = "/data/depot/update_config/fix_cdn_config/cdn_config.zip"

        path_mgr.rm(path="/data/depot/update_config/fix_cdn_config")
        path_mgr.mkdir(path="/data/depot/update_config/fix_cdn_config")
        if bk_env_mgr.is_trad():
            ftp = FtpMgr(**config.FTP_CONFIG_150_46)
        else:
            ftp = FtpMgr(**config.FTP_CONFIG_150_30)
        ftp.download_file(src=src, dst=dst)
        tar.decompress(src="/data/depot/update_config/fix_cdn_config/cdn_config.zip", dst="/data/depot/update_config/fix_cdn_config/")
        path_mgr.copy(src="/data/depot/update_config/fix_cdn_config/test", dst=f"/data/workspace/x5_mobile/mobile_dancer/trunk/exe")
        path_mgr.rm("/data/depot/update_config/fix_cdn_config/cdn_config.zip")

    def download_ftp_h5_config(self):
        """
        分发H5 CONFIG
        """
        src = "/version_test/update_config/h5_config/h5_res_list.csv"
        dst = "/data/depot/update_config/h5_config/h5_res_list.csv"

        path_mgr.rm(path="/data/depot/update_config/h5_config")
        path_mgr.mkdir(path="/data/depot/update_config/h5_config")
        ftp = FtpMgr(**config.FTP_CONFIG_150_30)
        ftp.download_file(src=src, dst=dst)
        path_mgr.copy(
            src="/data/depot/update_config/h5_config/h5_res_list.csv",
            dst=f"/data/workspace/x5_mobile/mobile_dancer/trunk/exe/resources/config/server/h3d",
        )
        path_mgr.rm(dst)


cdn_mgr = CdnMgr()
