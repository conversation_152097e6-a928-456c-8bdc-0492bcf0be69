import time
from frame import *
from project.x5m.test.deploy_private_server.mgr.env_mgr import env_mgr


class CoreMgr:
    def __init__(self):
        pass

    def __write_file(self, path: str, core_files: list):
        """
        将获取到的core写入文件
        Args:
            path: 待写入的历史文件路径
            core_files: 历史core列表
        """
        file = open(path, "w")
        file.write(str(core_files))
        file.close()

    def __get_current_cores(self, path: str) -> list:
        """
        获取某路径下的所有core
        Args:
            path: core文件地址，一般是exe/bin
        Returns:
            list: 机器上当前core集合
        """
        cores = [str(c.absolute()) for c in Path(path).glob("core.*")]
        log.info("local cores:{}".format(cores))
        return cores

    def __get_history_cores(self, path: str) -> list:
        """
        读取上次产生的core历史
        Args:
            path: 历史core文件的路径, core_files.txt
        Returns:
            list: 历史core集合
        """
        # 调试用
        # path_mgr.rm(path)
        old_cores = []
        try:
            file = open(path, encoding="utf-8")
            old_cores = file.read()
            file.close()
        except IOError as e:
            log.error(e)
        log.info("old cores:{}".format(old_cores))
        return old_cores

    def __diff_new_cores(self, history: list, current: list) -> list:
        """
        对比出新增的core
        Args:
            history: 历史core集合
            current: 机器上当前的core

        Returns:
            set: 新增的core集合
        """
        new_cores = [c for c in current if c not in history]
        log.info("new cores:{}".format(new_cores))
        return new_cores

    def __upload_nexus(self, src: str, dst: str):
        """
        将存放gdb解析的堆栈文件上传制品库
        Args:
            src: core文件名
            dst: 文件父路径
        """
        username = "productivity-robot"
        password = "productivity-robot"
        nexus = Nexus(username=username, password=password)
        nexus.upload(src=src, dst=dst)

    def __parse_stack(self, diff: list, path: str) -> list:
        """
        解析堆栈信息并写入文件
        Args:
            diff: 新增的core集合
        """
        urls = []
        for d in diff:
            create_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime(Path(d).stat().st_ctime))
            gdb_name = "gdb-{}-{}.txt".format(d.split("/")[-1], create_time)
            cmd.run_shell(cmds=["gdb svc_launch {} -ex bt -ex q > {}".format(d, gdb_name)], workdir=path)
            src = os.path.join(path, gdb_name)
            # dst = 'http://nexus.h3d.com.cn/repository/dev-productivity/x5m/gdbcore/{}'.format(gdb_name)
            # self.__upload_nexus(src=src, dst=dst)
            dst = advance.upload_pipeline_log(path=src)
            urls.append(dst)
        cmd.run_shell(cmds=["rm -rvf gdb-core*"], workdir=path)
        return urls

    def monitor(self):
        try:
            if proc_mgr.exists_proc("gdb"):
                log.warn("gdb is running, return")
                return
        except Exception as e:
            log.warn(e)
            return
        custom_paths = ["/data/workspace/x5_mobile/mobile_dancer/trunk/exe/bin", "/data/workspace/x5mobile/mobile_dancer/trunk/exe/bin"]
        for bin_path in custom_paths:
            # bin_path = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe/bin"
            p = Path(bin_path)
            if not p.exists():
                log.warn("{} path not exist".format(p.absolute()))
                continue

            txt_path = os.path.join(bin_path, "core_files.txt")
            # 获取历史的core
            history_cores = self.__get_history_cores(path=txt_path)
            # 获取当前的core
            current_cores = self.__get_current_cores(path=bin_path)
            # 将获取的core文件名写入.txt
            self.__write_file(path=txt_path, core_files=current_cores)
            # 对比找到新增的core
            diff_cores = self.__diff_new_cores(history=history_cores, current=current_cores)
            # 微信通知，通知内容：新增的core名称
            if len(diff_cores) > 0:
                url = self.__parse_stack(diff=diff_cores, path=bin_path)
                env_mgr.set_diff_cores(diff_cores=diff_cores)
                env_mgr.set_gdb_url(gdb_url=url)
