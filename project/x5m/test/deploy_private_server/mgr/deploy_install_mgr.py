# coding=utf-8
from frame import cmd, log
from frame.exception.exception import PyframeException


class DeployInstallMgr:
    def __init__(self):
        self.__WORKDIR = r"/data/workspace/x5_mobile/mobile_dancer/trunk/exe"

    def deploy_install(self, max_pipeline_id: str, week_config: str):
        """
        部署安装
        """
        config = "release"
        if week_config == "resources":
            config = "resources"
        elif week_config == "release":
            config = "release"
        elif week_config == "online":
            config = "online"
        else:
            log.info("week_config input error")
        log.info("choose config:{}".format(config))
        ret = cmd.run_shell(cmds=["bash ./remote_install_whitebox.sh {} {}".format(max_pipeline_id, config)], workdir=self.__WORKDIR)
        if ret[0] != 0:
            log.error("remote_install_whitebox.sh failed")
            raise PyframeException("remote_install_whitebox.sh执行失败")


deploy_install_mgr = DeployInstallMgr()
