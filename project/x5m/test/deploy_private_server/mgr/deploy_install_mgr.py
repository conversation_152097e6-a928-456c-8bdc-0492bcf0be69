# coding=utf-8
from frame import cmd, log
from frame.exception.exception import PyframeException
from project.x5m.test.deploy_private_server.mgr.env_mgr import bk_env_mgr


class DeployInstallMgr:
    def __init__(self):
        self.__WORKDIR = r"/data/workspace/x5_mobile/mobile_dancer/trunk/exe"

    def deploy_install(self, max_pipeline_id: str, week_config: str):
        """
        部署安装
        """
        config = "release"
        if bk_env_mgr.is_trad():
            if week_config not in ["release_trad", "resources_trad", "online_trad"]:
                log.error("week_config not in [release_trad, resources_trad, online_trad]")
                raise PyframeException("week_config不存在于[release_trad, resources_trad, online_trad]")
        else:
            if week_config not in ["release", "resources", "online"]:
                log.error("week_config not in [release, resources, online]")
                raise PyframeException("week_config不存在于[release, resources, online]")
        config = week_config
        log.info("choose config:{}".format(config))
        ret = cmd.run_shell(cmds=["bash ./remote_install_whitebox.sh {} {}".format(max_pipeline_id, config)], workdir=self.__WORKDIR)
        if ret[0] != 0:
            log.error("remote_install_whitebox.sh failed")
            raise PyframeException("remote_install_whitebox.sh执行失败")


deploy_install_mgr = DeployInstallMgr()
