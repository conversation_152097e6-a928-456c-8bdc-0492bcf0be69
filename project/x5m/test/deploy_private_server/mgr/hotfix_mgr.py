# coding=utf-8
import os

from frame import path_mgr, FtpMgr, tar
from project.x5m import config


class HotFixMgr:
    def __init__(self):
        self.__WORKDIR = r"/data/workspace/x5_mobile/mobile_dancer/trunk/exe"
        self.ip = config.FTP_CONFIG_150_30["ip"]
        self.username = config.FTP_CONFIG_150_30["username"]
        self.password = config.FTP_CONFIG_150_30["password"]
        self.port = config.FTP_CONFIG_150_30["port"]

    def download_ftp_hotfix(self, config_path: str):
        """
        分发热更文件
        """
        src = f"/version_test/update_config/{config_path}/"
        dst = f"/data/depot/{config_path}"
        ftp = FtpMgr(**config.FTP_CONFIG_150_30)
        ftp.download_folder(src=src, dst=dst)

    def current_version_brush_hotfix(self, config_path: str):
        """
        现网版本刷热更
        """
        files = os.listdir(os.path.join("/data/depot", config_path))
        dest_path = os.path.join("/data/depot/update_config", config_path)
        for file in files:
            zip_file = "/data/depot/" + config_path + "/" + file
            tar.decompress(src=zip_file, dst=dest_path)
        src_dir = os.path.join(dest_path, "test")
        des_dir = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe"
        # 拷贝文件夹
        path_mgr.copy(src_dir, des_dir)
        path_mgr.rm("/data/depot/" + config_path)


hot_fix_mgr = HotFixMgr()
