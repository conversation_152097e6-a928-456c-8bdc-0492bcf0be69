# coding=utf-8
from frame import cmd, log, path_mgr, Nexus, common, FtpMgr, env
from frame.exception.exception import PyframeException
from project.x5m import config
from project.x5m.test.deploy_private_server.mgr.env_mgr import env_mgr


class ServerPackageMgr:
    def __init__(self):
        self.__local_path = "/data/depot/make_dev_files"
        self.__ftp = FtpMgr(**config.FTP_CONFIG_150_30)

    def get_max_pipeline_id(self, artifacts_branch: str) -> int:
        """
        获取最大的pipeline_id
        Args:
            artifacts_branch: 制品库的分支
        Returns:
            int: 最大的pipeline_id
        """
        nexus = Nexus(**config.NEXUS_CONFIG)
        items = nexus.search(
            name=f"stag/server_pack/{artifacts_branch}/dgm_server_*.zip", repository="mobile", group=f"/stag/server_pack/{artifacts_branch}"
        )
        log.info(items)
        pipeline_ids = []
        for item in items:
            log.debug(item)
            name = item.get("name").split("/")[-1]
            pipeline_ids.append(int(name.replace(".zip", "").split("_")[-1]))
        return max(pipeline_ids)

    def download_server_package(self, max_pipeline_id: str, artifacts_branch: str):
        """
        从制品库下载服务器包
        """
        path_mgr.mkdir("/data/depot")
        is_special_branch = env_mgr.get_is_special_branch()
        branch = env_mgr.get_special_branch()
        nexus = Nexus(**config.NEXUS_CONFIG)
        if common.is_true(is_special_branch):
            src = f"http://nexus.h3d.com.cn/repository/mobile/stag/server_pack/special_branch/{branch}_dgm_server_{max_pipeline_id}.zip"
        else:
            src = f"http://nexus.h3d.com.cn/repository/mobile/stag/server_pack/{artifacts_branch}/dgm_server_{max_pipeline_id}.zip"
        dst = f"/data/depot/dgm_server_{max_pipeline_id}.zip"
        try:
            nexus.download(src=src, dst=dst)
        except PyframeException as e:
            log.error(e)
            message = env.get("PYFRAME_EXCEPTION_MESSAGE")
            if "制品库上不存在" in message:
                # 如果制品库上不存在服务器包，则从FTP下载
                self.download_server_package_from_ftp(max_pipeline_id, artifacts_branch)
            else:
                raise e

    def download_server_package_from_ftp(self, max_pipeline_id: str, artifacts_branch: str):
        """
        从FTP下载服务器包
        Args:
            max_pipeline_id:
            artifacts_branch:

        Returns:

        """
        is_special_branch = env_mgr.get_is_special_branch()
        branch = env_mgr.get_special_branch()
        if common.is_true(is_special_branch):
            src = f"/version_test/server_pack/special_branch/{branch}_dgm_server_{max_pipeline_id}.zip"
        else:
            src = f"/version_test/server_pack/{artifacts_branch}/dgm_server_{max_pipeline_id}.zip"
        dst = f"/data/depot/dgm_server_{max_pipeline_id}.zip"
        self.__ftp.download_file(src=src, dst=dst)

    def download_script_package(self, max_pipeline_id: str, artifacts_branch: str):
        """
        从制品库下载脚本包
        """
        is_special_branch = env_mgr.get_is_special_branch()
        branch = env_mgr.get_special_branch()
        nexus = Nexus(**config.NEXUS_CONFIG)
        if common.is_true(is_special_branch):
            src = f"http://nexus.h3d.com.cn/repository/mobile/stag/server_pack/special_branch/{branch}_dgm_sh_{max_pipeline_id}.zip"
        else:
            src = f"http://nexus.h3d.com.cn/repository/mobile/stag/server_pack/{artifacts_branch}/dgm_sh_{max_pipeline_id}.zip"
        dst = f"/data/depot/dgm_sh_{max_pipeline_id}.zip"
        try:
            nexus.download(src=src, dst=dst)
        except PyframeException as e:
            log.error(e)
            message = env.get("PYFRAME_EXCEPTION_MESSAGE")
            if "制品库上不存在" in message:
                # 如果制品库上不存在服务器包，则从FTP下载
                self.download_script_package_from_ftp(max_pipeline_id, artifacts_branch)
            else:
                raise e

    def download_script_package_from_ftp(self, max_pipeline_id: str, artifacts_branch: str):
        """
        从FTP下载脚本包
        Args:
            max_pipeline_id:
            artifacts_branch:

        Returns:

        """
        is_special_branch = env_mgr.get_is_special_branch()
        branch = env_mgr.get_special_branch()
        if common.is_true(is_special_branch):
            src = f"/version_test/server_pack/special_branch/{branch}_dgm_sh_{max_pipeline_id}.zip"
        else:
            src = f"/version_test/server_pack/{artifacts_branch}/dgm_sh_{max_pipeline_id}.zip"
        dst = f"/data/depot/dgm_sh_{max_pipeline_id}.zip"
        self.__ftp.download_file(src=src, dst=dst)

    def unzip_script_package(self, max_pipeline_id):
        """
        解压脚本文件
        """
        # 如果 make_dev_files 目录已经存在 先删除它
        local_path = self.__local_path
        path_mgr.rm(path=local_path)
        # 重新创建 make_dev_files 目录
        path_mgr.mkdir(path=local_path)
        # 移动 dgm_sh_{max_pipeline_id}.zip
        path_mgr.move(f"/data/depot/dgm_sh_{max_pipeline_id}.zip", f"{local_path}/dgm_sh.zip")
        # 解压 dgm_sh.zip
        ret = cmd.run_shell(cmds=["unzip dgm_sh.zip"], workdir=local_path)
        if ret[0] != 0:
            log.error("unzip dgm_sh.zip failed!")
            raise PyframeException("解压dgm_sh.zip失败")


server_package_mgr = ServerPackageMgr()
