# coding=utf-8
import json
import re
import time

import requests
from frame import *
from project.x5m.test.deploy_public_server.mgr.cdn_mgr import cdn_mgr
from project.x5m.test.deploy_public_server.mgr.config_mgr import config_mgr
from project.x5m.test.deploy_public_server.mgr.hotfix_mgr import hotfix_mgr
from project.x5m.test.deploy_public_server.mgr.new_gitlab_mgr import new_gitlab_mgr
from project.x5m.test.deploy_public_server.mgr.params_mgr import params_mgr
from project.x5m.test.deploy_public_server.mgr.server_mgr import server_mgr

WORKDIR = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe"


@advance.stage(stage="判断是否为特殊分支")
def prepare(**kwargs):
    branch = env.get("BRANCH")
    if re.compile(r".*[0-9]$").match(branch) or branch == "master":
        is_special_branch = False
    else:
        is_special_branch = True
    env.set({"is_special_branch": is_special_branch})


@advance.stage(stage="克隆x5mobile(编译机)")
def clone_x5mobile_on_compiler(**kwargs):
    advance.check_disk_size(threshold=5)
    branch = env.get("BRANCH")
    new_gitlab_mgr.get_max_pipeline_id(branch=branch)
    new_gitlab_mgr.get_commit_id(branch=branch)
    new_gitlab_mgr.clone_x5mobile(branch=branch)


@advance.stage(stage="编译")
def build(**kwargs):
    now = time.time()
    try:
        server_mgr.build()
    finally:
        duration = common.format_duration(time.time() - now)
        env.set({"build_duration": duration})


@advance.stage(stage="替換配置")
def update_config(**kwargs):
    config_mgr.update_config()


@advance.stage(stage="打包")
def package(**kwargs):
    server_mgr.package()


@advance.stage(stage="上传服务器包到FTP")
def upload_ftp_server_package(**kwargs):
    max_pipeline_id = env.get("max_pipeline_id")
    artifacts_branch = env.get("artifacts_branch")
    server_mgr.upload_ftp_server_package(max_pipeline_id=max_pipeline_id, artifacts_branch=artifacts_branch)


@advance.stage(stage="上传服务器包到nexus")
def upload_nexus_server_package(**kwargs):
    artifacts_branch = env.get("artifacts_branch")
    # 获取 week_config
    max_pipeline_id = env.get("max_pipeline_id")
    server_mgr.upload_nexus_server_package(max_pipeline_id=max_pipeline_id, artifacts_branch=artifacts_branch)


@advance.stage(stage="调起白盒打镜像")
def white_box_build(**kwargs):
    ref = env.get("BRANCH")
    max_pipeline_id = env.get("max_pipeline_id")
    server_pack = "dgm_server_{}.zip".format(max_pipeline_id)
    headers = {
        "Content-Type": "application/json",
    }
    params = {
        "token": "x5m_server_build_git",
    }
    data = {"ref": ref, "server_binary_zip_pack_file_name": server_pack}
    # 新流水线
    response = requests.post(
        url="http://jenkins-testdev.h3d.com.cn/generic-webhook-trigger/invoke", params=params, headers=headers, data=json.dumps(data)
    )
    log.info(response.text)
    log.info(response.status_code)


@advance.stage(stage="更新参数")
def update_server_package_params(**kwargs):
    """
    更新参数信息
    """
    artifacts_branch = env.get("artifacts_branch")
    params_mgr.update_server_package_params(artifacts_branch=artifacts_branch)


@advance.stage(stage="克隆x5mobile(部署机)")
def clone_x5mobile_on_server(**kwargs):
    branch = env.get("BRANCH")
    new_gitlab_mgr.clone_x5mobile(branch=branch)


@advance.stage(stage="踢人")
def kick_player(**kwargs):
    config_mgr.kick_player()


@advance.stage(stage="关服")
def shutdown_server(**kwargs):
    server_mgr.stop_server()


@advance.stage(stage="下载server包")
def download_ftp_server_package(**kwargs):
    # 获取三个项目中最大的 max_pipeline_id
    max_pipeline_id = env.get("max_pipeline_id")
    artifacts_branch = env.get("artifacts_branch")
    server_mgr.download_ftp_server_package(max_pipeline_id=max_pipeline_id, artifacts_branch=artifacts_branch)
    # 解压server安装包
    server_mgr.unzip_server_package(max_pipeline_id=max_pipeline_id)


def __move_config():
    """
    覆盖ip_config.csv和region_list_config.xml文件
    """
    # 删除原文件
    path_mgr.rm(path="/data/depot/make_dev_files/ip_config.csv")
    path_mgr.rm(path="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/ip_config.csv")
    path_mgr.rm(path="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/resources/config/server/op/region_list_config.xml")
    # 复制新文件到所需位置
    path_mgr.copy(src="/data/workspace/ip_config.csv", dst="/data/depot/make_dev_files/ip_config.csv")
    path_mgr.copy(src="/data/workspace/ip_config.csv", dst="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/ip_config.csv")
    path_mgr.copy(
        src="/data/workspace/region_list_config.xml",
        dst="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/resources/config/server/op/region_list_config.xml",
    )


@advance.stage(stage="部署安装")
def deploy_install(**kwargs):
    # 替换配置文件
    if common.get_host_ip() == "*************" or common.get_host_ip() == "*************":
        __move_config()

    max_pipeline_id = env.get("max_pipeline_id")
    week_config = env.get("week_config")
    if week_config not in ["release", "resources", "online"]:
        log.error("week_config not in [release, resources, online]")
        raise PyframeException("week_config不存在于[release, resources, online]")
    ret = cmd.run_shell(cmds=["bash ./remote_install_whitebox.sh {} {}".format(max_pipeline_id, week_config)], workdir=WORKDIR)
    if ret[0] != 0:
        log.error("remote_install_whitebox.sh failed")
        raise PyframeException("remote_install_whitebox.sh执行失败, 请联系管理员")


@advance.stage(stage="分发热更文件")
def download_ftp_hotfix(**kwargs):
    branch = env.get("BRANCH")
    config_path = env.get("config_path")
    weekly_version = env.get("weekly_version")
    hotfix_mgr.download_ftp_hotfix(config_path=config_path, weekly_version=weekly_version, branch=branch)


@advance.stage(stage="现网版本刷热更")
def current_version_brush_hotfix(**kwargs):
    config_path = env.get("config_path")
    hotfix_mgr.current_version_brush_hotfix(config_path=config_path)


@advance.stage(stage="分发CDN配置")
def download_ftp_cdn_config(**kwargs):
    cdn_mgr.download_ftp_cdn_config()


@advance.stage(stage="修改gcloud配置")
def update_gcloud_config(**kwargs):
    config_mgr.update_gcloud_config()


@advance.stage(stage="同步数据库")
def sync_db(**kwargs):
    # 替换配置文件
    if common.get_host_ip() == "*************" or common.get_host_ip() == "*************":
        __move_config()
    config_mgr.sync_db()


@advance.stage(stage="start_all_server")
def start_all_server(**kwargs):
    server_mgr.start_all_server()


@advance.stage(stage="start_global_server")
def start_global_server(**kwargs):
    server_mgr.start_global_server()


def __get_committer():
    errors = env.get("errors")
    errors_with_committer = []
    committers = []
    if errors:
        for error in errors:
            try:
                error = error.replace("\n", "")
                error_path = error.split(":")[0]
                glob_path = "**/" + error_path.lstrip("../").strip()
                line_num = error.split(":")[1]
                glob_paths = Path("/data/workspace/x5_mobile/mobile_dancer/trunk/server").glob(glob_path)
                for path in glob_paths:
                    committer, commit_time = new_gitlab_mgr.get_x5mobile_committer(path=str(path), line=int(line_num))
                    now = time.time()
                    error = error.replace(error_path, str(path).replace("/data/workspace/x5_mobile/mobile_dancer/trunk/", ""))
                    # 3天内的错误才会通知
                    if int(now - float(commit_time)) < 60 * 60 * 24 * 3:
                        committers.append(committer)
                        errors_with_committer.append(f"{error} <@{committer}>")
            except Exception as e:
                log.error(e)
                continue
    env.set({"COMMITTERS": committers, "ERRORS_WITH_COMMITTER": errors_with_committer})


def __get_msg():
    build_server_ip = env.get("build_server")
    main_server_ip = env.get("main_server")
    sub_server_ip = env.get("sub_server")
    max_pipeline_id = env.get("max_pipeline_id")
    hotfix_file = env.get("hotfix_file")
    branch = env.get("BRANCH")
    package_link = env.get("package_link")
    server_pack = f"dgm_server_{max_pipeline_id}.zip"
    sh_pack = f"dgm_sh_{max_pipeline_id}.zip"
    is_special_branch = env.get("is_special_branch")
    build_duration = env.get("build_duration")
    msg = f"**编译机**: {build_server_ip}"
    msg += f"\n**主服务器**: {main_server_ip}"
    msg += f"\n**从服务器**: {sub_server_ip}"
    msg += f"\n**当前分支**: {branch}"
    msg += f"\n**编译时长**: {build_duration}"
    if hotfix_file is not None:
        msg += f"\n**热更包**: {hotfix_file}"
    if package_link:
        if common.is_true(is_special_branch):
            server_package_url = common.join_url("https://dgmdd.h3d.com.cn/dgm", package_link, f"{branch}_{server_pack}")
            sh_package_url = common.join_url("https://dgmdd.h3d.com.cn/dgm", package_link, f"{branch}_{sh_pack}")
        else:
            server_package_url = common.join_url("https://dgmdd.h3d.com.cn/dgm", package_link, server_pack)
            sh_package_url = common.join_url("https://dgmdd.h3d.com.cn/dgm", package_link, sh_pack)
        log.debug(f"server_package_url: {server_package_url}")
        log.debug(f"sh_package_url: {sh_package_url}")
        msg += f"\n**服务器包**: [{server_pack}]({server_package_url})"
        msg += f"\n**sh脚本包**: [{sh_pack}]({sh_package_url})"
    errors_with_committer = env.get("ERRORS_WITH_COMMITTER")
    if errors_with_committer is not None:
        if len(errors_with_committer) > 0:
            msg += "**错误定位**: \n"
            msg += "\n".join(errors_with_committer)

    build_version = env.get("build_version")
    msg += f"\n**编译类型:** {build_version}\n" if build_version else ""
    is_rebuild = env.get("is_rebuild")
    msg += f"\n**是否全量编译**: {is_rebuild}\n" if is_rebuild else ""
    return msg


def __get_user_list():
    user_list = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ]
    return user_list


def post_success(**kwargs: dict):
    userlist = __get_user_list()
    commiters = env.get("COMMITTERS")
    if commiters:
        userlist += commiters
    wechat.send_unicast_post_success(user_list=userlist, content=__get_msg())
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=70aed93f-22c8-4d81-b4f7-27076de1ce36", content=__get_msg()
    )
    advance.insert_pipeline_history_on_success()


def post_failure(**kwargs: dict):
    userlist = __get_user_list()
    if env.get_failure_stage() == "编译":
        userlist.extend(["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"])
    commiters = env.get("COMMITTERS")
    if commiters:
        userlist += commiters
    wechat.send_unicast_post_failure(user_list=userlist, content=__get_msg(), to_admin=False)
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=70aed93f-22c8-4d81-b4f7-27076de1ce36", content=__get_msg(), rescue=False
    )
    advance.insert_pipeline_history_on_failure()


def post_canceled(**kwargs: dict):
    userlist = __get_user_list()
    commiters = env.get("COMMITTERS")
    if commiters:
        userlist += commiters
    wechat.send_unicast_post_canceled(user_list=userlist, content=__get_msg())
    wechat.send_multicast_post_canceled(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=70aed93f-22c8-4d81-b4f7-27076de1ce36", content=__get_msg()
    )
    advance.insert_pipeline_history_on_canceled()
