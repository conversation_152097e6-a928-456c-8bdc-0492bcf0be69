# coding=utf-8
from frame import FtpMgr, tar, path_mgr
from project.x5m import config


class CdnMgr:
    def __init__(self):
        pass

    def download_ftp_cdn_config(self):
        """
        分发CDN CONFIG
        """

        src = "/version_test/update_config/fix_cdn_config/cdn_config.zip"
        dst = "/data/depot/update_config/fix_cdn_config/cdn_config.zip"

        path_mgr.rm(path="/data/depot/update_config/fix_cdn_config")
        path_mgr.mkdir(path="/data/depot/update_config/fix_cdn_config")
        ftp = FtpMgr(**config.FTP_CONFIG_150_30)
        ftp.download_file(src=src, dst=dst)
        tar.decompress(src="/data/depot/update_config/fix_cdn_config/cdn_config.zip", dst="/data/depot/update_config/fix_cdn_config/")
        path_mgr.copy(src="/data/depot/update_config/fix_cdn_config/test", dst=f"/data/workspace/x5_mobile/mobile_dancer/trunk/exe")
        path_mgr.rm("/data/depot/update_config/fix_cdn_config/cdn_config.zip")


cdn_mgr = CdnMgr()
