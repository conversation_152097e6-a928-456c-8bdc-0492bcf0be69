import datetime
import os
from pathlib import Path

from lxml import etree

from frame import log, cmd, env, PyframeException, path_mgr


class ConfigMgr:
    def __init__(self):
        self.WORKDIR = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe"

    def __git_clone_code(self, branch: str, remote_repo: str, root_path: str, commit_id: str):
        if not os.path.exists(path=os.path.join(root_path, branch)):
            ret = cmd.run_shell(cmds=[f"git clone -b {branch} --single-branch {remote_repo} {root_path}/{branch}"])
            if ret[0] != 0:
                log.error(f"git clone {remote_repo} failed")
                raise PyframeException(f"拉取{remote_repo}仓库失败")
        ret = cmd.run_shell(cmds=["git fetch", f"git reset --hard {commit_id}", "git clean -xdf"], workdir=os.path.join(root_path, branch))
        if ret[0] != 0:
            log.error(f"change commit failed")
            raise PyframeException(f"切换到指定的commitID失败")

    def update_config(self):
        """
        替換配置
        """
        max_pipeline_id = env.get("max_pipeline_id")
        commit_id_1 = env.get("x5mconfig_commit_id")
        commit_id_2 = env.get("x5mweek_resources_commit_id")
        commit_id_3 = env.get("x5mweek_release_commit_id")
        commit_id_4 = env.get("x5mweek_online_commit_id")
        branch = env.get("BRANCH")
        if not max_pipeline_id:
            log.error("max pipeline id is None!")
            raise PyframeException("max pipelineID 为空")
        config_commit = "HEAD" if not commit_id_1 else commit_id_1
        resources_commit = "HEAD" if not commit_id_2 else commit_id_2
        release_commit = "HEAD" if not commit_id_3 else commit_id_3
        online_commit = "HEAD" if not commit_id_4 else commit_id_4
        # cur_path = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe"
        trunk_path = "/data/workspace/x5_mobile/mobile_dancer/trunk"
        # x5_mobile_remote_repo = "https://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git"
        x5_config_remote_repo = "https://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig.git"
        x5_weekly_remote_repo = "https://x5mobile-gitlab.h3d.com.cn/dgm/x5mweek.git"
        config_repo_root_path = "/data/workspace/x5mconfig"
        weekly_repo_root_path = "/data/workspace/x5mweek"
        # 更新/拉取 config仓库分支
        self.__git_clone_code(branch=branch, remote_repo=x5_config_remote_repo, root_path=config_repo_root_path, commit_id=config_commit)
        # 更新/拉取 weekly仓库分支
        self.__git_clone_code(branch="resources", remote_repo=x5_weekly_remote_repo, root_path=weekly_repo_root_path, commit_id=resources_commit)
        self.__git_clone_code(branch="release", remote_repo=x5_weekly_remote_repo, root_path=weekly_repo_root_path, commit_id=release_commit)
        self.__git_clone_code(branch="online", remote_repo=x5_weekly_remote_repo, root_path=weekly_repo_root_path, commit_id=online_commit)
        # 安装二进制
        path_mgr.cp(
            src=os.path.join(trunk_path, "server/build/third_party/H3DXMLLib/libh3dxml.so"),
            dst=os.path.join(trunk_path, "server/third_party/H3DXMLLib/libh3dxml.so"),
        )
        path_mgr.cp(
            src=os.path.join(trunk_path, "server/third_party/H3DXMLLib/libh3dxml.so"),
            dst=os.path.join(trunk_path, "exe/bin/libh3dxml.so"),
        )
        path_mgr.cp(
            src=os.path.join(trunk_path, "server/third_party/itc/lib_linux64/libitc_client.so"),
            dst=os.path.join(trunk_path, "exe/bin/libitc_client.so"),
        )
        path_mgr.cp(
            src=os.path.join(trunk_path, "server/third_party/tss_uic/lib/libtss_sdk.so"),
            dst=os.path.join(trunk_path, "exe/bin/libtss_sdk.so"),
        )
        path_mgr.cp(
            src=os.path.join(trunk_path, "server/third_party/open_api/outlib/openssl/lib/libcrypto.so.0.9.8"),
            dst=os.path.join(trunk_path, "exe/bin/libcrypto.so.0.9.8"),
        )
        path_mgr.cp(
            src=os.path.join(trunk_path, "server/third_party/tcmalloc/lib/libunwind.so"),
            dst=os.path.join(trunk_path, "exe/bin/libunwind.so"),
        )
        path_mgr.cp(
            src=os.path.join(trunk_path, "server/third_party/tcmalloc/lib/libtcmalloc.so"),
            dst=os.path.join(trunk_path, "exe/bin/libtcmalloc.so"),
        )
        path_mgr.cp(
            src=os.path.join(trunk_path, "server/third_party/tcmalloc/lib/libprofiler.so"),
            dst=os.path.join(trunk_path, "exe/bin/libprofiler.so"),
        )
        # 使用CMake之后，不需要再执行make install
        # ret = cmd.run_shell(cmds=["make install >> ../exe/build_out.txt 2>&1"], workdir=os.path.join(trunk_path, "server"))
        # if ret[0] != 0:
        #     log.error("make install failed")
        #     raise PyframeException("安装二进制失败")
        # 安装分支配置
        path_mgr.rm(path=os.path.join(trunk_path, "exe/resources/level"))
        path_mgr.mkdir(path=os.path.join(trunk_path, "exe/resources/level"))
        path_mgr.rm(path=os.path.join(trunk_path, "exe/resources/experience"))
        path_mgr.mkdir(path=os.path.join(trunk_path, "exe/resources/experience"))
        path_mgr.rm(path=os.path.join(trunk_path, "exe/resources/config"))
        path_mgr.mkdir(path=os.path.join(trunk_path, "exe/resources/config"))
        path_mgr.cp(src=os.path.join(config_repo_root_path, f"{branch}/level"), dst=os.path.join(trunk_path, "exe/resources"), recursive=True)
        path_mgr.cp(src=os.path.join(config_repo_root_path, f"{branch}/experience"), dst=os.path.join(trunk_path, "exe/resources"), recursive=True)
        path_mgr.cp(
            src=os.path.join(config_repo_root_path, f"{branch}/config/server"), dst=os.path.join(trunk_path, "exe/resources/config"), recursive=True
        )
        path_mgr.cp(
            src=os.path.join(config_repo_root_path, f"{branch}/config/shared"), dst=os.path.join(trunk_path, "exe/resources/config"), recursive=True
        )
        path_mgr.cp(
            src=os.path.join(trunk_path, f"client_build_config/config/shared/tencent_xg"),
            dst=os.path.join(trunk_path, "exe/resources/config/shared"),
            recursive=True,
        )
        # 安装weekly配置
        week_src_path = os.path.join(trunk_path, "exe/resources/week_src")

        path_mgr.rm(path=week_src_path)
        path_mgr.mkdir(path=os.path.join(week_src_path, "resources/config/"))
        path_mgr.mkdir(path=os.path.join(week_src_path, "release/config/"))
        path_mgr.mkdir(path=os.path.join(week_src_path, "online/config/"))
        path_mgr.cp(
            src=os.path.join(weekly_repo_root_path, "resources/config/server"), dst=os.path.join(week_src_path, "resources/config"), recursive=True
        )
        path_mgr.cp(
            src=os.path.join(weekly_repo_root_path, "resources/config/shared"), dst=os.path.join(week_src_path, "resources/config"), recursive=True
        )
        path_mgr.cp(
            src=os.path.join(weekly_repo_root_path, "release/config/server"), dst=os.path.join(week_src_path, "release/config"), recursive=True
        )
        path_mgr.cp(
            src=os.path.join(weekly_repo_root_path, "release/config/shared"), dst=os.path.join(week_src_path, "release/config"), recursive=True
        )
        path_mgr.cp(src=os.path.join(weekly_repo_root_path, "online/config/server"), dst=os.path.join(week_src_path, "online/config"), recursive=True)
        path_mgr.cp(src=os.path.join(weekly_repo_root_path, "online/config/shared"), dst=os.path.join(week_src_path, "online/config"), recursive=True)

    def kick_player(self):
        """
        踢人
        """
        xml_path = r"/data/workspace/x5_mobile/mobile_dancer/trunk/exe/resources/config/server/op/shutdown.xml"
        if not Path(xml_path).exists():
            log.warn(f"{xml_path} doesn't exist")
            return
        shut_down_time = (datetime.datetime.now() + datetime.timedelta(minutes=5)).strftime("%Y-%m-%d %H:%M")
        log.info(f"shut_down_time:{shut_down_time}")
        root = etree.parse(xml_path)
        nodes = root.findall("items/item")
        if len(nodes) == 0:
            log.warn("no item")
            raise PyframeException("踢人失败, 请联系管理员")
        nodes[0].set("shut_time", shut_down_time)
        root.write(
            xml_path,
            encoding="utf-8",
            pretty_print=True,
            xml_declaration=True,
        )

    def update_gcloud_config(self):
        """
        修改gcloud配置
        """
        ret = cmd.run_shell(
            cmds=['sed -i "s?<is_open>true</is_open>?<is_open>false</is_open>?g" gcloud.xml'],
            workdir="/data/workspace/x5_mobile/mobile_dancer/trunk/exe/resources/config/server/tencent",
        )
        if ret[0] != 0:
            log.error("change gcloud failed")
            raise PyframeException("修改gcloud配置失败, 请联系管理员")

    def sync_db(self):
        """
        同步数据库
        """
        script_path = env.get("script_path")
        exe_path = env.get("exe_path")
        main_deploy_ip = env.get("main_server")
        sub_deploy_ip = env.get("sub_server")
        branch_name = env.get("ver_path")
        # branch_name = env.get("BRANCH")
        if len(script_path) < 0:
            script_path = exe_path
        ret = cmd.run_shell(
            cmds=[f"python {script_path}sync_db_with_server_ip.py {main_deploy_ip},{sub_deploy_ip} {branch_name} {exe_path}"],
            workdir=exe_path,
            return_output=True,
        )
        if ret[0] != 0:
            log.error("sync_db_with_server_ip.py failed")
            raise PyframeException("同步数据库失败, 请联系管理员")
        script_out = ret[1]
        log.info(script_out)


config_mgr = ConfigMgr()
