from frame import *


class JenkinsEnvMgr:
    @staticmethod
    def get_is_rebuild() -> bool:
        """
        是否全量构建
        Returns:

        """
        return env.get("is_rebuild") == "true"


class GlobalEnvMgr:
    @staticmethod
    def set_last_build_version(build_version: str):
        # pipeline_id = env.get("BK_CI_PIPELINE_ID")
        ip = common.get_host_ip().replace(".", "_")
        env.set_global({f"build_version_{ip}": build_version})

    @staticmethod
    def get_last_build_version() -> str:
        # pipeline_id = env.get("BK_CI_PIPELINE_ID")
        ip = common.get_host_ip().replace(".", "_")
        return env.get_global(f"build_version_{ip}")


global_env_mgr = GlobalEnvMgr()
jenkins_env_mgr = JenkinsEnvMgr()
