from frame import *
from project.x5m import config


class HotfixMgr:
    def __init__(self):
        self.WORKDIR = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe"

    def download_ftp_hotfix(self, config_path: str, weekly_version: str, branch: str):
        """
        分发热更文件
        """
        branch = branch.replace(".", "")
        src = f"/version_test/update_config/{config_path}/"
        dst = f"/data/depot/{config_path}"
        path_mgr.rm(dst)
        path_mgr.mkdir(dst)

        ftp = FtpMgr(**config.FTP_CONFIG_150_30)
        branch_files = []
        if weekly_version.strip() == "":
            hotfix_files = ftp.dirs(path=src)
            for file in hotfix_files:
                version = file.split("_")[1]
                if version.startswith(branch[0:3]):
                    branch_files.append(file)
            branch_files.sort()
            if not branch_files:
                log.error(f"no files in {src}")
                raise PyframeException(f"FTP的{src}目录下没有{branch}分支的热更文件")
            weekly_version = branch_files[-1]

        # 用户输入的版本号
        if not weekly_version.endswith(".zip"):
            weekly_version = f"resources_{weekly_version}.zip"

        try:
            ftp.download_file(src=os.path.join(src, weekly_version), dst=os.path.join(dst, weekly_version))
        except Exception:
            log.error(f"{src}{weekly_version} does not exist")
            raise PyframeException(f"FTP的{src}目录下不存在{weekly_version}热更包，请确认热更包填写正确")

    def current_version_brush_hotfix(self, config_path: str):
        """
        现网版本刷热更
        """
        files = os.listdir(os.path.join("/data/depot", config_path))
        dst_path = os.path.join("/data/depot/update_config", config_path)
        path_mgr.rm(dst_path)
        path_mgr.mkdir(dst_path)
        # 实际目录下只有一个file文件
        for file in files:
            zip_file = "/data/depot/" + config_path + "/" + file
            tar.decompress(src=zip_file, dst=dst_path, verbose=True)
            env.set({"hotfix_file": file})
        src_dir = os.path.join(dst_path, "test")
        dst_dir = "/data/workspace/x5_mobile/mobile_dancer/trunk/exe"
        # 拷贝文件夹
        path_mgr.copy(src_dir, dst_dir)
        path_mgr.rm("/data/depot/" + config_path)


hotfix_mgr = HotfixMgr()
