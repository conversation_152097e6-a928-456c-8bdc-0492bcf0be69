# coding=utf-8

from frame import *
from project.x5m import config


class NewGitlabMgr:
    def __init__(self):
        self.x5mobile_gitlab_mgr = GitlabMgr(url=config.GITLAB_MAINTAINER["url"], token=config.GITLAB_MAINTAINER["token"], project="dgm/x5mobile")
        self.x5mconfig_gitlab_mgr = GitlabMgr(url=config.GITLAB_MAINTAINER["url"], token=config.GITLAB_MAINTAINER["token"], project="dgm/x5mconfig")
        self.x5mweek_gitlab_mgr = GitlabMgr(url=config.GITLAB_MAINTAINER["url"], token=config.GITLAB_MAINTAINER["token"], project="dgm/x5mweek")
        self.x5mobile_git_mgr = GitMgr(workdir="/data/workspace", project_name="x5_mobile")

    def get_max_pipeline_id(self, branch: str):
        """
        获取三个项目中最大的 max_pipeline_id
        """
        x5mobile_pipeline_id = self.x5mobile_gitlab_mgr.get_newest_pipeline_id(branch=branch)
        x5mconfig_pipeline_id = self.x5mconfig_gitlab_mgr.get_newest_pipeline_id(branch=branch)
        x5mweek_resources_pipeline_id = self.x5mweek_gitlab_mgr.get_newest_pipeline_id(branch="resources")
        x5mweek_release_pipeline_id = self.x5mweek_gitlab_mgr.get_newest_pipeline_id(branch="release")
        x5mweek_online_pipeline_id = self.x5mweek_gitlab_mgr.get_newest_pipeline_id(branch="online")
        max_pipeline_id = max(
            x5mobile_pipeline_id, x5mconfig_pipeline_id, x5mweek_online_pipeline_id, x5mweek_resources_pipeline_id, x5mweek_release_pipeline_id
        )
        log.info("x5mobile_pipeline_id: {}".format(x5mobile_pipeline_id))
        log.info("x5mconfig_pipeline_id: {}".format(x5mconfig_pipeline_id))
        log.info("x5mweek_resources_pipeline_id: {}".format(x5mweek_resources_pipeline_id))
        log.info("x5mweek_release_pipeline_id: {}".format(x5mweek_release_pipeline_id))
        log.info("x5mweek_online_pipeline_id: {}".format(x5mweek_online_pipeline_id))
        log.info("max_pipeline_id: {}".format(max_pipeline_id))
        env.set({"max_pipeline_id": max_pipeline_id})

    def get_commit_id(self, branch: str):
        """
        获取三个项目中的最近 commit_id
        """
        x5mobile_commit_id = self.x5mobile_gitlab_mgr.get_newest_commit_id(branch=branch)
        x5mconfig_commit_id = self.x5mconfig_gitlab_mgr.get_newest_commit_id(branch=branch)
        x5mweek_resources_commit_id = self.x5mweek_gitlab_mgr.get_newest_commit_id(branch="resources")
        x5mweek_release_commit_id = self.x5mweek_gitlab_mgr.get_newest_commit_id(branch="release")
        x5mweek_online_commit_id = self.x5mweek_gitlab_mgr.get_newest_commit_id(branch="online")
        env.set(
            {
                "x5mobile_commit_id": x5mobile_commit_id,
                "x5mconfig_commit_id": x5mconfig_commit_id,
                "x5mweek_resources_commit_id": x5mweek_resources_commit_id,
                "x5mweek_release_commit_id": x5mweek_release_commit_id,
                "x5mweek_online_commit_id": x5mweek_online_commit_id,
            }
        )
        log.info("x5mobile_commit_id: {}".format(x5mobile_commit_id))
        log.info("x5mconfig_commit_id: {}".format(x5mconfig_commit_id))
        log.info("x5mweek_resources_commit_id: {}".format(x5mweek_resources_commit_id))
        log.info("x5mweek_release_commit_id: {}".format(x5mweek_release_commit_id))
        log.info("x5mweek_online_commit_id: {}".format(x5mweek_online_commit_id))

    @staticmethod
    def __is_delete_x5mobile() -> bool:
        """
        判断是否需要删除x5mobile项目
        Returns:

        """
        # build_version = env.get("build_version")
        # last_build_version = global_env_mgr.get_last_build_version()
        #
        # # 如果没有上次构建版本，那么就不删除
        # if not last_build_version:
        #     return False
        #
        # # 如果当前构建版本和上次构建版本一致，那么就不删除
        # if build_version == last_build_version:
        #     return False
        # return True
        return False

    def clone_x5mobile(self, branch: str):
        # 删除x5mobile项目
        if self.__is_delete_x5mobile():
            path_mgr.rm("/data/workspace/x5_mobile")

        if not Path("/data/workspace/x5_mobile").exists():
            log.info("/data/workspace/x5_mobile doesn't exist")
            self.x5mobile_git_mgr.clone(
                url="http://oauth2:<EMAIL>/dgm/x5mobile.git",
                branch=branch,
            )
        else:
            self.x5mobile_git_mgr.reset()
            # self.x5mobile_git_mgr.clean()
            self.x5mobile_git_mgr.advance_pull(branch=branch)

    def get_x5mobile_committer(self, path: str, line: int) -> (str, str):
        return self.x5mobile_git_mgr.blame(path=path, line=line).committer_mail, self.x5mobile_git_mgr.blame(path=path, line=line).committer_time


new_gitlab_mgr = NewGitlabMgr()
