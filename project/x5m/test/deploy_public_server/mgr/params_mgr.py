# coding=utf-8
import json

import requests

from frame import FtpMgr, env, log, common, PyframeException
from project.x5m import config


class ParamsMgr:
    def __init__(self):
        self.bk_host = "http://**************:21935/"
        # TODO 个人账号密码
        self.bk_headers = {"X-DEVOPS-UID": "<EMAIL>", "content-type": "application/json"}
        self.bk_pipeline_id = {
            "trunk": "p-7eab561f670f4474931cf3c3f5d65ce3",
            "branch": "p-1743b67330ee4fe8b24e7c9df098bc4b",
            "release": "p-f6d75545ffe04edc999af0dea8f63564",
            "resource": "p-64ec7b311fef45bcb96baf0862b5cffa",
            "online": "p-96da380a1da94050b8f5ed048910174d",
        }
        self.special_branch_bk_pipeline_id = "p-81d6895e60a544c5bb2eeec00e7e6353"

    def __get_pipeline_params(self, artifacts_branch: str):
        """
        获取流水线参数信息
        """
        bk_pipeline_id = ""
        is_special_branch = env.get("is_special_branch")
        if common.is_true(is_special_branch):
            bk_pipeline_id = self.special_branch_bk_pipeline_id
        else:
            if artifacts_branch in self.bk_pipeline_id.keys():
                bk_pipeline_id = self.bk_pipeline_id.get(artifacts_branch)
        log.info(bk_pipeline_id)
        url = self.bk_host + f"api/apigw-user/v3/projects/dgm/pipelines/{bk_pipeline_id}"
        resp = requests.get(url=url, headers=self.bk_headers)
        log.info(f"get params result: {resp.status_code}, {resp.json()}")
        return resp.json()

    def __update_pipeline_params(self, data: dict, artifacts_branch: str):
        """
        更新流水线参数信息
        Args:
            data:
        """
        bk_pipeline_id = ""
        is_special_branch = env.get("is_special_branch")
        if common.is_true(is_special_branch):
            bk_pipeline_id = self.special_branch_bk_pipeline_id
        else:
            if artifacts_branch in self.bk_pipeline_id.keys():
                bk_pipeline_id = self.bk_pipeline_id.get(artifacts_branch)
        log.info(f"bk_pipeline_id:{bk_pipeline_id}")
        url = self.bk_host + f"api/apigw-user/v3/projects/dgm/pipelines/{bk_pipeline_id}"
        resp = requests.put(url=url, headers=self.bk_headers, data=json.dumps(data.get("data")))
        ret = resp.json()
        log.info(f"update params result: {resp.status_code}, {ret}")
        if ret.get("status") == 0 and ret.get("data") is True:
            return True
        else:
            return False

    def update_server_package_params(self, artifacts_branch: str):
        """
        更新参数信息
        """
        is_special_branch = env.get("is_special_branch")
        ftp = FtpMgr(**config.FTP_CONFIG_150_30)
        if common.is_true(is_special_branch):
            path = f"/version_test/server_pack/special_branch/"
        else:
            path = f"/version_test/server_pack/{artifacts_branch}/"
        package_list = ftp.dirs(path=path)

        # 过滤出dgm_server开头的包
        dgm_servers = [package for package in package_list if "dgm_server" in package]

        # 重新排序，按照版本号大小排序，并且只取最新的100个
        def get_number(package_name: str) -> int:
            return int(package_name.split("_")[-1].split(".")[0])

        dgm_servers = sorted(dgm_servers, key=get_number, reverse=True)[:100]

        package_lists = [{"key": i, "value": i} for i in dgm_servers]
        ret = self.__get_pipeline_params(artifacts_branch=artifacts_branch)
        status = ret.get("status")
        if status == 0:
            params = ret.get("data").get("stages")[0].get("containers")[0].get("params")
            for param in params:
                log.info(param.get("id"))
                if param.get("id") == "server_package":
                    param.update({"options": package_lists, "defaultValue": ""})
        else:
            raise PyframeException("蓝盾接口报错，请检查后重试")
        self.__update_pipeline_params(data=ret, artifacts_branch=artifacts_branch)


params_mgr = ParamsMgr()
