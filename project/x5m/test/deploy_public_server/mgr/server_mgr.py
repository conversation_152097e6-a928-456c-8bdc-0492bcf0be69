# coding=utf-8
import os.path

from frame import *
from project.x5m import config
from project.x5m.test.deploy_public_server.mgr.env_mgr import jenkins_env_mgr, global_env_mgr


class ServerMgr:
    def __init__(self):
        pipeline_workspace = env.pipeline.workspace()
        self.WORKDIR = os.path.join(pipeline_workspace, "x5_mobile/mobile_dancer/trunk/exe")
        self.server_dir = os.path.join(pipeline_workspace, "x5_mobile/mobile_dancer/trunk/server")

    @property
    def distcc_log_path(self):
        return os.path.join(self.WORKDIR, f"distcc_{env.pipeline.build_num()}.log")

    def build_old(self):
        """
        使用CMake之后，此方法已经废弃
        """
        build_version = env.get("build_version")
        if build_version == "release":
            cmd_line = ["sh stop_allserver.sh", "sh pipeline_build_server_release.sh"]
        else:
            cmd_line = ["sh stop_allserver.sh", "sh pipeline_build_server.sh"]

        ret = cmd.run_shell(
            cmds=cmd_line,
            workdir=self.WORKDIR,
            environ={
                "DISTCC_IO_TIMEOUT": "1200",
            },
            log_to_file=self.distcc_log_path,
        )
        # 保存本次编译版本
        global_env_mgr.set_last_build_version(build_version)

        if ret[0] != 0:
            log.error(f"build failed, return {ret[0]}")
            advance.distcc.raise_x5m_distcc_log_exception(log_path=self.distcc_log_path)
        else:
            log.info("compile successfully!")

    def build(self):
        """
        编译服务器
        """
        # build_version = env.get("build_version")

        # 清理tmp目录
        # clean_tmp = "rm -rf /tmp/distcc*"  # TODO 临时解决方案，后续需要修改

        # 刷新cmake预设
        refresh_preset = "cmake --preset time-profile --fresh"  # 刷新cmake预设

        # 编译命令
        compile_launcher_prefix = "compilelauncher -t --save-db --loki-job x5mservbuild -- cmake --build build --preset time-profile"
        # compile_launcher_prefix = "cmake --build build --preset default"  # 不记录编译时间
        compile_launcher_suffix = " --clean-first" if jenkins_env_mgr.get_is_rebuild() else ""  # 是否全量构建
        gen_t_log = "bash ../exe/tools/tlog_x5mobile/gentlogxml.sh"  # 生成tlog

        # if build_version == "release":
        #     cmd_line = [
        #         refresh_preset,
        #         f"{compile_launcher_prefix} {compile_launcher_suffix}",
        #         gen_t_log,
        #     ]
        # else:
        #     cmd_line = [
        #         refresh_preset,
        #         f"{compile_launcher_prefix} {compile_launcher_suffix}",
        #         gen_t_log,
        #     ]

        cmds = [
            refresh_preset,
            f"{compile_launcher_prefix} {compile_launcher_suffix}",
            gen_t_log,
        ]
        path_mgr.mkdir("/data/distcc_tmp")
        ret = cmd.run_shell(
            cmds=cmds,
            workdir=self.server_dir,
            environ={
                "DISTCC_IO_TIMEOUT": "1200",
                "DISTCC_HOSTS": advance.distcc.request_distcc_hosts(system=advance.distcc.System.tlinux12),
                "TMPDIR": "/data/distcc_tmp",
                "DISTCC_VERBOSE": "0",
            },
            log_to_file=self.distcc_log_path,
        )
        # 保存本次编译版本
        # global_env_mgr.set_last_build_version(build_version)

        if ret[0] != 0:
            log.error(f"build failed, return {ret[0]}")
            advance.distcc.raise_x5m_distcc_log_exception(log_path=self.distcc_log_path)
        else:
            log.info("compile successfully!")

    def package(self):
        """
        打包
        """
        max_pipeline_id = env.get("max_pipeline_id")
        commit_id_1 = env.get("x5mconfig_commit_id")
        commit_id_2 = env.get("x5mweek_resources_commit_id")
        commit_id_3 = env.get("x5mweek_release_commit_id")
        commit_id_4 = env.get("x5mweek_online_commit_id")
        pack_zip_type = env.get("pack_zip_type")
        cmd.run_shell(
            cmds=[f"bash pack_dev_zip.sh {max_pipeline_id} {commit_id_1} {commit_id_2} {commit_id_3} {commit_id_4} {pack_zip_type}"],
            workdir=self.WORKDIR,
        )
        ret = cmd.run_shell(
            cmds=[
                f"zip dgm_sh_{max_pipeline_id}.zip ./stop_allserver.sh ./start_global_server.sh ./start_allserver.sh ./remote_install_whitebox.sh ./remote_install.py ./dgm_common.sh ./ip_config.csv ./start_allserver_slave.sh ./sync_db_with_server_ip.py ./mysql-schema-sync"
            ],
            workdir=self.WORKDIR,
        )
        if ret[0] != 0:
            log.error("打包failed")
            raise PyframeException("zip失败")
        ret = cmd.run_shell(cmds=[f"mv dgm_sh_{max_pipeline_id}.zip /data/depot"], workdir=self.WORKDIR)
        if ret[0] != 0:
            log.error(f"mv dgm_sh_{max_pipeline_id}.zip failed")
            raise PyframeException(f"备份dgm_sh_{max_pipeline_id}.zip失败")

    def start_all_server(self):
        """
        启动服务器
        """
        ret = cmd.run_shell(
            cmds=["bash ./start_allserver.sh"],
            workdir=self.WORKDIR,
            dot_kill_me=True,
        )
        if ret[0] != 0:
            log.error("start_allserver.sh failed")
            raise PyframeException("start_allserver失败")

    def start_global_server(self):
        """
        启动服务器
        """
        ret = cmd.run_shell(
            cmds=["bash ./start_global_server.sh"],
            workdir=self.WORKDIR,
            dot_kill_me=True,
        )
        if ret[0] != 0:
            log.error("start_global_server.sh failed")
            raise PyframeException("start_global_server失败")

    def stop_server(self):
        """
        关服
        """
        ret = cmd.run_shell(cmds=["bash ./stop_allserver.sh"], workdir=self.WORKDIR)
        if ret[0] != 0:
            log.error("stop_allserver.sh failed")
            raise PyframeException("stop_allserver失败")

    def upload_ftp_server_package(self, max_pipeline_id: str, artifacts_branch: str):
        """
        上传 ftp *************
        """
        is_special_branch = env.get("is_special_branch")
        branch = env.get("BRANCH")
        server_pack = "dgm_server_{}.zip".format(max_pipeline_id)
        sh_pack = "dgm_sh_{}.zip".format(max_pipeline_id)
        # 是否是trunk需要确认
        config_zip_path = "/data/depot/dgm_config"
        if path_mgr.exists(config_zip_path):
            path_mgr.rm(config_zip_path)
        path_mgr.mkdir(config_zip_path)
        files = os.listdir(os.path.join(env.pipeline.workspace(), "x5_mobile/mobile_dancer/trunk/exe/tools/tlog_x5mobile"))
        x5m_tlog = ""
        for file in files:
            if file.startswith("x5m_tlog_trunk_"):
                x5m_tlog = file
        if x5m_tlog == "":
            log.error("x5m_tlog配置文件不存在")
            raise PyframeException("x5m_tlog配置文件不存在")

        path_mgr.copy(
            src=os.path.join(env.pipeline.workspace(), "x5_mobile/mobile_dancer/trunk/exe/tools/tlog_x5mobile/ADDITEM_RESOURCE.csv"),
            dst=os.path.join(config_zip_path, "ADDITEM_RESOURCE.csv"),
        )
        path_mgr.copy(
            src=os.path.join(env.pipeline.workspace(), "x5_mobile/mobile_dancer/trunk/exe/tools/tlog_x5mobile", x5m_tlog),
            dst=os.path.join(config_zip_path, x5m_tlog),
        )
        zip_src = config_zip_path
        zip_dst = f"/data/depot/dgm_config_{max_pipeline_id}.zip"
        tar.compress(src=zip_src, dst=zip_dst)
        src_server = os.path.join("/data/depot", server_pack)
        src_sh = os.path.join("/data/depot", sh_pack)
        if common.is_true(is_special_branch):
            dst = f"/version_test/server_pack/special_branch/"
            path_mgr.move(src_server, os.path.join("/data/depot", f"{branch}_{server_pack}"))
            path_mgr.move(src_sh, os.path.join("/data/depot", f"{branch}_{sh_pack}"))
            path_mgr.move(zip_dst, os.path.join("/data/depot", f"{branch}_dgm_config_{max_pipeline_id}.zip"))
            src_server = os.path.join("/data/depot", f"{branch}_{server_pack}")
            src_sh = os.path.join("/data/depot", f"{branch}_{sh_pack}")
            zip_dst = os.path.join("/data/depot", f"{branch}_dgm_config_{max_pipeline_id}.zip")
        else:
            dst = f"/version_test/server_pack/{artifacts_branch}/"
        ftp = FtpMgr(**config.FTP_CONFIG_150_30)
        ftp.upload_file(dst=dst, src=src_server)
        ftp.upload_file(dst=dst, src=src_sh)
        ftp.upload_file(dst=dst, src=zip_dst)
        env.set({"package_link": dst})

    def download_ftp_server_package(self, max_pipeline_id: str, artifacts_branch: str):
        """
        下载安装包
        """
        is_special_branch = env.get("is_special_branch")
        branch = env.get("BRANCH")
        server_pack = f"dgm_server_{max_pipeline_id}.zip"
        if common.is_true(is_special_branch):
            src = f"/version_test/server_pack/special_branch/{branch}_{server_pack}"
        else:
            src = f"/version_test/server_pack/{artifacts_branch}/{server_pack}"
        dst = f"/data/depot/{server_pack}"
        ftp = FtpMgr(**config.FTP_CONFIG_150_30)
        ftp.download_file(src=src, dst=dst, process_bar=True)

    def unzip_server_package(self, max_pipeline_id: str):
        """
        解压服务器包
        """
        server_pack = f"dgm_server_{max_pipeline_id}.zip"
        src = f"/data/depot/{server_pack}"
        dst = os.path.join(env.pipeline.workspace(), "x5_mobile/mobile_dancer/trunk/exe")
        tar.decompress(src=src, dst=dst)

    def upload_nexus_server_package(self, max_pipeline_id: str, artifacts_branch: str):
        """
        上传服务器包到制品库
        """
        # 获取 week_config
        if artifacts_branch == "resource":
            week_config = "resource"
        elif artifacts_branch == "release":
            week_config = "release"
        elif artifacts_branch == "online":
            week_config = "online"
        else:
            week_config = artifacts_branch
        # 上传 制品库
        is_special_branch = env.get("is_special_branch")
        branch = env.get("BRANCH")
        server_pack = "dgm_server_{}.zip".format(max_pipeline_id)
        sh_pack = "dgm_sh_{}.zip".format(max_pipeline_id)
        nexus = Nexus(**config.NEXUS_CONFIG)
        src = "/data/depot/{}".format(server_pack)
        if common.is_true(is_special_branch):
            src = os.path.join("/data/depot", f"{branch}_{server_pack}")
            dst = f"http://nexus.h3d.com.cn/repository/mobile/stag/server_pack/special_branch/{branch}_{server_pack}"
        else:
            dst = f"http://nexus.h3d.com.cn/repository/mobile/stag/server_pack/{week_config}/{server_pack}"
        nexus.upload(src=src, dst=dst)

        src = "/data/depot/{}".format(sh_pack)
        if common.is_true(is_special_branch):
            src = os.path.join("/data/depot", f"{branch}_{sh_pack}")
            dst = f"http://nexus.h3d.com.cn/repository/mobile/stag/server_pack/special_branch/{branch}_{sh_pack}"
        else:
            dst = f"http://nexus.h3d.com.cn/repository/mobile/stag/server_pack/{week_config}/{sh_pack}"
        nexus.upload(src=src, dst=dst)


server_mgr = ServerMgr()
