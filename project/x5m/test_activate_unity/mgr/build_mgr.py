import re
from typing import List, <PERSON><PERSON>

import xlrd

from frame import *
from project.x5m import config
from project.x5m.small_island_package.mgr.env_mgr import env_mgr, global_env_mgr


class BuildMgr:
    def __init__(self):
        self.debug = env_mgr.get_debug()
        self.platform = env_mgr.get_platform()
        self.workspace = env_mgr.get_workspace()
        self.resource_dir_prefix = f"//x5_mobile/mr/art_release_test/art_src/island" if self.debug else f"//x5_mobile/mr/art_release/art_src/island"
        self.table_rule_prefix = f"//x5_mobile/mr/art_release_test/table_rule" if self.debug else f"//x5mplan/resmg/island"
        if self.debug:
            self.ab_hotfix_dir_prefix = "//x5_mobile/mr/art_release_test/onlineupdate/{branch}/{hotfix_version}/client/{platform}/assetbundles/art"
            self.ab_trunk_dir_prefix = "//x5_mobile/mr/art_release_test/Resources/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art"
            self.ab_branch_dir_prefix = "//x5_mobile/mr/art_release_test/b/{branch}/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art"
            self.ab_cdn_dir_prefix = "//x5_mobile/mr/art_release_test/{platform}/assetbundles"
        else:
            self.ab_hotfix_dir_prefix = "//x5_mobile/mr/onlineupdate/{branch}/{hotfix_version}/client/{platform}/assetbundles/art"
            self.ab_trunk_dir_prefix = "//x5_mobile/mr/Resources/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art"
            self.ab_branch_dir_prefix = "//x5_mobile/mr/b/{branch}/ResourcePublish/CDN/SourceFiles/{platform}/assetbundles/art"
            self.ab_cdn_dir_prefix = "//x5m/res/cdn/cooked/{platform}/assetbundles"
        self.client = f"jenkins-{env.pipeline.function_name()}-{self.platform}-{common.get_host_ip()}-test"
        self.p4_mgr = P4Client(
            host=config.P4_CONFIG_JENKINS["host"],
            username=config.P4_CONFIG_JENKINS["username"],
            password=config.P4_CONFIG_JENKINS["password"],
            client=self.client,
        )
        self.p4_mgr.set_options(clobber=True)
        self.git_mgr = GitMgr(
            workdir=self.workspace,
            project_name="x5_mobile",
        )
        self.package_project_url = "http://x5mobile-gitlab.h3d.com.cn/dgm/arttrunk.git"
        self.unity = "Unity.exe"
        self.log_path = os.path.join(self.workspace, "jenkins_log")
        self.package_log = os.path.join(self.log_path, f"{self.platform}_{env.pipeline.build_num()}.log")
        self.project_path = os.path.join(self.workspace, "x5_mobile", "mobile_dancer", "arttrunk", "client")
        self.source_prefix = "Assets/StaticResources/art/3d"
        self.project_art_path = os.path.join(self.project_path, self.source_prefix)
        self.ab_path = os.path.join(self.workspace, "ab")
        self.report_path = os.path.join(self.project_path, "AssetBundleTool", "AssetBundleToolLogs")
        self.resources_txt = os.path.join(self.project_path, "AssetBundleTool", "resources.txt")

    def check_package(self):
        """
        检查是否需要打包
        """
        first_changelist = env_mgr.get_first_changelist()
        last_changelist = global_env_mgr.get_last_changelist(platform=self.platform)
        if first_changelist is None or first_changelist == "0" or first_changelist == "":
            if last_changelist == "0":
                raise PyframeException("first_changelist为空，且数据库中上次打包版本号也为空，无法打包")
            changelist = int(last_changelist) + 1
        else:
            changelist = first_changelist

        changes = self.p4_mgr.get_changes(f"{self.resource_dir_prefix}/...@{changelist},now", max=1000)
        log.info(f"changes: {changes}")
        if len(changes) == 0:
            pipeline_mgr.stop_current_build()
        else:
            env_mgr.set_changes(platform=self.platform, changes=changes)

    def update_project(self):
        """
        获取打包工程
        """
        if not self.git_mgr.exist():
            self.git_mgr.clone_with_password(
                url=self.package_project_url, branch="master", username="<EMAIL>", password="reporter123"
            )
        else:
            self.git_mgr.pull()

    def clean_resource(self):
        """
        清理资源
        """
        log.info("开始清理原始资源")
        if path_mgr.exists(self.project_art_path):
            path_mgr.rm(self.project_art_path)

        log.info("开始清理ab资源")
        if path_mgr.exists(self.ab_path):
            path_mgr.rm(self.ab_path)

        log.info("开始清理打包报告")
        if path_mgr.exists(self.report_path):
            path_mgr.rm(self.report_path)

        log.info("开始清理打包日志")
        if path_mgr.exists(self.log_path):
            path_mgr.rm(self.log_path)
        # 创建打包日志目录
        path_mgr.mkdir(self.log_path)

    def __get_resource_id_and_path_and_suffix(self, changelist: str) -> Tuple[List[str], List[str], List[str]]:
        """
        根据changelist获取资源ID
        Args:
            changelist:

        Returns:

        """
        files = self.p4_mgr.get_files_by_changelist(changelist)
        # 通过正则表达式获取资源ID
        id_pattern = re.compile(r"^//.*?(\d{9,10}|//.*?island/common/island_\w+_\w+).*?$")
        source_ids = []
        path_pattern = re.compile(r"^(//.*?\d{9,10}|//.*?island/common/island_\w+_\w+).*?$")
        source_paths = []
        suffix_pattern = re.compile(r"^//.*?(island/.*?/.*?/\d{9,10}|island/common/island_\w+_\w+).*?$")
        source_suffixes = []
        for file in files:
            ret = re.findall(id_pattern, file)
            if ret:
                if ret[0] not in source_ids:
                    source_ids.append(ret[0])
            ret = re.findall(path_pattern, file)
            if ret:
                if ret[0] not in source_paths:
                    source_paths.append(ret[0])
                    log.info(f"ret {ret[0]}")
            ret = re.findall(suffix_pattern, file)
            if ret:
                if ret[0] not in source_suffixes:
                    source_suffixes.append(ret[0])
        log.info(f"source_paths: {source_paths}")
        # 过滤掉已经删除的路径
        exist_source_paths = []
        for source_path in source_paths:
            if self.p4_mgr.dirs(source_path):
                exist_source_paths.append(source_path)

        return source_ids, exist_source_paths, source_suffixes

    def __get_resource_views(self, source_paths: list) -> List[str]:
        """
        获取原始资源映射
        Args:
            source_paths: 资源路径
        Returns:
            list: 映射列表
        """
        views = []
        for source_path in source_paths:
            view = f"{source_path}/... //{self.client}/{source_path.replace('//', '')}/..."
            if view not in views:
                views.append(view)
        return views

    def __get_dist_views(self) -> list:
        """
        获取分发表映射
        Returns:
            list: 映射列表
        """
        view = f"{self.table_rule_prefix}/... //{self.client}/{self.table_rule_prefix.replace('//', '')}/..."
        return [view]

    def sync_resource(self):
        """
        更新P4资源
        """
        all_changes = env_mgr.get_changes(platform=self.platform)
        users = []
        source_paths = []
        for change in all_changes:
            user = change.get("user")
            if not user.endswith("h3d.com.cn"):
                user = f"{user}@h3d.com.cn"
            if user not in users:
                users.append(user)
            changelist = change.get("change")
            _, source_path, _ = self.__get_resource_id_and_path_and_suffix(changelist)
            source_paths += source_path

        env_mgr.set_source_paths(source_paths=source_paths)
        env_mgr.set_users(platform=self.platform, users=users)
        # 记录最新changelist
        env_mgr.set_changelist(platform=self.platform, changelist=all_changes[0].get("change"))
        views = []

        # 原始资源映射
        resource_views = self.__get_resource_views(source_paths)
        views.extend(resource_views)

        # 分发表映射
        dist_views = self.__get_dist_views()
        views.extend(dist_views)

        # 设置P4 view 和 root，同步资源
        self.p4_mgr.set_root(self.workspace)
        self.p4_mgr.set_view(views=views)
        self.p4_mgr.sync_all(force=True)

        self.__copy_resource_to_project()

    def __copy_resource_to_project(self):
        """
        复制原始资源到打包工程
        """
        # 复制需要打包的原始资源到打包工程,并记录资源路径
        if not path_mgr.exists(self.project_art_path):
            path_mgr.mkdir(self.project_art_path)
        source_paths = env_mgr.get_source_paths()
        relative_sources = []
        relative_pattern = re.compile(r"^//(.*?island/.*?\d{9,10}|.*?island/common/island_\w+_\w+).*?$")
        for source_path in source_paths:
            ret = re.findall(relative_pattern, source_path)
            if not ret:
                continue
            relative_path = ret[0]
            log.info(f"相对路径: {relative_path}")
            src = os.path.join(self.workspace, relative_path)
            if not path_mgr.exists(src):
                log.warn(f"资源不存在: {src}")
                raise PyframeException(f"资源不存在: {src}, 请检查P4是否同步成功")
            temp = relative_path.replace("x5_mobile/mr/art_release/art_src/", "")
            dst = os.path.join(self.project_art_path, temp)

            # 资源路径如果有重复，只复制一次
            relative_source = f"{self.source_prefix}/" + temp
            if relative_source not in relative_sources:
                path_mgr.xcopy(src, dst, dst_is_file=False)
                relative_sources.append(relative_source)

        env_mgr.set_relative_source(",".join(relative_sources))

    def start_package(self):
        """
        调用打包命令
        """
        path = env_mgr.get_relative_source()
        if not path:
            log.info("没有需要打包的资源，直接取消流水线运行")
            s_changelist = env_mgr.get_changelist(platform=self.platform)
            global_env_mgr.set_last_changelist(platform=self.platform, changelist=s_changelist)
            pipeline_mgr.stop_current_build()

        cmd_line = f'"{self.unity}" '
        cmd_line += f"-quit -batchmode -projectPath {self.project_path} -logFile {self.package_log} "
        cmd_line += f"-executeMethod H3DBuildTools.BuildArt -buildTarget {self.platform} path={path} "
        cmd_line += f"out_path={self.ab_path} "
        ret, _ = cmd.run_shell(
            cmds=[cmd_line],
            workdir=self.workspace,
        )
        if ret != 0:
            try:
                advance.raise_unity_log_exception(log_path=self.package_log)
            except UnityInvalidLicenseException:
                log.info("开始自动激活")
                advance.unity.activate_unity(unity_path=self.unity)
                wechat.send_multicast_post_failure(
                    webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1f4b9fde-a26e-4e1a-89eb-9ca5dfd401af", content="Unity自动激活成功"
                )
            except Exception as e:
                raise PyframeException(f"打包命令执行失败: {e}")
            else:
                raise PyframeException(f"打包命令执行失败，错误码:{ret}")

    @staticmethod
    def __get_report_detail(read_lines: list) -> dict:
        """
        获取打包结果
        Returns:
        """
        success_pattern = re.compile(r"^\[info]\s+\d+\s.*?(\d+$|\d+_extend\d$|island_\w+_\w+$)")
        fail_pattern = re.compile(r"^\[Error]\s+\d+\s.*?(\d+$|\d+_extend\d$|island_\w+_\w+$)")
        success_ids = []
        fail_ids = []
        for read_line in read_lines:
            suc = re.findall(success_pattern, read_line)
            if suc:
                success_ids.extend(suc)

            fail = re.findall(fail_pattern, read_line)
            if fail:
                fail_ids.extend(fail)
        success_ids = list(set(success_ids))
        fail_ids = list(set(fail_ids))
        report_detail = {"success_ids": success_ids, "fail_ids": fail_ids}
        return report_detail

    def calculate_package_result(self):
        """
        计算打包结果
        """
        reports = path_mgr.glob(self.report_path, "*.txt")
        if not reports:
            raise PyframeException(f"获取打包结果失败，请检查查看打包日志")
        report = reports[0]
        with open(report, "r", encoding="utf-8") as f:
            content = f.readlines()
            # 解析打包结果
            report_detail = self.__get_report_detail(content)
            # 记录打包结果
            log.debug(f"platform: {self.platform}, report_detail: {report_detail}")
            env_mgr.set_report(report=report_detail)

    def __get_dist_table(self) -> str:
        """
        获取分发表本地路径
        Returns:
            str: 分发表本地路径
        """
        return os.path.join(self.workspace, f"{self.table_rule_prefix.replace('//', '')}")

    @staticmethod
    def __get_island_type_by_id(resource_id: str) -> str or None:
        """
        根据ID获取island类型
        Args:
            resource_id:

        Returns:

        """
        source_paths = env_mgr.get_source_paths()
        pattern = re.compile(rf"^//.*?(island.*?{resource_id})$")
        for source_path in source_paths:
            ret = re.findall(pattern, source_path)
            if ret:
                return ret[0]

    @staticmethod
    def __get_version_and_hotfix(hotfix_version: str) -> Tuple[str, str]:
        """
        拆分热更版本号
        Args:
            hotfix_version:

        Returns:

        """
        # 正则校验热更版本号，如果校验失败，返回空
        if not hotfix_version:
            return "", ""
        pattern = re.compile(r"^(\d{4}).\d{7}")
        ret = re.findall(pattern, hotfix_version)
        if ret:
            major_version = ret[0]
            return major_version, hotfix_version
        raise PyframeException(f"请检查热更分发表中的path路径是否正确: {hotfix_version}")

    def __match_hotfix_dist_path(self, resource_id: str) -> str or None:
        """
        匹配热更分发规则
        Args:
            resource_id: 资源ID
        Returns:
            str: 匹配到的热更
        """
        dist_table_path = self.__get_dist_table()
        # 获取热更分发表
        hotfix_table = os.path.join(dist_table_path, "hotfix-small-island-rule.xlsx")
        if path_mgr.exists(hotfix_table):
            dist_table = xlrd.open_workbook(hotfix_table)
            sheet = dist_table.sheets()[0]  # 获取第一个sheet
            rows = sheet.nrows  # 行数
            row_values = sheet.row_values(0)  # 获取第一行的数据
            # 如果第一行第一列的值不等于name，且第二列的值不等于path，则返回
            if row_values and row_values[0] != "name" and row_values[1] != "path":
                log.warning(f"热更分发表格式不正确，第一行第一列的值不等于name，且第二列的值不等于path")
                return None
            # 从第二行数据开始，如果第一列的值等于资源id，则返回第二列的值
            for i in range(1, rows):
                row = sheet.row_values(i, 0, 2)
                if resource_id == row[0]:
                    return row[1]
        else:
            log.info("未找到热更分发表，")
            return None

    def get_current_branch(self) -> str:
        """
        获取当前分支
        Returns:
            str: 当前分支
        """
        dirs = self.p4_mgr.dirs("//x5_mobile/mr/b/*")
        pattern = re.compile(r"^//x5_mobile/mr/b/(\d+.\d+.\d+$)")
        new = []
        for d in dirs:
            ret = re.findall(pattern, d)
            if ret:
                new.append(ret[0])
        if not new:
            raise PyframeException("获取当前分支失败")
        # new = ['6.05.0_i']
        new = sorted(new, reverse=True)
        return new[0]

    # 判断当前分支是否是特殊分支
    def __is_special_branch(self) -> bool:
        """
        判断当前分支是否是特殊分支
        Returns:
            bool: True 是特殊分支，False 不是特殊分支
        """
        branch = self.get_current_branch()
        pattern = re.compile(r"^\d+.\d+.\d+$")
        ret = re.findall(pattern, branch)
        if ret:
            return False
        return True

    def dist_ab(self):
        """
        分发AB资源
        """
        report_detail = env_mgr.get_report(platform=self.platform)
        success_ids = report_detail.get("success_ids", [])
        current_branch = self.get_current_branch()
        is_special_branch = self.__is_special_branch()
        if success_ids:
            # 设置映射更新AB
            views = []
            for success_id in success_ids:
                hotfix_version = self.__match_hotfix_dist_path(success_id)
                if hotfix_version:  # 热更
                    major_version, hotfix_version = self.__get_version_and_hotfix(hotfix_version)
                    # 映射热更分发路径
                    hotfix_dir = self.ab_hotfix_dir_prefix.format(branch=major_version, hotfix_version=hotfix_version, platform=self.platform)
                    view = f"{hotfix_dir}/... //{self.client}/{hotfix_dir.replace('//', '')}/..."
                    if view not in views:
                        views.append(view)
                else:  # 默认版内
                    # 映射主支分发路径，如果不是特殊分支，则映射主支路径
                    if not is_special_branch:
                        trunk_dir = self.ab_trunk_dir_prefix.format(platform=self.platform)
                        view = f"{trunk_dir}/... //{self.client}/{trunk_dir.replace('//', '')}/..."
                        if view not in views:
                            views.append(view)
                    # 映射分支分发路径
                    branch_dir = self.ab_branch_dir_prefix.format(branch=current_branch, platform=self.platform)
                    view = f"{branch_dir}/... //{self.client}/{branch_dir.replace('//', '')}/..."
                    if view not in views:
                        views.append(view)
            log.info(f"设置映射: {views}")
            self.p4_mgr.set_view(views)
            self.p4_mgr.sync_all()

            # 进行分发
            dist_destinations = []
            for success_id in success_ids:
                hotfix_version = self.__match_hotfix_dist_path(success_id)

                island_info = self.__get_island_type_by_id(success_id)
                if not island_info:
                    raise PyframeException(f"未解析到资源{success_id}的ID信息, ")

                ab_src = os.path.join(self.ab_path, "art", island_info)
                ab_manifest_src = os.path.join(self.ab_path, "art", f"{island_info}.h3dmanifest")
                if hotfix_version:  # 热更
                    major_version, hotfix_version = self.__get_version_and_hotfix(hotfix_version)
                    dst = os.path.join(
                        self.workspace,
                        self.ab_hotfix_dir_prefix.format(branch=major_version, hotfix_version=hotfix_version, platform=self.platform).replace(
                            "//", ""
                        ),
                        island_info,
                    )
                    path_mgr.mkdir(dst)
                    path_mgr.xcopy(ab_src, dst, dst_is_file=False)
                    path_mgr.xcopy(ab_manifest_src, dst, dst_is_file=False)
                    dist_destinations.append(dst)
                else:  # 默认版内
                    dst = os.path.join(
                        self.workspace, self.ab_branch_dir_prefix.format(branch=current_branch, platform=self.platform).replace("//", ""), island_info
                    )
                    path_mgr.mkdir(dst)
                    path_mgr.xcopy(ab_src, dst, dst_is_file=False)
                    path_mgr.xcopy(ab_manifest_src, dst, dst_is_file=False)
                    dist_destinations.append(dst)

                    # 如果不是特殊分支，则分发到主支
                    if not is_special_branch:
                        dst = os.path.join(self.workspace, self.ab_trunk_dir_prefix.format(platform=self.platform).replace("//", ""), island_info)
                        path_mgr.mkdir(dst)
                        path_mgr.xcopy(ab_src, dst, dst_is_file=False)
                        path_mgr.xcopy(ab_manifest_src, dst, dst_is_file=False)
                        dist_destinations.append(dst)
            env_mgr.set_dist_destinations(platform=self.platform, destinations=dist_destinations)

        else:
            log.warning("没有打包成功的资源，无需分发提交")

    def submit_p4(self):
        """
        提交P4资源
        """
        # 获取分发路径
        dist_destinations = env_mgr.get_dist_destinations(platform=self.platform)
        reconciles = []
        for dist_destination in dist_destinations:
            reconciles += self.p4_mgr.reconcile_without_tmp(f"{dist_destination}/...")
        # 如果有资源需要提交，则提交
        if reconciles:
            # 原始资源提交人、流水线名称、流水线编号、流水线链接
            users = env_mgr.get_users(platform=self.platform)
            desc = f"uploader:{''.join(users)}\n"
            desc += f"Identity:#Jenkins#{env.pipeline.function_name()}#{env.pipeline.build_num()}"
            desc += f"link:{env.pipeline.build_url()}"

            submit_rets = self.p4_mgr.submit(desc=desc, revert_if_failed=True)
            if submit_rets:
                submitted_change = submit_rets[0].get("change")
                env_mgr.set_submit_changelist(platform=self.platform, changelist=submitted_change)

        # 获取失败的资源
        report = env_mgr.get_report(platform=self.platform)
        fail_ids = report.get("fail_ids")
        if not fail_ids:
            # 记录最后一次打包的changelist
            s_changelist = env_mgr.get_changelist(platform=self.platform)
            global_env_mgr.set_last_changelist(platform=self.platform, changelist=s_changelist)
        else:
            raise PyframeException(f"存在打包失败的资源，请修改后重新提交资源")

    def upload_report(self):
        """
        上传打包报告
        """
        reports = path_mgr.glob(self.report_path, "*.html")
        if not reports:
            return log.warning("无打包报告生成")
        report = reports[0]
        renamed_report = rf"{self.report_path}\report_{env.pipeline.build_num()}.html"
        log.info(f"重命名打包报告为: {renamed_report}")
        path_mgr.move(report, renamed_report)
        dst = advance.upload_pipeline_log(renamed_report)
        if dst:
            env_mgr.set_package_report(platform=self.platform, package_report=dst)

    def upload_log(self):
        """
        上传日志
        """
        if not path_mgr.exists(self.package_log):
            return log.warning("无打包日志生成")
        dst = advance.upload_pipeline_log(self.package_log)
        if dst:
            env_mgr.set_log(platform=self.platform, log_path=dst)

    @staticmethod
    def get_notify_info() -> str:
        """
        组织通知信息
        Returns:
            str: 通知信息
        """
        msg = ""
        for platform in ["android", "ios"]:
            msg += f"**{platform}:** \n"
            package_report = env_mgr.get_package_report(platform=platform)
            msg += f"**打包报告:** [打包报告]({package_report})\n" if package_report else ""
            logs = env_mgr.get_log(platform=platform)
            msg += f"**打包日志:** [打包日志]({logs})\n" if logs else ""
            source_changelist = env_mgr.get_changelist(platform=platform)
            msg += f"**原始资源:** {source_changelist}\n"
            submitted_change = env_mgr.get_submit_changelist(platform=platform)
            if submitted_change:
                msg += f"**AB 资源:** {submitted_change}\n"
            else:
                error_msg = env.get("PYFRAME_EXCEPTION_MESSAGE")
                if error_msg:
                    msg += ""
                else:
                    msg += f"**AB 资源:** 与P4服务器文件一致\n"

            report = env_mgr.get_report(platform=platform)
            success_ids = report.get("success_ids")
            fail_ids = report.get("fail_ids")
            msg += f"**打包数量:** {len(success_ids + fail_ids)}\n" if success_ids is not None and fail_ids is not None else ""

            success_id_str = ""
            if success_ids:
                msg += f"**成功数量:** {len(success_ids)}\n"
                success_id_str = ", ".join(success_ids)
            msg += f"**成功 ID:** {success_id_str}\n" if success_id_str else ""

            fail_id_str = ""
            if fail_ids:
                fail_id_str = ", ".join(fail_ids)
            msg += f"**失败 ID:** {fail_id_str}\n" if fail_id_str else ""
        return msg

    @staticmethod
    def get_android_ios_users() -> list:
        """
        获取通知用户
        Returns:
            list: 通知用户
        """
        android_users = env_mgr.get_users(platform="android")
        ios_users = env_mgr.get_users(platform="ios")
        users = []
        if android_users:
            users += android_users
        if ios_users:
            users += ios_users
        return users
