import json

from frame import *


class GlobalEnvMgr:
    @staticmethod
    def set_last_changelist(platform: str, changelist: str):
        function_name = env.pipeline.function_name()
        env.set_global({f"{function_name}-{platform}": changelist})

    @staticmethod
    def get_last_changelist(platform: str):
        function_name = env.pipeline.function_name()
        return env.get_global(f"{function_name}-{platform}", "0")


class EnvMgr:
    @staticmethod
    def get_workspace():
        return os.environ.get("WORKSPACE")

    @staticmethod
    def get_debug():
        return common.str2bool(os.environ.get("debug"))

    @staticmethod
    def get_first_changelist():
        return os.environ.get("first_changelist")

    @staticmethod
    def get_platform():
        platform = os.environ.get("PLATFORM")
        if platform not in ["android", "ios"]:
            raise PyframeException("PLATFORM is not android or ios")
        return platform

    @staticmethod
    def set_source_paths(source_paths: list):
        env.set({"source_paths": json.dumps(source_paths)})

    @staticmethod
    def get_source_paths():
        return json.loads(env.get("source_paths", "[]"))

    @staticmethod
    def set_relative_source(relative_source: str):
        env.set({"relative_source": relative_source})

    @staticmethod
    def get_relative_source():
        return env.get("relative_source", "")

    @staticmethod
    def set_report(platform: str, report: dict):
        return env.set({f"{platform}_report": report})

    @staticmethod
    def get_report(platform: str):
        return env.get(f"{platform}_report", {})

    @staticmethod
    def set_resource_suffix(resource_id: str, suffix: str):
        return env.set({resource_id: suffix})

    @staticmethod
    def get_resource_suffix(resource_id: str):
        return env.get(resource_id, "")

    @staticmethod
    def set_dist_destinations(platform: str, destinations: list):
        return env.set({f"{platform}_dist_destinations": destinations})

    @staticmethod
    def get_dist_destinations(platform: str):
        return env.get(f"{platform}_dist_destinations", [])

    @staticmethod
    def set_source_uploader(uploader: str):
        return env.set({"source_uploader": uploader})

    @staticmethod
    def get_source_uploader():
        return env.get("source_uploader", "")

    @staticmethod
    def set_log(platform: str, log_path: str):
        return env.set({f"{platform}_log": log_path})

    @staticmethod
    def get_log(platform: str):
        return env.get(f"{platform}_log")

    @staticmethod
    def set_package_report(platform: str, package_report: str):
        return env.set({f"{platform}_package_report": package_report})

    @staticmethod
    def get_package_report(platform: str):
        return env.get(f"{platform}_package_report")

    @staticmethod
    def set_changes(platform: str, changes: list):
        return env.set({f"{platform}_changes": changes})

    @staticmethod
    def get_changes(platform: str):
        return env.get(f"{platform}_changes")

    @staticmethod
    def set_users(platform: str, users: list):
        return env.set({f"{platform}_users": users})

    @staticmethod
    def get_users(platform: str):
        return env.get(f"{platform}_users")

    @staticmethod
    def set_changelist(platform: str, changelist: list):
        return env.set({f"{platform}_changelist": changelist})

    @staticmethod
    def get_changelist(platform: str):
        return env.get(f"{platform}_changelist")

    @staticmethod
    def set_submit_changelist(platform: str, changelist: list):
        return env.set({f"{platform}_submit_changelist": changelist})

    @staticmethod
    def get_submit_changelist(platform: str):
        return env.get(f"{platform}_submit_changelist")


env_mgr = EnvMgr()
global_env_mgr = GlobalEnvMgr()
