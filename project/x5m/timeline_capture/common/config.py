import getpass
import os
from pathlib import Path

from frame import env, log


class TimelineCaptureConfig:
    def __init__(self):
        # 系统环境变量
        self.unity = "unity"
        self.art_trunk_branch = env.get("ARTTUNK_BRANCH", "")
        self.project_path = os.path.join(env.pipeline.workspace(), self.art_trunk_branch, "arttrunk", "mobile_dancer", "arttrunk", "client")
        self.artifacts = os.path.join(env.pipeline.workspace(), self.art_trunk_branch, "artifacts")  # 保存缩略图的路径
        self.report_path = os.path.join(env.pipeline.workspace(), self.art_trunk_branch, "reports")  # 保存报告的路径
        self.webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1d143382-fafd-40fd-8599-262860acbb36"
        self.system_user = getpass.getuser()
        self.unity_log = rf"C:\Users\<USER>\AppData\Local\Unity\Editor\Editor.log"
        self.unity_log_admin = rf"C:\Users\<USER>\AppData\Local\Unity\Editor\Editor.log"

        # P4 配置
        self.port = "x5_mobile.p4.com:1666"
        self.user = "dgm_bkci"
        self.password = "x5m12345"
        self.logger = log

        # S3 配置
        self.endpoint = "s3.h3d.com.cn"
        self.access_key = "VCQSFC3BZ0CZEBB5HHJG"
        self.secret_key = "29ocvfw6qZWhXmvCmsASnfQuYBf9BzZZQX3j7cwf"
        self.secure = False
        # self.expires = 7  # 分享链接有效期，单位：天
        self.owner_name = "timeline_x5mobile"

        # 网页工具
        self.video_callback_uri = "/video/recording/callback"
        self.picture_callback_uri = "/preview/callback"
        self.update_callback_uri = "/update/environment/callback"

        # 环境变量
        self.timeline_id = env.get("TIMELINE_ID", "")
        self.sky_color = env.get("SKY_COLOR", "")
        self.series = env.get("SERIES", "")
        self.sub = env.get("SUB", "all")
        self.p4_path = env.get("P4_PATHS", "").split(",")
        self.expire = env.get("EXPIRE", 2)
        self.m_resources = env.get("M_RESOURCES", "")
        self.f_resources = env.get("F_RESOURCES", "")
        self.unity_username = env.get("UNITY_USERNAME", "<EMAIL>")
        self.unity_password = env.get("UNITY_PASSWORD", "Unity.2022")
        self.makeup_ids = env.get("MAKEUP_IDS")
        self.makeup_style = env.get("MAKEUP_STYLE")
        self.child_id = env.get("CHDID")
        self.derivative_id = env.get("YANSHENGID")
        self.exten_key = env.get("EXTENDKEY")


class TimelineCapturePictureConfigDev(TimelineCaptureConfig):
    def __init__(self):
        super().__init__()
        self.client = "x5m_timeline_capture_picture_dev"
        self.bucket_name = "devops-test"
        self.host = "https://dgm-timeline-test.h3d.com.cn"
        self.content_type = "image/png"


class TimelineCapturePictureConfigProd(TimelineCaptureConfig):
    def __init__(self):
        super().__init__()
        self.client = "x5m_timeline_capture_picture_prod"
        self.bucket_name = "devops-test"
        self.host = "https://dgm-timeline.h3d.com.cn"
        self.content_type = "image/png"


class TimelineCaptureVideoConfigDev(TimelineCaptureConfig):
    def __init__(self):
        super().__init__()
        self.client = "x5m_timeline_capture_video_dev"
        self.bucket_name = "devops-test"
        self.host = "https://dgm-timeline-test.h3d.com.cn"
        self.content_type = "video/mp4"
        self.unity = r"Unity.exe" if not Path(self.unity).exists() else self.unity


class TimelineCaptureVideoConfigProd(TimelineCaptureConfig):
    def __init__(self):
        super().__init__()
        self.client = "x5m_timeline_capture_video_prod"
        self.bucket_name = "devops-test"
        self.host = "https://dgm-timeline.h3d.com.cn"
        self.content_type = "video/mp4"
        self.unity = r"Unity.exe" if not Path(self.unity).exists() else self.unity


class TimelineCaptureUpdateConfigDev(TimelineCaptureConfig):
    def __init__(self):
        super().__init__()
        self.client = "x5m_timeline_capture_update_dev"
        self.bucket_name = "devops-test"
        self.host = "https://dgm-timeline-test.h3d.com.cn"


class TimelineCaptureUpdateConfigProd(TimelineCaptureConfig):
    def __init__(self):
        super().__init__()
        self.client = "x5m_timeline_capture_update_dev"
        self.bucket_name = "devops-test"
        self.host = "https://dgm-timeline.h3d.com.cn"
