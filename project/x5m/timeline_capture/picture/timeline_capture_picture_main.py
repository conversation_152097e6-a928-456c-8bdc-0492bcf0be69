# coding=utf-8
import os
from pathlib import Path

import boto3

from frame import env, cmd, wechat, Perforce, advance, PyframeException, log, path_mgr
from project.x5m.timeline_capture.common.utils import callback, mk_report_dir, get_extra_info

webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1d143382-fafd-40fd-8599-262860acbb36"
debug = env.get("DEBUG", 0)  # 获取环境变量，DEBUG=1时，使用测试环境配置；DEBUG=0时，使用正式环境配置
if int(debug) == 1:
    from project.x5m.timeline_capture.common.config import TimelineCapturePictureConfigDev

    config = TimelineCapturePictureConfigDev()
else:
    from project.x5m.timeline_capture.common.config import TimelineCapturePictureConfigProd

    config = TimelineCapturePictureConfigProd()

# 初始化P4
p4 = Perforce(port=config.port, user=config.user, password=config.password, logger=config.logger)

# 初始化s3
s3 = boto3.resource(
    service_name="s3",
    endpoint_url=f"https://{config.endpoint}",
    aws_access_key_id=config.access_key,
    aws_secret_access_key=config.secret_key,
)


@advance.stage(stage="初始化参数")
def init_params(**kwargs):
    timeline_id = config.timeline_id
    if not timeline_id:
        raise PyframeException("timeline_id不能为空")

    sky_color = config.sky_color
    if not sky_color:
        raise PyframeException("sky_color不能为空")

    series = config.series
    if not series:
        raise PyframeException("series不能为空")

    p4_path = config.p4_path
    if not p4_path:
        raise PyframeException("p4_path不能为空")

    m_resources = config.m_resources
    if not m_resources:
        raise PyframeException("m_resources不能为空")

    f_resources = config.f_resources
    if not f_resources:
        raise PyframeException("f_resources不能为空")


@advance.stage(stage="获取P4资源")
def sync_p4(**kwargs):
    view = []
    client = config.client
    workspace = env.get("WORKSPACE")
    if not workspace:
        workspace = env.pipeline.workspace()
    workspace = os.path.join(workspace, config.art_trunk_branch)

    for timeline_path in config.p4_path:
        if timeline_path.startswith("//x5_mobile/mr/art_release/art_src/timeline/"):
            temp_list = timeline_path.split("/")[-3:]
        else:
            temp_list = timeline_path.split("/")[6:]
        relative_path = "/".join(temp_list)
        view.append(f"{timeline_path}/... //{client}/arttrunk/mobile_dancer/arttrunk/client/assets/resources/art/{relative_path}/...")

    p4.login()
    p4.set_workspace(client=client, view=view, options=["clobber", "rmdir"], root=workspace)
    p4.update_all(force_sync=True)


@advance.stage(stage="获取缩略图")
def capture_picture(**kwargs):
    mk_report_dir(config.artifacts)
    cmds = f'"{config.unity}" -batchmode -executeMethod PreviewTexture.open '
    cmds += f"-buildTarget Android "
    cmds += f"-projectPath={config.project_path} "
    cmds += f"--id {config.timeline_id} "
    cmds += f"--sky-color {config.sky_color} "
    cmds += f"--m-resources {config.m_resources} "
    cmds += f"--f-resources {config.f_resources} "
    cmds += f"--series {config.series} "
    cmds += f'--sub {config.sub.replace("|", ",")} '
    cmds += f"--save-path {config.artifacts} "
    if config.makeup_ids:
        cmds += f"--makeup {config.makeup_ids} "
    if config.makeup_style:
        cmds += f"--styleid {config.makeup_style} "
    cmds += f"-username {config.unity_username} "
    cmds += f"-password {config.unity_password} "
    cmds += f"-force-free "
    ret = cmd.run_shell(
        cmds=[cmds],
        workdir=env.pipeline.workspace(),
    )
    unity_log_path = "C:/Users/<USER>/AppData/Local/Unity/Editor/Editor.log"
    if ret[0] != 0:
        errors = []
        for line in Path(unity_log_path).read_text().splitlines():
            if "Unity has not been activated with a valid License" in line:
                errors.append(line)
        if len(errors) > 0:
            raise PyframeException(f"获取缩略图失败,机器unity证书过期")
        else:
            raise PyframeException(f"获取缩略图失败,原因未知,ret:{ret}")


@advance.stage(stage="上传缩略图")
def upload_picture(**kwargs):
    file_name = f"{config.timeline_id}{config.sub}.png"  # 本地文件名称
    file_path = os.path.join(config.artifacts, file_name)  # 本地文件路径
    object_name = f"{config.timeline_id}/{file_name}".replace("#", "")  # 远程文件名称
    if Path(file_path).exists():
        # 上传文件
        try:
            s3.Bucket(config.bucket_name).upload_file(file_path, object_name, ExtraArgs={"ACL": "public-read", "ContentType": config.content_type})
        except Exception as e:
            raise PyframeException(f"上传缩略图失败, e:{e}")
        log.info(
            f"上传预览图成功: \n"
            f"bucket_name: {config.bucket_name} \n"
            f"file_path: {file_path} \n"
            f"object_name: {object_name} \n"
            f"content_type: {config.content_type}"
        )

        # 组织URL访问链接
        share_url = f"https://{config.endpoint}/{config.owner_name}:{config.bucket_name}/{object_name}"

        # 设置环境变量
        env.set({"SHARE_URL": share_url})
    else:
        raise PyframeException(f"在指定路径: {file_path} 未找到预览图，请检查是否生成成功")


@advance.stage(stage="上传unity日志")
def upload_unity_logs(**kwargs):
    file_path = config.unity_log if path_mgr.exists(config.unity_log) else config.unity_log_admin
    log.info(f"unity log: {file_path}")
    if path_mgr.exists(file_path):
        log_url = advance.upload_pipeline_log(file_path)
        env.set({"LOG_URL": log_url})
    else:
        log.warn(f"unity 日志不存在，请检查unity是否运行或日志目录是否正确")


@advance.stage(stage="回调网页工具")
def call_back(**kwargs):
    callback_url = f"{config.host}{config.picture_callback_uri}"
    log.info(f"callback_url: {callback_url}")
    callback(callback_url)


def do_timeline_success(**kwargs):
    content = get_extra_info()
    wechat.send_unicast_post_success(content=content)
    wechat.send_multicast_post_success(webhook=webhook, content=content)
    advance.insert_pipeline_history_on_success()


def do_timeline_failure(**kwargs):
    content = get_extra_info()
    wechat.send_unicast_post_failure(content=content, rescue=False)
    wechat.send_multicast_post_failure(webhook=webhook, content=content)
    advance.insert_pipeline_history_on_failure()


def do_timeline_cancel(**kwargs):
    content = get_extra_info()
    wechat.send_unicast_post_canceled(content=content)
    wechat.send_multicast_post_canceled(webhook=webhook, content=content)
    advance.insert_pipeline_history_on_canceled()
