# coding=utf-8
from frame import wechat, env, Perforce, advance
from project.x5m.timeline_capture.common.utils import callback

debug = env.get("DEBUG", 0)  # 获取环境变量，DEBUG=1时，使用测试环境配置；DEBUG=0时，使用正式环境配置
if debug == 1 or debug == "1":
    from project.x5m.timeline_capture.common.config import TimelineCapturePictureConfigDev

    config = TimelineCapturePictureConfigDev()
else:
    from project.x5m.timeline_capture.common.config import TimelineCapturePictureConfigProd

    config = TimelineCapturePictureConfigProd()

# 初始化P4
p4 = Perforce(port=config.port, user=config.user, password=config.password, logger=config.logger)


@advance.stage(stage="更新图片流水线")
def update_picture(**kwargs):
    pass


@advance.stage(stage="更新视频流水线")
def update_video(**kwargs):
    pass


@advance.stage(stage="回调网页工具")
def call_back(**kwargs):
    """
    回调函数
    """
    callback_url = f"{config.host}{config.update_callback_uri}"
    env.set({"SHARE_URL": env.pipeline.build_url()})
    callback(callback_url)


def __get_msg():
    picture_machine = env.get("PICTURE_SERVER")
    video_machine = env.get("PICTURE_SERVER")
    arttrunk_branch = env.get("ARTTRUNK_BRANCH")
    task_id = env.get("TASK_ID")
    arttrunk = env.get("ARTTRUNK")
    pyframe = env.get("PYFRAME")
    common_resources = env.get("PYFRAME")

    msg = f"**预览机器**: {picture_machine}\n"
    msg += f"**录屏机器**: {video_machine}\n"
    msg += f"**arttrunk分支**: {arttrunk_branch}\n"
    msg += f"**task_id**: {task_id}\n"
    msg += f"**是否更新arttrunk**: {arttrunk}\n"
    msg += f"**是否更新pyframe**: {pyframe}\n"
    msg += f"**需要更新的通用资源**: {common_resources}"
    return msg


def do_timeline_success(**kwargs):
    wechat.send_unicast_post_success(user_list=[], content=__get_msg())
    wechat.send_multicast_post_success(webhook=config.webhook)
    advance.insert_pipeline_history_on_success()


def do_timeline_failure(**kwargs):
    wechat.send_unicast_post_failure(user_list=[], content=__get_msg())
    wechat.send_multicast_post_failure(webhook=config.webhook)
    advance.insert_pipeline_history_on_failure()


def do_timeline_cancel(**kwargs):
    wechat.send_unicast_post_canceled(user_list=[], content=__get_msg())
    wechat.send_multicast_post_canceled(webhook=config.webhook)
    advance.insert_pipeline_history_on_canceled()
