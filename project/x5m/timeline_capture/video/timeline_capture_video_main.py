# coding=utf-8
import os
from pathlib import Path
import re
import time

import boto3

from frame import env, Perforce, cmd, wechat, advance, PyframeException, log, path_mgr
from project.x5m.timeline_capture.common.utils import callback, analysis_report, mk_report_dir, get_extra_info

webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1d143382-fafd-40fd-8599-262860acbb36"
male_resources = env.get("MALE_RESOURCE", [])
female_resources = env.get("FEMALE_RESOURCE", [])

debug = env.get("DEBUG", 0)  # 获取环境变量，DEBUG=1时，使用测试环境配置；DEBUG=0时，使用正式环境配置
if int(debug) == 1:
    from project.x5m.timeline_capture.common.config import TimelineCaptureVideoConfigDev

    config = TimelineCaptureVideoConfigDev()
else:
    from project.x5m.timeline_capture.common.config import TimelineCaptureVideoConfigProd

    config = TimelineCaptureVideoConfigProd()

# 初始化P4
p4 = Perforce(port=config.port, user=config.user, password=config.password, logger=config.logger)

# 初始化s3
s3 = boto3.resource(
    service_name="s3",
    endpoint_url=f"https://{config.endpoint}",
    aws_access_key_id=config.access_key,
    aws_secret_access_key=config.secret_key,
)


@advance.stage(stage="初始化参数")
def init_params(**kwargs):
    timeline_id = config.timeline_id
    if not timeline_id:
        raise PyframeException(f"timeline_id不能为空")

    sky_color = config.sky_color
    if not sky_color:
        raise PyframeException("sky_color不能为空")

    series = config.series
    if not series:
        raise PyframeException("series不能为空")

    p4_path = config.p4_path
    log.info(f"p4_path: {p4_path}")
    if not p4_path:
        raise PyframeException("p4_path不能为空")

    m_resources = config.m_resources
    if not m_resources:
        raise PyframeException("m_resources不能为空")

    f_resources = config.f_resources
    if not f_resources:
        raise PyframeException("f_resources不能为空")


@advance.stage(stage="获取P4资源")
def sync_p4(**kwargs):
    # TODO 临时加入
    client = config.client
    view = [
        f"//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048576/... //{client}/arttrunk/mobile_dancer/arttrunk/client/assets/resources/art/timeline/qinglvtao/1048576/...",
        f"//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048575/... //{client}/arttrunk/mobile_dancer/arttrunk/client/assets/resources/art/timeline/qinglvtao/1048575/...",
        f"//x5_mobile/mr/art_release/art_src/timeline/newmulti/1048577/... //{client}/arttrunk/mobile_dancer/arttrunk/cliet/assets/resources/art/timeline/newmulti/1048577/...",
        f"//x5_mobile/mr/art_resource/art_src/role/makeup/female/... //{client}/arttrunk/mobile_dancer/arttrunk/cliet/assets/resources/art/role/makeup/female/...",
        f"//x5_mobile/mr/art_resource/art_src/role/makeup/male/... //{client}/arttrunk/mobile_dancer/arttrunk/cliet/assets/resources/art/role/makeup/male/...",
    ]
    timeline_paths = config.p4_path
    for timeline_path in timeline_paths:
        if timeline_path.startswith("//x5_mobile/mr/art_release/art_src/timeline/"):
            temp_list = timeline_path.split("/")[-3:]
        else:
            temp_list = timeline_path.split("/")[6:]
        relative_path = "/".join(temp_list)
        view.append(f"{timeline_path}/... //{client}/arttrunk/mobile_dancer/arttrunk/client/assets/resources/art/{relative_path}/...")

    # TODO 临时逻辑
    pattern_02 = re.compile(r"02/")
    pattern_03 = re.compile(r"03/")
    for i in view:
        log.info(f"i: {i}")
        if (i.startswith("//x5_mobile/mr/art_release/art_src/role/bodypart") or i.startswith("//x5_mobile/mr/art_release/art_src/role/link")) and (
            i.endswith("02/...") or i.endswith("03/...")
        ):
            new_value = "01"
            new_strings_02 = pattern_02.sub(new_value + "/", i)
            if new_strings_02 not in view:
                view.append(new_strings_02)
            new_strings_03 = pattern_03.sub(new_value + "/", i)
            if new_strings_03 not in view:
                view.append(new_strings_03)

    workspace = env.get("WORKSPACE")
    if not workspace:
        workspace = env.pipeline.workspace()
    workspace = os.path.join(workspace, config.art_trunk_branch)

    p4.login()
    p4.set_workspace(client=client, view=view, options=["clobber", "rmdir"], root=workspace)
    p4.update_all(force_sync=True)


@advance.stage(stage="获取录屏")
def capture_video(**kwargs):
    mk_report_dir(config.report_path)
    mk_report_dir(config.artifacts)
    cmds = f'"{config.unity}" -batchmode -executeMethod CaptureTimeline.open '
    cmds += f"-buildTarget Android "
    cmds += f"-projectPath={config.project_path} "
    cmds += f"--id {config.timeline_id} "
    cmds += f"--sky-color {config.sky_color} "
    cmds += f"--m-resources {config.m_resources} "
    cmds += f"--f-resources {config.f_resources} "
    cmds += f"--series {config.series} "
    cmds += f'--sub {config.sub.replace("|", ",")} '
    cmds += f"--save-path {config.artifacts} "
    cmds += f"--logPath {config.report_path} "
    # TODO 特殊逻辑 临时
    if config.timeline_id == "1048632":
        cmds += "--firstZhanshi "

    if config.makeup_ids:
        cmds += f"--makeup {config.makeup_ids} "
    if config.makeup_style:
        cmds += f"--styleid {config.makeup_style} "
    if config.child_id:
        cmds += f"--chdId {config.child_id} "
    cmds += "--Capture "
    # timeline衍生ID
    if config.derivative_id:
        cmds += f"--yanshengId {config.derivative_id} "
    cmds += f"-username {config.unity_username} "
    cmds += f"-password {config.unity_password} "
    if config.exten_key:
        cmds += f"--extendKey {config.exten_key} "
    # TODO 测试
    ret = cmd.run_shell(
        cmds=[cmds],
        workdir=env.pipeline.workspace(),
    )
    if ret[0] != 0:
        raise PyframeException(f"获取录屏失败, ret:{ret}")


@advance.stage(stage="上传录屏视频")
def upload_video(**kwargs):
    video_path = config.artifacts  # 本地文件所在目录

    if Path(video_path).exists():
        # 上传文件
        report = os.path.join(config.report_path, f"{config.timeline_id}_{config.sky_color}.json")
        timeline_id, sky_color, videos, _ = analysis_report(report)
        share_urls = {}
        for video in videos:
            try:
                time_type_id = video.get("id")
                video_name = f"{time_type_id}.mp4"  # 本地文件名
                file_path = os.path.join(video_path, video_name)  # 本地文件路径
                gender_msg = video.get("genderMssge")
                now = time.strftime("%Y_%m_%d_%H_%M_%S")
                if gender_msg:
                    object_name = f"{timeline_id}/{timeline_id}-{gender_msg}-{sky_color}-{now}.mp4"  # 远程文件名称
                else:
                    object_name = f"{timeline_id}/{time_type_id}-{sky_color}-{now}.mp4"  # 远程文件名称
                s3.Bucket(config.bucket_name).upload_file(
                    file_path, object_name, ExtraArgs={"ACL": "public-read", "ContentType": config.content_type}
                )
            except Exception as e:
                raise PyframeException(f"上传录屏视频失败, e:{e}")
            log.info(
                f"上传录屏成功: \n"
                f"bucket_name: {config.bucket_name} \n"
                f"file_path: {file_path} \n"
                f"object_name: {object_name} \n"
                f"content_type: {config.content_type}"
            )

            # 组织URL访问链接
            share_url = f"https://{config.endpoint}/{config.owner_name}:{config.bucket_name}/{object_name}"
            share_urls.update({time_type_id[-2:]: share_url})

        # 设置环境变量
        env.set({"SHARE_URL": share_urls})
    else:
        raise PyframeException(f"在指定路径未: {video_path} 找到预视频，请检查是否生成成功")


@advance.stage(stage="上传unity录屏日志")
def upload_unity_logs(**kwargs):
    unity_log = config.unity_log if path_mgr.exists(config.unity_log) else config.unity_log_admin
    log.info(f"unity log: {unity_log}")
    if path_mgr.exists(unity_log):
        log_url = advance.upload_pipeline_log(unity_log)
        env.set({"LOG_URL": log_url})
    else:
        log.warn(f"unity 日志不存在，请检查unity是否运行或日志目录是否正确")


@advance.stage(stage="回调网页工具")
def call_back(**kwargs):
    callback_url = f"{config.host}{config.video_callback_uri}"
    log.info(f"callback_url: {callback_url}")
    callback(callback_url)


def do_timeline_success(**kwargs):
    content = get_extra_info()
    wechat.send_unicast_post_success(content=content)
    wechat.send_multicast_post_success(webhook=webhook, content=content)
    advance.insert_pipeline_history_on_success()


def do_timeline_failure(**kwargs):
    content = get_extra_info()
    wechat.send_unicast_post_failure(content=content, rescue=False)
    wechat.send_multicast_post_failure(webhook=webhook, content=content)
    advance.insert_pipeline_history_on_failure()


def do_timeline_cancel(**kwargs):
    content = get_extra_info()
    wechat.send_unicast_post_canceled(content=content)
    wechat.send_multicast_post_canceled(webhook=webhook, content=content)
    advance.insert_pipeline_history_on_canceled()
