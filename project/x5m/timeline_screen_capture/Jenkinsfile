import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.json.JsonSlurperClassic

def json_load_output(raw_out) {
    println raw_out
    def out = raw_out.trim().split('\n')
    out = out.last()
    return new JsonSlurperClassic().parseText(out)
}

PLATFORM_INFO = [:]
PYTHON = 'python'
RETURN_CODE = 0

node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        label "timeline_screenshot"
    }
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 180, unit: 'MINUTES')
        buildDiscarder(logRotator(numToKeepStr: env.numsToKeepStr, daysToKeepStr: env.daysToKeepStr))
    }
    parameters {
        text(name: 'GLOBAL', defaultValue: '{}', description: '')
        text(name: 'COMMON', defaultValue: '{}', description: '')
        text(name: 'MULTI', defaultValue: '[]', description: '')
        text(name: 'P4_PATHS', defaultValue: '', description: '')
        string(name: 'TIMELINE_ID', defaultValue: '', description: '')
        string(name: 'SKY_COLOR', defaultValue: '#000000', description: '')
        string(name: 'TASK_ID', defaultValue: '', description: '')
        booleanParam(name: 'P4_FORCE', defaultValue: false, description: '')
    }
    environment {
        KIND = 'timeline_screen_capture'
        PYTHONIOENCODING = 'utf-8'
        COMMON = "$params.COMMON"
        TASK_ID = "$params.TASK_ID"
    }
    stages {
        stage('更新流水线依赖') {
            steps {
                dir('pyframe-pipeline') {
                    script {
                        PLATFORM_INFO.WORKSPACE = env.WORKSPACE
                        PLATFORM_INFO.NODE_NAME = env.NODE_NAME
                        bat(
                                script: "\"$PYTHON\" -m pip install --upgrade pip -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn",
                                returnStdout: true
                        )
                        bat(
                                script: "\"$PYTHON\" -m pip install -r requirements.txt -i http://nexus.h3d.com.cn/repository/pypi_group/simple --trusted-host nexus.h3d.com.cn",
                                returnStdout: true
                        )
                        bat(
                                script: "git config --system core.longpaths true"
                        )
                    }
                }
            }
        }
        stage("获取处理任务ID") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        def data = [
                                workspace  : env.WORKSPACE,
                                global_val : params.GLOBAL,
                                multi_val  : params.MULTI,
                                p4_paths   : params.P4_PATHS,
                                timeline_id: params.TIMELINE_ID,
                                sky_color  : params.SKY_COLOR,
                                platform   : "Android",
                                p4_force   : params.P4_FORCE
                        ]
                        def out = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} |\"${PYTHON}\" x5m_cli.py --kind=$KIND --method=get_ins_id'",
                                encoding: 'utf8',
                                returnStdout: true)
                        out = out.trim().split('\n')
                        PLATFORM_INFO.INS_ID = new JsonSlurper().parseText(out.last())
                    }
                }
            }
        }

        stage('展示系统信息') {
            steps {
                dir('pyframe-pipeline') {
                    script {
                        def data = [
                                ins_id: PLATFORM_INFO.INS_ID
                        ]
                        powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=prepare'",
                                encoding: 'utf8'
                        )
                    }
                }
            }
        }
        stage('获取打包工具repo') {
            steps {
                dir('pyframe-pipeline') {
                    script {
                        def data = [
                                ins_id: PLATFORM_INFO.INS_ID
                        ]
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=update_or_clone_repo'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                    }
                }
            }
        }
        stage("清理上次打包资源") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        def data = [
                                ins_id: PLATFORM_INFO.INS_ID,
                        ]
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=clean_resource'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                    }
                }
            }
        }
        stage("获取P4资源") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        def data = [
                                ins_id: PLATFORM_INFO.INS_ID,
                        ]
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=get_p4_resource'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                        PLATFORM_INFO.P4_RESOURCE = json_load_output(output)
                    }
                }
            }
        }
        stage("准备打包资源") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        def data = [
                                ins_id: PLATFORM_INFO.INS_ID,
                        ]
                        // 数据太长, 使用文件传输
                        // data << PLATFORM_INFO.P4_RESOURCE
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=prepare_resource'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                        PLATFORM_INFO.PACKAGE_RESOURCE = json_load_output(output)
                    }
                }
            }
        }
        stage("打包") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        def data = [
                                ins_id: PLATFORM_INFO.INS_ID,
                        ]
//                                    data << PLATFORM_INFO.PACKAGE_RESOURCE
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=package'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                        PLATFORM_INFO.PACKAGE_RESULT = json_load_output(output)
                    }
                }
            }
        }
        stage("上传录屏文件") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        def data = [
                                ins_id: PLATFORM_INFO.INS_ID,
                        ]
                        data << PLATFORM_INFO.PACKAGE_RESULT
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=upload_video'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                        def capture_urls = json_load_output(output)
                        PLATFORM_INFO.CAPTURE_URLS = capture_urls
                    }
                }
            }
        }
        stage("上传unity log") {
            steps {
                dir("pyframe-pipeline") {
                    script {
                        def data = [
                                ins_id: PLATFORM_INFO.INS_ID,
                        ]
                        data << PLATFORM_INFO.PACKAGE_RESULT
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=upload_unity_logs'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                        def URL_INFO = json_load_output(output)
                        PLATFORM_INFO.UNITY_LOG_URL = URL_INFO['url']
                    }
                }
            }
        }

    }
    post {
        always {
            node(PLATFORM_INFO.NODE_NAME) {
                dir("${PLATFORM_INFO.WORKSPACE}\\pyframe-pipeline") {
                    script {
                        def data = [
                                ins_id    : PLATFORM_INFO.INS_ID,
                                status    : currentBuild.currentResult == 'SUCCESS' ? 0 : 1,
                                share_urls: PLATFORM_INFO['CAPTURE_URLS'],
                                task_id   : params.TASK_ID,
                        ]
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=call_back'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                    }
                }
            }

        }
        unstable {
            // 默认路径为 {slave workspace}/workspace/job_name  所以需要加两个 /..
            node(PLATFORM_INFO.NODE_NAME) {
                dir("${PLATFORM_INFO.WORKSPACE}\\pyframe-pipeline") {
                    script {
                        def data = [
                                task_id   : params.TASK_ID,
                                log_url   : PLATFORM_INFO['UNITY_LOG_URL'],
                                share_urls: PLATFORM_INFO['CAPTURE_URLS']
                        ]
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=on_unstable'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                    }
                }
            }
        }
        success {
            node(PLATFORM_INFO.NODE_NAME) {
                dir("${PLATFORM_INFO.WORKSPACE}\\pyframe-pipeline") {
                    script {
                        def data = [
                                task_id   : params.TASK_ID,
                                log_url   : PLATFORM_INFO['UNITY_LOG_URL'],
                                share_urls: PLATFORM_INFO['CAPTURE_URLS']
                        ]
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=on_success'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                    }
                }
            }
        }
        failure {
            node(PLATFORM_INFO.NODE_NAME) {
                dir("${PLATFORM_INFO.WORKSPACE}\\pyframe-pipeline") {
                    script {
                        def data = [
                                task_id   : params.TASK_ID,
                                log_url   : PLATFORM_INFO['UNITY_LOG_URL'],
                                share_urls: PLATFORM_INFO['CAPTURE_URLS']
                        ]
                        def output = powershell(
                                script: "cmd /c 'echo ${JsonOutput.toJson(data)} | \"${PYTHON}\" x5m_cli.py --kind=$KIND --method=on_failure'",
                                encoding: 'utf8',
                                returnStdout: true
                        )
                    }
                }
            }
        }
    }

}
