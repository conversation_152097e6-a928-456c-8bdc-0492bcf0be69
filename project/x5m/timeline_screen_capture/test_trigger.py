import unittest
from urllib.parse import urljoin

import jenkins


class TimelineScreenCaptureTest(unittest.TestCase):
    host = "http://art-jenkins.h3d.com.cn:8067"
    url = urljoin(host, "/job/timeline_screenshot/buildWithParameters")

    class JenkinsConfig:
        username = "<EMAIL>"
        password = "hmvvPOFjMwn3aEaIoqjU42paLd3EMCeJ"

    def test_trigger(self):
        data = dict(
            GLOBAL='{"timelineId": "1048922", "timelineSeries": "qinglvtao", "yanshengId": "", "skyColor": "#000000", "childId": ""}',
            COMMON='{"subIds": "01|02|03|04", "maleIds": "0401892201,0408892201,0406892201,0409892201,0421892201,0400880301", "femaleIds": "1401892201,1408892201,1406892201,1409892201,1421892201,1400865601", "closeSkinId_m": "", "closeSkinId_f": "", "makeupIds": "", "styleId": "", "extendKey": ""}',
            MULTI="[]",
            TIMELINE_ID="1048922",
            TASK_ID="797",
            P4_PATHS="//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048922,//x5_mobile/mr/art_release/art_src/npc,//x5_mobile/mr/art_release/art_src/role/bodypart/male/hair/0401892201,//x5_mobile/mr/art_release/art_src/role/bodypart/male/suit/0408892201,//x5_mobile/mr/art_release/art_src/role/bodypart/male/shoe/0406892201,//x5_mobile/mr/art_release/art_src/role/link/male/0409892201,//x5_mobile/mr/art_release/art_src/role/link/male/0421892201,//x5_mobile/mr/art_release/art_src/role/bodypart/male/face/0400880301,//x5_mobile/mr/art_release/art_src/role/bodypart/female/hair/1401892201,//x5_mobile/mr/art_release/art_src/role/bodypart/female/suit/1408892201,//x5_mobile/mr/art_release/art_src/role/bodypart/female/shoe/1406892201,//x5_mobile/mr/art_release/art_src/role/link/female/1409892201,//x5_mobile/mr/art_release/art_src/role/link/female/1421892201,//x5_mobile/mr/art_release/art_src/role/bodypart/female/face/1400865601",
        )
        server = jenkins.Jenkins(
            self.host,
            username=self.JenkinsConfig.username,
            password=self.JenkinsConfig.password,
        )
        number = server.build_job("timeline_screenshot", data)
        self.assertIsNotNone(number)
