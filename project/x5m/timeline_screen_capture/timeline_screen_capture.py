import json
import logging
import os
import re
import subprocess
import time
from datetime import datetime
from itertools import chain
from pathlib import Path
from typing import Iterable, Optional, Any
from urllib.parse import urljoin

import boto3
import requests

from frame.advance.advance import advance
from frame.env.env import env
from project.x5m.base_mgr import BaseWorkflow
from project.x5m.base_mgr.git_mgr import X5mGitMgr
from .config import TimelineScreenCaptureConfig


class TimelineScreenCapture(BaseWorkflow):
    config = TimelineScreenCaptureConfig
    kind = "timeline_screen_capture"
    execute_method = "CaptureTimeline.open"
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1d143382-fafd-40fd-8599-262860acbb36"

    s3 = boto3.resource(
        service_name="s3",
        endpoint_url=f"https://{config.endpoint}",
        aws_access_key_id=config.access_key,
        aws_secret_access_key=config.secret_key,
    )
    p4_base_views = (
        "//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048576/...",
        "//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048575/...",
        "//x5_mobile/mr/art_release/art_src/timeline/newmulti/1048577/...",
    )

    def workflow(self):
        return (
            ("展示系统信息", self.prepare),
            ("获取打包工具repo", self.update_or_clone_repo),
            ("清理上次打包资源", self.clean_resource),
            ("获取P4资源", self.get_p4_resource),
            ("准备打包资源", self.prepare_resource),
            ("打包-录屏", self.package),
            ("上传录屏文件", self.upload_video),
            ("上传unity log", self.upload_unity_logs),
            ("通知网页工具", self.call_back),
            # ("配置需要提交的文件", self.get_need_submit_files),
            # ("复制包到P4路径", self.copy_package_to_p4_path),
            # ("提交文件", self.submit_files_to_p4),
            # ("持久化配置", self.save_result),
            # ("备份+上传", self.always),
        )

    def __init__(
        self,
        *,
        workspace,
        root: Optional[str] = None,
        p4_force=False,
        changelist=0,
        global_val: str = None,
        common_val: str = "",
        multi_val: str = "",
        p4_paths: str = "",
        timeline_id: str = "",
        sky_color: str = "#000000",
        **kwargs,
    ):
        super().__init__(
            workspace=workspace,
            root=root,
            p4_force=p4_force,
            changelist=changelist,
            **kwargs,
        )
        self.global_val = self.format_params(global_val, default={})
        self.common_val = (
            json.loads(env.get("COMMON"))
            if env.get("COMMON")
            else self.format_params(common_val, default={})
        )
        self.multi_val = self.format_params(multi_val, default=[])
        self.timeline_id = timeline_id
        self.sky_color = sky_color
        self.p4_paths = p4_paths.split(",") if p4_paths else []
        self.start_time = datetime.now()

    def clean_resource(self, **kwargs):
        self._safe_clean(self._report_root, "开始清理打包日志")
        os.makedirs(self._report_root, exist_ok=True)
        for depot in self.p4_base_views:
            local_path = self.p4_mgr.get_files_local_path(depot)
            dst_part = self.transform_p4_to_art_trunk_path(
                local_path, auto_full_path=True
            )
            self._safe_clean(os.path.dirname(dst_part))

        self.p4_mgr.set_p4_views([], format_view=False)
        return kwargs

    @staticmethod
    def format_params(val, default=()):
        if not val:
            return default
        if isinstance(val, str):
            return json.loads(val)
        return val

    def get_p4_target(self, *args, **kwargs):
        return []

    def update_or_clone_repo(self, *args, **kwargs) -> dict:
        grm_arttrunk = X5mGitMgr(self.workspace, "arttrunk", "release")
        grm_arttrunk.update(clean=False, commit_id="origin/release", depth=1)

    def get_p4_resource(self, *args, **kwargs) -> dict:
        return super().get_p4_resource(self.p4_paths)

    def prepare_resource(
        self,
        depot_files: Iterable[str] = (),
        depot_bind_files: Iterable[str] = (),
        *args,
        **kwargs,
    ):
        # TODO: 增加复制资源路径是因为衍生物存在资源依赖， 单独文件无法打包， 后续如果可以提供资源引用， 可以移除此粗糙方法
        if not depot_files:
            depot_files = self.runtime_conf["get_p4_resource"]["output"]["depot_files"]
        if not depot_bind_files:
            depot_bind_files = self.runtime_conf["get_p4_resource"]["output"]["depot_bind_files"]
        self.prepare_base_resource(depot_files=depot_files)
        dst_paths = set()
        for depot_item in chain(depot_files, depot_bind_files):
            # 'D:\\jenkins_workspace\\plaza_role/x5_mobile/mr/art_release/art_src/plaza_role/suit/2008000102'
            local_path = self.p4_mgr.get_files_local_path(depot_item[2:])
            if not os.path.exists(local_path):
                continue
            client_path = self._project_path.lower().replace("\\", "/")
            dst_part = self.transform_p4_to_art_trunk_path(local_path)
            dst = f"{client_path}/{dst_part}"
            if depot_item == "//x5_mobile/mr/art_release/art_src/npc":
                self._safe_clean(dst, f"删除NPC路径{dst}")
            dst_path = self.copy_local_p4_to_dst(local_path, dst)
            dst_path and dst_paths.add(dst_path)

        return {"depot_files": depot_files, "dst_paths": list(dst_paths)}

    def get_depot_bind_resources(self, p4_depots: Iterable[str] = (), **kwargs):
        """

        :param p4_depots: ("//x5_mobile/mr/art_release/art_src/timeline/qinglvtao/1048919", )
        :param kwargs:
        :return:
        """
        views = set()
        for depot in p4_depots:
            views.add(self.format_p4_depot(depot))

        views_extra = set()
        for i in views:
            if (
                i.startswith("x5_mobile/mr/art_release/art_src/role/bodypart")
                or i.startswith("x5_mobile/mr/art_release/art_src/role/link")
            ) and (i.endswith("02") or i.endswith("03")):
                _v = f"{i[:-2]}01"
                views_extra.add(_v)
            if i.startswith("x5_mobile/mr/art_release/art_src/role/link/male/04"):
                male_link = re.compile(r"/04").sub("/00", i)
                if male_link not in views:
                    views_extra.add(male_link)
            elif i.startswith("x5_mobile/mr/art_release/art_src/role/link/female/14"):
                female_link = re.compile(r"/14").sub("/10", i)
                if female_link not in views:
                    views_extra.add(female_link)
        return [
            f"//{depot_path}/... //{self.p4_mgr.client}/{depot_path}/..."
            for depot_path in chain(views, views_extra)
        ], [f"//{depot_path}" for depot_path in views_extra]

    def package(
        self, depot_files: Iterable[str] = None, source_paths=None, *args, **kwargs
    ):
        # self._input_file_size_check(files)
        if not depot_files:
            depot_files = self.runtime_conf["prepare_resource"]["output"].get(
                "depot_files"
            )
        xml_path = self.struct_params(self.global_val, self.common_val, self.multi_val)
        cmd_line = (
            f"{self._platform.package_tool} -batchmode "
            f"-buildTarget {self.platform} "
            f"-executeMethod {self.execute_method} --xmlPath {xml_path} "
            f"-projectPath {self._project_path} -logFile {self.unity_log} "
        )
        cmd_line.replace("/", "\\")
        logging.info(f"pacakge with cmd: {cmd_line}")
        p = subprocess.Popen(
            cmd_line,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=self.workspace,
            shell=True,
        )
        stdout, stderr = p.communicate()
        logging.info(f"popen: {stdout}: {stderr} {p.returncode}")
        if stdout or stderr or p.returncode:
            raise ValueError(f"打包命令执行失败")
        return {"depot_files": depot_files}

    @property
    def artifacts_path(self) -> str:
        return os.path.join(self._project_path, "artifacts")

    @property
    def report_path(self) -> str:
        return os.path.join(self._project_path, "reports")

    @property
    def unity_log(self):
        return os.path.join(
            self._log_root, f"{self._platform}_{env.pipeline.build_num()}.log"
        )

    @property
    def log_path(self):
        return os.path.join(self._project_path, "logs")

    def struct_params(
        self, global_val: dict, common_val: dict, multi_val: Iterable[Any], **kwargs
    ):
        artifacts_path = self.artifacts_path
        log_path = self.log_path
        report_path = self.report_path
        global_val.update(
            {
                "savePath": artifacts_path,
                "logPath": log_path,
                "isFirstZhanshi": False,
            }
        )
        self.recreate_dir(artifacts_path)
        self.recreate_dir(log_path)
        self.recreate_dir(report_path)
        xml_path = self.generate_capture_video_params_xml(
            artifacts_path, global_val, common_val, multi_val
        )
        return xml_path

    @staticmethod
    def generate_capture_video_params_xml(
        artifacts_path, global_val, common_val, multi_val
    ):
        xml_path = os.path.join(artifacts_path, "params.xml")
        from dicttoxml import dicttoxml

        content = (
            b"<root>"
            + dicttoxml(global_val, custom_root="global", xml_declaration=False)
            + dicttoxml(common_val, custom_root="common", xml_declaration=False)
            + (
                b"".join(
                    dicttoxml(item, custom_root="multi", xml_declaration=False)
                    for item in multi_val
                )
                if multi_val
                else b""
            )
            + b"</root>"
        )
        with open(xml_path, "wb") as f:
            f.write(content)

        return xml_path

    def upload_video(self, video_path: str = "", **kwargs):
        video_path = video_path or self.artifacts_path
        if not Path(video_path).exists():
            raise RuntimeError("生成录屏失败")
        # 上传文件
        report = os.path.join(
            self.log_path, f"{self.timeline_id}_{self.sky_color}.json"
        )
        timeline_id, sky_color, videos, _ = self.analysis_report(report)
        share_urls = {}
        for video in videos:
            try:
                time_type_id = video.get("id")
                video_name = f"{time_type_id}.mp4"  # 本地文件名
                file_path = os.path.join(video_path, video_name)  # 本地文件路径
                gender_msg = video.get("genderMssge")
                now = time.strftime("%Y_%m_%d_%H_%M_%S")
                if gender_msg:
                    object_name = f"{timeline_id}/{timeline_id}-{gender_msg}-{sky_color}-{now}.mp4"  # 远程文件名称
                else:
                    object_name = f"{timeline_id}/{time_type_id}-{sky_color}-{now}.mp4"  # 远程文件名称
                self.s3.Bucket(self.config.bucket_name).upload_file(
                    file_path,
                    object_name,
                    ExtraArgs={
                        "ACL": "public-read",
                        "ContentType": self.config.content_type,
                    },
                )
            except Exception as e:
                raise ValueError(f"上传录屏视频失败, e:{e}")
            logging.info(
                f"上传录屏成功: \n"
                f"bucket_name: {self.config.bucket_name} \n"
                f"file_path: {file_path} \n"
                f"object_name: {object_name} \n"
                f"content_type: {self.config.content_type}"
            )

            # 组织URL访问链接
            share_url = f"https://{self.config.endpoint}/{self.config.owner_name}:{self.config.bucket_name}/{object_name}"
            share_urls.update({time_type_id[-2:]: share_url})

        # 设置环境变量
        return share_urls

    @staticmethod
    def analysis_report(report_path):
        """
        解析报告
        """
        if not Path(report_path).exists():
            raise ValueError(f"报告文件不存在, report_path:{report_path}")

        with open(report_path, "r") as f:
            report_data = json.load(f)
            success_list = report_data.get("successList", [])
            fail_list = report_data.get("failList", [])
            timeline_id = report_data.get("id")
            sky_color = report_data.get("skyColor").replace("#", "")
            return timeline_id, sky_color, success_list, fail_list

    def upload_unity_logs(self, **kwargs):
        unity_log = (
            self.unity_log
            if Path(self.unity_log).exists()
            else self.config.Unity.unity_log_admin
        )
        logging.info(f"unity log: {unity_log}")
        if not Path(unity_log).exists():
            raise ValueError(f"unity 日志不存在，请检查unity是否运行或日志目录是否正确")
        log_url = advance.upload_pipeline_log(unity_log)
        return {"url": log_url}

    def call_back(
        self, share_urls, status, task_id: str = "", error_msg: str = "", **kwargs
    ):
        callback_url = urljoin(self.config.host, self.config.video_callback_uri)
        logging.info(f"callback_url: {callback_url}")
        callback_data = {
            "task_id": task_id or env.get("TASK_ID", ""),
            "start_time": self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": status,
            "error_msg": error_msg,
            "data": {} if not share_urls else {"url": share_urls},
        }
        resp = requests.post(url=callback_url, data=json.dumps(callback_data))
        resp_json = resp.json()
        status = resp_json.get("code")
        error_msg = resp_json.get("error_msg")
        logging.info(f"call_back: {resp.text}")
        if status != 0:
            raise ValueError(f"回调网页工具失败, error_msg:{error_msg}")
        return status

    @staticmethod
    def get_extra_info(task_id, share_urls: str = "", log_url: str = ""):
        """
        组织通知中的额外信息
        """
        content = ""
        user = env.get("USER")
        content += f"**远程调用:** {user}\n" if user else ""
        content += f"**任务ID:** {task_id}\n" if task_id else ""
        content += "**使用环境:** 正式\n"
        if isinstance(share_urls, str):
            picture_name = share_urls.split("/")[-1]
            content += f"**预览图地址:** [{picture_name}]({share_urls})\n"
        if isinstance(share_urls, dict):
            for share_url in share_urls:
                url = share_urls.get(share_url)
                video_name = url.split("/")[-1]
                content += f"**录屏-{share_url}地址:** [{video_name}]({url})\n"
        if log_url:
            log_name = log_url.split("/")[-1]
            content += f"**unity日志:** [{log_name}]({log_url})\n"
        return content

    @classmethod
    def on_success(cls, task_id, share_urls: str = "", log_url: str = "", **kwargs):
        content = cls.get_extra_info(task_id, share_urls, log_url)
        cls.wechat.send_unicast_post_success(content=content)
        cls.wechat.send_multicast_post_success(webhook=cls.webhook, content=content)

    @classmethod
    def on_failure(cls, task_id, share_urls: str = "", log_url: str = "", **kwargs):
        content = cls.get_extra_info(task_id, share_urls, log_url)
        cls.wechat.send_unicast_post_failure(content=content, rescue=False)
        cls.wechat.send_multicast_post_failure(webhook=cls.webhook, content=content)
        advance.insert_pipeline_history_on_failure()

    @classmethod
    def on_cancel(cls, task_id, share_urls: str = "", log_url: str = "", **kwargs):
        content = cls.get_extra_info(task_id, share_urls, log_url)
        cls.wechat.send_unicast_post_canceled(content=content)
        cls.wechat.send_multicast_post_canceled(webhook=cls.webhook, content=content)
        advance.insert_pipeline_history_on_canceled()
