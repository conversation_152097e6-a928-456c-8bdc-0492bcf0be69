# coding=utf-8
import copy
import os.path
import stat
from typing import Tuple

from frame import *
from project.x5m.ui_package.config.config import WORKDIR, prefab_path, atlas_path, new_atlas_path, out_info
from project.x5m.ui_package.mgr.common_mgr import CommonMgr, Action
from project.x5m.ui_package.mgr.env_mgr import env_mgr, jenkins_env_mgr, global_env_mgr
from project.x5m.ui_package.mgr.git_mgr import ui_git_mgr
from project.x5m.ui_package.mgr.file_mgr import ui_file_mgr


class CheckMgr:
    def __init__(self):
        self._workspace = WORKDIR
        self.unity = "Unity.exe"
        # if jenkins_env_mgr.get_branch() in ["master", '7.03.0', "7.02.0_vedio", "7.04.0"]:
        #     self.unity = "C:\\Program Files\\Unity 2022.3.10f1\\Editor\\Unity.exe"
        # else:
        #     self.unity = "C:\\Program Files\\Unity\\Editor\\Unity.exe"
        # self.unity = (
        #     "C:\\Program Files\\Unity 2022.3.10f1\\Editor\\Unity.exe"
        #     if jenkins_env_mgr.get_branch() == "master"
        #     else "C:\\Program Files\\Unity\\Editor\\Unity.exe"
        # )

    def check_need_package(self):
        """
        判断是否需要打包
        """
        input_commit_id = jenkins_env_mgr.get_input_commit_id()
        input_pipeline_id = jenkins_env_mgr.get_input_pipeline_id()
        last_commit, platform = self.get_last_commit_and_platform()
        if not input_commit_id and not input_pipeline_id:
            ui_git_mgr.get_info_by_commit_id(commit_id=env_mgr.commit)
        elif input_commit_id:
            ui_git_mgr.get_info_by_commit_id(commit_id=input_commit_id.strip())
        elif input_pipeline_id:
            ui_git_mgr.get_info_by_pipeline_id(pipeline_id=input_pipeline_id.strip())
        commit = env_mgr.commit

        self._check_submit_files(last_commit_id=last_commit, commit_id=commit)

        package_by_input = env_mgr.package_by_input

        if env_mgr.action == Action.TRUE.value:
            if not env_mgr.prefab and not package_by_input:
                self.set_last_commit(platform=platform, commit=commit)
                # advance.stage_skip = True
                pipeline_mgr.stop_current_build()
        elif env_mgr.action == Action.DELETE_P4.value:
            log.info("action = delete p4")
        elif env_mgr.action == Action.FALSE.value or env_mgr.action == Action.ALL_DELETE.value:
            if not package_by_input:
                self.set_last_commit(platform=platform, commit=commit)
            # advance.stage_skip = True
            log.debug("skip package")
            pipeline_mgr.stop_current_build()
        else:
            log.warn("unknown action type")
        if platform == "android":
            env_mgr.is_android_package = env_mgr.action
        else:
            env_mgr.is_ios_package = env_mgr.action

    def _check_submit_files(self, last_commit_id: str, commit_id: str):
        """
        执行命令行，支持linux, windows, mac

        Args:
            last_commit_id: 流水线记录的上一次构建的gitlab commit id
            commit_id：本次触发的gitlab commit id
        """
        delete_list = []
        add_or_revise_list = []
        temp_list = []
        if not env_mgr.package_by_input:
            if last_commit_id:
                git_ret = ui_git_mgr.get_remote_modified_files_with_status_between(old_commit_id=last_commit_id, new_commit_id=commit_id)
                temp_list = copy.deepcopy(git_ret)
        else:
            git_ret = ui_git_mgr.get_commit_status(commit_id=commit_id)
            temp = copy.deepcopy(git_ret)
            submit_index = temp[temp.index("") + 1 :].index("") + temp.index("") + 1 + 1
            temp_list = temp[submit_index:]
        for file in temp_list:
            if len(file) != 0:
                if "D" == file[0]:
                    delete_list.append(file.split()[1])
                elif "R" == file[0]:
                    delete_list.append(file.split()[1])
                    add_or_revise_list.append(file.split()[2])
                else:
                    add_or_revise_list.append(file.split()[1])
        log.info(f"delete_list: {delete_list}")
        log.info(f"add_or_revise_list: {add_or_revise_list}")

        if len(delete_list) != 0:
            check_ret = self._check_delete_contain_prefab(input_list=delete_list)
            if len(check_ret) != 0:
                check_delete_dirs_ret = self._check_delete_dirs(delete_list=delete_list)
                log.info(f"check_delete_dirs_ret: {check_delete_dirs_ret}")
                if len(check_delete_dirs_ret) != 0:
                    env_mgr.action = Action.DELETE_P4.value
                    env_mgr.delete_p4_files = self._rm_dup_and_get_assets(input_list=check_delete_dirs_ret)
                else:
                    # 增加目录过滤，过滤出已经不存在的目录，如果目录不存在，则不需要进行打包
                    # 此处应对程序移动目录的情形
                    check_ret = self._exist_dirs(check_ret)
                    check_list = check_ret + add_or_revise_list
                    self._get_action_result(add_or_revise_list=check_list)
            else:
                if len(temp_list) == len(delete_list) and len(add_or_revise_list) == 0:
                    env_mgr.action = Action.ALL_DELETE.value
                else:
                    self._get_action_result(add_or_revise_list=add_or_revise_list)
        else:
            self._get_action_result(add_or_revise_list=add_or_revise_list)

    def _exist_dirs(self, dirs: list) -> list:
        """
        检查目录是否存在，如果存在则返回目录列表，不存在则返回空列表
        Args:
            dirs:

        Returns:

        """
        exist_dirs = []
        for d in dirs:
            abs_path = os.path.join(self._workspace, d)
            log.info(f"abs_path: {abs_path}")
            if path_mgr.exists(abs_path):
                exist_dirs.append(d)
        return exist_dirs

    def _get_action_result(self, add_or_revise_list: list):
        """
        对文件列表进行获取打包需要的文件列表并记录

        Args：
            add_or_revise_list：添加或修订的文件列表
        """
        deal_ret = self._deal_prefab_and_atlas(submit_files=add_or_revise_list)
        if deal_ret:
            env_mgr.action = Action.TRUE.value
            env_mgr.prefab = self._rm_dup_and_get_assets(deal_ret[0])
            env_mgr.atlas = self._rm_dup_and_get_assets(deal_ret[1])
        else:
            env_mgr.action = Action.FALSE.value

    def _deal_prefab_and_atlas(self, submit_files: list) -> bool or list and list:
        """
        对提交列表进行处理获取打包需要的文件列表

        Args:
            submit_files：提交文件列表
        Returns:
            prefab_temp: 打包需要的prefab文件列表
            atlas_temp：打包需要的patlas文件列表
        """
        prefab_files_list = []
        atlas_files_list = []
        for i in submit_files:
            if len(i) >= len(prefab_path) and "/".join(i.split("/")[0:7]) == prefab_path:
                prefab_files_list.append("/".join(i.split("/")[:-1]))
            elif len(i) >= len(atlas_path) and "/".join(i.split("/")[0:7]) == atlas_path:
                atlas_files_list.append("/".join(i.split("/")[:-1]))
            # 活动图集移出包外，将新路径加上
            elif len(i) >= len(new_atlas_path) and "/".join(i.split("/")[0:7]) == new_atlas_path:
                atlas_files_list.append("/".join(i.split("/")[:-1]))
        if len(prefab_files_list) == 0 and len(atlas_files_list) == 0:
            return False
        else:
            prefab_temp = []
            atlas_temp = []
            for i in list(set(prefab_files_list)):
                index = i.find("Assets")
                if index != -1:
                    prefab_temp_item = i[index:]
                    # 去掉根目录
                    if prefab_temp_item != "Assets/Res/Editor/resources/c":
                        prefab_temp.append(i[index:])
            for i in list(set(atlas_files_list)):
                index = i.find("Assets")
                if index != -1:
                    atlas_temp_item = i[index:]
                    if atlas_temp_item != "Assets/StaticResources/art/UIAtlas/NewUI":
                        atlas_temp.append(i[index:])
        return prefab_temp, atlas_temp

    def _check_delete_dirs(self, delete_list: list) -> list:
        """
        检查删除状态的文件列表

        Args：
            delete_list: 删除文件列表
        Returns：
            delete_dir_list：过滤后的删除文件列表
        """
        delete_dir_list = []
        prefab_list = []
        for file in delete_list:
            father_path = self._workspace + "\\x5mobile\\" + "\\".join(file.split("/")[:-1])
            if father_path.endswith(os.path.join("resources", "c")) or father_path.endswith(os.path.join("NewUI", "activity")):
                continue

            log.info(f"father_path: {father_path}")
            for _, _, files in os.walk(father_path):
                for j in files:
                    if j.split("\\")[-1].split(".")[-1] == "prefab":
                        prefab_list.append(j)
            if len(prefab_list) == 0:
                delete_dir_list.append(father_path)
        return list(set(delete_dir_list))

    def _check_delete_contain_prefab(self, input_list: list) -> list:
        """
        检查删除文件列表是否在指定路径下

        Args：
            input_list：删除文件列表
        Returns：
            temp:过滤后的文件列表
        """
        temp = []
        for i in list(set(input_list)):
            index = i.find("Assets/Res/")
            if index != -1:
                temp.append(i)
        return temp

    def __rm_dup_and_get_assets(self, input_list: list) -> str:
        """
        检查文件列表是否在指定路径下
        Args：
            input_list：需要检查的文件列表
        Returns：
            view:过滤后的文件拼接的字符串
        """
        temp = []
        for i in list(set(input_list)):
            index = i.find("Assets")
            if index != -1:
                temp.append(i[index:])
        view = ""
        view += ",".join(list(set(temp)))
        return view

    def unity_pack_check(self):
        """
        使用unity进行打包检查
        """
        if env_mgr.action == Action.DELETE_P4.value or CommonMgr.plat_need_pack() == Action.TRUE.value:
            if jenkins_env_mgr.get_platform() == "android":
                platform = "Android"
            else:
                platform = "iOS"
            build_num = env.pipeline.build_num()
            prefab = self.deal_none_to_str(env_mgr.prefab)
            atlas = self.deal_none_to_str(env_mgr.atlas)
            del_p4_files = self.deal_none_to_str(env_mgr.delete_p4_files)
            pack_check_path = f"{self._workspace}\\jenkins_log\\{build_num}"
            if not os.path.exists(pack_check_path):
                path_mgr.mkdir(path=pack_check_path)
            pack_check_log = f"{pack_check_path}\\check_pack_{platform}_{build_num}.log"

            cmds = f'"{self.unity}" -quit -batchmode -logFile {pack_check_log} -projectPath "{self._workspace}\\x5mobile\\mobile_dancer\\trunk\\client" -executeMethod DealIndexTable.CheckIndexChange -buildTarget {platform} prefab_files={prefab} atlas_files={atlas} delete_p4_files={del_p4_files}'
            ret = cmd.run_shell(cmds=[cmds], workdir=self._workspace)
            if ret[0] != 0:
                log.error(f"{cmds} failed")
                advance.raise_unity_log_exception(log_path=pack_check_log)
                raise PyframeException(f"{cmds}执行失败")
        else:
            log.warn("pass")

    @staticmethod
    def deal_none_to_str(obj) -> str:
        """
        将none对象处理成字符串
        Args：
            obj：传入的对象，可能是none或字符串
        Returns:
            obj：处理成空字符串的none对象或字符串对象
        """
        if obj == None:
            return ""
        else:
            return obj

    def check_pack_pic(self):
        """
        对需要打包的图片进行检查
        """
        if env_mgr.action == Action.DELETE_P4.value or CommonMgr.plat_need_pack() == Action.TRUE.value:
            txt_path = os.path.join(self._workspace, out_info)
            if not os.path.exists(txt_path):
                log.info(f"{txt_path} don't exist")
                return
            os.chmod(txt_path, stat.S_IWRITE)
            with open(txt_path, "r", encoding="UTF-8") as f:
                not_exist_atlas = []
                need_package_prefab = []
                not_exist_index_prefabs = []
                not_exist_index_atlas = []
                not_quote_atlas = []
                out_delete_p4_paths = []
                all_lines_in_file = f.readlines()
                for index in range(len(all_lines_in_file)):
                    if all_lines_in_file[index].startswith("not_exist_atlas"):
                        not_exist_atlas = self.__out_error_info(index, all_lines_in_file)
                    if all_lines_in_file[index].startswith("need_package_prefab"):
                        need_package_prefab = self.__out_error_info(index, all_lines_in_file)
                    if all_lines_in_file[index].startswith("not_exist_index_prefabs"):
                        not_exist_index_prefabs = self.__out_error_info(index, all_lines_in_file)
                    if all_lines_in_file[index].startswith("not_exist_index_atlas"):
                        not_exist_index_atlas = self.__out_error_info(index, all_lines_in_file)
                    if all_lines_in_file[index].startswith("not_quote_atlas"):
                        not_quote_atlas = self.__out_error_info(index, all_lines_in_file)
                    if all_lines_in_file[index].startswith("out_delete_p4_paths"):
                        out_delete_p4_paths = self.__out_error_info(index, all_lines_in_file)
            self.__deal_check_package(
                not_exist_atlas, need_package_prefab, not_exist_index_prefabs, not_exist_index_atlas, not_quote_atlas, out_delete_p4_paths
            )
            env_mgr.error_info = ""
            if env_mgr.need_package_prefab == False:
                env_mgr.need_again_package_path = env_mgr.need_package_prefab_files
                log.info("需要重新打包的prefab：" + env_mgr.need_again_package_path)
            if env_mgr.not_exist_atlas == False:
                errorlist = "".join(not_exist_atlas)
                if errorlist == "":
                    env_mgr.error_info += f"**error**: prefab引用的图集不存在-错误列表为{len(not_exist_atlas)}个空值\n"
                else:
                    env_mgr.error_info += "**error**: prefab引用的图集不存在\n"
            if env_mgr.not_exist_index_prefabs == False:
                env_mgr.error_info += "**error**: prefab不在prefab-atlas索引表里\n"
            if env_mgr.not_exist_index_atlas == False:
                env_mgr.error_info += "**error**: atlas不在atlas-prefab索引表里\n"
            if env_mgr.not_quote_atlas == False:
                env_mgr.error_info += "**error**: 没有任何prefab引用的图集"
            if env_mgr.out_delete_p4_paths == False:
                # TODO 此处逻辑存疑
                # delete_p4_ab_files = env_mgr.out_delete_p4_paths_files
                # log.info("需要删除p4：" + delete_p4_ab_files)
                log.info("需要删除p4")
                # git 有删除，照样打包，不知道这里之前在干嘛
                if jenkins_env_mgr.get_platform() == "android":
                    env_mgr.is_android_package = Action.TRUE.value
                else:
                    env_mgr.is_ios_package = Action.TRUE.value
        else:
            log.warn("pass")

    def __deal_check_package(
        self,
        not_exist_atlas: list,
        need_package_prefab: list,
        not_exist_index_prefabs: list,
        not_exist_index_atlas: list,
        not_quote_atlas: list,
        out_delete_p4_paths: list,
    ):
        """
        对打包图集进行检查
        Args：
            not_exist_atlas：不存在的atlas文件列表
            need_package_prefab: 需要打包的prefab文件列表
            not_exist_index_prefabs: 不存在的prefab文件索引列表
            not_exist_index_atlas:不存在的atlas文件索引列表
            not_quote_atlas: 没有引用的atlas文件列表
            out_delete_p4_paths: 删除的p4文件路径列表
        """
        env_mgr.not_exist_atlas = False
        env_mgr.need_package_prefab = False
        env_mgr.not_exist_index_prefabs = False
        env_mgr.not_exist_index_atlas = False
        env_mgr.not_quote_atlas = False
        env_mgr.out_delete_p4_paths = False
        rm_not_res_list = self._check_delete_contain_prefab(need_package_prefab)
        if len(not_exist_atlas) == 0:
            env_mgr.not_exist_atlas = True
        if len(rm_not_res_list) == 0:
            env_mgr.need_package_prefab = True
        if len(not_exist_index_prefabs) == 0:
            env_mgr.not_exist_index_prefabs = True
        if len(not_exist_index_atlas) == 0:
            env_mgr.not_exist_index_atlas = True
        if len(not_quote_atlas) == 0:
            env_mgr.not_quote_atlas = True
        if len(out_delete_p4_paths) == 0:
            env_mgr.out_delete_p4_paths = True
        env_mgr.not_exist_atlas_files = self._rm_dup_and_get_assets(not_exist_atlas)
        env_mgr.need_package_prefab_files = self._rm_dup_and_get_assets(rm_not_res_list)
        env_mgr.not_exist_index_prefabs_files = self._rm_dup_and_get_assets(not_exist_index_prefabs)
        env_mgr.not_exist_index_atlas_files = self._rm_dup_and_get_assets(not_exist_index_atlas)
        env_mgr.not_quote_atlas_files = self._rm_dup_and_get_assets(not_quote_atlas)
        temp = ""
        temp += ",".join(out_delete_p4_paths)
        env_mgr.delete_p4_paths_files = temp

    def _rm_dup_and_get_assets(self, input_list: list) -> str:
        """
        检查文件列表是否在指定路径下
        Args：
            input_list：需要检查的文件列表
        Returns：
            view:过滤后的文件拼接的字符串
        """
        temp = []
        for i in list(set(input_list)):
            index = i.find("Assets")
            if index != -1:
                temp.append(i[index:])
        view = ""
        view += ",".join(list(set(temp)))
        return view

    def __out_error_info(self, index: int, all_lines_in_file: list) -> list:
        """
        将文件列表中的错误信息过滤掉
        Args：
            index：索引值
            all_lines_in_file：文件列表
        Returns：
            out_list:过滤后的文件列表
        """
        out_list = []
        count = all_lines_in_file[index + 1].replace("\n", "")
        for i in range(index + 1, index + +1 + int(count)):
            out_list.append(all_lines_in_file[i + 1].replace("\n", ""))
        return out_list

    @staticmethod
    def get_last_commit_and_platform() -> Tuple[str, str]:
        if jenkins_env_mgr.get_platform() == "android":
            # return env_mgr.last_commit_android, "android"
            return global_env_mgr.get_last_commit_android(), "android"
        else:
            # return env_mgr.last_commit_ios, "ios"
            return global_env_mgr.get_last_commit_ios(), "ios"

    @staticmethod
    def set_last_commit(platform: str, commit: str):
        if platform == "android":
            # env_mgr.last_commit_android = commit
            global_env_mgr.set_last_commit_android(last_commit_android=commit)
        else:
            # env_mgr.last_commit_ios = commit
            global_env_mgr.set_last_commit_ios(last_commit_ios=commit)

    @staticmethod
    def delete_temp_or_library():
        """
        当出现脚本错误时，进行删除temp和library文件件逻辑
        """
        platform = jenkins_env_mgr.get_platform()
        if platform == "android":
            check_error_times = env_mgr.get_check_android_error_times()
        elif platform == "ios":
            check_error_times = env_mgr.get_check_ios_error_times()
        else:
            raise PyframeException(f"平台信息获取有误，请检查：{platform}")

        # 当前默认为第一次
        if not check_error_times:
            check_error_times = 1
        if check_error_times and isinstance(check_error_times, str):
            check_error_times = int(check_error_times)

        log.info(f"当前是第{check_error_times}次出现脚本编译错误")
        if check_error_times > 1:
            log.info(f"脚本编译次数{check_error_times}大于1次 需要同时删除library和temp")
            ui_file_mgr.delete_library()
            ui_file_mgr.delete_temp()
        else:
            log.info(f"脚本编译次数{check_error_times}小于等于1次 仅删除temp")
            ui_file_mgr.delete_temp()

        # 此时需要更新次数
        check_error_times += 1
        if platform == "android":
            env_mgr.set_check_android_error_times(check_error_times)
        elif platform == "ios":
            env_mgr.set_check_ios_error_times(check_error_times)


ui_check_mgr = CheckMgr()
