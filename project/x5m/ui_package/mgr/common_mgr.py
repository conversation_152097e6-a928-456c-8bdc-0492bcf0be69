# coding=utf-8
from enum import Enum

from frame import *
from project.x5m.ui_package.mgr.env_mgr import jenkins_env_mgr, env_mgr


class CommonMgr:
    @staticmethod
    def plat_need_pack():
        if jenkins_env_mgr.get_platform() == "android":
            return env_mgr.is_android_package
        elif jenkins_env_mgr.get_platform() == "ios":
            return env_mgr.is_ios_package


class Action(Enum):
    TRUE = "true"
    FALSE = "false"
    ALL_DELETE = "all_delete"
    DELETE_P4 = "delete_p4"
