# coding=utf-8
from project.x5m.ui_package.config.config import <PERSON><PERSON><PERSON><PERSON><PERSON>, GITLAB_MAINTAINER
from frame import *
from project.x5m.ui_package.mgr.env_mgr import jenkins_env_mgr, env_mgr
from project.x5m.ui_package.mgr.common_mgr import CommonMgr, Action
from typing import List


class UIGitMgr:
    def __init__(self):
        self._workdir = WORKDIR
        self._branch = jenkins_env_mgr.get_branch()
        self._x5mobile_git = GitMgr(workdir=self._workdir, project_name="x5mobile")
        self._x5mconfig_git = GitMgr(workdir=self._workdir, project_name="x5mconfig")

        self._x5mobile_gitlab = GitlabMgr(
            *********************["url"],
            token=GITLAB_MAINTAINER["token"],
            project="dgm/x5mobile",
        )

    def update_x5mobile(self):
        if not Path(os.path.join(self._workdir, "x5mobile", ".git")).exists():
            self._x5mobile_git.clone(
                url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git",
                branch=self._branch,
            )
            self._x5mobile_git.exec("git lfs install")
            self._x5mobile_git.exec("git lfs pull")
        else:
            local_commit_id_head = self._x5mobile_git.get_local_latest_commit_id()
            remote_commit_id_head = self._x5mobile_gitlab.get_newest_commit_id(branch=self._branch)
            if local_commit_id_head == remote_commit_id_head:
                log.warn(f"本地分支{self._branch}已经是最新的了，无需更新")

            # 还原本地删除的文件
            deleted_files = self._x5mobile_git.get_local_deleted_files()
            for deleted_file in deleted_files:
                self._x5mobile_git.exec(f"git checkout -- {deleted_file}")

            # 还原本地修改、远程也修改的文件，避免冲突
            local_modified_files = self._x5mobile_git.get_local_modified_files()
            self._x5mobile_git.exec(f"git fetch origin {self._branch}")
            remote_modified_files = self._x5mobile_git.get_remote_modified_files_between(
                old_commit_id=local_commit_id_head,
                new_commit_id=remote_commit_id_head,
            )
            conflict_files = list(set(local_modified_files).intersection(set(remote_modified_files)))
            for conflict_file in conflict_files:
                self._x5mobile_git.exec(f"git checkout -- {conflict_file}")

            # 删除本地新增，远程也新增的文件，避免冲突
            local_new_files = self._x5mobile_git.get_local_new_files()
            conflict_files = list(set(local_new_files).intersection(set(remote_modified_files)))
            for conflict_file in conflict_files:
                path_mgr.rm(path=os.path.join(self._x5mobile_git.project_dir(), conflict_file))

            if Path(os.path.join(self._workdir, "x5mobile", ".gitattributes")).exists():
                self._x5mobile_git.exec("git rm -rf .gitattributes")
            self._x5mobile_git.exec("git merge --ff-only")
            if not Path(os.path.join(self._workdir, "x5mobile", ".gitattributes")).exists():
                self._x5mobile_git.exec("git reset HEAD .gitattributes")
                self._x5mobile_git.exec("git checkout -- .gitattributes")
            self._x5mobile_git.exec("git lfs install")
            self._x5mobile_git.exec("git lfs pull")
            self._x5mobile_git.reset()

        env_mgr.commit = self._x5mobile_git.get_local_latest_commit_id()

    def update_x5mconfig_xml(self):
        """
        检出x5mconfig的config/campaigninpack.xml文件
        """
        xml = "config/campaigninpack.xml"
        if not Path(os.path.join(self._workdir, "x5mconfig", ".git")).exists():
            path_mgr.mkdir(path=os.path.join(self._workdir, self._x5mconfig_git.project_name()))
            self._x5mconfig_git.exec("git init")
            self._x5mconfig_git.exec("git remote add -f origin http://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig.git")
            self._x5mconfig_git.exec("git config core.sparsecheckout true")
            self.__write_xml_into_config(xml=xml)
            self._x5mconfig_git.exec(f"git checkout {self._branch}")
        else:
            self._x5mconfig_git.advance_pull(branch=self._branch)

    def __write_xml_into_config(self, xml: str):
        command = f"echo {xml} >> .git/info/sparse-checkout"
        ret = cmd.run_shell(
            cmds=[command],
            workdir=self._x5mconfig_git.project_dir(),
            return_output=True,
            encoding="utf-8",
            errors="ignore",
        )
        if ret[0] != 0:
            log.error(f"{command} failed")
            raise PyframeException(f"{command}执行失败")

    def get_info_by_commit_id(self, commit_id: str):
        committer, email = self._x5mobile_gitlab.get_committer_by_commit_id(branch=self._branch, commit_id=commit_id)
        pipeline_id = self._x5mobile_gitlab.get_pipeline_id_by_commit_id(branch=self._branch, commit_id=commit_id)
        env_mgr.committer = committer
        env_mgr.commit_email = email
        env_mgr.pipeline_id = pipeline_id
        env_mgr.pipeline_id = pipeline_id
        if commit_id != env_mgr.commit:
            env_mgr.package_by_input = True
        env_mgr.commit = commit_id

    def get_info_by_pipeline_id(self, pipeline_id: str):
        commit_id = self._x5mobile_gitlab.get_commit_id_by_pipeline_id(branch=self._branch, pipeline_id=int(pipeline_id))
        committer, email = self._x5mobile_gitlab.get_committer_by_commit_id(branch=self._branch, commit_id=commit_id)
        env_mgr.committer = committer
        env_mgr.commit_email = email
        env_mgr.pipeline_id = pipeline_id
        env_mgr.commit = commit_id
        env_mgr.package_by_input = True

    def get_remote_modified_files_with_status_between(self, old_commit_id: str, new_commit_id: str) -> List[str]:
        return self._x5mobile_git.get_remote_modified_files_with_status_between(old_commit_id=old_commit_id, new_commit_id=new_commit_id)

    def get_commit_status(self, commit_id: str) -> List[str]:
        return self._x5mobile_git.get_commit_status(commit_id=commit_id)

    def upload_txt(self):
        if CommonMgr.plat_need_pack() == Action.TRUE.value:
            self._x5mobile_git.exec(f"git checkout {self._branch}")
            self._x5mobile_git.add(file_path="mobile_dancer/trunk/client/Assets/engine/UIBuild/Editor/UIAtlasRefCache.txt")
            ret = cmd.run_shell(
                cmds=[f'git commit -m "update indexTable"'],
                workdir=os.path.join(self._workdir, "x5mconfig"),
                return_output=True,
                encoding="utf-8",
            )
            if ret[0] != 0:
                log.info("txt don't need update")
                env_mgr.upload_txt = False
                return
            self._x5mobile_git.pull(branch=self._branch)
            self._x5mobile_git.push(branch=self._branch)
            env_mgr.upload_txt = True
        else:
            log.warn("pass")


ui_git_mgr = UIGitMgr()
