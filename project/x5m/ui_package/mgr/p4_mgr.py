# coding=utf-8
from frame import *
from project.x5m.ui_package.__init__ import *
from project.x5m.ui_package.mgr.env_mgr import env_mgr, jenkins_env_mgr
from project.x5m.ui_package.mgr.common_mgr import CommonMgr, Action


class UIP4Mgr:
    def __init__(self):
        self.p4_config = config.p4
        self.p4 = P4Client(host=self.p4_config.port, username=self.p4_config.username, password=self.p4_config.password, client=self.p4_config.client, charset=P4Client.Charset.CP936)
        self.p4.set_view(views=self.p4_config.views)
        self.p4.set_root(path=self.p4_config.root)
        self.p4.set_options(allwrite=True, clobber=True, rmdir=True)

    def sync_all(self):
        self.p4.sync_all(force=False)

    def upload_p4(self):
        if CommonMgr.plat_need_pack() == Action.TRUE.value:
            if jenkins_env_mgr.get_platform() == "android":
                ui_ab_path_android = f"x5mobile/mr/{env_mgr.get_res_branch()}/ResourcePublish/CDN/SourceFiles/android/..."
                submit_path = os.path.join(self.p4_config.root, ui_ab_path_android)
                ret_reconcile = self.p4.reconcile(path=submit_path)
                if ret_reconcile:
                    ret_submit = self.p4.submit(desc=f"jenkins_ui_package, pipelineID{env_mgr.pipeline_id}", revert_if_failed=True)
                    env_mgr.changelist = ret_submit[0]["change"]
                else:
                    log.info("没有改动的文件, 不需要提交")
            else:
                ui_ab_path_ios = f"x5mobile/mr/{env_mgr.get_res_branch()}/ResourcePublish/CDN/SourceFiles/ios/..."
                submit_path = os.path.join(self.p4_config.root, ui_ab_path_ios)
                ret_reconcile = self.p4.reconcile(path=submit_path)
                if ret_reconcile:
                    ret_submit = self.p4.submit(desc=f"jenkins_ui_package, pipelineID{env_mgr.pipeline_id}", revert_if_failed=True)
                    env_mgr.changelist = ret_submit[0]["change"]
                else:
                    log.info("没有改动的文件, 不需要提交")
        else:
            log.warn("pass")


ui_p4_mgr = UIP4Mgr()
