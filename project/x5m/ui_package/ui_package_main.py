# coding=utf-8

from frame import PyframeException, advance, log, pipeline_mgr, wechat
from project.x5m.ui_package.mgr.build_mgr import build_mgr
from project.x5m.ui_package.mgr.check_mgr import Action, ui_check_mgr
from project.x5m.ui_package.mgr.env_mgr import (
    env_mgr,
    global_env_mgr,
    jenkins_env_mgr,
)
from project.x5m.ui_package.mgr.file_mgr import ui_file_mgr
from project.x5m.ui_package.mgr.git_mgr import ui_git_mgr


@advance.stage(stage="准备")
def prepare(**kwargs):
    log.info(f"========== 流水线语言: {jenkins_env_mgr.get_language()} ==========")
    # 设置资源分支
    branch = jenkins_env_mgr.get_branch()
    if branch == "master":
        res_branch = "Resources"
    else:
        res_branch = f"b/{branch}"
    env_mgr.set_res_branch(res_branch=res_branch)
    # # 删除缓存文件
    # if jenkins_env_mgr.get_delete_library():
    #     ui_file_mgr.delete_library()

    # # 每次必删library
    # ui_file_mgr.delete_library()

    # if jenkins_env_mgr.get_delete_temp():
    #     ui_file_mgr.delete_temp()


@advance.stage(stage="克隆或更新git仓库")
def update_git(**kwargs):
    # 更新x5mobile可能有问题，比如png
    ui_git_mgr.update_x5mobile()
    ui_git_mgr.update_x5mconfig_xml()


@advance.stage(stage="更新p4")
def update_p4(**kwargs):
    from project.x5m.ui_package.mgr.p4_mgr import ui_p4_mgr
    ui_p4_mgr.sync_all()


@advance.stage(stage="检查是否需要打包")
def check_package(**kwargs):
    ui_check_mgr.check_need_package()


@advance.stage(stage="复制xml")
def copy_xml(**kwargs):
    ui_file_mgr.copy_xml()


@advance.stage(stage="活动链接")
def campaign_link(**kwargs):
    ui_file_mgr.campaign_link()


@advance.stage(stage="删除ab资源")
def delete_ab(**kwargs):
    ui_file_mgr.delete_ab()


@advance.stage(stage="打包检查")
def pack_check(**kwargs):
    try:
        ui_check_mgr.unity_pack_check()
    except PyframeException as e:
        if len(str(e)) < 1.5 * len("Scripts have compiler errors, 脚本有编译错误, 请程序检查代码问题"):
            ui_check_mgr.delete_temp_or_library()
            ui_check_mgr.unity_pack_check()
        else:
            raise PyframeException(e.__str__())
    log.info("打包检查通过")


@advance.stage(stage="检查打包图集")
def check_pack_pic(**kwargs):
    ui_check_mgr.check_pack_pic()


@advance.stage(stage="打包")
def package(**kwargs):
    build_mgr.package()


@advance.stage(stage="计算打包结果")
def cal_package_result(**kwargs):
    build_mgr.cal_package_result()


@advance.stage(stage="将ab包移动到分支目录")
def move_ab(**kwargs):
    ui_file_mgr.move_ab()


@advance.stage(stage="上传")
def upload(**kwargs):
    from project.x5m.ui_package.mgr.p4_mgr import ui_p4_mgr
    # 上传索引表
    if jenkins_env_mgr.get_upload_git():
        ui_git_mgr.upload_txt()
    # 上传错误结果
    build_mgr.upload_error_result()
    # 上传p4
    ui_p4_mgr.upload_p4()


def __get_msg():
    msg = f"**分支**: {jenkins_env_mgr.get_branch()}\n"

    pipeline_id = env_mgr.pipeline_id
    if pipeline_id:
        msg += f"**pipeline id**: {pipeline_id}\n"
    commit = env_mgr.commit
    if isinstance(commit, str):
        commit = commit[0:8]
    if commit:
        msg += f"**commit id**: {commit}\n"
    committer = env_mgr.committer
    if committer:
        msg += f"**提交人**: {committer}\n"

    msg += f"**安卓**: \n"
    changelist_android = env_mgr.changelist
    if changelist_android:
        msg += f"**ab包changelist**: {changelist_android}\n"
    android_error_url = env_mgr.android_error_url
    if android_error_url:
        android_error_name = android_error_url.split("/")[-1]
        msg += f"**android错误统计:** [{android_error_name}]({android_error_url})\n"
    android_log_url = env_mgr.android_log_url
    if android_log_url:
        android_log_name = android_log_url.split("/")[-1]
        msg += f"**android build日志:** [{android_log_name}]({android_log_url})\n"
    android_check_log_url = env_mgr.get_android_check_log()
    if android_check_log_url:
        android_log_name = android_check_log_url.split("/")[-1]
        msg += f"**android check日志:** [{android_log_name}]({android_check_log_url})\n"
    upload_txt = env_mgr.upload_txt
    if upload_txt:
        msg += f"**上传索引表**: {upload_txt}\n"
    else:
        msg += f"**上传索引表**: android不提交索引表\n"
    packaged_android = env_mgr.packaged
    succ_android = env_mgr.succ
    err_android = env_mgr.err
    if packaged_android:
        msg += f"**打包统计**: \n"
        msg += f"**打包数量**: {packaged_android}\n"
    if succ_android:
        msg += f"**成功数量**: {succ_android}\n"
    if err_android:
        msg += f"**失败数量**: {err_android}\n"

    msg += f"**ios**: \n"
    changelist_ios = env_mgr.changelist
    if changelist_ios:
        msg += f"**ab包changelist**: {changelist_ios}\n"
    ios_error_url = env_mgr.ios_error_url
    if ios_error_url:
        ios_error_name = ios_error_url.split("/")[-1]
        msg += f"**ios错误统计:** [{ios_error_name}]({ios_error_url})\n"
    ios_log_url = env_mgr.ios_log_url
    if ios_log_url:
        ios_log_name = ios_log_url.split("/")[-1]
        msg += f"**ios build日志:** [{ios_log_name}]({ios_log_url})\n"
    ios_check_log_url = env_mgr.get_ios_check_log()
    if ios_check_log_url:
        ios_log_name = ios_check_log_url.split("/")[-1]
        msg += f"**ios check日志:** [{ios_log_name}]({ios_check_log_url})\n"
    upload_txt = env_mgr.upload_txt
    if upload_txt:
        msg += f"**上传索引表**: {upload_txt}\n"
    else:
        msg += f"**上传索引表**: ios不提交索引表\n"

    packaged_ios = env_mgr.packaged
    succ_ios = env_mgr.succ
    err_ios = env_mgr.err
    if packaged_ios:
        msg += f"**打包统计**: \n"
        msg += f"**打包数量**: {packaged_ios}\n"
    if succ_ios:
        msg += f"**成功数量**: {succ_ios}\n"
    if err_ios:
        msg += f"**失败数量**: {err_ios}\n"

    err_info = env_mgr.error_info
    if err_info:
        msg += f"**错误信息**: {err_info}\n"
    return msg


def check_stop_build():
    is_android_package = env_mgr.is_android_package
    is_ios_package = env_mgr.is_ios_package
    if is_android_package and is_ios_package:
        if is_android_package != Action.TRUE.value and is_ios_package != Action.TRUE.value:
            log.info(f"Action.TRUE.value: {Action.TRUE.value} Action.TRUE.value: {Action.TRUE.value}")
            log.info(f"is_android_package: {is_android_package} is_ios_package: {is_ios_package}")
            pipeline_mgr.stop_current_build()
        elif is_android_package == Action.TRUE.value and is_ios_package == Action.TRUE.value and env_mgr.prefab == "":
            log.info(f"aaa Action.TRUE.value: {Action.TRUE.value} Action.TRUE.value: {Action.TRUE.value}")
            log.info(f"aaa is_android_package: {is_android_package} is_ios_package: {is_ios_package}")
            pipeline_mgr.stop_current_build()


def on_always(**kwargs):
    # 上传unity日志
    build_mgr.upload_log()
    check_stop_build()


def on_success(**kwargs):
    # env_mgr.last_commit_android = env_mgr.commit
    # env_mgr.last_commit_ios = env_mgr.commit
    global_env_mgr.set_last_commit_android(last_commit_android=env_mgr.commit)
    global_env_mgr.set_last_commit_ios(last_commit_ios=env_mgr.commit)
    wechat.send_unicast_post_success(
        user_list=[
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        content=__get_msg(),
    )
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cbbc94ca-4e9b-496c-b844-3ac9d939f2b1",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    wechat.send_unicast_post_failure(
        user_list=[
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        content=__get_msg(),
        to_admin=False,
        rescue=False,
    )
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cbbc94ca-4e9b-496c-b844-3ac9d939f2b1",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(
        user_list=[
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        content=__get_msg(),
    )
    wechat.send_multicast_post_canceled(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cbbc94ca-4e9b-496c-b844-3ac9d939f2b1",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_canceled()
