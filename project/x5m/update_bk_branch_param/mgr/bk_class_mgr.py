import json
from abc import ABCMeta, abstractmethod

from frame import env
from frame.exception.exception import PyframeException
from frame.log.log import log
from frame.pipeline_mgr.bk.bk_mgr import BkMgr
from project.x5m.update_bk_branch_param.mgr.params_options_mgr import ParamsMgr

CLASS_BLACK_LIST = ["BaseMetaClass", "ParamsBase", "PipelineParamsBase", "TemplateParamsBase", "TacPipeline", "TacTemplate"]


class BaseMetaClass(ABCMeta):
    record_cls = []

    def __new__(mcs, name, bases, namespace, **kwargs):
        cls = super().__new__(mcs, name, bases, namespace, **kwargs)
        # 排除基类和示例类
        if name not in CLASS_BLACK_LIST:
            BaseMetaClass.record_cls.append(cls)
        return cls


class ParamsBase(object, metaclass=BaseMetaClass):
    def __init__(self):
        self.bk_mgr = BkMgr(self.project_name)

    def get_cache_params(self):
        """
        查询缓存中的参数
        Returns:

        """
        raise NotImplementedError

    def set_cache_params(self):
        """
        将参数缓存
        Returns:

        """
        raise NotImplementedError

    def get_old_params(self):
        raise NotImplementedError

    def make_new_params(self, old_params: list) -> list:
        """
        组织新参数
        Returns:

        """
        for old_param in old_params:
            if old_param.get("id") == self.param_id:
                old_param["options"] = self.param_options
                old_params[old_params.index(old_param)] = old_param
                break
        return old_params

    def update_params(self, new_params: list):
        raise NotImplementedError

    @property
    @abstractmethod
    def project_name(self):
        return self.project_name

    @project_name.setter
    def project_name(self, value):
        self.project_name = value

    @property
    @abstractmethod
    def param_options(self):
        return self.param_options

    @param_options.setter
    @abstractmethod
    def param_options(self, value):
        self.param_options = value

    @property
    @abstractmethod
    def param_id(self):
        return self.param_id

    @param_id.setter
    @abstractmethod
    def param_id(self, value):
        self.param_id = value

    def start_update(self):
        # 先获取缓存参数
        cache_params = self.get_cache_params()

        # 如果缓存命中，对比缓存参数和新参数
        if cache_params:
            log.info(f"cache cached, cache_params: {cache_params}")
            # 如果参数一致直接结束
            if cache_params == self.param_options:
                log.info(f"params no change, stop update")
                return
            # 如果参数不同，则更新参数
            else:
                log.info(f"params changed, start update")
                old_params = self.get_old_params()
                new_params = self.make_new_params(old_params)
                self.update_params(new_params)
        # 如果缓存未命中，直接获取流水线或者模板中的参数，进行更新
        else:
            log.info(f"cache not found, start update")
            old_params = self.get_old_params()
            new_params = self.make_new_params(old_params)
            self.update_params(new_params)
        log.info(f"update params success, cache params")
        self.set_cache_params()


class PipelineParamsBase(ParamsBase):
    def __init__(self):
        super().__init__()
        self.pipeline_id = None

    @property
    @abstractmethod
    def pipeline_name(self):
        return self.pipeline_name

    @pipeline_name.setter
    @abstractmethod
    def pipeline_name(self, value):
        self.pipeline_name = value

    def get_pipeline_id(self):
        """
        获取pipeline ID
        Returns:

        """
        pipeline_id = self.bk_mgr.get_pipeline_id_by_name(self.pipeline_name)
        if not pipeline_id:
            raise PyframeException(f"未找到相应流水线，请检查流水线名称:【{self.pipeline_name}】是否正确")
        self.pipeline_id = pipeline_id

    def get_cache_params(self) -> str:
        """
        从MongoDB缓存中取出参数
        Returns:

        """
        self.get_pipeline_id()
        cache = env.get_global(f"{self.pipeline_id}-{self.param_id}")
        if cache:
            return json.loads(cache)
        return cache

    def set_cache_params(self):
        """
        将本次修改的参数保存到MongoDB中
        Returns:

        """
        value = json.dumps(self.param_options)
        env.set_global({f"{self.pipeline_id}-{self.param_id}": value})

    def get_old_params(self) -> list:
        """
        获取旧的参数信息
        Returns:

        """
        return self.bk_mgr.get_pipeline_params(self.pipeline_id)

    def update_params(self, new_params: list) -> bool:
        """
        更新参数
        Args:
            new_params:

        Returns:

        """
        if not new_params:
            return False
        update_ret = self.bk_mgr.update_pipeline_params(self.pipeline_id, new_params)
        return update_ret


class TemplateParamsBase(ParamsBase):
    def __init__(self):
        super().__init__()
        self.template_id = None

    @property
    @abstractmethod
    def template_name(self):
        return self.template_name

    @template_name.setter
    @abstractmethod
    def template_name(self, value):
        self.template_name = value

    def get_template_id(self):
        """
        获取模板ID
        Returns:

        """
        template_id = self.bk_mgr.get_template_id_by_name(self.template_name)
        if not template_id:
            raise PyframeException("未找到相应流水线模板，请检查流水线模板名称是否正确")
        self.template_id = template_id

    def get_cache_params(self) -> str:
        """
        从MongoDB缓存中取出参数
        Returns:

        """
        self.get_template_id()
        cache = env.get_global(f"{self.template_id}-{self.param_id}")
        if cache:
            return json.loads(cache)
        return cache

    def set_cache_params(self):
        """
        将本次修改的参数保存到MongoDB中
        Returns:

        """
        value = json.dumps(self.param_options)
        env.set_global({f"{self.template_id}-{self.param_id}": value})

    def get_old_params(self) -> list:
        """
        获取旧参数
        Returns:

        """
        return self.bk_mgr.get_template_params(self.template_id)

    def update_params(self, new_params: list):
        """
        更新参数
        Args:
            new_params:

        Returns:

        """
        if not new_params:
            return False

        return self.bk_mgr.update_template_params(self.template_id, new_params) and self.bk_mgr.update_instance_params(self.template_id, new_params)


class TacPipeline(PipelineParamsBase):
    """
    未使用模板，单独更新流水线参数示例
    """

    def __init__(self):
        super().__init__()

    @property
    def project_name(self):
        return "技术支持中心-流水线"

    @property
    def pipeline_name(self):
        return "test-lixin02"

    @property
    def param_id(self):
        return "debug"

    @property
    def param_options(self):
        return [
            {
                "key": "0",
                "value": "0",
            },
            {"key": "1", "value": "1"},
            {"key": "3", "value": "3"},
        ]


class TacTemplate(TemplateParamsBase):
    """
    通过模板更新参数示例
    """

    def __init__(self):
        super().__init__()

    @property
    def project_name(self):
        return "技术支持中心-流水线"

    @property
    def template_name(self):
        return "test-lixin02_template"

    @property
    def param_id(self):
        return "debug"

    @property
    def param_options(self):
        return [
            {
                "key": "0",
                "value": "0",
            },
            {"key": "1", "value": "1"},
            {"key": "2", "value": "2"},
        ]


class MusicEffectPackageSoundBoundVER(PipelineParamsBase):
    """
    音乐效果包更新流水线SOUND_BOUND_VER参数
    """

    def __init__(self):
        super().__init__()

    @property
    def project_name(self):
        return "炫舞手游"

    @property
    def pipeline_name(self):
        return "音效打包-美术-新"

    @property
    def param_id(self):
        return "SOUND_BOUND_VER"

    @property
    def param_options(self):
        return ParamsMgr.get_branches_info(r"^\d+\.\d+.\d+([_\w]+)?$")


class MusicEffectPackageSoundResVER(PipelineParamsBase):
    """
    音乐效果包更新流水线SOUND_RES_VER参数
    """

    def __init__(self):
        super().__init__()

    @property
    def project_name(self):
        return "炫舞手游"

    @property
    def pipeline_name(self):
        return "音效打包-美术-新"

    @property
    def param_id(self):
        return "SOUND_RES_VER"

    @property
    def param_options(self):
        return ParamsMgr().get_week_version()
