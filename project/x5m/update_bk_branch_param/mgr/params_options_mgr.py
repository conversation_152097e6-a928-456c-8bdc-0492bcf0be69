import re

from frame import P4Client
from frame.gitlab_mgr.gitlab_mgr import GitlabMgr
from project.x5m import config


class ParamsMgr:
    @staticmethod
    def get_week_version() -> list:
        """
        获取周更版本
        """
        p4_client = P4Client(
            host=config.P4_CONFIG_BKCI_P4CLIENT.get("host"),
            username=config.P4_CONFIG_BKCI_P4CLIENT.get("username"),
            password=config.P4_CONFIG_BKCI_P4CLIENT.get("password"),
            client="192_168_6_150",
        )
        branches = p4_client.dirs("//x5_mobile/mr/onlineupdate/*/*")
        new_branches = branches[::-1]
        temp = [i.split("/")[-1] for i in new_branches]
        options = [{"key": i, "value": i} for i in temp]
        return options[:10]

    @staticmethod
    def get_branches_info(ptn=r"^\d+\.\d+.\d+$"):
        """
        获取分支版本信息
        """
        git_mgr = GitlabMgr(url=config.GITLAB_MAINTAINER.get("url"), token=config.GITLAB_MAINTAINER.get("token"), project="DGM/x5mobile")
        all_branches = git_mgr.get_all_branches()

        pattern = re.compile(ptn)
        branches = [{"key": branch, "value": branch} for branch in all_branches if re.match(pattern, branch)]

        def get_value(elem):
            return elem.get("value")

        branches.sort(key=get_value, reverse=True)
        branches.insert(0, {"key": "trunk", "value": "trunk"})
        return branches
