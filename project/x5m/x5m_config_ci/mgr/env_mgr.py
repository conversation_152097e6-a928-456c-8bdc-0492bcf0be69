from frame import *


class BKEnvMgr:
    @staticmethod
    def get_is_all():
        return common.str2bool(env.get("IS_ALL", "false"))

    @staticmethod
    def get_branch():
        return env.get("BK_CI_HOOK_BRANCH")

    @staticmethod
    def get_start_user_id():
        return env.get("BK_CI_START_USER_ID")

    @staticmethod
    def get_commit_author():
        return env.get("BK_REPO_GIT_WEBHOOK_PUSH_COMMIT_AUTHOR_1")

    # BK_REPO_GIT_WEBHOOK_PUSH_BEFORE_COMMIT
    @staticmethod
    def get_before_commit():
        before_commit = env.get("BK_REPO_GIT_WEBHOOK_PUSH_BEFORE_COMMIT")
        if isinstance(before_commit, str) and len(before_commit) > 8:
            return before_commit[0:8]
        return before_commit

    # BK_REPO_GIT_WEBHOOK_PUSH_BEFORE_COMMIT
    @staticmethod
    def get_after_commit():
        after_commit = env.get("BK_REPO_GIT_WEBHOOK_PUSH_AFTER_COMMIT")
        if isinstance(after_commit, str) and len(after_commit) > 8:
            return after_commit[0:8]
        return after_commit


class JenkinsEnvMgr:
    @staticmethod
    def get_is_all():
        return common.str2bool(env.get("IS_ALL", "false"))

    # DEBUG
    @staticmethod
    def get_debug():
        return common.str2bool(env.get("DEBUG", "false"))

    @staticmethod
    def get_username():
        return env.get("GITLABUSERUSERNAME")

    @staticmethod
    def get_branch():
        return env.get("GITLABBRANCH")

    @staticmethod
    def get_after_commit():
        after_commit = env.get("GITLABAFTER")
        if isinstance(after_commit, str) and len(after_commit) > 8:
            return after_commit[0:8]
        return after_commit

    @staticmethod
    def get_before_commit():
        before_commit = env.get("GITLABBEFORE")
        if isinstance(before_commit, str) and len(before_commit) > 8:
            return before_commit[0:8]
        return before_commit


class EnvMgr:
    @staticmethod
    def set_check_results(check_result):
        env.set({"CHECK_RESULT": check_result})

    @staticmethod
    def get_check_results():
        return env.get("CHECK_RESULT", [])

    @staticmethod
    def set_is_exist_error(is_exist_error: bool = False):
        return env.set({"IS_EXIST_ERROR": is_exist_error})

    @staticmethod
    def get_is_exist_error():
        return env.get("IS_EXIST_ERROR")


bk_mgr = BKEnvMgr()
env_mgr = EnvMgr()
jenkins_mgr = JenkinsEnvMgr()
