# coding=utf-8
import stat
from pathlib import Path

from frame import PyframeException, advance, cmd, env, log, os, path_mgr
from project.x5m import LanguageEnum
from project.x5m.xml_to_byte.mgr.env_mgr import env_mgr, jenkins_env_mgr


class BuildMgr:
    def __init__(self):
        self._workdir = jenkins_env_mgr.get_workspace()
        self._res_branch = env_mgr.get_resource_branch()

    def copy_xml(self):
        """
        拷贝xml到输出目录
        """
        xml_path = os.path.join(
            self._workdir,
            f"mr\\{self._res_branch}\\ResourcePublish\\CDN\\SourceFiles\\xml_sources",
        )
        if not os.path.exists(xml_path):
            path_mgr.mkdir(xml_path)
        # 清空旧配置
        # path_mgr.rm(xml_path)
        # path_mgr.mkdir(xml_path)

        # 从x5mweek拷贝到x5mconfig
        path_mgr.xcopy(
            src=os.path.join(self._workdir, "x5mweek\\config"),
            dst=os.path.join(self._workdir, "x5mconfig\\config"),
            dst_is_file=False,
            ignore_error=True,
            quiet=True,
        )
        # 从x5mconfig拷贝到x5mobile下
        path_mgr.xcopy(
            src=os.path.join(self._workdir, "x5mconfig\\level"),
            dst=os.path.join(
                self._workdir,
                f"mr\\{self._res_branch}\\ResourcePublish\\CDN\\SourceFiles\\crossplatform\\level",
            ),
            dst_is_file=False,
            ignore_error=True,
            quiet=True,
        )
        path_mgr.xcopy(
            src=os.path.join(self._workdir, "x5mconfig\\config"),
            dst=os.path.join(
                self._workdir,
                f"mr\\{self._res_branch}\\ResourcePublish\\CDN\\SourceFiles\\crossplatform\\config",
            ),
            dst_is_file=False,
            ignore_error=True,
            quiet=True,
        )

        # 再次拷贝
        path_mgr.xcopy(
            src=os.path.join(
                self._workdir,
                f"mr\\{self._res_branch}\\ResourcePublish\\CDN\\SourceFiles\\crossplatform\\config",
            ),
            dst=os.path.join(
                self._workdir,
                f"mr\\{self._res_branch}\\ResourcePublish\\CDN\\SourceFiles\\xml_sources\\config",
            ),
            dst_is_file=False,
            ignore_error=True,
            quiet=True,
        )
        path_mgr.xcopy(
            src=os.path.join(
                self._workdir,
                f"mr\\{self._res_branch}\\ResourcePublish\\CDN\\SourceFiles\\crossplatform\\config\\server",
            ),
            dst=os.path.join(self._workdir, "mobile_dancer\\trunk\\exe\\resources\\config\\server"),
            dst_is_file=False,
            ignore_error=True,
            quiet=True,
        )

        path_mgr.rm(
            path=os.path.join(
                self._workdir,
                f"mr\\{self._res_branch}\\ResourcePublish\\CDN\\SourceFiles\\crossplatform\\config\\server",
            )
        )

    def xml_to_byte(self):
        """
        xml转byte
        """
        lang = jenkins_env_mgr.get_language()
        if lang not in LanguageEnum.__members__:
            raise PyframeException(f"未知语言类型: {lang}")

        count = 0
        while True:
            count += 1
            if count >= 3:
                log.error(f"xml to bytes error")
                raise PyframeException("xml转byte重试3次依然失败, 返回值3221225477")
            unity_path = "Unity.exe"
            unity_cmd = f'"{unity_path}" -quit -batchmode -logFile {env.pipeline.build_num()}_xmlToBytes_unity.log -projectPath {self._workdir}\\mobile_dancer\\trunk\\client\\ -executeMethod XmltransformBuf.JenkinsXmlToBytes -buildTarget android xml_path={self._workdir}\\mr\\{self._res_branch}\\ResourcePublish\\CDN\\SourceFiles\\crossplatform lite_white_path={self._workdir}\\mr\\{self._res_branch}\\ResourcePublish\\CDN\\SourceFiles\\pack\\lite\\Lite_white_list.csv forbid_open_log=true -force-free'

            # 可以增加语言参数的分支
            # if self._res_branch in ["b/6.11.0_tc", "Resources", "b/7.01.0", "b/7.02.0", "b/7.03.0", "b/7.02.0_vedio"]:
            #     unity_cmd += f" LanguageType={lang}"
            # 之后的分支都应该支持多语言，所以暂时注掉判断逻辑
            unity_cmd += f" LanguageType={lang}"
            ret = cmd.run_shell(
                cmds=[unity_cmd],
                workdir=self._workdir,
                return_output=False,
            )
            if ret[0] == 0:
                log.info(f"xml_to_byte success")
                env_mgr.set_unity_return_code(code=ret[0])
                break
            elif ret[0] == 3221225477:  # 未知错误，重试
                log.error(f"xml_to_byte return {ret[0]}, retry")
                path_mgr.rm(path=os.path.join(self._workdir, "mobile_dancer\\trunk\\client\\Library"))
                continue
            else:
                log.error(f"xml_to_byte return {ret[0]}, throw exception")
                env_mgr.set_unity_return_code(code=ret[0])
                # 解析unity日志错误
                advance.raise_unity_log_exception(log_path=self._get_xml_log_path())
                raise PyframeException(f"xml转byte失败, 未知错误, 返回值: {ret[0]}")

    def stat_result(self):
        """
        统计结果
        """
        result = {}
        bytes = ""
        success_bytes = []
        failed_bytes = []
        file_new = self._get_xml_txt_path()
        os.chmod(file_new, stat.S_IWRITE)
        with open(file_new, "r", encoding="UTF-8") as f:
            all_lines_in_file = f.readlines()
            for index in range(0, len(all_lines_in_file) - 2):
                all_lines_in_file[index] = all_lines_in_file[index].replace("/", "\\")
                bytes += all_lines_in_file[index]
                if all_lines_in_file[index].startswith("[Info][xml 转换成功]"):
                    success_bytes.append(all_lines_in_file[index])
                else:
                    failed_bytes.append(all_lines_in_file[index])
            for index in range(len(all_lines_in_file) - 2, len(all_lines_in_file)):
                bytes += all_lines_in_file[index]
        with open(file_new, "w", encoding="UTF-8") as f:
            f.write(bytes)

        failed_files = self.__get_failed_files(bytes_list=failed_bytes)
        env_mgr.set_failed_files(failed_files=failed_files)

        all_count = len(all_lines_in_file) - 2

        success_count = len(success_bytes)
        log.info(f"success_count : {success_count}")

        fail_count = len(failed_bytes)
        log.info(f"fail_count : {fail_count}")

        result["all_count"] = all_count
        result["success_count"] = len(success_bytes)
        result["fail_count"] = len(failed_bytes)
        result["change"] = "part"

        if all_count == len(success_bytes):
            result["change"] = "success"
        if all_count == len(failed_bytes):
            result["change"] = "failed"

        env_mgr.set_statistic_result(statistic_result=result)

    def _get_xml_txt_path(self):
        """
        获取xml转byte的txt文件路径, txt里好像是统计结果
        """
        xml_txt_path = os.path.join(
            self._workdir,
            f"mobile_dancer\\trunk\\client\\AssetBundleTool\\AssetBundleToolLogs\\{env.pipeline.build_num()}_xmltobytes.txt",
        )
        return xml_txt_path

    def _get_xml_log_path(self):
        """
        获取xml转byte的log文件路径, log里好像是unity日志
        """
        xml_log_path = os.path.join(self._workdir, f"{env.pipeline.build_num()}_xmlToBytes_unity.log")
        return xml_log_path

    def rename_log(self):
        """
        重命名日志
        """
        # rename txt
        file_list = os.listdir(
            path=os.path.join(
                self._workdir,
                "mobile_dancer\\trunk\\client\\AssetBundleTool\\AssetBundleToolLogs",
            )
        )
        txt_list = []
        for file in file_list:
            if file.endswith(".txt"):
                txt_list.append(file)
        txt_list.sort(
            key=lambda x: os.path.getmtime(
                os.path.join(
                    self._workdir,
                    f"mobile_dancer\\trunk\\client\\AssetBundleTool\\AssetBundleToolLogs\\{x}",
                )
            ),
            reverse=True,
        )

        src = os.path.join(
            self._workdir,
            f"mobile_dancer\\trunk\\client\\AssetBundleTool\\AssetBundleToolLogs\\{txt_list[0]}",
        )
        dst = self._get_xml_txt_path()
        if os.path.exists(path=src):
            path_mgr.move(src=src, dst=dst)

    def upload_log(self):
        # 上传 *xmltobytes.txt
        xml_txt_path = self._get_xml_txt_path()
        if os.path.exists(path=xml_txt_path):
            xml_txt_url = advance.upload_pipeline_log(path=xml_txt_path)
            env_mgr.set_xml_txt_url(url=xml_txt_url)
        else:
            log.warn(f"xml_txt_path not exists: {xml_txt_path}")

        # 上传 *xmlToBytes_unity.log
        xml_log_path = self._get_xml_log_path()
        if os.path.exists(path=xml_log_path):
            xml_log_url = advance.upload_pipeline_log(path=xml_log_path)
            env_mgr.set_xml_log_url(url=xml_log_url)
        else:
            log.warn(f"xml_log_path not exists: {xml_log_path}")

    def __get_failed_files(self, bytes_list: list) -> list:
        files_list = []
        for byte in bytes_list:
            files_list.append("/".join(byte.split("]")[3].split("\\")[3 : len(byte)]))
        return files_list

    def rm_csv(self):
        """P4提交前，将包含对应二进制文件的csv删除，不提交到P4上"""
        self.__rm_language_csv()
        p4root = Path(self._workdir)
        _dir = p4root / f"mr/{self._res_branch}/ResourcePublish/CDN/SourceFiles/crossplatform/"

        def rm_xml_bytes_csv(p4dir: Path):
            """删除目录下已有.xml.bytes的csv文件"""
            for csv in p4dir.rglob("*.csv"):
                log.info(f"找到 csv {csv.relative_to(p4root)}")
                xml_bytes = csv.parent / csv.name.replace(".csv", ".xml.bytes")
                if xml_bytes.exists():
                    path_mgr.chmod(path=csv, read=True, write=True)
                    path_mgr.rm(csv)

        rm_xml_bytes_csv(_dir / "config")
        rm_xml_bytes_csv(_dir / "level")

    def __rm_language_csv(self):
        language_root = os.path.join(self._workdir, f"mr/{self._res_branch}/ResourcePublish/CDN/SourceFiles/crossplatform/config/shared/language")
        for file in os.listdir(language_root):
            if file.endswith(".csv"):
                csv = os.path.join(language_root, file)
                path_mgr.chmod(path=csv, read=True, write=True)
                path_mgr.rm(csv)


build_mgr = BuildMgr()
