# coding=utf-8
import re

from frame import common, env
from project.x5m import LanguageEnum


class JenkinsEnvMgr:
    def __init__(self):
        pass

    def get_force_p4(self) -> bool:
        return common.str2bool(env.get("FORCE_P4"))

    def get_force_to_bytes(self) -> bool:
        return common.str2bool(env.get("FORCE_TO_BYTES"))

    def get_workspace(self) -> str:
        return env.pipeline.workspace()

    def get_build_url(self) -> str:
        return env.pipeline.build_url()

    def get_language(self) -> str:
        lang = env.get("LANGUAGE")
        if not lang or lang == "None":
            lang = LanguageEnum.chin_simp.value
        return lang


class EnvMgr:
    def __init__(self):
        pass

    def get_branch_name(self) -> str:
        return env.get("BRANCH_NAME")

    def set_resource_branch(self, branch: str):
        env.set({"RESOURCE_BRANCH": branch})

    def get_resource_branch(self) -> str:
        return env.get("RESOURCE_BRANCH")

    def set_has_new_config(self, has_new_config: bool):
        env.set({"HAS_NEW_CONFIG": has_new_config})

    def get_has_new_config(self) -> bool:
        return env.get("HAS_NEW_CONFIG")

    def set_latest_change_list(self, change_list: str):
        env.set({"LATEST_CHANGE_LIST": change_list})

    def get_latest_change_list(self) -> str:
        return env.get("LATEST_CHANGE_LIST")

    def set_submit_files(self, submit_files: list):
        env.set({"SUBMIT_FILES": submit_files})

    def get_submit_files(self) -> list:
        return env.get("SUBMIT_FILES")

    def set_failed_files(self, failed_files: list):
        env.set({"FAILED_FILES": failed_files})

    def get_failed_files(self) -> list:
        return env.get("FAILED_FILES")

    def set_statistic_result(self, statistic_result: dict):
        env.set({"STATISTIC_RESULT": statistic_result})

    def get_statistic_result(self) -> dict:
        return env.get("STATISTIC_RESULT")

    def get_statistic_result_failed(self) -> str:
        return env.get("STATISTIC_RESULT")["fail_count"]

    def set_xml_txt_url(self, url: str):
        env.set({"XML_TXT_URL": url})

    def get_xml_txt_url(self) -> str:
        return env.get("XML_TXT_URL")

    def set_xml_log_url(self, url: str):
        env.set({"XML_LOG_URL": url})

    def get_xml_log_url(self) -> str:
        return env.get("XML_LOG_URL")

    def set_unity_return_code(self, code: int):
        env.set({"UNITY_RETURN_CODE": code})

    def get_unity_return_code(self) -> int:
        return env.get("UNITY_RETURN_CODE")

    def get_failed_files_line(self) -> tuple:
        msg = env.get("PYFRAME_EXCEPTION_MESSAGE")
        match = re.search(r"([^\n]+)\((\d+),\d+\)", msg)
        if match:
            file = match.group(1)
            line_number = match.group(2)
            return (
                file,
                line_number,
            )
        return None


jenkins_env_mgr = JenkinsEnvMgr()
env_mgr = EnvMgr()
