# coding=utf-8
import re

from frame import *
from project.x5m import config
from project.x5m.xml_to_byte.mgr.env_mgr import env_mgr, jenkins_env_mgr


class X5mGitMgr:
    def __init__(self):
        self.__workdir = jenkins_env_mgr.get_workspace()
        self.__branch = env_mgr.get_branch_name()

        self.x5mobile_git_mgr = GitMgr(workdir=self.__workdir, project_name=".")
        self.x5mconfig_git_mgr = GitMgr(workdir=self.__workdir, project_name="x5mconfig")
        self.x5mweek_release_git_mgr = GitMgr(workdir=self.__workdir, project_name="x5mweek")

        self.x5mobile_gitlab_mgr = GitlabMgr(url=config.GITLAB_MAINTAINER["url"], token=config.GITLAB_MAINTAINER["token"], project="dgm/x5mobile")

    def check_branch(self):
        """
        检查分支是否是最新、次新、主支、自定义分支
        """
        branch_list = self.__get_latest_and_second_branch()
        branch_list.append("master")
        branch_list.append("5.12.0_s")
        log.info(f"target branches: {branch_list}")
        if self.__branch not in branch_list:
            raise PyframeException(f"分支{self.__branch}不在{branch_list}中, 请检查该分支是否需要转换")

    # def __compare_version(self, v1: str, v2: str) -> int:
    #     """
    #     比较版本号大小
    #    Args:
    #         v1: 待比较的字符串版本号1
    #         v2: 待比较的字符串版本号2
    #     Returns:
    #         int: v1是否大于v2
    #     """
    #     for _v1, _v2 in zip(v1.split("."), v2.split(".")):
    #         _v1, _v2 = int(_v1), int(_v2)
    #         if v1 > v2:
    #             return 1
    #         elif v1 < v2:
    #             return -1
    #     return 0

    def __get_latest_and_second_branch(self) -> list:
        """
        获取最大，次大分支
        Returns:
            list: 包含最大，次大分支的列表
        """
        # branches = self.x5mobile_gitlab_mgr.get_all_branches()
        # branch_num = []
        # for branch in branches:
        #     num = re.findall(r"^\d.\d+.\d$", branch)
        #     if num:
        #         branch_num.append(num[0])
        # for branch_one in range(len(branch_num)):
        #     for branch_two in range(0, len(branch_num)-1):
        #         if self.__compare_version(branch_num[branch_one], branch_num[branch_two]) == -1:
        #             branch_num[branch_one], branch_num[branch_two] = branch_num[branch_two], branch_num[branch_one]
        # return branch_num[-2:]

        branches = self.x5mobile_gitlab_mgr.get_all_branches()
        temp = []
        for branch in branches:
            if re.match(r"^\d.\d+.\d$", branch):
                temp.append(re.match(r"^\d.\d+.\d$", branch).group())
        temp.sort()
        # max_branch, second = a[-1], a[-2]
        branch_num = temp[-2:]
        return branch_num[-2:]

    def clone_x5mconfig(self):
        """
        更新x5mconfig
        """
        if not Path(os.path.join(self.__workdir, "x5mconfig")).exists():
            self.x5mconfig_git_mgr.clone(url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mconfig.git", branch=self.__branch)
            env_mgr.set_has_new_config(has_new_config=True)
        else:
            old_commit_id = self.x5mconfig_git_mgr.get_local_latest_commit_id()
            log.info(f"old_commit_id: {old_commit_id}")
            self.x5mconfig_git_mgr.reset(commit_id="HEAD")
            self.x5mconfig_git_mgr.clean()
            self.x5mconfig_git_mgr.advance_pull(branch=self.__branch)
            new_commit_id = self.x5mconfig_git_mgr.get_local_latest_commit_id()
            log.info(f"new_commit_id: {new_commit_id}")
            if old_commit_id != new_commit_id:
                env_mgr.set_has_new_config(has_new_config=True)

    def clone_x5mweek(self):
        """
        更新x5mweek
        """
        if not Path(os.path.join(self.__workdir, "x5mweek")).exists():
            self.x5mweek_release_git_mgr.clone(url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mweek.git", branch="release")
            env_mgr.set_has_new_config(has_new_config=True)
        else:
            old_commit_id = self.x5mweek_release_git_mgr.get_local_latest_commit_id()
            log.info(f"old_commit_id: {old_commit_id}")
            self.x5mweek_release_git_mgr.reset(commit_id="HEAD")
            self.x5mweek_release_git_mgr.clean()
            self.x5mweek_release_git_mgr.advance_pull(branch="release")
            new_commit_id = self.x5mweek_release_git_mgr.get_local_latest_commit_id()
            log.info(f"new_commit_id: {new_commit_id}")
            if old_commit_id != new_commit_id:
                env_mgr.set_has_new_config(has_new_config=True)

    def clone_x5mobile(self):
        """
        更新x5mobile
        """
        if not Path(os.path.join(self.__workdir, "x5mobile")).exists():
            self.x5mobile_git_mgr.clone(
                url="http://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile.git",
                branch=self.__branch,
            )
        else:
            if Path(os.path.join(self.__workdir, "x5mobile", ".gitattributes")).exists():
                self.x5mobile_git_mgr.exec("git rm .gitattributes")
            self.x5mobile_git_mgr.reset(commit_id="HEAD")
            self.x5mobile_git_mgr.exec("git pull")
            self.x5mobile_git_mgr.clean()
            self.x5mobile_git_mgr.advance_pull(branch=self.__branch)

    def reset_to_last_commit_id(self):
        """
        回退到上一次的commit id
        """
        self.x5mconfig_git_mgr.reset(commit_id="HEAD^^")
        self.x5mweek_release_git_mgr.reset(commit_id="HEAD^^")
