# coding=utf-8
from frame import (
    PyframeException,
    advance,
    log,
    path_mgr,
    pipeline_mgr,
    proc_mgr,
    wechat,
)
from project.x5m import LanguageEnum
from project.x5m.xml_to_byte.mgr.build_mgr import build_mgr
from project.x5m.xml_to_byte.mgr.env_mgr import env_mgr, jenkins_env_mgr
from project.x5m.xml_to_byte.mgr.p4_mgr import P4Mgr
from project.x5m.xml_to_byte.mgr.x5m_git_mgr import X5mGitMgr


@advance.stage(stage="准备")
def prepare(**kwargs):
    lang = jenkins_env_mgr.get_language()
    if lang not in LanguageEnum.__members__:
        raise PyframeException(f"未知语言类型: {lang}")

    log.info(f"======== 语言类型: {lang} ========")
    branch = env_mgr.get_branch_name()
    # x5m_git_mgr = X5mGitMgr(workdir=WORKDIR, branch=branch)
    # x5m_git_mgr.check_branch()
    proc_mgr.kill_proc(name="Everything.exe")
    if branch == "master":
        resource_branch = "Resources"
    else:
        resource_branch = f"b/{branch}"

    env_mgr.set_resource_branch(branch=resource_branch)
    env_mgr.set_has_new_config(has_new_config=False)


@advance.timeout(seconds=60 * 30, exception_msg="更新p4超时")
@advance.stage(stage="更新p4")
def sync_p4(**kwargs):
    p4_mgr = P4Mgr()
    p4_mgr.sync_all(force=jenkins_env_mgr.get_force_p4())
    env_mgr.set_latest_change_list(change_list=p4_mgr.get_latest_changelist())


# @advance.stage(stage="更新x5mobile")
# def clone_x5mobile(**kwargs):
#     # 更新x5mobile
#     log.info("clone_x5mobile")
#     branch = env.get("BRANCH_NAME")
#     x5m_git_mgr = X5mGitMgr(workdir=WORKDIR, branch=branch)
#     x5m_git_mgr.clone_x5mobile()


@advance.stage(stage="更新x5mconfig")
def clone_x5mconfig(**kwargs):
    x5m_git_mgr = X5mGitMgr()
    x5m_git_mgr.clone_x5mconfig()


@advance.stage(stage="更新x5mweek")
def clone_x5mweek(**kwargs):
    x5m_git_mgr = X5mGitMgr()
    x5m_git_mgr.clone_x5mweek()


@advance.stage(stage="检查是否需要转换")
def whether_continue(**kwargs):
    if not jenkins_env_mgr.get_force_to_bytes() and not env_mgr.get_has_new_config():
        log.info("没有新的配置文件，不需要转换")
        pipeline_mgr.stop_current_build()
        # pipeline_mgr.delete_current_build()


@advance.stage(stage="拷贝xml")
def copy_xml(**kwargs):
    build_mgr.copy_xml()


@advance.stage(stage="xml转byte")
def xml_to_byte(**kwargs):
    build_mgr.xml_to_byte()


@advance.stage(stage="统计结果")
def stat_result(**kwargs):
    build_mgr.rename_log()
    build_mgr.stat_result()

    # statistic_failed = env_mgr.get_statistic_result_failed()
    # if statistic_failed != 0:
    #     raise PyframeException(f"xml转bytes存在失败文件")


@advance.stage(stage="上传日志")
def upload_log(**kwargs):
    build_mgr.upload_log()


@advance.stage(stage="提交p4")
def submit_p4(**kwargs):
    # 删除两个P4提交目录下已有对应二进制文件的csv
    build_mgr.rm_csv()
    p4_mgr = P4Mgr()
    p4_mgr.submit()
    submit_files = env_mgr.get_submit_files()
    submit_files = [] if submit_files is None else submit_files

    # 此步骤在此不合适:如果没有提交文件, 但是仍有转换失败的文件就不会再次通知
    # if len(submit_files) == 0:
    # pipeline_mgr.stop_current_build()
    # pipeline_mgr.delete_current_build()

    statistic_failed = env_mgr.get_statistic_result_failed()
    if statistic_failed != 0:
        raise PyframeException(f"xml转bytes存在失败文件")


def __get_msg():
    msg = "**P4最新changelist**: {}\n".format(env_mgr.get_latest_change_list())

    # 获取转换统计信息
    if env_mgr.get_unity_return_code() == 0:
        statistic_results = env_mgr.get_statistic_result()
        failed_files = env_mgr.get_failed_files()
        msg += "**转换总数**: {}\n".format(statistic_results["all_count"])
        msg += "**成功总数**: {}\n".format(statistic_results["success_count"])
        msg += "**失败总数**: {}\n".format(statistic_results["fail_count"])
        if failed_files:
            msg += "**失败文件**:"
            for file in failed_files:
                msg += "{}\n".format(file)
        msg += "**转换是否成功**: {}\n".format(statistic_results["change"])

    # 上传unity日志
    xml_log_url = env_mgr.get_xml_log_url()
    if xml_log_url:
        xml_log_name = xml_log_url.split("/")[-1]
        msg += f"**unity日志:** [{xml_log_name}]({xml_log_url})\n"

    xml_txt_url = env_mgr.get_xml_txt_url()
    if xml_txt_url:
        xml_txt_name = xml_txt_url.split("/")[-1]
        msg += f"**转换结果:** [{xml_txt_name}]({xml_txt_url})\n"

    submit_files = env_mgr.get_submit_files()
    submit_files = [] if submit_files is None else submit_files
    if len(submit_files) > 0:
        msg += "**P4提交的配置**: \n{}".format("\n".join(submit_files))

    return msg


def on_always(**kwargs):
    if not env_mgr.get_xml_log_url() or not env_mgr.get_xml_txt_url():
        build_mgr.upload_log()


def on_success(**kwargs):
    submit_files = env_mgr.get_submit_files()
    submit_files = [] if submit_files is None else submit_files
    if len(submit_files) == 0:
        log.info("没有提交的文件，不需要通知")
        return
    wechat.send_unicast_post_success(user_list=[], content=__get_msg())
    wechat.send_multicast_post_success(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fac52e4b-933e-4f4e-b95c-5e89317d9a1f",
        content=__get_msg(),
    )
    # statistic_failed = env_mgr.get_statistic_result_failed()
    # if statistic_failed == 0:
    #     wechat.send_unicast_post_success(
    #         user_list=[],
    #         content=__get_msg()
    #     )
    #     wechat.send_multicast_post_success(
    #         webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fac52e4b-933e-4f4e-b95c-5e89317d9a1f",
    #         content=__get_msg()
    #     )
    # else:
    #     wechat.send_unicast_post_unstable(
    #         user_list=[],
    #         content=__get_msg()
    #     )
    #     wechat.send_multicast_post_unstable(
    #         webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fac52e4b-933e-4f4e-b95c-5e89317d9a1f",
    #         content=__get_msg()
    #     )
    #     # git仓库回退
    #     x5m_git_mgr = X5mGitMgr()
    #     x5m_git_mgr.reset_to_last_commit_id()
    advance.insert_pipeline_history_on_success()


def on_failure(**kwargs):
    # git仓库回退
    x5m_git_mgr = X5mGitMgr()
    x5m_git_mgr.reset_to_last_commit_id()
    hash = x5m_git_mgr.x5mobile_git_mgr.get_local_latest_commit_hash()
    failed_info = env_mgr.get_failed_files_line()
    metioned_list = []
    if failed_info:
        file, line = failed_info
        if path_mgr.exists(file):
            blame = x5m_git_mgr.x5mobile_git_mgr.blame(file, int(line))
            metioned_list = [blame.committer_mail]
    # committer = x5m_git_mgr.x5mobile_git_mgr.get_local_latest_committer_email()
    content = __get_msg()
    if hash:
        content += f"\n**git commit:** [Gitlab](https://x5mobile-gitlab.h3d.com.cn/dgm/x5mobile/-/commit/{hash}) \n"

    wechat.send_unicast_post_failure(user_list=[], content=content)
    wechat.send_multicast_post_failure(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fac52e4b-933e-4f4e-b95c-5e89317d9a1f",
        content=content,
        # mentioned_list=[committer],
        mentioned_list=metioned_list,
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()


def on_canceled(**kwargs):
    wechat.send_unicast_post_canceled(user_list=[], content=__get_msg())
    wechat.send_multicast_post_canceled(
        webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fac52e4b-933e-4f4e-b95c-5e89317d9a1f",
        content=__get_msg(),
    )
    advance.insert_pipeline_history_on_canceled()
