from frame import advance, wechat
from project39.pipeline.pymug_ci.mgr.ci_mgr import PymugCIMgr


@advance.stage(stage="代码检查")
def full_codecheck():
    mgr = PymugCIMgr()
    mgr.full_codecheck()


@advance.stage(stage="运行测试用例")
def run_test_cases():
    mgr = PymugCIMgr()
    mgr.run_test_cases()


# @advance.stage(stage="自动合并")
# def auto_merge():
#     mgr = PymugCIMgr()
#     mgr.auto_merge()


def on_success():
    mgr = PymugCIMgr()
    wechat.send_unicast_post_success(content=mgr.pipeline_msg)


def on_failure():
    mgr = PymugCIMgr()
    wechat.send_unicast_post_failure(content=mgr.pipeline_msg)


def on_canceled():
    wechat.send_unicast_post_canceled()
