node {
    wrap([$class: 'BuildUser']) {
        env.BUILD_USER = env.BUILD_USER
        env.BUILD_START_TIME = currentBuild.startTimeInMillis
        env.PIPELINE_NAME = currentBuild.fullDisplayName
        env.BUILD_USER_EMAIL = env.BUILD_USER_EMAIL
    }
}

pipeline {
    agent {
        node {
            label "*************"
            customWorkspace "D://jenkins//publish_ue_engine"
        }
    }
    parameters {
        booleanParam(name: 'P4_FORCE', defaultValue: false, description: '是否强更p4')
        string(name: 'ENGINE_GIT_TAG', defaultValue: "", description: '引擎代码仓库tag', trim: true)
        string(name: 'ENGINE_TARGET_BRANCH', defaultValue: "5.3master", description: '引擎代码仓库合并的目标分支', trim: true)
        extendedChoice(
            name: 'BUILD_TYPE',
            defaultValue: 'WithWin64,WithAndroid',
            description: '编译类型',
            multiSelectDelimiter: ',',
            quoteValue: false,
            saveJSONParameterToFile: false,
            type: 'PT_CHECKBOX',
            value:'WithWin64,WithAndroid',
            visibleItemCount: 5
        )
    }
    options {
        disableConcurrentBuilds()
    }

    environment {
       GIT_NAME = 'pyframe-pipeline'
       CLIENT_GIT_BRANCH = 'master'
    }

    stages {
        stage("安装poetry run python 依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(
                            script: """
                                poetry install
                            """
                        )
                    }
                }
            }
        }
        stage("更新引擎代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python   team3.py publish_ue_engine --job=update_engine")
                    }
                }
            }
        }
        stage("缓存引擎依赖") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=download_engine_cache")
                    }
                }
            }
        }
        stage("更新P4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=update_p4")
                    }
                }
            }
        }
        stage("编译引擎源码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=ue51_build")
                    }
                }
            }
        }
        stage("更新客户端工程") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=update_client")
                    }
                }
            }
        }
        stage("创建软链接") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=create_symbolic_link")
                    }
                }
            }
        }
        stage("编译C++") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=build_cpp")
                    }
                }
            }
        }
        stage("C++导出ts") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=export_ts")
                    }
                }
            }
        }
        stage("ts导出js") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=export_js")
                    }
                }
            }
        }
        stage("生成manifest") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=generate_manifest")
                    }
                }
            }
        }
        stage("提交p4") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=submit_p4")
                    }
                }
            }
        }
        stage("合并引擎代码") {
            steps {
                dir("${env.GIT_NAME}") {
                    script {
                        bat(script: "poetry run python  team3.py publish_ue_engine --job=merge_engine")
                    }
                }
            }
        }
        

    }
    post {
        always {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "poetry run python  team3.py publish_ue_engine --job=on_always")
                }
            }
        }
        unstable {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "poetry run python  team3.py publish_ue_engine --job=on_unstable")
                }
            }
        }
        success {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "poetry run python  team3.py publish_ue_engine --job=on_success")
                }
            }
        }
        failure {
            dir("${env.GIT_NAME}") {
                script {
                    bat(script: "poetry run python  team3.py publish_ue_engine --job=on_failure")
                }
            }
        }
    }
}