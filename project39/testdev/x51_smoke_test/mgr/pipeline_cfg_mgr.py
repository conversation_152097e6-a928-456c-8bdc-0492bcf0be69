# coding=utf-8

from frame import *


class X51MgcPipelineCfgMgr:
    def __init__(self):
        pass

    def get_pipeline_cfg_info_from_env(self):
        self.pipeline_cfg = {
            "host_server": env.get("SERVER_IP", ""),
            "PERFORCE_BRANCH": env.get("BRANCH", ""),
            "target_cases_platforms": env.get("target_cases_platforms", "no-win"),
            "target_testset_name": env.get("TARGET_TEST_SET", "10_project_test_set_smoke_test_x51"),
            "target_cases_name_keyword": env.get("target_cases_name_keyword", ""),
            "LOCAL_PATH": env.get("LOCAL_PATH", ""),
            "ios_client_id": env.get("ios_client_id", ""),
            "ios_anchor_id": env.get("ios_anchor_id", ""),
            "wda_package_name": env.get("wda_package_name", ""),
            "itunes_exe_path": env.get("itunes_exe_path", ""),
            "simulator_exe_path": env.get("simulator_exe_path", ""),
            "remote_apk_path": env.get("remote_apk_path", ""),
            "remote_ipa_path": env.get("remote_ipa_path", ""),
            "execute_debug_mode": env.get("execute_debug_mode", "true"),
            "ios_anchor_actual_account": env.get("ios_anchor_actual_account", ""),
        }
        log.info("-Get paras from pipeline paras")
        return self.pipeline_cfg

    def output_cfg_file(self, root_path: str):
        para_file_path = os.path.join(root_path, "pipeline_paras.txt")
        path_mgr.rm(para_file_path)

        try:
            output_lines = []
            with open(para_file_path, "w", encoding="gb2312") as f:
                for item_key, item_value in self.pipeline_cfg.items():
                    output_lines.append("{}:::{} \n".format(item_key, item_value))
                fileContent = "".join(output_lines)
                f.write(fileContent)
        except Exception as e:
            log.error(f"生成参数配置文件{para_file_path}失败, 异常: {e}")
            raise PyframeException(f"生成参数配置文件{para_file_path}失败, 异常: {e}")

        if not os.path.exists(para_file_path):
            log.error(f"Error: 参数配置文件 {para_file_path} 不存在，生成失败")
            raise PyframeException(f"参数配置文件{para_file_path}不存在，请检查参数")
        else:
            log.info(f"参数配置文件生成完成 {para_file_path}")
