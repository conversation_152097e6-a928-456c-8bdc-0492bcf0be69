# coding=utf-8
import json
import os
import time

import jenkins
import requests

from frame import PyframeException, advance, cmd, log, wechat
from project39.testdev.x51_smoke_test.mgr.config_mgr import ConfigMgr
from project39.testdev.x51_smoke_test.mgr.env_mgr import jenkins_env_mgr
from project39.testdev.x51_smoke_test.mgr.pipeline_cfg_mgr import (
    X51MgcPipelineCfgMgr,
)
from project39.testdev.x51_smoke_test.mgr.x51_mgc_git_mgr import X51MgcGitMgr
from project.x51.deploy.deploy_client import deploy_client_main
from project.x51.deploy.deploy_server import deploy_server_main


def server_prepare():
    deploy_server_main.prepare()


def server_sync_p4():
    deploy_server_main.sync_p4()


def server_modify_config():
    deploy_server_main.modify_config() 


def server_download_decompress_linux_bin():
    deploy_server_main.download_decompress_linux_bin()


def server_download_svn_script():
    deploy_server_main.download_svn_script()


def server_start():
    deploy_server_main.start_server()


def client_prepare():
    deploy_client_main.prepare()


def client_sync_p4():
    deploy_client_main.sync_p4()


def client_modify_config():
    deploy_client_main.modify_config()


@advance.stage(stage="准备配置文件")
def prepare_config():
    config_mgr = X51MgcPipelineCfgMgr()
    config_mgr.get_pipeline_cfg_info_from_env()
    config_mgr.output_cfg_file(root_path=jenkins_env_mgr.get_local_path())


@advance.stage(stage="下载测试代码")
def sync_airtest_git():
    x5m_git_mgr = X51MgcGitMgr(workdir=jenkins_env_mgr.get_local_path())
    x5m_git_mgr.pull_test_files_from_git()


@advance.stage(stage="检查服务器状态")
def check_server_status():
    branch = jenkins_env_mgr.get_branch()  # like branch_2023/QQX5_Mainland_6.5.6_07
    server_ip = jenkins_env_mgr.get_server_ip()
    # /data/workspace/x51/branch_2023/QQX5_Mainland_6.5.6_07/exe/bin]# ./wbox_console_admin ************* -i :: -wbox_status
    chmod_cmd_str = "chmod 777 wbox_console_admin"
    status_cmd_str = "./wbox_console_admin {} -i :: -wbox_status".format(server_ip)
    workspace_str = "/data/workspace/x51/{}/exe/bin".format(branch)

    retry_cnt = 0
    success = get_server_status([chmod_cmd_str, status_cmd_str], workspace_str)
    while retry_cnt < 5:
        if success == True:
            break
        if success == None:
            raise PyframeException("执行命令时未返回期望结果，请检查原因 .....")
        time.sleep(30)
        retry_cnt += 1
        success = get_server_status([chmod_cmd_str, status_cmd_str], workspace_str)
    if success == False:
        raise PyframeException("服务器未正常启动，请检查原因")


def get_server_status(cmd_list, workspace_str):
    retry_cnt = 0
    while retry_cnt < 20:
        code, output_obj = cmd.run_shell(cmds=cmd_list, workdir=workspace_str, return_output=True)
        if code != 0:
            retry_cnt += 1
            log.info(f"{retry_cnt} - Failed to get server status as passed. Try after 30 seconds...")
            time.sleep(30)
        else:
            break
    if len(output_obj) == 0:
        raise PyframeException("Failed to get server status as passed after 10 minutes...")
    success = None
    for line in output_obj:
        if line.find('{"start_status":"true"}') >= 0:
            log.info("Server was started successfullly.")
            success = True
        elif line.find('{"start_status":"false"}') >= 0:
            log.error("Server has services not started..")
            success = False
    return success


@advance.stage(stage="修改配置文件")
def modify_config():
    local_path = jenkins_env_mgr.get_local_path()
    branch = jenkins_env_mgr.get_branch()
    server_ip = jenkins_env_mgr.get_server_ip()

    config_mgr = ConfigMgr(root=local_path, branch=branch)
    config_mgr.modify_config(server_ip=server_ip)


@advance.stage(stage="修改测试相关配置")
def update_test_cfg_file():
    log.info("config.X51_LANDUN_WORKSPACE=" + jenkins_env_mgr.get_local_path())
    target_file = "AirtestProj/DeployScripts/x51_smoke/x51_win_smoke_update_paras_cfg_from_pipelines.py"
    target_file_path = os.path.join(jenkins_env_mgr.get_local_path(), target_file)
    if os.path.exists(target_file_path) == False:
        raise PyframeException("执行文件不存在：" + target_file_path)
    ret = cmd.run_shell(
        cmds=["python {}".format(target_file)],
        workdir=jenkins_env_mgr.get_local_path(),
        return_output=True,
    )
    if ret[0] != 0:
        log.error("return {}".format(ret[1]))
        raise PyframeException("修改流水线配置文件失败, 请检查日志, 返回码: {}".format(ret[0]))

    ret = cmd.run_shell(
        cmds=["python x51_win_smoke_deploy_mgc_automation_script.py --manual false"],
        workdir=os.path.join(
            jenkins_env_mgr.get_local_path(),
            "AirtestProj",
            "DeployScripts",
            "x51_smoke",
        ),
    )
    if ret[0] != 0:
        log.error("return {}".format(ret[1]))
        raise PyframeException("准备测试相关环境失败, 请检查日志, 返回码: {}".format(ret[0]))


@advance.stage(stage="启动冒烟测试用例")
def start_client():
    # ret = cmd.run_shell(cmds=["run_case.cmd"], workdir=os.path.join(jenkins_env_mgr.get_local_path(), "AirtestProj", "PyCaseRunner"))
    run_cmd_file = os.path.join(jenkins_env_mgr.get_local_path(), "AirtestProj", "PyCaseRunner", "run_case.cmd")
    if os.path.exists(run_cmd_file) == False:
        raise FileNotFoundError("run_case.cmd未找到，请检测步骤")
    # 等待2分钟，使得服务器能完全启动
    time.sleep(120)
    cmd_list = []
    with open(run_cmd_file, "r", encoding="gb2312") as f:
        for ann in f.readlines():
            cmd_list.append(ann)
    ret = cmd.run_shell(
        cmds=[cmd_list[-1]],
        workdir=os.path.join(jenkins_env_mgr.get_local_path(), "AirtestProj", "PyCaseRunner"),
    )
    if ret[0] != 0:
        log.error("return {}".format(ret[1]))
        raise PyframeException("运行测试用例过程中出现错误, 请检查日志, 返回码: {}".format(ret[0]))
    # 分析结果
    result_json_file = os.path.join(
        jenkins_env_mgr.get_local_path(),
        "AirtestProj",
        "PyCaseRunner",
        "case_result_summary.json",
    )
    if os.path.exists(result_json_file) == True:
        with open(result_json_file, "r") as f:
            result_dict: dict = json.load(f)
            has_false = False
            for item_name, value in result_dict.items():
                if value == False:
                    log.error("测试用例{} 结果失败".format(item_name))
                    has_false = True
            if has_false == True:
                raise PyframeException("测试用例执行结果失败, 请检查日志")


def __get_msg():
    music_t = jenkins_env_mgr.get_changelist()
    branch = jenkins_env_mgr.get_branch()
    p4_force_update = jenkins_env_mgr.get_force_update()
    local_path = jenkins_env_mgr.get_local_path()
    server_ip = jenkins_env_mgr.get_server_ip()
    client_ip = jenkins_env_mgr.get_client_ip()
    svn_version = jenkins_env_mgr.get_svn()
    p4_changelist = jenkins_env_mgr.get_changelist()

    msg = f"**分支**: {branch}\n" if branch else ""
    msg += f"**服务器**: {server_ip}\n" if server_ip else ""
    msg += f"**客户端**: {client_ip}\n" if client_ip else ""
    msg += f"**svn**: {svn_version}\n" if svn_version else ""
    msg += f"**changelist**: {p4_changelist}\n" if p4_changelist else ""
    msg += f"**p4根目录**: {local_path}\n" if local_path else ""
    msg += f"**是否强更**: {p4_force_update}\n" if p4_force_update else ""
    msg += f"**music_t版本**: {music_t}\n" if music_t else ""
    return msg


def __get_webhook():
    return "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5fe30b70-be00-452c-9e6c-3a6dc5ef5f90"


def smoke_test_callback(success: bool):
    record_test_results(success)
    # if success and jenkins_env_mgr.deploy_server_ip:
    if jenkins_env_mgr.deploy_server_ip:
        run_deploy_pipeline()


def run_deploy_pipeline():
    server_ip = jenkins_env_mgr.deploy_server_ip
    if not server_ip or server_ip == "None":
        return
    server = jenkins.Jenkins(
        "http://jenkins-x51.h3d.com.cn/",
        username="<EMAIL>",
        password="11c9d4f0b9e397c0547537348ebdb45781",
    )
    job_name = "center/test/x51_deploy_server_test"
    params = {
        "SERVER_IP": server_ip,
        # 格式: branch_2023/QQX5_Mainland_6.5.6_07
        "BRANCH": jenkins_env_mgr.get_branch(),
        "SVN_VERSION": jenkins_env_mgr.get_svn(),
        "CHANGELIST": jenkins_env_mgr.get_changelist(),
        "FORCE_UPDATE": jenkins_env_mgr.get_force_update(),
    }
    try:
        log.info(f"调用部署流水线 {params}")
        server.build_job(job_name, parameters=params)
    except Exception as e:
        raise PyframeException(f"回调部署流水线失败: {e}")


def record_test_results(success):
    host = "http://mug-qeapi.h3d.com.cn"
    if jenkins_env_mgr.test_env:
        host = "http://mug-qeapi-test.h3d.com.cn"

    p4_changelist = jenkins_env_mgr.get_changelist()
    if not p4_changelist:
        raise ValueError(f"p4_changelist: {p4_changelist}")

    svn_version = jenkins_env_mgr.get_svn()
    if not svn_version:
        raise ValueError(f"svn_version: {svn_version}")

    payload = {
        "result": {"pass": success},
        "svn": int(svn_version),
        "p4_changelist": int(p4_changelist),
    }
    print(payload)

    res = requests.post(
        f"{host}/x51wb_server/api/v1/compile/smoke/callback",
        headers={"content-type": "application/json"},
        json=payload,
    )
    print(res.text)


def on_success():
    msg = __get_msg()
    wechat.send_unicast_post_success(user_list=[], content=msg)
    # wechat.send_multicast_post_success(webhook=__get_webhook(), content=msg)
    advance.insert_pipeline_history_on_success()

    smoke_test_callback(True)


def on_failure():
    msg = __get_msg()
    wechat.send_unicast_post_failure(user_list=[], content=msg)
    wechat.send_multicast_post_failure(
        webhook=__get_webhook(),
        content=msg,
        mentioned_list=["<EMAIL>"],
        rescue=False,
    )
    advance.insert_pipeline_history_on_failure()

    smoke_test_callback(False)


def on_canceled():
    wechat.send_unicast_post_canceled(user_list=[], content=__get_msg())
    advance.insert_pipeline_history_on_canceled()
