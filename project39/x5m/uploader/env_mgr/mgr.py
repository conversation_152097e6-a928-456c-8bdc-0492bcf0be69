import os


class UploaderEnv:

    @staticmethod
    def get_language():
        return os.environ.get("LANGUAGE", "simp")
    
    @staticmethod
    def is_trad():
        return os.environ.get("LANGUAGE", "simp") == "trad"

    @staticmethod
    def get_cdn_workspace():
        return os.environ.get("CDN_WORKSPACE") or "D:\\White_box_tools"
    
    @staticmethod
    def get_src_path():
        return os.environ.get("SRC")
    
    @staticmethod
    def get_dst_path():
        return os.environ.get("DST")


uploader_env = UploaderEnv()
