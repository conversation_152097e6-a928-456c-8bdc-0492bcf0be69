import datetime
import re
from enum import Enum
from typing import Tuple

from frame import *
from project.x5m import config
from project39.x5m.weekly_updates_release.mgr.env_mgr import env_mgr


class MergeType(Enum):
    """
    合并类型
    """

    HOT = "hot"
    FINAL = "final"


class BuildMgr:
    def __init__(self, hot_package: str, debug: int, is_stop_service: bool = False, is_merge = False):
        self.hot_package = hot_package
        self.is_stop_service = is_stop_service
        self.is_merge = is_merge
        self.ftp_dir = "/server/x5m_server/test" if debug else "/server/x5m_server"
        self.ftp = FtpMgr(**config.TENCENT_FTP)
        self.workspace = r"D:\weekly_updates_release"
        self.final_path = os.path.join(self.workspace, "final_servers")
        self.hot_path = os.path.join(self.workspace, "hot_servers")
        self.merge_path = os.path.join(self.workspace, "merge_servers")
        self.merged_path = os.path.join(self.workspace, "merged_servers")

    def __is_validate(self) -> bool:
        """
        验证热更版本是否有效
        Returns:

        """
        pattern = re.compile(r"^\d{4}\.\d{6,7}$")
        ret = re.findall(pattern, self.hot_package)
        if ret:
            return True
        return False

    def __get_merge_info(self) -> Tuple[bool, str]:
        """
        检查热更版本是否需要合, 如果需要合并，返回合并类型
        Returns:

        """
        # 验证并解析热更版本
        if not self.__is_validate():
            raise PyframeException("热更版本无效，请检查热更版本参数")
        prefix, suffix = self.hot_package.split(".")
        final_version = self.__get_real_final(prefix)
        env_mgr.set_final_version(final_version)
        hot_version = final_version + "." + suffix[:4]
        env_mgr.set_hot_version(hot_version)

        # 判断是否需要合并，如果需要合并，返回合并类型
        if suffix[0:2] == "01":
            if suffix[2:4] == "01":
                return True, MergeType.FINAL.value
            else:
                return True, MergeType.HOT.value
        elif suffix[0:2] > "01":
            if suffix[2:4] == "01":
                return False, ""
            else:
                return True, MergeType.HOT.value
        else:
            return False, ""

    @staticmethod
    def __get_real_final(hot_prefix: str):
        """
        根据热更版本号的头部分，组织出Final版本号
        热更版本号 6052.0101125
        hot_prefix 6052
        Args:
            hot_prefix:

        Returns:

        """
        return f"{hot_prefix[0]}.{hot_prefix[1:3]}.{hot_prefix[-1]}"

    def __find_final_package(self) -> str:
        """
        查找Final服务器包

        Returns:

        """

        final_version = env_mgr.get_final_version()
        dirs = self.ftp.dirs(f"{self.ftp_dir}/{final_version}")
        pattern = re.compile(r"^dgm_server_\d+\.\d+\.\d+_\d{8}\.zip$")
        dgm_servers = []
        for d in dirs:
            if re.findall(pattern, d):
                dgm_servers.append(d)
                log.info(f"找到DGM服务器包: {d}")
        if len(dgm_servers) > 1:
            package_name = sorted(dgm_servers, key=lambda x: x.split("_")[-1].split(".")[0])[0]
        elif len(dgm_servers) == 1:
            package_name = dgm_servers[0]
        else:
            raise PyframeException("未找到DGM服务器包")
        ftp_package = f"{self.ftp_dir}/{final_version}/{package_name}"
        log.info(f"最终DGM服务器包: {ftp_package}")
        return ftp_package

    def __download_final_package(self):
        """
        获取Final服务器包
        Returns:

        """
        ftp_package = self.__find_final_package()
        if not path_mgr.exists(self.final_path):
            path_mgr.mkdir(self.final_path)
        local_package = os.path.join(self.final_path, ftp_package.split("/")[-1])
        env_mgr.set_local_final_server(local_package)
        self.ftp.download_file(ftp_package, local_package)

    def __find_hot_package(self, is_last: bool = False) -> str:
        """
        查找热更服务器包
        Returns:

        """
        if not is_last:
            hot_version = env_mgr.get_hot_version()
        else:
            hot_version = env_mgr.get_last_hot_version()
        pattern = re.compile(r"^dgm_server_\d+\.\d+\.\d+_\d{4}_\d{8}\.zip$|^config\d{8}\.zip$")
        dirs = self.ftp.dirs(f"{self.ftp_dir}/{hot_version}")
        weekly_package = []
        for d in dirs:
            if re.findall(pattern, d):
                weekly_package.append(d)
                log.info(f"找到热更服务器包: {d}")
        if len(weekly_package) > 1:
            package_name = sorted(weekly_package, key=lambda x: x, reverse=True)[0]
        elif len(weekly_package) == 1:
            package_name = weekly_package[0]
        else:
            raise PyframeException("未找到热更服务器包")
        ftp_package = f"{self.ftp_dir}/{hot_version}/{package_name}"
        log.info(f"最终热更服务器包: {ftp_package}")
        return ftp_package

    def __delete_hot_package(self):
        """
        删除热更服务器包
        Returns:

        """
        ftp_package = self.__find_hot_package()
        self.ftp.delete_file(ftp_package)

    def __download_hot_package(self, is_last: bool = False):
        """
        获取热更服务器包
        Returns:

        """
        ftp_package = self.__find_hot_package(is_last)
        if not path_mgr.exists(self.hot_path):
            path_mgr.mkdir(self.hot_path)
        local_package = os.path.join(self.hot_path, ftp_package.split("/")[-1])
        if is_last:
            env_mgr.set_local_last_hot_server(local_package)
        else:
            env_mgr.set_local_hot_server(local_package)
        self.ftp.download_file(ftp_package, local_package)

    # 递归处理目录
    def __recursive_dir(self, directory_path: str, target_directory: str, output_file):
        """
        递归处理目录
        Args:
            directory_path:
            target_directory:
            output_file:

        Returns:

        """
        for file_name in os.listdir(directory_path):
            file_path = os.path.join(directory_path, file_name)
            relative_path = os.path.relpath(file_path, start=target_directory)
            if file_path == output_file.name:
                continue  # 排除MD5文件本身
            if os.path.isfile(file_path):
                md5 = common.compute_md5(file_path)
                relative_path = relative_path.replace("\\", "/")
                output_file.write(f"{md5}  ./{relative_path}\n")
            elif os.path.isdir(file_path):
                self.__recursive_dir(file_path, target_directory, output_file)

    def __cal_dirs_md5_new(self, target: str, md5_file: str):
        with open(md5_file, "w", encoding="UTF-8") as output_file:
            self.__recursive_dir(target, target, output_file)

    @staticmethod
    def __cal_dirs_md5(target: str, md5_file: str):
        """
        计算某个目录下的所有文件的md5
        Args:
            target:
            md5_file:

        Returns:

        """
        md5_list = []
        for root, _, files in os.walk(target):
            for file in files:
                file_path = os.path.join(root, file)
                md5 = common.compute_md5(file_path)
                rel_path = os.path.relpath(file_path, target)
                rel_path = rel_path.replace("\\", "/")
                md5_list.append(f"{md5}  ./{rel_path}")
        with open(md5_file, "w", encoding="UTF-8") as f:
            f.write("\n".join(md5_list))
        log.info(f"target: {target}, md5_file: {md5_file}")

    # 计算单个文件的md5
    @staticmethod
    def __cal_file_md5(target: str, md5_file: str):
        """
        计算单个文件的md5
        Args:
            target:

        Returns:

        """
        md5 = common.compute_md5(target)
        with open(md5_file, "w", encoding="UTF-8") as f:
            rel_path = os.path.relpath(target, os.path.dirname(md5_file))
            rel_path = rel_path.replace("\\", "/")
            f.write(f"{md5}  ./{rel_path}")
        log.info(f"target: {target}, md5_file: {md5_file}")

    def __upload_merge_package(self):
        """
        上传合并包及md5
        Returns:

        """
        hot_version = env_mgr.get_hot_version()
        merged_ftp = f"{self.ftp_dir}/{hot_version}"
        merged_package = env_mgr.get_merged_package()
        self.ftp.upload_file(merged_package, merged_ftp)
        merged_md5 = env_mgr.get_merged_md5()
        self.ftp.upload_file(merged_md5, merged_ftp)

    def __merge_final(self):
        """
        合并Final包
        Returns:

        """
        # 下载Final包
        self.__download_final_package()

        # 解压Final包到合并目录
        local_package = env_mgr.get_local_final_server()
        if not local_package:
            raise PyframeException("未找到Final服务器包")
        tar.decompress(local_package, self.merge_path)

        # 合并公共函数
        self.__merge_common_func()

    def __download_last_hot_package(self):
        """
        下载上个版本的热更包
        Returns:

        """
        hot_version = env_mgr.get_hot_version()
        last_hot_version = re.sub(r"\d{2}(?=$)", lambda m: str(int(m.group(0)) - 1).zfill(2), hot_version)
        env_mgr.set_last_hot_version(last_hot_version)
        self.__download_hot_package(is_last=True)

    @staticmethod
    def __generate_new_name() -> str:
        """
        生成新的包名
        Returns:

        """
        version_info = env_mgr.get_hot_version()
        if not version_info:
            raise PyframeException("未找到热更版本号")
        major_version = ".".join(version_info.split(".")[:-1])
        sub_version = version_info.split(".")[-1]
        time_info = datetime.datetime.now().strftime("%Y%m%d")
        new_name = f"dgm_server_{major_version}_{sub_version}_{time_info}.zip"
        log.info(f"新的包名: {new_name}")
        return new_name

    def __change_name_and_upload(self):
        """
        在不合并的情况下，修改包名并上传
        Returns:

        """
        # 下载热更包
        self.__download_hot_package()

        # 删除热更包
        self.__delete_hot_package()

        local_package = env_mgr.get_local_hot_server()
        # 如果名字是dgm_server*.zip，就不用修改
        base_name = os.path.basename(local_package)
        if re.match(r"dgm_server_\d{1,2}\.\d{1,2}_\d{8}\.zip", base_name):
            return

        # 解压到合并目录
        tar.decompress(local_package, self.merge_path)

        # 处理md5文件
        md5_files = path_mgr.glob(self.merge_path, "*.md5")
        if md5_files:
            md5_file = md5_files[0]
            base_name = os.path.basename(md5_file)
            if base_name != "md5.md5":
                # 重命名为md5.md5
                new_md5_name = os.path.join(self.merge_path, "md5.md5")
                path_mgr.move(md5_file, new_md5_name)
        else:
            new_md5_name = os.path.join(self.merge_path, "md5.md5")
            self.__cal_dirs_md5_new(self.merge_path, new_md5_name)

        # 重命名
        new_name = self.__generate_new_name()
        new_package = os.path.join(self.merged_path, new_name)
        tar.compress(self.merge_path, new_package)
        env_mgr.set_merged_package(new_package)
        # 生成合并包md5
        self.__cal_file_md5(new_package, f"{new_package}.md5")
        env_mgr.set_merged_md5(f"{new_package}.md5")

        # 上传合并包及md5
        self.__upload_merge_package()

    def __merge_common_func(self):
        """
        合并公共函数
        Returns:

        """
        # 下载周更中的DGM服务器包
        self.__download_hot_package()

        # 下载成功后，删除远程目录的服务器包
        self.__delete_hot_package()

        # 解压周更中的DGM服务器包到合并目录并覆盖
        local_package = env_mgr.get_local_hot_server()
        tar.decompress(local_package, self.merge_path)

        # 获取md5文件名
        md5_files = path_mgr.glob(self.merge_path, "*.md5")
        if md5_files:
            md5_file = md5_files[0]
        else:
            md5_file = os.path.join(self.merge_path, "md5.md5")

        # 计算md5
        if path_mgr.exists(md5_file):
            path_mgr.rm(md5_file)

        if self.is_stop_service:
            md5_file = os.path.join(self.merge_path, "md5.md5")

        self.__cal_dirs_md5_new(self.merge_path, md5_file)

        # 获取合并包名
        local_package = env_mgr.get_local_hot_server().split("\\")[-1]
        # 如果需要停服更新，则需要重命名合并包及对应的md5
        if self.is_stop_service:
            local_package = self.__generate_new_name()
        merged_package = os.path.join(self.merged_path, local_package)

        # 压缩合并包
        tar.compress(self.merge_path, merged_package)
        env_mgr.set_merged_package(merged_package)

        # 生成合并包md5
        self.__cal_file_md5(merged_package, f"{merged_package}.md5")
        env_mgr.set_merged_md5(f"{merged_package}.md5")

        # 上传合并包及md5
        self.__upload_merge_package()

    def __merge_hot(self):
        """
        合并Hot包
        Returns:

        """
        # 下载上个版本的热更包
        self.__download_last_hot_package()

        # 解压上个版本的热更包到合并目录
        last_local_package = env_mgr.get_local_last_hot_server()
        if not last_local_package:
            raise PyframeException("未找到上个版本的热更包")
        tar.decompress(last_local_package, self.merge_path)

        self.__merge_common_func()

    # TODO 目前写成一个函数，后续流水线重构是可以直接拆分
    def merge_package(self):
        """
        合并Final包
        Returns:

        """
        # 清理合并目录
        for i in [self.merge_path, self.merged_path, self.hot_path, self.final_path]:
            if path_mgr.exists(i):
                path_mgr.rm(i)
        if not self.is_merge:
            log.info("不需要合并")
            if self.is_stop_service:
                log.info("停服更新, 需要重命名合并包")
                self.__change_name_and_upload()
            return
        # 判断是否需要合并以及合并类型
        need_merge, merge_type = self.__get_merge_info()
        log.info(f"need_merge: {need_merge}, merge_type: {merge_type}")
        if not need_merge:
            log.info("不需要合并")
            if self.is_stop_service:
                log.info("停服更新, 需要重命名合并包")
                self.__change_name_and_upload()
            return

        # 合并Final包
        if merge_type == MergeType.FINAL.value:
            self.__merge_final()

        # 合并Hot包
        elif merge_type == MergeType.HOT.value:
            self.__merge_hot()

        else:
            log.info("无需合并包")


# if __name__ == '__main__':
#     build_mgr = BuildMgr("6062.0401077", 1, True)
#     build_mgr.merge_package()
