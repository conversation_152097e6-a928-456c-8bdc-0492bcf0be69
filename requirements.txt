api4jenkins==1.9.1
APScheduler==3.9.1
atomicwrites==1.4.0
attrs==21.4.0
bcrypt==3.2.0
beautifulsoup4==4.10.0
boto3~=1.23.10
bs4==0.0.1
certifi==2021.10.8
cffi==1.15.0
chardet==4.0.0
charset-normalizer==2.0.10
chinesecalendar==1.8.0
click==8.0.4
colorama==0.4.4
cos-python-sdk-v5==1.9.23
cryptography==36.0.1
DBUtils==3.0.2
Deprecated==1.2.13
dnspython==2.2.1
dynaconf==3.1.5
docopt==0.6.2
fake-useragent==0.1.11
fastapi==0.75.2
func_timeout==4.3.5
GitPython==3.1.20
idna==3.3
importlib-metadata==4.8.3
iniconfig==1.1.1
jenkinsapi==0.3.11
lxml==4.7.1
mkdocs==1.3.0
multi-key-dict==2.0.3
ntlm-auth==1.5.0
p4==0.1.5
#p4python==2021.1.2265002
p4python==2020.1.2056111; python_version<'3.7'
p4python>=2021.1.2265002; python_version>='3.7'
packaging==21.3
pandas==1.1.5
paramiko==2.11.0
pathos==0.2.8
pbr==5.8.1
pluggy==1.0.0
pre-commit==2.17.0
psutil==5.9.1
py==1.11.0
pycparser==2.21
pylint==2.13.9
pymongo==4.1.1
PyMySQL==1.0.2
PyNaCl==1.5.0
pyparsing==3.0.7
pytest==7.0.1
python-dateutil==2.8.2
python-jenkins==1.7.0
python-gitlab==2.10.1
pytz==2021.3
# pywin32==303; sys_platform=="win32"
requests==2.27.1
requests-ntlm==1.1.0
redis==3.2.0
six==1.16.0
soupsieve==2.3.1
tenacity==8.0.1
# tidevice==0.10.1
tomli==1.2.3
typing_extensions==4.1.1
urllib3==1.26.8
uvicorn==0.16.0
wrapt==1.14.0
xlrd==1.1.0
xmltodict==0.12.0
yarg==0.1.9
zipp==3.6.0
ruamel.yaml==0.17.21
pendulum==2.1.2
PyAutoGUI==0.9.53; sys_platform=="win32"
Pillow==8.4.0; sys_platform=="win32"
ntplib==0.4.0
pysmb==*******