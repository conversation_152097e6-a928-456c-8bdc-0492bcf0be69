#!/bin/bash
# 安装依赖
yum install openssl-devel bzip2-devel libffi-devel zlib-devel -y

# 编译安装
cd /data/
wget http://nexus.h3d.com.cn/repository/dev-productivity/python/3.9.12/Python-3.9.12.tgz  --no-check-certificate
tar -xzvf Python-3.9.12.tgz
cd Python-3.9.12
./configure --prefix=/usr/local/python3 --with-ssl
make && make install

# 建立pip软连接
ln -sf /usr/local/python3/bin/python3.9 /usr/bin/python3
ln -sf /usr/local/python3/bin/pip3 /usr/bin/pip

# 设置pip源
mkdir -p ~/.pip
cat > ~/.pip/pip.conf <<EOF
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
[install]
trusted-host = https://pypi.tuna.tsinghua.edu.cn
EOF

alias rm=rm
cd /data/
rm -r Python-3.9.12*

python3 -m pip install --upgrade pip
