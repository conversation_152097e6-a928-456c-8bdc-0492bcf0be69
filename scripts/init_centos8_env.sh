#!/bin/bash

# 换源
cd /etc/yum.repos.d/
for i in *; do
  mv "${i}" "${i}".bak
done

wget http://nexus.h3d.com.cn/repository/dev-productivity/yum.repos.d/centos8/centos8.tar.gz
tar -zxvf centos8.tar.gz
alias rm=rm
rm centos8.tar.gz

# 安装必备软件
#epel-release
yum install  lrzsz git python3 svn yum-utils nfs-utils cifs-utils zstd -y
yum install htop -y

# 安装并配置docker
yum remove podman -y
yum install yum-utils device-mapper-persistent-data lvm2 -y
yum-config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo
yum install docker-ce --allowerasing -y

mkdir -p /data/docker
cat > /etc/docker/daemon.json << EOF
{
    "bip": "***********/16",
    "dns": [
        "************"
    ],
    "data-root": "/data/docker",
    "insecure-registries": [
        "**************:5000",
        "registry.tac.com",
        "registry2.tac.com"
    ],
    "registry-mirrors": [
        "https://docker-mirror.h3d.com.cn"
    ],
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "1g"
    }
}
EOF
systemctl status docker
systemctl enable docker
systemctl start docker
systemctl status docker


# 安装p4
wget -q https://package.perforce.com/perforce.pubkey
sudo rpm --import ./perforce.pubkey
rm perforce.pubkey

cat > /etc/yum.repos.d/perforce.repo << EOF
[perforce]
name=Perforce
baseurl=http://package.perforce.com/yum/rhel/8/x86_64
enabled=1
gpgcheck=1
EOF

yum install helix-p4d -y

# 安装mysql57
wget http://nexus.h3d.com.cn/repository/dev-productivity/software/mysql/mysql80-community-release-el7-6.noarch.rpm
rpm -ivh mysql80-community-release-el7-6.noarch.rpm
rm mysql80-community-release-el7-6.noarch.rpm
yum module disable mysql -y
yum-config-manager --disable mysql80-community
yum-config-manager --enable mysql57-community

wget http://nexus.h3d.com.cn/repository/dev-productivity/yum.repos.d/centos8/mysql57.tar.gz
tar -zxvf mysql57.tar.gz
rm mysql57.tar.gz

yum install mysql-community-server -y