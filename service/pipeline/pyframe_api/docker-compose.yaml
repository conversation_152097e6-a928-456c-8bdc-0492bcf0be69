# cd pyframe-pipeline

# 编译并启动
# docker-compose -f service/pipeline/pyframe_api/docker-compose.yaml up -d --build

# 升级步骤
# 1. 构建pyframe_service和pyframe_service_bak
# docker-compose -f service/pipeline/pyframe_api/docker-compose.yaml build pyframe_service
# docker-compose -f service/pipeline/pyframe_api/docker-compose.yaml build pyframe_service_bak
#
# 2. 停止并删除pyframe_service
# docker-compose -f service/pipeline/pyframe_api/docker-compose.yaml rm -s -v -f pyframe_service
#
# 3. 更新pyframe_service
# docker-compose -f service/pipeline/pyframe_api/docker-compose.yaml up -d pyframe_service
#
# 4. 停止并删除pyframe_service_bak
# docker-compose -f service/pipeline/pyframe_api/docker-compose.yaml rm -s -v -f pyframe_service_bak
#
# 5. 更新pyframe_service_bak
# docker-compose -f service/pipeline/pyframe_api/docker-compose.yaml up -d pyframe_service_bak


version: "3"
services:
  pyframe_service:
    container_name: pyframe_service
    build:
      context: ../../../
      dockerfile: service/pipeline/pyframe_api/Dockerfile
    expose:
      - 8000
    networks:
      - pyframe_network
    restart: always

  pyframe_service_bak:
    container_name: pyframe_service_bak
    build:
      context: ../../../
      dockerfile: service/pipeline/pyframe_api/Dockerfile
    expose:
      - 8000
    networks:
      - pyframe_network
    restart: always

  nginx:
    container_name: nginx
    image: registry.h3d.com.cn/library/nginx:1.16-alpine
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
    ports:
      - "80:80"
    networks:
      - pyframe_network
    depends_on:
      - pyframe_service
      - pyframe_service_bak

networks:
  pyframe_network:
    driver: bridge
