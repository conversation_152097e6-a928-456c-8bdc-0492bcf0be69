# coding=utf-8
import os
import re
import sys
import time
import xml.etree.ElementTree as ET
from typing import Union

import jenkins
import requests
import uvicorn
from fastapi import FastAPI, Body

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
from service.pipeline.pyframe_api.utils.response import *
from fastapi import Response
from frame.gitlab_mgr.gitlab_mgr import GitlabMgr
from frame.git_mgr.git_mgr import GitMgr
from project.x5m import config
from project.m2 import config as m2_config
from frame import Nexus
from frame import common
from frame import MysqlClient

app = FastAPI()


@app.get("/beijing_time", status_code=status.HTTP_200_OK)
async def beijing_time():
    time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    return SuccessJsonResponse({"time": time_str})


@app.get("/jenkins_node_status")
async def jenkins_node_status(jenkins_url: str, username: str, password: str, node_name: str):
    # 指定节点名称查询节点状态
    if not jenkins_url or not username or not password or not node_name:
        return ErrorJsonResponse(code=1, error_msg="invalid params")

    server = jenkins.Jenkins(jenkins_url, username=username, password=password)
    if not server.node_exists(node_name):
        return ErrorJsonResponse(code=2, error_msg="node not exist")
    node_info = server.get_node_info(node_name)
    node_status = False if node_info["offline"] else True
    return SuccessJsonResponse(data=[{"node_name": node_name, "node_status": node_status}])


@app.get("/jenkins_nodes_status_by_label")
async def jenkins_nodes_status_by_label(jenkins_url: str, username: str, password: str, label_name: str):
    # 查询指定标签下的节点状态
    if not jenkins_url or not username or not password or not label_name:
        return ErrorJsonResponse(code=1, error_msg="invalid params")

    try:
        server = jenkins.Jenkins(jenkins_url, username=username, password=password)
        nodes = server.get_nodes()
        node_names = []
        data = []
        for count in range(1, len(nodes)):
            node_name = nodes[count]["name"]
            node_config = server.get_node_config(node_name)
            tree = ET.XML(node_config)
            try:
                node_label = tree.find("label").text
            except:
                node_label = "None"
            if node_label == label_name:
                node_names.append(node_name)

        if len(node_names) > 0:
            for node_name in node_names:
                node_info = server.get_node_info(node_name)
                node_status = False if node_info["offline"] else True
                out = {"node_name": node_name, "node_status": node_status}
                data.append(out)
            return SuccessJsonResponse(data=data)
        else:
            return ErrorJsonResponse(
                code=-1,
                error_msg="node not exist under the label",
            )

    except requests.exceptions.ConnectionError:
        return ErrorJsonResponse(
            code=-2,
            error_msg="connection failed",
        )
    except Exception as e:
        return ErrorJsonResponse(
            code=-3,
            error_msg=f"{e}",
        )


@app.get("/x5m/x5mconfig_online_update")
async def get_online_update(path: Union[str, None] = None):
    """
    获取gitlab周更目录
    Args:
        path: 周更目录中的分支信息，如6.02.0 如果未给出，则查询默认最新的两个

    Returns:

    """
    mgr = GitlabMgr(url=config.GITLAB_MAINTAINER["url"], token=config.GITLAB_MAINTAINER["token"], project="dgm/x5mconfig")
    if not path:
        pattern = r"^(\d+.\d+.0)$"
        branches = mgr.get_all_branches()
        branches = [branch for branch in branches if re.findall(pattern, branch)]
        branches = branches[-2:]
    else:
        branches = [path]

    online_updates = []
    for branch in branches:
        online_updates.extend(mgr.get_repository_tree(path=f"onlineupdate/{branch}", ref="hotfix"))

    hotfixes = [{"text": online_update.get("name"), "value": online_update.get("name")} for online_update in online_updates]
    hotfixes.sort(key=lambda x: x.get("value"), reverse=True)
    hotfixes.insert(0, {"text": "版本内", "value": "版本内"})
    return JSONResponse(hotfixes)


@app.get("/m2/server/branch")
async def get_server_branch():
    """
    获取M2服务器端所有分支
    Returns:

    """
    try:
        mgr = GitMgr(os.getcwd(), project_name="")
        branches = mgr.get_romote_heads(url=m2_config.GITLAB_MAINTAINER["url"], username=m2_config.GITLAB_MAINTAINER["username"], password=m2_config.GITLAB_MAINTAINER["password"])
        res = ','.join(branches)
        return Response(res)
    except Exception:
        return Response("master")


@app.get("/m2/server/package")
async def get_server_package(branch: str = "master"):
    """
    获取M2服务器端zip包
    Returns:
    Args:
        branch: 分支信息，如果不加则默认查询主支下的服务器包
    """
    n = Nexus(**m2_config.NEXUS_CONFIG)
    ret = n.browse_repository(repository_name="m2-pipeline", node=f"m2/server/{branch}")
    success = ret.get("result").get("success")
    if common.str2bool(success):
        server_packages = ret.get("result").get("data")
    else:
        server_packages = []
    server_names = ",".join([server_package.get("text") for server_package in server_packages])
    return Response(server_names)


DB_CONFIG = {
    "host": "*************",
    "port": "6603",
    "username": "root",
    "password": "Horizon#d",
}
# DB_CONFIG = {
#     "host": "127.0.0.1",
#     "port": "3306",
#     "username": "root",
#     "password": "Horizon3d",
# }
mysql = MysqlClient(**DB_CONFIG)


@app.get("/x5m/hot_version/detail")
async def get_hot_detail(hot_version: str):
    """
    查询热更包的详细信息
    Returns:

    """
    # 判断表是否存在
    if not mysql.exists_table("api_service", "hot_detail"):
        return JSONResponse({"code": -3, "msg": "not found table", "data": {}})

    # 查询数据 不存在则返回空
    sql = f"select * from api_service.hot_detail where hot_version='{hot_version}'"
    ret = mysql.select_one(sql)
    if ret:
        changelist = ret.get("changelist")
        pipeline_id = ret.get("pipeline_id")
        return JSONResponse({"code": 0, "msg": "success", "data": {"changelist": changelist, "pipeline_id": pipeline_id}})
    return JSONResponse({"code": -2, "msg": "not found data", "data": {}})


@app.post("/x5m/hot_version/detail")
async def save_hot_detail(hot_version: str = Body(...), changelist: str = Body(...), pipeline_id: str = Body(...)):
    """
    保存热更包信息
    Returns:

    """
    # 确认表是否存在
    if not mysql.exists_table("api_service", "hot_detail"):
        return JSONResponse({"code": -3, "msg": "not found table", "data": {}})

    # 确认数据是否存在，存在则更新，不存在则插入
    sql = f"select * from api_service.hot_detail where hot_version='{hot_version}'"
    ret = mysql.select_one(sql)
    if ret:
        sql = f"update api_service.hot_detail set changelist='{changelist}',pipeline_id='{pipeline_id}' where hot_version='{hot_version}'"
        mysql.update_one(sql)
    else:
        sql = f"insert into api_service.hot_detail (hot_version, changelist, pipeline_id) values ('{hot_version}', '{changelist}', '{pipeline_id}')"
        mysql.insert_one(sql)
    return JSONResponse({"code": 0, "msg": "success", "data": {}})


# 手游iOS客户端打包机xcode版本
@app.get("/x5m/xcode_version")
async def get_xcode_version():
    """
    获取xcode版本
    Returns:

    """
    resp = {"code": 0, "msg": "success", "data": {"Xcode": "15.3", "Build version": "15E204a"}}
    return JSONResponse(resp)


# 手游客户端打包机unity版本
@app.get("/x5m/unity_version")
async def get_unity_version():
    """
    获取unity版本
    Returns:

    """
    resp = {
        "code": 0,
        "msg": "success",
        "data": {
            "Windows": "2022.3.10.14226",
            "Darwin": "2022.3.10f1",
        },
    }
    return JSONResponse(resp)


if __name__ == "__main__":
    uvicorn.run(
        app="main:app",
        host="0.0.0.0",
        port=8000,
    )
