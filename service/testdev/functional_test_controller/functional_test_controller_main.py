# coding=utf-8
import os
import sys
import time

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
print(sys.path)

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from apscheduler.schedulers.background import BackgroundScheduler
from service.testdev.functional_test_controller.mgr.task_mgr import task_mgr
from service.testdev.functional_test_controller.mgr.machine_mgr import machine_mgr
from service.testdev.functional_test_controller.mgr.bk_mgr import bk_mgr
from service.testdev.functional_test_controller.mgr.task_history_mgr import task_history_mgr
from frame import log

scheduler = BackgroundScheduler()

CORS_ALLOW_ORIGINS = ["*"]

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ALLOW_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
def test_get():
    return {"Hello": "World"}


# /////////////////////////////////////////////////////////////
# task_mgr
# /////////////////////////////////////////////////////////////
class Task(BaseModel):
    uid: str
    project: str
    task: str


@app.post("/enqueue_task/")
def enqueue_task(t: Task):
    ret = task_mgr.enqueue_task(uid=t.uid, project=t.project, task=t.task)
    log.info(ret)
    return ret


@app.get("/dequeue_task/")
def dequeue_task():
    ret = task_mgr.dequeue_task()
    log.info(ret)
    return ret


# /////////////////////////////////////////////////////////////
# machine_mgr
# /////////////////////////////////////////////////////////////
@app.get("/get_available_machine/")
def get_available_machine():
    ret = machine_mgr.get_available_machines()
    log.info(ret)
    return ret


@app.get("/get_unavailable_machine/")
def get_unavailable_machine():
    ret = machine_mgr.get_unavailable_machine()
    log.info(ret)
    return ret


@app.get("/get_all_machine/")
def get_all_machine():
    ret = machine_mgr.get_all_machines()
    log.info(ret)
    return ret


class OccupyMachine(BaseModel):
    server_ip: str
    client_ip: str


@app.post("/occupy_machine/")
def occupy_machine(m: OccupyMachine):
    ret = machine_mgr.occupy_machine(server_ip=m.server_ip, client_ip=m.client_ip)
    log.info(ret)
    return ret


class ReturnMachine(BaseModel):
    server_ip: str
    client_ip: str


@app.post("/return_machine/")
def return_machine(m: ReturnMachine):
    ret = machine_mgr.return_machine(server_ip=m.server_ip, client_ip=m.client_ip)
    log.info(ret)
    return ret


class AddMachine(BaseModel):
    server_ip: str
    client_ip: str
    server_type: int
    client_type: int


@app.post("/add_machine/")
def add_machine(m: AddMachine):
    ret = machine_mgr.add_machine(server_ip=m.server_ip, client_ip=m.client_ip, server_type=m.server_type, client_type=m.client_type)
    log.info(ret)
    return ret


class RemoveMachine(BaseModel):
    server_ip: str
    client_ip: str


@app.post("/remove_machine/")
def remove_machine(m: RemoveMachine):
    ret = machine_mgr.remove_machine(server_ip=m.server_ip, client_ip=m.client_ip)
    log.info(ret)
    return ret


def consume():
    log.info(time.time())
    time.sleep(10)
    # 从任务队列取任务
    ret = task_mgr.dequeue_task()
    # 如果没有任务，则啥也不干
    if len(ret) == 0:
        return
    uid = ret["uuid"]
    project = ret["project"]
    params = ret["params"]
    # 如果有任务，则获取可用机器
    while True:
        machine = machine_mgr.get_available_machines()
        if len(machine) != 0:
            server_ip, client_ip = machine[0]["server_ip"], machine[0]["client_ip"]
            # 如果有可用机器，则调用流水线
            machine_mgr.occupy_machine(server_ip=server_ip, client_ip=client_ip)
            if True:
                bk_mgr.call_bk_pipeline(
                    url="http://bk-devops.h3d.com.cn/ms/process/api/external/pipelines/aa1dedd761d448c49814cea1d7952474/build",
                    params={"SERVER_IP": server_ip, "CLIENT_IP": client_ip, "UUID": uid},
                )
                task_history_mgr.insert_record_on_create(uid=uid, project=project, server_ip=server_ip, client_ip=client_ip, params=params)
            # else:
            #     bk_mgr.call_bk_pipeline(
            #         url='http://bk-devops.h3d.com.cn/ms/process/api/external/pipelines/aa1dedd761d448c49814cea1d7952474/build',
            #         params={"SERVER_IP": server, "CLIENT_IP": client}
            #     )
            break
        else:
            # 如果没有可用任务，则等待
            # scheduler.pause()
            time.sleep(5)


# python .\project\testdev\functional_test_controller\functional_test_controller_main.py
if __name__ == "__main__":
    scheduler.add_job(consume, "interval", seconds=30)
    scheduler.start()
    scheduler.print_jobs()
    # scheduler.shutdown(wait=False)
    uvicorn.run("functional_test_controller_main:app", host="127.0.0.1", port=5000, log_level="info")
