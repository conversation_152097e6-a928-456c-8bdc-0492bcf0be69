# coding=utf-8
import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
import requests
import uuid
import json
from frame import log

url = "http://127.0.0.1:5000/"


def test_get():
    resp = requests.get(url=url)
    log.info(resp.text)


# /////////////////////////////////////////////////////////////
# task_mgr
# /////////////////////////////////////////////////////////////
def test_enqueue_task():
    data = {"uid": str(uuid.uuid1()), "project": "x51", "task": "go home"}
    headers = {"Content-Type": "application/json"}
    resp = requests.post(url="{}enqueue_task/".format(url), data=json.dumps(data), headers=headers).json()
    log.info(resp)


def test_dequeue_task():
    resp = requests.get(url="{}dequeue_task/".format(url))
    log.info(resp.text)


# /////////////////////////////////////////////////////////////
# machine_mgr
# /////////////////////////////////////////////////////////////
def test_get_available_machine():
    resp = requests.get(url="{}get_available_machine/".format(url))
    log.info(resp.text)


def test_get_unavailable_machine():
    resp = requests.get(url="{}get_unavailable_machine/".format(url))
    log.info(resp.text)


def test_get_all_machine():
    resp = requests.get(url="{}get_all_machine/".format(url))
    log.info(resp.text)


def test_occupy_machine():
    data = {"server_ip": "127.0.0.1", "client_ip": "*********"}
    headers = {"Content-Type": "application/json"}
    resp = requests.post(url="{}occupy_machine/".format(url), data=json.dumps(data), headers=headers).json()
    log.info(resp)


def test_return_machine():
    data = {"server_ip": "127.0.0.1", "client_ip": "*********"}
    headers = {"Content-Type": "application/json"}
    resp = requests.post(url="{}return_machine/".format(url), data=json.dumps(data), headers=headers).json()
    log.info(resp)


def test_add_machine():
    data = {"server_ip": "127.0.0.1", "client_ip": "*********", "server_type": 0, "client_type": 1}
    headers = {"Content-Type": "application/json"}
    resp = requests.post(url="{}add_machine/".format(url), data=json.dumps(data), headers=headers).json()
    log.info(resp)


def test_remove_machine():
    data = {"server_ip": "127.0.0.1", "client_ip": "*********"}
    headers = {"Content-Type": "application/json"}
    resp = requests.post(url="{}remove_machine/".format(url), data=json.dumps(data), headers=headers).json()
    log.info(resp)


# python .\project\testdev\functional_test_controller\functional_test_controller_test.py
if __name__ == "__main__":
    test_get()
    test_enqueue_task()
    # test_dequeue_task()
    # test_get_available_machine()
    # test_get_unavailable_machine()
    # test_get_all_machine()
    test_return_machine()
    # test_occupy_machine()
    # test_add_machine()
    # test_remove_machine()
