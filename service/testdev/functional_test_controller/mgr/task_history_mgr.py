# coding=utf-8
from frame import MysqlClient, log


class TaskHistoryMgr:
    def __init__(self):
        self.__mysql = MysqlClient(host="************", port="3306", username="root", password="123456")

    def insert_record_on_create(self, uid: str, server_ip: str, client_ip: str, project: str, params: str):
        """
        测试创建时插入记录

        Args:
            uid: 请求唯一标识
            server_ip: 服务器ip
            client_ip: 客户端ip
            project: 项目类型
            params: 请求参数
        """
        sql = (
            f"insert into `testdev`.`task_history` (uuid, server_ip, client_ip, project, status, params) values "
            f"('{uid}', '{server_ip}', '{client_ip}', '{project}', 0, '{params}');"
        )
        ret = self.__mysql.insert_one(sql=sql)
        log.info(ret)

    def update_record_on_start(self, uid: str, url: str):
        """
        测试开始时插入记录
        Args:
            uid: 请求唯一标识
            url: 日志链接
        """
        sql = f"update `testdev`.`task_history` set status=1, url='{url}' where uuid='{uid}';"
        ret = self.__mysql.update_one(sql=sql)
        log.info(ret)

    def update_record_on_finish(self, uid: str, status: int):
        """
        测试结束时更新记录
        Args:
            uid: 请求唯一标识
            status: 状态
        """
        sql = f"update `testdev`.`task_history` set status='{status}' where uuid='{uid}';"
        ret = self.__mysql.update_one(sql=sql)
        log.info(ret)

    def get_all_history(self):
        """
        获取所有的历史记录（最近三天）
        """
        pass

    def get_history(self, uid: str):
        """
        根据uuid获取特定的历史记录
        Args:
            uid: uuid

        """
        pass


task_history_mgr = TaskHistoryMgr()
