from operator import methodcaller

from frame import *


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--git_branch", default="master", help="ue5代码仓库分支")
def build_ue51_engine_ci(job: str, git_branch: str):
    """
    team3 ue5.1 ci流水线
    """
    kw = {
        "job": job,
        "git_branch": git_branch,
    }
    log.info("params: " + str(kw))
    from project.team3.build_ue51_engine import build_ue51_engine_ci_main

    methodcaller(job, **kw)(build_ue51_engine_ci_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def build_ue51_engine_publish(job: str):
    """
    team3 ue5.1 ci流水线
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.team3.build_ue51_engine import build_ue51_engine_publish_main

    methodcaller(job, **kw)(build_ue51_engine_publish_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def build_ue51_engine_prepublish(job: str):
    """
    team3 ue5.1 ci流水线
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.team3.build_ue51_engine import build_ue51_engine_prepublish_main

    methodcaller(job, **kw)(build_ue51_engine_prepublish_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def build_ue51_engine_render_benchmark(job: str):
    """
    team3 ue5.1 render_benchmark流水线
    """
    from project.team3.build_render_benchmark import build_render_benchmark_main

    methodcaller(job)(build_render_benchmark_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def ue5_engine_ci(job: str):
    """
    team3 ue5 ci流水线
    """
    from project.team3.ue5_engine_ci import ue5_engine_ci_main

    methodcaller(job)(ue5_engine_ci_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def publish_ue_engine(job: str):
    """
    team3 ue5 发版流水线
    """
    from project.team3.publish_ue_engine import publish_ue_engine_main

    methodcaller(job)(publish_ue_engine_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def pack_ue51_client_render_benchmark(job: str):
    """
    team3 ue5.1 render_benchmark客户端打包流水线
    """
    from project.team3.pack_render_benchmark import pack_client_render_benchmark_main

    methodcaller(job)(pack_client_render_benchmark_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def sync_ue_engine_source(job: str):
    """
    team3 ue5.1 同步github上的UE引擎源码到gitlab
    """
    from project.team3.sync_ue_engine_source import sync_ue_engine_source_main

    methodcaller(job)(sync_ue_engine_source_main)


if __name__ == "__main__":
    cli()
