from frame import common


def test_check_disk_usage():
    common.check_disk_usage(threshold=30)


def test_increase_x_x_x():
    version = "9.1.1"
    assert common.increase_x_x_x(version) == "9.1.2"


def test_join_url():
    assert common.join_url("https://www.aaa.com/", "/bbb/ccc/", "ddd", "/eee") == "https://www.aaa.com/bbb/ccc/ddd/eee"
    assert common.join_url("https://www.aaa.com", "bbb/ccc") == "https://www.aaa.com/bbb/ccc"
    assert common.join_url("https://www.aaa.com/", "ddd") == "https://www.aaa.com/ddd"
    assert common.join_url("https://www.aaa.com/", "/eee") == "https://www.aaa.com/eee"
    assert common.join_url("https://www.aaa.com/", "eee/", "fff") == "https://www.aaa.com/eee/fff"
