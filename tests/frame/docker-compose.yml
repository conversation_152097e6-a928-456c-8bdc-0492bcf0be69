version: '3'
services:
  cockpit:
    # build:
    #    context: ./
    #    dockerfile: Dockerfile
    # image: "docker_build_test:v0.0.1"
    # container_name: docker_build_test
    # network_mode: bridge
    # restart: always
    image: alpine:3.6
    stdin_open: true
    tty: true
    command: sh -c "while true; do echo hello world; usleep 10; done"
    logging:
      options:
        max-size: "2g"
        max-file: "3"
    container_name: docker_build_test
    network_mode: bridge
    restart: always
