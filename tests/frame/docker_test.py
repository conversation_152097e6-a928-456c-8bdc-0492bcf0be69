# coding=utf-8

from frame.docker.docker import Docker


def test_docker_build():
    docker = Docker(registry="registry.h3d.com.cn", username="dev-productivity", password="Productivity123")
    docker.build(
        workdir="tests/frame/docker_test",
        file="Dockerfile",
        repository="registry.h3d.com.cn/dev-productivity/docker-build-test",
        tag="v0.0.1",
        no_cache=False,
    )


def test_docker_pull():
    docker = Docker(registry="registry.h3d.com.cn", username="dev-productivity", password="Productivity123")
    docker.pull(image="registry.h3d.com.cn/dev-productivity/python:3.9.2")


def test_docker_push():
    docker = Docker(registry="registry.h3d.com.cn", username="dev-productivity", password="Productivity123")
    docker.push(image="registry.h3d.com.cn/dev-productivity/docker-build-test:v0.0.1")


# def test_exists_container():
#     """
#     容器存在时
#     """
#     docker = Docker(
#         registry="registry.h3d.com.cn",
#         username="dev-productivity",
#         password="Productivity123"
#     )
#     out = docker.exists_container(
#         container="docker_build_test"
#     )
#     assert out is True


def test_not_exists_container():
    """
    容器不存在时
    """
    docker = Docker(registry="registry.h3d.com.cn", username="dev-productivity", password="Productivity123")
    out = docker.exists_container(container="docker_z_test")
    assert out is False


# def test_exists_image():
#     """
#     镜像存在时
#     """
#     docker = Docker(
#         registry="registry.h3d.com.cn",
#         username="dev-productivity",
#         password="Productivity123"
#     )
#     out = docker.exists_image(
#         image="cockpit_collect",
#         tag="v"
#     )
#     assert out is True


def test_not_exists_image():
    """
    镜像不存在时
    """
    docker = Docker(registry="registry.h3d.com.cn", username="dev-productivity", password="Productivity123")
    out = docker.exists_image(image="java-test", tag="latest")
    assert out is False


# def test_rmi():
#     docker = Docker(
#         registry="registry.h3d.com.cn",
#         username="dev-productivity",
#         password="Productivity123"
#     )
#     docker.rmi(
#         image="python_test"
#     )


def test_get_dangling_image_ids():
    docker = Docker(registry="registry.h3d.com.cn", username="dev-productivity", password="Productivity123")
    docker.get_dangling_image_ids()


# def test_rmi_all_dangling():
#     docker = Docker(
#         registry="registry.h3d.com.cn",
#         username="dev-productivity",
#         password="Productivity123"
#     )
#     docker.rmi_all_dangling()
