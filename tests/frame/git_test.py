# coding=utf-8
# import os
# import git
# from frame import log, common
# from pathlib import Path


# class GitClient:
#     def __init__(self, url: str, branch: str = "master", to_path: str = "", username: str = "", password: str = ""):
#         self.__url = url
#         self.__branch = branch
#         if to_path == "":
#             to_path = os.path.abspath(url.split("/")[-1].rstrip(".git"))
#         self.__to_path = to_path
#         self.__username = username
#         self.__password = password
#         self.__repo = None
#         self.__heads = None
#         self.git = None
#         self.__current_branch = None
#
#     def clone(self):
#         """
#
#         Args:
#             url: git仓库链接
#             to_path: clone到本地的路径
#             branch: 分支
#
#         Returns:
#
#         """
#         if not os.path.exists(self.__to_path):
#             self.__repo = git.Repo.clone_from(url=self.__url, to_path=self.__to_path, branch=self.__branch)
#             log.info("mkdir -p {} & cd {} & git clone -b {} {}".format(self.__to_path, self.__to_path, self.__branch, self.__url))
#         else:
#             self.__repo = git.Repo(self.__to_path)
#             self.git = self.__repo.git
#             # log.info(self.git.pull())
#
#     def pull(self):
#         pass
#
#     def checkout(self):
#         pass
#
#     def diff(self):
#         log.info(self.git.diff())
#         pass
#
#     def branch(self):
#         rets = self.git.branch().split("\n")
#         branches = []
#         for ret in rets:
#             if ret.startwith("*"):
#                 self.__current_branch = ret.replace("*", "").strip()
#                 branches.append(self.__current_branch)
#             else:
#                 branches.append(ret.strip())
#         return branches
#
#
# def test_clone():
#     git_client = GitClient(url="https://gitlab.h3d.com.cn/dev-productivity/pyframe-pipeline.git",
#                            to_path="../../../pyframe-pipeline-test",
#                            branch="master")
#     git_client.clone()
#     git_client.diff()
#
#
# if __name__ == "__main__":
#     test_clone()
