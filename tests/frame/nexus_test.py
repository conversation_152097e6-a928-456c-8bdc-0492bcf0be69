# coding=utf-8
from frame import Nexus


def test_upload():
    username = "productivity-robot"
    password = "productivity-robot"
    nexus = Nexus(username=username, password=password)
    src = "../python-3.6.8.tgz"
    dst = "http://nexus.h3d.com.cn/repository/dev-productivity/python/3.6.8/python-3.6.8.tgz"
    src = "x51.py"
    dst = "http://nexus.h3d.com.cn/repository/dev-productivity/test/x51.py"
    nexus.upload(src=src, dst=dst)


def test_download():
    username = "productivity-robot"
    password = "productivity-robot"
    nexus = Nexus(username=username, password=password)
    src = "http://nexus.h3d.com.cn/repository/dev-productivity/python/3.6.8/python-3.6.8.tgz"
    dst = "temp/python-3.6.8.tgz"
    nexus.download(src=src, dst=dst)
