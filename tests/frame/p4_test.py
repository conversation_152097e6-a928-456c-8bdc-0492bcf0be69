import pytest

from frame import log
from frame.p4.p4 import Perforce


class TestP4Manager:
    def test_p4_login(self):
        auth = {
            "port": "p4.com:2003",
            "user": "niushuaibing",
            "password": "ampusa",
        }
        p4 = Perforce(**auth)
        p4.login()

    def test_workspace(self):
        auth = {
            "port": "p4.com:2003",
            "user": "niushuaibing",
            "password": "ampusa",
        }
        p4 = Perforce(**auth)
        p4.switch_client("192_168_7_140_test")

    def test_add_view(self):
        auth = {
            "port": "p4.com:2003",
            "user": "niushuaibing",
            "password": "ampusa",
        }
        p4 = Perforce(**auth)
        p4.add_view(client="192_168_7_140_test", view_list=["//H3D_Tech-Center/engine_is/a/... //192_168_7_140_test/H3D_Tech-Center/engine_is/a/..."])

    def test_update_file(self):
        auth = {
            "port": "p4.com:2003",
            "user": "niushuaibing",
            "password": "ampusa",
        }
        p4 = Perforce(**auth)
        p4.update(file="//H3D_Tech-Center/engine_is/a/...")

    def test_update_all(self):
        auth = {
            "port": "p4.com:2003",
            "user": "niushuaibing",
            "password": "ampusa",
        }
        p4 = Perforce(**auth)
        p4.update_all()

    # def test_add(self):
    #     p4.add(r'E:\p4workspace\niushuaibing_test\H3D_Tech-Center\engine_is\test\test2.txt')

    def test_edit(self):
        auth = {
            "port": "p4.com:2003",
            "user": "niushuaibing",
            "password": "ampusa",
        }
        p4 = Perforce(**auth)
        p4.edit(r"e:\p4workspace\x51_test\H3D_Tech-Center\engine_is\a\a.txt")

    # def test_reconcile(self):
    #     reconciled_file = p4.reconcile(file="//H3D_Tech-Center/engine_is/a/a1.txt")
    #     print(reconciled_file)

    def test_submit(self):
        auth = {
            "port": "p4.com:2003",
            "user": "niushuaibing",
            "password": "ampusa",
        }
        p4 = Perforce(**auth)
        p4.submit("add test2")

    def test_get_changes(self):
        auth = {
            "port": "p4.com:2003",
            "user": "niushuaibing",
            "password": "ampusa",
        }
        p4 = Perforce(**auth)
        changes = p4.get_changes(["-m1", "//H3D_Tech-Center/engine_is/a/a.txt"])
        log.info(changes)

    def test_get_latest_change(self):
        auth = {
            "port": "p4.com:2003",
            "user": "niushuaibing",
            "password": "ampusa",
        }
        p4 = Perforce(**auth)
        latest_changes = p4.get_latest_change(file="//H3D_Tech-Center/engine_is/a/a.txt")
        log.info(latest_changes)


if __name__ == "__main__":
    pytest.main()
