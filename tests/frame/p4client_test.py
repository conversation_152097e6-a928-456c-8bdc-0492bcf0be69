# coding=utf-8
import pytest

from frame.common.common import common
from frame.p4.p4client import P4Client


# from frame.log.log import log


# def test_get_options():
#     host = "p4.com:2003"
#     username = "liu<PERSON><PERSON><PERSON>"
#     password = "sq5r53"
#     client = "192_168_6_150_1"
#     p4 = P4Client(host=host, username=username, password=password, client=client)


def test_is_exist_client():
    host = "p4.com:2003"
    # TODO 个人账号密码
    username = "songmin"
    password = "Lang5208"
    client = "pyframe_test_{}".format(common.get_host_ip())
    p4 = P4Client(host=host, username=username, password=password, client=client)
    # p4 = P4Client(host=host, username=username, password=password, client=client, charset=P4Client.Charset.NONE)
    # log.info(p4)
    assert p4.is_exist_client("xxxxxxxxxxxxxxxxxxxxaaaaa") is False
    assert p4.is_exist_client(client) is True


def test_set_options():
    host = "p4.com:2003"
    # TODO 个人账号密码
    username = "songmin"
    password = "Lang5208"
    client = "pyframe_test_{}".format(common.get_host_ip())
    p4 = P4Client(host=host, username=username, password=password, client=client)
    p4.set_options(clobber=False)
    # log.info(p4)
    p4.set_options(clobber=True)
    # log.info(p4)


def test_set_view():
    host = "p4.com:2003"
    # TODO 个人账号密码
    username = "songmin"
    password = "Lang5208"
    client = "pyframe_test_{}".format(common.get_host_ip())
    p4 = P4Client(host=host, username=username, password=password, client=client)
    views = [
        "//H3D_X51_res/... //{}/H3D_X51_res/...".format(client),
        "//H3D_Tech-Center/... //{}/H3D_Tech-Center/...".format(client),
        "//H3D_X51_res/QQX5_Mainland/branch_2022/... //{}/QQX5_Mainland/branch_2022/...".format(client),
    ]
    p4.set_view(views=views)
    # log.info(p4)


def test_append_view():
    host = "p4.com:2003"
    # TODO 个人账号密码
    username = "songmin"
    password = "Lang5208"
    client = "pyframe_test_{}".format(common.get_host_ip())
    p4 = P4Client(host=host, username=username, password=password, client=client)
    views = ["//H3D_X51_res/QQX5_Mainland/branch_2021/... //{}/QQX5_Mainland/branch_2021/...".format(client)]
    p4.append_view(views=views)
    # log.info(p4)


def test_sync():
    host = "p4.com:2003"
    # TODO 个人账号密码
    username = "songmin"
    password = "Lang5208"
    client = "pyframe_test_{}".format(common.get_host_ip())
    p4 = P4Client(host=host, username=username, password=password, client=client)
    # log.info(p4)
    p4.sync("//H3D_X51_res/QQX5_Mainland/branch_2022/QQX5_Mainland_3yue_from_trunc/exe/tglog/...")
    p4.sync("//H3D_X51_res/QQX5_Mainland/branch_2022/QQX5_Mainland_3yue_from_trunc/exe/deploy_client.bat")


def test_sync_force():
    host = "p4.com:2003"
    # TODO 个人账号密码
    username = "songmin"
    password = "Lang5208"
    client = "pyframe_test_{}".format(common.get_host_ip())
    p4 = P4Client(host=host, username=username, password=password, client=client)
    # log.info(p4)
    # p4.sync_force("//H3D_Tech-Center/engine_is/a/...")
    p4.sync_force("//H3D_X51_res/QQX5_Mainland/branch_2022/QQX5_Mainland_3yue_from_trunc/exe/ip_setting.vbs")


# def test_edit():
#     host = "p4.com:2003"
#     username = "liutongqing"
#     password = "sq5r53"
#     client = "pyframe_test_{}".format(common.get_host_ip())
#     p4 = P4Client(host=host, username=username, password=password, client=client)
#     p4.edit("//H3D_Tech-Center/engine_is/a/a.txt")
#     log.info(p4.submit("pyframe test"))


# def test_reconcile():
#     host = "p4.com:2003"
#     username = "liutongqing"
#     password = "sq5r53"
#     client = "pyframe_test_{}".format(common.get_host_ip())
#     p4 = P4Client(host=host, username=username, password=password, client=client)
#     log.info(p4.reconcile("//H3D_Tech-Center/engine_is/a/..."))
#     p4.submit("pyframe test reconcile")


# def test_fast_submit():
#     host = "p4.com:2003"
#     username = "liutongqing"
#     password = "sq5r53"
#     client = "pyframe_test_{}".format(common.get_host_ip())
#     p4 = P4Client(host=host, username=username, password=password, client=client)
#     p4.fast_submit(filelist=["//H3D_Tech-Center/engine_is/a/a.txt"])


def test_dirs():
    host = "p4.com:2003"
    # TODO 个人账号密码
    username = "songmin"
    password = "Lang5208"
    client = "pyframe_test_{}".format(common.get_host_ip())
    p4 = P4Client(host=host, username=username, password=password, client=client)
    try:
        p4.dirs(path="//H3D_Tech-Center/engine_is/*")
    except Exception as e:
        pytest.raises(e)


# if __name__ == "__main__":
#     test_fast_submit()
#     pass
