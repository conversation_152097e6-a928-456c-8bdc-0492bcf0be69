# coding=utf-8
# import os.path
# from pathlib import Path
# from frame.path_mgr.path_mgr import path_mgr


# def test_xcopy():
#     # src = r"E:\test\one"
#     src = r"E:\test\one"
#     dst = r"E:\test\test\two"
#     path_mgr.xcopy(src=src, dst=dst, dst_is_file=True)
#     assert Path(dst).exists() == True

# def test_copyfile():
#     src = r"README.md"
#     dst = r"tests/readme.md"
#     path_mgr.__copy_file(src=src, dst=dst)
#     assert Path(os.path.join("tests", "README.md")).exists() == True

# def test_move():
#     path_mgr.move(src="tests/README.md", dst="tests/frame1/")
#     assert Path(os.path.join("tests/frame", "README.md")).exists() == True


# def test_copydir():
#     src = r"zhangtao"
#     dst = r"tests/zahngtao"
#     path_mgr.__copy_folder(src=src, dst=dst)
#     assert Path(os.path.join("tests", "zahngtao")).exists() == True


def test_move():
    src = "zhangtao"
    dst = r"zhangtao2"
    path_mgr.move(src=src, dst=dst)
    assert Path(os.path.join("zhangtao", "test.txt")).exists() == True


# def test_cp():
#     src = "/data/test1"
#     dst = "/data/test2"
#     path_mgr.cp(src=src, dst=dst, recursive=True)
#     assert Path(dst).exists() == True
