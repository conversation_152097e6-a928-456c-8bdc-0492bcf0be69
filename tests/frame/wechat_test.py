from frame.wechat.wechat import wechat


def test_send_multicast_text_msg():
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1d143382-fafd-40fd-8599-262860acbb36"
    content = "text message\n\n"
    mentioned_list = ["<EMAIL>", "<EMAIL>"]
    (code, _) = wechat.send_multicast_msg(webhook=webhook, content=content, mentioned_list=mentioned_list, markdown=False)
    assert code == 200


def test_send_multicast_markdown_msg():
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1d143382-fafd-40fd-8599-262860acbb36"
    content = "# 标题\nmarkdown message\n\n"
    mentioned_list = ["<EMAIL>", "<EMAIL>"]
    (code, _) = wechat.send_multicast_msg(webhook=webhook, content=content, mentioned_list=mentioned_list, markdown=True)
    assert code == 200


def test_send_unicast_markdown_msg():
    content = "# python frame个人消息通知测试\nmarkdown message\n\n"
    (code, _) = wechat.send_unicast_msg(["<EMAIL>"], content, markdown=True)
    assert code == 200


def test_send_unicast_text_msg():
    content = "python frame个人消息通知测试, text message"
    (code, _) = wechat.send_unicast_msg(["<EMAIL>"], content, markdown=False)
    assert code == 200
