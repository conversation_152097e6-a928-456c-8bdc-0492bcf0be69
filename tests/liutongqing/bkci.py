# coding=utf-8
import requests

project = "dgm"
url = f"http://172.17.100.211:21935/api/apigw-user/v3/projects/{project}/environment/thirdPartAgent/nodeList"

# 发送post请求
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "X-DEVOPS-UID": "<EMAIL>",
    # "userId": "<EMAIL>",
}
resp = requests.get(url=url, headers=headers).json()
for node in resp["data"]:
    nodeHashId = node["nodeHashId"]
    # ms/openapi/api/apigw/v3/projects/{projectId}/environment/thirdPartAgent/nodes/status
    node_url = f"http://172.17.100.211:21935/api/apigw-user/v3/projects/dgm/environment/thirdPartAgent/nodes/status?nodeHashId={nodeHashId}"
    print(node_url)
    node_resp = requests.get(url=node_url, headers=headers).json()
    print(node_resp)
