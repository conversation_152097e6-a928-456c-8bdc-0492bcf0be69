# coding=utf-8
import os
import sys
import time

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from frame import *
from elasticsearch import Elasticsearch


# curl -H "Content-Type: application/json" -XPOST http://172.17.200.125:9200/assethub_x51/_search -d '{"query": {"term":  {"name": "liutongqing"}}}'
def search_x51():
    es = Elasticsearch(["http://172.17.200.125:9200"])
    query_json = {"query": {"term": {"name": "blabla"}}}

    while True:
        query = es.search(index="assethub_x51", body=query_json)
        log.info(query)
        if query["hits"]["total"]["value"] != 0:
            break
        else:
            time.sleep(1)
    # query_json = {
    #     "query": {
    #         "match_all": {
    #         }
    #     }
    # }
    # query = es.search(index='assethub_x51', body=query_json)
    # log.info(query)


if __name__ == "__main__":
    search_x51()
