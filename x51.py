# coding=utf-8
from operator import methodcaller

from frame import cli, click, log


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--days", default="", help="保留的天数")
@click.option("--branches", default="", help="保留的分支数")
def clean_server(job, days, branches):
    """
    清理服务器
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x51/p-8fae74ae94b34633beb04c455b2cca7d/preview
    示例：
        python x51.py clean_server --job=clean_core
    """
    kw = {"job": job, "days": days, "branches": branches}
    from project.x51.clean_server import clean_server_main

    methodcaller(job, **kw)(clean_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--branch", default="trunc", help="分支")
@click.option("--changelist", default="head", help="p4 changelist")
@click.option("--force_update", default=False, help="是否强更")
def art_deploy_server(job, branch, changelist, force_update):
    """
    美术-部署服务器
    流水线地址：
        http://bk-devops.h3d.com.cn/console/pipeline/x51/p-04caf2cc55144d44a83113beb8db2ec7/preview
    示例：
        python x51.py art_deploy_server --job=stop_server
    """
    kw = {
        "job": job,
        "branch": branch,
        "changelist": changelist,
        "force_update": force_update,
    }
    from project.x51.art.deploy_server import art_deploy_server_main

    methodcaller(job, **kw)(art_deploy_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def art_deploy_client(job):
    """
    美术-部署客户端
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x51/p-fd6de3c8bdd1468093c07126c13a630f/history
    示例：
        python x51.py art_deploy_client --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x51.art.deploy_client import art_deploy_client_main

    methodcaller(job, **kw)(art_deploy_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def art_update_client_environment(job):
    """
    美术-部署客户端
    流水线地址：

    示例：
        python x51.py art_update_client_environment --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x51.art.update_client_environment import (
        art_update_client_environment_main,
    )

    methodcaller(job, **kw)(art_update_client_environment_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--branch", default="trunc", help="分支")
@click.option("--changelist", default="head", help="p4 changelist")
@click.option("--force_update", default=False, help="是否强更")
def plan_deploy_server(job, branch, changelist, force_update):
    """
    策划-部署服务器
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x51/p-1346e7cd4bc542c6b8130d642a04ebad/history
    示例：
        python x51.py plan_deploy_server --job=stop_server
    """
    kw = {
        "job": job,
        "branch": branch,
        "changelist": changelist,
        "force_update": force_update,
    }
    from project.x51.plan.deploy_server import plan_deploy_server_main

    methodcaller(job, **kw)(plan_deploy_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def plan_modify_game_time(job):
    """
    策划-修改游戏时间
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x51/p-1346e7cd4bc542c6b8130d642a04ebad/history
    示例：
        python x51.py plan_modify_game_time --job=restart_server
    """
    kw = {
        "job": job,
    }
    from project.x51.plan.modify_game_time import plan_modify_game_time_main

    methodcaller(job, **kw)(plan_modify_game_time_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def plan_restart_server(job):
    """
    策划-重启服务器
    流水线地址：

    示例：
        python x51.py plan_restart_server --job=restart_server
    """
    kw = {
        "job": job,
    }
    from project.x51.plan.restart_server import plan_restart_server_main

    methodcaller(job, **kw)(plan_restart_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--branch", default="trunc", help="分支")
@click.option("--changelist", default="", help="p4 changelist")
@click.option("--force_update", default=False, help="是否强更")
@click.option("--svn_version", default="", help="svn版本")
@click.option("--start_type", default="", help="启动类型")
def test_deploy_server(job, branch, changelist, force_update, svn_version, start_type):
    """
    测试-部署服务器
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x51/p-1346e7cd4bc542c6b8130d642a04ebad/history
    示例：
        python x51.py test_deploy_server --job=stop_server
    """
    kw = {
        "job": job,
        "branch": branch,
        "changelist": changelist,
        "force_update": force_update,
        "svn_version": svn_version,
        "start_type": start_type,
    }
    from project.x51.test.deploy_server import test_deploy_server_main

    methodcaller(job, **kw)(test_deploy_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def core_dump_monitor(job: str):
    """
    服务器core监控
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x51/p-80e260dca005495d9082fc940f404686/history
    示例：
        python x51.py core_dump_monitor --job=monitor
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x51.core_dump_monitor import core_dump_monitor_main

    methodcaller(job, **kw)(core_dump_monitor_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def publish_idip(job: str):
    """
    发布idip
    流水线地址：
        http://bk-devops.h3d.com.cn/console/pipeline/x51/p-337bc2c423a8422f85d8c752d9ea106f/preview
    示例：
        python3 x51.py publish_idip --job=pull_code
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x51.publish_idip import publish_idip_main

    methodcaller(job, **kw)(publish_idip_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def publish_bbs(job: str):
    """
    发布bbs
    流水线地址：
        http://bk-devops.h3d.com.cn/console/pipeline/x51/p-18044643a22b4d39a9a886fc312da128/preview
    示例：
        python3 x51.py publish_bbs --job=pull_code
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x51.publish_bbs import publish_bbs_main

    methodcaller(job, **kw)(publish_bbs_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def publish_forum(job: str):
    """
    发布论坛
    流水线地址：
        http://bk-devops.h3d.com.cn/console/pipeline/x51/p-4c8c399ac8ff451c94e47dd9f59356f7/preview
    示例：
        python3 x51.py publish_forum --job=pull_code
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x51.publish_forum import publish_forum_main

    methodcaller(job, **kw)(publish_forum_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def clean_shared_space(job: str):
    """
    清理共享目录上的linux_bin
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x51/p-198145fe650a48f5b9309f2a925ea74d/preview
    示例：
        python3 x51.py clean_shared_space --job=mount
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x51.clean_shared_space import clean_shared_space_main

    methodcaller(job, **kw)(clean_shared_space_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def replace_server_shop(job: str):
    """
    替换服务器shop
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x51/p-79a535079ab447998646d72e437bee64/detail/b-c904383cecf64d5d9d99826d8a078f2f
    示例：
        python3 x51.py replace_server_shop --job=sync_p4
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x51.art.replace_server_shop import replace_server_shop_main

    methodcaller(job, **kw)(replace_server_shop_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def replace_client_shop(job: str):
    """
    替换客户端shop
    流水线地址：

    示例：
        python3 x51.py replace_client_shop --job=sync_p4
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x51.art.replace_client_shop import replace_client_shop_main

    methodcaller(job, **kw)(replace_client_shop_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def update_pipeline_param(job: str):
    """
    更新流水线参数
    流水线地址：

    示例：
        python3 x51.py update_bk_branch_param --job=update_params
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x51.update_bk_branch_param import update_bk_branch_param_main

    methodcaller(job, **kw)(update_bk_branch_param_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def cron_notify(job: str):
    """
    x51定时通知
    流水线地址：

    示例：
        python x51.py cron_notify --job=upsource_notify
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x51.cron_notify import cron_notify_main

    methodcaller(job, **kw)(cron_notify_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--branch", default="trunc", help="分支")
@click.option("--changelist", default="head", help="p4 changelist")
@click.option("--force_update", default=False, help="是否强更")
def music_deploy_server(job, branch, changelist, force_update):
    """
    音乐关卡-部署服务器
    流水线地址：
    示例：
        python x51.py music_deploy_server --job=stop_server
    """
    kw = {
        "job": job,
        "branch": branch,
        "changelist": changelist,
        "force_update": force_update,
    }
    from project.x51.music.deploy_private_env.deploy_server import (
        music_deploy_server_main,
    )

    methodcaller(job, **kw)(music_deploy_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def music_deploy_client(job):
    """
    音乐关卡-部署客户端
    流水线地址：
    示例：
        python x51.py music_deploy_client --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x51.music.deploy_private_env.deploy_client import (
        music_deploy_client_main,
    )

    methodcaller(job, **kw)(music_deploy_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def update_engine(job):
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x51.engine_update import engine_update_main

    methodcaller(job, **kw)(engine_update_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def dump_collection(job: str):
    """
    dump收集
    """
    from project.x51.dump_analysis import dump_collection_main

    methodcaller(job)(dump_collection_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def dump_analysis(job: str):
    """
    dump分析
    """
    from project.x51.dump_analysis import dump_analysis_main

    methodcaller(job)(dump_analysis_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def check_analysis_worker(job: str):
    from project.x51.dump_analysis import dump_analysis_monitor_main

    methodcaller(job)(dump_analysis_monitor_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def dump_trigger(job: str):
    from project.x51.dump_analysis import dump_trigger_main

    methodcaller(job)(dump_trigger_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def compile_server(job: str):
    from project.x51.compile.compile_server import compile_server_main

    methodcaller(job)(compile_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def compile_client(job: str):
    from project.x51.compile.compile_client import compile_client_main

    methodcaller(job)(compile_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def compile_server_client(job: str):
    from project.x51.compile import compile_server_client_main

    methodcaller(job)(compile_server_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def deploy_client(job: str):
    from project.x51.deploy.deploy_client import deploy_client_main

    methodcaller(job)(deploy_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def deploy_server(job: str):
    from project.x51.deploy.deploy_server import deploy_server_main

    methodcaller(job)(deploy_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def pack_server(job: str):
    from project.x51.test.pack_server import test_pack_server_main

    methodcaller(job)(test_pack_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def excel2csv(job: str):
    from project.x51.excel2csv import excel2csv_main

    methodcaller(job)(excel2csv_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def compile_engine_client(job: str):
    from project.x51.compile_engine_client import compile_engine_client_main

    methodcaller(job)(compile_engine_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def clean_x51_ftp(job: str):
    from project.x51.clean_ftp import clean_ftp_main

    methodcaller(job)(clean_ftp_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def compile_record_tool(job: str):
    from project.x51.compile_record_tool import compile_record_tool_main

    methodcaller(job)(compile_record_tool_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def pack_pc(job: str):
    from project.x51.test.pack_pc import pack_pc_main

    methodcaller(job)(pack_pc_main)


if __name__ == "__main__":
    cli()
