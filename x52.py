# coding=utf-8
from operator import methodcaller

from frame import PyframeException, cli, click, log


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--days", default="", help="保留的天数")
@click.option("--branches", default="", help="保留的分支数")
def clean_server(job, days, branches):
    """
    清理服务器
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x52/p-2dd5f9dc91ef4612bffcffebccd6136b/preview
    示例：
        python x52.py clean_server --job=clean_core
    """
    kw = {"job": job, "days": days, "branches": branches}
    from project.x52.clean_server import clean_server_main

    methodcaller(job, **kw)(clean_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--p4_force_update", default=False, help="是否强更p4")
@click.option("--branch", default="", help="分支")
@click.option("--code_changelist", default="", help="代码p4版本号，HEAD表示最新版本号")
@click.option("--rebuild", default=False, help="是否重新构建")
def build_server(job, p4_force_update, branch, code_changelist, rebuild):
    """
    编译服务器
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x52/p-d41117d07e114609a06b265fdf055418/history
    示例：
        python x52.py build_server --job=build_server
    """
    # log.info(job, p4_force_update, branch, code_changelist, rebuild)
    if len(branch) != 0:
        branch = branch.strip()
        log.info(branch)
        if branch != "trunc" and len(branch.split(".")) != 3:
            raise PyframeException("branch应为trunc或者x.x.x")
    code_changelist = code_changelist.lower().strip()
    # rebuild = True if rebuild == "true" else False
    # p4_force_update = True if p4_force_update == "true" else False
    kw = {
        "job": job,
        "P4_FORCE_UPDATE": p4_force_update,
        "BRANCH": branch,
        "CODE_CHANGELIST": code_changelist,
        "REBUILD": rebuild,
    }
    log.info(kw)
    from project.x52.build_server import build_server_main

    methodcaller(job, **kw)(build_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def core_dump_monitor(job: str):
    """
    服务器core监控
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x51/p-80e260dca005495d9082fc940f404686/history
    示例：
        python x52.py core_dump_monitor --job=monitor
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.core_dump_monitor import core_dump_monitor_main

    methodcaller(job, **kw)(core_dump_monitor_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--publish_svr", default="", help="发布服务器ip")
@click.option("--publish_version", default="*******", help="当前版本号")
@click.option("--src_svr", default="", help="当前版本的linux服务器地址")
@click.option("--sql_dir", default="", help="SQL_DIR")
@click.option("--song_ids", default="901358,902162", help="歌曲id")
@click.option("--all_config_path", default="\$OPENAPI_CONFIG \$CL5_CONFIG", help="")
@click.option("--tlog_file", default="", help="tlog的头文件路径")
@click.option("--tlog_cppfile", default="", help="tlog的实现文件路径")
@click.option("--is_publish_sql", default="", help="是否发布sql语句")
@click.option("--is_publish_music", default="", help="是否发布歌曲sql")
@click.option("--is_publish_config", default="", help="是否发布配置文件")
@click.option("--is_publish_tlog", default="", help="是否发布tlog文件")
def publish_versions(
    job,
    publish_svr,
    publish_version,
    src_svr,
    sql_dir,
    song_ids,
    all_config_path,
    tlog_file,
    tlog_cppfile,
    is_publish_sql,
    is_publish_music,
    is_publish_config,
    is_publish_tlog,
):
    """
    x52 版本发布
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x52/p-8f4ccb5e2e0d4ae8a4fb5956e869b545/history
    示例：
        python x52.py publish_version --job=print_msg
    """
    kw = {
        "job": job,
        "publish_svr": publish_svr,
        "publish_version": publish_version,
        "src_svr": src_svr,
        "sql_dir": sql_dir,
        "song_ids": song_ids,
        "all_config_path": all_config_path,
        "tlog_file": tlog_file,
        "tlog_cppfile": tlog_cppfile,
        "is_publish_sql": is_publish_sql,
        "is_publish_music": is_publish_music,
        "is_publish_config": is_publish_config,
        "is_publish_tlog": is_publish_tlog,
    }
    from project.x52.publish_version import publish_version_main

    methodcaller(job, **kw)(publish_version_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--version_info", default="", help="版本参数")
@click.option("--code_file", default="*******", help="代码版本")
def update_client_version(job, version_info, code_file):
    """
    x52 改版本号
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x52/p-bbd03d4539e34908ab3716338e77fec3/history
    示例：
        python x52.py update_client_version --job=download_p4_com_2002
    """
    kw = {"job": job, "version_info": version_info, "code_file": code_file}
    from project.x52.update_client_version import update_client_version_main

    methodcaller(job, **kw)(update_client_version_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def delete_not_exist_item(job):
    """
    x52 删除不存在物品
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x52/p-204627b236fb4567be097fe4c957eb51/history
    示例：
        python x52.py delete_not_exist_item --job=delete_all_item
    """
    kw = {
        "job": job,
    }
    from project.x52.delete_not_exist_item import delete_not_exist_item_main

    methodcaller(job, **kw)(delete_not_exist_item_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def clean_shared_space(job: str):
    """
    清理共享目录上的linux_bin
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x51/p-198145fe650a48f5b9309f2a925ea74d/preview
    示例：
        python3 x52.py clean_shared_space --job=mount
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.clean_shared_space import clean_shared_space_main

    methodcaller(job, **kw)(clean_shared_space_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def art_deploy_server(job: str):
    """
    美术部署服务器
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x52/p-e904affc561c4474886e3299c38b7889/history
    示例：
        python3 x52.py art_deploy_server --job=prepare
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.art.deploy_server import art_deploy_server_main

    methodcaller(job, **kw)(art_deploy_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def art_sync_config(job: str):
    """
    美术配置刷新
    流水线地址：
        测试: https://bk-devops.h3d.com.cn/console/pipeline/x52/p-f685744e7e42443e906835f649b884cc/preview
        正式: https://bk-devops.h3d.com.cn/console/pipeline/x52/p-ec5a6723b3224c35ad6ca5a9770da754/preview
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.art.sync_config import art_sync_config_main

    methodcaller(job, **kw)(art_sync_config_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def plan_deploy_server(job: str):
    """
    策划部署服务器
    流水线地址：
    示例：
        python3 x52.py plan_deploy_server --job=prepare
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.plan.deploy_server import plan_deploy_server_main

    methodcaller(job, **kw)(plan_deploy_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def test_deploy_server(job: str):
    """
    测试部署服务器
    流水线地址：
    示例：
        python3 x52.py test_deploy_server --job=prepare
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.test.deploy_server import test_deploy_server_main

    methodcaller(job, **kw)(test_deploy_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def deploy_private_server(job: str):
    """
    测试部署服务器
    流水线地址：
    示例：
        python3 x52.py deploy_private_server --job=prepare
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.test.deploy_private_server import (
        deploy_private_server_main,
    )

    methodcaller(job, **kw)(deploy_private_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def replace_shop_xml(job: str):
    """
    替换shop.xml
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x52/p-b6d327043cfc4fd1b2aedf9b6464cb81/edit
    示例：
        python3 x52.py replace_shop_xml --job=sync_code
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.art.replace_shop_xml import replace_shop_xml_main

    methodcaller(job, **kw)(replace_shop_xml_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def replace_prize_pool(job: str):
    """
    替换大奖池
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x52/p-b6d327043cfc4fd1b2aedf9b6464cb81/edit
    示例：
        python x52.py replace_prize_pool --job=sync_code
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.art.replace_prize_pool import replace_prize_pool_main

    methodcaller(job, **kw)(replace_prize_pool_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def publish_package(job: str):
    """
    发版前打包
    流水线地址：

    示例：
        python x52.py publish_package --job=on_success
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.publish_package import publish_package_main

    methodcaller(job, **kw)(publish_package_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def update_pipeline_param(job: str):
    """
    更新流水线参数
    流水线地址：

    示例：
        python3 x52.py update_bk_branch_param --job=update_params
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.update_bk_branch_param import update_bk_branch_param_main

    methodcaller(job, **kw)(update_bk_branch_param_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--p4_force_update", default=False, help="是否强更p4")
@click.option("--branch", default="", help="分支")
@click.option("--code_changelist", default="", help="代码p4版本号，HEAD表示最新版本号")
@click.option("--rebuild", default=False, help="是否重新构建")
def build_server_from_special_branch(job, p4_force_update, branch, code_changelist, rebuild):
    """
    编译服务器特殊分支，单独处理
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/x52/p-d41117d07e114609a06b265fdf055418/history
    示例：
        python x52.py build_server_from_special_branch --job=build_server
    """
    # log.info(job, p4_force_update, branch, code_changelist, rebuild)
    code_changelist = code_changelist.lower().strip()
    kw = {
        "job": job,
        "P4_FORCE_UPDATE": p4_force_update,
        "BRANCH": branch,
        "CODE_CHANGELIST": code_changelist,
        "REBUILD": rebuild,
    }
    log.info(kw)
    from project.x52.build_server_from_special_branch import build_server_main

    methodcaller(job, **kw)(build_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def branch_vacation_compare(job):
    """
    美术资源：最新分支和主支比较
    """
    from project.x52.art_resource_check import branch_vacation_compare_main

    methodcaller(job)(branch_vacation_compare_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def small_package_compare(job):
    """
    美术资源：小包资源比较
    """
    from project.x52.art_resource_check import small_package_compare_main

    methodcaller(job)(small_package_compare_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def xw_current_compare(job):
    """
    美术资源：现网和最新分支比较
    """
    from project.x52.art_resource_check import xw_current_compare_main

    methodcaller(job)(xw_current_compare_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def create_new_branch(job):
    """
    创建新分支
    """
    from project.x52.create_new_branch import create_new_branch_main

    methodcaller(job)(create_new_branch_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def build_client(job: str):
    """
    示例：
        python3 x52.py build_client --job=update_p4
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x52.build_client import build_client_main

    methodcaller(job, **kw)(build_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def clean_x52_ftp(job: str):
    from project.x52.clean_ftp import clean_ftp_main

    methodcaller(job)(clean_ftp_main)


if __name__ == "__main__":
    cli()
