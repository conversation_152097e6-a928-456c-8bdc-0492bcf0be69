# coding=utf-8
from operator import methodcaller

from frame import PyframeException, cli, click, log


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--param1", default="param1", help="参数1")
@click.option("--param2", default="param2", help="参数2")
def pyframe_test(job, param1, param2):
    """
    pyframe_test流水线
    示例：
        python x5m.py pyframe_test --job=do_first_job
        python x5m.py pyframe_test --job=do_second_job
    """
    kw = {
        "job": job,
        "param1": param1,
        "param2": param2,
    }
    log.info("params: " + str(kw))
    from project.x5m.pyframe_test import pyframe_test_main

    methodcaller(job, **kw)(pyframe_test_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--platform", default="", help="平台，android或者ios")
@click.option("--task_status", default="", help="任务状态，success、failed或者canceled")
@click.option("--debug", default="", help="是否为调试模式")
def silent_distribution(job, platform, task_status, debug):
    """
    静默资源分发
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-6d504df3051b4f9da84cb066b184849d/history
    示例：
        python x5m.py silent_distribution --job=test
    """
    kw = {"job": job, "platform": platform, "task_status": task_status, "debug": debug}
    from project.x5m.silent_distribution import silent_distribution_main

    methodcaller(job, **kw)(silent_distribution_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--platform", default="", help="平台，android或者ios")
@click.option("--task_status", default="", help="任务状态，success、failed或者canceled")
@click.option("--debug", default="", help="是否为调试模式")
def lite_distribution(job, platform, task_status, debug):
    """
    Lite资源分发
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-5bc3d25cb04f4727962d3cc6d0280e5d/history
    示例：
        python x5m.py silent_distribution --job=test
    """
    kw = {"job": job, "platform": platform, "task_status": task_status, "debug": debug}
    from project.x5m.lite_distribution import lite_distribution_main

    methodcaller(job, **kw)(lite_distribution_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--days", default="", help="保留的天数")
def clean_server(job, days):
    """
    清理服务器
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-d70ace1bf0c74439a5145539293a1e01/history
    示例：
        python x5m.py clean_server --job=clean_core
    """
    kw = {"job": job, "days": days}
    from project.x5m.clean_server import clean_server_main

    methodcaller(job, **kw)(clean_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def clean_ftp_150_30(job):
    """
    清理ftp 150.30
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-104a8c89e6bf4b6d89c9b7ccd91439b9/history
    示例：
        python x5m.py clean_ftp_150_30 --job=clean_lite
        python x5m.py clean_ftp_150_30 --job=clean_silent
        python x5m.py clean_ftp_150_30 --job=clean_dynamic
        python x5m.py clean_ftp_150_30 --job=clean_dress
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x5m.clean_ftp_150_30 import clean_ftp_150_30_main

    methodcaller(job, **kw)(clean_ftp_150_30_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def build_server(job: str):
    """
    编译服务器
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-ff269a1126a94630af13f452ec3447c7/history
    示例：
        python x5m.py build_server --job=clone_x5mobile --branch=master --pipeline_id=10000 --commit_id=xxxxxxxx
        python x5m.py build_server --job=build_server
        python x5m.py build_server --job=upload_artifacts
    """
    from project.x5m.build_server import build_server_main

    methodcaller(job)(build_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def bugly_upload(job):
    """
    iOS符号表上传bugly平台
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-ed1a8d7d572a4f66a50a71c6a4018b8e/history
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x5m.bugly_upload import bugly_upload_main

    methodcaller(job, **kw)(bugly_upload_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def project39_bugly_upload(job):
    """
    iOS符号表上传bugly平台
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-ed1a8d7d572a4f66a50a71c6a4018b8e/history
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project39.x5m.bugly_upload import bugly_upload_main

    methodcaller(job, **kw)(bugly_upload_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--platform", default="", help="平台，android或者ios")
def installation_distribution(job: str, platform: str):
    """
    IPA/APK安装包分发
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-32605c5f0cf44b478785a63112e5a3a2/history
    示例：
        python x5m.py installation_distribution --job=test
    """
    kw = {
        "job": job,
        "platform": platform,
    }
    log.info("params: " + str(kw))
    from project.x5m.installation_distribution import (
        installation_distribution_main,
    )

    methodcaller(job, **kw)(installation_distribution_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def core_dump_monitor(job: str):
    """
    服务器core监控
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-f05f5fb705e141898148efe09595a7fc/history
    示例：
        python x5m.py core_dump_monitor --job=monitor
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x5m.core_dump_monitor import core_dump_monitor_main

    methodcaller(job, **kw)(core_dump_monitor_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--platform", default="", help="平台，android或者ios")
def music_effect_package(job: str, platform: str):
    """
    音效打包
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-1c49b124300c4313ae47ea39646ffb4d/history
    示例：
        python x5m.py music_effect_package --job=on_success
    """
    kw = {
        "job": job,
        "platform": platform,
    }
    log.info("params: " + str(kw))
    from project.x5m.music_effect_package import music_effect_package_main

    methodcaller(job, **kw)(music_effect_package_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def card_package(job):
    """
    名片打包-美术消息通知优化
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-202a1331df5b465ba19f4f65b12c4a5f/history
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x5m.art_package.card_package import card_package_main

    methodcaller(job, **kw)(card_package_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def deploy_public_server(job):
    """
    部署服务器
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-2190ff7f85fd4ab58fd2703ec522ee87/history
    示例：
        python x5m.py deploy_public_server --job=get_max_pipeline_id
    """
    kw = {
        "job": job,
    }
    from project.x5m.test.deploy_public_server import deploy_public_server_main

    methodcaller(job, **kw)(deploy_public_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def deploy_private_server(job):
    """
    部署私服服务器
    流水线地址：
    https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-********************************/edit
    示例：
        python x5m.py deploy_private_server --job=get_max_pipeline_id
    """
    kw = {
        "job": job,
    }
    from project.x5m.test.deploy_private_server import (
        deploy_private_server_main,
    )

    methodcaller(job, **kw)(deploy_private_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--kind", default="", help="制品类型")
def publish_ptr_rc_final(job, kind):
    """
    Ptr、Rc、Final发布工具-PM消息通知优化
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-09c07eeb73fa470cb099b125ff873fba/history
    """
    kw = {"job": job, "kind": kind}
    log.info("params: " + str(kw))
    from project.x5m.publish_ptr_rc_final import publish_ptr_rc_final_main

    methodcaller(job, **kw)(publish_ptr_rc_final_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def manual_package_trigger(job):
    """
    手动触发打包
    流水线地址：
        http://jenkins-x5mobile.h3d.com.cn/job/manual_package_trigger/
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x5m.manual_package_trigger import manual_package_trigger_main

    methodcaller(job, **kw)(manual_package_trigger_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def restart_public_server(job):
    """
    重启公服
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-1275ab19ad6e46868cd35535ac41390e/edit
    示例：
        python x5m.py restart_public_server --job=shutdown_server
    """
    kw = {
        "job": job,
    }
    from project.x5m.test.restart_public_server import (
        restart_public_server_main,
    )

    methodcaller(job, **kw)(restart_public_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def build_gm_tool(job):
    """
    编译gm工具
    流水线地址：https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-4f45d7682cb244ad88986f8b6032969a
    示例：
        python x5m.py build_gm_tool --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x5m.build_gm_tool import build_gm_tool_main

    methodcaller(job, **kw)(build_gm_tool_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def cdn_silent_download_check(job):
    """
    cdn_silent下载检查
    流水线地址：https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-e8d4488d4f354787a9f6e9fb6abf5051
    示例：
        python3 x5m.py cdn_silent_download_check --job=prepare
    """
    from project.x5m.cdn_silent_download_check import (
        cdn_silent_download_check_main,
    )

    methodcaller(job)(cdn_silent_download_check_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def xml_to_byte(job):
    """
    二进制转换
    流水线地址：
    示例：
        python3 x5m.py xml_to_byte --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x5m.xml_to_byte import xml_to_byte_main

    methodcaller(job, **kw)(xml_to_byte_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def package_android_client(job):
    """
    安卓客户端打包
    流水线地址：
        http://jenkins-x5mobile.h3d.com.cn/job/%E6%B5%81%E6%B0%B4%E7%BA%BF%E7%BB%84%E9%87%8D%E6%9E%84/job/package-android/
    示例：
        python3 x5m.py package_android_client --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x5m.package_android_client import package_android_client_main

    methodcaller(job, **kw)(package_android_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def silent_package_android(job):
    """
    silent安卓打包
    流水线地址：
        http://jenkins-x5mobile.h3d.com.cn/job/%E6%B5%81%E6%B0%B4%E7%BA%BF%E7%BB%84%E9%87%8D%E6%9E%84/job/silent_package_android/
    示例：
        python3 x5m.py silent_package_android --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x5m.silent_package import silent_package_android_main

    methodcaller(job, **kw)(silent_package_android_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def silent_package_ios(job):
    """
    silent ios打包
    流水线地址：
        http://jenkins-x5mobile.h3d.com.cn/job/%E6%B5%81%E6%B0%B4%E7%BA%BF%E7%BB%84%E9%87%8D%E6%9E%84/job/silent_package_ios/
    示例：
        python3 x5m.py silent_package_ios --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x5m.silent_package import silent_package_ios_main

    methodcaller(job, **kw)(silent_package_ios_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def refresh_hotfix(job):
    """
    刷热更
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-1e03ca4d8d154561b1c863a85508902b/history
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-c76dfa83d7c0482ba871a0abb3802688/history
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-8ef4b5f933b9418fb84d36fffaaf1272/history
    示例：
        python x5m.py refresh_hotfix --job=kick_player
    """
    kw = {
        "job": job,
    }
    from project.x5m.test.refresh_hotfix import refresh_hotfix_main

    methodcaller(job, **kw)(refresh_hotfix_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def restart_private_server(job):
    """
    重启私服
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-65d7ff69f01245dd847a41b78b7a03e1/history
    示例：
        python x5m.py restart_private_server --job=stop_all_server
    """
    kw = {
        "job": job,
    }
    from project.x5m.test.restart_private_server import (
        restart_private_server_main,
    )

    methodcaller(job, **kw)(restart_private_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def close_private_server(job):
    """
    关闭私服
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-2a15254d44ba4f82be43364759eca698/history
    示例：
        python x5m.py close_private_server --job=stop_all_server
    """
    kw = {
        "job": job,
    }
    from project.x5m.test.close_private_server import close_private_server_main

    methodcaller(job, **kw)(close_private_server_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def find_tmp_in_p4(job):
    """
    定时提醒
    流水线地址：
    示例：
        python x5m.py find_tmp_in_p4 --job=find_tmp
    """
    kw = {
        "job": job,
    }
    from project.x5m.find_tmp_in_p4 import find_tmp_in_p4_main

    methodcaller(job, **kw)(find_tmp_in_p4_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def timeline_capture_picture(job):
    """
    时光机截图
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-ce75ba7b480b480ca0722a2a85d881ef/history
    示例：
        python x5m.py timeline_capture_picture --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x5m.timeline_capture.picture import (
        timeline_capture_picture_main,
    )

    methodcaller(job, **kw)(timeline_capture_picture_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def timeline_capture_video(job):
    """
    时光机录屏
    流水线地址：
    示例：
        python x5m.py timeline_capture_picture --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x5m.timeline_capture.video import timeline_capture_video_main

    methodcaller(job, **kw)(timeline_capture_video_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def timeline_capture_update(job):
    """
    时光机更新
    流水线地址：
        https://bk-devops.h3d.com.cn/console/pipeline/dgm/p-b04f645bdcd7404093e77fc34b614d1f/history
    示例：
        python x5m.py timeline_capture_picture --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x5m.timeline_capture.update import timeline_capture_update_main

    methodcaller(job, **kw)(timeline_capture_update_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def package_ios_client(job):
    """
    iOS客户端打包
    流水线地址：
        http://jenkins-x5mobile.h3d.com.cn/job/%E6%B5%81%E6%B0%B4%E7%BA%BF%E7%BB%84%E9%87%8D%E6%9E%84/job/lixin02-ios/
    示例：
        python3 x5m.py package_ios_client --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x5m.package_ios_client import package_ios_client_main

    methodcaller(job, **kw)(package_ios_client_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def auto_merge_x5mconfig_cdn(job):
    """
    CND配置自动merge
    流水线地址：http://jenkins-x5mobile.h3d.com.cn/job/git_auto_merge/

    示例：
        python3 x5m.py auto_merge_x5mconfig_cdn --job=success
    """
    kw = {
        "job": job,
    }
    from project.x5m.auto_merge_x5mconfig_cdn import (
        auto_merge_x5mconfig_cdn_main,
    )

    methodcaller(job, **kw)(auto_merge_x5mconfig_cdn_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def update_pipeline_param(job: str):
    """
    更新流水线参数
    流水线地址：

    示例：
        python3 x5m.py update_bk_branch_param --job=update_params
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x5m.update_bk_branch_param import update_bk_branch_param_main

    methodcaller(job, **kw)(update_bk_branch_param_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def ui_package(job):
    """
    版本内界面打包
    流水线地址：

    示例：
        python x5m.py ui_package --job=prepare
    """
    kw = {
        "job": job,
    }
    from project.x5m.ui_package import ui_package_main

    methodcaller(job, **kw)(ui_package_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def arthub_upload_p4(job):
    """
    arthub上传p4
    流水线地址：

    示例：
        python x5m.py arthub_upload_p4 --job=upload_arthub
    """
    kw = {
        "job": job,
    }
    from project.x5m.arthub_upload_p4 import arthub_upload_p4_main

    methodcaller(job, **kw)(arthub_upload_p4_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def small_island_package(job: str):
    """
    小岛打包
    流水线地址：

    示例：
        python3 x5m.py small_island_package --job=check_changelist
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x5m.small_island_package import small_island_package_main

    methodcaller(job, **kw)(small_island_package_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def test_activate_unity(job: str):
    """
    小岛打包
    流水线地址：

    示例：
        python3 x5m.py test_activate_unity --job=check_changelist
    """
    kw = {
        "job": job,
    }
    log.info("params: " + str(kw))
    from project.x5m.test_activate_unity import test_activate_unity

    methodcaller(job, **kw)(test_activate_unity)


@cli.command()
@click.option("--job", default="", help="步骤")
def check_p4_opened_files(job: str):
    from project.x5m.check_p4_opened_files import check_p4_opened_files_main

    methodcaller(job)(check_p4_opened_files_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--debug", default="", help="是否为debug模式")
@click.option("--hot_version", default="", help="热更版本")
@click.option("--is_stop_service", default="", help="是否停服更新")
@click.option("--is_merge", default="", help="是否合包")
def weekly_updates_release(job: str, debug: int, hot_version: str, is_stop_service: str, is_merge: str):
    from project39.x5m.weekly_updates_release import weekly_updates_release_main

    kw = {
        "job": job,
        "debug": debug,
        "hot_version": hot_version,
        "is_stop_service": is_stop_service,
        "is_merge": is_merge
    }
    methodcaller(job, **kw)(weekly_updates_release_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def game_icon_upload_git(job: str):
    from project.x5m.game_icon_upload_git import game_icon_upload_git_main

    methodcaller(job)(game_icon_upload_git_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def x5m_config_ci(job: str):
    from project.x5m.x5m_config_ci import x5m_config_ci_main

    methodcaller(job)(x5m_config_ci_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--resource", default="", help="资源类型")
def trad_package(job: str, resource: str):
    from project.x5m.art_package.trad_package.uislice_package import (
        uislice_package_main,
    )

    packers = {
        "uislice": uislice_package_main,
    }
    if not resource in packers:
        raise PyframeException("不支持的资源类型")

    methodcaller(job)(packers[resource])


@cli.command()
@click.option("--job", default="", help="步骤")
def clean_ftp_150_46(job: str):
    from project.x5m.clean_ftp_150_46 import clean_ftp_150_46_main

    methodcaller(job)(clean_ftp_150_46_main)


@cli.command()
@click.option("--job", default="", help="步骤")
def card_packages(job: str):
    from project.x5m.card_package import card_package_main

    methodcaller(job)(card_package_main)

@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--resource", default="", help="资源类型")
def art_package(job: str, resource: str):
    from frame import env
    env.set({"RESOURCE": resource})
    from project.x5m.art_package_mgr import art_package_main
    methodcaller(job)(art_package_main)

@cli.command()
@click.option("--job", default="", help="步骤")
def publish_cdn_hotfix(job: str):
    from project.x5m.publish_cdn_hotfix import publish_cdn_hotfix_main
    methodcaller(job)(publish_cdn_hotfix_main)


@cli.command()
@click.option("--job", default="", help="步骤")
@click.option("--folder_path", default="", help="文件夹路径")
@click.option("--count", default=3, type=click.INT, help="文件夹保留数量")
@click.option("--days", default=7, type=click.INT, help="保留的天数")
@click.option("--is_force", default=False, type=click.BOOL, help="是否强制删除, 当启用强制删除时，完全按照保留数量策略")
def clean_node_folder(job: str, folder_path: str, count: int, days: int, is_force: bool):
    """
    按照保留数量和保留时间，清理节点上指定的目录

    NOTE: 当指定了强制删除，则完全按照保留数量策略删除，不再考虑保留时间
    
    示例：
        python x5m.py clean_node_folder --job=cleanup_dgm_server_folders --folder_path=xxx --count=3 --days=7 --is_force=False
    """
    kw = {"job": job, "folder_path": folder_path, "days": days, "count": count, "is_force": is_force}
    from project.x5m.clean_server import clean_server_main

    methodcaller(job, **kw)(clean_server_main)


if __name__ == "__main__":
    cli()
