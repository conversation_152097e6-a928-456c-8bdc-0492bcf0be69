from distutils.errors import CompileError

import click
import os
import sys
import logging
import json
from operator import methodcaller
import threading
import queue

MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
if MODULE_DIR not in sys.path:
    sys.path.append(MODULE_DIR)
from contrib.utils import get_kind_cls

class JsonType(click.ParamType):
    name = 'json'

    def convert(self, value, param, ctx):
        try:
            # 尝试解析 JSON 字符串
            return json.loads(value)
        except json.JSONDecodeError:
            # 如果解析失败，提供错误信息
            self.fail(f'invalid JSON: {value}', param, ctx)


def get_stdin_data(timeout:int=0.5):
    result_queue = queue.Queue()
    def _get_stdin_data(q):
        result = sys.stdin.read().strip()
        result and q.put(result)

    thread = threading.Thread(target=_get_stdin_data, args=(result_queue,))
    thread.daemon = True
    thread.start()
    try:
        result = result_queue.get(timeout=timeout)
        logging.info(f"stdin_data:{result}")
        return json.loads(result)
    except queue.Empty as e:
        return {}
    finally:
        thread.join(1)

@click.command()
@click.option("--kind", default="legal_affairs", help="类唯一标识")
@click.option("--method", default="run", help="类方法")
@click.option("--kwargs", type=JsonType(), default="{}", help="函数参数")
@click.pass_context
def cli(ctx, kind: str, method: str, kwargs=None):
    """
    x5m jenkins pipeline 入口函数
    流水线地址：http://jenkins-x5mobile.h3d.com.cn/job/git_auto_merge/

    示例：
        python cli.py --kind=legal_affairs --method=echo --kwargs='{\"month\": 8, \"force\": true, \"kind\": [\"X5M_CARD\"]}'
    """
    logging.info(f"{ctx.params}")
    logging.info(f"pipeline start with argv: {kind}, {method}")
    body = get_stdin_data()
    kwargs and body.update(kwargs)
    logging.info(f"get info from std: {body}")
    ins_id = body.pop('ins_id', None)
    name, cls = get_kind_cls(
        kind,
        os.path.dirname(os.path.abspath(__file__)),
        only_dirs=('project.x5m',)
    )
    ins = cls.load_ins(ins_id) if ins_id else cls
    result = methodcaller(method, **body)(ins)
    result_json = json.dumps(result, ensure_ascii=False)
    logging.info(f"pipeline return: {result_json}")
    print(result_json)

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    cli()
